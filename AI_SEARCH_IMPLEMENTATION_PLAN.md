# AI-Search Implementation Plan

## Project Overview

Build an AI-powered semantic code search system for the Open eLMS codebase using:
- **Repomix** (https://github.com/yamadashy/repomix) to aggregate code from specific directories
- **Vector database** (PostgreSQL + pgvector) to store embeddings 
- **MCP Server** to expose search functionality to Claude Code

### Docker-Only Approach Benefits
- **No local dependencies**: No need to install Node.js, Python, or any packages locally
- **Consistent environment**: Same versions and configurations across all developer machines
- **Easy cleanup**: Simple `docker compose down` removes everything
- **Resource isolation**: Services don't interfere with local development environment
- **Version control**: All dependencies versioned in Dockerfiles
- **Developer-friendly**: Works on any machine with Docker installed

## Architecture

```
Repomix → Code Aggregation → AI Embeddings → Vector Database → MCP Server → Claude Code
```

### Components (All Dockerized)
1. **AI-Search Container**: All-in-one container with repomix, vectorizer, and API endpoint
2. **PostgreSQL + pgvector**: Database container with vector extension for storing embeddings
3. **MCP Server Integration**: Built into the AI-Search container for Claude Code access

### Docker Services Architecture (Simplified)
```yaml
# Simplified architecture - just two containers
services:
  postgres_vector:  # PostgreSQL with pgvector extension
  ai_search:        # All-in-one container with:
                    # - Repomix for code aggregation
                    # - Vectorizer for embeddings
                    # - API endpoints for search
                    # - MCP server for Claude Code
                    # - Web UI for search interface
```

### Alternative: Unified AI-Search Container
For maximum simplicity, we can create a single container that includes everything:
- Node.js and Python runtimes
- Repomix pre-installed
- Vectorization logic
- REST API endpoints
- MCP server
- Embedded SQLite with vector extension (instead of separate PostgreSQL)

## Implementation Approach: Unified Container

### Single Dockerfile Approach
Create `docker/Dockerfile.ai-search` that includes everything:

```dockerfile
FROM python:3.11-slim

# Install Node.js for repomix
RUN apt-get update && apt-get install -y \
    nodejs \
    npm \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install repomix globally
RUN npm install -g repomix

# Python dependencies
WORKDIR /app
COPY ai-search/requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY ai-search/ /app/

# Create directories for data
RUN mkdir -p /data /cache

# Expose ports
EXPOSE 8080 8081

# Use the start script from ai-search directory
CMD ["./start.sh"]
```

### Simplified docker-compose.yml
```yaml
services:
  postgres_vector:
    image: pgvector/pgvector:pg16
    container_name: lms_postgres_vector
    environment:
      POSTGRES_USER: lms_vector
      POSTGRES_PASSWORD: lms_vector_pass
      POSTGRES_DB: lms_vectors
    volumes:
      - postgres_vector_data:/var/lib/postgresql/data
      - ./postgres-init:/docker-entrypoint-initdb.d:ro
    networks:
      - lms_network

  ai_search:
    build:
      context: .
      dockerfile: Dockerfile.ai-search
    container_name: lms_ai_search
    depends_on:
      - postgres_vector
    environment:
      - DATABASE_URL=************************************************************/lms_vectors
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
    ports:
      - "8080:8080"  # REST API
      - "8081:8081"  # MCP Server
    volumes:
      - ../src:/code:ro                    # Source code to analyze
      - ./ai-search-data:/data             # Persistent data storage
      - ./ai-search/repomix.config.json:/app/repomix.config.json:ro
    networks:
      - lms_network
```

## Implementation Phases

### Phase 1: Foundation Setup
**Goal**: Basic infrastructure and tooling setup (all Docker-based)

#### 1.1 Repomix Integration (Dockerized)
- [ ] Create `docker/Dockerfile.repomix`:
  ```dockerfile
  FROM node:18-alpine
  WORKDIR /app
  RUN npm install -g repomix
  COPY ai-search/repomix.config.json /app/
  VOLUME ["/code", "/output"]
  CMD ["repomix", "--config", "/app/repomix.config.json", "--output-dir", "/output"]
  ```
- [ ] Add repomix service to docker-compose.yml:
  ```yaml
  repomix:
    build: 
      context: .
      dockerfile: Dockerfile.repomix
    container_name: lms_repomix
    volumes:
      - ../src:/code:ro
      - ./ai-search/data:/output
  ```
- [ ] Create `ai-search/repomix.config.json` for Open eLMS specific directories:
  ```json
  {
    "include": [
      "src/app/**/*",
      "src/classes/**/*", 
      "src/crons/**/*",
      "src/scripts/**/*",
      "src/tpls/**/*",
      "src/public/index.php",
      "src/public/scorm/**/*",
      "src/public/api/**/*"
    ],
    "exclude": [
      "**/*.log",
      "**/*.cache", 
      "**/vendor/**",
      "**/node_modules/**",
      "src/public/api/data/**",
      "src/logs/**",
      "src/temp/**",
      "src/private/**"
    ],
    "output": {
      "format": "xml",
      "fileName": "lms-codebase.xml"
    }
  }
  ```
- [ ] Test repomix generation via Docker: `docker compose run repomix`
- [ ] Verify output covers ~7,000+ PHP files in key directories

#### 1.2 Database Setup
- [ ] Add PostgreSQL + pgvector to docker-compose.yml:
  ```yaml
  postgres_vector:
    image: pgvector/pgvector:pg16
    container_name: lms_postgres_vector
    environment:
      POSTGRES_USER: lms_vector
      POSTGRES_PASSWORD: lms_vector_pass
      POSTGRES_DB: lms_vectors
    ports:
      - "5433:5432"  # Different port to avoid conflicts
    volumes:
      - postgres_vector_data:/var/lib/postgresql/data
      - ./postgres-init:/docker-entrypoint-initdb.d:ro
  ```
- [ ] Create database schema in `postgres-init/01-init-schema.sql`:
  ```sql
  CREATE EXTENSION IF NOT EXISTS vector;
  CREATE SCHEMA IF NOT EXISTS code_search;
  
  CREATE TABLE code_search.code_embeddings (
    id SERIAL PRIMARY KEY,
    file_path TEXT NOT NULL,
    file_type VARCHAR(20),
    content_type VARCHAR(50), -- 'function', 'class', 'route', etc.
    name VARCHAR(255),        -- function/class name
    content_chunk TEXT NOT NULL,
    line_start INTEGER,
    line_end INTEGER,
    embedding VECTOR(1536),   -- OpenAI embedding dimension
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
  );
  
  -- Indexes for performance
  CREATE INDEX idx_embeddings_file_path ON code_search.code_embeddings (file_path);
  CREATE INDEX idx_embeddings_content_type ON code_search.code_embeddings (content_type);
  CREATE INDEX idx_embeddings_name ON code_search.code_embeddings (name);
  CREATE INDEX idx_embeddings_vector ON code_search.code_embeddings 
    USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);
  ```

### Phase 2: Code Processing & Vectorization
**Goal**: Extract and vectorize code chunks from repomix output

#### 2.1 Vectorizer Service (Dockerized)
- [ ] Create `docker/Dockerfile.vectorizer`:
  ```dockerfile
  FROM python:3.11-slim
  WORKDIR /app
  COPY requirements.vectorizer.txt .
  RUN pip install --no-cache-dir -r requirements.vectorizer.txt
  COPY vectorizer/ /app/
  ENV PYTHONUNBUFFERED=1
  CMD ["python", "main.py"]
  ```
- [ ] Create `docker/requirements.vectorizer.txt`:
  ```
  asyncpg==0.29.0
  pgvector==0.2.5
  openai==1.12.0
  anthropic==0.18.1
  lxml==5.1.0
  python-dotenv==1.0.1
  tenacity==8.2.3
  ```
- [ ] Create `docker/vectorizer/main.py`:
  - Parse repomix XML output
  - Extract meaningful code chunks (functions, classes, routes)
  - Generate embeddings using OpenAI/Anthropic API
  - Store in PostgreSQL with metadata
- [ ] Add vectorizer service to docker-compose.yml:
  ```yaml
  vectorizer:
    build:
      context: .
      dockerfile: Dockerfile.vectorizer
    container_name: lms_vectorizer
    depends_on:
      - postgres_vector
    environment:
      - DATABASE_URL=************************************************************/lms_vectors
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
    volumes:
      - ./ai-search/data:/data:ro
    networks:
      - lms_network
  ```

#### 2.2 Code Chunk Extraction
- [ ] **PHP Functions**: Extract `public/private/protected function name()`
- [ ] **PHP Classes**: Extract `class ClassName`
- [ ] **Routes**: Extract Slim route definitions `$app->get/post/put/delete`
- [ ] **Configuration**: Extract key configuration arrays
- [ ] **Database Models**: Extract Eloquent model definitions
- [ ] Store with metadata: file path, line numbers, chunk type, name

#### 2.3 Embedding Generation
- [ ] Use OpenAI `text-embedding-ada-002` model
- [ ] Batch processing for efficiency (up to 100 chunks per request)
- [ ] Error handling and retry logic
- [ ] Progress tracking and logging
- [ ] Estimated ~6,000-10,000 code chunks to process

### Phase 3: MCP Server Development
**Goal**: Expose search functionality via Model Context Protocol

#### 3.1 MCP Server Core (Dockerized)
- [ ] Create `docker/Dockerfile.mcp`:
  ```dockerfile
  FROM python:3.11-slim
  WORKDIR /app
  COPY requirements.mcp.txt .
  RUN pip install --no-cache-dir -r requirements.mcp.txt
  COPY mcp-server/ /app/
  ENV PYTHONUNBUFFERED=1
  CMD ["python", "main.py"]
  ```
- [ ] Add MCP server to docker-compose.yml:
  ```yaml
  mcp_server:
    build:
      context: .
      dockerfile: Dockerfile.mcp
    container_name: lms_mcp_server
    depends_on:
      - postgres_vector
    environment:
      - DATABASE_URL=************************************************************/lms_vectors
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
    ports:
      - "8080:8080"  # MCP server port
    networks:
      - lms_network
  ```
- [ ] Create `docker/mcp-server/main.py` using FastMCP
- [ ] Database connection with asyncpg + pgvector
- [ ] Embedding generation for search queries
- [ ] Vector similarity search implementation

#### 3.2 MCP Tools Implementation
- [ ] **semantic_search**: Natural language code search
  ```python
  @mcp.tool()
  async def semantic_search(query: str, limit: int = 10, threshold: float = 0.3):
      # Generate query embedding
      # Perform vector similarity search
      # Return ranked results with context
  ```
- [ ] **find_similar_code**: Find code similar to provided snippet
- [ ] **search_by_file**: Search within specific files/patterns
- [ ] **get_code_context**: Get detailed context for specific code chunk

#### 3.3 MCP Resources
- [ ] **stats**: Database statistics and coverage info
- [ ] **schema**: Database schema information

### Phase 4: Integration & Testing
**Goal**: Claude Code integration and validation

#### 4.1 Claude Code MCP Configuration
- [ ] Create MCP configuration for Claude Code
- [ ] Test tool discovery and execution
- [ ] Validate search results quality and relevance

#### 4.2 Performance Optimization
- [ ] Query performance tuning
- [ ] Index optimization
- [ ] Connection pooling
- [ ] Caching strategies for common queries

#### 4.3 Documentation & Usage
- [ ] Create usage examples and documentation
- [ ] Integration with development workflow
- [ ] Search best practices guide

## Technical Specifications

### Target Coverage
- **Files**: ~7,000+ PHP files
- **Code Chunks**: ~6,000-15,000 (functions, classes, routes)
- **Embedding Dimensions**: 1536 (OpenAI ada-002)
- **Search Threshold**: 0.3 (configurable)

### Performance Goals
- **Search Response**: <1 second
- **Vectorization Time**: 5-10 minutes for full codebase
- **Storage**: ~100MB for embeddings + metadata
- **Accuracy**: High relevance for semantic code queries

### API Keys Required
- **OpenAI API Key**: For embedding generation
- **Anthropic API Key**: Optional alternative for embeddings

## Directory Structure (Fully Integrated)
```
lms/
├── docker/
│   ├── Dockerfile.ai-search         # Unified container with everything
│   ├── postgres-init/
│   │   └── 01-init-schema.sql      # Database schema for vector search
│   ├── ai-search/
│   │   ├── main.py                 # Main application with:
│   │   │                           # - FastAPI endpoints
│   │   │                           # - Vectorization logic
│   │   │                           # - MCP server
│   │   │                           # - Repomix runner
│   │   ├── repomix.config.json     # Repomix configuration
│   │   ├── requirements.txt        # Python dependencies
│   │   └── start.sh                # Startup script
│   ├── ai-search-data/             # Volume mount for persistent data
│   │   ├── lms-codebase.xml        # Generated by repomix
│   │   └── cache/                  # Cached embeddings
│   └── docker-compose.yml          # Includes ai-search services
└── .env                            # API keys configuration
```

### Benefits of Integrated Structure
- **Single location**: Everything Docker-related stays in the docker/ directory
- **Consistent with existing structure**: Follows the pattern of other Docker services
- **Easy maintenance**: All Docker configs and services in one place
- **Clear separation**: Application code (ai-search/) separate from data (ai-search-data/)
- **No top-level clutter**: Keeps the repository root clean

## Simple Usage Example
```bash
# 1. Set up environment
echo "OPENAI_API_KEY=your-key-here" >> .env
echo "ANTHROPIC_API_KEY=your-key-here" >> .env

# 2. Start the system
docker compose up -d

# 3. Wait for services to be ready
docker compose logs -f ai_search

# 4. Use the search API
curl "http://localhost:8080/api/search?q=user+authentication"

# 5. Use from Claude Code via MCP
# MCP server automatically available at localhost:8081
```

## API Endpoints (Unified Container)

The AI-Search container exposes these REST API endpoints:

### Search Endpoints
```bash
# Semantic search
GET http://localhost:8080/api/search?q=user+authentication&limit=10

# Find similar code
POST http://localhost:8080/api/similar
{
  "code": "function authenticate($username, $password) { ... }",
  "limit": 5
}

# Get code context
GET http://localhost:8080/api/context/{chunk_id}

# Search by file pattern
GET http://localhost:8080/api/search/file?pattern=*.php&q=database+connection
```

### Management Endpoints
```bash
# Regenerate repomix output
POST http://localhost:8080/api/repomix/generate

# Update vectorization
POST http://localhost:8080/api/vectorize/update

# Get statistics
GET http://localhost:8080/api/stats

# Health check
GET http://localhost:8080/api/health
```

## Docker Commands (Simplified)
```bash
# Start AI-search system (just 2 containers)
docker compose up -d postgres_vector ai_search

# View logs
docker compose logs -f ai_search

# Regenerate code index (via API)
curl -X POST http://localhost:8080/api/repomix/generate
curl -X POST http://localhost:8080/api/vectorize/update

# Search for code
curl "http://localhost:8080/api/search?q=user+login&limit=5"

# Stop services
docker compose stop postgres_vector ai_search

# Clean up
docker compose down
docker volume rm lms_postgres_vector_data
```

## Success Metrics

### Functional Requirements
- ✅ Can search for "user authentication" and find relevant login functions
- ✅ Can search for "database queries" and find Eloquent models
- ✅ Can search for "API endpoints" and find route definitions
- ✅ Can find similar code based on provided snippets
- ✅ Claude Code can use all MCP tools successfully

### Non-Functional Requirements
- ✅ Search results in <1 second
- ✅ High relevance score (subjective evaluation)
- ✅ Full codebase coverage of key directories
- ✅ Reliable service startup and operation
- ✅ Easy maintenance and updates

## Implementation Timeline

| Phase | Estimated Time | Key Deliverables |
|-------|---------------|------------------|
| Phase 1 | 1-2 days | Repomix + Database setup |
| Phase 2 | 2-3 days | Vectorization service |
| Phase 3 | 2-3 days | MCP server development |
| Phase 4 | 1-2 days | Integration & testing |
| **Total** | **6-10 days** | **Working AI code search** |

## Risks & Mitigation

### Technical Risks
- **OpenAI API Limits**: Use batching, implement retry logic
- **Vector Search Performance**: Proper indexing, query optimization
- **Large Codebase Processing**: Incremental updates, chunking strategies
- **Docker Resource Usage**: Resource limits, cleanup strategies

### Operational Risks
- **API Key Management**: Secure environment variable handling
- **Data Persistence**: Regular backups, volume management
- **Service Dependencies**: Health checks, restart policies

## Future Enhancements

### Advanced Features
- **Code Change Detection**: Incremental updates when files change
- **Multi-Language Support**: JavaScript, CSS, configuration files
- **Advanced Filters**: By file type, modification date, author
- **Similarity Clustering**: Group related code patterns
- **Usage Analytics**: Track search patterns and optimize

### Integration Opportunities
- **IDE Plugins**: VS Code, PhpStorm extensions
- **CI/CD Integration**: Automated vectorization on code changes
- **Documentation Generation**: Auto-generate docs from vectorized code
- **Code Review**: Find similar patterns for code review suggestions

---

**Next Steps**: Begin with Phase 1 - Foundation Setup
- Install repomix and create configuration
- Set up PostgreSQL + pgvector in Docker
- Test basic infrastructure components