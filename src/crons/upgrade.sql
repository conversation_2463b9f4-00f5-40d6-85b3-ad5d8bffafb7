/*
Will figure how to run upgrade after each pull request, versioning, etc.
*/
/*
	Prefill structure with data, ID's are included, they won't change, can be moved in structure, but ID's don't change, if something is deleted, comment out, do not remove, might be some problems managing this later, but looking at how rarely if ever navigation items are updated, this should not be too big of a issue.
	Any suggestions -> bring it on.
*/
SET FOREIGN_KEY_CHECKS=0;
TRUNCATE `structure`;
REPLACE INTO `structure` (`id`, `name`,  `name_template`, `description`, `description_template`, `icon`, `color`, `key`, `parent_id`, `order`, `hidden`, `status`, `show_help`, `created_at`, `updated_at`) VALUES

	(103, 'Home', NULL, '%%home_menu__reports_description%%', NULL, 'svg/top_menu_admin_home.svg', '--menu-color-6', 'home', NULL, 0, 0, 0, 0, NULL, NULL),
		(104, 'Roles', NULL, NULL, NULL, NULL, NULL, 'home-roles', 103, 1, 0, 0, 0, NULL, NULL),
			(105, 'List', NULL, NULL, NULL, NULL, NULL, 'home-roles-list', 104, 2, 0, 0, 0, NULL, NULL),

	(106, 'Dashboard', NULL, NULL, NULL, 'svg/top_menu_admin_dashboard.svg', '--menu-color-6', 'charts', NULL, 3, 0, 0, 0, NULL, NULL),
		(107, 'Custom', NULL, NULL, NULL, NULL, NULL, 'charts-custom', 106, 1, 0, 0, !isSMCR!, NULL, NULL),
			(108, 'List', NULL, NULL, NULL, NULL, NULL, 'charts-custom-list', 107, 2, 0, 0, 0, NULL, NULL),
	(11116, 'Power BI Dashboard', NULL, NULL, NULL, 'svg/top_menu_power_bi.svg', 'C89134', 'powerbi-charts-main', NULL, 4, 0, 0, 0, NULL, NULL),
			(11118, 'Custom', NULL, NULL, NULL, NULL, NULL, 'powerbi-charts-custom', 11116, 1, 0, 0, 0, NULL, NULL),
				(11119, 'List', NULL, NULL, NULL, NULL, NULL, 'powerbi-charts', 11118, 1, 0, 0, 0, NULL, NULL),

	(1, 'Manage Learning', NULL, '%%manage_learning_desc%%', NULL, 'svg/top_menu_admin_manage.svg', '--menu-color-1', 'manage-learning', NULL, 4, 0, 0, 0, NULL, NULL),
		(2, 'Learning Resources', NULL, NULL, NULL, NULL, NULL, 'manage-learning-resources', 1, 2, 0, 0, 1, NULL, NULL),
			(3, '%%lessons_and_learning_resources%%', NULL, NULL, NULL, NULL, NULL, 'lessons-and-learning-resources', 2, 1, 0, 0, 0, NULL, NULL),

	(89, 'Check Learning', NULL, '%%check_learning_menu__reports_description%%', NULL, 'svg/top_menu_admin_manage.svg', '--menu-color-1', 'check-learning', NULL, 1, 0, 0, 0, NULL, NULL),
		(90, 'Learning Resources', NULL, NULL, NULL, NULL, NULL, 'check-learning-resources', 89, 1, 0, 0, 1, NULL, NULL),
			(91, 'Lessons and Learning Resources', NULL, NULL, NULL, NULL, NULL, 'check-lessons-and-learning-resources', 90, 1, 0, 0, 0, NULL, NULL),

			#(4, 'Graph', NULL, NULL, NULL, NULL, 'lessons-and-learning-resources-graph', 2, 2, 1, 0, 0, NULL, NULL),
			#(5, 'Calendar', NULL, NULL, NULL, NULL, 'lessons-and-learning-resources-calendar', 2, 3, 1, 0, 0, NULL, NULL),
			#(6, 'Help', NULL, NULL, NULL, NULL, 'lessons-and-learning-resources-help', 2, 4, 1, 0, 0, NULL, NULL),
		#(7, 'Learning %%programmes%%', NULL, NULL, NULL, NULL, 'manage-learning-programmes', 1, 1, 1, 0, 1, NULL, NULL),
			#(8, 'Learning %%programmes%%', NULL, NULL, NULL, NULL, 'manage-learning-programmes-list', 7, 1, 1, 0, 0, NULL, NULL),
			#(9, 'Graph', NULL, NULL, NULL, NULL, 'manage-learning-programmes-graph', 7, 2, 1, 0, 0, NULL, NULL),
			#(10, 'Calendar', NULL, NULL, NULL, NULL, 'manage-learning-programmes-calendar', 7, 3, 1, 0, 0, NULL, NULL),
			#(11, 'Help', NULL, NULL, NULL, NULL, 'manage-learning-programmes-help', 7, 4, 1, 0, 0, NULL, NULL),
	(77, 'Administer Learning', NULL, '%%administer_learning_menu__reports_description%%', NULL, 'svg/top_menu_admin_manage.svg', '--menu-color-1', 'administer-learning', NULL, 5, 0, 0, 0, NULL, NULL),
		(78, 'Learning Resources', NULL, NULL, NULL, NULL, NULL, 'administer-learning-resources', 77, 1, 0, 0, 1, NULL, NULL),
			(79, 'Lessons and Learning Resources', NULL, NULL, NULL, NULL, NULL, 'administer-lessons-and-learning-resources', 78, 1, 0, 0, 0, NULL, NULL),
	(12, 'Learning Library', NULL, NULL, NULL, 'svg/top_menu_admin_library.svg', '--menu-color-2', 'learning-library', NULL, 6, 0, 0, 0, NULL, NULL),
		(13, '%%learning_resources%%', NULL, 'Create, edit and assign lessons. Lessons involve a collection of learning resources packaged together which can be added to events.', NULL, NULL, NULL, 'library-learning-resources', 12, 1, 0, 0, 1, NULL, NULL),
			(14, '%%learning_resources%%', NULL, NULL, NULL, NULL, NULL, 'library-learning-resources-and-lessons', 13, 1, 0, 0, 0, NULL, NULL),
			(62, 'Learning Resources and Lessons Users', NULL, NULL, NULL, NULL, NULL, 'library-learning-resources-and-lessons-users', 13, 2, 1, 0, 0, NULL, NULL),
			/*(15, 'Help', NULL, NULL, NULL, NULL, 'library-learning-resources-and-lessons-help', 13, 3, 0, 0, 0, NULL, NULL),*/
		(117, '%%lessons%%', NULL, 'Create, edit and assign lessons. %%lessons%% include an collection of learning resources packaged together for self-paced
		learning or added to your calendar for the delivery of live smart lessons.', NULL, NULL, NULL, 'library-learning-lessons', 12, 2, 0, 0, 1, NULL, NULL),
			(118, '%%lessons%% data', NULL, NULL, NULL, NULL, NULL, 'library-learning-lessons-data', 117, 1, 0, 0, 0, NULL, NULL),
		(4124, 'Skills', NULL, 'These skills can be assigned directly or used to make up skills monitoring programmes, managers and users sign-off on these skills, you can add prerequisite learning to these skills and assign queries which will add/remove skills from certain kinds of learners. ', NULL, NULL, NULL, 'learning-skill', 12, 3, 0, 0, 1, NULL, NULL),
			(4125,'Skill Data',NULL,NULL,NULL,NULL,NULL,'library-learning-skill',4124,1,0,0,0,NULL,NULL),
			#(18, 'Help', NULL, NULL, NULL, NULL, 'standards-and-other-programmes-help', 16, 2, 0, 0, 0, NULL, NULL),
		(16, 'Learning %%programmes%%', NULL, '%%learning_programmes_menu__reports_description%%', NULL, NULL, NULL, 'library-learning-programmes', 12, 4, 0, 0, 1, NULL, NULL),
			(17, 'Standards and Other Programmes', NULL, NULL, NULL, NULL, NULL, 'standards-and-other-programmes', 16, 1, 0, 0, 0, NULL, NULL),
		(4116, 'Forms', NULL, 'Create and edit forms.', NULL, NULL, NULL, 'learning-library-forms', 12, 4, 0, 0, 1, NULL, NULL),
			(4118,'Custom Fields',NULL,NULL,NULL,NULL,NULL,'system-setup-custom-field',4116,1,0,0,0,NULL,NULL),
			/*(4127, 'Forms', NULL, NULL, NULL, NULL, NULL, 'library-learning-forms-data', 4116, 2, 0, 0, 0, NULL, NULL),*/
			(4128, 'Report Templates', NULL, NULL, NULL, NULL, NULL, 'library-learning-document-template-data', 4116, 3, 0, 0, 0, NULL, NULL),
			(4129, '%%workflow%%', NULL, NULL, NULL, NULL, NULL, 'library-learning-form-workflow-data', 4116, 4, 0, 0, 0, NULL, NULL),
			(4117, 'Form Data', NULL, NULL, NULL, NULL, NULL, 'library-learning-forms-data', 4116, 1, 0, 0, 0, NULL, NULL),
		/* (4120, 'Form Template', NULL, 'Create and edit Form Template', NULL, NULL, NULL, 'form-templates', 12, 5, 0, 0, 1, NULL, NULL), */
			(4121,'Form Template Data',NULL,NULL,NULL,NULL,NULL,'library-learning-document-template-data',4120,1,0,0,0,NULL,NULL),
		/*(4122, 'Form WorkFlow', NULL, 'Create and edit Form Work Flow', NULL, NULL, NULL, 'form-workflow', 12, 6, 0, 0, 1, NULL, NULL), */
			(4123,'Form WorkFlow Data',NULL,NULL,NULL,NULL,NULL,'library-learning-form-workflow-data',4122,1,0,0,0,NULL,NULL),
	(19, 'Report Builder', NULL, '%%report_builder_menu__reports_description%%', 'review-list.html', 'svg/assigned_reports.svg', '--menu-color-3', 'review', NULL, 7, 0, 0, 0, NULL, NULL),
		(20, 'Learning Resources', NULL, NULL, NULL, NULL, NULL, 'review-learning-resources', 19, 1, 0, 0, 1, NULL, NULL),
			(21, 'Data', NULL, NULL, NULL, NULL, NULL, 'review-learning-resources-data', 20, 1, 0, 0, 0, NULL, NULL),
			#(22, 'Chart', NULL, NULL, NULL, NULL, NULL, 'review-learning-resources-chart', 20, 2, 0, 0, 0, NULL, NULL),
			#(23, 'Help', NULL, NULL, NULL, NULL, 'review-learning-resources-help', 20, 3, 0, 0, 0, NULL, NULL),
		(24, 'Learning %%programmes%%', NULL, NULL, NULL, NULL, NULL, 'review-learning-programmes', 19, 2, 0, 0, 1, NULL, NULL),
			(25, 'Data', NULL, NULL, NULL, NULL, NULL, 'review-learning-programmes-data', 24, 1, 0, 0, 0, NULL, NULL),
			#(26, 'Chart', NULL, NULL, NULL, NULL, 'review-learning-programmes-chart', 24, 2, 0, 0, 0, NULL, NULL),
			#(27, 'Help', NULL, NULL, NULL, NULL, 'review-learning-programmes-help', 24, 3, 0, 0, 0, NULL, NULL),
		(4132, 'Data Tables', NULL, NULL, NULL, NULL, NULL, 'review-custom-reports', 19, 3, 0, 0, 1, NULL, NULL),
			(4131, 'Data', NULL, NULL, NULL, NULL, NULL, 'review-custom-reports-data', 4132, 1, 0, 0, 0, NULL, NULL),
		(63432, '%%custom_reports%%', NULL, NULL, NULL, NULL, NULL, 'custom-report', 19, 5, 0, 0, 1, NULL, NULL),
			(63433, 'Data', NULL, NULL, NULL, NULL, NULL, 'custom-report-data', 63432, 1, 0, 0, 0, NULL, NULL),
			#(26, 'Chart', NULL, NULL, NULL, NULL, 'review-custom-reports-chart', 24, 2, 0, 0, 0, NULL, NULL),
			#(27, 'Help', NULL, NULL, NULL, NULL, 'review-custom-reports-help', 24, 3, 0, 0, 0, NULL, NULL),
		#(63455, 'Quiz Analysis', NULL, NULL, NULL, NULL, NULL, 'quiz', 19, 7, 0, 0, 0, NULL, NULL),
			(63456, 'Data', NULL, NULL, NULL, NULL, NULL, 'quiz-analysis', 63455, 1, 0, 0, 0, NULL, NULL),
		(61, 'Print', NULL, NULL, NULL, NULL, NULL, 'review-print', 19, 0, 1, 0, 0, NULL, NULL),
	(191, '%%administration_menu__reports%%', NULL, '%%administration_menu__reports_description%%', NULL, 'svg/top_menu_admin_reports.svg', '--menu-color-3', 'reports', NULL, 6, 0, 0, 0, NULL, NULL),
		(201, '%%administration_menu__reports%%', NULL, NULL, NULL, NULL, NULL, 'reports-assigned', 191, 2, 0, 0, 1, NULL, NULL),
			(211, 'Data', NULL, NULL, NULL, NULL, NULL, 'reports-assigned-data', 201, 1, 0, 0, 0, NULL, NULL),
	(28, 'System Setup', NULL, NULL, NULL, 'svg/top_menu_admin_setup.svg', '--menu-color-5', 'system-setup', NULL, 8, 0, 0, 0, NULL, NULL),
		(29, 'Organisation', NULL, '%%organisation_menu__reports_description%%', NULL, NULL, NULL, 'system-setup-organisation', 28, 1, 0, 0, 1, NULL, NULL),
			(30, '%%users%%', NULL, NULL, NULL, NULL, NULL, 'system-setup-organisation-users', 29, 1, 0, 0, 0, NULL, NULL),
			(31, '%%countries%%', NULL, NULL, NULL, NULL, NULL, 'system-setup-organisation-countries', 29, 2, 0, 0, 0, NULL, NULL),
			(32, '%%cities%%', NULL, NULL, NULL, NULL, NULL, 'system-setup-organisation-cities', 29, 3, 0, 0, 0, NULL, NULL),
			(33, '%%companies%%', NULL, NULL, NULL, NULL, NULL, 'system-setup-organisation-companies', 29, 4, 0, 0, 0, NULL, NULL),
			(34, '%%departments%%', NULL, NULL, NULL, NULL, NULL, 'system-setup-organisation-departments', 29, 5, 0, 0, 0, NULL, NULL),
			(35, '%%jobs%%', NULL, NULL, NULL, NULL, NULL, 'system-setup-organisation-jobs', 29, 6, 0, 0, 0, NULL, NULL),
			(5015, 'Career Paths', NULL, NULL, NULL, NULL, NULL, 'system-setup-career-path', 29, 7, 0, 0, 0, NULL, NULL),
			(36, '%%locations%%', NULL, NULL, NULL, NULL, NULL, 'system-setup-organisation-locations', 29, 8, 0, 0, 0, NULL, NULL),
			(37, '%%groups%%', NULL, NULL, NULL, NULL, NULL, 'system-setup-organisation-groups', 29, 9, 0, 0, 0, NULL, NULL),
			(38, '%%managers%%', NULL, NULL, NULL, NULL, NULL, 'system-setup-organisation-coach-trainers', 29, 10, 0, 0, 0, NULL, NULL),
			(39, 'Roles', NULL, NULL, NULL, NULL, NULL, 'system-setup-organisation-roles', 29, 11, 0, 0, 0, NULL, NULL),
			(84, 'SMCR Staff Type', NULL, NULL, NULL, NULL, NULL, 'system-setup-organisation-staff-type', 29, 12, 1, 0, 0, NULL, NULL),
			(82, 'Functions', NULL, NULL, NULL, NULL, NULL, 'system-setup-organisation-functions', 29, 13, 0, 0, 0, NULL, NULL),
			(83, 'Responsibilities', NULL, NULL, NULL, NULL, NULL, 'system-setup-organisation-responsibilities', 29, 14, 0, 0, 0, NULL, NULL),
			(85, 'Committees', NULL, NULL, NULL, NULL, NULL, 'system-setup-organisation-committees', 29, 15, 0, 0, 0, NULL, NULL),
			(86, 'Committee Roles', NULL, NULL, NULL, NULL, NULL, 'system-setup-organisation-committee-roles', 29, 16, 1, 0, 0, NULL, NULL),
			(88, 'Committee Role Personnel', NULL, NULL, NULL, NULL, NULL, 'system-setup-organisation-committee-role-person', 29, 17, 1, 0, 0, NULL, NULL),
			(110, 'F&P Categories', NULL, NULL, NULL, NULL, NULL, 'system-setup-f-p-categories', 29, 18, 0, 0, 0, NULL, NULL),
			(122, 'Venues', NULL, NULL, NULL, NULL, NULL, 'system-setup-organisation-venues', 29, 19, 0, 0, 0, NULL, NULL),
			#(40, 'Help', NULL, NULL, NULL, NULL, 'system-setup-organisation-help', 29, 11, 0, 0, 0, NULL, NULL),
			(2901, 'Watch', NULL, NULL, NULL, NULL, NULL, 'system-setup-organisation-watch', 29, 20, 0, 0, 0, NULL, NULL),

		(41, 'Learning', NULL, '%%learning_menu__reports_description%%', NULL, NULL, NULL, 'system-setup-learning', 28, 2, 0, 0, 1, NULL, NULL),
			(42, 'Learning Categories', NULL, NULL, NULL, NULL, NULL, 'system-setup-learning-categories', 41, 1, 0, 0, 0, NULL, NULL),
			(464, 'Upload Types', NULL, NULL, NULL, NULL, NULL, 'system-setup-upload-types', 41, 1, 0, 0, 0, NULL, NULL),
			(43, 'Learning Providers', NULL, NULL, NULL, NULL, NULL, 'system-setup-learning-providers', 41, 2, 0, 0, 0, NULL, NULL),
			(44, 'Learning Resource Maintenance', NULL, NULL, NULL, NULL, NULL, 'system-setup-learning-maintenance', 41, 3, 0, 0, 0, NULL, NULL),
			(441, 'Learning Results', NULL, NULL, NULL, NULL, NULL, 'system-setup-learning-results', 41, 4, 0, 0, 0, NULL, NULL),
			(45, 'Competencies', NULL, NULL, NULL, NULL, NULL, 'system-setup-learning-competencies', 41, 5, 0, 0, 0, NULL, NULL),
			(462, 'Custom Programme Statuses', NULL, NULL, NULL, NULL, NULL, 'system-setup-custom-programme-statuses', 41, 6, 0, 0, 0, NULL, NULL),
			(463, 'Learning Resource Types', NULL, NULL, NULL, NULL, NULL, 'system-setup-custom-learning-resources-types', 41, 10, 0, 0, 0, NULL, NULL),
			#(66, 'Help', NULL, NULL, NULL, NULL, 'system-setup-learning-help', 41, 6, 0, 0, 0, NULL, NULL),
			(59, 'Feedback', NULL, NULL, NULL, NULL, NULL, 'system-setup-learning-feedback', 41, 7, 0, 0, 0, NULL, NULL),
			(4160, 'Rejection Reasons', NULL, NULL, NULL, NULL, NULL, 'system-setup-rejection-reasons', 41, 11, 0, 0, 0, NULL, NULL),
			(76, 'e-Learning Distribution', NULL, NULL, NULL, NULL, NULL, 'system-setup-learning-distribution', 41, 8, 0, 0, 0, NULL, NULL),
			(80, 'Free Jackdaw resources', NULL, NULL, NULL, NULL, NULL, 'system-setup-learning-free-jackdaw-resources', 41, 9, 0, 0, 0, NULL, NULL),
			(63435, 'Coupons', NULL, NULL, NULL, NULL, NULL, 'system-setup-custom-learning-coupons', 41, 10, 0, 0, 0, NULL, NULL),
			(471, 'Creator Media Library', NULL, NULL, NULL, NULL, NULL, 'system-setup-creator-media-library', 41, 11, 0, 0, 0, NULL, NULL),
			(63458, 'Signposting', NULL, NULL, NULL, NULL, NULL, 'system-setup-sign-posting', 41, 12, 0, 0, 0, NULL, NULL),

			(4190, '%%target_catalogue%%', NULL, NULL, NULL, NULL, NULL, 'system-setup-learning-target-catalogue', 41, 12, 0, 0, 0, NULL, NULL),
			(4192, '%%delivery_provider_type%%', NULL, NULL, NULL, NULL, NULL, 'system-setup-learning-delivery-provider-type', 41, 13, 0, 0, 0, NULL, NULL),
			(4194, '%%group_department_code%%', NULL, NULL, NULL, NULL, NULL, 'system-setup-learning-group-department-code', 41, 14, 0, 0, 0, NULL, NULL),

		(411, '%%events%%', NULL, 'Manage %%events%% related data.', NULL, NULL, NULL, 'system-setup-events', 28, 3, 0, 0, 1, NULL, NULL),
			(116, '%%event%% Types', NULL, NULL, NULL, NULL, NULL, 'system-setup-event-types', 411, 1, 0, 0, 0, NULL, NULL),
			(4113, '%%event%% Visit Types', NULL, NULL, NULL, NULL, NULL, 'system-setup-event-visit-types', 411, 2, 0, 0, 0, NULL, NULL),


		(46, 'Defaults', NULL, '%%defaults_menu__reports_description%%', NULL, NULL, NULL, 'system-setup-defaults', 28, 4, 0, 0, 1, NULL, NULL),
			(47, 'Emails', NULL, NULL, NULL, NULL, NULL, 'system-setup-defaults-emails', 46, 1, 0, 0, 0, NULL, NULL),
			(48, 'Timings', NULL, NULL, NULL, NULL, NULL, 'system-setup-defaults-timings', 46, 2, 0, 0, 0, NULL, NULL),
			(111, 'Holidays', NULL, NULL, NULL, NULL, NULL, 'system-setup-defaults-holidays', 46, 3, 0, 0, 0, NULL, NULL),
			(49, 'Labels', NULL, NULL, NULL, NULL, NULL, 'system-setup-defaults-labels', 46, 4, 0, 0, 0, NULL, NULL),
			(461, 'Default Labels', NULL, NULL, NULL, NULL, NULL, 'system-setup-defaults-default-labels', 46, 5, 0, 0, 0, NULL, NULL),
			(50, 'Settings', NULL, NULL, NULL, NULL, NULL, 'system-setup-defaults-configuration', 46, 6, 0, 0, 0, NULL, NULL),
            (63444, 'Integration', NULL, NULL, NULL, NULL, NULL, 'system-setup-defaults-integration', 46, 6, 0, 0, 0, NULL, NULL),

			(51, 'API Setup', NULL, NULL, NULL, NULL, NULL, 'system-setup-defaults-api-setup', 46, 7, 0, 0, 0, NULL, NULL),
			(101, 'Dashboards', NULL, NULL, NULL, NULL, NULL, 'system-setup-defaults-dashboards', 46, 8, 0, 0, 0, NULL, NULL),
            (4110, 'Power BI Dashboards', NULL, NULL, NULL, NULL, NULL, 'system-setup-defaults-power-bi-dashboards', 46, 9, 0, 0, 0, NULL, NULL),
			#(102, 'Learner\'s interface', NULL, NULL, NULL, NULL, NULL, 'system-setup-learners-interface', 46, 9, 0, 0, 0, NULL, NULL),
			(109, 'Cron Jobs', NULL, NULL, NULL, NULL, NULL, 'system-setup-cron-jobs', 46, 13, 0, 0, 0, NULL, NULL),
			(120, 'Branding', NULL, NULL, NULL, NULL, NULL, 'system-setup-branding', 46, 14, 0, 0, 0, NULL, NULL),
			(124, 'Registration Form', NULL, NULL, NULL, NULL, NULL, 'system-setup-registration-form', 46, 15, 0, 0, 0, NULL, NULL),
			(465, 'History', NULL, NULL, NULL, NULL, NULL, 'system-setup-table-history', 46, 16, 0, 0, 0, NULL, NULL),
			(4119,'Form Queries',NULL,NULL,NULL,NULL,NULL,'system-setup-saved-queries',46,17,0,0,0,NULL,NULL),
			(5119,'Payments Engines',NULL,NULL,NULL,NULL,NULL,'system-setup-defaults-payment-engines',46,18,0,0,0,NULL,NULL),
            (63442, 'Stripe Subscriptions', NULL, NULL, NULL, NULL, NULL, 'system-setup-defaults-stripe-subscriptions', 46, 19, 0, 0, 0, NULL, NULL),
            (63443, 'Stripe Licenses', NULL, NULL, NULL, NULL, NULL, 'system-setup-defaults-stripe-licenses', 46, 20, 0, 0, 0, NULL, NULL),

		(112, 'API\'s', NULL, '%%api_menu__reports_description%%', NULL, NULL, NULL, 'system-setup-api', 28, 5, 0, 0, 1, NULL, NULL),
			(113, 'GO1', NULL, NULL, NULL, NULL, NULL, 'system-setup-api-go1', 112, 1, 0, 0, 0, NULL, NULL),
			(114, 'Anders Pink', NULL, NULL, NULL, NULL, NULL, 'system-setup-api-anders-pink', 112, 2, 0, 0, 0, NULL, NULL),
			(115, 'Salesforce', NULL, NULL, NULL, NULL, NULL, 'system-setup-api-salesforce', 112, 3, 0, 0, 0, NULL, NULL),
			#(119, 'Credas', NULL, NULL, NULL, NULL, NULL, 'system-setup-api-credas', 112, 4, 0, 0, 0, NULL, NULL),
			(121, 'Badgr', NULL, NULL, NULL, NULL, NULL, 'system-setup-api-badgr', 112, 5, 0, 0, 0, NULL, NULL),

		(500, 'Audit', NULL, 'Audit.', NULL, NULL, NULL, 'system-setup-audit', 28, 6, 0, 0, 1, NULL, NULL),
			(501, 'Learning Results', NULL, NULL, NULL, NULL, NULL, 'system-setup-learning-results', 500, 1, 0, 0, 0, NULL, NULL),
			(5012, '%%lesson_resources%% Assignment', NULL, NULL, NULL, NULL, NULL, 'system-setup-resource-assignment', 500, 2, 0, 0, 0, NULL, NULL),
			(502, 'Email Log', NULL, NULL, NULL, NULL, NULL, 'system-setup-audit-email-logs', 500, 3, 0, 0, 0, NULL, NULL),
			(5021, 'Email Queue', NULL, NULL, NULL, NULL, NULL, 'system-setup-audit-email-queue', 500, 4, 0, 0, 0, NULL, NULL),
			(465, 'Table data History', NULL, NULL, NULL, NULL, NULL, 'system-setup-table-history', 500, 5, 0, 0, 0, NULL, NULL),
			(4112, 'Deleted %%events%%', NULL, NULL, NULL, NULL, NULL, 'system-setup-deleted-events', 500, 6, 0, 0, 0, NULL, NULL),
			(4114, '%%event%% items', NULL, NULL, NULL, NULL, NULL, 'system-setup-event-items', 500, 7, 0, 0, 0, NULL, NULL),
			(5013, 'Failed login attempts', NULL, NULL, NULL, NULL, NULL, 'system-setup-audit-failed-login-attempts', 500, 8, 0, 0, 0, NULL, NULL),
			(5014, 'Form Logs', NULL, NULL, NULL, NULL, NULL, 'system-setup-form-logs', 500, 9, 0, 0, 0, NULL, NULL),
			(5022, 'Request Log', NULL, NULL, NULL, NULL, NULL, 'system-setup-audit-request-log', 500, 10, 0, 0, 0, NULL, NULL),
			(63442,'Mapping to Programme Log', NULL, NULL, NULL, NULL, NULL, 'system-setup-audit-mapping-to-programme-log', 500, 11, 0, 0, 0, NULL, NULL),
			(63436,'Import Logs',NULL,NULL,NULL,NULL,NULL,'system-setup-import-logs',500,12,0,0,0,NULL,NULL),
            		(63437,'Integration API Requests',NULL,NULL,NULL,NULL,NULL,'system-setup-integration-api-requests',500,13,0,0,0,NULL,NULL),
            		(63438,'Payment transactions',NULL,NULL,NULL,NULL,NULL,'system-setup-payment-transactions',500,14,0,0,0,NULL,NULL),
			(63439, 'Javascript Errors', NULL, NULL, NULL, NULL, NULL, 'system-setup-javascript-errors', 500, 15, 0, 0, 0, NULL, NULL),
			(63440, 'Disk Space', NULL, NULL, NULL, NULL, NULL, 'system-setup-audit-disk-space', 500, 14, 0, 0, 0, NULL, NULL),


	(52, 'Trainee', NULL, NULL, NULL, NULL, NULL, 'trainee', NULL, 5, 1, 0, 0, NULL, NULL),
		(53, 'Learning resources', NULL, NULL, NULL, NULL, NULL, 'trainee-modules', 52, 1, 1, 0, 0, NULL, NULL),
		(54, 'Learning results', NULL, NULL, NULL, NULL, NULL, 'trainee-learning-results', 52, 2, 1, 0, 0, NULL, NULL),
		(55, 'Standards', NULL, NULL, NULL, NULL, NULL, 'trainee-standards', 52, 3, 1, 0, 0, NULL, NULL),
		(56, 'Feedback', NULL, NULL, NULL, NULL, NULL, 'trainee-feedback', 52, 4, 1, 0, 0, NULL, NULL),
		(57, 'Calendar', NULL, NULL, NULL, NULL, NULL, 'trainee-calendar', 52, 5, 1, 0, 0, NULL, NULL),
		(58, 'Upload Resources', NULL, NULL, NULL, NULL, NULL, 'trainee-upload-resources', 52, 6, 1, 0, 0, NULL, NULL),
		(60, 'Edit Profile', NULL, NULL, NULL, NULL, NULL, 'trainee-edit-profile', 52, 7, 1, 0, 0, NULL, NULL),
		(81, 'Book Training', NULL, NULL, NULL, NULL, NULL, 'trainee-book-training', 52, 8, 1, 0, 0, NULL, NULL),
	(63, 'Misc Permissions', NULL, NULL, NULL, NULL, NULL, 'misc-permissions', NULL, 6, 1, 0, 0, NULL, NULL),
		(64, 'My Profile', NULL, NULL, NULL, NULL, NULL, 'misc-permissions-my-profile', 63, 1, 1, 0, 0, NULL, NULL),
		(65, 'Change Password', NULL, NULL, NULL, NULL, NULL, 'misc-permissions-change-password', 63, 2, 1, 0, 0, NULL, NULL),
		(67, 'Export ILR', NULL, NULL, NULL, NULL, NULL, 'misc-permissions-export-ilr', 63, 3, 1, 0, 0, NULL, NULL),
		(68, 'Enroll Learning Modules', NULL, NULL, NULL, NULL, NULL, 'misc-permissions-enroll-learning-modules', 63, 4, 1, 0, 0, NULL, NULL),
		(69, 'Apprenticeship Routes', NULL, NULL, NULL, NULL, NULL, 'misc-permissions-apprenticeship-routes', 63, 5, 1, 0, 0, NULL, NULL),
		(70, 'Import %%users%%', NULL, NULL, NULL, NULL, NULL, 'misc-permissions-import-users', 63, 6, 1, 0, 0, NULL, NULL),
		(71, 'Change offline task status', NULL, NULL, NULL, NULL, NULL, 'misc-permissions-change-offline-task-status', 63, 7, 1, 0, 0, NULL, NULL),
		(72, 'Refresh Learning', NULL, NULL, NULL, NULL, NULL, 'misc-permissions-refresh-learning', 63, 8, 1, 0, 0, NULL, NULL),
		(73, 'User Managers', NULL, NULL, NULL, NULL, NULL, 'misc-permissions-user-managers', 63, 9, 1, 0, 0, NULL, NULL),
		(87, 'Evidence Types', NULL, NULL, NULL, NULL, NULL, 'misc-permissions-evidence-types', 63, 10, 1, 0, 0, NULL, NULL),
		(92, 'QA permission', NULL, NULL, NULL, NULL, NULL, 'misc-permissions-qa', 63, 11, 1, 0, 0, NULL, NULL),
		(93, 'Learning Results Comments', NULL, NULL, NULL, NULL, NULL, 'misc-permissions-learning-results-comments', 63, 12, 1, 0, 0, NULL, NULL),
		(94, 'Reviews', NULL, NULL, NULL, NULL, NULL, 'misc-permissions-reviews', 63, 13, 1, 0, 0, NULL, NULL),
		(95, 'SMCR Reports', NULL, NULL, NULL, NULL, NULL, 'misc-permissions-smcr-reports', 63, 14, 1, 0, 0, NULL, NULL),
		(96, 'Jackdaw instance', NULL, NULL, NULL, NULL, NULL, 'misc-permissions-jackdaw-instance', 63, 15, 1, 0, 0, NULL, NULL),
		(97, 'Task Assesments', NULL, NULL, NULL, NULL, NULL, 'misc-permissions-task-assesments', 63, 16, 1, 0, 0, NULL, NULL),
		(98, 'File Uploads', NULL, NULL, NULL, NULL, NULL, 'misc-permissions-file-uploads', 63, 17, 1, 0, 0, NULL, NULL),
		(99, 'Comments', NULL, NULL, NULL, NULL, NULL, 'misc-permissions-comments', 63, 18, 1, 0, 0, NULL, NULL),
		(100, 'Meetings', NULL, NULL, NULL, NULL, NULL, 'misc-permissions-meetings', 63, 19, 1, 0, 0, NULL, NULL),
		(631, 'Assignments', NULL, NULL, NULL, NULL, NULL, 'misc-permissions-assignments', 63, 20, 1, 0, 0, NULL, NULL),
        (63441, 'Query Builders', NULL, NULL, NULL, NULL, NULL, 'misc-permissions-query-builders', 63, 21, 1, 0, 0, NULL, NULL),
	(74, 'API permissions', NULL, NULL, NULL, NULL, NULL, 'api-permissions', NULL, 7, 1, 0, 0, NULL, NULL),
		(75, 'Containers', NULL, NULL, NULL, NULL, NULL, 'api-permissions-containers', 74, 1, 1, 0, 0, NULL, NULL),

		   /** (63429, 'Manage Graph', NULL, 'manage-graph', NULL, 'svg/top_menu_admin_manage.svg', 'cc0000', 'manage-graph', NULL, 5, 0, 0, 0, NULL, NULL),**/
			(63430, 'Graph', NULL, '%%graph_menu__reports_description%%', NULL, NULL, NULL, 'learning-library-graphs', 19, 4, 0, 0, 1, NULL, NULL),
			(63431, 'Graph Data', NULL, NULL, NULL, NULL, NULL, 'library-learning-graphs-data', 63430, 1, 0, 0, 0, NULL, NULL)
;
SET FOREIGN_KEY_CHECKS=1;
