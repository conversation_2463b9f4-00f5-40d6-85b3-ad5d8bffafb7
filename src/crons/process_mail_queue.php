#!/usr/bin/php
<?php
// die('remove this line to proceed'); // Commented out for testing
chdir(__DIR__);
include __DIR__ . "/setup.php";

use APP\Controllers\ImportController;
use APP\Controllers\ImportUserController;
use Models\ImportLog;
use Models\ImportLogError;
use Models\LearningModuleVectorizedData;
use Models\Schedule;
use Models\UserPaymentTransaction;

// Test function for Bradford import
// function testBradfordImport($settings) {
//     $testFile = '/var/www/html/src/temp/test_import/test_bradford_users.csv';

//     echo "Testing Bradford Import...\n";
//     echo "File: $testFile\n";

//     if (!file_exists($testFile)) {
//         return "Test file not found: $testFile";
//     }

//     try {
//         $result = ImportUserController::importBradford($settings, $testFile, [], false);
//         return [
//             'success' => true,
//             'file' => $testFile,
//             'result' => $result
//         ];
//     } catch (Exception $e) {
//         return [
//             'success' => false,
//             'error' => $e->getMessage(),
//             'trace' => $e->getTraceAsString(),
//             'file' => $testFile
//         ];
//     }
// }

// Run the Bradford import test
// $result = testBradfordImport($settings);
// echo "Bradford Import Test Result:\n";
// echo json_encode($result, JSON_PRETTY_PRINT);
// echo "\n";

		return \Models\UserDownload::processAll();
/*
// Original functions commented out for testing
// cronImportHRandLearningResults_S($settings);
//UserPaymentTransaction::sendCivicaDataMail();
//return \APP\Email::processEmailQueue($settings['email'], $logger, $settings['LMSUrl']);
//\Models\ApprenticeshipStandardUser::processRemoveAssigmentToDesignationUsers($settings);
//LearningModuleVectorizedData::vectorizeData($settings);

//\Models\Schedule::processEvents($settings,1048);

try {
	$n_emails = \APP\Email::processEmailQueue($settings['email'], $logger, $settings['LMSUrl']);
	// $logger->info("$n_emails emails sent.");
} catch(\Exception $e) {
	$logger->error($e->getMessage());
}
*/

