<?php

declare(strict_types=1);

use DI\ContainerBuilder;
use Slim\Factory\AppFactory;
use Slim\Factory\ServerRequestCreatorFactory;
use Illuminate\Container\Container;
use Illuminate\Database\Capsule\Manager as Capsule;
use Illuminate\Events\Dispatcher;
use Illuminate\Queue\Capsule\Manager as Queue;
use APP\Cache\FailoverCacheManager;
use Illuminate\Cache\CacheManager;
use Illuminate\Filesystem\Filesystem;
use Illuminate\Config\Repository as ConfigRepository;
use Illuminate\Container\Container as LaravelContainer;
use Illuminate\Redis\RedisManager;


header('P3P: CP="Potato"');


ini_set('session.cookie_httponly', '1');
ini_set('session.cookie_secure', '1');

require '../vendor/autoload.php';

// Instantiate PHP-DI ContainerBuilder
$containerBuilder = new ContainerBuilder();

// Set up settings and add capsule in container as well here!
$settings = require __DIR__ . '/../app/settings.php';
$settings($containerBuilder);

// Set up dependencies
$dependencies = require __DIR__ . '/../app/dependencies.php';
$dependencies($containerBuilder);

// Set up repositories
$repositories = require __DIR__ . '/../app/repositories.php';
$repositories($containerBuilder);

// Build PHP-DI Container instance
$container = $containerBuilder->build();
$settings = $container->get('settings');
\APP\ContainerRegistry::set($container);


$container->set('files', function () {
    return new Filesystem();
});

$redisConfig = [
    'client' => 'predis',
    'default' => [
        'host' => $settings['redis']['host'],
        'password' => $settings['redis']['password'],
        'port' => $settings['redis']['port'],
        'database' => $settings['redis']['database'],
    ],
];

$container->set('redis', function () use ($redisConfig) {
    return new RedisManager(new Illuminate\Container\Container(), $redisConfig['client'], $redisConfig);
});

$container->set('config', function () use ($settings, $redisConfig) {
    return new ConfigRepository([
        'cache.default' => 'redis',
        'cache.stores.redis' => [
            'driver' => 'redis',
            'connection' => 'default',
        ],
        'cache.stores.file' => [
            'driver' => 'file',
            'path' => __DIR__ . '/../cache',
        ],
        'cache.prefix' => preg_replace('/[^a-zA-Z0-9_]/', '_', $settings['database']['database']),
        'database.redis' => $redisConfig,
    ]);
});

$container->set('laravel_container', function () {
    return new LaravelContainer();
});

$container->set('cache', function ($c) {
    $config = $c->get('config');
    $laravelContainer = $c->get('laravel_container');

    $laravelContainer->instance('config', $config);
    $laravelContainer->instance('files', $c->get('files'));
    $laravelContainer->instance('redis', $c->get('redis'));

    $cacheManager = new CacheManager($laravelContainer);

    $primary = $cacheManager->store('redis');
    $fallback = $cacheManager->store('file');

    return new FailoverCacheManager($primary, $fallback);
});

if (!function_exists('cache')) {
    function cache() {
        global $container;
        return $container->get('cache');
    }
}

\APP\LoggerHelper::setLogger($container->get(Psr\Log\LoggerInterface::class));



$cookiePath = (isset($settings['LMSUri']) && $settings['LMSUri'] !== '/')
    ? rtrim(parse_url($settings['LMSUri'], PHP_URL_PATH) ?? '/', '/') . '/'
    : '/';

session_set_cookie_params([
    'lifetime' => 0,
    'path' => $cookiePath,
    'secure' => true,
    'httponly' => true,
    'samesite' => 'None', // Optional, modern browsers support this
]);

// Handle PHPSESSID override (if present)
if (isset($_REQUEST["PHPSESSID"])) {
    session_id($_REQUEST["PHPSESSID"]);
}

session_start();

// Instantiate the app
AppFactory::setContainer($container);

// Set view in Container
$container->set('view', function($c) use ($settings) {
    return new \Slim\Views\PhpRenderer($settings["LMSTplsPath"]);
});

$app = AppFactory::create();
if (strlen($settings["LMSUri"]) > 0) {
    $basePath = rtrim($settings["LMSUri"], '/');
    $app->setBasePath($basePath);
}
$callableResolver = $app->getCallableResolver();

// Register middleware
$middleware = require __DIR__ . '/../app/middleware.php';
$middleware($app);

// Register routes
$routes = require __DIR__ . '/../app/routes.php';
$routes($app);

$GLOBALS["CONFIG"] = new \APP\Config($settings);

if (\APP\Tools::getConfig('allowApi')) {
    if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
        if (isset($_SERVER['HTTP_ORIGIN'])) {
            header("Access-Control-Allow-Origin: {$_SERVER['HTTP_ORIGIN']}");
            header('Access-Control-Allow-Credentials: true');
            header('Access-Control-Max-Age: 86400');
        }
        if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_METHOD'])) {
            header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
        }
        if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS'])) {
            header("Access-Control-Allow-Headers: {$_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS']}");
        }
        exit(0);
    }
}

$ipBlock = \APP\Tools::getConfig('ipBlock');
if (
    $ipBlock &&
    (
        is_array($ipBlock) ||
        is_object($ipBlock)
    ) &&
    count($ipBlock) > 0
) {
    $ip = \APP\Auth::getIp();
    if ($ip) {
        foreach ($ipBlock as $ip_range_key => $ip_range) {
            if (\APP\Tools::ipInRange($ip, $ip_range)) {
                header('HTTP/1.0 403 Forbidden');
                die('403 Forbidden');
            }
        }
    }
}

// Add Routing Middleware
$app->addRoutingMiddleware();

// Add Body Parsing Middleware
$app->addBodyParsingMiddleware();

$errorHandler = require __DIR__ . '/../app/logs.php';

$customErrorHandler = $errorHandler($container, $app);

$errorMiddleware = $app->addErrorMiddleware(true, true, true);
$errorMiddleware->setDefaultErrorHandler($customErrorHandler);

// Example route to enqueue a job
$app->get('/enqueue', function ($request, $response, $args) {
    $queue = $this->get(Queue::class);
    $queue->push(new \App\Jobs\ExampleJob('Task data'));
    return $response->write('Task enqueued');
});

// Run app
$app->run();
