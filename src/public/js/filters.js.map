{"version": 3, "sources": ["baddatetoiso.js", "bytesToMB.js", "capitalize.js", "createarray.js", "datesuffix.js", "datetoiso.js", "dotsFilter.js", "escape.js", "fix2.js", "humanNumber.js", "ilr-learning-delivery-fields.js", "learner-tasks-filter.js", "learning-results-filter.js", "manager-names.js", "my-strict-filter.js", "nl2br.js", "num.js", "pack-and-go-filter.js", "randomsrc.js", "readable-minutes.js", "readable-time.js", "schedule-filter.js", "select-learning-resource-filter.js"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACTA;AACA;AACA;AACA;AACA;ACJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACtBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AClBA;AACA;AACA;AACA;AACA;AACA;ACLA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACrBA;AACA;AACA;ACFA;AACA;AACA;AACA;AACA;ACJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACRA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACtBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACtHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACjEA;AACA;AACA;AACA;AACA;AACA;AACA;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACnGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACRA;AACA;AACA;AACA;AACA;AACA;ACLA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACjCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACjBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACtBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACxCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "filters.js", "sourcesContent": ["angular.module('lmsApp').filter('badDateToISO', function() {\n\treturn function(badTime) {\n\t\tif (badTime) {\n\t\t\tvar goodTime = badTime.replace(/(.+) (.+)/, \"$1T$2Z\");\n\t\t\treturn goodTime;\n\t\t}\n\t};\n});", "angular.module('lmsApp').filter('bytesToMB', function() {\n    return function(bytes) {\n      if (isNaN(parseFloat(bytes)) || !isFinite(bytes) || bytes === null || bytes === undefined) {\n        return '-'; // Or any other default value for invalid input\n      }\n\n      var megabytes = bytes / (1024 * 1024); // Convert bytes to MB\n      return megabytes.toFixed(2) + ' MB'; // Format to 2 decimal places and add \"MB\"\n    };\n  });", "angular.module('lmsApp').filter('capitalize', function() {\n    return function(input) {\n      return (angular.isString(input) && input.length > 0) ? input.charAt(0).toUpperCase() + input.substr(1).toLowerCase() : input;\n    }\n});", "/*\nmeant to iterate over objects properties property, if object have multiple same properties with multiple same subproperties and all you want to do to iterate over subproperties, this will help!\n\texample:\n\t\tordersList.orders = [{\n\t\t\tupid: 1\n\t\t},\n\t\t{\n\t\t\tupid: 2\n\t\t}]\n\n<li ng-repeat=\"o in ordersList.orders | createarray: 'parcels'\">{{o.upid}}</li>\n*/\nangular.module('lmsApp').filter('createarray', function () {\n\treturn function (value, propertyName) {\n\t\tvar arrayList = [];\n\t\tangular.forEach(value, function (val) {\n\t\t\tangular.forEach(val[propertyName], function (v) {\n\t\t\t\tarrayList.push(v);\n\t\t\t});\n\t\t});\n\t\treturn arrayList;\n\t};\n});", "angular.module('lmsApp').filter('dateSuffix', function($filter) {\n\tvar suffixes = [\"th\", \"st\", \"nd\", \"rd\"];\n\treturn function(input) {\n\t\tif (input) {\n\t\t\tvar dtfilter = $filter('date')(input, 'd'),\n\t\t\t\tday,\n\t\t\t\trelevantDigits,\n\t\t\t\tsuffix\n\t\t\t;\n\t\t\tif (dtfilter) {\n\t\t\t\t\tday = parseInt(dtfilter.toString().slice(-2), 10);\n\t\t\t\t\trelevantDigits = (day < 30) ? day % 20 : day % 30;\n\t\t\t\t\tsuffix = (relevantDigits <= 3) ? suffixes[relevantDigits] : suffixes[0];\n\t\t\t\treturn dtfilter+suffix;\n\t\t\t}\n\n\t\t}\n\t};\n});", "angular.module('lmsApp').filter('dateToISO', function() {\n\treturn function(input) {\n\t\tinput = new Date(input);\n\t\treturn input;\n\t};\n});", "angular.module('lmsApp').filter('dotsFilter', [\n    '$filter',\n    function ($filter) {\n        /**\n         * Shorten the input and add dots if it's needed\n         * @param {string} input\n         * @param {number} limit\n         */\n        function dotsFilter(input, limit) {\n            if (\n                input &&\n                limit\n            ) {\n                input = input.replace(/<[^>]+>/gm, '');\n                var newContent = $filter('limitTo')(input, limit);\n                if (newContent.length < input.length) { newContent += '...'; }\n                return newContent;\n            }\n        }\n        return dotsFilter;\n    }\n]);", "angular.module('lmsApp').filter('escape', function() {\n\treturn window.encodeURIComponent;\n});", "angular.module('lmsApp').filter('fix2', function () {\n\treturn function (number, pre) {\n\t\treturn parseFloat(number).toFixed(pre);\n\t};\n});", "angular.module('lmsApp').filter('humanNumber', function () {\n\treturn function (num) {\n\t\tif (num === null || num === undefined || isNaN(num)) return '';\n\t\tnum = Number(num);\n\t\tif (num >= 1e6) return (num / 1e6).toFixed(1).replace(/\\.0$/, '') + 'M';\n\t\tif (num >= 1e3) return (num / 1e3).toFixed(1).replace(/\\.0$/, '') + 'K';\n\t\treturn num.toLocaleString(); // fallback, adds comma for 1,000+\n\t};\n});", "/*\nWill look into default for specific learning delivery record and restrict results for select boxes or any other uses.\n*/\nangular.module('lmsApp').filter('ILRrestrict', function () {\n\treturn function (results, learning_delivery, def, prop) {\n\t\tvar i,\n\t\t\tnew_results = [];\n\t\tif (\n\t\t\tdef\n\t\t\t&& def.record\n\t\t\t&& def.record[learning_delivery.record]\n\t\t\t&& def.record[learning_delivery.record][prop + 'Allow']\n\t\t) {\n\t\t\tfor (i = results.length - 1; i >= 0; i--) {\n\t\t\t\tif (def.record[learning_delivery.record][prop + 'Allow'].indexOf(results[i].value) > -1) {\n\t\t\t\t\tnew_results.push(results[i]);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn new_results.length > 0 ? new_results : results;\n\t};\n});", "angular.module('lmsApp').filter('learnerTasksFilter', function($filter,$dateOperation,$rootScope) {\n\treturn function(input, predicate) {\n\t\tvar response = input,\n\t\t\tstrict = false,\n\t\t\tcustom_predicate = {},\n\t\t\tcustom_response = [],\n\t\t\tcompletion_status = false,\n\t\t\tselectedPosition = predicate.position ? predicate.position.toLowerCase() : '';\n\t\t// some input fields must be strict\n\t\tangular.forEach(predicate, function(predicate_value, key) {\n\t\t\tcustom_predicate = {};\n\t\t\tcustom_predicate[key] = predicate_value;\n\t\t\tstrict = false;\n\n\t\t\tswitch(key) {\n\t\t\t\tcase 'completion_status':\n\t\t\t\t\tcompletion_status = true;\n\t\t\t\t\tresponse = $filter('filter')(response, custom_predicate, true);\n\t\t\t\tbreak;\n\t\t\t\tcase 'learningResult_due_at':\n\t\t\t\t\tangular.forEach(response, function(user_value) {\n\t\t\t\t\t\t/*\n\t\t\t\t\t\t'due_before',\n\t\t\t\t\t\t'completion_date_custom',\n\t\t\t\t\t\t'grace_at',\n\t\t\t\t\t\t'due_at',\n\t\t\t\t\t\t*/\n\t\t\t\t\t\tif (user_value.learningresult.due_before) {\n\t\t\t\t\t\t\tif (\n\t\t\t\t\t\t\t\tuser_value.learningresult.due_before < predicate_value\n\t\t\t\t\t\t\t) {\n\t\t\t\t\t\t\t\tcustom_response.push(user_value);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else if (user_value.learningresult.completion_date_custom) {\n\t\t\t\t\t\t\tif (\n\t\t\t\t\t\t\t\tuser_value.learningresult.completion_date_custom < predicate_value\n\t\t\t\t\t\t\t) {\n\t\t\t\t\t\t\t\tcustom_response.push(user_value);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else if (user_value.learningresult.grace_at) {\n\t\t\t\t\t\t\tif (\n\t\t\t\t\t\t\t\tuser_value.learningresult.grace_at < predicate_value\n\t\t\t\t\t\t\t) {\n\t\t\t\t\t\t\t\tcustom_response.push(user_value);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tif (\n\t\t\t\t\t\t\t\tuser_value.learningresult.due_at < predicate_value\n\t\t\t\t\t\t\t) {\n\t\t\t\t\t\t\t\tcustom_response.push(user_value);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\n\t\t\t\t\t});\n\t\t\t\t\tresponse = custom_response;\n\t\t\t\tbreak;\n\t\t\t\tcase 'competency_id':\n\t\t\t\t\t// look into entries competencies, return only those that has\n\t\t\t\t\tangular.forEach(response, function(user_value) {\n\t\t\t\t\t\tif (user_value.competencies.length > 0) {\n\t\t\t\t\t\t\tangular.forEach(user_value.competencies, function(comp_value) {\n\t\t\t\t\t\t\t\tif (comp_value.id === parseInt(predicate_value, 10)) {\n\t\t\t\t\t\t\t\t\tcustom_response.push(user_value);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\tresponse = custom_response;\n\t\t\t\tbreak;\n\t\t\t\tcase 'completion_date_custom':\n\t\t\t\t\tangular.forEach(response, function(user_value) {\n\t\t\t\t\t\tif (\n\t\t\t\t\t\t\t(\n\t\t\t\t\t\t\t\tpredicate_value === 'show' &&\n\t\t\t\t\t\t\t\tuser_value.completion_date_custom !== null\n\t\t\t\t\t\t\t) ||\n\t\t\t\t\t\t\t!predicate_value ||\n\t\t\t\t\t\t\tpredicate_value === 'hide'\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tcustom_response.push(user_value);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\tresponse = custom_response;\n\t\t\t\tbreak;\n        case 'event_due_date_uk':\n          custom_response = [];\n          let date = JSON.parse(predicate_value);\n          let period_from = moment(date.period_from).startOf('day');\n          let period_to = moment(date.period_to).endOf('day');\n          angular.forEach(response, function(user_value) {\n            let row_date = moment(user_value.startsAt);\n            if (\n              user_value.startsAt &&\n              moment(row_date).isBetween(period_from, period_to, null, '[]')\n            ) {\n              custom_response.push(user_value);\n            }\n          });\n          response = custom_response;\n          break;\n\t\t\t\tdefault:\n\t\t\t\t\tresponse = $filter('filter')(response, custom_predicate, strict);\n\t\t\t}\n\t\t});\n\n\t\tif (!completion_status) {\n\t\t\tresponse = $filter('filter')(response, {completion_status: \"!completed\"});\n\t\t}\n\n\t\tif (selectedPosition) {\n\t\t\tresponse = response.filter(item => item.position && item.position.toLowerCase() === selectedPosition);\n\t\t}\n\n\n\t\treturn response;\n\t};\n});\n", "angular.module('lmsApp').filter('learningResultsFilter', function($filter,$rootScope) {\n\treturn function(input, predicate) {\n\t\tvar response = input,\n\t\t\tstrict = false,\n\t\t\tcustom_predicate = {},\n\t\t\tcustom_response = []\n\t\t;\n\t\t// some input fields must be strict\n\t\tangular.forEach(predicate, function(predicate_value, key) {\n\t\t\tcustom_predicate = {};\n\t\t\tcustom_predicate[key] = predicate_value;\n\t\t\tstrict = false;\n\n\t\t\tswitch(key) {\n\t\t\t\tcase 'completion_status':\n\t\t\t\t\tresponse = $filter('filter')(response, custom_predicate, true);\n\t\t\t\tbreak;\n\t\t\t\tcase 'category_id':\n\t\t\t\t\tresponse = $filter('filter')(response, {module:{category_id: parseInt(predicate_value, 10)}}, true);\n\t\t\t\tbreak;\n\t\t\t\tcase 'type_id':\n\t\t\t\t\tresponse = $filter('filter')(response, {module:{type_id: parseInt(predicate_value, 10)}}, true);\n\t\t\t\tbreak;\n\n        case 'expected_completion_date_uk':\n          {\n          let date = JSON.parse(predicate_value);\n          let period_from = moment(date.period_from).format('YYYY-MM-DD')\n          let period_to = moment(date.period_to).format('YYYY-MM-DD')\n            angular.forEach(response, function(user_value) {\n              let current_row = moment(user_value.expected_completion_date_uk).format('YYYY-MM-DD');\n\t\t\t\t\t\tif (\n\t\t\t\t\t\t\tuser_value.expected_completion_date_uk &&\n\t\t\t\t\t\t\tmoment(current_row).isBetween(period_from, period_to, null, '[]')\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tcustom_response.push(user_value);\n\t\t\t\t\t\t}\n          });\n          }\n            response = custom_response;\n            break;\n        case 'refreshed_at':\n          {\n          let date = JSON.parse(predicate_value);\n          let period_from = moment(date.period_from).format('YYYY-MM-DD')\n            let period_to = moment(date.period_to).format('YYYY-MM-DD')\n          angular.forEach(response, function(user_value) {\n            let current_row = moment(user_value.refreshed_at).format('YYYY-MM-DD');\n\t\t\t\t\t\tif (\n\t\t\t\t\t\t\tuser_value.refreshed_at &&\n\t\t\t\t\t\t\tmoment(current_row).isBetween(period_from, period_to, null, '[]')\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tcustom_response.push(user_value);\n\t\t\t\t\t\t}\n          });\n          }\n\t\t\t\t\tresponse = custom_response;\n\t\t\t\tbreak;\n\t\t\t\tdefault:\n\t\t\t\t\tresponse = $filter('filter')(response, custom_predicate, strict);\n\t\t\t}\n\t\t});\n\t\treturn response;\n\t};\n});\n", "angular.module('lmsApp').filter('manager<PERSON><PERSON><PERSON>', function() {\n    return function(managers) {\n        return managers.map(function(manager) {\n            return manager.fname + ' ' + manager.lname;\n        }).join(', ');\n    };\n});", "angular.module('lmsApp').filter('myStrictFilter', function($filter) {\n\treturn function(input, predicate) {\n\t\tvar response = input,\n\t\t\tstrict = false,\n\t\t\tcustom_predicate = {},\n\t\t\tcustom_response = [];\n\t\t// some input fields must be strict\n\t\tangular.forEach(predicate, function(predicate_value, key) {\n\t\t\tcustom_predicate = {};\n\t\t\tcustom_predicate[key] = predicate_value;\n\t\t\tstrict = false;\n\n\t\t\tswitch(key) {\n\t\t\t\tcase 'completion_status':\n\t\t\t\t\tresponse = $filter('filter')(response, custom_predicate, true);\n\t\t\t\tbreak;\n\t\t\t\tcase 'learningResult_due_at':\n\t\t\t\t\tangular.forEach(response, function(user_value) {\n\t\t\t\t\t\t/*\n\t\t\t\t\t\t'due_before',\n\t\t\t\t\t\t'completion_date_custom',\n\t\t\t\t\t\t'grace_at',\n\t\t\t\t\t\t'due_at',\n\t\t\t\t\t\t*/\n\t\t\t\t\t\tif (user_value.learningresult.due_before) {\n\t\t\t\t\t\t\tif (\n\t\t\t\t\t\t\t\tuser_value.learningresult.due_before < predicate_value\n\t\t\t\t\t\t\t) {\n\t\t\t\t\t\t\t\tcustom_response.push(user_value);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else if (user_value.learningresult.completion_date_custom) {\n\t\t\t\t\t\t\tif (\n\t\t\t\t\t\t\t\tuser_value.learningresult.completion_date_custom < predicate_value\n\t\t\t\t\t\t\t) {\n\t\t\t\t\t\t\t\tcustom_response.push(user_value);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else if (user_value.learningresult.grace_at) {\n\t\t\t\t\t\t\tif (\n\t\t\t\t\t\t\t\tuser_value.learningresult.grace_at < predicate_value\n\t\t\t\t\t\t\t) {\n\t\t\t\t\t\t\t\tcustom_response.push(user_value);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tif (\n\t\t\t\t\t\t\t\tuser_value.learningresult.due_at < predicate_value\n\t\t\t\t\t\t\t) {\n\t\t\t\t\t\t\t\tcustom_response.push(user_value);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\n\t\t\t\t\t});\n\t\t\t\t\tresponse = custom_response;\n\t\t\t\tbreak;\n\t\t\t\tcase 'expected_completion_date':\n\t\t\t\t\tangular.forEach(response, function(user_value) {\n\t\t\t\t\t\tif (\n\t\t\t\t\t\t\tuser_value.expected_completion_date &&\n\t\t\t\t\t\t\tuser_value.expected_completion_date < predicate_value\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tcustom_response.push(user_value);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\tresponse = custom_response;\n\t\t\t\tbreak;\n\t\t\t\tcase 'competency_id':\n\t\t\t\t\t// look into entries competencies, return only those that has\n\t\t\t\t\tangular.forEach(response, function(user_value) {\n\t\t\t\t\t\tif (user_value.competencies.length > 0) {\n\t\t\t\t\t\t\tangular.forEach(user_value.competencies, function(comp_value) {\n\t\t\t\t\t\t\t\tif (comp_value.id === parseInt(predicate_value, 10)) {\n\t\t\t\t\t\t\t\t\tcustom_response.push(user_value);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\tresponse = custom_response;\n\t\t\t\tbreak;\n\t\t\t\tcase 'completion_date_custom':\n\t\t\t\t\tangular.forEach(response, function(user_value) {\n\t\t\t\t\t\tif (\n\t\t\t\t\t\t\t(\n\t\t\t\t\t\t\t\tpredicate_value === 'show' &&\n\t\t\t\t\t\t\t\tuser_value.completion_date_custom !== null\n\t\t\t\t\t\t\t) ||\n\t\t\t\t\t\t\t!predicate_value ||\n\t\t\t\t\t\t\tpredicate_value === 'hide'\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tcustom_response.push(user_value);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\tresponse = custom_response;\n\t\t\t\tbreak;\n\t\t\t\tdefault:\n\t\t\t\t\tresponse = $filter('filter')(response, custom_predicate, strict);\n\t\t\t}\n\t\t});\n\t\treturn response;\n\t};\n});", "angular.module('lmsApp').filter('nl2br', function($sce) {\n\treturn function (msg, is_xhtml) {\n\t\tvar breakTag;\n\t\tis_xhtml = is_xhtml || true;\n\t\tbreakTag = (is_xhtml) ? '<br />' : '<br>';\n\t\tmsg = (msg + '').replace(/([^>\\r\\n]?)(\\r\\n|\\n\\r|\\r|\\n)/g, '$1'+ breakTag +'$2');\n\t\treturn $sce.trustAsHtml(msg);\n\t};\n});", "// Convert string to integer in view\nangular.module('lmsApp').filter('num', function() {\n\treturn function(input) {\n\t\treturn parseInt(input, 10);\n\t};\n});", "angular.module('lmsApp').filter('packAndGoFilter', function($filter) {\n\treturn function(input, predicate) {\n\t\tvar response = input,\n\t\t\tstrict = false,\n\t\t\tcustom_predicate\n\t\t;\n\n\t\tangular.forEach(predicate, function(predicate_value, key) {\n\n\t\t\tcustom_predicate = {};\n\t\t\tcustom_predicate[key] = predicate_value;\n\t\t\tstrict = false;\n\n\t\t\tswitch(key) {\n\t\t\t\tcase 'programme':\n\t\t\t\t\tresponse = $filter('filter')(response, {standards: predicate_value}, strict);\n\t\t\t\tbreak;\n\n\t\t\t\tcase 'favorite':\n\t\t\t\t\tresponse = $filter('filter')(response, {module: {learning_result: {favorite: predicate_value}}}, strict);\n\t\t\t\tbreak;\n\n\t\t\t\tcase 'user_added':\n\t\t\t\t\tresponse = $filter('filter')(response, {user_id: predicate_value}, strict);\n\t\t\t\tbreak;\n\n\t\t\t\tdefault:\n\t\t\t\t\tresponse = $filter('filter')(response, custom_predicate, strict);\n\t\t\t}\n\t\t});\n\n\t\treturn response;\n\t};\n});", "// attach random string at the end of images, for modules.\nangular.module('lmsApp').filter('randomSrc', function () {\n\treturn function (input) {\n\t\tif (input) {\n\t\t\treturn input + '?r=' + Math.round(Math.random() * 999999);\n\t\t}\n\t};\n});", "angular.module('lmsApp').filter('readableMinutes', function() {\n    return function(minutes) {\n        if (!minutes || minutes < 0) return '0m';\n\n        var hours = Math.floor(minutes / 60);\n        var remainingMinutes = minutes % 60;\n\n        let result = '';\n\n        if (hours > 0) {\n            result += hours + 'h ';\n        }\n\n        result += remainingMinutes + 'm';\n\n        return result.trim();\n    };\n});", "angular.module('lmsApp').filter('readableTime', function() {\n    return function(seconds) {\n        if (!seconds || seconds < 0) return '0s';\n\n        var hours = Math.floor(seconds / 3600); // 3600 seconds in an hour\n        var minutes = Math.floor((seconds % 3600) / 60); // Remaining seconds after hours, divided by 60 for minutes\n        var remainingSeconds = seconds % 60;\n\n        let result = '';\n\n        if (hours > 0) {\n            result += hours + 'h ';\n        }\n\n        if (minutes > 0 || hours > 0) { // Display minutes even if hours are present\n            result += minutes + 'm ';\n        }\n\n        result += remainingSeconds + 's';\n\n        return result.trim(); // Remove trailing space\n    };\n});", "angular.module('lmsApp').filter('scheduleFilter', function($filter) {\n\treturn function(input, predicate) {\n\t\tvar response = input,\n\t\t\tstrict = false,\n\t\t\tcustom_predicate\n\t\t;\n\n\t\tangular.forEach(predicate, function(predicate_value, key) {\n\n\t\t\tcustom_predicate = {};\n\t\t\tcustom_predicate[key] = predicate_value;\n\t\t\tstrict = false;\n\n\t\t\tswitch(key) {\n\t\t\t\tcase 'schedule_department_name':\n\t\t\t\t\tresponse = $filter('filter')(response, {departments: {name: predicate_value}}, strict);\n\t\t\t\tbreak;\n\n\t\t\t\tcase 'schedule_group_name':\n\t\t\t\t\tresponse = $filter('filter')(response, {groups: {name: predicate_value}}, strict);\n\t\t\t\tbreak;\n\n\t\t\t\tcase 'schedule_user_name':\n\t\t\t\t\tresponse = $filter('filter')(response, {users: {full_name: predicate_value}}, strict);\n\t\t\t\tbreak;\n\n\t\t\t\tcase 'schedule_manager_name':\n\t\t\t\t\tresponse = $filter('filter')(response, {managers: {full_name: predicate_value}}, strict);\n\t\t\t\tbreak;\n\t\t\t\tcase 'completion_status':\n\t\t\t\t\t response = $filter('filter')(response, custom_predicate, true);\n\t\t\t\tbreak;\n\n\t\t\t\tdefault:\n\t\t\t\t\tresponse = $filter('filter')(response, custom_predicate, strict);\n\t\t\t}\n\t\t});\n\n\t\treturn response;\n\t};\n});", "angular.module('lmsApp').filter('selectLearningResourceFilter', function($filter) {\n\treturn function(input, predicate) {\n\t\tvar response = input,\n\t\t\tstrict = false,\n\t\t\tcustom_predicate\n\t\t;\n\n\t\tangular.forEach(predicate, function(predicate_value, key) {\n\n\t\t\tcustom_predicate = {};\n\t\t\tcustom_predicate[key] = predicate_value;\n\t\t\tstrict = false;\n\n\t\t\tswitch(key) {\n\t\t\t\tcase 'category_id':\n\t\t\t\t\tresponse = $filter('filter')(response, {category_id: parseInt(predicate_value, 10)}, true);\n\t\t\t\tbreak;\n\t\t\t\tcase 'type_id':\n\t\t\t\t\tresponse = $filter('filter')(response, {type_id: parseInt(predicate_value, 10)}, true);\n\t\t\t\tbreak;\n\t\t\t\tcase 'company_id':\n\t\t\t\t\tresponse = $filter('filter')(response, {company_id: parseInt(predicate_value, 10)}, true);\n\t\t\t\tbreak;\n\n\t\t\t\tdefault:\n\t\t\t\t\tresponse = $filter('filter')(response, custom_predicate, strict);\n\t\t\t}\n\t\t});\n\n\t\treturn response;\n\t};\n});"], "sourceRoot": "../tpls/js/filters/"}