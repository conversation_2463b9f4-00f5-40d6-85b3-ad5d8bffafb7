<?php
namespace DB;

use Models\ConfigurationCategory;
use Models\FormWorkflow;

class Configuration {
	public static function update ($settings = false) {
		$configuration_values = [
			'defaultUKPRN' => [
				'name' => 'Default UKPRN',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => "UK provider reference number (UKPRN). \nMust contain a value in the range 10000000 to 99999999",
				'created_by' => 0
			],
			'defaultPrevUKPRN' => [
				'name' => 'Default UKPRN in previous year',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => "UK provider reference number (UKPRN). \nMust contain a value in the range 10000000 to 99999999",
				'created_by' => 0
			],
			'defaultUsernameLabel' => [
				'name' => 'Default Username label on login screen.',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => "Default Username label on login screen.",
				'created_by' => 0
			],
			'learnerReferenceNumberIteration' => [
				'name' => 'Learner Reference Number iteration.',
				'type' => 'integer',
				'status' => 1,
				'value' => 1,
				'description' => 'Iteration for Learner reference number. Final result(example): "domain000001".',
				'created_by' => 0
			],
			'learnerReferenceNumberId' => [
				'name' => 'Learner Reference number first part.',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => 'Up to 6 character long string that goes in begginging for Learner Reference Number',
				'created_by' => 0
			],
			'showSocialLoginButtons' => [
				'name' => 'Show social log-in buttons on log-in page.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Show "Log in with Facebook" and "Sign in with Google" buttons on log-in page',
				'created_by' => 0
			],
			'allowRegistration' => [
				'name' => 'Allow Registration',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Allow registration for new users',
				'created_by' => 0
			],
			'allowRemoteRegistration' => [
				'name' => 'Remote Registration in iframe',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Allow Registration the "/register" extension to be used on remote sites. i.e. <iframe src="https://openelms.e-learningwmb.co.uk/SITEDOMAIN/register" width="100%" height="100%" frameborder="0" title="Registration form, embeded"></iframe>',
				'created_by' => 0,
				'update_name_desc' => true,
			],
			'defaultRegisterRole' => [
				'name' => 'Default role for registered user',
				'type' => 'integer',
				'status' => 1,
				'value' => 3,
				'description' => 'Default role for new registered user. Change this to your desired role ID.',
				'created_by' => 0
			],
			'defaultIlrRole' => [
				'name' => 'Default role for imported ILR users',
				'type' => 'integer',
				'status' => 1,
				'value' => 3,
				'description' => 'Default role for imported users using ILR import.',
				'created_by' => 0
			],
			'refreshCompletedAt' => [
				'name' => 'Refresh training when resource completed not first assigned.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 1,
				'description' => 'Learning Results refresh time and "due_at" will be calculated by using "completed_at" as base.',
				'created_by' => 0
			],
			'sendRefreshEmail' => [
				'name' => 'Send refresh learning e-mails',
				'type' => 'boolean',
				'status' => 1,
				'value' => 1,
				'description' => 'When learning resource is refreshed, send e-mail to learner as reminder.',
				'created_by' => 0
			],
			'allowLearnerRefreshLearning' => [
				'name' => 'Allow Learner to refresh Learning',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Allow Learner to refresh completed Learning Resource. When Learner will open completed Learning Resource, prompt will open with option to start new learning record.',
				'created_by' => 0
			],
			'allowAddBlogEntry' => [
				'name' => 'Allow Learner to add Blog Entry.',
				'type' => 'boolean',
				'status' => 1,
				'value' => $settings && isset($settings['licensing']) && isset($settings['licensing']['isApprentix']) && $settings['licensing']['isApprentix'] == 1 ? 1 : 0,
				'description' => 'Allow Learner to add Blog Entry as new learning type. That can be assigned to other learners by managers',
				'created_by' => 0
			],
			'allowApi' => [
				'name' => 'Enable API for site.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Allows API requests to be made to site.',
				'created_by' => 0,
			],
			'trainingWorkRatio' => [
				'name' => 'Training/work ratio',
				'type' => 'integer',
				'status' => 1,
				'value' => 0.2,
				'description' => 'This is the proportion of work time for every programme where training is carried out - this is 0.2 or 20% of UK apprenticeships.',
				'created_by' => 0
			],
			'defaultJackdawRegisterRole' => [
				'name' => 'Default role for registered Open eLMS AI Editor user',
				'type' => 'integer',
				'status' => 0,
				'value' => '',
				'description' => 'Default role for registered Open eLMS AI Editor users. Change this to your desired role ID from Orgainsation->Roles section',
				'created_by' => 0
			],
			'allowSCORMContainerPlayer' => [
				'name' => 'Allow SCORM Container to be played',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Allow SCORM container downloaded from site to be enabled/functioning.',
				'created_by' => 0
			],
			'isDemoAccess' => [
				'name' => 'Demo User Interface',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'This will allow any user to access to a site without registering.',
				'created_by' => 0
			],
			'demoUserId' => [
				'name' => 'Demo User ID',
				'type' => 'integer',
				'status' => 1,
				'value' => '',
				'description' => 'If demo User Interface is enabled, valid user\'s ID must be specified. Demo user will be logged as that user.',
				'created_by' => 0
			],
			'mandatoryDuration' => [
				'name' => 'Mandatory duration',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Mandatory duration for resources that learner wants to sign off.',
				'created_by' => 0
			],
			'schoolField' => [
				'name' => 'School Field',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Free text field in user profile to specify School.',
				'created_by' => 0
			],
			'allowLearnerEditILR' => [
				'name' => 'Allow Learner Edit ILR',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Allow Learner Edit ILR Data from my-profile window in learners interface.',
				'created_by' => 0
			],
			'signOffText' => [
				'name' => 'Sign off text',
				'type' => 'string',
				'status' => 1,
				'value' => 'I agree that the information provided here is an accurate account of what has taken place',
				'description' => 'Sign off text in learner\'s interface.',
				'created_by' => 0
			],
			'showEmergencyContactDetails' => [
				'name' => 'Emergency contact details',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Show Emergency contact details in users profile/form.',
				'created_by' => 0
			],
			'showVisaEligibilityChecks' => [
				'name' => 'Visa Eligibility Checks',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Show Visa Eligibility Checks in users profile/form.',
				'created_by' => 0
			],
			'apprentixEmailReminderFrequency' => [
				'name' => 'Frequency in days by which automated email alerts are sent out',
				'type' => 'integer',
				'status' => 1,
				'value' => 0,
				'description' => "Apprentix only. Resources assigned to standard only. Specified number will be frequency how often e-mails will be sent out as reminders for resources that are not completed. First e-mail will be sent out set days before expected completion date. Look in timings configuration for more information. \nSetting value to 0 will disable this functionality.",
				'created_by' => 0
			],

			'isMeetings' => [
				'name' => 'Make Meetings Optional',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Having this as false will remove it from the toolbar of Coach/Trainer and Quality Assurer.',
				'created_by' => 0
			],

			'isManageBookings' => [
				'name' => 'Make Manage Bookings Optional',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Having this as false will remove it from the toolbar of Coach/Trainer and Administrator.',
				'created_by' => 0
			],

			'isLearnerQAFilter' => [
				'name' => 'Show QA filter in learners interface',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Show QA filter in learners interface. Leaner can filter Rejected and Accepted resources.',
				'created_by' => 0
			],

			'learnerAimReferenceURL' => [
				'name' => 'Learning Aim Reference Service URL',
				'type' => 'string',
				'status' => 1,
				'value' => 'https://des.fasst.org.uk/Learning%20Aims/Downloads/Documents/20190308_LARS_V006_CSV.zip',
				'description' => 'Direct link to Learning Aim Reference Service ZIP file.',
				'created_by' => 0
			],

			'version' => [
				'name' => 'Version',
				'type' => 'string',
				'status' => 1,
				'value' => 'apprentix', // openelms, openelmstms, omniprez, smcrsolution, apprentix, openelmsschools, openelmscolleges, openelmsuniversities, openelmsbusiness, nras
				'description' => 'Version.',
				'created_by' => 0
			],

			//Power BI Configurations
			'powerbi_client_id' => [
				'name' => 'Power BI Client ID',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => 'The Client ID of Azure Application used to control access to Power BI',
				'created_by' => 0
			],

			'powerbi_client_secret' => [
				'name' => 'Power BI Client Secret',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => 'The Client Secret Key of Azure Application used to control access to Power BI',
				'secure' => true,
				'created_by' => 0
			],

			'powerbi_tenant_id' => [
				'name' => 'Power BI Tenant ID',
				'type' => 'string',
				'status' => 1,
				'value' => 'common',
				'description' => 'If you need to use single tenant endpoint, change common to yor Tenant ID to access Power BI',
				'created_by' => 0
			],

			'powerbi_azure_username' => [
				'name' => 'Power BI Azure username',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => 'The username of the Azure account used to export data to Power BI',
				'created_by' => 0,
				'delete' => true
			],

			'powerbi_azure_password' => [
				'name' => 'Power BI Azure password',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => 'The password of the Azure account used to export data to Power BI',
				'secure' => true,
				'created_by' => 0,
				'delete' => true
			],

			'powerbi_dashboard_url' => [
				'name' => 'Default Power BI Dashboard',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => 'Url to Power BI dashboard.',
				'created_by' => 0
			],

			'disableLazyLoading' => [
				'name' => 'Disable lazy load functionality',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Learners interface has lazy load enabled by default, when image enters viewport, it loads image. Possibly some compatibility problems on Apple devices, will enable this switch to disable.',
				'created_by' => 0
			],
			'moodleLink' => [
				'name' => 'Moodle Link',
				'type' => 'string',
				'status' => 1,
				'value' => "",
				'description' => 'Url of Moodle installation',
				'created_by' => 0
			],
			'nextDueTrafficLight' => [
				'name' => 'Traffic light system for next Due Date',
				'type' => 'integer',
				'status' => 1,
				'value' => 4,
				'description' => "When viewing 'Progress' on learner programme, amber indicates the 'next due date' is less than 4 weeks away, whilst red signifies the due date has passed (overdue). The 4 week period can be customised here (enter the number of weeks).  NB: Enter '0' if you don't want to use the traffic light colour system.",
				'update_name_desc' => true,
				'created_by' => 0
			],
			'allowEmptyEmailImport' => [
				'name' => 'Allow importing users without email',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'When user is imported without email, a randomly dummy e-mail(non functioning) is generated and assigned to user, as email field is unique and required.',
				'created_by' => 0
			],
			'loggingDataRentention' => [
				'name' => 'How many years logging data is retained',
				'type' => 'integer',
				'status' => 1,
				'value' => 6,
				'description' => 'After how many years data is deleted. Login-in/out, data export.',
				'created_by' => 0
			],
			'enableEmailFunctionality' => [
				'name' => 'This will turn on all email sending features',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'If you want to enable e-mail on system, set this to true, to disable, set to false',
				'created_by' => 0
			],
			'enhancedEmailErrorLogging' => [
				'name' => 'Enhanced Email Error Logging',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Enable enhanced debugging information in email error logs. Includes SMTP host, port, security settings, and authentication details for troubleshooting email delivery issues.',
				'created_by' => 0
			],
			'enableClassRoomManagerDropDown' => [
				'name' => 'Will enable drop down functionality for classroom learning type.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Will replace free text field with drop-down of managers to choose from.',
				'created_by' => 0
			],
			'showResponsibilitiesAndCommittees' => [
				'name' => 'Manage Responsibilities and Committees for non-SMCR sites.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Will show "Responsibilities" and "Committee Memberships" in "Manage Learning" interface, visible to managers.',
				'created_by' => 0
			],
			'competenciesGamification' => [
				'name' => 'Enable Competencies/"Gamification" functionality.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => '"Learner Programme", "Training Data" view will see Competencies table, Learner will see new head icon "Learning Profile" that will show summary of they profile with gamification table as well.',
				'created_by' => 0
			],
			'showLatestReleases' => [
				'name' => 'Show Latest Releases',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'This shows the latest 10 course releases added to the system.',
				'created_by' => 0
			],
			'companyCrm' => [
				'name' => 'Add CRM Fields to %%company%% data',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Ability to add products and contact details for sales activity',
				'created_by' => 0
			],
			'sessionTimeout' => [
				'name' => 'Session timeout for inactivity',
				'type' => 'integer',
				'status' => 1,
				'value' => 1,
				'description' => 'How many hours session will last untill user is logged out, due to inactivity.',
				'created_by' => 0
			],
			'linkIlrToUserProgramme' => [
				'name' => 'Uses dates in ILR record for standard.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'When activated nightly cron task or when user is updated/imported, will check if user is assigned to programme and if that programme holds reference to users ILR record, if match is found ILR record dates will be used!',
				'created_by' => 0
			],
			'enableEmailAttachments' => [
				'name' => 'Allows adding files to email templates.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 1,
				'description' => 'When activated, multiple files can be added to e-mail templates as attachments.',
				'created_by' => 0,
				//'force' => true,
			],
			'emailAttachmentsSize' => [
				'name' => 'Maximum size allowed to be added to templates (MB).',
				'type' => 'integer',
				'status' => 1,
				'value' => 5,
				'description' => 'The amount is the combined file size per e-mail template in MB. If file or files exceed specified limits, no new files can be added to template.',
				'created_by' => 0,
				//'force' => true,
			],
			'enableUploadType' => [
				'name' => 'Shows type dropdown for uploads.',
				'type' => 'boolean',
				'status' => 1, // Enable only for Apprentix and Colleges
				'value' => ($settings['licensing']['version'] == 'apprentix' || $settings['licensing']['version'] == 'openelmscolleges') ? 1 : 0,
				'description' => 'Shows type dropdown for uploads.',
				'created_by' => 0,
			],
			'enableJackdawHtml5' => [
				'name' => 'Switch Open eLMS AI Editor to HTML5.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 1,
				'description' => 'Uses new HTML5 Open eLMS AI Editor version.',
				'created_by' => 0,
				'delete' => true
			],
			'disableResourceTypes' => [
				'name' => 'Disable specific resource types',
				'type' => 'string',
				'status' => 1,
				'value' => 'classroom,moodle_course,google_classroom,microsoft_teams,zoom_meeting,event',
				'description' => 'DEPRECATED: This configuration has been migrated to database-driven resource type management. Use System Setup > Learning Resource Types to manage enabled/disabled resource types.',
				'update_name_desc' => true,
				'update_value_if_empty' => true,
				'created_by' => 0,
				'delete' => true  // This will be processed for migration then deleted
			],
			'olarkCode' => [
				'name' => 'Script from Olark',
				'type' => 'text',
				'status' => 1,
				'value' => "",
				'description' => 'Add code from Olark in Value field.',
				'created_by' => 0,
			],
			'enableOlark' => [
				'name' => 'Enable Olark code.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Enable Olark code.',
				'created_by' => 0,
			],
			'enableSchedule' => [
				'name' => 'Enable Schedule.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 1,
				'description' => 'Enable Schedule section in managers view.',
				'created_by' => 0,
			],
			'lessonDuration' => [
				'name' => 'Default Lesson duration when assigning lessons to class/departments',
				'type' => 'integer',
				'status' => 1,
				'value' => 60,
				'description' => 'Default Lesson duration, specified in minutes, used when assigning Lesson to Class for programme/standard.',
				'created_by' => 0,
				//'force' => true,
			],
			'learnerInterfaceV2' => [
				'name' => 'Enable changes to learner intrface.',
				'type' => 'boolean',
				'status' => 1,
				'value' => true,
				'description' => 'Learner interface - remove some top icons and replaces them with text in header.',
				'created_by' => 0,
				'delete' => true,
			],
			'enableFeedback' => [
				'name' => 'Enable feedback form and list for learner.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 1,
				'description' => 'Feedback interface for learner, enable to allow learner add rating and comment to resource.',
				'created_by' => 0,
			],
			'enableFeedbackList' => [
				'name' => 'When this is enabled, learner will also see list of feedback by other users.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 1,
				'description' => 'When this is enabled, learner will also see list of feedback by other users.',
				'created_by' => 0,
			],
			'offTheJobHoursForReviews' => [
				'name' => 'Off the job hours toggle for reviews.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Will show checkbox in reviews section, for managers to mark review as off the job, that will not be counted towards statistics..',
				'created_by' => 0,
			],
			'enableLeaderBoardImages' => [
				'name' => 'Show profile picture in leaderboards or random assigned icon.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 1,
				'description' => 'When this is enabled, leaderboard list will show either profile picture or if that is not available, randomly assigned person icon.',
				'created_by' => 0,
			],
			'enableLeaderBoardList' => [
				'name' => 'Show leaderboard list/table.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 1,
				'description' => 'When this is enabled, leaderboard list will be shown for learner and administration.',
				'created_by' => 0,
			],
			'scheduleReminderLead' => [
				'name' => 'How many minutes before to send e-mail reminder about starting scheduled event to users',
				'type' => 'integer',
				'status' => 1,
				'value' => 15,
				'description' => 'Send reminder to user about scheduled event that is going to start soon. Automated task is executed every 5 minutes, not suggestable to take this below 10 minutes.',
				'created_by' => 0,
				//'force' => true,
			],
			'importYoutubePlaylist' => [
				'name' => 'Enable youtube playlist import into learning library',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'By enabling this option, you can specify playlist URL in learning library to impoert each individual videa as learning resource.',
				'created_by' => 0,
				//'force' => true,
			],
			'jackdawVersion' => [
				'name' => 'Jackdaw Billing Version',
				'type' => 'string',
				'status' => 1,
				'value' => 'JACKDAW', // JACKDAW, JACKDAWCMS, JACKDAWREAD
				'description' => 'Jackdaw Billing Version',
				'created_by' => 0
			],
			'forceYoutubeTitleOverThumbnail' => [
				'name' => 'Show video title over thumbnail for YouTube resources',
				'type' => 'boolean',
				'status' => 1,
				'value' => 1,
				'description' => 'When enabled, title will be shown over thumbnail in learners interface regardless of custom thumbnail existance, for YouTube items.',
				'created_by' => 0
			],
			'hideCurriculum' => [
				'name' => 'Hide Curriculum',
				'type' => 'boolean',
				'status' => 1,
				'value' => ($settings['licensing']['version'] == 'openelmsuniversities' || $settings['licensing']['version'] == 'openelmscolleges' || $settings['licensing']['version'] == 'openelmsschools') ? 1 : 0,
				'description' => 'This hides the curriculum schedule from all users.',
				'created_by' => 0
			],
			'hideCurriculumMatching' => [
				'name' => 'Hide Curriculum Matching',
				'type' => 'boolean',
				'status' => 1,
				'value' => ($settings['licensing']['version'] == 'openelmsuniversities' || $settings['licensing']['version'] == 'openelmscolleges' || $settings['licensing']['version'] == 'openelmsschools') ? 1 : 0,
				'description' => 'This hides the functionality to allow learners to match their submitted work to parts of the curriculum.',
				'created_by' => 0
			],
			'hideCurriculumLearner' => [
				'name' => 'Hide Curriculum for Learner',
				'type' => 'boolean',
				'status' => 1,
				'value' => ($settings['licensing']['version'] == 'openelmsuniversities' || $settings['licensing']['version'] == 'openelmscolleges' || $settings['licensing']['version'] == 'openelmsschools') ? 1 : 0,
				'description' => 'This hides the curriculum schedule from users in Learners interface.',
				'created_by' => 0,
				'update_name_desc' => true,
			],
			'ZoomClientId' => [
				'name' => 'Zoom Client ID',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => 'SDK apps require an SDK Key and Secret for authentication. These credentials are account-level and are generated once per account. To generate SDK Keys and Secrets for your account, navigate to the Marketplace and https://marketplace.zoom.us/develop/create',
				'created_by' => 0
			],
			'ZoomClientSecret' => [
				'name' => 'Zoom Client Secret',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => 'SDK apps require an SDK Key and Secret for authentication. These credentials are account-level and are generated once per account. To generate SDK Keys and Secrets for your account, navigate to the Marketplace and https://marketplace.zoom.us/develop/create',
				'secure' => true,
				'created_by' => 0
			],
			'isBlackColourScheme' => [
				'name' => 'Black Colour Scheme',
				'type' => 'boolean',
				'status' => 1,
				'value' => 1,
				'description' => 'This colour scheme shows the training with a dark background - making this false will show a light background and dark icons/writing.',
				'created_by' => 0
			],
			'AndersPinkApiKey' => [
				'name' => 'Private API key for Anders Pink',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => 'Key can be obtained from https://anderspink.com/export-settings/ ',
				'secure' => true,
				'created_by' => 0
			],
			'isCategroyFilter' => [
				'name' => 'Category Filter in Learners interface',
				'type' => 'boolean',
				'status' => 1,
				'value' => 1,
				'description' => 'When true, a category filter is added to the learner’s interface allowing the learner to display one expanded category at a time.',
				'created_by' => 0
			],
			'isLearnerLandingPage' => [
				'name' => 'Add learner landing page',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Setting this to true will set a landing page showing pictures of all learning categories which have an image assigned to them. Do ensure an image is defined for category if you are using this functionality.',
				'created_by' => 0
			],
			'learnerLandingPageDescription' => [
				'name' => 'Learner landing page description',
				'type' => 'text',
				'status' => 1,
				'value' => '',
				'description' => 'This text will show above landing page category images.',
				'created_by' => 0
			],
			'learnerLandingPageNameCentered' => [
				'name' => 'Centre category labels',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Category landing page will have its name centred.',
				'created_by' => 0,
				'update_name_desc' => true,
			],
			'learnerLandingPageDescriptionFontSize' => [
					'name' => 'Learner landing page description font size',
					'type' => 'integer',
					'status' => 1,
					'value' => 16,
					'description' => 'This will set font size for text ath will show above landing page category images.',
					'created_by' => 0
			],
			'defaultTimezone' => [
				'name' => 'Default time zone',
				'type' => 'string',
				'status' => 1,
				'value' => 'Europe/London',
				'description' => 'Used for font and back end. Adjust if instance is used in different time zones.',
				'created_by' => 0
			],
			'thumbnailRedesign' => [
				'name' => 'Thumbnail redesign in Learners interface',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'On learners interface, thumbnails shown will be displayed with image on one side and name on another, all enclosed with white background.',
				'created_by' => 0
			],
			'thumbnailRedesignFont' => [
				'name' => 'Font used in thumbnail redesign, learners interface',
				'type' => 'string',
				'status' => 1,
				'value' => 'Roboto',
				'description' => 'Possible values can be: "Arial", "Roboto", "Times New Roman", "Times", "Courier New", "Courier", "Verdana", "Georgia", "Palatino", "Garamond", "Bookman", "Comic Sans MS", "Candara", "Arial Black", "Impact"',
				'created_by' => 0
			],
			'disableLookOvers' => [
				'name' => 'Disable Look Overs button/functionality in Learners profile for managers',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'With creation of events/schedule view this function will become obselete and won\'t be used for new clients. Reporting will go to custom reviews.',
				'created_by' => 0
			],
			'go1clientID' => [
				'name' => 'Go1 client ID',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => 'Client ID which is a publicly exposed string used by the GO1 API to identify the OAuth client.',
				'created_by' => 0
			],
			'go1clientSecret' => [
				'name' => 'Go1 client secret',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => 'Client secret which is used to authenticate the identity of the OAuth client and must be kept private between the partner application and the API.',
				'secure' => true,
				'created_by' => 0
			],
			'go1GrantCode' => [
				'name' => 'Go1 authorization code',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => 'After access is provided to the portal, the user will be redirected back to the Partner application (redirect_uri specified in Step 1) with an authorization code as a GET-Parameter.',
				'secure' => true,
				'created_by' => 0
			],
			'go1AuthToken' => [
				'name' => 'Go1 Auth token',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => 'The Partner server will exchange the authorization code for an access token by making a POST request to the authorization server\'s token endpoint.',
				'secure' => true,
				'created_by' => 0
			],
			'go1AccessToken' => [
				'name' => 'Go1 Access token',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => 'The "access_token" will be valid for 12 hours. If it expires, it can be refreshed by the OAuth client using the "refresh_token", which is a one-time-use token and valid for 90 days.',
				'secure' => true,
				'created_by' => 0
			],
			'go1RefreshToken' => [
				'name' => 'Go1 Refresh token',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => 'Each refresh will give you another "refresh_token", which is valid for another 90 days. In case the "access_token" and "refresh_token" are both invalid or not stored, the user will need to reauthorize starting at step 1.',
				'secure' => true,
				'created_by' => 0
			],
			'go1AccessTokenExpiresIn' => [
				'name' => 'Go1 Access token expires in',
				'type' => 'integer',
				'status' => 1,
				'value' => 0,
				'description' => '',
				'created_by' => 0
			],
			'go1AccessTokenType' => [
				'name' => 'Go1 Access token type',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => '',
				'created_by' => 0
			],
			'go1InstallResourcesCron' => [
				'name' => 'Automatically install resources from authenticated GO1 library',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'If system holds authenticated Go1 credentials, cron task will check for resources on GO1 endpoint and install them automatically if new ones will become available.',
				'created_by' => 0
			],
			'makeResourcesLinktoHome' => [
				'name' => 'Make Resources link to Home',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Setting this variable to true will allow the learner to navigate to the Home screen (where categories are displayed if activated) when pressing on the resources link in the header bar.',
				'created_by' => 0
			],
			'googleAnalyticsCode' => [
				'name' => 'Google Analytics',
				'type' => 'text',
				'status' => 1,
				'value' => "",
				'description' => 'Add code for Google Analytics in Value field.',
				'created_by' => 0,
			],
			'enableGoogleAnalytics' => [
				'name' => 'Enable Google Analytics code.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Enable Google Analytics code.',
				'created_by' => 0,
			],
			'learnerCategoryLandingRows' => [
				'name' => 'Learner Category landing page maximum shown rows.',
				'type' => 'integer',
				'status' => 1,
				'value' => 3,
				'description' => 'Choose between number 2 to 6 to show that many rows of categories in learner landing page, if enabled.',
				'created_by' => 0,
				'delete' => true
			],
			'learnerCategoryLandingMaxColumns' => [
				'name' => 'Maximum items per row in Learner category landing page.',
				'type' => 'integer',
				'status' => 1,
				'value' => 3,
				'description' => 'Choose between number 2 to 4 to show that many category items per row.',
				'created_by' => 0,
			],
			'enableUserFieldAlertSystem' => [
				'name' => 'Enable %%user%% field alert system.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'List of field names which - when empty - will create a monthly alert asking the user to complete them.',
				'created_by' => 0,
			],
			'userFieldAlertSystemInterval' => [
				'name' => '%%user%% field alert system alert interval/check.',
				'type' => 'integer',
				'status' => 1,
				'value' => 30,
				'description' => 'This is the interval at which the user fields are checked (in minutes) to see if any alerts need to be sent.Only adjust this setting if enableUserFieldAlertSystem is true.',
				'created_by' => 0,
			],
			'userFieldAlertSystemIntervalRunTime' => [
				'name' => '%%user%% field alert system alert sent time/date',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => 'Holds stringified date when alert was sent last time.',
				'created_by' => 0,
			],
			'userFieldAlertSystemMonitoredFields' => [
				'name' => 'List of fields in user table, monitored for completion',
				'type' => 'text',
				'status' => 1,
				'value' => "",
				'description' => 'If user field alert system is enabled and fields are added here, they will be checked for completion, if not completed, reminder will be sent to users!',
				'created_by' => 0,
			],
			'hideResourcesInLesson' => [
				'name' => 'Hide learning resources in lessons',
				'type' => 'boolean',
				'status' => 1,
				'value' => 1,
				'description' => 'Hide resources in Learner interface if they are in Lesson',
				'created_by' => 0,
				'update_name_desc' => true,
			],
			'startupCourseID' => [
				'name' => 'Startup Course ID',
				'type' => 'integer',
				'status' => 1,
				'value' => '',
				'description' => 'If this has been defined then this course will play on start up whenever a new user accesses the system i.e. This will no longer show IF the course status = Completed. When this is the case the user will go into the home page as normal.',
				'created_by' => 0,
			],
			'showCriteriaCompletion' => [
				'name' => 'Show Criteria completion percentages',
				'description' => 'Show Criteria completion percentages in learner and administrator interface, usable for Open eLMS for Apprenticeships',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'created_by' => 0,
				'update_name_desc' => true,
			],
			'salesforceSoapCredentials' => [
				'name' => 'Salesforce SOAP credentials',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => 'When entering credentials in API section, make sure your user can login only from this server IP (ask support for it) and have only restricted access you require.',
				'created_by' => 0
			],
			'launchResourceText' => [
				'name' => 'Launch button text in Learners interface',
				'type' => 'string',
				'status' => 1,
				'value' => 'Launch resource',
				'description' => 'Custom launch text in Learners interface. Empty value to have default "Launch resource".',
				'created_by' => 0,
				'update_name_desc' => true,
			],
			'learnerSkipUploadPrompt' => [
				'name' => 'Skip "existing learning" prompt in Learners interface.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 1,
				'description' => 'When enabled will not show "Select an existing learning resource or add new" dialogue when uploading in Learners interface, will go stright to uploading.',
				'created_by' => 0,
			],
			'mobileAPI' => [
				'name' => 'Enable mobile API to work with this instance.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'By enabling, mobile Application will be able to used with this instance for users to log in and do training.',
				'created_by' => 0,
				'delete' => true,
			],
			'learnerProgressGauge' => [
				'name' => 'Show gauge in Learners interface',
				'type' => 'boolean',
				'status' => 1,
				'value' => 1,
				'description' => 'This shows the overall progress gauge for learners in a the header of the learner`s interface.',
				'created_by' => 0,
			],
			'buttonStyle' => [
				'name' => 'eLearning button style',
				'type' => 'string',
				'status' => 1,
				'value' => 'traditional',
				'description' => 'Selected button style for SCORM player.',
				'created_by' => 0,
			],
			'promoGradient' => [
				'name' => 'Promo Image gradient',
				'type' => 'boolean',
				'status' => 1,
				'value' => 1,
				'description' => 'Show gradient on promo image',
				'created_by' => 0,
			],
			'enableMFA' => [
				'name' => 'Multifactor Authentication',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Enable Multi Factor Authentication for all role types, if you want to disable this feature for particular roles, then edit the role and select to "Disable MFA for this role"',
				'created_by' => 0,
			],
			'IncludeCredasForms' => [
				'name' => 'Include Credas Forms',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Setting this to true allows you to use the Credas Forms for onboarding students and manage the data process flow between the learner and other parties such as employer and training provider (common with apprenticeships).  The system is also used to collect data on progress reviews.',
				'created_by' => 0,
				'delete' => true,
			],
			'CredasApiKey' => [
				'name' => 'Credas API Key',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => '',
				'secure' => true,
				'created_by' => 0,
				'delete' => true,
			],
			'CredasAccessUrl' => [
				'name' => 'Credas Access URL',
				'type' => 'string',
				'status' => 1,
				'value' => 'https://connect.credasdemo.com',
				'description' => '',
				'created_by' => 0,
				'delete' => true,
			],
			'CredasCourseTitleSuffix' => [
				'name' => 'Title of Credas Course registration suffix',
				'type' => 'string',
				'status' => 1,
				'value' => 'Apprenticeship registration',
				'description' => 'This value will be appended to title when registering course: "John Johnson Apprenticeship registration"',
				'created_by' => 0,
				'delete' => true,
			],
			'CredasSuperUserEmail' => [
				'name' => 'Credas Super user email specified in call',
				'type' => 'string',
				'status' => 1,
				'value' => '<EMAIL>',
				'description' => 'Update with email that was given for this instance.',
				'created_by' => 0,
				'delete' => true,
			],
			'CredasDisableCourseRegistration' => [
				'name' => 'Disable course registration functionality for Credas.',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => "When enabled together with IncludeCredasForms, Course registration is not available, but Credas reports can be used.",
				'created_by' => 0,
				'delete' => true,
			],
			'isOpeneLMSClassroom' => [
				'name' => 'Open eLMS Classroom',
				'type' => 'boolean',
				'status' => 1,
				'value' => 1,
				'description' => 'If this value is true then the system is able to schedule Smart Classroom events within the calendar scheduling system.',
				'created_by' => 0,
				'update_name_desc' => true,
			],
			'LMSTitle' => [
				'name' => 'Title used in HTML tag <TITLE>',
				'type' => 'string',
				'status' => 1,
				'value' => 'Open eLMS',
				'description' => 'This is the title used in the browser window or tab at the top of the screen.  This will identify your company name when accessing the system. Use an entry such as ""Acme - LMS"".',
				'update_value_if_empty' => true,
				'created_by' => 0,
			],
			'showCalendarTasksOnly' => [
				'name' => 'Show calendar tasks',
				'type' => 'boolean',
				'status' => 1,
				'value' => 1,
				'description' => 'Set "Show calendar tasks only" on learner interface to checked/unchecked.',
				'created_by' => 0,
				'update_name_desc' => true,
			],
			'submitAssessmentText' => [
				'name' => 'Submit Assessment Text',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => 'Change this text to alter the feedback given to the learner once the assessment has been submitted.',
				'created_by' => 0,
			],
			'isGroupCountries' => [
				'name' => 'Group Countries',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Setting this allows you to create Country groups (by region etc.) and group them together when selecting countries from a list.',
				'created_by' => 0,
			],
			'HelpVideoURL' => [
				'name' => 'Help Video',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => "Setting this value changes the default learner's help video to one of your choice",
				'created_by' => 0,
			],
			'isPeertoPeerVideo' => [
				'name' => 'Peer to Peer Video',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => "Setting this value to true, will enable learners to communicate with each other via Skype etc. from within the Open eLMS learner interface.",
				'created_by' => 0,
			],
			'DefaultWelcomeText' => [
				'name' => 'Welcome text in log-in screen',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => 'If empty, will use default.',
				'created_by' => 0,
			],
			'TeamsRedirectUrl' => [
				'name' => 'Custom Microsoft Teams redirect URL',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => 'If empty, will use default.',
				'created_by' => 0,
			],
			'enableMaytasImport' => [
				'name' => 'Show old MAYTAS import option',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Will allow import maytas export for importing ILR users, might now rok out of the box as maytas filescan be custom made, contact administration first.',
				'created_by' => 0,
				'delete' => true,
			],

			'EnforceDefaultRole' => [
				'name' => 'Enforce Default Role',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'If set to true, then the default role (see defaultIlrRole) is used each time a learner logs in - regardless of the previous logged in role.  If the user does not have permissions to access the default role then they log in normally.',
				'created_by' => 0,
				//'force' => true,
			],

			'enableContactYourColleagues' => [
				'name' => 'Show “contact your colleagues“ section in Learner interface When Launch Conversation is clicked.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => "Setting this to true makes a button visible in the top left of the learner's interface which allows them to contact learners (on the same learning programmes) and trainers (assigned to them).The system shows the contact details including Teams, Zoom, Skype and email where applicable.",
				'created_by' => 0,
			],
			'TasksShowByDefaultCalendarEntriesOnly' => [
				'name' => 'Task list shows calendar entries only',
				'type' => 'boolean',
				'status' => 1,
				'value' => 1,
				'description' => 'Setting this value to False will show all tasks (including those with no expected deadline or appointment associated with it that does not appear on the users or managers calendars.)',
				'created_by' => 0,
			],
			'TasksSortedbySoonestExpectedCompletionDate' => [
				'name' => 'Task list sorted by expected completion date',
				'type' => 'boolean',
				'status' => 1,
				'value' => 1,
				'description' => 'Setting this value to true will sort these tasks by the latest task first, if false then the earliest task will be the first listed (i.e. the one that the user is most behind with.)',
				'created_by' => 0,
			],
			'TasksDefaultSelectedTaskType' => [
				'delete' => true,
			],
			'TasksDefaultSelectedEventType' => [
				'name' => 'Task list filtered by a selected type when loads',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => 'If a task type is entered then the list is filtered by this task when it is first loaded.',
				'created_by' => 0,
			],
			'TasksDefaultSelectedResourceType' => [
				'name' => 'Task list filtered by a selected type when loads',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => 'If a task type is entered then the list is filtered by this task when it is first loaded.',
				'created_by' => 0,
			],
			'BadgrApiKey' => [
				'name' => 'Data for Badgr API',
				'value' => ',',
				'description' => '',
				'type' => 'string',
				'status' => 1,
				'secure' => true,
				'created_by' => 0,
			],
			'badgesEnabled' => [
				'name' => 'Status of Badges API',
				'type' => 'boolean',
				'value' => false,
				'status' => 1,
				'description' => '',
				'created_by' => 0,
			],
			'badgrRegion' => [
				'name' => 'Badgr API Region',
				'type' => 'string',
				'value' => 'eu.',
				'status' => 1,
				'description' => '',
				'created_by' => 0,
			],
			'allowJamboards' => [
				'name' => 'Use Jamboards',
				'type' => 'boolean',
				'value' => 0,
				'status' => 1,
				'description' => 'Set this value to true if you wish to use Jamboard smart boards whenever creating a live lesson.',
				'created_by' => 0,
			],
			'GoogleJamboardClientId' => [
				'name' => 'Google Jamboard Client Id',
				'type' => 'string',
				'value' => '',
				'status' => 1,
				'description' => '',
				'created_by' => 0,
			],
			'GoogleJamboardClientSecret' => [
				'name' => 'Google Jamboard Client Secret',
				'type' => 'string',
				'value' => '',
				'status' => 1,
				'description' => '',
				'secure' => true,
				'created_by' => 0,
			],
			'JamboardTemplateFileId' => [
				'name' => 'Jamboard Template FileId',
				'type' => 'string',
				'value' => '',
				'status' => 1,
				'description' => '',
				'created_by' => 0,
			],
			'JamboardRedirectUrl' => [
				'name' => 'Jamboard Redirect Url',
				'type' => 'string',
				'value' => '',
				'status' => 1,
				'description' => '',
				'created_by' => 0,
			],
			'addCustomProgrammeStatus' => [
				'name' => 'Custom Programme Status',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Setting this value to true allows the user to be defined by additional customisable statuses.  This allows more granularity to the standard Not Started, In Progress and Completed statuses - this does not replace these core statuses though.',
				'created_by' => 0,
			],
			'hideProgrammeStatusFromLearnerProgressView' => [
				'name' => 'Hide Programme status from learner progress view',
				'type' => 'boolean',
				'status' => 1,
				'value' => 1,
				'description' => 'Setting this value to true will hide Programme status from learner progress view.',
				'created_by' => 0,
			],
			'showOnlyAssignedCategories' => [
				'name' => 'Show only assigned categories for Learner.',
				'type' => 'boolean',
				'value' => 0,
				'status' => 1,
				'description' => 'Show only categories that are linked with resources assigned to Learner, learner interface. Upload/Evidence/Blog entry, etc...',
				'created_by' => 0,
			],
			'isApproveLearners' => [
				'name' => 'Approve learners before use.',
				'type' => 'boolean',
				'value' => 0,
				'status' => 1,
				'description' => 'If this is set to true, then any user who self-register on the system will need to be approved before using the system.',
				'created_by' => 0,
			],
			'PasswordExpiryDays' => [
				'name' => 'Password Expiry in Days',
				'type' => 'integer',
				'status' => 1,
				'value' => '',
				'description' => 'Entering a number here will set a number of days after which a password will need to be refreshed.  The user will get warned of this on logon.',
				'created_by' => 0,
			],
			'PasswordMaxAttempts' => [
				'name' => 'Password Maximum Attempts',
				'type' => 'integer',
				'status' => 1,
				'value' => 10,
				'description' => 'Entering a number here will limit the number of access attempts allowed.',
				'created_by' => 0,
			],
			'H5P_CLIENT_ID' => [
				'name' => 'H5P Client Id',
				'type' => 'string',
				'value' => 'emilelchananreisserweston-a8432',
				'status' => 1,
				'description' => 'H5P LTI 1.1 Client Id - this is licenced for demo purposes only (DO NOT USE ON A LIVE INSTALL AS THESE RESOURCES ARE LIKELY TO GET DELETED PERIODICALLY).  Change the LTI configuration if H5P is purchased by your organisation to your own secret key.  See https://www.e-learningwmb.com/page/h5p for details on how this is done.',
				'created_by' => 0,
			],
			'H5P_CLIENT_SECRET' => [
				'name' => 'H5P Client Secret',
				'type' => 'string',
				'value' => 'tsqvZC2P9sZx0eOTjIE99AoyYWbjweA0',
				'status' => 1,
				'description' => 'H5P LTI 1.1 Secret  - this is licenced for demo purposes only (DO NOT USE ON A LIVE INSTALL AS THESE RESOURCES ARE LIKELY TO GET DELETED PERIODICALLY).  Change the LTI configuration if H5P is purchased by your organisation to your own secret key.  See https://www.e-learningwmb.com/page/h5p for details on how this is done.',
				'secure' => true,
				'created_by' => 0,
			],
			'H5P_LTI_URL' => [
				'name' => 'H5P LTI 1.1 Url',
				'type' => 'string',
				'value' => 'https://elearningwmb.h5p.com/lti',
				'status' => 1,
				'description' => 'H5P LTI 1.1 Url  - this is licenced for demo purposes only (DO NOT USE ON A LIVE INSTALL AS THESE RESOURCES ARE LIKELY TO GET DELETED PERIODICALLY).  Change the LTI configuration if H5P is purchased by your organisation to your own secret key.  See https://www.e-learningwmb.com/page/h5p for details on how this is done.',
				'created_by' => 0,
			],
			'allowIframeEmbedFromDomains' => [
				'name' => 'Specify domains that can embed site.',
				'description' => "Example: 'self' example.com *.example.net",
				'type' => 'string',
				'value' => '',
				'status' => 1,
				'created_by' => 0,
			],
			'reEnableUsersOnImport' => [
				'name' => 'Enable disabled users on Import.',
				'type' => 'boolean',
				'value' => 1,
				'status' => 1,
				'description' => 'If manual or automatic user import is done on user who was disabled on system, user will be enabled back to active status.',
				'created_by' => 0,
			],
			'civicaPaymentsEngine' => [
				'name' => 'Civica Payments Engine.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'If set to true then cost settings are enabled to interact with the system’s configured payments engine.',
				'created_by' => 0,
				'is_payment_configuration' => true,
			],
			'redirectBackToLesson' => [
				'name' => 'Redirect learner back to lesson upon closing learning resource that is part of lesson.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 1,
				'description' => 'If learning resource is part of lesson, after closing player for that resource, learner is taken back to lesson.',
				'created_by' => 0,
			],
			'registerShowCountry' => [
				'name' => 'Show Country And City field when registering new user..',
				'type' => 'boolean',
				'status' => 1,
				'value' => 1,
				'description' => '',
				'created_by' => 0,
			],
			'registerShowCompany' => [
				'name' => 'Show %%company%% field when registering new user..',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Public user register page will show %%company%% drop-down with all companies entered in system.',
				'created_by' => 0,
				'update_name_desc' => true,
			],
			'registerMandatoryCompany' => [
				'name' => '%%company%% field in registration page is mandatory',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => "Setting this value to true will make the company field in the registration page mandatory. Remember to activate the registration (allowRegistration = true) for this setting to come into effect.",
				'created_by' => 0,
			],
			'CertificateMessageTop' => [
				'name' => 'Certificate top message.',
				'type' => 'string',
				'status' => 1,
				'value' => $settings['DefaultCertificateMessageTop'],
				'description' => 'First message for certificate below logo.',
				'created_by' => 0,
			],
			'CertificateMessageBottom1' => [
				'name' => 'Certificate bottom first message.',
				'type' => 'string',
				'status' => 1,
				'value' => $settings['DefaultCertificateMessageBottom1'],
				'description' => 'First bottom message, footer.',
				'created_by' => 0,
			],
			'CertificateMessageBottom2' => [
				'name' => 'Certificate bottom second message.',
				'type' => 'string',
				'status' => 1,
				'value' => $settings['DefaultCertificateMessageBottom2'],
				'description' => 'Second bottom message, footer.',
				'created_by' => 0,
			],
			'enableGlobalOutlookIntegration' => [
				'name' => 'Enable Global Outlook integration.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => "Add all events to a single (global) Outlook calendar. The GlobalOutlookIntegrationSecurityToken options must also be set.",
				'created_by' => 0,
			],
			'GlobalOutlookIntegrationSecurityToken' => [
				'name' => 'Global Outlook integration security token.',
				'type' => 'string',
				'status' => 1,
				'secure' => 1,
				'value' => $settings['GlobalOutlookIntegrationSecurityToken'] ?? "",
				'description' => "Security token used to add all events to users' calendars. Use this link to generate a token " . $settings["LMSUrl"] . "./teams/globaloutlooktoken",
				'created_by' => 0,
			],
			'addAllEventsToOutlook' => [
				'name' => 'Add all events to Outlook.',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => "Automatically add all events to Outlook. This will only work if enableGlobalOutlookIntegration is true and GlobalOutlookIntegrationSecurityToken contains a valid security token. ",
				'created_by' => 0,
			],
			'OutlookRequestResponse' => [
				'name' => 'Outlook Request Response',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => "Let participants in an event inform the event owner whether they will attend or not.",
				'created_by' => 0,
			],
			'DefaultLearnerScreen' => [
				'name' => 'Default Learner Screen.',
				'type' => 'select-list',
				'status' => 1,
				'value' => 'resources',
				'select_values' => [
					[
						'name' => 'Calendar',
						'value' => 'tasks/calendar'
					],
					[
						'name' => 'Tasks',
						'value' => 'tasks/list'
					],
					[
						'name' => 'Learning',
						'value' => 'resources'
					],
					[
						'name' => 'Reports',
						'value' => 'reports'
					]
				],
				'description' => "The default learner screen is \"Learning\".  Set to \"Calendar\", \"Tasks\" or \"Learning\" to change the screen the learner loads in once logged in or presses the home link.\n\nConsider following other configuration options:\n1) startupCourseID - by specifying resource ID, user will be redirected to it and it will be auto-played.\n2) isLearnerLandingPage - if this is set to true, learner will be redirected to category landing page.",
				'created_by' => 0,
			],
			'hideReviewButton' => [
				'name' => 'Hide review button',
				'type' => 'boolean',
				'status' => 1,
				'value' => 1,
				'description' => 'This hides the old style review functionality from the Manager’s view of the learner record which has been superseded',
				'created_by' => 0
			],
			'DisableUserDepartmentOnImport' => [
				'name' => 'When user is imported with specified department, they are automatically disabled.',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => 'Specify exact Department name in value field if you wish to automatically disable users, with that department, during import.',
				'created_by' => 0,
			],
			'EnableUserDepartmentOnImport' => [
				'name' => 'When user is imported with specified department, they are automatically enabled.',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => 'Specify exact Department name in value field if you wish to automatically enable users, with that department, during import.',
				'created_by' => 0,
			],
			'allowLearnerUploads' => [
				'name' => 'Allow Learner Uploads.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 1,
				'description' => 'Making this value false will prevent any learners from uploading files to the system.',
				'created_by' => 0,
			],
			'HideReflectiveLog' => [
				'name' => 'Hide Reflective Log',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Set this value to true if you want to hide the Reflective Log option when learners add files to the system. The reflective log is a template which allows learners to add reflections on what they have learnt (e.g. at work via on the job training etc. to evidence learning to others - useful for marking apprenticeships etc). Other options include adding Evidence of Work or adding a new form.',
				'created_by' => 0,
			],
			'MicrosoftAppClientId' => [
				'name' => 'Microsoft Application Client Id.',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => trim("
				You need to define the MicrosoftAppClientId and MicrosoftAppClientSecret to integrate MS Services. " .
				"Currently these values are set using Open eLMS’s MS Azure values for demonstration purposes only.\n\n" .
				"To create the app, log in to https://portal.azure.com and create an app.  Then set the value in Open eLMS " .
				"Configuration settings for the MicrosoftAppClientId to its client id and set MicrosoftAppClientSecret. " .
				"Give this app the correct permissions (i.e. accessing Teams, One Drive, PowerBi, Outlook calendar, etc.) " .
				"and set up the following redirect urls: \n\n" .
					str_replace($settings["LMSUri"], "", $settings["LMSUrl"]) . "/msintegration/teams (to enable teams integraton)\n" .
					str_replace($settings["LMSUri"], "", $settings["LMSUrl"]) . "/msintegration/powerbi (powerbi)\n" .
					str_replace($settings["LMSUri"], "", $settings["LMSUrl"]) . "/msintegration/teams/outlook (outlook)\n" .
					str_replace($settings["LMSUri"], "", $settings["LMSUrl"]) . "/msintegration/teams/onedrive (one drive, needed to fetch teams recordings)\n"),
				'created_by' => 0,
			],
			'MicrosoftAppClientSecret' => [
				'name' => 'Microsoft Application Client Secret.',
				'type' => 'string',
				'status' => 1,
				'secure' => 1,
				'value' => '',
				'description' => trim("
				You need to define the MicrosoftAppClientId and MicrosoftAppClientSecret to integrate MS Services. " .
				"Currently these values are set using Open eLMS’s MS Azure values for demonstration purposes only.\n\n" .
				"To create the app, log in to https://portal.azure.com and create an app.  Then set the value in Open eLMS " .
				"Configuration settings for the MicrosoftAppClientId to its client id and set MicrosoftAppClientSecret. " .
				"Give this app the correct permissions (i.e. accessing Teams, One Drive, PowerBi, Outlook calendar, etc.) " .
				"and set up the following redirect urls: \n\n" .
					str_replace($settings["LMSUri"], "", $settings["LMSUrl"]) . "/msintegration/teams (to enable teams integraton)\n" .
					str_replace($settings["LMSUri"], "", $settings["LMSUrl"]) . "/msintegration/powerbi (powerbi)\n" .
					str_replace($settings["LMSUri"], "", $settings["LMSUrl"]) . "/msintegration/teams/outlook (outlook)\n" .
					str_replace($settings["LMSUri"], "", $settings["LMSUrl"]) . "/msintegration/teams/onedrive (one drive, needed to fetch teams recordings)\n"),
				'created_by' => 0,
			],
			'UseHTMLLineBreaksInOutlook' => [
				'name' => 'Replace line breaks in Outlook calendar emails with HTML tags.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Replace new lines with <BR/> tags.',
				'created_by' => 0,
			],
			'isTrackLearningResourceTime' => [
				'name' => 'Track Time for Learning Resources.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 1,
				'description' => 'Set this value to true to track time on each learning resource.',
				'created_by' => 0,
			],
				'setSmallPromoImage' => [
				'name' => 'Set Small Promo Image.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Setting this value to true reduces the size of the promo image so more of the screen is visible.',
				'created_by' => 0,
			],
			'enableImpersonate' => [
				'name' => 'Enable Impersonate Learners by Admin functionality.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 1,
				'description' => 'Set this value to true to allow Administrators impersonate Learners.',
				'created_by' => 0,
			],
			'isCivicaPaymentsEngine' => [
				'name' => 'Enabled the Civica Payments Engine',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Enabling this option will allow learning to be purchased from the system.',
				'created_by' => 0,
				'is_payment_configuration' => true,
			],
			'civicaDefaultGeneralLedgerCode' => [
				'name' => 'Civica Default General Ledger Code',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => 'This is the default general ledger code used for Civica payments.  The system otherwise records the ‘Learning Resource Code’ if it is defined.',
				'created_by' => 0,
				'is_payment_configuration' => true,
			],
			'civicaPaymentsEngineRequestURL' => [
				'name' => 'Civica Payments Engine Request URL',
				'type' => 'string',
				'status' => 1,
				'value' => 'https://www.civicaepay.co.uk/SuffolkPartnershipXMLTest/Paylinkxmlui/Default.aspx',
				'description' => 'Request URL for Civica Payments Engine. Note: remove the word Test for the Live configuration',
				'created_by' => 0,
				'is_payment_configuration' => true,
			],
			'civicaPaymentsEngineVatCode' => [
				'name' => 'Civica Vat code relating to product',
				'type' => 'string',
				'status' => 1,
				'value' => '05',
				'description' => 'Vat code relating to product, default to use is 05 zero rated [01|Standard|20.00] [03|Fuel rate|5.00] [04|Out of scope|0.00] [05|Zero rated|0.00] [06|Exempt:0.00]',
				'created_by' => 0,
				'is_payment_configuration' => true,
			],
			'CalendarStartTime' => [
				'name' => 'This is the start time of shown in all calendars throughout Open eLMS',
				'type' => 'string',
				'status' => 1,
				'value' => '0800',
				'default_value' => '0800',
				'description' => 'This is the start time of shown in all calendars throughout Open eLMS.  This value defaults to 0800 if not set.',
				'created_by' => 0,
			],
			'CalendarEndTime' => [
				'name' => 'This is the end time of shown in all calendars throughout Open eLMS',
				'type' => 'string',
				'status' => 1,
				'value' => '1800',
				'default_value' => '1800',
				'description' => 'This is the end time of shown in all calendars throughout Open eLMS. This value defaults to 1800 if not set.',
				'created_by' => 0,
			],
			'showSevenDepartmentSubLevels' => [
				'name' => 'Show 6 sublevels of department',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => "Setting this value to true will create 7 levels of location each connected to each other e.g. London, Southwark, Tooley Street, Building A, Zone B, Room 2, Desk Y)",
				'created_by' => 0,
			],
			"showResetCustomDatesButton" => [
				'name' => 'Show reset custom completion dates button for learning programmes',
				'type' => 'select-list',
				'status' => 1,
				'value' => 'none',
				'select_values' => [
					[
						'name' => 'Administrators',
						'value' => 'admin'
					],
					[
						'name' => 'Managers',
						'value' => 'manager'
					],
					[
						'name' => 'No one',
						'value' => 'none'
					]
				],
				'description' => "Level of access to the option to reset custom completion dates for ALL users in a learning programme",
				'created_by' => 0,
			],
			"showUserResetCustomDatesButton" => [
				'name' => 'Show reset custom completion dates button for users',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => "Show to the button for reseting custom completion dates for a particular user in a given learning programme",
				'created_by' => 0,
			],
			"optionalResourceProgrammeLink" => [
				'name' => 'Optional %%programme%% selection',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => "Make %%programme%% selection optional",
				'created_by' => 0,
			],
			"showRecommendations" => [
				'name' => 'Show learning recommendations',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => "Show recommended learnings",
				'created_by' => 0,
			],
			"listAllEventsToManagers" => [
				'name' => 'Show all events to all Managers',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => "All managers will be able to see all events, with certain restrictions.",
				'created_by' => 0,
			],
			'CrontTaskKillTime' => [
				'name' => 'Maximum time cron task is allowed to run before it is killed, minutes',
				'type' => 'integer',
				'status' => 1,
				'value' => 120,
				'description' => 'Default is 2 hours, increase or decrease as you see fit, ceiling is 5 hours, if no value is set, it defaults to 2 hours. Minimum is 20 minutes.',
				'created_by' => 0,
			],
			"importUserCodeForMissingUsername" => [
				'name' => 'Use usercode if username is missing during user import.',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => "If users are imported without username and there is usercode in table, use that to set up username instead of email",
				'created_by' => 0,
			],
			'defaultEmailFromName' => [
				'name' => 'Overwrites email from field to all emails sent out from system',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => "Leave empty to use name in from field, from user who sent out email.",
				'created_by' => 0
			],
			'LearningResourceVisibleDays' => [
				'name' => 'Hide learning on Learners view after x days, after completion.',
				'type' => 'integer',
				'status' => 1,
				'value' => '',
				'description' => 'This number indicates the number of days after which any learning on the learner’s view and calendar will be hidden.  The Progress view will continue to show all events/learning resources.',
				'created_by' => 0,
			],
			'EventsVisibleDays' => [
				'name' => 'Hide events on Learners view after x days, after completion.',
				'type' => 'integer',
				'status' => 1,
				'value' => '',
				'description' => 'This number indicates the number of days after which any event (lesson/meeting etc.) on the learner’s view and calendar will be hidden.  The Progress view will continue to show all events/learning resources.',
				'created_by' => 0,
			],
			'isLearningOutsideProgrammeVisibleonFirstLogin' => [
				'name' => 'Learning outside the learning programme is visible when first logging in.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 1,
				'description' => 'Learning resources assigned directly to each learner (not part of the selected programme) are visible when first logging in.',
				'created_by' => 0,
			],
			'isCompletedLearningVisibleonFirstLogin' => [
				'name' => 'Completed learning is visible to learners when first logging in.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 1,
				'description' => 'Completed learning resources are visible to the learner when first logging in.',
				'created_by' => 0,
			],

			'isInProgressLearningVisibleonFirstLogin' => [
				'name' => 'In progress learning is visible to the learner when first logging in.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 1,
				'description' => 'Learning which is in progress is visible to learners when first logging in.',
				'created_by' => 0,
			],
			'isNotStartedLearningVisibleonFirstLogin' => [
				'name' => 'Not started learning resources are visible when first logging in.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 1,
				'description' => 'Not started learning resources are visible to the learner when first logging in.',
				'created_by' => 0,
			],
			'isFailedLearningVisibleonFirstLogin' => [
				'name' => 'Failed learning resources are visible when first logging in.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 1,
				'description' => 'Failed learning resources are visible to the learner when first logging in.',
				'created_by' => 0,
			],
			'isLockedLearningVisibleonFirstLogin' => [
				'name' => 'Learning which is locked is visible to learners when first logging in.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 1,
				'description' => 'Learning which is locked (due to precursor learning needing to be completed first) is visible to learners when first logging in.',
				'created_by' => 0,
			],
			'isEnrollableLearningVisibleonFirstLogin' => [
				'name' => 'Enrollable learning is visible to learners when first logging in.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Learning which is enrollable (i.e. anyone using the system can browse such learning and use it) is visible to learners when first logging in.',
				'created_by' => 0,
			],
			'isRefresherLearningVisibleonFirstLogin' => [
				'name' => 'Refresher learning is visible to learners when first logging in.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Learning which is need to be refreshed (i.e. repeated periodically to refresh knowledge) is visible to learners when first logging in.',
				'created_by' => 0,
			],
			'isMandatoryLearningVisibleonFirstLogin' => [
				'name' => 'Mandatory learning is visible to learners when first logging in.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Learning which is mandatory (i.e. all learners need to do the training) is visible to learners when first logging in.',
				'created_by' => 0,
			],
			'isFavouriteLearningVisibleonFirstLogin' => [
				'name' => 'Favourite learning is visible to learners when first logging in.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Learning which is has been favourited (sometimes called ‘nudged’) is visible to learners when first logging in.',
				'created_by' => 0,
			],
			'isProgrammeFilterVisibleonFirstLogin' => [
				'name' => 'Programme if assigned is displayed when first logging in.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 1,
				'description' => 'Programme if assigned is visible to learners when first logging in.',
				'created_by' => 0,
			],
			'allowLearnersToDelete' => [
				'name' => 'Allow Learners to delete uploaded evidence',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Allow Learners to delete uploaded evidence',
				'created_by' => 0
			],
			'hideMeetingAndZoom'=>[
				'name'=>'Hide Meet and Zoom',
				'type'=>'boolean',
				'status'=>1,
				'value'=>false,
				'description'=>'Hide meeting from table and learner list',
				'created_by'=>0
			],
			'allowSendCivicaData' => [
				'name' => 'Send Civica Data',
				'type' => 'boolean',
				'status' => 1,
				'value' => '0',
				'description' => 'If set to true, then a data dump of all sales via civica are sent to a named party.  Define CivicaManagerEmail to receive this data.  Define CivicaFrequencyDays to define how frequently this csv file is sent.',
				'created_by' => 0,
			],
			'CivicaManagerEmail' => [
				'name' => 'Civica Manager Email',
				'type' => 'list',
				'status' => 1,
				'value' => '',
				'description' => 'Define CivicaManagerEmail to receive Civica data periodically. See allowSendCivicaData to activate this functionality. Define CivicaFrequencyDays to define how frequently this csv file is sent.',
				'created_by' => 0,
			],
			'CivicaFrequencyDays' => [
				'name' => 'Civica Frequency Days',
				'type' => 'integer',
				'status' => 1,
				'value' => '',
				'description' => 'Define CivicaFrequencyDays to define how frequently this csv report is sent out.  See allowSendCivicaData to activate this functionality.  Define CivicaManagerEmail to record where the data is sent to.',
				'created_by' => 0,
			],
			'ipBlock' => [
				'name' => 'Block specified IP\'s or range.',
				'description' => "Specify IP or IP range to block from accessing site.\nExamples:\n192.168.10.1\n192.168.10.*\n10.1.0.0-************\n172.16.1.0/24",
				'type' => 'list',
				'value' => '',
				'status' => 1,
				'created_by' => 0,
			],
			'alternativeIdsEnabled' => [
				'name' => 'Alternative User IDs.',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => "If this is set, then this allows more than one account to be shown on the same learner interface which share the same Alternative ID. So the learner interface could show learning for multiple employee IDs.",
				'created_by' => 0,
			],
			'uniqueEmailPerUser' => [
				'name' => 'Allow Unique Email For User.',
				'type' => 'boolean',
				'status' => 1,
				'value' => true,
				'description' => "Email field is unique in user table, set this to false to allow importing multiple users with same e-mail.",
				'created_by' => 0,
			],
			'uniqueUsernamePerUser' => [
				'name' => 'Allow Unique username field For User.',
				'type' => 'boolean',
				'status' => 1,
				'value' => true,
				'description' => "Username field is unique in user table, set this to false to allow importing multiple users with same username.",
				'created_by' => 0,
			],
			'deleteAndLinkManagersOnUserImport' => [
				'name' => 'Delete user-manager relationship during import and link with specified managers.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => '',
				'created_by' => 0
			],
			"RecommendationsNumber" => [
				'name' => 'Number of recommendations to show',
				'type' => 'integer',
				'status' => 1,
				'value' => '10',
				'description' => "Number of recommended learnings  to show",
				'created_by' => 0
			],
			'enableMightlyUserDataExport' => [
				'name' => 'Nightly user data export',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Exports enabled user data on CSV file, every night. Contact system provider for more information',
				'created_by' => 0,
			],
			'isBrowsebyDefault' => [
				'name' => 'to not open last Learning resource on login',
				'type' => 'boolean',
				'status' => 1,
				'value' => true,
				'description' => 'This hides a promo image of the last selected learning resource when the user first logs into the system.',
				'created_by' => 0,
			],
			'cronTaskDebug' => [
				'name' => 'Sends alerts to system administrator if there is problem with any Cron task.',
				'type' => 'boolean',
				'status' => 1,
				'value' => true,
				'description' => '',
				'created_by' => 0,
			],
			'hideNotAssignedLearnerInformationToManagersInEvents' => [
				'name' => 'Hide learner information in event view to managers who do not have access to these learners.',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => "Setting this to true will send an email alert to the Open eLMS system administrator if there are problems with any Cron task.This is a setting controlled by Open eLMS and set to true in cases where there are issues with the Open eLMS CRON tasks.",
				'created_by' => 0,
				'delete' => true,
			],
			'AttachManagerstoUploads'=>[
				'name'=>'Attach Assigned Managers to Uploads',
				'type'=>'select-list',
				'status'=>1,
				'value'=>'',
				'description'=>"Setting this value determines how learners and managers can select assigned managers to an upload.  The four options are listed below, use the string for each option e.g. 'Attach Assigned Managers' to activate that option (a blank value will remove the option).
1. Attach Assigned Managers
This allows learners and managers to attach managers already assigned to that learner when adding/editing an upload.
2.Attached Any Managers
This allows learners and managers to attach any manager in the system to that learner when adding/editing an upload.
3.Attach Assigned Managers by Manager only
This allows only managers to attach managers already assigned to that learner when adding/editing an upload.
4.Attached Any Managers by Manager only
This allows only managers to attach any manager in the system to that learner when adding/editing an upload.",
				'created_by'=>0,
				'select_values' => [
					[
						'name'=>'',
						'value'=>''
					],
					[
						'name'=>'Attach Assigned Managers',
						'value'=>'attach_assigned_managers'
					],
					[
						'name'=>'Attached Any Managers',
						'value'=>'attached_any_managers'
					],
					[
						'name'=>'Attach Assigned Managers by Manager only',
						'value'=>'attach_assigned_managers_by_manager_only'
					],
					[
						'name'=>'Attached Any Managers by Manager only',
						'value'=>'attached_any_managers_by_manager_only'
					]
				]
			],
			'showSchedulePeriodicReview' => [
				'name' => 'Shows checkbox "Schedule Periodic Review" for programmes.',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => '',
				'created_by' => 0,
			],
			'showEditExpectedCompletionDateResource' => [
				'name' => 'Display and edit expected completion date inside resource view',
				'type' => 'boolean',
				'status' => 1,
				'value' => true,
				'description' => 'If true - display expected completion date inside resource and make available to edit.',
				'created_by' => 0,
			],
			'HideQAReports' => [
				'name' => 'Hide QA Reports',
				'type' => 'boolean',
				'status' => 1,
				'value' => true,
				'description' => 'Setting this value to true will hide all system references to old style QA reports.  This functionality can be switched on to reference QA data pre 2022 (Aphrodite release).',
				'created_by' => 0,
			],
			'SingleSignOnButtonURL' => [
				'name' => 'Single Sign On Button URL',
				'type' => 'text',
				'status' => 1,
				'value' => '',
				'description' => 'Add a URL here to create a button on the login page which will take users to the system via SSO ( e.g. https://lms.e-learningwmb.co.uk/[YOURDOMAIN]/saml ).',
				'created_by' => 0,
			],
			'sessionProtection' => [
				'name' => 'Protect session against fixation',
				'type' => 'boolean',
				'status' => 1,
				'value' => true,
				'description' => 'This will add more security layers on to prevent session fixation.',
				'created_by' => 0,
			],
			// https://emil-reisser-weston.atlassian.net/browse/SCOR-3270
			'hideResourcesNeedAttentionHeaderButton' => [
				'name' => 'Hides the Resources Need Attention button on the header',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'Set this value to true to hide the Resources Need Attention button on the header of the (\'Netflix style\') learner view. This button alerts learners when submitted work has been reviewed, this may not be needed in cases where the learning experience does not require feedback from managers (e.g. manually marked work.)',
				'created_by' => 0,
			],
			'hideHelpHeaderButton' => [
				'name' => 'Hides the Help button on the header',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'Set this value to true to hide the Help button on the header of the (\'Netflix style\') learner view. You may consider that the interface does not need a learner\'s help, if so then this can be removed. Note you can customise the help video by editing HelpVideoURL configuration key.',
				'created_by' => 0,
			],
			'hideLearningHeaderButton' => [
				'name' => 'Hides the Learning button on the header',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'Set this value to true to hide the Learning button on the header of the (\'Netflix style\') learner view. This does the same task as the main company logo and can be removed, so long as the configuration key \'isLearnerLandingPage\' equals to false.',
				'created_by' => 0,
			],
			'hideCalendarHeaderButton' => [
				'name' => 'Hides the Calendar button on the header',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'Set this value to true to hide the Calendar button on the header of the (\'Netflix style\') learner view. Learners do not need this if you are not setting learning events (Open eLMS Classroom) or including deadlines within the calendar.',
				'created_by' => 0,
			],
			'hideAssignmentsHeaderButton' => [
				'name' => 'Hides the Assignments button on the header',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'Set this value to true to hide the Assignmements button on the header of the (\'Netflix style\') learner view. This is a list view of the schedule information, so it is not mission critical and can be hidden if not wanted or you are not booking any live meetings etc.',
				'created_by' => 0,
			],
			'hideReportsHeaderButton' => [
				'name' => 'Hides the Reports button on the header',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'Set this value to true to hide the Reports button on the header of the (\'Netflix style\') learner view. This button allows organisations to distribute reports to learners, if you are not using this functionality in Admin > Review > Custom Reports > Distribute - then you can set this value to \'true\'.',
				'created_by' => 0,
			],
			'hideAddFormsInLearnerFile' => [
				'name' => 'Hide add forms in learner file',
				'type' => 'boolean',
				'status' => 1,
				'value' => true,
				'description' => 'This will hide the add forms section from learner file',
				'created_by' => 0
			],
			'showWorkflowOnLearnerInterface' => [
				'name' => 'Configuration to Hide/Show Work Flow in Learner Interface.',
				'type' => 'boolean',
				'status' => 1,
				'value' => true,
				'description' => 'If true - display Work Flow in Learner Interface.',
				'created_by' => 0
			],
			'showEmailHistoryOnLearnerInterface' => [
				'name' => 'Configuration to Hide/Show Email History in Learner Interface.',
				'type' => 'boolean',
				'status' => 1,
				'value' => true,
				'description' => 'If true - display Email History in Learner Interface.',
				'created_by' => 0
			],
			'isCompletedLearningFilterVisible' => [
				'name' => 'Show the Completed Learning filter on the Learners screen',
				'type' => 'boolean',
				'status' => 1,
				'value' => true,
				'description' => 'Setting this value to true will show the filter which displays completed learning resources to the learner.',
				'created_by' => 0,
			],
			'isLearningOutsideProgrammeFilterVisible' => [
				'name' => 'Show the Learning Outside Program filter on the Learners screen',
				'type' => 'boolean',
				'status' => 1,
				'value' => true,
				'description' => 'Setting this value to true will show the filter which displays learning outside the learning program to the learner.',
				'created_by' => 0,
			],
			'isInProgressLearningFilterVisible' => [
				'name' => 'Show the In Progress Learning filter on the Learners screen',
				'type' => 'boolean',
				'status' => 1,
				'value' => true,
				'description' => 'Setting this value to true will show the filter which displays in progress learning resources to the learner.',
				'created_by' => 0,
			],
			'isNotStartedLearningFilterVisible' => [
				'name' => 'Show the Not Started Learning filter on the Learners screen',
				'type' => 'boolean',
				'status' => 1,
				'value' => true,
				'description' => 'Setting this value to true will show the filter which displays not started learning resources to the learner.',
				'created_by' => 0,
			],
			'isLockedLearningFilterVisible' => [
				'name' => 'Show the Locked Learning filter on the Learners screen',
				'type' => 'boolean',
				'status' => 1,
				'value' => true,
				'description' => 'Setting this value to true will show the filter which displays locked learning resources to the learner.',
				'created_by' => 0,
			],
			'isEnrollableLearningFilterVisible' => [
				'name' => 'Show the Enrollable Learning filter on the Learners screen',
				'type' => 'boolean',
				'status' => 1,
				'value' => true,
				'description' => 'Setting this value to true will show the filter which displays enrollable learning resources to the learner.',
				'created_by' => 0,
			],
			'isRefresherLearningFilterVisible' => [
				'name' => 'Show the Refresher Learning filter on the Learners screen',
				'type' => 'boolean',
				'status' => 1,
				'value' => true,
				'description' => 'Setting this value to true will show the filter which displays refresher learning resources to the learner.',
				'created_by' => 0,
			],
			'isMandatoryLearningFilterVisible' => [
				'name' => 'Show the Mandatory Learning filter on the Learners screen',
				'type' => 'boolean',
				'status' => 1,
				'value' => true,
				'description' => 'Setting this value to true will show the filter which displays mandatory learning resources to the learner.',
				'created_by' => 0,
			],
			'isFavouriteLearningFilterVisible' => [
				'name' => 'Show the Favourite learning filter on the Learners screen',
				'type' => 'boolean',
				'status' => 1,
				'value' => true,
				'description' => 'Setting this value to true will show the filter which displays favourite learning resources to the learner.',
				'created_by' => 0,
			],
			'isProgrammeFilterVisible' => [
				'name' => 'Show the Program Filter on the Learners screen',
				'type' => 'boolean',
				'status' => 1,
				'value' => true,
				'description' => 'Setting this value to true will show the filter which displays programme resources to the learner.',
				'created_by' => 0,
			],
			'redirectAllLinksTroughSSO' => [
				'name' => 'All links in email templates will go trough SSO',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'Any link in email will go trough SSO system then redirect back to original URL.',
				'created_by' => 0,
				'delete' => true,
			],
			'redirectAllLinksThroughSSO' => [
				'name' => 'All links in email templates will go trough SSO',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'Any link in email will go trough SSO system then redirect back to original URL.',
				'created_by' => 0,
			],
			'duplicatedEmailCheck' => [
				'name' => 'Disable duplicated emails check when sending out emails', // This was introduced because on huge emails site slowed to crawl.
				'type' => 'boolean',
				'status' => 1,
				'value' => true,
				'description' => 'Set this to true should you want administrators to check for multiple emails going to the same person.  It is a good idea to set this value to true to avoid email queries resulting in spamming the recipient with multiple emails.',
				'created_by' => 0,
			],
			'RegisterCompanyText' => [
				'name' => 'Organisation field in registration page is text',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'When true, this will allow that user enters free text for organisation during registration and this will be created in the system as new organisation (or if the text matches existing organisation with the same name, the user will be added to it)',
			],
			'showGraphFilterForWrap' => [
				'name' => 'Show wrap specific options for graph',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => "Setting this value to true will show dynamic department filter for graph",
				'created_by' => 0,
			],
			'duplicatedEmailNotification' => [
				'name' => 'Notify system administrator about duplicated emails being sent out.',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => '',
				'created_by' => 0,
			],
			'duplicatedEmailLimit' => [
				'name' => 'Set limit of duplicated emails allowed to go out in same day.',
				'type' => 'integer',
				'status' => 1,
				'value' => 2,
				'description' => 'If email sent out to same person with same content goes out in same day more than x additional times, then prevent it from sending out, record this in audit log.',
				'created_by' => 0,
			],
			'RolesListHelpUrl' => [
				'name' => 'Help Video on the bottom of role select page',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => "Put link to help video/page/document if needed.",
				'created_by' => 0,
			],
			'ApproveEventUserAfterPayment' => [
				'name' => 'Approve user automatically when user is paid for',
				'type' => 'boolean',
				'status' => 1,
				'value' => true,
				'description' => 'This makes any management approval unnecessary if a payment has been made by the user to enrol on an event.  It is advisable to set this value to true if the system is taking payments, else the system will take money off the learner but then may not be able to fulfil the service.',
				'created_by' => 0,
			],
			'loadingAnimation' => [
				'name' => 'File name of selected Loading animation.',
				'type' => 'string',
				'status' => 1,
				'value' => 'gears.svg',
				'description' => 'This is the name of the loading image which appears between screens.  To review the options for changing this, go to Admin > System Settings > Branding to review the options displayed in this order: block-pulse.svg, circles-warp.svg, circles.svg, dna.svg and  gears.svg.',
				'created_by' => 0,
			],
			'enableProgrammeTitlesLearnerLandingPage' => [
				'name' => 'Enable Learner landing page grouping categories using Programme.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'If the landing page is activated for the learner (i.e. isLearnerLandingPage = true) then this option will group all categories on that landing page by the respective assigned programme for the learner.It is advised to set this to true if you envisage most learners to have more than one programme of learning to attend to.',
				'created_by' => 0,
			],
			'askForFeedbackOnComplete' => [
				'name' => 'Ask the user for feedback when completing the learning resource.',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'Set this value is true to automatically directed the learner to the feedback form once the learning is completed.',
				'created_by' => 0,
			],
			'randomString' => [
				'name' => 'A randomly selected string that signals a changed system state.',
				'type' => 'string',
				'status' => 1,
				'value' => '1234567890abc',
				'description' => '',
				'created_by' => 0,
			],
			'daysSinceLastReviewWarningThreshold' => [
				'name' => 'Days since last review - warning threshold',
				'type' => 'integer',
				'status' => 1,
				'value' => 90,
				'description' => 'Set this value in days, so it will warn each user so that they need to book a follow up review with their manager.  For instance if learners are required to have 3 monthly reviews, then set this value to 90.',
			],
			'isSearchTabVisible' => [
				'name' => 'Show Search field on the Learners\' screen',
				'type' => 'boolean',
				'status' => 1,
				'value' => true,
				'description' => "Setting this value to true will show the Search field on the learners\' screen.\nSetting this value to false will hide the Search field on the learners\' screen.",
				'created_by' => 0,
				'category' => 'learner_interface',
				'update_category' => true,
			],
			'showWeekCountForOutcome' => [
				'name' => 'Show Week count in Criteria Of Programme',
				'type' => 'boolean',
				'status' => 1,
				'value' => true,
				'description' => "Setting this value to true will show the week field in Add criteria Screen of Programme.",
				'created_by' => 0,
			],
			'HideLearningProgrammesReporting' => [
				'name' => 'Hide legacy %%programmes%% reporting',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'Set this value to true to hide the old style reporting from users who create reports.',
				'created_by' => 0,
			],
			'HideLearingResourcesReporting' => [
				'name' => 'Hide legacy %%lesson_resources%% reporting',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'Set this value to true to hide the old style reporting from users who create reports.',
				'created_by' => 0,
			],
			'showShareResourceSsoUrl' => [
				'name' => 'Show share button in learner interface for SSO users.',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'This button will copy resource url in memory that will work with SSO.',
				'created_by' => 0,
				'update_name_desc' => true,
			],
			'showShareResourceUrl' => [
				'name' => 'Show share button in learner interface.',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'This button will copy resource url in memory that will work even without SSO.',
				'created_by' => 0,
				'update_name_desc' => true,
			],
			'formSignatureDisable' => [
				'name' => 'Disable form sign off signature in forms',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'Disable form sign off signature in forms. I will hide signature box from sign off section',
				'created_by' => 0,
			],
			'userRegisterWaitingForApprovalMessage' => [
				'name' => 'Default message that is shown when user registers and needs administration approval.',
				'type' => 'string',
				'status' => 1,
				'value' => 'You have been successfully registered! Please wait for the admin approval.',
				'description' => 'Should isApproveLearners be set to true, then set this message in order to warn any registrants that they will need to wait for administration approval prior to accessing the system.',
				'created_by' => 0,
			],
			'isLearnerSkillsSignOff' => [
				'name' => "Enforce learners to sign off skills",
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => "Setting this value to true will allow learners to attest that they have achieved the skill (as well as management.)  This will turn the skill green on the management interface. Setting it to false will remove the sign off from the learner interface completely.",
				'created_by' => 0,
			],
			'SignOffCompletedSkillsProgramme' => [
				'name' => "Sign-Off completed Skills Programme",
				'type' => 'boolean',
				'status' => 1,
				'value' => 1,
				'description' => 'Set this value to true to ensure that all skills in a skills monitoring programme are set to "Complete" before the entire programme can be signed off.',
				'created_by' => 0,
			],
			'SignOffAutomatedSkillsMonitoring' => [
				'name' => "Automatic Sign-Off of Skills Monitoring Programmes",
				'type' => 'boolean',
				'status' => 1,
				'value' => 1,
				'description' => 'Set this value to true if you want skills monitoring programmes to automatically set to ‘Complete’ once all associated skills are set to a completed status.  Setting this to false will display a manual sign-off option.',
				'created_by' => 0,
			],
			'eventReminderAdvanceNotificationInterval' => [
				'name' => 'Event Reminder Advance Notification Interval',
				'type' => 'list',
				'status' => 1,
				'value' => '1,7',
				'description' => 'Send reminder interval measured in days for an automated email called "Event Reminder Advance Notification" to be sent to all meeting attendees.',
				'created_by' => 0,
			],
			'manualAdminURL' => [
				'name' => 'Link to system’s manual, administration interface.',
				'type' => 'string',
				'status' => 1,
				'value' => 'https://docs.google.com/document/d/1my5uV3i2xezHQeZ44nltTjRTBDnjmd4HvWqGYXT8Ocs',
				'description' => "This is the link to the system manual available via the administrator's menu in the top right hand corner of the screen.  The default manual is linked at https://docs.google.com/document/d/1my5uV3i2xezHQeZ44nltTjRTBDnjmd4HvWqGYXT8Ocs but this can be changed to a custom branded manual or branded document should you wish to do this.",
				'created_by' => 0,
			],
			'globalHelpURL' => [
				'name' => 'Will be included in IFRAME to all help modal windows.',
				'type' => 'string',
				'status' => 1,
				'value' => 'https://d30jalha6vlrl8.cloudfront.net/',
				'description' => '',
				'created_by' => 0
			],
			'showLegacyReportsManageButtons' => [
				'name' => 'Will show "Add view" and "Report visbility" button.',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => "Legacy reporting include 'Learning Resource' and 'Learning Programme' reports.  These are being phased out and can be disabled by making this false.  A value of true will show the respective 'Add view' and 'Report visibility' buttons.",
				'created_by' => 0,
			],
			'sendEmailsCronAssignTask' => [
				'name' => 'Send emails to users when assigning resources to department/job/group users.',
				'type' => 'boolean',
				'status' => 1,
				'value' => true,
				'description' => 'Administrator will need to approve any outgoing email in Email Queue list, Audit section.  Set this to true whilst testing the system.   Note all emails will be sent in the queue when this is set back to false.',
				'created_by' => 0,
			],
			'showLinkedAccountsInUserMenuAllTheTime' => [
				'name' => 'If set to true, linked accounts will be shown for all user roles, not only learner interface',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'If set to true, linked accounts will be shown for all user roles, not only learner interface.  This will allow users who are logged in as managers, admins etc. to switch to another (linked account) in the system.  Note users can have linked accounts by sharing Alternative User IDs; the configuration value alternativeIdsEnabled needs to be true to start enabling this functionality.',
				'created_by' => 0,
			],
			'forceLessonDueDateToResource' => [
				'name' => 'Overwrite resources due date with lessons.',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => "Lessons are a batch or collection of learning resources.  Learning resources can have separate sign off dates depending on their refresh rate, when they were individually assigned or in accordance with an associated learning prorgamme's timing.  Lessons can have a separate due date which does not coincide with the learning resource due date.  Set this value to true if you want all learning resource dates to display the associated lesson's due date instead.  Setting this value to largely due to personal preference and how learning is set up, leave this as false and change to true dependent upon user feedback.",
				'created_by' => 0,
			],
			'uniqueEmailPerUserLogin' => [
				'name' => 'If set to true, by logging in email will be used to find user as well.',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'By logging in using email, first linked account will be found and user logged into that, if any of linked accounts are defined as Primary, it will be used as priority, Secondary, etc.',
				'created_by' => 0,
				'delete' => true,
			],
			'enableEmailUserLogin' => [
				'name' => 'If set to true, by logging in, email will be used to find user as well.',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'By logging in using email, first linked account will be found and user logged into that, if any of linked accounts are defined as Primary, it will be used as priority, Secondary, etc.',
				'created_by' => 0,
			],
			'userImportReplaceUsernameWithEmplyeeId' => [
				'name' => 'If set to true, this can be used to change usernames for users.',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'Username(and employee_id) field from spreadsheet will be used to look up users(username in table), if username is different than employee_id specified in spreadsheet, then username in user table will be replaced with employee_id',
				'created_by' => 0,
			],
			'AssessmentNoClose' => [
				'name' => 'Do not close assessments when submitted, before last screen',
				'type' => 'boolean',
				'status' => 1,
				'value' => true,
				'description' => 'If set to true, assesment will keep open when submitted.',
				'created_by' => 0,
			],
			'APITokenAccess' => [
				'name' => 'Token string for accessing API requests.',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => '',
				'secure' => true,
				'created_by' => 0
			],
			'hideLaunchSkills' => [
				'name' => 'Hide Launch resource button from Learning resource of type Skills.',
				'type' => 'boolean',
				'status' => 1,
				'value' => true,
				"description" => 'Hide Launch resource button from Learning resource of type Skills.',
				'created_by' => 0,
			],
			'enableAutomaticStarUpInstructionEmails' => [
				'name' => 'Automatically Send Start Up Instruction Emails',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'Automatically send start up instructions to newly added users.',
				'created_by' => 0,
			],
			'enableSnoozeSignOffForm' => [
				'name'=> 'Snooze Sign off form enable',
				'type' => 'boolean',
				'status'=> 1,
				'value'=>false,
				'description'=>'If set to true , Snooze sign off form option will be show',
				'created_by'=>0,
			],
			'ExtraUserFieldsWatchAndPosRef' => [
				'name' => 'Use additional fields when editing users.',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => "This adds to extra fields called 'Position Reference' and 'Watch' to the user details.  These are specific to fire services and this value can be set to false in 99% of cases.",
				'created_by' => 0,
			],
			'assignAllLinkedUsersToAllLinkedManagers' => [
				'name' => 'Whenever user is assigned to manager, cross-assign all linked accounts as well',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'Set this to true if you are using linked accounts (alternativeIdsEnabled is set to true) and you want any user assigned to a manager to be cross-assign all linked accounts as well.  This defaults to false and is dependent upon how managerial responsibility in your organisation works.',
				'created_by' => 0,
			],
			'assignUsersToProgrammeDuringEventImport' => [
				'name' => 'Assign users to Programme when importing Events linked with Programme',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'If you are importing events into the system which has a linked programme, then assign the associated programme to the learner for who you are importing the event.  This is only relevant if importing linked events which make up part of a programme and that programme would for some reason no already be assigned to the learner.  This is a fairly unique use case and it is recommended to leave this value as false.',
				'created_by' => 0,
			],
			'PasswordResetAnonymousMessage' => [
				'name' => 'By setting this setting to true, when user resets password, no information will be leaked if user exists in system or not.',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => "Set this to true if you do not want the username to be sent with the password reset message.  Change this in line with your organisation's security policy.",
				'created_by' => 0,
			],
			'ManagerAccessToLinkedUsers' => [
				'name' => 'Managers who have user assigned will have access to all other users linked by 1st user email.',
				'type' => 'boolean',
				'status' => 1,
				'value' => true,
				'description' => 'In the event you are using linked accounts (alternativeIdsEnabled is set to true) then set this value to true for a manager to see all users who have linked accounts.  This can be left as true (as will not affect most cases).  Check the value of assignAllLinkedUsersToAllLinkedManagers to see how this is set - if set True then this value should be left as true.',
				'created_by' => 0,
			],
			'eventsListLessonsOnlyFromLibrary' => [
				'name' => 'When event is created, lessons created only in learning library will be shown.',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'Lessons can be created in one of two ways: they can be created in the Learning Library OR then can be created when an event is created and learning resources are added to it, this lesson can be saved for reuse later.  Set this value to true to restrict the lessons to those created in the library, this will give you greater control over learning content but will not allow normal trainers to add to the library of learning.  We recommend leaving this as false and switching it to true if the amount of lessons added becomes uncontrollable.',
				'created_by' => 0,
			],
			'systemEmailSendingTime' => [
				'name' => 'Set time when system emails will be sent out.',
				'type' => 'string',
				'status' => 1,
				'value' => '08:00-17:30', // ^(?:[01]\d|2[0-3]):[0-5]\d-(?:[01]\d|2[0-3]):[0-5]\d$
				'description' => 'Set value in followign 24 hour format "HH:mm-HH:mm"',
				'secure' => false,
				'created_by' => 0
			],
			'globalPaymentsEngine' => [
				'name' => 'GlobalPaymentsEngine',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'If set to true then cost settings are enabled to interact with the system’s configured payments engine.',
				'created_by' => 0,
				'is_payment_configuration' => true,
				'delete'=>true
			],
			'isGlobalPaymentsEngine' => [
				'name' => 'Enabled the Global Payments Engine',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Enabling this option will allow learning to be purchased from the system.',
				'created_by' => 0,
				'is_payment_configuration' => true,
			],
			'globalPaymentsEngineAccountReference' => [
				'name' => 'Global Payments Engine Account Reference',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => 'This is the default general ledger code used for Civica payments.  The system otherwise records the ‘Learning Resource Code’ if it is defined.',
				'created_by' => 0,
				'is_payment_configuration' => true,
			],
			'globalPaymentsEngineRequestURL' => [
				'name' => 'Global Payments Engine Request URL',
				'type' => 'string',
				'status' => 1,
				'value' => 'https://pay.sandbox.realexpayments.com/pay',
				'description' => 'Request URL for Global Payments Engine.',
				'created_by' => 0,
				'is_payment_configuration' => true,
			],
			//FIXME: or should we use GlobalPaymentsEngineAccountReference for this ?
			'globalPaymentsMerchantID' => [
				'name' => 'Global Payments Engine Merchant ID',
				'type' => 'string',
				'status' => 1,
				'value' => "",
				'description' => 'Merchant ID for Global Payments Engine.',
				'secure' => false,
				'created_by' => 0,
				'is_payment_configuration' => true,
			],
			'globalPaymentsAccountID' => [
				'name' => 'Global Payments Engine Account ID',
				'type' => 'string',
				'status' => 1,
				'value' => "internet",
				'description' => 'Account ID for Global Payments Engine.',
				'secure' => false,
				'created_by' => 0,
				'is_payment_configuration' => true,
			],
			'globalPaymentsSharedSecret' => [
				'name' => 'Global Payments Engine Shared Secret',
				'type' => 'string',
				'status' => 1,
				'value' => "",
				'description' => 'Shared Secret for Global Payments Engine.',
				'secure' => true,
				'created_by' => 0,
				'is_payment_configuration' => true,
			],
			'currencyCode'=>[
				'name'=>'Standard currency codes',
				'type'=>'string',
				'status'=>1,
				'value'=>"CAD",
				'description'=>"Standard currency codes",
				'created_by'=>0,
				'is_payment_configuration' => true,
			],
			'globalPaymentCurrencyCode'=>[
				'name'=>'Standard currency codes',
				'type'=>'string',
				'status'=>1,
				'value'=>"CAD",
				'description'=>"Standard currency codes",
				'created_by'=>0,
				'is_payment_configuration' => true,
				'delete' => true,
			],
			'isEmbeddedPowerBI' => [
				'name' => 'Embed Power BI into Dashboard',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'Setting this value to true will embed a Power BI dashboard to the interface (rather than linking to a separate Power BI installation, requiring licences etc.).  Please contact Open eLMS in order to purchase this service prior to switching this service on, you will otherwise be charged.',
				'secure' => false,
				'created_by' => 0
			],
			'powerbiDashboardClientId' => [
				'name' => 'Microsoft Application Client Id for PowerBi.',
				'type' => 'string',
				'status' => 1,
				'value' => '68a2742e-92f8-471a-b1c4-7251ca5b2fa9',
				'description' => trim("
				You need to define the MicrosoftAppClientId and MicrosoftAppClientSecret to integrate MS Services. " .
				"Currently these values are set using Open eLMS’s MS Azure values for demonstration purposes only.\n\n" .
				"To create the app, log in to https://portal.azure.com and create an app.  Then set the value in Open eLMS " .
				"Configuration settings for the MicrosoftAppClientId to its client id and set MicrosoftAppClientSecret. " .
				"Give this app the correct permissions (i.e. PowerBi) " .
				"and set up the following redirect urls: \n\n" .
					str_replace($settings["LMSUri"], "", $settings["LMSUrl"]) . "/powerbi-dashboards"),
				'created_by' => 0,
			],
			'powerbiDashboardClientSecret' => [
				'name' => 'Microsoft Application Client Secret for PowerBi.',
				'type' => 'string',
				'status' => 1,
				'secure' => 1,
				'value' => '3yrR[RZ_2nuF82gwtKTUBMJjYCstxw]:',
				'description' => trim("
				You need to define the MicrosoftAppClientId and MicrosoftAppClientSecret to integrate MS Services. " .
				"Currently these values are set using Open eLMS’s MS Azure values for demonstration purposes only.\n\n" .
				"To create the app, log in to https://portal.azure.com and create an app.  Then set the value in Open eLMS " .
				"Configuration settings for the MicrosoftAppClientId to its client id and set MicrosoftAppClientSecret. " .
				"Give this app the correct permissions (i.e. PowerBi) " .
				"and set up the following redirect urls: \n\n" .
					str_replace($settings["LMSUri"], "", $settings["LMSUrl"]) . "/powerbi-dashboards"),
				'created_by' => 0,
			],
			'sortLearningCategoriesByOrder' => [
				'name' => 'Sort Learning Categories by Order parameter',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'By selecting this option, the order parameter for Learning Categories is activated, allowing for them to be organized and sorted based on defined values.',
				'created_by' => 0
			],
			'formSignOffText' => [
				'name' => 'Text for Sign Off in forms',
				'type' => 'string',
				'status' => 1,
				'value' => 'Sign Off',
				'description' => 'Text shown for Sign Off in forms.',
				'created_by' => 0,
			],
			'registrationFormFields' => [
				'name' => 'Registration Form Configuration',
				'type' => 'text',
				'status' => 1,
				'value' => '',
				'description' => 'To modify use: System Setup > Defaults > Registration Form',
				'created_by' => 0,
			],
			'testEmailAccount' => [
				'name' => 'Test Email Account',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => 'If this value is set then all emails generated by the system will go to this account.  Empty this field when you are ready to launch the system and are happy with the way the system is sending out emails.',
				'created_by' => 0,
			],
			'openelmsAiLinkToken' => [
				'name' => 'Authorization token to access openelms.ai',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => 'Authorization token to access openelms.ai',
				'created_by' => 0,
				'secure' => true,
			],
			'approveEmailQueueItems' => [
				'name' => 'If this is true, no email will be sent out until approved.',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'Administrator will need to approve any outgoing email in Email Queue list, Audit section.',
				'created_by' => 0
			],
			'sendInternalErrorToEmails' => [
				'name' => 'Specify emails that will recieve Internal Error Messages(500).',
				'type' => 'list',
				'status' => 1,
				'value' => '',
				'description' => 'Internal Server Error. A generic error message indicating an unexpected condition was encountered.',
				'created_by' => 0
			],
			'cronImportFailureEmail' => [
				'name' => 'Cron Import Failure Email',
				'type' => 'string',
				'status' => 1,
				'value' => '<EMAIL>',
				'description' => 'Activate this configuration to receive email alerts when scheduled user import processes encounter failures. Provide the email address where these alerts should be sent.',
				'secure' => false,
				'created_by' => 0
			],
			'PurgeAuditRecordsDays' => [
				'name' => 'Purge audit records days',
				'type' => 'list-definition',
				'status' => 1,
				'value' => '[{"name": "TableHistory","value": 365},{	"name": "EmailHistory",		"value": 365	},	{		"name": "UserLearningModuleArchive",		"value": 365	}, {"name": "PaymentHistory", "value": 365}]',
				'description' => 'Days to retain audit records before purging. Types: TableHistory (table_history), EmailHistory (email_history), UserLearningModuleArchive (user_learning_module_archives), PaymentHistory (user_payment_transactions), Logs (logs). Default: 365 days each.',
				'created_by' => 0,
			],
			'isShowCancellationsOnEvents' => [
				'name' => 'Show cancellations on events',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'Set this value to true to show any learners who have cancelled the event once it was assigned to them.  Managers will be able to access their reason for cancelling (which would have been sent in an email.)',
				'created_by' => 0
			],
			'EnableCredlyBadge'=>[
				'name'=>'By setting this setting to true, you can assign Credly badges to user when learning resource complete.Also need to update Credly Organisation ID and TOKEN',
				'type'=>'boolean',
				'status'=>1,
				'value'=>false,
				'description'=>'',
				'created_by'=>0,
			],
			'CredlyOrganizationID'=>[
				'name'=>'Obtain the Credly Organization ID from the Credly website',
				'type'=>'string',
				'status'=>1,
				'value'=>'',
				'description'=>'',
				'created_by'=>0
			],
			'CredlyAuthorizationToken'=>[
				'name'=>'Obtain the Credly Authorization Token from Credly website',
				'type'=>'string',
				'value'=>'',
				'status'=>true,
				'description'=>'',
				'created_by'=>0,
				'secure'=>true
			],
			'RefresherNotificationTimings' => [
				'name' => 'Refresher Notification Timings',
				'type' => 'list',
				'status' => 1,
				'value' => '3,15,30,60,90',
				'description' => 'This list of timings is the number of days before a learning resource is due when an email alert will be sent to the learner.',
				'created_by' => 0
			],
			'BookingDeadline' => [
				'name' => 'Booking Deadline',
				'type' => 'list-definition',
				'status' => 1,
				'value' => '[{"name": "DeadlineHours","value": 24},{	"name": "DeadlinePassedMessage",		"value": "The booking deadline has passed as it is too near the starting time for the event.  A booking cannot be made through the system."	}]',
				'description' => 'Set a default time - in hours - before the event time during which a course cannot be enrolled on.  For instance if the value was 24, then no bookings on the event can be made a day before the event is due to start. Set a default deadline passed message to display in the system',
				'created_by' => 0
			],
			'EventDropOffDeadline' => [
				'name' => 'Event Drop Off Deadline',
				'type' => 'list-definition',
				'status' => 1,
				'value' => '[{"name": "EventDropOffDeadlineHours","value": 1},{	"name": "EventDropOffDeadlineMessage",		"value": "The event drop-off deadline has already passed since it\'s too close to the event\'s starting time. You are unable to drop-off at this point. Please reach out to your manager for assistance." }]',
				'description' => 'Configure a default time window in hours before the event starts, during which course drop-offs are not allowed. For example, if the value is set to 1, no drop-offs will be permitted within 1 hour of the event. "EventDropOffDeadlineMessage" is used to set the default alert message displayed in the system when an event drop-off deadline has passed.',
				'created_by' => 0
			],
			'supervisorRoleID' => [
				'name'=> 'Supervisor Role ID',
				'type'=>'integer',
				'status'=>1,
				'value'=>'',
				'description'=>"To assign the supervisor role to a supervisor during the import process from the 'Myhr' system, it is essential to include the Supervisor Role ID. You can obtain this Role ID by navigating to the 'Roles' section within the 'Organization' category in the 'System Setup' menu.",
				'created_by'=>0
			],
			'SFTPHRHost'=>[
				"name"=>"MyHR SFTP Host Name",
				"type"=>"string",
				"status"=>1,
				"value"=>"",
				"description"=>"This is the hostname of the SFTP location to save a local copy of the file be synchonised with the user table in Open eLMS.  This will run overnight and import whatever data is in that location.",
				"created_by"=>0
			],
			'SFTPHRPort'=>[
				"name"=>"MyHR SFTP PORT Number",
				"type"=>"integer",
				"status"=>1,
				"value"=>"22",
				"description"=>"This is the port of the SFTP location to save a local copy of the file be synchonised with the user table in Open eLMS.  This will run overnight and import whatever data is in that location.",
				"created_by"=>0
			],
			"SFTPHRUsername"=>[
				"name"=>"HR SFTP username ",
				"type"=>"string",
				"status"=>1,
				"value"=>"",
				"description"=>"This is the username of the SFTP location to save a local copy of the file be synchonised with the user table in Open eLMS.  This will run overnight and import whatever data is in that location.",
				"created_by"=>0
			],
			'SFTPHRPassword'=>[
				"name"=>"HR SFTP password",
				"type"=>"string",
				"secure"=>1,
				"status"=>1,
				"value"=>"",
				"description"=>"This is the password of the SFTP location to save a local copy of the file be synchonised with the user table in Open eLMS.  This will run overnight and import whatever data is in that location."
			],
			'managerSelfAssignmentEnabled' => [
				'name' => 'Include Managers as Self-Learners',
				'type' => 'boolean',
				'status' => 1,
				'value' => 1,
				'description' => 'When enabled, managers will automatically be assigned to themselves as learners after 10-15 minutes for reporting purposes. This allows managers to see their own learning progress in dashboards and exports.',
				'created_by' => 0,
			],
			'SFTPHRClientUpload'=>[
				"name"=>"SFTP HR Clinet Upload Location",
				"type"=>"string",
				"status"=>1,
				"value"=>"",
				"description"=>"SFTP HR Clinet Upload Location  SFTPHRClientUpload  (This is the client’s location to fetch the data and save as a local copy of the file to be synchronized with the user table in Open eLMS. This will run overnight and import whatever data is in that location.)"
			],
			'SFTPHRSystemLocation'=>[
				"name"=>"SFTP HR default system Location",
				"type"=>"string",
				"status"=>1,
				"value"=>"hr",
				"description"=>"SFTP HR default system Location  SFTPHRSystemLocation (This is  default location to save a local copy of the file be synchonised with the user table in Open eLMS.  This will run overnight and import whatever data is in that location.)"
			],
			'useOutlookVenues' => [
				'name' => 'Use Outlook Venues',
				'type' => 'boolean',
				'status'=> 1,
				'value' => false,
				'description'=> "Set this to true to use venues defined in Outlook to learning events.  Venues are set up in Outlook with a mailbox as with any person in the system but the type property as ‘resource’. This relies on resources being set up correctly in Outlook (not part of the Open eLMS functionality) with availability kept up to date.",
				'created_by'=>0
			],
			'OutlookRoomAccessToken'=>[
				'name'=>'Outlook Room Access Token',
				'type'=>'string',
				'secure'=>1,
				'status'=>1,
				'value'=>'',
				'description'=>"Outlook Room Access Token",
				'created_by'=>0
			],
			'OutLookRoomRefreshToken'=>[
				'name'=>'OutLook Room Refresh Token',
				'type'=>'string',
				'status'=>1,
				'secure'=>1,
				'value'=>'',
				'description'=>'Outlook Room Refresh Token',
				'created_by'=>0
			],
			'MicrosoftTenantID' => [
				'name' => 'Microsoft Tenant ID',
				'type' => 'string',
				'status' => 1,
				'value' => 'common',
				'description' => 'If you need to use single tenant endpoint, change common to yor Tenant ID',
				'created_by' => 0,
			],
			'globalPaymentsRebatePassword' => [
				'name' => 'Global Payments Rebate Password',
				'type' => 'string',
				'value' => '',
				'status' => true,
				'description' => 'Global Payment rebate password for refund',
				'created_by' => 0,
				'secure' => true,
				'is_payment_configuration' => true
			],
			'SFTPHREncriptionKey'=>[
				"name"=>"SFTP HR Decryption Private Key",
				"type"=>"text",
				"secure"=>1,
				"status"=>1,
				"value"=>"",
				"description"=>"This is the decryption key for the SFTP location used to save a local copy of the file that will be synchronized with the user table in Open eLMS. This process will run overnight and import any data found in that location."
			],
			'SFTPHREncriptionPassword'=>[
				"name"=>"SFTP HR Decryption Private Key Password",
				"type"=>"string",
				"secure"=>1,
				"status"=>1,
				"value"=>"",
			"description"=>"This is the decryption key password for the SFTP location used to save a local copy of the file that will be synchronized with the user table in Open eLMS. This process will run overnight and import any data found in that location."
			],
			'enableMaximo' => [
				'name' => 'Enable Maximo functionality',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'If true and "Maximo credentials" has data, learning item status will update connected Maximo instance.',
				'created_by' => 0
			],
			'maximoCreds' => [
				'name' => 'Maximo credentials',
				'type' => 'list-definition',
				'status' => 1,
				'value' => '[{"name": "base_uri","value": ""},{	"name": "X-IBM-Client-Id",		"value": ""	},	{		"name": "X-IBM-Client-Secret",		"value": ""	}]',
				'description' => 'The credentials required for accessing Maximo',
				'created_by' => 0,
			],
			'OnboardingSignoffFormWorkflow' => [
				'name' => 'Onboarding Sign-off Form Workflow',
				'type' => 'select-list',
				'status' => 1,
				'value' => '',
				'description' => 'This form "Onboarding Sign-off Form" has been created and is supplied for the purposes of signing-off the Individualised Learner Record in accordance with the UK Government Apprenticeship process. An alternative form can be changed to match any onboarding process desired.',
				'created_by' => 0,
				'select_values' => []
			],
			'ShowFormReportingOnMainInterface' => [
				'name' => 'Show Form Reporting on Main Interface',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'If true, the form reporting that are completed will be displayed on the learner\'s interface Reports section.',
				'created_by' => 0
			],
			'h5pPassMark' => [
				'name' => 'Default H5P Passmark Score',
				'type' => 'integer',
				'status' => 1,
				'value' => 80,
				'description' => 'Global H5P passmark score that needs to be reached for learning resource, to be completed.',
				'created_by' => 0
			],
			'Hide100PercentDiscountedEnrollableLearningResources'=>[
				'name' => 'Hide enrollable learning resources if there is a 100% discount applied to the company',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'Set this value to true if you wish to use the LMS to both provide assigned learning resources to your learners (by directly assigning them to learners) AND sell learning resources to third parties.  This setting prevents internal users being overwhelmed with learning resources that do not apply to them.',
				'created_by' => 0
			],
			'showEmployeeIdInReports' => [
				'name' => 'Show Employee ID field in default reports as filter field.',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'Use employee ID in reports for filtering.',
				'created_by' => 0,
			],
			'AIChatLink' => [
				'name' => 'AI Chat Link',
				'type' => 'string',
				'status' => 1,
				'value' => "",
				'description' => 'This is the link to the first line of support from Open eLMS.  Consult the chat bot regarding any query you have about Open eLMS.',
				'created_by' => 0
			],
			'SQLQueryHelpText' => [
				'name' => 'Query Help Text',
				'type' => 'string',
				'status' => 1,
				'value' => "Enter a SQL query here to filter the data on any parameter you would like, some example queries include:
				1) XXX
				2) YYYY
				3) ZZZ",
				'description' => 'This text guides the user in inputting SQL queries into the system to assign learning.  Change the text to include any standard queries which your organisation may use.',
				'created_by' => 0,
			],
			'showExtraResourceFields_TDG' => [
				'name' => 'Show extra fields for learning resources/lessons',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'Target Catalogue - multi select box, Delivery Provider Type - drop-down, Group/Department Code - drop-down',
				'created_by' => 0
			],
			'ShowEventsineLearningView'=>[
				'name' => 'Show Events in Learning View',
				'type' => 'boolean',
				'status' => 1,
				'value' => true,
				'description' => 'Set this value to true if you wish to make learning events visible in the main learning view.  If this is set to false the learner will need to use the calendar to view events or click on lessons which require a connected event enrollment.',
				'created_by' => 0,
			],
			'HideCustomFieldLearningResource'=>[
				'name' => 'Hide Custom Field for Learning Resource',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'Make this value true to prevent administrators for adding custom fields to Learning Resources in Admin > Defaults > Configuration settings.',
				'created_by' => 0,
			],
			'HideCustomFieldLessons'=>[
				'name' => 'Hide Custom Field for Lessons',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'Make this value true to prevent administrators for adding custom fields to Lessons in Admin > Defaults > Configuration settings.',
				'created_by' => 0,
			],
			'HideCustomFieldLearningProgrammes'=>[
				'name' => 'Hide Custom Field for Learning Programmes',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'Make this value true to prevent administrators for adding custom fields to Learning Programmes in Admin > Defaults > Configuration settings.',
				'created_by' => 0,
			],
			'currencySymbol'=>[
				'name'=>'Standard currency symbol',
				'type'=>'string',
				'status'=>1,
				'value'=>"$",
				'description'=>"Standard currency symbol",
				'created_by'=>0,
				'is_payment_configuration' => true,
			],
			'globalPaymentCurrencySymbol'=>[
				'name'=>'Standard currency symbol',
				'type'=>'string',
				'status'=>1,
				'value'=>"$",
				'description'=>"Standard currency symbol",
				'created_by'=>0,
				'is_payment_configuration' => true,
				'delete' => true,
			],
			'SeparateDistributionAssignFormsandFormReports'=>[
				'name'=>'Separate the Distribution and Assignment of Forms and Form Reports',
				'type'=>'boolean',
				'status'=>1,
				'value'=>false,
				'description'=>'Should managers need to separate the assignment and distribution of forms (e.g. managers get assign form reports but not learners) then this value should be set to true.',
				'created_by'=>0
			],
			'AwaitingSignOffFormReminder' => [
				'name' => 'Forms Awaiting Sign Off Email Reminder Frequency',
				'type' => 'select-list',
				'select_values' => [
					[
						'name' => 'None',
						'value' => 'none'
					],
					[
						'name' => 'Daily',
						'value' => 'daily'
					],
					[
						'name' => 'Weekly',
						'value' => 'weekly'
					],
					[
						'name' => 'Monthly',
						'value' => 'monthly'
					],
					[
						'name' => 'Every 3 days',
						'value' => 'every_three_days'
					]
				],
				'status' => 1,
				'value' => 'daily',
				'description' => 'This configuration sets the frequency of Email remainder send to the users for Forms Awaiting Sign Off, Based on this configuration value will be sending email remainder to the user who have forms awaiting sign off',
				'created_by' => 0
			],
			'selectProcessingManagerIsMandatory' => [
				'name' => 'Make it mandatory to select a processing manager for uploads.',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'Make it mandatory to select a processing manager for uploads. Works in tandem with the configuration AttachManagerstoUploads',
				'created_by' => 0
			],
			"showEventVisitTypes" => [
				'name' => 'Show event visit types in the Events listing table.',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => "When enabled Shows event visit types in the table for the Events listing table",
				'created_by' => 0,
			],
			'changeResourceDateWhenEventDateChange' => [
				'name'=>'Change Resource Completion Date Based On Event Date',
				'type'=>'boolean',
				'status'=>1,
				'value'=>false,
				'description'=>'Set this to true should you wish any learning resource - that is attached to an event - to have its completion date set to the event date rather than the date of the actual completion of the blended learning.  By default this value is set to false as this allows the system to record the actual date when learning resources were completed and therefore is an accurate record of learning behaviour.',
				'update_name_desc' => true
			],
			'isEditableMaxMinOutlookVenueLimts'=>[
				'name' => 'EditableMaximum and Minimum Limits for Outlook Venues',
				'type' => 'boolean',
				'status'=>1,
				'value'=>false,
				'description'=>'Set this to true this allows managers to edit the maximum and minimum limitations on the event once these figures are populated by Outlook. This means that rooms can be reconfigured to allow more or less people rather than strictly sticking to constraints defined within Outlook venues.',
				'created_by'=>0
			],
			'RolesNotOverwrittenDuringImport' => [
				'name' => 'Roles not to be overwritten during the import',
				'type' => 'list',
				'status' => 1,
				'value' => '',
				'description' => 'Set these roles not to be overwritten during any import of the data (manual or automated.).',
				'created_by' => 0,
			],
			'powerbiDashboardViewerRoleID' => [
				'name' => 'Powerbi Dashboard Viewer Role ID.',
				'type' => 'integer',
				'status' => 1,
				'value' => 64,
				'description' => 'Fix to show Powerbi Dashboard on Dashboard Viewer Role.',
				'created_by' => 0
			],
			'HideNotApplicableLearning' => [
				'name' => 'Hide Not Applicable Learning in Queries',
				'type' => 'boolean',
				'status' => 1,
				'value'=> false,
				'description' => 'Set this value to true to hide from the users ePortfolio any learning which has a query attached that is in turn not applicable to the current learner.  For example, a learner could start his job as a “Junior Programmer” and gets assigned a “C++ for beginners” course which is assigned to all people with a “Junior Programmer” job through a query attached to that learning.  A few years later he becomes a systems analyst, this course is no longer applicable (so if this value is true) it is now no longer visible on the learner’s ePortfolio although is visible via the progress tab and on reporting.',
				'created_by' => 0
			],
			'RemoveNotApplicableNotCompletedLearning' => [
				'name' => 'Remove Not Applicable Learning in Queries that is not completed',
				'type' => 'boolean',
				'status' => 1,
				'value'=> false,
				'description' => 'Set this value to true to remove from the users ePortfolio any learning which has a query attached that is in turn not applicable to the current learner that has not ‘Completed’.  For example, a learner could start his job as a “Junior Programmer” and gets assigned a “C++ for beginners” course which is assigned to all people with a “Junior Programmer” job through a query attached to that learning.  A few years later he becomes a systems analyst, this course is no longer applicable and he has not completed the learning (so if this value is true) it is now unassigned from the learner and no record of it remains on the learner’s progress tab.',
				'created_by' => 0
			],
			'showEventLessonsUserProfile' => [
				'name' => 'Show Event linked Lessons in user profile',
				'type' => 'boolean',
				'status' => 1,
				'value'=> false,
				'description' => 'If set to true, lessons created in learning library, linked to event that is assigned to user, will show up in "Learning" section, user profile.',
				'created_by' => 0
			],
			'RolePreferencesPassword' => [
				'name' => 'Role Preferences Password',
				'type' => 'string',
				'status' => 1,
				'value' => 'Password123',
				'description' => 'Set this value to limit access to the editing of Role preferences to prevent knock-on permission errors by unwanted editing.',
				'secure' => true,
				'created_by' => 0
			],
			'defaultDateFormat' => [
				'name' => 'Default date format across the system.',
				'type' => 'select-list',
				'select_values' => [
					[
						'name' => 'DD/MM/YYYY (09/02/2024)',
						'value' => 'd/m/Y'
					],
					[
						'name' => 'YYYY/MM/DD (2024/02/09)',
						'value' => 'Y/m/d'
					],
					[
						'name' => 'MM/DD/YYYY (02/09/2024)',
						'value' => 'm/d/Y'
					],
					[
						'name' => 'DD.MM.YYYY (09.02.2024)',
						'value' => 'd.m.Y'
					],
					[
						'name' => 'DD-MM-YYYY (09-02-2024)',
						'value' => 'd-m-Y'
					],
					[
						'name' => 'YYYY-MM-DD (2024-02-09)',
						'value' => 'Y-m-d'
					]
				],
				'status' => 1,
				'value' => 'd/m/Y',
				'description' => 'This is the default date format used by the system overwriting the system default.',
				'created_by' => 0,
			],
			'powerbiPushAllTables' => [
				'name' => 'Whether to push all tables data to Power BI',
				'type' => 'boolean',
				'status' => 1,
				'value'=> false,
				'description' => 'Set this value to true to push all tables data to powerbi',
				'created_by' => 0
			],
			'hidePastEventsInLearningView' => [
				'name' => 'Hide all past events in Learner interface, learning view.',
				'type' => 'boolean',
				'status' => 1,
				'value'=> true,
				'description' => "Set this value to true to hide all past events in the learner's view of learning.  It is advised to set this value to true to avoid cluttering the screen with past data.",
				'created_by' => 0,
			],
			'hideLearnerDueDate' => [
				'name' => "Hides the learner's due date when selecting learning in the learning interface",
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => "Set this value to true to hide the learning due date for the learning resources and lessons. This value will be set to false in almost all cases (some users of skills may not want the learner to be aware of this since they cannot effect the result.)",
				'created_by' => 0,
			],
			'hideLearnerStatus' => [
				'name' => "Hides the learner’s status when selecting learning in the learning interface",
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => "Set this value to true to hide the learning status for the learning resources and lessons. This value will be set to false in almost all cases (some users of skills may not want the learner to be aware of this since they cannot effect the result.)",
				'created_by' => 0,
			],
			'hideLearnerDetails' => [
				'name' => "Hides the learner's details tab when selecting learning in the learning interface",
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => "Set this value to true to hide the learning details tab for the learning resources and lesson. This value will be set to false in almost all cases (some users may wish to do this to clean the interface for the end user.)",
				'created_by' => 0,
			],
			'hideLearnerSignOff' => [
				'name' => "Hides the learner's sign off when selecting learning in the learning interface",
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => "Set this value to true to hide the learner's sign off for the learning resources or skills. This value will be set to false in almost all cases (some users of skills may not want the learner to have the responsiblity of signing off learning - this especially applies to skills monitoring programmes.)",
				'created_by' => 0,
			],
			'outlookOutGoingEmail' => [
				'name'=>'Outlook Outgoing Email',
				'type' => 'select-list',
				'select_values' => [
					[
						'name' => 'Event creator company email',
						'value' => '1'
					],
					[
						'name' => 'Outlook Venue email',
						'value' => '2'
					],
				],
				'status'=>1,
				'value'=>'1',
				'description'=>'Outlook Outgoing Email this email must have permission for rooms and resources',
				'created_by'=>0,
				'force' => true,
			],
			'automateEventCompletionState' => [
				'name' => "Sets Event attendance to completed if Event resources are completed",
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => "If set to true, Learner Event attendance will be automatically marked as complete if the resources linked to the event are completed by the learner.",
				'created_by' => 0,
			],
			'defaultLearnerTaskView' => [
				'name' => 'Default Learner task view',
				'type' => 'select-list',
				'select_values' => [
					[
						'name' => 'Learning Resources',
						'value' => 'learning_resources'
					],
					[
						'name' => 'Events',
						'value' => 'events'
					],
				],
				'status' => 1,
				'value' => 'events',
				'description' => 'Shows Learning resources in the list',
				'created_by' => 0,
			],
			'refreshLessonLinkedToEvent' => [
				'name' => "Refresh lesson linked to event, if lesson is marked as open in event only",
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => "If event lesson is due to be refreshed for event attendees, lesson will be refreshed automatically by cron task. Any attendee who attended an event that is linked to 'Open in event only' lesson and the lesson has a refresh period that has passed since last event date. That lesson will change status to not attended.",
				'created_by' => 0,
			],
			'PowerbiRedirectUrl' => [
				'name' => 'Custom Powerbi BI redirect URL',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => 'If empty, will use default.',
				'created_by' => 0,
			],
			'hideDueBefore' => [
				'name' => 'Hide Due Before',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Hide Due Before column in Learning Resource Listing.',
				'created_by' => 0,
			],
			'hideRefreshAt' => [
				'name' => 'Hide Refresh At',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Hide Refresh At column in Learning Resource Listing.',
				'created_by' => 0,
			],
			'hideLearnerCalenderDescriptionColumn' => [
				'name' => 'Hide Description Column in Learner Task',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Hide Description Column in Learner Task.',
				'created_by' => 0,
			],
			'viewMangersFormLearnerView' => [
				'name' => 'Show managers button on learner view',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Show managers button on learner view.',
				'created_by' => 0,
			],
			'powerbiPushData_Forms' => [
				'name' => 'Whether to certain forms tables data to Power BI',
				'type' => 'boolean',
				'status' => 1,
				'value'=> false,
				'description' => 'Set this value to true to push certain tables data to powerbi.',
				'created_by' => 0,
			],
			'billingMenuSymbol' => [
				'name' => 'Billing menu item currency symbol.',
				'type' => 'select-list',
				'select_values' => [
					[
						'name' => '$',
						'value' => 'usd'
					],
					[
						'name' => '£',
						'value' => 'gbp'
					],
					[
						'name' => '¥',
						'value' => 'yen'
					],
					[
						'name' => '€',
						'value' => 'euro'
					],
				],
				'status' => 1,
				'value' => 'gbp',
				'description' => 'Standard currency symbol used in the billing menu.',
				'created_by' => 0,
			],
			'enableStripePayment' => [
				'name' => 'Enable Stripe Payment',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'Enable Stripe Payment',
				'created_by' => 0,
				'is_payment_configuration'=>true
			],
			'stripePublishableKey' => [
				'name' => 'Stripe Publishable Key',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => 'Stripe Publishable Key',
				'created_by' => 0,
				'is_payment_configuration'=>true
			],
			'stripeSecretKey' => [
				'name' => 'Stripe Secret Key',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'secure' => 1,
				'description' => 'Stripe Secret Key',
				'created_by' => 0,
				'is_payment_configuration'=>true
			],
			'enableUpsellEnterpriseVersionFunctionality' => [
				'name' => 'Enable Upsell Enterprise Version Functionality',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'Enable Upsell Enterprise Version Functionality',
				'created_by' => 0,
				'is_payment_configuration'=>false
			],
			'stripeCreditPriceTableID'=>[
				'name'=>'Stripe Credit Price Table ID',
				'type'=>'string',
				'status'=>1,
				'value'=>'',
				'description'=>'Stripe Credit Purchase Table ID obtained from the Stripe Dashboard.',
				'created_by'=>0,
				'is_payment_configuration'=>true
			],
			'stripeProductID'=>[
				'name'=>'Stripe Product ID',
				'type'=>'string',
				'status'=>1,
				'value'=>'',
				'description'=>'Stripe Product ID obtained from the Stripe Dashboard.',
				'created_by'=>0,
				'is_payment_configuration'=>true
			],
			'refreshFormRoles'=>[
				'name' => 'Refresh Form Roles',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => 'This will refresh the form roles for the forms that are assigned to the users.Add roles separated by comma.',
				'created_by' => 0,
				'delete' => true,
			],
			'stripeLicensePriceTableID'=>[
				'name'=>'Stripe License Price Table ID',
				'type'=>'string',
				'status'=>1,
				'value'=>'prctbl_1PHORIGyS2vkMC0xbQYqZKeF',
				'description'=>'Stripe License Price Table ID obtained from the Stripe Dashboard.',
				'created_by'=>0,
				// 'delete' => true,
				'is_payment_configuration'=>true
			],
			'stripeFreeLicensePriceID'=>[
				'name'=>'Stripe Free License Price ID',
				'type'=>'string',
				'status'=>1,
				'value'=>'price_1PIlLkGyS2vkMC0xXz3efViO',
				'description'=>'Stripe License Price ID obtained from the Stripe Dashboard. This is not Price Table ID.',
				'created_by'=>0,
				'delete' => true,
				'is_payment_configuration'=>true
			],
			'stripeLicenseFreeProductID'=>[
				'name'=>'Stripe Free License Product ID',
				'type'=>'string',
				'status'=>1,
				'value'=>'prod_Q7dRxoJIxHcFoC',
				'description'=>'Stripe Product ID obtained from the Stripe Dashboard.',
				'created_by'=>0,
				'delete' => true,
				'is_payment_configuration'=>true
			],
			'stripeLicenseProfessionalProductID'=>[
				'name'=>'Stripe Professional License Product ID',
				'type'=>'string',
				'status'=>1,
				'value'=>'prod_Q7dcXZugT2Sop7',
				'description'=>'Stripe Product ID obtained from the Stripe Dashboard.',
				'created_by'=>0,
				'delete' => true,
				'is_payment_configuration'=>true
			],
			'stripeLicenseEnterpriseProductID'=>[
				'name'=>'Stripe Enterprise License Product ID',
				'type'=>'string',
				'status'=>1,
				'value'=>'prod_NUkFI15JXlxafm',
				'description'=>'Stripe Product ID obtained from the Stripe Dashboard.',
				'created_by'=>0,
				'delete' => true,
				'is_payment_configuration'=>true
			],
			'stripeCatalogueCreditsPriceID'=>[
				'name'=>'Stripe Catalogue Credits Product Price ID',
				'type'=>'string',
				'status'=>1,
				'value'=>'price_1PFyZbGyS2vkMC0x2PgY58GE',
				'description'=>'Stripe Product Price ID obtained from the Stripe Dashboard.',
				'created_by'=>0,
				'is_payment_configuration'=>true
			],
			'showUnassignedDisabledLearning' => [
				'name' => 'List unassigned/disabled learning items in learners profile',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'When enabled, any completed learning that is unassigned or linked to disabled resource will be visible in all reports and user profile.',
				'created_by' => 0,
			],
			'expandLearningCategories'=>[
				'name'=>'Expand Learning Categories',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Set this value to true to expand the learning categories by default.',
				'created_by' => 0
			],
			'neutralAvatarForAll'=>[
				'name'=>'Neutral Avatar Image Enabled',
				'type' => 'boolean',
				'status' => 1,
				'value' => 1,
				'description' => 'A neutral placeholder image will be used in case a custom profile picture is not set.',
				'created_by' => 0
			],
			'WorkingWeekHours' => [
				'name' => 'Working Week hours',
				'type' => 'integer',
				'status' => 1,
				'value' => 37.5,
				'description' => '',
				'created_by' => 0,
			],
			'rolesAtPageHeader'=>[
				'name'=>'Roles listed in the header of the page',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'The header of the page has a menu that allows you to switch roles.',
				'created_by' => 0,
			],
			'rolesAtPageHeaderLearner'=>[
				'name'=>'Roles listed in the header of the page',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'The header of the page has a menu that allows you to switch roles.',
				'created_by' => 0,
			],
			'API_ignoreManuallyLinked'=>[
				'name'=>'When removing managers by API call, ignore manually added managers',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => '',
				'created_by' => 0
			],
			'ShowLearningIDLearnerProfile'=>[
				'name' => 'Show Learning item ID in Learner profile, %%programme%% and %%lesson_resources%% section',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => "Show Managers button on the learner's progress view so they can see the list of their direct line managers.",
				'created_by' => 0,
			],
			'googleTranslate'=>[
					'name'=>'Google Translate',
					'type' => 'boolean',
					'status' => 1,
					'value' => 0,
					'description' => 'Setting this to true will enable the Google Translate service.  Check with Google regarding your eligibility to use this service.',
					'created_by' => 0
			],
			'powerbiPushData_Manager' => [
					'name' => 'Whether to push manager <-> user relation and custom programme status data to Power BI',
					'type' => 'boolean',
					'status' => 1,
					'value'=> false,
					'description' => 'Set this value to true to push manager <-> user relation and custom programme status data to Power BI',
					'created_by' => 0
			],
			'powerbiPushData_LearningReports' => [
					'name' => 'Whether to push learningreports data to Power BI',
					'type' => 'boolean',
					'status' => 1,
					'value'=> true,
					'description' => 'Set this value to true to learningreports data to Power BI',
					'created_by' => 0
			],
			'enablePay360' => [
				'name' => 'Enable Pay 360 Payment',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => '',
				'created_by' => 0,
				'is_payment_configuration' => true
			],
			'pay360_wsdl' => [
				'name' => 'Pay 360 wsdl file name.',
				'type' => 'string',
				'status' => 1,
				'value' => 'scpSimpleClient.wsdl',
				'description' => 'Please provide file/s (in case one is for testing, one for LIVE) to OpeneLMS and then specify exact file name here',
				'created_by' => 0,
				'is_payment_configuration' => true
			],
			'pay360_SCP_Id' => [
				'name' => 'Pay 360 Capita SCP Id',
				'type' => 'integer',
				'status' => 1,
				'value' => '',
				'description' => '',
				'created_by' => 0,
				'is_payment_configuration' => true
			],
			'pay360_Site_Id' => [
				'name' => 'Pay 360 Capita Site Id',
				'type' => 'integer',
				'status' => 1,
				'value' => '',
				'description' => '',
				'created_by' => 0,
				'is_payment_configuration' => true
			],
			'pay360_hmacKey_Id' => [
				'name' => 'Pay 360 Capita hmacKey Id',
				'type' => 'integer',
				'status' => 1,
				'value' => '',
				'description' => '',
				'created_by' => 0,
				'is_payment_configuration' => true
			],
			'pay360_hmacKey' => [
				'name' => 'Pay 360 Capita hmacKey',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => '',
				'secure' => 1,
				'created_by' => 0,
				'is_payment_configuration' => true
			],
			'pay360_subjectType' => [
				'name' => 'Pay 360 subjectType element value',
				'type' => 'string',
				'status' => 1,
				'value' => 'CapitaPortal',
				'description' => '',
				'created_by' => 0,
				'is_payment_configuration' => true
			],
			'pay360_algorithm' => [
				'name' => 'Pay 360 algorithm element value',
				'type' => 'string',
				'status' => 1,
				'value' => 'Original',
				'description' => '',
				'created_by' => 0,
				'is_payment_configuration' => true
			],
			'pay360_systemCode' => [
				'name' => 'Pay 360 systemCode element value',
				'type' => 'string',
				'status' => 1,
				'value' => 'SCP',
				'description' => '',
				'created_by' => 0,
				'is_payment_configuration' => true
			],
			'pay360_requestType' => [
				'name' => 'Pay 360 requestType element value',
				'type' => 'string',
				'status' => 1,
				'value' => 'payOnly',
				'description' => '',
				'created_by' => 0,
				'is_payment_configuration' => true
			],
			'pay360_panEntryMethod' => [
				'name' => 'Pay 360 panEntryMethod element value',
				'type' => 'string',
				'status' => 1,
				'value' => 'ECOM',
				'description' => '',
				'created_by' => 0,
				'is_payment_configuration' => true
			],
			'pay360_fundCode' => [
				'name' => 'Pay 360 fundCode element value',
				'type' => 'string',
				'status' => 1,
				'value' => 'SL',
				'description' => '',
				'created_by' => 0,
				'is_payment_configuration' => true
			],
			'pay360_vatCode' => [
				'name' => 'Pay 360 vatCode element value',
				'type' => 'string',
				'status' => 1,
				'value' => 'STD20',
				'description' => '',
				'created_by' => 0,
				'is_payment_configuration' => true
			],
			'pay360_vatRate' => [
				'name' => 'Pay 360 vatRate element value',
				'type' => 'integer',
				'status' => 1,
				'value' => 20,
				'description' => '',
				'created_by' => 0,
				'is_payment_configuration' => true
			],
			'pay360DefaultGeneralLedgerCode' => [
				'name' => 'Pay360 Default General Ledger Code',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => 'This is the default general ledger code used for Pay360 payments.  The system otherwise records the ‘Learning Resource Code’ if it is defined.',
				'created_by' => 0,
				'is_payment_configuration' => true,
			],
			'billingCurrencySymbol'=> [ // need something more universal here
				'name' => 'Billing default currency symbol',
				'type' => 'string',
				'status' => 1,
				'value' => "£",
				'description' => "",
				'created_by' => 0,
				'is_payment_configuration' => true,
			],
			'billingCurrencyCode'=> [
				'name' => 'Billing default currency code',
				'type' => 'string',
				'status' => 1,
				'value' => "GBP",
				'description' => "",
				'created_by' => 0,
				'is_payment_configuration' => true,
			],
			'paymentTimeout' => [
				'name' => 'Payment gateway timeout(minutes)',
				'type' => 'number',
				'status' => 1,
				'value' => 60,
				'description' => '',
				'created_by' => 0,
				'is_payment_configuration' => true
			],
			'hideHomeButton' => [
				'name' => 'Hide Home Button',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'If set to true then the home button in the top menu is no longer visible and this explainer screen is no longer used.',
				'created_by' => 0,
				'force' => true,
			],
			'updateLearningResultGraceAtForSchedule' => [
				'name' => 'Update Learning Result Grace At For Schedule',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Update Learning Result Grace At For Schedule',
				'created_by' => 0,
			],
			'disableVideoClicks' => [
				'name' => 'Disable seek bar and other functionality for vido playback, learner interface.',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => '',
				'created_by' => 0
			],
			'defaultLearnerRole' => [
				'name' => 'Default role for registered learner',
				'type' => 'integer',
				'status' => 1,
				'value' => 3,
				'description' => 'Default role for new registered learner. Change this to your desired role ID.',
				'created_by' => 0
			],

			'TrackFormSignOffRoles'=>[
				'name' => 'Track Form Sign Off Roles',
				'type' => 'list',
				'status' => 1,
				'value' => "",
				'description' => "Enables role-specific tracking of form sign-off statuses, allowing coaches/trainers and managers who are responsible for the form to monitor pending approvals in the 'Awaiting Sign Off' section. This helps maintain oversight on which stakeholders have yet to complete their sign-offs",
				'created_by' => 0,
				'category' => 'Forms'
			],
			'fixUserCodeOnImport' => [
				'name' => 'Custom functionality to replace usercode field value data',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => "will replace usercode that starts with 1xxx… into 01xxx…",
				'created_by' => 0,
			],
			'fixUserNameOnImport' => [
				'name' => 'Custom functionality to replace username field value data',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => "will replace username that starts with 1xxx… into 01xxx…",
				'created_by' => 0,
			],
			'RetakeFeeLimit'=>[
				'name'=>'Limit in days by which a retake fee applies',
				'type' => 'integer',
				'status' => 1,
				'value' => 0,
				'description' => 'This offers the learning resource, event or programme at a lower rate (if one is defined) if the retake is within a certain number of days.  If this value is left blank then this retake fee is applied to any learning that has been previously completed.',
				'created_by' => 0
			],
			'AIPoweredHelpAssistantAdmin' => [
				'name' => 'AI Powered Help Assistant - Administration interface',
				'type' => 'string',
				'status' => 1,
				'value' => "tVSRGchClmAn8UAhtc6o/egUWw7qYTNqd9ptbjfJX",
				'description' => 'Add the ability to integrate an AI powered help chat facility to the LMS.  The default value of our chat agent is tVSRGchClmAn8UAhtc6o/egUWw7qYTNqd9ptbjfJX (team_id/bot_id).  This has been trained to answer questions about Open LMS’s learning management system and wider business capabilities, but this can be altered to reflect your own AI powered chat engine.  Should you wish to remove the help button for management entirely, then make this value blank.',
				'created_by' => 0
			],
			'AIPoweredHelpAssistantLearner' => [
				'name' => 'AI Powered Help Assistant - Learner interface',
				'type' => 'string',
				'status' => 0,
				'value' => "",
				'description' => 'Add the ability to integrate an AI powered help chat facility to the LMS. Should you wish to remove the help button for management entirely, then make this value blank.',
				'created_by' => 0
			],
			'powerbiPushDataGroup' => [
				'name' => 'Whether to push groups table data to Power BI',
				'type' => 'boolean',
				'status' => 1,
				'value'=> true,
				'description' => 'Set this value to true to push groups table to powerbi.',
				'created_by' => 0,
			],
			'powerbiPushData_TutorDashboard' => [
				'name' => 'Whether to certain forms tables data to Power BI',
				'type' => 'boolean',
				'status' => 1,
				'value'=> false,
				'description' => 'Set this value to true to push certain tables data to powerbi.',
				'created_by' => 0,
			],
			'ProgrammeCreationAIURL' => [
				'name' => 'Programme Creation AI URL',
				'type' => 'string',
				'status' => 1,
				'value' => 'http://************',
				'description' => 'URL for the AI service to create programmes',
				'created_by' => 0,
				'is_payment_configuration' => false
			],
			'ProgrammeCreationAISimilarity' => [
				'name' => 'Programme Creation AI SIMILARITY',
				'type' => 'string',
				'status' => 1,
				'value' => 0.3,
				'description' => 'SIMILARITY for the AI service to create programmes',
				'created_by' => 0,
				'is_payment_configuration' => false
			],
			'ProgrammeCreationAILimit' => [
				'name' => 'Programme Creation AI LIMIT',
				'type' => 'string',
				'status' => 1,
				'value' => 10,
				'description' => 'LIMIT for the AI service to create programmes',
				'created_by' => 0,
				'is_payment_configuration' => false
			],
			'ProgrammeCreationAISiteName'=>[
				'name'=>'Programme Creation AI Site Name',
				'type'=>'string',
				'status'=>1,
				'value'=>'',
				'description'=>'Site name for the AI service to create programmes',
				'created_by'=>0,
				'is_payment_configuration'=>false
			] ,
			'PowerBIWorkspaces' => [
				'name' => 'PowerBI Workspace group id',
				'type' => 'list',
				'status' => 1,
				'value'=> '',
				'description' => 'These workspaces are independent entities. When one workspace reaches its limit, it will automatically switch to the next available workspace, make sure to add me as first workspace in the list.',
				'created_by' => 0,
				'category' => 'power_bi',
			],
			'PowerBIDefaultworkspace' => [
				'name' => 'PowerBI default workspace id',
				'type' => 'string',
				'status' => 1,
				'value'=> 'me',
				'description' => 'Default workspace id for powerbi operations, this will be automatically setup by the system',
				'created_by' => 0,
				'category' => 'power_bi',
			],
			'creatorModuleAPI'=>[
				'name'=> 'Creator Module API',
				'type'=> 'string',
				'status'=> 1,
				'category' => 'learning_resources',
				'value'=> 'https://library.openelms.com/',
				'description'=> 'Creator Module API URL',
				'created_by'=> 0,
				'is_payment_configuration' => false
			],
			'creatorModuleAPIKey'=>[
				'name'=> 'Creator Module API Key',
				'type'=> 'string',
				'category' => 'learning_resources',
				'status'=> 1,
				'value'=> '7|JrAaYn97tx23f1sMPCKD0ZkR6LvN4BlXnjRW9npDb569fcf1',
				'description'=> 'Creator Module API Key',
				'created_by'=> 0,
				'is_payment_configuration' => false,
				'secure' => 1
			],
			'MaxAttemptsatQuiz' => [
				'name' => 'Number of Attempts at Quiz before an Email Notification is sent to Learner',
				'type' => 'integer',
				'status' => 1,
				'value' => 0,
				'description' => 'If the learner has greater than the number of attempts at the quiz defined in this setting, then an email (using the template ‘Multi Attempts at Quiz Notification’) is sent to the learner. This is designed to warn and push the learner into taking the learning more seriously but it does not block the course in any way.',
				'created_by' => 0,
				'category' => 'learning_resources'
			],
			'hideSkillsMonitoringDetails' => [
				'name'=>'Hide Skills Monitoring Details',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description'=> "This configuration setting enables the hiding of specific programme details within the skills monitoring section of a learner's file. When enabled, it will conceal the Start Date, Duration, Expected Finish Date, and Hours from view. This feature is designed to simplify the interface for users who do not require this level of detail and prefer a cleaner, more focused display of only the most pertinent information related to skills monitoring. The setting ensures that the system remains flexible and adaptable to the needs of different organizations who may prioritize different aspects of programme information visibility.",
				'created_by' => 0,
				'category'=>'skills_monitoring'
			],
			'passwordResetPageCustomCode' => [
				'name' => 'Custom code for password reset page',
				'type' => 'text',
				'status' => 1,
				'value' => "",
				'description' => '',
				'visible_manager' => false,
				'editable_manager' => false,
				'created_by' => 0,
			],
			'sharedClients' => [
				'name'=>'Shared Clients',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description'=> "",
				'created_by' => 0,
				'visible_manager' => false,
				'editable_manager' => false,
			],

			'ResetPasswordText' => [
				'name' => 'Reset Password Text',
				'type' => 'rich-text',
				'status' => 1,
				'value' => '',
				'description' => 'If empty, will use default.',
				'created_by' => 0,
				'category' => 'login'
			],
			'ResetPasswordLogo' => [
				'name' => 'Reset Password Logo',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => 'If empty, will use default.',
				'created_by' => 0,
				'category' => 'login'
			],
			'PaymentsEngine' => [
				'name' => 'Payments Engine.',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'Payments Engine',
				'created_by' => 0,
				'is_payment_configuration' => true,
			],
			'OpeneLMSCreatorMenu' => [
				'name' => 'OpeneLMS Creator Menu',
				'type' => 'boolean',
				'force' => true,
				'status' => 1,
				'value' => 1,
				'description' => 'This is the URL for the OpeneLMS Creator Menu',
				'created_by' => 0,
				'category' => 'login'
			],
			'APITokenAccessMediaLibrary' => [
				'name' => 'API Access Token for Media Library',
				'type' => 'string',
				'hidden' => true,
				'status' => 1,
				'value' => '',
				'description' => 'This is the API Access Token for the Media Library',
				'created_by' => 0
			],
			'hideManagerRefreshButton'=>[
				'name'=>'Hide Manager Refresh Button',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => "This hides the ability for managers  to refresh learning on a learner’s profile, so the learning resource’s status is archived and the current status is set to ‘Not Started’.",
				'created_by' => 0
			],
			'powerBIALNEventTypeSlugs' => [
				'name' => 'PowerBI ALN Event Types',
				'type' => 'list',
				'status' => 1,
				'value'=>'ALNReviewVisit_b3f6e17b',
				'description' => 'Comma separated list of event types that will be pushed to PowerBI',
				'created_by' => 0,
				'category' => 'power_bi'
			],
			'powerBILearnAimRef'=> [
				'name' => 'PowerBI Learn Aim Ref',
				'type' => 'list-definition',
				'status' => 1,
				'value' =>'[{"name":"60346085","value":"Level 1 English"},{"name":"60346061","value":"Level 2 English"},{"name":"60348070","value":"Level 1 Maths"},{"name":"60348082","value":"Level 2 Maths"}]',
				'description' => 'Comma separated list of event types that will be pushed to PowerBI',
				'created_by' => 0,
				'category' => 'power_bi'
			],
			'WaitingListRecycleTime'=>[
				'name'=>'Waiting List Recycle Time',
				'type' => 'string',
				'status' => 1,
				'value' => 24,
				'description' => 'Change this value from a blank field to move the waiting list up one place after.  This number (measured in hours) is the time after which the waiting lists shuffles one place so the person in the first place moves to the end of the waiting list, the second person moves to the start of the waiting list etc. For instance setting this value to 24, will shuffle the waiting list every day.  Use this functionality if you do not want to have to manually manage the waiting list.',
				'created_by' => 0
			],
			'lastWaitingListRecycle'=>[
				'name'=>'Last Waiting List Recycle Update Date',
				'type'=>'string',
				'status'=>1,
				'value'=>'',
				'description'=>'Last Waiting List Recycle Update date . Not configurable by users'
			],
			//create configuration for powerbi to limit resouce based on resource query builder
			'powerBIResourceQueryBuilder' => [
				'name' => 'PowerBI Resource considering Resource Query Builder',
				'type' => 'boolean',
				'status' => 1,
				'value' => false,
				'description' => 'Enable PowerBI Resource Query Builder',
				'created_by' => 0
			],
			'initialSkillDueDays'=>[
				'name'=>'Initial Skill Due Days',
				'type' => 'integer',
				'status' => 1,
				'value' => 30,
				'description' => "This is the number of days until the skill is due for the first time. After that, it will use the repetition period to calculate the due date.",
				'created_by' => 0,
				'category'=>'learning_resources'
			],
			'percentageChangeTrafficLight' => [
				'name' => 'Percentage Change Traffic Light',
				'type' => 'integer',
				'status' => 1,
				'value' => 5,
				'description' => 'This value sets the % boundary at which “On Target - Time completed” turns from Green, to Amber to Red.',
				'created_by' => 0,
			],
			'resourceProgressColor' => [
				'name' => 'Resource Progress Color',
				'type' => 'string',
				'status' => 1,
				'value' => 'purple',
				'description' => 'This value sets the color of the progress bar for resources. The value could be color name or hex code.',
				'created_by' => 0,
			],
			'criteriaProgressColor' => [
				'name' => 'Criteria Progress Color',
				'type' => 'string',
				'status' => 1,
				'value' => 'blue',
				'description' => 'This value sets the color of the progress bar for criteria. The value could be color name or hex code.',
				'created_by' => 0,
			],
			'timeProgressColor' => [
				'name' => 'Time Progress Color',
				'type' => 'string',
				'status' => 1,
				'value' => 'green',
				'description' => 'This value sets the color of the progress bar for time. The value could be color name or hex code.',
				'created_by' => 0,
				'update_name_desc' => true,
			],
             'showFormIDNumberLearnerInterface'=>[
                'name'=>'Show Form ID Number in Assigned Forms Table in Learner Interface',
                'type' => 'boolean',
                'status' => 1,
                'value' => 1,
                'description' => 'Setting this to true will show the Form ID number in Assigned Forms Table in the Learner Interface.',
                'created_by' => 0
            ],
            'showWorkflowInLearnerInterface'=>[
                'name'=>'Show Workflow in Assigned Forms Table Learner in Interface',
                'type' => 'boolean',
                'status' => 1,
                'value' => 1,
                'description' => 'Setting this to true will show the Workflow in Assigned Forms Table in the Learner Interface.',
                'created_by' => 0
            ],
			'enableGovUKPay'=>[
				'name'=>'Enable Gov UK Pay',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Setting this to true will enable the Gov UK Pay service.  Check with Gov UK regarding your eligibility to use this service.',
				'created_by' => 0,
				'is_payment_configuration' => true
			],
			'govUKPayAPIKey'=>[
				'name'=>'Gov UK Pay API Key',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => 'Gov UK Pay API Key',
				'created_by' => 0,
				'is_payment_configuration' => true,
			],
			'PasswordStrength' => [
				'name' => 'Password strength, Zxcvbn password strength estimator.',
				'type' => 'select-list',
				'status' => 1,
				'value' => "2",
				'select_values' => [
					[
						'name' => '0 - Too guessable: risky password. (guesses < 10^3)',
						'value' => "0"
					],
					[
						'name' => '1 - Very guessable: protection from throttled online attacks. (guesses < 10^6)',
						'value' => "1"
					],
					[
						'name' => '2 - Somewhat guessable: protection from unthrottled online attacks. (guesses < 10^8)',
						'value' => "2"
					],
					[
						'name' => '3 - Safely unguessable: moderate protection from offline slow-hash scenario. (guesses < 10^10)',
						'value' => "3"
					],
					[
						'name' => '4 - Very unguessable: strong protection from offline slow-hash scenario. (guesses >= 10^10)',
						'value' => "4"
					],
				],
				'description' => "Minimum strength requirement.",
				'created_by' => 0,
				'visible_manager' => false,
				'editable_manager' => false,
			],
			'tryTraineeReminder'=>[
				'name' => 'Try Trainee Reminder',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Set this to true to ensure that managers get notification of trying the trainee interface when they log out of the system IF they have not yet tried the trainee interface.',
				'created_by' => 0
			],
			'tryTraineeReminderText'=>[
				'name' => 'Try Trainee Reminder Text',
				'type' => 'string',
				'status' => 1,
				'value' => 'You have not yet tried the trainee interface, do you wish to do this now?',
				'description' => 'This text is visible to managers when they log out of the system IF they have not yet tried the trainee interface.',
				'created_by' => 0
			],
			'AIGradingPrompt' => [
				'name' => 'AI Grading Prompt',
				'type' => 'text',
				'status' => 1,
				'value' => 'Read and understand the learner\'s work.  Evaluate it using the appropriate academic expectations for that level. Take into account: Relevance to the topic/question, Structure and clarity of arguments, Use of evidence or examples, Critical thinking or problem-solving ability, Grammar, spelling, and formatting and Referencing, if required at that level. Give detailed written feedback in under 500 words to include: Strengths, Areas for improvement, Tips for progression.',
				'description' => 'This text is added to the grading prompt to give it a consistency of approach when grading. Edit this to adjust the grading approach common to your organisation.  It can be edited on a case by case basis.',
				'category' => 'artificial_intelligence',
				'created_by' => 0,
				'visible_manager' => false,
				'editable_manager' => false,
				'public' => true,
			],
			'AIGradingSchemeDefault' => [
				'name' => 'AI Grading Scheme Default',
				'type' => 'picklist',
				'select_values' => 'ai_grading_scheme_options',
				'status' => 1,
				'value' => 'school-leaver',
				'description' => 'Select the grading scheme default used for marking. Edit this to adjust the marking approach common to your organisation.  It can be edited on a case by case basis.',
				'category' => 'artificial_intelligence',
				'created_by' => 0,
				'visible_manager' => false,
				'editable_manager' => false,
				'public' => true,
			],
			'AIGradingQuestionPrompt' => [
				'name' => 'AI Grading Question Prompt',
				'type' => 'text',
				'status' => 1,
				'value' => 'Read the attached document carefully. Identify the central topic and the main point or argument being made. Then, formulate a single, concise essay-style question that the document would answer. The question should resemble a prompt that could be used in an academic setting. For example, if the text is about how tanks were used at the Battle of Amiens, the question might be: “Describe how tanks were used at the Battle of Amiens.” Output only the generated question. Do not include any explanation or context.',
				'description' => '',
				'category' => 'artificial_intelligence',
				'created_by' => 0,
				'visible_manager' => false,
				'editable_manager' => false,
				'public' => true,
			],
			'AIGradingOpenAIAPIKEY'=>[
				'name' => 'AI Grading OpenAI API Key',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => 'API Key for accessing OpenAI services for grading.',
				'created_by' => 0,
				'visible_manager' => false,
				'editable_manager' => false,
				'secure' => true,
			],
			'AIGradingOpenAIAsisstantID'=>[
				'name' => 'AI Grading Assistant ID',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => 'Open AI Asisstant ID for accessing OpenAI services for grading.',
				'created_by' => 0,
				'visible_manager' => false,
				'editable_manager' => false,
			],
            'restrictUsernamePasswordDuringImport' => [
                'name' => 'Restrict Username and Password Update During Import',
                'type' => 'boolean',
                'status' => 1,
                'value' => 1,
                'description' => 'If set to true, the username and password will not be updated when importing users.',
                'created_by' => 0,
                'category' => 'import_hr_data'
            ],
            'restrictAccountTypeDuringImport' => [
                'name' => 'Restrict Account Type Update During Import',
                'type' => 'boolean',
                'status' => 1,
                'value' => 1,
                'description' => 'If set to true, the account type will not be updated when importing users.',
                'created_by' => 0,
                'category' => 'import_hr_data'
            ],
            'uniqueUserCodePerUser' => [
				'name' => 'Allow Unique user-code(Employee ID) field For User.',
				'type' => 'boolean',
				'status' => 1,
				'value' => true,
				'description' => "Employee ID field is unique in user table, set this to false to allow importing multiple users with same Employee ID.",
                'created_by' => 0,
                'category' => 'organisation'
			],

			// Twilio configuration
			'MobilePhoneAuthentication' => [
				'name' => 'Mobile Phone Authentication',
				'type' => 'boolean',
				'value' => 0,
				'status' => 1,
				'description' => 'Will enable the use of Twilio to authenticate users mobile phone number.',
				'created_by' => 0,
				'category' => 'twilio',
			],
			'TwilioSID' => [
				'name' => 'Twilio Security Identiofier',
				'type' => 'string',
				'value' => '**********************************',
				'status' => 1,
				'description' => 'Twilio SID (Security Identifier) is a unique identifier assigned to your Twilio account, services, or resources. It is used to authenticate API requests and manage Twilio services.  See https://console.twilio.com/ for details.',
				'created_by' => 0,
				'category' => 'twilio',
			],
			'TwilioSecret' => [
				'name' => 'Twilio Security Token',
				'type' => 'string',
				'value' => '361da7d6eb6385f0833f62a83ffbb724', // This one is incorrect, replace with correct one
				'status' => 1,
				'description' => 'Twilio Secret (also called Auth Token) is a secure password-like key used to authenticate API requests. It is paired with your Account SID to verify your identity when using Twilio services.',
				'secure' => true,
				'created_by' => 0,
				'category' => 'twilio',
			],
			'TwilioPhoneNumber' => [
				'name' => 'Twilio Phone Number',
				'type' => 'string',
				'value' => '+************',
				'status' => 1,
				'description' => 'A Twilio phone number is a virtual number provided by Twilio that allows you to send and receive SMS',
				'created_by' => 0,
				'category' => 'twilio',
			],
            // Central auth conf
			'CentralAuthServiceURL' => [
				'name' => 'Base URL for the Central Auth Service App',
				'type' => 'string',
				'value' => "https://id.openelms.com",
				'status' => 1,
				'description' => 'Central auth service is responsible for generating secure sessions of OELMS application',
				'created_by' => 0,
				'category' => 'organisation',
				// 'force' => true, // force to reset the value everytime
				// 'delete' => true, // force to delete the record
			],
            'openAIKey' => [
				'name' => 'OpenAI Key',
                'type' => 'string',
                'secure' => true,
				'status' => 1,
				'value' => true,
				'description' => "OpenAI Key is utilised to establish a connection with OpenAPI for purposes like category generation, program/skill structure generation, etc.",
                'created_by' => 0,
                'category' => 'artificial_intelligence'
			],
			'powerBIRemoveALNLLDDHealthProb'=>[
                'name'=>'PowerBI Remove ALN LLDD Health Prob',
                'type' => 'boolean',
                'status' => 1,
                'value' => 0,
                'description' => 'Setting this to true will remove the ALN LLDD Health Prob from PowerBI View table creation query.',
                'created_by' => 0,
                'category' => 'power_bi'
            ],

               'SFTPHRClientName' => [
                'name'=>'SFTP HR Client Name',
                'type' => 'string',
                'status' => 1,
                'value' => "",
                'description'=> "This configuration is used to run the cron job for the SFTP HR import. It should be the name of the client.",
                'created_by' => 0,
                'category'=>'import_hr_data'
            ],
				'SFTPHRNumberOfFiles' => [
				'name' => 'SFTP HR Number of Files',
				'type' => 'integer',
				'status' => 1,
				'value' => "1",  // default to 1
				'description' => "Number of latest CSV/XLSX files to download and process from the SFTP HR folder.",
				'created_by' => 0,
				'category' => 'import_hr_data'
			],
			'MaximoRetryCount' => [
				'name' => 'Maximo Retry Count',
				'type' => 'integer',
				'status' => 1,
				'value' => 3,
				'description' => 'Number of times to retry when updating Maximo',
				'created_by' => 0,
			],
			'MaximoRetryDelay' => [
				'name' => 'Maximo Retry Delay',
				'type' => 'integer',
				'status' => 1,
				'value' => 5,
				'description' => 'Delay in minutes between retries when updating Maximo',
				'created_by' => 0,
			],
			'defaultFromEmail' => [
				'name' => 'Overwrites email from email field to all emails sent out from system',
				'type' => 'string',
				'status' => 1,
				'value' => '',
				'description' => "Leave empty to use name in from field, from user who sent out email.",
				'created_by' => 0
			],
			'showDetailsTabForUploadResource'=>[
				'name'=>'Show Details Tab for Upload Resource',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Setting this to true will show the details tab for the resources with upload type.',
				'created_by' => 0
			],
			'isLearningAI'=>[
				'name'=>'Learning AI',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Activating this allows learners to review companywide available learning using an AI chatbot. This functionality is only available to client who have purchased the AI Enterprise pack.  Contact <EMAIL> to activate this feature.',
				'created_by' => 0,
			],
			'chatBotApiBaseUrl'=>[
				'name'=>'Chat-Bot api base url',
				'type' => 'string',
				'status' => 1,
				'value' => "",
				'description' => 'Python api base url for chat-bot',
				'created_by' => 0,
			],

            'SFTPHRNumberOfFiles' => [
				'name' => 'SFTP HR Number of Files',
				'type' => 'integer',
				'status' => 1,
				'value' => "1",  // default to 1
				'description' => "Number of latest CSV/XLSX files to download and process from the SFTP HR folder.",
				'created_by' => 0,
				'category' => 'import_hr_data'
			],
            'pay360ErrorMessageContactPerson' => [
                'name' => 'Pay360 Error Message Contact Person',
                'type' => 'string',
                'status' => 1,
                'value' => 'Administrator',
                'description' => 'Contact details for the person to be notified when a Pay360 error message occurs. This will be displayed on the frontend.',
                'created_by' => 0,
                'is_payment_configuration' => true,
            ],
			'managerSelfAssignmentEnabled' => [
                'name' => 'Include Managers as Self-Learners',
                'type' => 'boolean',
                'status' => 1,
                'value' => 1,
                'description' => 'When enabled, managers will automatically be assigned to themselves as learners for reporting purposes. This allows managers to see their own learning progress in dashboards and exports.',
                'created_by' => 0,
                'category' => 'manager_interface'
            ],
			'isMultiSelectShoppingBasket'=>[
				'name'=>'Multi-Select Shopping Basket',
				'type' => 'boolean',
				'status' => 1,
				'value' => 0,
				'description' => 'Set this value to true to allow users to purchase more than one item at a time prior to checkout.',
				'created_by' => 0,
				'category' => 'payments'
			],
        ];
		$configurationCategoryArray = [
			'addAllEventsToOutlook' => 'outlook',
			'addCustomCompanyStatus' => 'organisation',
			'addCustomProgrammeStatus' => 'programmes',
			'AIChatLink' => 'artificial_intelligence',
			'AIGradingOpenAIAPIKEY' => 'artificial_intelligence',
			'AIGradingOpenAIAsisstantID' => 'artificial_intelligence',
			'allowAddBlogEntry' => 'learner_interface',
			'allowApi' => 'api_settings',
			'Allowemailalertassignedlearningoverdue' => 'learning_resources',
			'allowEmptyEmailImport' => 'emails',
			'allowIframeEmbedFromDomains' => 'registration',
			'allowJamboards' => 'google_jamboard',
			'allowLearnerAdjustDates' => 'programmes',
			'allowLearnerChangeRefreshInterval' => 'learner_interface',
			'allowLearnerCustomization' => 'learner_interface',
			'allowLearnerEditILR' => 'apprenticeships',
			'allowLearnerRefreshLearning' => 'learning_resources',
			'allowLearnersToDelete' => 'learner_interface',
			'allowLearnerUploads' => 'learner_interface',
			'allowManagerChangeRefreshInterval' => 'manager_interface',
			'allowMandatoryTrainingEmailAlert' => 'emails',
			'allowRegistration' => 'registration',
			'allowRemoteRegistration' => 'registration',
			'allowSCORMContainerPlayer' => 'elearning',
			'allowSendCivicaData' => 'civica_payments',
			'alternativeIdsEnabled' => 'organisation',
			'AndersPinkApiconfiguration_key' => 'ander_pink',
			'API_ignoreManuallyLinked' => 'open_elms_api',
			'APIRateLimiterBackendRoute' => 'open_elms_api',
			'APITokenAccess' => 'open_elms_api',
			'apprentixEmailReminderFrequency' => 'emails',
			'approveEmailQueueItems' => 'emails',
			'ApproveEventUserAfterPayment' => 'events',
			'askForFeedbackOnComplete' => 'learner_interface',
			'AssessmentNoClose' => 'task_assessments',
			'assignAllLinkedUsersToAllLinkedManagers' => 'organisation',
			'assignUsersToProgrammeDuringEventImport' => 'events',
			'AttachManagerstoUploads' => 'uploading_work',
			'automateEventCompletionState' => 'events',
			'AwaitingSignOffFormReminder' => 'emails',
			'badgesEnabled' => 'badgr',
			'BadgrApiKey' => 'badgr',
			'badgrRegion' => 'badgr',
			'BillingContact_ID' => 'xero',
			'BillingContactName' => 'xero',
			'billingCurrencyCode' => 'xero',
			'billingCurrencySymbol' => 'xero',
			'BillingDefaultCurrency' => 'payments',
			'BillingEmailAddress' => 'payments',
			'BillingFirstName' => 'xero',
			'BillingisChargebyTime' => 'xero',
			'BillingisDemo' => 'xero',
			'BillingisMembership' => 'xero',
			'BillingisUnlimitedCourses' => 'xero',
			'BillingJackdawVersion' => 'xero',
			'BillingLastName' => 'xero',
			'billingMenuSymbol' => 'payments',
			'BillingNext_Invoice' => 'xero',
			'BillingRepeats_Months' => 'xero',
			'BillingTelephone' => 'xero',
			'BookingDeadline' => 'events',
			'buttonStyle' => 'learner_interface',
			'CalendarEndTime' => 'calendar',
			'CalendarStartTime' => 'calendar',
			'canDeleteEvents' => 'events',
			'category' => 'learner_interface',
			'CertificateMessageBottom1' => 'certificate',
			'CertificateMessageBottom2' => 'certificate',
			'CertificateMessageTop' => 'certificate',
			'changeResourceDateWhenEventDateChange' => 'events',
			'civicaDefaultGeneralLedgerCode' => 'civica_payments',
			'CivicaFrequencyDays' => 'civica_payments',
			'CivicaManagerEmail' => 'civica_payments',
			'civicaPaymentsEngine' => 'civica_payments',
			'civicaPaymentsEngineAccountReference' => 'civica_payments',
			'civicaPaymentsEngineRequestURL' => 'civica_payments',
			'civicaPaymentsEngineVatCode' => 'civica_payments',
			'companyCrm' => 'crm',
			'competenciesGamification' => 'progress_view',
			'CredlyAuthorizationToken' => 'credly',
			'CredlyOrganizationID' => 'credly',
			'cronImportFailureEmail' => 'emails',
			'cronTaskDebug' => 'emails',
			'CrontTaskKillTime' => 'timings',
			'currencyCode' => 'payments',
			'currencySymbol' => 'payments',
			'daysSinceLastReviewWarningThreshold' => 'forms',
			'defaultDateFormat' => 'timings',
			'defaultEmailFromName' => 'emails',
			'defaultIlrRole' => 'registration',
			'defaultJackdawRegisterRole' => 'registration',
			'DefaultLearnerScreen' => 'learner_interface',
			'defaultLearnerTaskView' => 'learner_interface',
			'defaultPrevUKPRN' => 'apprenticeships',
			'defaultRegisterRole' => 'registration',
			'defaultTimezone' => 'timings',
			'defaultUKPRN' => 'apprenticeships',
			'defaultUsernameLabel' => 'login',
			'DefaultWelcomeText' => 'login',
			'deleteAndLinkManagersOnUserImport' => 'organisation',
			'demoUserId' => 'registration',
			'disableLazyLoading' => 'learner_interface',
			'disableLookOvers' => 'learner_interface',
			'disableResourceTypes' => 'learning_resources',
			'DisableUserDepartmentOnImport' => 'organisation',
			'disableVideoClicks' => 'learner_interface',
			'duplicatedEmailCheck' => 'emails',
			'duplicatedEmailLimit' => 'emails',
			'duplicatedEmailNotification' => 'emails',
			'emailAttachmentsSize' => 'emails',
			'emailReminderFrequency' => 'emails',
			'enableAutomaticStarUpInstructionEmails' => 'emails',
			'enableContactYourColleagues' => 'learner_interface',
			'EnableCredlyBadge' => 'credly',
			'enableEmailAttachments' => 'emails',
			'enableEmailFunctionality' => 'emails',
			'enhancedEmailErrorLogging' => 'emails',
			'enableEmailUserLogin' => 'login',
			'enableFeedback' => 'learner_interface',
			'enableFeedbackList' => 'learner_interface',
			'enableGlobalOutlookIntegration' => 'outlook',
			'enableGoogleAnalytics' => 'google_analytics',
			'enableImpersonate' => 'admin_interface',
			'enableLeaderBoardImages' => 'progress_view',
			'enableLeaderBoardList' => 'progress_view',
			'enableMaximo' => 'maximo',
			'enableMaytasImport' => 'maytas',
			'enableMFA' => 'login',
			'enableNightlyUserDataExport' => 'organisation',
			'enableOlark' => 'olark',
			'enablePay360' => 'pay360',
			'enableProgrammeTitlesLearnerLandingPage' => 'learner_interface',
			'enableSchedule' => 'manager_interface',
			'enableSnoozeSignOffForm' => 'forms',
			'enableStripePayment' => 'stripe',
			'enableUploadType' => 'learning_resources',
			'enableUpsellEnterpriseVersionFunctionality' => 'help',
			'EnableUserDepartmentOnImport' => 'organisation',
			'enableUserFieldAlertSystem' => 'registration',
			'EnforceDefaultRole' => 'login',
			'EventDropOffDeadline' => 'events',
			'eventQueryBuilderRestriction' => 'events',
			'eventReminderAdvanceNotificationInterval' => 'events',
			'eventReminderLead' => 'events',
			'eventsListLessonsOnlyFromLibrary' => 'lessons',
			'EventsVisibleDays' => 'events',
			'expandLearningCategories' => 'learner_interface',
			'ExtraUserFieldsWatchAndPosRef' => 'blue_light_services',
			'fixUserCodeOnImport' => 'organisation',
			'fixUserNameOnImport' => 'organisation',
			'forceLessonDueDateToResource' => 'timings',
			'forceYoutubeTitleOverThumbnail' => 'learner_interface',
			'formSignatureDisable' => 'forms',
			'formSignOffText' => 'forms',
			'globalHelpURL' => 'help',
			'GlobalOutlookIntegrationSecurityToken' => 'outlook',
			'globalPaymentCurrencyCode' => 'global_payments',
			'globalPaymentCurrencySymbol' => 'global_payments',
			'globalPaymentsAccountID' => 'global_payments',
			'globalPaymentsEngine' => 'global_payments',
			'globalPaymentsEngineAccountReference' => 'global_payments',
			'globalPaymentsEngineRequestURL' => 'global_payments',
			'globalPaymentsMerchantID' => 'global_payments',
			'globalPaymentsRebatePassword' => 'global_payments',
			'globalPaymentsSharedSecret' => 'global_payments',
			'go1AccessTokenExpiresIn' => 'go1',
			'Go1 Access token type' => 'go1',
			'Go1 Refresh token' => 'go1',
			'go1AccessToken' => 'go1',
			'go1AuthToken' => 'go1',
			'go1clientID' => 'go1',
			'go1clientSecret' => 'go1',
			'go1GrantCode' => 'go1',
			'go1InstallResourcesCron' => 'go1',
			'googleAnalyticsCode' => 'google_analytics',
			'GoogleJamboardClientId' => 'google_jamboard',
			'GoogleJamboardClientSecret' => 'google_jamboard',
			'googleTranslate' => 'admin_interface',
			'H5P_CLIENT_ID' => 'h5p',
			'H5P_CLIENT_SECRET' => 'h5p',
			'H5P_LTI_URL' => 'h5p',
			'h5pPassMark' => 'h5p',
			'HelpVideoURL' => 'learner_interface',
			'Hide100PercentDiscountedEnrollableLearningResources' => 'learning_resources',
			'hideAddFormsInLearnerFile' => 'learner_interface',
			'hideAssignmentsHeaderButton' => 'learner_interface',
			'hideCalendarHeaderButton' => 'learner_interface',
			'hideCurriculum' => 'events',
			'hideCurriculumLearner' => 'learner_interface',
			'hideCurriculumMatching' => 'uploading_work',
			'HideReflectiveLog' => 'learner_interface',
			'HideCustomFieldLearningProgrammes' => 'programmes',
			'HideCustomFieldLearningResource' => 'learning_resources',
			'HideCustomFieldLessons' => 'lessons',
			'hideDueBefore' => 'manager_interface',
			'HideFormsLearner' => 'forms',
			'hideHelpHeaderButton' => 'learner_interface',
			'hideHomeButton' => 'manager_interface',
			'hideLaunchSkills' => 'skills_monitoring',
			'HideLearingResourcesReporting' => 'reporting',
			'hideLearnerDetails' => 'learner_interface',
			'hideLearnerDueDate' => 'skills_monitoring',
			'hideLearnerSignOff' => 'skills_monitoring',
			'hideLearnerStatus' => 'skills_monitoring',
			'hideLearningHeaderButton' => 'learner_interface',
			'HideLearningProgrammesReporting' => 'reporting',
			'hideManagerRefreshButton' => 'manager_interface',
			'hideMeetingAndZoom' => 'learner_interface',
			'HideNotApplicableLearning' => 'learning_resources',
			'hidePastEventsInLearningView' => 'events',
			'hideProgrammeStatusFromLearnerProgressView' => 'programmes',
			'hideProgressHeaderButton' => 'learner_interface',
			'HideQAReports' => 'quality_assurance',
			'hideRefreshAt' => 'manager_interface',
			'hideReportsHeaderButton' => 'learner_interface',
			'hideResourcesInLesson' => 'learner_interface',
			'hideResourcesNeedAttentionHeaderButton' => 'learner_interface',
			'hideReviewButton' => 'reporting',
			'importUserCodeForMissingUsername' => 'organisation',
			'importYoutubePlaylist' => 'youtube',
			'IncludeCredasForms' => 'credas',
			'ipBlock' => 'security',
			'isApproveLearners' => 'organisation',
			'isBlackColourScheme' => 'learner_interface',
			'isBrowsebyDefault' => 'learner_interface',
			'isCategroyFilter' => 'learner_interface',
			'isCivicaPaymentsEngine' => 'civica_payments',
			'isCompletedLearningFilterVisible' => 'learner_interface',
			'isCompletedLearningVisibleonFirstLogin' => 'learner_interface',
			'isDemoAccess' => 'registration',
			'isEditableMaxMinOutlookVenueLimts' => 'events',
			'isEmbeddedPowerBI' => 'power_bi',
			'isEnrollableLearningFilterVisible' => 'learner_interface',
			'isEnrollableLearningVisibleonFirstLogin' => 'learner_interface',
			'isFavouriteLearningVisibleonFirstLogin' => 'learner_interface',
			'isGlobalPaymentsEngine' => 'global_payments',
			'isGroupCountries' => 'organisation',
			'isInProgressLearningFilterVisible' => 'learner_interface',
			'isInProgressLearningVisibleonFirstLogin' => 'learner_interface',
			'isLearnerLandingPage' => 'learner_interface',
			'isLearnerQAFilter' => 'learner_interface',
			'isLearnerSkillsSignOff' => 'skills_monitoring',
			'isLearningOutsideProgrammeFilterVisible' => 'learner_interface',
			'isLearningOutsideProgrammeVisibleonFirstLogin' => 'learner_interface',
			'isLockedLearningFilterVisible' => 'learner_interface',
			'isLockedLearningVisibleonFirstLogin' => 'learner_interface',
			'isManageBookings' => 'events',
			'isMandatoryLearningFilterVisible' => 'learner_interface',
			'isMandatoryLearningVisibleonFirstLogin' => 'learner_interface',
			'isMeetings' => 'events',
			'isNotStartedLearningVisibleonFirstLogin' => 'learner_interface',
			'isOpeneLMSClassroom' => 'events',
			'isPeertoPeerVideo' => 'Communications',
			'isProgrammeFilterVisibleonFirstLogin' => 'learner_interface',
			'isProgrammeFilterVisible' => 'learner_interface',
			'isRefresherLearningVisibleonFirstLogin' => 'learner_interface',
			'isShowCancellationsOnEvents' => 'events',
			'isTrackLearningResourceTime' => 'learning_resources',
			'isYammer' => 'microsoft',
			'jackdawVersion' => 'organisation',
			'JamboardRedirectUrl' => 'google_jamboard',
			'JamboardTemplateFileId' => 'google_jamboard',
			'launchResourceText' => 'learner_interface',
			'learnerAimReferenceURL' => 'apprenticeships',
			'learnerCategoryLandingMaxColumns' => 'learner_interface',
			'learnerInterfaceV2' => 'learner_interface',
			'learnerLandingPageDescription' => 'learner_interface',
			'learnerLandingPageDescriptionFontSize' => 'learner_interface',
			'learnerLandingPageNameCentered' => 'learner_interface',
			'learnerProgressGauge' => 'learner_interface',
			'learnerReferenceNumberId' => 'registration',
			'learnerReferenceNumberIteration' => 'apprenticeships',
			'learnersDefineEvidenceCredits' => 'programmes',
			'learnerSkipUploadPrompt' => 'learner_interface',
			'LearningResourceVisibleDays' => 'learning_resources',
			'lessonDuration' => 'events',
			'linkIlrToUserProgramme' => 'apprenticeships',
			'listAllEventsToManagers' => 'events',
			'LMSTitle' => 'branding',
			'loadingAnimation' => 'branding',
			'loggingDataRentention' => 'auditing',
			'makeResourcesLinktoHome' => 'learner_interface',
			'ManagerAccessToLinkedUsers' => 'organisation',
			'mandatoryDuration' => 'learning_resources',
			'manualAdminURL' => 'help',
			'maximoCreds' => 'maximo',
			'MicrosoftAppClientId' => 'microsoft',
			'MicrosoftAppClientSecret' => 'microsoft',
			'MicrosoftTenantID' => 'outlook',
			'moodleLink' => 'moodle_',
			'Multi-factor Authentication' => 'communications',
			'nextDueTrafficLight' => 'progress_view',
			'offTheJobHoursForReviews' => 'apprenticeships',
			'olarkCode' => 'olark',
			'OnboardingSignoffFormWorkflow' => 'apprenticeships',
			'openelmsAiLinkToken' => 'open_elms_ai',
			'optionalResourceProgrammeLink' => 'learning_resources',
			'OutlookRequestResponse' => 'outlook',
			'OutlookRoomAccessToken' => 'outlook',
			'OutLookRoomRefreshToken' => 'outlook',
			'PasswordExpiryDays' => 'login',
			'PasswordMaxAttempts' => 'login',
			'PasswordResetAnonymousMessage' => 'security',
			'PaymentsEngine' => 'payments',
			'powerbi_dashboard_url' => 'power_bi',
			'powerbi_tenant_id' => 'power_bi',
			'powerbi_azure_password' => 'power_bi',
			'powerbi_azure_username' => 'power_bi',
			'powerbi_client_id' => 'power_bi',
			'powerbi_client_secret' => 'power_bi',
			'powerbiDashboardClientId' => 'power_bi',
			'powerbiDashboardClientSecret' => 'power_bi',
			'powerbiDashboardViewerRoleID' => 'power_bi',
			'powerbiPushAllTables' => 'power_bi',
			'promoGradient' => 'learner_interface',
			'PurgeAuditRecordsDays' => 'auditing',
			'randomString' => 'organisation',
			'RecommendationsNumber' => 'artificial_intelligence',
			'redirectAllLinksThroughSSO' => 'single_sign_on',
			'redirectBackToLesson' => 'learner_interface',
			'reEnableUsersOnImport' => 'organisation',
			'refreshCompletedAt' => 'learning_resources',
			'RefresherNotificationTimings' => 'timings',
			'refreshLessonLinkedToEvent' => 'events',
			'RegisterCompanyText' => 'registration',
			'RegisterJobText' => 'registration',
			'registerMandatoryCompany' => 'registration',
			'registerShowCompany' => 'registration',
			'registerShowCountry' => 'registration',
			'registerShowCountry' => 'registration',
			'registrationFormFields' => 'registration',
			'RetakeFeeLimit' => 'learning_resources',
			'RemoveNotApplicableNotCompletedLearning' => 'learning_resources',
			'RolePreferencesPassword' => 'admin_interface',
			'RolesListHelpUrl' => 'help',
			'RolesNotOverwrittenDuringImport' => 'import_hr_data',
			'Salesforce access token' => 'salesforce',
			'Salesforce client secret' => 'salesforce',
			'Salesforce client_id' => 'salesforce',
			'Salesforce code' => 'salesforce',
			'salesforceAccessTokenExp' => 'salesforce',
			'salesforceApiURL' => 'salesforce',
			'salesforceId' => 'salesforce',
			'salesforceIdToken' => 'salesforce',
			'salesforceInstanceUrl' => 'salesforce',
			'salesforceIssuedAt' => 'salesforce',
			'salesforceRefreshToken' => 'salesforce',
			'salesforceScope' => 'salesforce',
			'salesforceSignature' => 'salesforce',
			'salesforceSoapCredentials' => 'salesforce',
			'salesforceTokenType' => 'salesforce',
			'scheduleReminderLead' => 'emails',
			'schoolField' => 'registration',
			'selectProcessingManagerIsMandatory' => 'uploading_work',
			'sendEmailsCronAssignTask' => 'emails',
			'sendInternalErrorToEmails' => 'emails',
			'sendRefreshEmail' => 'emails',
			'SeparateDistributionAssignFormsandFormReports' => 'forms',
			'sessionProtection' => 'security',
			'sessionTimeout' => 'timings',
			'setSmallPromoImage' => 'learner_interface',
			'SFTPHRClientUpload' => 'import_hr_data',
			'SFTPHREncriptionconfiguration_key' => 'import_hr_data',
			'SFTPHREncriptionPassword' => 'import_hr_data',
			'SFTPHRHost' => 'import_hr_data',
			'SFTPHRPassword' => 'import_hr_data',
			'SFTPHRPort' => 'import_hr_data',
			'SFTPHRSystemLocation' => 'import_hr_data',
			'SFTPHRUsername' => 'import_hr_data',
			'showAddedByForEvidence' => 'learner_interface',
			'showCalendarTasksOnly' => 'learner_interface',
			'showCriteriaCompletion' => 'salesforce',
			'showDetailsTabForUploadResource' => 'learner_interface',
			'showEditExpectedCompletionDateResource' => 'learning_resources',
			'showEmailHistoryOnLearnerInterface' => 'progress_view',
			'showEmergencyContactDetails' => 'registration',
			'showEmployeeIdInReports' => 'reporting',
			'showEventLessonsUserProfile' => 'events',
			'ShowEventsineLearningView' => 'events',
			'showEventVisitTypes' => 'progress_view',
			'showExtraResourceFields_TDG' => 'learning_resources',
			'showFormIDNumberLearnerInterface' => 'learner_interface',
			'ShowFormReportingOnMainInterface' => 'forms',
			'showGraphFilterForWrap' => 'hidden',
			'showHideWorkflowOnLearnerInterface' => 'learner_interface',
			'showLatestReleases' => 'learner_interface',
			'ShowLearningIDLearnerProfile' => 'manager_interface',
			'showLegacyReportsManageButtons' => 'reporting',
			'showLinkedAccountsInUserMenuAllTheTime' => 'organisation',
			'showOnlyAssignedCategories' => 'learner_interface',
			'showRecommendations' => 'artificial_intelligence',
			'showResetCustomDatesButton' => 'programmes',
			'showResponsibilitiesAndCommittees' => 'manager_interface',
			'showSchedulePeriodicReview' => 'learner_interface',
			'showSevenDepartmentSubLevels' => 'organisation',
			'showShareResourceSsoUrl' => 'learner_interface',
			'showShareResourceUrl' => 'learner_interface',
			'showSocialLoginButtons' => 'login',
			'showUnassignedDisabledLearning' => 'learner_interface',
			'showUserResetCustomDatesButton' => 'programmes',
			'showVisaEligibilityChecks' => 'registration',
			'showWeekCountForOutcome' => 'learner_interface',
			'showWorkflowInLearnerInterface' => 'learner_interface',
			'showWorkflowOnLearnerInterface' => 'learner_interface',
			'SignOffAutomatedSkillsMonitoring' => 'skills_monitoring',
			'SignOffCompletedSkillsProgramme' => 'skills_monitoring',
			'signOffText' => 'learning_resources',
			'SingleSignOnButtonURL' => 'single_sign_on',
			'sortLearningCategoriesByOrder' => 'admin_interface',
			'SQLQueryHelpText' => 'help',
			'ssoUrl' => 'learner_interface',
			'startupCourseID' => 'learner_interface',
			'stripeCatalogueCreditsPriceID' => 'stripe',
			'stripeCreditPriceTableID' => 'stripe',
			'stripeLicensePriceTableID' => 'stripe',
			'stripeProductID' => 'stripe',
			'stripePublishableKey' => 'stripe',
			'stripeSecretKey' => 'stripe',
			'submitAssessmentText' => 'learner_interface',
			'supervisorRoleID' => 'organisation',
			'systemEmailSendingTime' => 'timings',
			'TasksDefaultSelectedEventType' => 'task_assessments',
			'TasksDefaultSelectedResourceType' => 'task_assessments',
			'TasksDefaultSelectedTaskType' => 'task_assessments',
			'TasksShowByDefaultCalendarEntriesOnly' => 'events',
			'TasksSortedbySoonestExpectedCompletionDate' => 'task_assessments',
			'TeamsClientId' => 'teams',
			'TeamsClientSecret' => 'teams',
			'TeamsRedirectUrl' => 'teams',
			'<EMAIL>' => 'reporting',
			'testEmailAccount' => 'emails',
			'thumbnailRedesign' => 'learner_interface',
			'thumbnailRedesignFont' => 'learner_interface',
			'TrackFormSignOffRoles' => 'forms',
			'trainingWorkRatio' => 'apprenticeships',
			'tryTraineeReminder' => 'manager_interface',
			'tryTraineeReminderText' => 'manager_interface',
			'uniqueEmailPerUser' => 'organisation',
			'uniqueUsernamePerUser' => 'organisation',
			'UseHTMLLineBreaksInOutlook' => 'microsoft',
			'useOutlookVenues' => 'outlook',
			'userFieldAlertSystemInterval' => 'registration',
			'userFieldAlertSystemIntervalRunTime' => 'registration',
			'userFieldAlertSystemMonitoredFields' => 'registration',
			'userImportReplaceUsernameWithEmplyeeId' => 'organisation',
			'userRegisterWaitingForApprovalMessage' => 'registration',
			'version' => 'learner_interface',
			'viewMangersFormLearnerView' => 'manager_interface',
			'virtualEventReminderLead' => 'events',
			'VisitTypesNotMapped' => 'programmes',
			'WaitingListRecycleTime' => 'events',
			'WorkingWeekHours' => 'apprenticeships',
			'wrapLearnerRoleSlug' => 'wrap',
			'wrapOrganisationManagerRoleSlug' => 'wrap',
			'wrapSiteManagerRoleSlug' => 'wrap',
			'yammerAccessToken' => 'yammer',
			'yammerAccessTokenData' => 'yammer',
			'yammerClientId' => 'yammer',
			'yammerClientSecret' => 'yammer',
			'yammerMembershiUrl' => 'yammer',
			'yammerRedirectUri' => 'yammer',
			'ZoomClientId' => 'zoom',
			'ZoomClientSecret' => 'zoom',
			'ZoomVerificationToken' => 'zoom',
			'pay360_algorithm' => 'pay360',
			'pay360_fundCode' => 'pay360',
			'pay360_hmacKey' => 'pay360',
			'pay360_hmacKey_Id' => 'pay360',
			'pay360_panEntryMethod' => 'pay360',
			'pay360_requestType' => 'pay360',
			'pay360_SCP_Id' => 'pay360',
			'pay360_Site_Id' => 'pay360',
			'pay360_subjectType' => 'pay360',
			'pay360_systemCode' => 'pay360',
			'pay360_vatCode' => 'pay360',
			'pay360_vatRate' => 'pay360',
			'pay360_wsdl' => 'pay360',
			'pay360DefaultGeneralLedgerCode' => 'pay360',
			'pay360ErrorMessageContactPerson' => 'pay360',
			'defaultLearnerRole' => 'registration',
			'AIPoweredHelpAssistantAdmin' => 'help',
			'AIPoweredHelpAssistantLearner' => 'help',
			'ProgrammeCreationAIURL' => 'artificial_intelligence',
			'ProgrammeCreationAISimilarity' => 'artificial_intelligence',
			'ProgrammeCreationAILimit' => 'artificial_intelligence',
			'ProgrammeCreationAISiteName' => 'artificial_intelligence',
			'passwordResetPageCustomCode' => 'login',
			'sharedClients' => 'learner_interface',
			'percentageChangeTrafficLight' => 'progress_view',
			'resourceProgressColor' => 'progress_view',
			'criteriaProgressColor' => 'progress_view',
			'timeProgressColor' => 'progress_view',
			'rolesAtPageHeader' => 'manager_interface',
			'rolesAtPageHeaderLearner' => 'learner_interface',
			'enableGovUKPay' => 'gov_uk_pay',
			'govUKPayAPIKey' => 'gov_uk_pay',
			'PasswordStrength' => 'login',

            // Central auth conf
            'CentralAuthServiceURL' => 'organisation',

            // Twilio conf
            'TwilioSID' => 'twilio',
            'TwilioSecret' => 'twilio',
            'TwilioPhoneNumber' => 'twilio',

            'defaultFromEmail' => 'emails',

			'showDetailsTabForUploadResource' => 'learner_interface',
			'managerSelfAssignmentEnabled' => 'manager_interface',
			'isLearningAI' => 'learner_interface',
			'chatBotApiBaseUrl' => 'learner_interface'
		];

		ConfigurationCategories::insertOrUpdateCategories($settings);

		// Call smart update methods for JSON-based configurations
		// Add more calls here as needed for other JSON configurations
		self::smartUpdateJsonConfiguration($configuration_values, 'PurgeAuditRecordsDays');

		// Example usage for other configurations:
		// self::smartUpdateJsonConfiguration($configuration_values, 'AnotherJsonConfig', 'id');
		// self::smartUpdateJsonConfiguration($configuration_values, 'ThirdJsonConfig', 'key');

		foreach ($configuration_values as $key => $configuration_value) {

			// Migration: Transfer disableResourceTypes configuration to database status field
			if ($key == 'disableResourceTypes') {
				// Get current configuration value
				$currentConfig = \Models\Configuration::where('key', 'disableResourceTypes')->first();
				if ($currentConfig && !empty($currentConfig->value)) {
					$disabledTypes = array_map('trim', explode(',', $currentConfig->value));
					
					// Update learning_module_types table - disable the specified types
					if (!empty($disabledTypes)) {
						$typesToDisable = \Models\LearningModuleType::withoutGlobalScope('type_filter')
							->whereIn('slug', $disabledTypes)
							->get();
						
						foreach ($typesToDisable as $type) {
							$type->status = 0;
							$type->save(); // Triggers 'saved' event and cache clearing
						}
					}
				}
				
				// Delete the configuration option since we're now using database status
				\Models\Configuration::where('key', 'disableResourceTypes')->delete();
				
				// Skip creating/updating this configuration entry
				continue;
			}

			if (empty($configuration_value['delete'])) {
				$entry = \Models\Configuration
					::firstOrCreate(
						['key' => $key],
						[
							'name' => $configuration_value['name'],
							'type' => $configuration_value['type'],
							'category_id' =>isset($configuration_value['category_id'])?$configuration_value['category_id']:null,
							'status' => $configuration_value['status'],
							'value' => $configuration_value['value'],
							'description' => $configuration_value['description'],
							'created_by' => isset($configuration_value['created_by']) ? $configuration_value['created_by'] : 0,
							'secure' => (isset($configuration_value['secure']) ? $configuration_value['secure'] : false),
							'is_payment_configuration' => (isset($configuration_value['is_payment_configuration']) ? $configuration_value['is_payment_configuration'] : false),
							'visible_manager' => (isset($configuration_value['visible_manager']) ? $configuration_value['visible_manager'] : true),
							'editable_manager' => (isset($configuration_value['editable_manager']) ? $configuration_value['editable_manager'] : true),
						]
					);

				// For certain configuration option, force it for certain site types
				// enableSchedule for schools/colleges
				if (
					$key == 'enableSchedule' &&
					(
						$settings['licensing']['version'] == 'openelmsschools' ||
						$settings['licensing']['version'] == 'openelmscolleges'
					)
				) {
					$entry->value = 1;
				}

				// Logic to replace globalHelpURL if certain value is there.
				if (
					$key == 'globalHelpURL' &&
					$configuration_value['value'] == 'https://d30jalha6vlrl8.cloudfront.net/'
				) {
					$configuration_value['value'] = 'https://emil-reisser-weston.atlassian.net/servicedesk/customer/portal/2';
					$configuration_value['force'] = true;
				}

				# update AIPoweredHelpAssistantAdmin value to new one if old one found.
				if (
					$key == 'AIPoweredHelpAssistantAdmin' &&
					$entry->value == 'tVSRGchClmAn8UAhtc6o/E8LHsXHn1HyE8ho0XJ7O'
				) {
					$configuration_value['value'] = 'tVSRGchClmAn8UAhtc6o/egUWw7qYTNqd9ptbjfJX';
					$configuration_value['force'] = true;
				}

				// Custom logic for submitAssessmentText, if updated_by is null, set value to ',
				if (
					$key == 'submitAssessmentText' &&
					$entry->updated_by == null
				) {
					$configuration_value['force'] = true;
				}

				// Remove testing value for MicrosoftAppClientId and MicrosoftAppClientSecret
				if (
					$key == 'MicrosoftAppClientId' &&
					(
						$configuration_value['value'] == '68a2742e-92f8-471a-b1c4-7251ca5b2fa9' ||
						$entry->value == '68a2742e-92f8-471a-b1c4-7251ca5b2fa9'
					)
				) {
					$configuration_value['value'] = '';
					$configuration_value['force'] = true;
				}
				if (
					$key == 'MicrosoftAppClientSecret' &&
					(
						$configuration_value['value'] == '3yrR[RZ_2nuF82gwtKTUBMJjYCstxw]:' ||
						$entry->value == '3yrR[RZ_2nuF82gwtKTUBMJjYCstxw]:'
					)
				) {
					$configuration_value['value'] = '';
					$configuration_value['force'] = true;
				}

				$entry->name = $configuration_value['name'];
				$entry->description = $configuration_value['description'];

				if (isset($configuration_value['secure'])) {
					$entry->secure = $configuration_value['secure'];
				}

				$entry->public = isset($configuration_value['public']) ? $configuration_value['public'] : false;

				// default_value will allways be updated by system, not by user
				if (isset($configuration_value['default_value'])) {
					$entry->default_value = $configuration_value['default_value'];
				}

				if (
					isset($configuration_value['update_value_if_empty']) &&
					$configuration_value['update_value_if_empty'] &&
					$entry->value == ''
				) {
					$entry->value = $configuration_value['value'];
				}

				if (
					isset($configuration_value['update_category']) &&
					$configuration_value['update_category']
				) {
					$categoryNeedUpdate = $configuration_value['category'] ?? null;
					if ($categoryNeedUpdate) {
						$category = ConfigurationCategory::where('name', $categoryNeedUpdate)
														->orWhere('slug', $categoryNeedUpdate)
														->first();
						if ($category) {
							$entry->category_id = $category->id;
						}
					}
				}

				if (
					isset($configuration_value['force']) &&
					$configuration_value['force']
				) {
					$entry->name = $configuration_value['name'];
					$entry->type = $configuration_value['type'];
					$entry->status = $configuration_value['status'];
					$entry->value = $configuration_value['value'];
					$entry->description = $configuration_value['description'];
					$entry->created_by = isset($configuration_value['created_by']) ? $configuration_value['created_by'] : 0;
					$entry->secure = isset($configuration_value['secure']) ? $configuration_value['secure'] : false;
					$entry->is_payment_configuration = isset($configuration_value['is_payment_configuration']) ? $configuration_value['is_payment_configuration'] : false;
					if (isset($configuration_value['visible_manager'])) {
						$entry->visible_manager = $configuration_value['visible_manager'];
					}
					if (isset($configuration_value['editable_manager'])) {
						$entry->editable_manager = $configuration_value['editable_manager'];
					}
				}

				// update select_values allways, they change sometimes, updating them won't change selectged value
				$entry->select_values = isset($configuration_value['select_values']) ? $configuration_value['select_values'] : null;

				if (
					empty($entry->type) ||
					$entry->type == ''
				) {
					$entry->type = $configuration_value['type'];
				}
				if (isset($configuration_value['category'])) {
					$category = ConfigurationCategory::where('slug', '=', $configuration_value['category'])->first();
					if ($category) {
						$entry->category_id = $category->id;
						$entry->save();
					}

				}
				$entry->save();

			} else {
				if ($configuration_value['delete']) {
					// delete entry, if marked for deletion
					\Models\Configuration::where('key', $key)->delete();
				}
			}
			// Check if 'hidden' is set to true
			if (isset($configuration_value['hidden']) && $configuration_value['hidden'] === true) {
				$entry->hidden = true;
				$entry->save();
			}
		}
		foreach ($configurationCategoryArray as $key => $category_slug) {
			if (array_key_exists($key, $configuration_values)) {
				$entry =  \Models\Configuration::where('key', $key)->first();
				if($entry) {
					$category = ConfigurationCategory::where('slug', '=', $category_slug)->first();
					if ($category) {
						$entry->category_id = $category->id;
						$entry->save();
					}
				}

			}
		}

	}

	/**
	 * Smart update for JSON-based configuration options
	 * Compares against default configuration and adds any missing entries without overwriting existing values
	 *
	 * @param array $configuration_values The default configuration values array
	 * @param string $configKey The configuration key to update (e.g., 'PurgeAuditRecordsDays')
	 * @param string $itemIdentifier The field name used to identify items (default: 'name')
	 * @return void
	 */
	public static function smartUpdateJsonConfiguration($configuration_values, $configKey, $itemIdentifier = 'name') {
		// Check if the configuration key exists in default values
		if (!isset($configuration_values[$configKey])) {
			return;
		}

		$defaultConfig = json_decode($configuration_values[$configKey]['value'], true);

		if (!is_array($defaultConfig)) {
			return;
		}

		// Get all existing configurations for this key
		$existingConfigs = \Models\Configuration::where('key', $configKey)->get();

		foreach ($existingConfigs as $config) {
			$currentConfig = json_decode($config->value, true);

			if (!is_array($currentConfig)) {
				continue;
			}

			$updated = false;

			// Create lookup array of existing items by identifier
			$existingItems = [];
			foreach ($currentConfig as $item) {
				if (isset($item[$itemIdentifier])) {
					$existingItems[$item[$itemIdentifier]] = true;
				}
			}

			// Add any missing items from default configuration
			foreach ($defaultConfig as $defaultItem) {
				if (isset($defaultItem[$itemIdentifier]) && !isset($existingItems[$defaultItem[$itemIdentifier]])) {
					$currentConfig[] = $defaultItem;
					$updated = true;
				}
			}

			// Save if we added any missing items
			if ($updated) {
				$config->value = json_encode($currentConfig);
				$config->save();
			}
		}
	}
}
