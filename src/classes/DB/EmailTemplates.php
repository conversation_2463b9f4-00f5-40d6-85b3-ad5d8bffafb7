<?php
namespace DB;

use Models\EmailTemplate;

class EmailTemplates {
	public static function update ($settings = false) {

		// Handle renaming email templates
		$rename_templates = [
			'Jackdaw Free Licence Activated' => [
				'to' => 'Jackdaw Licence Activated',
				'subject' => 'Jackdaw Licence Activated'
			],
			'Learning Resource Signed Off by Coach' => [
				'to' => 'Learning Resource Signed Off by %%manager%%',
				'subject' => 'Learning Resource Signed Off by %%manager%%'
			],
			'Learning Resource Signed Off by Learner' => [
				'to' => 'Learning Resource Signed Off by %%user%%',
				'subject' => 'Learning Resource Signed Off by %%user%%'
			],
			'Assessment Notification' => [
				'to' => '%%assessment_notification%%',
				'subject' => '%%assessment_notification%%'
			],
			'Learning Resource Signed Off by %%user%%' => [
				'to' => '%%learning_resource%% Signed Off by %%user%%',
				'šubject' => '%%learning_resource%% Signed Off by %%user%%'
			],
			'Learning Resource Signed Off by %%manager%%' => [
				'to' => '%%learning_resource%% Signed Off by %%manager%%',
				'subject' => '%%learning_resource%% Signed Off by %%manager%%'
			],
			'Learning Resource Comment for Learner' => [
				'to' => '%%learning_resource%% Comment for %%user%%',
			],
			'Open Elms registration' => [
				'to' => '%%registration_email%%',
				'subject' => '%%registration_email%%',
			],
		];


		foreach ($rename_templates as $key => $rename_template) {
			$update_template = \Models\EmailTemplate
				::where('name', $key)
				->first()
			;
			if ($update_template) {
				$update_template->name = $rename_template['to'];
				if (isset($rename_template['subject'])) {
					$update_template->subject = $rename_template['subject'];
				}
				$update_template->save();
			}
		}


		$templates = [];
		$templates[] = [
			'name' => "Forgotten Password Link",
			'subject' => "Forgotten Password Link",
			'body' => '
				<p>Dear %%USER_FNAME%% %%USER_LNAME%%</p>,
					Click on this <a href=\"%%CONFIG_LMSUrl%%forgottenpassword/%%FORGOTTENPASSWORDID%%\">link</a> to recover your password.
					<br/>
					%%CONFIG_LMSUrl%%forgottenpassword/%%FORGOTTENPASSWORDID%%
					<br/>
					<br/><br/><p>Best Regards</p><p>Apprenticeship Administrator<br/>
				</p>
			',
			'site_versions' => '',
			'force_update' => false,
			'system_trigger' => 'User requests password reset via forgotten password form',
			'recipients' => 'User who requested password reset',
			'timings_recurrence' => 'Sent immediately upon request',
			'developer_notes' => 'This template is used in the password reset flow when users submit their username/email through the forgotten password form. It\'s triggered in the /forgottenpassword POST route in app.php. The template includes a unique token that allows the user to access the password reset form. The system checks if PasswordResetAnonymousMessage is enabled to determine response messaging. The token has validation and expiration built into the ForgottenPasswordToken model.'
		];


		$templates[] = [
			'name' => "Booking Cancellation",
			'subject' => "Cancellation of %%BOOKING_SESSION%%",
			'body' => '
				<p>Dear %%USER_FNAME%% %%USER_LNAME%%</p><br/><p>Unfortunately we have had to cancel the %%BOOKING_SESSION%% that you are booked on, sorry for the inconvenience.</p><p>Please review your schedule and rebook.</p><br/><br/><p>Best Regards</p><p>Apprenticeship Administrator<br/></p>
			',
			'site_versions' => '"apprentix" "openelmsschools" "openelmscolleges" "openelmsuniversities" "openelmsbusiness"',
			'force_update' => false,
			'system_trigger' => 'Administrator cancels booking session',
			'recipients' => 'Learners booked on cancelled session',
			'timings_recurrence' => 'Sent immediately upon cancellation',
			'developer_notes' => 'This template is triggered in the PUT /learning/module/{id} route (learning.php:2543) when administrators update learning module sessions and remove existing training sessions. The system compares current sessions in $learning->material->sessions with submitted session data by session_uid. When a session_uid exists in the database but not in the submitted data, it identifies a cancelled session. The system queries LearningSession model to find all users who booked that specific session_uid and sends them cancellation emails. The BOOKING_SESSION variable contains the learning module name. After email notification, the cancelled session records are deleted from the database. This ensures learners are notified when their booked training sessions are cancelled during resource management updates. Requires \'library-learning-resources-and-lessons\' update permissions.',
			'delete' => true
		];


		$templates[] = [
			'name' => "Booking Approval Request",
			'subject' => "Important training booking request needs approving",
			'body' => '
				<p>
				Dear %%USER_FNAME%%,<BR/>
				%%TRAINEE_FNAME%% %%TRAINEE_LNAME%% (%%TRAINEE_EMAIL%%) has requested to go on the following training course: %%LEARNING_NAME%%.<BR>
				You will need to approve this action before they can be accepted on the course...<BR>
				Click <a href=\"%%CONFIG_LMSUrl%%app/bookmodule/%%LEARNING_ID%%/%%TRAINEE_ID%%\">here</a> to approve/disapprove.
				<BR/><BR/>
								%%CONFIG_LMSUrl%%app/bookmodule/%%LEARNING_ID%%/%%TRAINEE_ID%%
				<BR/><BR/>
				Details of the course and trainee are listed below<BR>
				<BR>
				Many thanks<BR>
				Learning & Development
				<BR><BR><BR>
				Training Details:<BR>
				%%TRAINEE_FNAME%% %%TRAINEE_LNAME%% (%%TRAINEE_EMAIL%%) %%TRAINEE_USERNAME%%
				<BR>
				%%LEARNING_NAME%% at %%SESSION_LOCATION%% On %%SESSION_DATE%% with %%SESSION_TRAINER%%
				</p>
			',
			'site_versions' => '"apprentix" "openelmsschools" "openelmscolleges" "openelmsuniversities" "openelmsbusiness"',
			'force_update' => false,
			'system_trigger' => 'Learner requests booking approval',
			'recipients' => 'Manager/Approver',
			'timings_recurrence' => 'Sent immediately upon request',
			'developer_notes' => 'This template is triggered when a learner creates a new learning session booking that requires manager approval. It\'s fired in the created() event of the LearningSession model (src/classes/Models/LearningSession.php:84). The system checks if the current user is the session owner, is not a manager/admin, and the session is not yet approved. The email is sent to all managers of the learner with details about the training session including trainee name, session details, and date/time.',
			'delete' => true
		];


		$templates[] = [
			'name' => "Booking Disapproval",
			'subject' => "Important your training booking has NOT been approved",
			'body' => '
				<p>
				Dear %%USER_FNAME%%,<BR>
				Your booking for the %%LEARNING_NAME%% at %%SESSION_LOCATION%% on %%SESSION_DATE%% with %%SESSION_TRAINER%% has NOT been approved.<BR>
				Please make sure the booking is listed in your diary for the %%SESSION_DATE%%.<BR>
				Should you need to amend any details, log into the Training Management System at %%CONFIG_LMSUrl%% with your user ID of %%LEARNING_ID%% and search for %%LEARNING_NAME%%.<BR>
								<BR>
				Many thanks<BR>
				Learning & Development
				</p>
			',
			'site_versions' => '"apprentix" "openelmsschools" "openelmscolleges" "openelmsuniversities" "openelmsbusiness"',
			'force_update' => false,
			'delete' => true,
		];


		$templates[] = [
			'name' => "Booking Approval",
			'subject' => "Important information about your training booking approval",
			'body' => '
				<p>
				Dear %%USER_FNAME%%,<BR/>
				Your booking for the %%LEARNING_NAME%% at %%SESSION_LOCATION%% on %%SESSION_DATE%% with %%SESSION_TRAINER%% has been approved.<BR/>
				Please make sure the booking is listed in your diary for the %%SESSION_DATE%%.<BR/>
				Should you need to amend any details, log into the Training Management System at %%CONFIG_LMSUrl%% with your user ID of %%LEARNING_ID%% and search for %%LEARNING_NAME%%.<BR/>
					<BR/>
				Many thanks<BR/>
				Learning & Development
				</p>
			',
			'site_versions' => '"apprentix" "openelmsschools" "openelmscolleges" "openelmsuniversities" "openelmsbusiness"',
			'force_update' => false,
			'delete' => true
		];

		$templates[] = [
			'name' => "%%assessment_notification%%",
			'subject' => "%%assessment_notification%%",
			'body' => '
				Dear %%USER_FNAME%% %%USER_LNAME%%,
				We are glad to have you here.

				%%EMPLOYEE_USERNAME%%
				%%EMPLOYEE_FNAME%%
				%%EMPLOYEE_LNAME%%
				%%EMPLOYEE_EMAIL%%
				%%COURSE_NAME%%
				%%ASSESSMENT_DATE%%
				%%LMS_NAME%%
				%%LMS_LINK%%
				%%REGARDS%%
			',
			'site_versions' => '"apprentix" "openelmsschools" "openelmscolleges" "openelmsuniversities" "openelmsbusiness" "smcrsolution"',
			'force_update' => false,
			'system_trigger' => 'Learner completes and submits assessment via SCORM player',
			'recipients' => 'Managers of the learner who submitted the assessment',
			'timings_recurrence' => 'Sent immediately upon assessment submission when SendAssessmentNotificationEmail is enabled',
			'developer_notes' => 'This template is triggered in the Assessment::updateUserAssessments() method (Assessment.php:746) when a learner submits an assessment via SCORM player. The workflow: 1) User completes assessment in SCORM player, 2) SCORM calls submitassessment.php, 3) Assessment::submitAssessment() sets status to 2 (submitted), 4) Assessment::updateUserAssessments() processes submitted assessments and calls sendAssessmentNotification(), 5) Email sent to all managers of the learner. Controlled by $GLOBALS["CONFIG"]->SendAssessmentNotificationEmail configuration (default: false). Variables include employee details (username, name, email), course name, assessment date, and system information. Used for assessment workflow where managers need to review and grade submitted assessments.'
		];

		$templates[] = [
			'name' => "Registration Email",
			'subject' => "Welcome to the Training %%CONFIG_LMSTitle%%",
			'body' => '
				Dear %%USER_FNAME%% %%USER_LNAME%%,
				We are glad to have you here.
				<p>
				Dear %%USER_FNAME%%,<BR>
				You have now been signed up for the %%CONFIG_LMSTitle%%.<BR>
				You can log in any time at %%CONFIG_LMSUrl%% using your user ID of %%TRAINEE_USERNAME%% and password (if you have forgotten the password, click <a href=\"%%CONFIG_LMSUrl%%forgottenpassword\">here</a>).<BR>
				Many thanks<BR><BR>
				Learning & Development<BR>
				<p>
			',
			'site_versions' => '',
			'force_update' => false,
			'system_trigger' => 'User completes registration process or system syncs with Salesforce',
			'recipients' => 'Newly registered user',
			'timings_recurrence' => 'Sent immediately upon successful registration',
			'developer_notes' => 'This template is used in the NRAS Salesforce integration workflow in app.php when allowRemoteRegistration is enabled and licensing version is nras. It is triggered after successful Salesforce sync via API::salesforceUpdateLocalUser() method. The template provides welcome message with login instructions and password reset link for newly registered users from external systems.'
		];

		$templates[] = [
			'name' => "Jackdaw Licence Activated",
			'subject' => "Jackdaw Licence Activated",
			'body' => '
				<p>Hi</p>
				<p>You\'ve registered on Jackdaw and are accessing the FREE account - congratulations you are on the way to producing the most creative and engaging e-learning possible using system that represents a step-change in usability. Your licence allows you to create and download a single e-learning course, You can either create your own from scratch or use one of our 9 free courses to customise and use yourself.</p>
				<p>Hopefully you are now logged in and enjoying the experience. Should you need to reset your password at any time please <a href="%%CONFIG_LMSUrl%%forgottenpassword/%%FORGOTTENPASSWORDID%%">press on this link.</a><br>
					%%CONFIG_LMSUrl%%forgottenpassword/%%FORGOTTENPASSWORDID%%
				</p>
				<p>Should you need any help using the system please email or call us at any time.</p>
				<p>
					The Jackdaw Support Team Open eLMS
				</p>
			',
			'site_versions' => '',
			'force_update' => false
		];

		$templates[] = [
			'name' => "%%registration_email%%",
			'subject' => "%%registration_email%%",
			'body' => '
				<p>Hi</p>
				<p>You\'ve registered on Open Elms and are accessing the FREE account - Open Elms is the most usable and flexible learning system around. Your licence allows you to fully explore the trainee interface and use any of the 9 free courses that have been created using Open Elms\' integrated authoring system - Jackdaw Cloud.</p>
				<p>Hopefully you are now logged in and enjoying the experience. Should you need to reset your password at any time please  <a href="%%CONFIG_LMSUrl%%forgottenpassword/%%FORGOTTENPASSWORDID%%">press on this link. </a><br>
					%%CONFIG_LMSUrl%%forgottenpassword/%%FORGOTTENPASSWORDID%%
				</p>
				<p>Should you need to access the administration system, an online tour or fancy a go at creating and downloading your own e-learning then do let us know by replying to this email or calling the office.</p>
				<p>
					The Open Elms Support Team Open eLMS
				</p>
			',
			'site_versions' => '',
			'force_update' => false,
			'system_trigger' => 'User completes registration process or system syncs with Salesforce',
			'recipients' => 'Newly registered user',
			'timings_recurrence' => 'Sent immediately upon successful registration',
			'developer_notes' => 'This template is used in two scenarios: 1) When users register with no password (direct POST request) in the registration flow (app.php), where it sends a registration email with a password reset link, and 2) When NRAS systems with allowRemoteRegistration enabled sync with Salesforce. The template name gets renamed from "Open Elms registration" to "%%registration_email%%" via the rename_templates array. Different product versions use different subject lines as defined in APP/Licensing.php.'
		];

		$templates[] = [
			'name' => "Course Refresh Notification",
			'subject' => "Course Refresh Notification",
			'body' => '
				<p>Dear %%USER_FNAME%% %%USER_LNAME%%,</p>
				<p>It is time to take %%COURSE_NAME%% again.</p>
				<p>Click <a href="%%CONFIG_LMSUrl%%app/learner/resources?sq=%%COURSE_ID%%">here</a> to proceed.</p>
			',
			'site_versions' => '',
			'system_trigger' => 'Learning resource reaches refresh date or nightly cron processes refresh requirements',
			'recipients' => 'Learner assigned to refreshed learning resource',
			'timings_recurrence' => 'Sent when refresh is triggered (based on refresh date configuration or completion period)',
			'developer_notes' => 'This template is used in the learning refresh system via APP/Refresh.php sendRefreshEmail() method. It\'s triggered when learning resources need to be refreshed, either by reaching their refresh date or being processed by the nightly cron job "refreshResults". The sending can be controlled by the "sendRefreshEmail" configuration setting. Individual courses can have custom refresh email templates that override this default one via refresh_custom_email_subject and refresh_custom_email_body fields.'
		];

		$templates[] = [
			'name' => "Learning Resource Needs Attention",
			'subject' => "Learning Resource Needs Attention",
			'body' => '
				<p>Dear %%USER_FNAME%% %%USER_LNAME%%,</p>
				<br>
				<p>The following learning resource has been altered and will require your attention - <a href="%%CONFIG_LMSUrl%%app/learner/resources/%%COURSE_ID%%">%%LEARNING_RESOURCE_NAME%%</a>.</p>
				<br>
				<p>Please visit the link and respond accordingly ASAP.</p>
				<br>
				<p>%%REGARDS%%</p>
			',
			'site_versions' => '"apprentix" "openelmsschools" "openelmscolleges" "openelmsuniversities" "openelmsbusiness" "smcrsolution"',
			'system_trigger' => 'Manager refuses sign-off on learning resource completion',
			'recipients' => 'Learner whose learning resource completion was refused',
			'timings_recurrence' => 'Sent immediately when sign-off is refused by manager',
			'developer_notes' => 'This template is used when a manager refuses to sign off on a learner\'s completion of a learning resource. It\'s triggered in the learning result update endpoint (learning.php) when "sign_off_refused" is set to true. The email includes a link to the learning resource so the learner can take corrective action. This is primarily used in apprenticeship and educational systems where manager approval is required for completion. The template is available for multiple site versions as indicated in the site_versions field.'
		];

		$templates[] = [
			'name' => "%%learning_resource%% Signed Off by %%user%%",
			'subject' => "%%learning_resource%% Signed Off by %%user%%",
			'body' => '
				<p>Dear %%USER_FNAME%% %%USER_LNAME%%,</p>
				<br>
				<p>The following learning resource has been signed off - "%%LEARNING_RESOURCE_NAME%%" by user "%%LEARNER_FNAME%% %%LEARNER_LNAME%%" (%%LEARNER_ID%%).<br>This now requires Coach/Trainer approval.</p>
				<br>
				<p>Please visit <a href="%%CONFIG_LMSUrl%%">learning site</a> and respond accordingly ASAP.</p>
				<br>
				<p>%%REGARDS%%</p>
			',
			'site_versions' => '"apprentix" "openelmsschools" "openelmscolleges" "openelmsuniversities" "openelmsbusiness" "smcrsolution"',
			'system_trigger' => 'Learner signs off on their own learning resource completion',
			'recipients' => 'Managers/coaches assigned to the learner',
			'timings_recurrence' => 'Sent immediately when learner completes sign-off process',
			'developer_notes' => 'This template is triggered when a learner signs off on their own learning result using the signOffLearningResult function (APP/Learning.php). It notifies managers/coaches that the learner has completed their work and is requesting approval. The email is sent to all active managers of the learner (excluding those who have disabled manager notifications). It\'s only sent for resources that track progress and don\'t require management sign-off initially. The workflow is: learner completes work → learner signs off → managers get notified → managers can approve or refuse the completion.'
		];

		$templates[] = [
			'name' => "%%learning_resource%% Signed Off by %%manager%%",
			'subject' => "%%learning_resource%% Signed Off by %%manager%%",
			'body' => '
				<p>Dear %%USER_FNAME%% %%USER_LNAME%%,</p>
				<br>
				<p>The following learning resource has been successfully signed off by your Coach/Trainer - <a href="%%CONFIG_LMSUrl%%app/learner/resources/%%COURSE_ID%%">%%LEARNING_RESOURCE_NAME%%</a>.</p>
				<br>
				<p>No further action is required.</p>
				<br>
				<p>%%REGARDS%%</p>
			',
			'site_versions' => '"apprentix" "openelmsschools" "openelmscolleges" "openelmsuniversities" "openelmsbusiness" "smcrsolution"',
			'system_trigger' => 'Manager/coach approves learner\'s learning resource completion',
			'recipients' => 'Learner whose learning resource was approved',
			'timings_recurrence' => 'Sent immediately when manager completes approval process',
			'developer_notes' => 'This template is used when a manager/coach successfully approves a learner\'s learning resource completion. It\'s sent to the learner to notify them that their work has been approved. The email is triggered in multiple contexts: learning result updates (learning.php), email reminders with signoff parameter (Email.php), H5P content completion (h5p.php), and Turnitin submissions (turnitin.php). The template tells the learner no further action is required since their work is now approved. This completes the approval workflow cycle: learner completes work → learner signs off → manager gets notified → manager approves → learner gets confirmation.'
		];


		// $templates[] = [
		// 	'name' => "Form Needs Attention",
		// 	'subject' => "Form Needs Attention",
		// 	'body' => '
		// 		<p>Dear %%USER_FNAME%% %%USER_LNAME%%,</p>
		// 		<br>
		// 		<p>The following form has been altered and will require your attention - <a href="%%CONFIG_LMSUrl%%app</a>.</p>
		// 		<br>
		// 		<p>Please visit the link and respond accordingly ASAP.</p>
		// 		<br>
		// 		<p>%%REGARDS%%</p>
		// 	',
		// 	'site_versions' => '"apprentix" "openelmsschools" "openelmscolleges" "openelmsuniversities" "openelmsbusiness" "smcrsolution"',
		// ];

		$templates[] = [
			'name' => "Form Signed Off by %%user%%",
			'subject' => "Form Signed Off by %%user%%",
			'body' => '
				<p>Dear %%USER_FNAME%% %%USER_LNAME%%,</p>
				<br>
				<p>The Form has been signed off - "%%FORM_NAME%%" by user "%%LEARNER_FNAME%% %%LEARNER_LNAME%%" (%%LEARNER_ID%%).<br>This now requires manager approval.</p>
				<br>
				<p>Please visit <a href="%%CONFIG_LMSUrl%%"> site</a> and respond accordingly ASAP.</p>
				<br>
				<p>%%REGARDS%%</p>
			',
			'site_versions' => '"apprentix" "openelmsschools" "openelmscolleges" "openelmsuniversities" "openelmsbusiness" "smcrsolution"',
			'system_trigger' => 'Learner completes and signs off on a form submission',
			'recipients' => 'Managers assigned to the learner with appropriate sign-off roles',
			'timings_recurrence' => 'Sent immediately when learner completes form sign-off process',
			'developer_notes' => 'This template is used in the form system when a learner signs off on a form submission. It\'s triggered via UserForm::sendSignOffMailToManagers() in form-types.php when a learner completes a form (isLearner() context). The email is sent to managers who have roles defined in FormSignoffRole table for the specific form. The system checks if the user\'s role requires sign-off and only sends emails to managers with appropriate sign-off roles. This is only sent for forms that don\'t have sign-off order (has_sign_off_order = false).'
		];

		$templates[] = [
			'name' => "Form Signed Off by %%manager%%",
			'subject' => "Form Signed Off by %%manager%%",
			'body' => '
				<p>Dear %%USER_FNAME%% %%USER_LNAME%%,</p>
				<br>
				<p>"%%FORM_NAME%%"  form  has been successfully signed off by your manager - Please visit <a href="%%CONFIG_LMSUrl%%">site</a>.</p>
				<br>
				<p>No further action is required.</p>
				<br>
				<p>%%REGARDS%%</p>
			',
			'site_versions' => '"apprentix" "openelmsschools" "openelmscolleges" "openelmsuniversities" "openelmsbusiness" "smcrsolution"',
			'system_trigger' => 'Manager approves and signs off on learner\'s form submission',
			'recipients' => 'Learner whose form was approved by manager',
			'timings_recurrence' => 'Sent immediately when manager completes form approval',
			'developer_notes' => 'This template is defined but currently not in active use (code is commented out in form-types.php lines 411-432). It was intended to notify learners when their form submission has been approved by a manager. The template would be triggered after a manager signs off on a form, completing the approval workflow. The workflow would be: learner submits form → manager gets "Form Signed Off by %%user%%" → manager approves → learner gets this "Form Signed Off by %%manager%%" confirmation. Currently the email functionality is disabled but the template remains available for future use.'
		];


		$templates[] = [
			'name' => "%%learning_resource%% Comment for %%user%%",
			'subject' => "Comment has been made concerning a learning resource you are currently working on",
			'body' => '
				<p>Dear %%USER_FNAME%% %%USER_LNAME%%,</p>
				<br>
				<p>The following comment has been made concerning a learning resource you are currently working on:</p>
				<p>
					<strong>"%%LEARNING_RESOURCE_NAME%%"</strong> - %%COMMENT%%
				</p>

				<p><a href="%%CONFIG_LMSUrl%%app/learner/resources/%%COURSE_ID%%/signoff">%%LEARNING_RESOURCE_NAME%%</a>.</p>
				<br>
				<br>
				<p>%%REGARDS%%</p>
			',
			'site_versions' => '"apprentix" "openelmsschools" "openelmscolleges" "openelmsuniversities" "openelmsbusiness" "smcrsolution"',
			'system_trigger' => 'Manager or admin adds comment to learner\'s learning result',
			'recipients' => 'Learner who is working on the learning resource',
			'timings_recurrence' => 'Sent immediately when comment is added and visible to learner',
			'developer_notes' => 'This template is used in the learning comment system via the /addlearningresultscomment POST route in learning.php. It\'s triggered when a manager, admin, or processing manager adds a comment to a learning result that is marked as visible to the learner (visible_learner = true). The email is only sent if the commenter is not QA and the learning resource tracks progress. The template includes the actual comment text and provides a link to the learning resource sign-off page. The system also updates last contact dates for both the learner and the manager when comments are added.'
		];

		$templates[] = [
			'name' => "Learning Resource Comment for Manager",
			'subject' => "Comment has been made concerning a learning resource you are currently working on",
			'body' => '
				<p>Dear %%USER_FNAME%% %%USER_LNAME%%,</p>
				<br>
				<p>The following comment has been made concerning a learning resource you are currently working on:</p>
				<p>
					<strong>"%%LEARNING_RESOURCE_NAME%%"</strong> - %%COMMENT%%
				</p>
				<br>
				<br>
				<p>%%REGARDS%%</p>
			',
			'site_versions' => '"apprentix" "openelmsschools" "openelmscolleges" "openelmsuniversities" "openelmsbusiness" "smcrsolution"',
			'system_trigger' => 'Learner adds comment to their own learning result',
			'recipients' => 'Managers assigned to the learner',
			'timings_recurrence' => 'Sent immediately when learner adds a comment',
			'developer_notes' => 'This template is used in the learning comment system via the /addlearningresultscomment POST route in learning.php when a learner (isLearner() context) adds a comment to their own learning result. It notifies all managers assigned to the learner who have manager notifications enabled and are active. The email is sent to managers to keep them informed of learner activity and communication. The template includes the learner\'s comment text but does not include a direct link like the learner version. This creates a bidirectional communication system where both learners and managers can be notified of comments.'
		];

		$templates[] = [
			'name' => "%%programme%% resources reminder",
			'subject' => "Upcoming Learning",
			'body' => '
				<p>Dear %%USER_FNAME%% %%USER_LNAME%%,</p>
				<br>
				<p>
					Please look at the following learning. Your next reminder will be sent in %%FREQUENCY_DAYS%% days.
				</p>
				%%REMINDER_RESOURCES%%
				<p>
					If you are going to be unable to meet any deadlines then please contact your tutor as soon as possible.
				</p>
				<br>
				<br>
				<p>%%REGARDS%%</p>
			',
			'site_versions' => '"apprentix" "openelmsschools" "openelmscolleges" "openelmsuniversities" "openelmsbusiness"',
			'system_trigger' => 'Nightly cron job checks for apprenticeship learning resources approaching deadlines',
			'recipients' => 'Apprenticeship learners with upcoming learning resource deadlines',
			'timings_recurrence' => 'Sent when resources are within configured reminder period and last reminder was sent longer than frequency setting',
			'developer_notes' => 'This template is used in the Apprentix system via the apprentixNightlyReminders cron job (Models/Cron.php). It\'s triggered by ApprenticeshipStandardUser::sendReminderEmail() when learning resources are approaching their completion deadlines. The system checks learning results for apprenticeship standard users where the grace_at or completion_date_custom is within the configured reminder period (apprentixEmailReminderFrequency setting). The template includes a dynamic list of resources (%%REMINDER_RESOURCES%%) and respects the email frequency to avoid spam. This is part of the apprenticeship deadline management system to help learners stay on track with their learning requirements.'
		];

		$templates[] = [
			'name' => "Learner needs to Accept Function/Responsibility",
			'subject' => "Change of Function/Responsibility",
			'body' => '
				<p>Dear %%USER_FNAME%% %%USER_LNAME%%,</p>
				<p>
					You have been allocated this "%%FUNCTION_RESPONSIBILITY%%" under the Senior Managers and Certification Regime (SMCR). <br>
					You can log in to <a href="%%CONFIG_LMSUrl%%">%%CONFIG_LMSUrl%%</a> to check the full list of assigned to you.
				</p>

				<br>
				<br>
				<p>%%REGARDS%%</p>
			',
			'site_versions' => '"smcrsolution"',
		];

		$templates[] = [
			'name' => "Staff Approval Notification (learner been signed off)",
			'subject' => "Staff Approval Notification (learner been signed off)",
			'body' => '
				<p>Dear %%USER_FNAME%% %%USER_LNAME%%,</p>
				<p>Congratulations, you have been accepted as a %%SMCR_TYPE%% under the SMCR.</p>
				<br>
				<br>
				<p>%%REGARDS%%</p>
			',
			'site_versions' => '"smcrsolution"',
		];

		$templates[] = [
			'name' => "Committee position vacancy assigned - action needed",
			'subject' => "Committee position vacancy assigned - action needed",
			'body' => '
				<p>
					You have been assigned to a committee. <br>
					Please log in and either accept or reject the assigned position.
				</p>
				<br>
				<br>
				<p>%%REGARDS%%</p>
			',
			'site_versions' => '"smcrsolution"',
		];

		$templates[] = [
			'name' => "New work assigned",
			'subject' => "New work assigned",
			'body' => '
				<p>
					You have been assigned the following task. Please log in to the %%CONFIG_LMSName%% at <a href="%%CONFIG_LMSUrl%%">%%CONFIG_LMSUrl%%</a> to complete this task.
				</p>
				<br>
				<p>
					%%ASSIGNED_TASKS%%
				</p>
			',
			'site_versions' => '',
			'system_trigger' => 'Learning modules assigned to users through job designation changes, direct assignments, cron tasks, or evidence/blog/form creation',
			'recipients' => 'Users who have learning modules assigned through various assignment methods',
			'timings_recurrence' => 'Sent immediately when modules are assigned (controlled by sendEmailsCronAssignTask configuration for cron assignments)',
			'developer_notes' => 'This template is used across multiple assignment workflows: 1) Job designation changes via Models\Designation::sendNotification() and AssignCheck() methods, 2) Direct learning module assignments via learningusers.php route, 3) Cron-based assignments (department, designation, group) in UserLearningModule::assignUsers() when sendEmailsCronAssignTask config is enabled, 4) Evidence/blog/form creation assignments in learner.php route, 5) Apprenticeship standard assignments via ApprenticeshipStandardUser cron tasks, 6) Apprenticeship issue assignments via ApprenticeshipIssuesUserLearningModules. The template uses the "New work assigned" name in Email::sendEmailReminder() method. The ASSIGNED_TASKS variable contains formatted HTML links to the assigned resources.'
		];

		$templates[] = [
			'name' => "QA task completed and re-submitted for Quality Approval",
			'subject' => "QA task completed and re-submitted for Quality Approval",
			'body' => '
				<p>
					Hello, this email is to inform you that "%%LEARNING_RESOURCE_NAME%%" by "%%QA_LEARNER_NAME%%" which had Quality Assurance actions has now been completed by "%%COACH_NAME%%" and re-submitted for Quality Approval
				</p>
				<br>
				<p>
					<a href="%%CONFIG_LMSUrl%%">Follow the link should you wish to review it.</a>
				</p>
				<br>
				<p>%%REGARDS%%</p>
			',
			'site_versions' => '"apprentix" "openelmsschools" "openelmscolleges" "openelmsuniversities" "openelmsbusiness"',
			'system_trigger' => 'Learning resource with QA link is signed off by both trainee and manager after QA actions were completed',
			'recipients' => 'QA user assigned to review the learning resource',
			'timings_recurrence' => 'Sent immediately when both trainee and manager sign-off occurs after QA completion',
			'developer_notes' => 'This template is triggered in the PUT route learning.php /{module_id}/{user_id}/{id} when a learning result is updated with both sign_off_trainee and sign_off_manager set to true, and a QaLearningResult record exists linking the learning result to a QA user. The system checks for track_progress enabled on the module and active QA user status before sending. Custom variables include COACH_NAME (current authenticated user) and QA_LEARNER_NAME (the learner whose work was reviewed). This is part of the quality assurance workflow for apprenticeship learning resources.'
		];


		// SMCR specific Templates.

		$templates[] = [
			'name' => "Senior Manager requires sign-off",
			'subject' => "Senior Manager requires sign-off",
			'body' => '
				<p>
					"%%SMCR_STAFF%%" has completed all the necessary documentation and training is ready to be signed off. Please visit their account and select the sign-off button if satisfied.
				</p>
				<br>
				<p>
					<a href="%%CONFIG_LMSUrl%%">Follow the link should you wish to review it.</a>
				</p>
			',
			'site_versions' => '"smcrsolution"',
			'system_trigger' => 'Senior Manager staff completes all required training and documentation in SMCR system',
			'recipients' => 'Managers or administrators authorized to sign off Senior Manager staff',
			'timings_recurrence' => 'Sent when all completion requirements are met for Senior Manager staff type',
			'developer_notes' => 'This template is part of the SMCR (Senior Manager and Certification Regime) system for financial services compliance. It would be triggered when a user with staff_type_id corresponding to Senior Manager completes all required training, documentation, and assessments. The sign-off process is handled through the smcr-users.php routes and would typically involve checking completion status of F&P (Fitness and Propriety) category learning resources, training requirements, and documentation. The SMCR_STAFF variable contains the staff member name requiring sign-off. This template is only available for smcrsolution site version.'
		];
		$templates[] = [
			'name' => "Certification Staff Member requires sign-off",
			'subject' => "Certification Staff Member requires sign-off",
			'body' => '
				<p>
					"%%SMCR_STAFF%%" has completed all the necessary documentation and training is ready to be signed off. Please visit their account and select the sign-off button if satisfied.
				</p>
				<br>
				<p>
					<a href="%%CONFIG_LMSUrl%%">Follow the link should you wish to review it.</a>
				</p>
			',
			'site_versions' => '"smcrsolution"',
		];


		// Sent out to Compliance Manager - anyone with is_manager will get email.
		// Whenever full committee loses at least one member.
		$templates[] = [
			'name' => "Committee needs new member(s)",
			'subject' => "Committee needs new member(s)",
			'body' => '
				<p>
					"%%COMMITTEE%%" committee has become understaffed and needs new member(s).
				</p>
				<br>
				<p>
					<a href="%%CONFIG_LMSUrl%%">Follow the link should you wish to review it.</a>
				</p>
			',
			'site_versions' => '"smcrsolution"',
		];


		$templates[] = [
			'name' => "Senior Manager Approval",
			'subject' => "Senior Manager Approval",
			'body' => '
				<p>
					Congratulations - you have been granted approval in line with the SMCR Regulation.<br>
					You can log into your account and print off this year’s Statement of Responsibilities.
				</p>
				<br>
				<p>
					<a href="%%CONFIG_LMSUrl%%">Follow the link should you wish to review it.</a>
				</p>
			',
			'site_versions' => '"smcrsolution"',
		];

		$templates[] = [
			'name' => "Certification Staff Approval",
			'subject' => "Certification Staff Approval",
			'body' => '
				<p>
					Congratulations - you have been granted approval in line with the SMCR Regulation.<br>
					You can log into your account and print off this year’s Certificate of Completion.
				</p>
				<br>
				<p>
					<a href="%%CONFIG_LMSUrl%%">Follow the link should you wish to review it.</a>
				</p>
			',
			'site_versions' => '"smcrsolution"',
		];

		$templates[] = [
			'name' => "Senior Manager role needs to be (re)assessed",
			'subject' => "Senior Manager role needs to be (re)assessed",
			'body' => '
				<p>
					Time for reassessment<br>
					Please visit the %%CONFIG_LMSName%% at <a href="%%CONFIG_LMSUrl%%">%%CONFIG_LMSUrl%%</a> as you are due for reassessment in line with the SMCR.
				</p>
			',
			'site_versions' => '"smcrsolution"',
		];

		$templates[] = [
			'name' => "Certification Staff member needs to be (re)assessed",
			'subject' => "Certification Staff member needs to be (re)assessed",
			'body' => '
				<p>
					Time for reassessment<br>
					Please visit the %%CONFIG_LMSName%% at <a href="%%CONFIG_LMSUrl%%">%%CONFIG_LMSUrl%%</a> as your periodic training is due again in line with the SMCR.
				</p>
			',
			'site_versions' => '"smcrsolution"',
		];

		$templates[] = [
			'name' => "Conduct Rules Staff member needs to be (re)assessed",
			'subject' => "Conduct Rules Staff member needs to be (re)assessed",
			'body' => '
				<p>
					Time for reassessment<br>
					Please visit the %%CONFIG_LMSName%% at <a href="%%CONFIG_LMSUrl%%">%%CONFIG_LMSUrl%%</a> as you are due for reassessment in line with the SMCR.
				</p>
			',
			'site_versions' => '"smcrsolution"',
		];
		$templates[] = [
			'name' => "Conduct Rules Staff member needs to be (re)trained",
			'subject' => "Conduct Rules Staff member needs to be (re)trained",
			'body' => '
				<p>
					Time for refresher training<br>
					Please visit the %%CONFIG_LMSName%% at <a href="%%CONFIG_LMSUrl%%">%%CONFIG_LMSUrl%%</a> as you are due for refresher training in line with the requirements of the SMCR.
				</p>
			',
			'site_versions' => '"smcrsolution"',
		];

		// Send out after TIMING settings if position is not accepted or rejected!
		$templates[] = [
			'name' => "Committee Position Due",
			'subject' => "Committee Position Due",
			'body' => '
				<p>
					You have been assigned a committee position "%%COMMITTEE%%" which you are required to take action on as soon as possible. Please log in and accept or reject that offer.
				</p>
				<p>
					<a href="%%CONFIG_LMSUrl%%">Follow the link should you wish to review it.</a>
				</p>
			',
			'site_versions' => '"smcrsolution"',
		];

		$templates[] = [
			'name' => "Actions required following Quality Review",
			'subject' => "Actions required following Quality Review",
			'body' => '
				<p>
					The following work has been rejected by the quality assurance process. Please have a look and make amendments as necessary.
				</p>
				<p>
					<strong>%%REJECTED_WORK%%</strong>(%%REJECTED_WORK_ID%%) by "%%REJECTED_LEARNER%%".
				</p>
				<p></p>
				<p>
					<a href="%%CONFIG_LMSUrl%%">Follow the link should you wish to review it.</a>
				</p>
			',
			'site_versions' => '"apprentix" "openelmsschools" "openelmscolleges" "openelmsuniversities" "openelmsbusiness"',
			'system_trigger' => 'QA user rejects learning resource or apprenticeship standard work during quality review process',
			'recipients' => 'Managers of the learner whose work was rejected (excluding managers with email notifications disabled)',
			'timings_recurrence' => 'Sent immediately when QA status is set to "Rejected"',
			'developer_notes' => 'This template is triggered in two routes: 1) learning.php PUT /{module_id}/{user_id}/{id} when a QA user sets qa field to "Rejected" on a learning result, and 2) qa-control.php POST /save when quality control for apprenticeship standards is rejected. Both scenarios reset completion status to "in progress", clear sign-offs, and notify the learner\'s managers. The system checks manager notification preferences and active status before sending. Variables include REJECTED_WORK (learning resource/standard name), REJECTED_WORK_ID (resource/standard ID), and REJECTED_LEARNER (learner name).'
		];

		$templates[] = [
			'name' => "QA judgement has been responded",
			'subject' => "QA judgement has been responded",
			'body' => '
				<p>
					The following QA judgement has been responded to:
				</p>
				<p>
					%%TASK_TYPE%%
				</p>
				<p>
					%%FEEDBACK%%
				</p>
			',
			'site_versions' => '"apprentix" "openelmsschools" "openelmscolleges" "openelmsuniversities" "openelmsbusiness"',
			'system_trigger' => 'QA user provides feedback response to quality control judgement',
			'recipients' => 'Learner who originally submitted the work that received QA feedback',
			'timings_recurrence' => 'Sent immediately when QA feedback is submitted',
			'developer_notes' => 'This template is triggered in qa-control.php POST /savefeedback route when a QA user provides feedback response to a quality control judgement. The system creates a new QualityControl record with type "feedback" and qa status "Rejected", then notifies the original learner about the response. The TASK_TYPE variable contains either the provided typeTitle or the learning module name from the LearningResult. The FEEDBACK variable includes the judgement reason, QA user name, and timestamp. Only the learner who submitted the original work receives this notification.'
		];


		$templates[] = [
			'name' => "QA Report ready for Quality Approval",
			'subject' => "QA Report ready for Quality Approval",
			'body' => '
				<p>
					Hello, this email is to inform you that the %%QUALITY_REPORT%% for %%LEARNER_NAME%% has been actioned by %%COACH_NAME%% and submitted for Quality Approval
				</p>
				<p></p>
				<p>
					<a href="%%CONFIG_LMSUrl%%">Follow the link should you wish to review it.</a>
				</p>
			',
			'site_versions' => '"apprentix" "openelmsschools" "openelmscolleges" "openelmsuniversities" "openelmsbusiness"',
			'system_trigger' => 'Non-QA user adds comment to QA Report that is not yet completed, changing status to In Progress',
			'recipients' => 'QA user who originally created the review (review creator)',
			'timings_recurrence' => 'Sent immediately when QA Report status changes from non-InProgress to InProgress',
			'developer_notes' => 'This template is triggered in apprenticeshipissues.php POST /user/review/comment route when a non-QA user (admin, manager, or user with access) adds a comment to a ManagerReview with visit_type "QA Report" that is not completed. The system automatically sets the review completion_status to "In Progress" and notifies the QA user who created the review. Variables include QUALITY_REPORT (visit_type), LEARNER_NAME (review user full name), and COACH_NAME (current authenticated user full name). This ensures QA users are notified when their reports receive updates from coaches or managers.'
		];


		$templates[] = [
			'name' => "Welcome message Admin",
			'subject' => "Welcome to the system",
			'body' => '
				<p>Hello %%USER_FNAME%%</p>

				<p>Welcome to the %%CONFIG_LMSName%% - you can access it at <a href="%%CONFIG_LMSUrl%%">%%CONFIG_LMSUrl%%</a> - you can use %%USER_USERNAME%% to log in. If you do not already have a password then click on the forgotten password at the bottom of the screen to reset your password and gain entry.</p>

				<p>You will need to follow the onboarding process to set up the system for your employees/students etc, you will find this in a flow chart at the very back of the system manual at <a href="%%CONFIG_LMSUrl%%help/%%manual_file_name%%">%%CONFIG_LMSUrl%%help/%%manual_file_name%%</a>. Feel free to get started with this straight away - it has links to the relevant sections of the manual for each task and indicates where support can help you out.</p>

				<p>The first step you will probably need to do is to complete the spreadsheet at <a href="%%CONFIG_LMSUrl%%tpl/excel/import_new_users.xlsx">%%CONFIG_LMSUrl%%tpl/excel/import_new_users.xls</a> - we can help you with this during onboarding but please ensure the details are there. Take care of the field entitled "Job" since this one is the one to which %%learning_resources%% are linked (i.e. all people with the same "Designation" or job field can be assigned the same default %%learning_resources%% on start up).</p>

				<p>Many thanks</p>

				<p>Open eLMS</p>
			',
			'site_versions' => '',
			'system_trigger' => 'New admin user account creation or instruction email sending for existing admin users',
			'recipients' => 'Users with admin role permissions',
			'timings_recurrence' => 'Sent immediately when instruction email is triggered for admin users, or via cron job when enableAutomaticStarUpInstructionEmails is enabled',
			'developer_notes' => 'This template is used by Email::sendInstructionEmail() method when sending welcome/instruction emails to users with admin role (is_admin = true). It can be triggered in multiple ways: 1) Manual sending via user.php routes when send_instruction_email option is selected during user creation/update, 2) During user import processes in ImportUserController and ImportController when notify_roles includes admin role, 3) Automatic sending via sendAutomaticStarUpInstructionEmail cron task for users with startup_instructions_sent = false. The template includes onboarding guidance specific to admin users, including links to system manual, user import spreadsheet, and setup instructions.'
		];

		$templates[] = [
			'name' => "Welcome Message Manager",
			'subject' => "Welcome to the system",
			'body' => '
				<p>Hello %%USER_FNAME%%</p>

				<p>Welcome to the %%CONFIG_LMSName%% - you can access it at <a href="%%CONFIG_LMSUrl%%">%%CONFIG_LMSUrl%%</a> - you can use %%USER_USERNAME%% to log in. If you do not already have a password then click on the forgotten password at the bottom of the screen to reset your password and gain entry.</p>

				<p>When logging in you should be a dashboard giving you an overview of performance in the organisation. Select the dark orange button in the top right of the screen. This will show you the management dashboard. It should be clear from the design what you will need to do - just follow the instructions on the screen.</p>

				<p>There is no need for a formal manual - should you need any assistance you can press the blue help button (usually towards the top right of the screen) and it will give you a guided tour of functionality there.</p>

				<p>We hope you enjoy using the software.</p>

				<p>Many thanks</p>

				<p>Open eLMS</p>
			',
			'site_versions' => '',
			'system_trigger' => 'User account created with manager role, or when send instruction email is manually triggered for manager users',
			'recipients' => 'Managers - users with manager role permissions',
			'timings_recurrence' => 'Sent immediately upon account creation or when manually triggered',
			'developer_notes' => 'Part of the welcome email system in Email::sendInstructionEmail() method. Template selection is based on user role permissions - users with is_manager role get this template. Can be triggered during user creation (routes/user.php), user imports (ImportUserController.php), or via cron task for automatic startup instructions. Role-based routing ensures managers get appropriate welcome message with system access instructions.'
		];

		$templates[] = [
			'name' => "Welcome Message Trainee",
			'subject' => "Welcome to the system",
			'body' => '
				<p>Hello %%USER_FNAME%%</p>

				<p>Welcome to the %%CONFIG_LMSName%% - you can access it at <a href="%%CONFIG_LMSUrl%%">%%CONFIG_LMSUrl%%</a> - you can use %%USER_USERNAME%% to log in. If you do not already have a password then click on the forgotten password at the bottom of the screen to reset your password and gain entry.</p>

				<p>There is no need for a formal manual - should you need any assistance you can press the white help button on the top right of the screen and it will give you a guided tour of functionality there.</p>

				<p>Should you have any issues please contact your local system administrator.</p>

				<p>We hope you enjoy using the software.</p>

				<p>Many thanks</p>

				<p>Open eLMS</p>
			',
			'site_versions' => '',
			'system_trigger' => 'User account created with learner role, or when send instruction email is manually triggered for learner users',
			'recipients' => 'Learners - users with learner role permissions',
			'timings_recurrence' => 'Sent immediately upon account creation or when manually triggered',
			'developer_notes' => 'Part of the welcome email system in Email::sendInstructionEmail() method. Template selection is based on user role permissions - users with is_learner role get this template. Can be triggered during user creation (routes/user.php), user imports (ImportUserController.php), or via cron task for automatic startup instructions. Role-based routing ensures learners get appropriate welcome message with basic system access instructions and support contact information.'
		];

		$templates[] = [
			'name' => "Visit set up",
			'subject' => "Visit set up",
			'body' => '
				<p>
					You have a new visit scheduled. Please log in to the %%CONFIG_LMSName%% at <a href="%%CONFIG_LMSUrl%%">%%CONFIG_LMSUrl%%</a> to review details and add it to your diary.
				</p>
				<p>
					%%ASSIGNED_TASKS%%
				</p>
			',
			'site_versions' => '"apprentix" "openelmsschools" "openelmscolleges" "openelmsuniversities" "openelmsbusiness" "smcrsolution"',
		];


		$templates[] = [
			'name' => "Visit Completed",
			'subject' => "Visit Completed",
			'body' => '
				<p>
					You have a completed your scheduled visit. Feel free to log in to the %%CONFIG_LMSName%% at <a href="%%CONFIG_LMSUrl%%">%%CONFIG_LMSUrl%%</a> to review details of the visit. It will be added to your training records.
				</p>
				<p>
					%%ASSIGNED_TASKS%%
				</p>
			',
			'site_versions' => '"apprentix" "openelmsschools" "openelmscolleges" "openelmsuniversities" "openelmsbusiness" "smcrsolution"',
		];

		$templates[] = [
			'name' => "Virtual meeting booked",
			'subject' => "Virtual meeting booked",
			'slug' => 'schedule_created',
			'body' => '
				<p>
					Dear %%USER_FNAME%%
				</p>
				<p>
					You have been invited to the following learning session. Please make add this to any external calendar you need to.
				</p>
				<p>
					Venue Instructions : %%VENUE_INSTRUCTIONS%%
				</p>
				<p>
					<img src="%%CONFIG_LMSUrl%%venue/%%VENUE_IMAGE%%" alt="venue image" width="750" height="550" />
				</p>
				<p>
					<a href="%%CONFIG_LMSUrl%%app/learner/resources/%%LESSON_ID%%-%%EVENT_ID%%">
						%%EVENT_NAME%% at %%EVENT_TIME%% with %%MANAGER_NAMES%%
					</a>
				</p>
				<p>
					%%SPECIAL_REQUIREMENTS%%
				</p>
				<p>
					%%ATTACHMENT_LIST%%
				</p>
				<p>
					Regards<br>
					%%MANAGER_NAMES%%
				</p>
			',
			'site_versions' => '"apprentix" "openelmsschools" "openelmscolleges" "openelmsuniversities" "openelmsbusiness" "smcrsolution"',
			'force_update' => false,
			'system_trigger' => 'Virtual meeting or lesson schedule (type: lesson or default) created and learners are added to the event',
			'recipients' => 'Learners assigned to the scheduled virtual meeting or lesson',
			'timings_recurrence' => 'Sent immediately when schedule is created with learner assignments',
			'developer_notes' => 'This template is triggered by Schedule::sendEmailsToUsers() when a new schedule is created with type "lesson" or default. The slug "schedule_created" maps to this template. Email queue deduplication prevents multiple emails for the same event (EmailQueue model removes duplicate schedule_created emails). The template includes venue details, event links, manager information, and attachments. Also used for rescheduled events with different template variant.'
		];

		$templates[] = [
			'name' => "Virtual meeting booked rescheduled",
			'subject' => "Revised event details - please make a note",
			'slug' => 'schedule_created_rescheduled',
			'body' => '
				<p>
					Dear %%USER_FNAME%%
				</p>
				<p>
					You have been invited to the following learning session. Please make add this to any external calendar you need to.
				</p>
				<p>
					Venue Instructions : %%VENUE_INSTRUCTIONS%%
				</p>
				<p>
					<img src="%%CONFIG_LMSUrl%%venue/%%VENUE_IMAGE%%" alt="venue image" width="750" height="550" />
				</p>
				<p>
					<a href="%%CONFIG_LMSUrl%%app/learner/resources/%%LESSON_ID%%-%%EVENT_ID%%">
						%%EVENT_NAME%% at %%EVENT_TIME%% with %%MANAGER_FNAME%% %%MANAGER_LNAME%%
					</a>
				</p>
				<p>
					Booking Deadline : %%BOOKING_DEADLINE_DATE%%
				</p>
				<p>
					Cancellation Deadline : %%CANCELLATION_DEADLINE_DATE%%
				</p>
				<p>
					%%SPECIAL_REQUIREMENTS%%
				</p>
				<p>
					%%ATTACHMENT_LIST%%
				</p>
				<p>
					Regards<br>
					%%MANAGER_FNAME%% %%MANAGER_LNAME%%
				</p>
			',
			'site_versions' => '"apprentix" "openelmsschools" "openelmscolleges" "openelmsuniversities" "openelmsbusiness" "smcrsolution"',
			'force_update' => false,
			'system_trigger' => 'Virtual meeting or lesson schedule (type: lesson or default) is rescheduled (time, date, or details changed)',
			'recipients' => 'Learners assigned to the rescheduled virtual meeting or lesson',
			'timings_recurrence' => 'Sent immediately when schedule is updated/rescheduled, only for future events',
			'developer_notes' => 'This template is triggered by Schedule::sendRescheduledMail() when an existing schedule is modified. The slug "schedule_created_rescheduled" maps to this template for lesson type or default schedules. Only sends for future events (past events are ignored). Includes additional booking/cancellation deadline information compared to the original booking email. Template selection is based on schedule type: lesson uses this template, meeting uses schedule_meeting_created_rescheduled.'
		];

		$templates[] = [
			'name' => "Refreshed Learning Resource Notification",
			'subject' => "Your learning has been refreshed and is due your attention",
			'slug' => 'refreshed_learning_resource_notification',
			'body' => '
				<p>
					Dear %%USER_FNAME%%
				</p>
				<p>
					Learning that has been assigned to you will soon need retaking on %%REFRESH_DATE%%.  Please log in and check out the following should you wish to this early:
				</p>
				<p>
					%%LEARNING_RESOURCE_NAME%%.
				</p>
				<p>
					%%REGARDS%%
				</p>
			',
			'site_versions' => '"apprentix" "openelmsschools" "openelmscolleges" "openelmsuniversities" "openelmsbusiness" "smcrsolution"',
			'force_update' => false,
			'system_trigger' => 'Learning resources that require periodic refreshing reach their notification timing threshold',
			'recipients' => 'Learners assigned to learning resources that are due for refresh',
			'timings_recurrence' => 'Sent based on RefresherNotificationTimings configuration at specified intervals before refresh due date',
			'developer_notes' => 'This template is triggered by the emailNotifyRefreshedResources cron task, which runs every 360 minutes (6 hours). The LearningResult::emailNotifyRefreshedResources() method identifies learning resources (not courses/lessons) that are approaching their refresh due date based on RefresherNotificationTimings configuration. Template selection differentiates between courses (Refreshed Lesson Notification) and regular learning resources (this template). The system notifies learners in advance so they can complete refresher training before the deadline.'
		];

		$templates[] = [
			'name' => "Refreshed Lesson Notification",
			'subject' => "Learning content has been refreshed and is due your attention",
			'slug' => 'refreshed_lesson_notification',
			'body' => '
				<p>
					Dear %%USER_FNAME%%
				</p>
				<p>
					Learning that has been assigned to you will soon need retaking on %%REFRESH_DATE%%.  Please log in and check out the following should you wish to this early - you may need to book on to a related event to it is advisable to book a date in early:
				</p>
				<p>
					%%LESSON_NAME%%.
				</p>
				<p>
					%%REGARDS%%
				</p>
			',
			'site_versions' => '"apprentix" "openelmsschools" "openelmscolleges" "openelmsuniversities" "openelmsbusiness" "smcrsolution"',
			'force_update' => false,
			'system_trigger' => 'Course/lesson content that requires periodic refreshing reaches notification timing threshold',
			'recipients' => 'Learners assigned to courses/lessons that are due for refresh',
			'timings_recurrence' => 'Sent based on RefresherNotificationTimings configuration at specified intervals before refresh due date',
			'developer_notes' => 'This template is triggered by the emailNotifyRefreshedResources cron task, which runs every 360 minutes (6 hours). The LearningResult::emailNotifyRefreshedResources() method identifies courses/lessons (is_course = 1) that are approaching their refresh due date based on RefresherNotificationTimings configuration. Template selection differentiates between courses/lessons (this template) and regular learning resources (Refreshed Learning Resource Notification). Includes guidance about booking related events early since courses often have scheduling requirements.'
		];

		$templates[] = [
			'name' => "Refreshed Learning Programme Notification",
			'subject' => "Learning content has been refreshed and is due your attention",
			'slug' => 'refreshed_learning_programme_notification',
			'body' => '
				<p>
					Dear %%USER_FNAME%%
				</p>
				<p>
					A Learning programme that has been assigned to you will soon need retaking on %%REFRESH_DATE%%.  Please make the necessary preparations to manage your time to deal with the learning accordingly.
				</p>
				<p>
					%%PROGRAMME_NAME%%.
				</p>
				<p>
					%%REGARDS%%
				</p>
			',
			'site_versions' => '"apprentix" "openelmsschools" "openelmscolleges" "openelmsuniversities" "openelmsbusiness" "smcrsolution"',
			'force_update' => false,
			'system_trigger' => 'Apprenticeship learning programmes that require periodic refreshing reach notification timing threshold',
			'recipients' => 'Learners assigned to apprenticeship standards/programmes that are due for refresh',
			'timings_recurrence' => 'Sent based on RefresherNotificationTimings configuration at specified intervals before programme refresh due date',
			'developer_notes' => 'This template is triggered by the emailNotifyRefreshedResources cron task, which runs every 360 minutes (6 hours). The ApprenticeshipStandardUser::emailNotifyRefreshedResources() method identifies apprenticeship programmes that are approaching their refresh due date based on RefresherNotificationTimings configuration. The system tracks refresh_notify_mail_send_at to prevent duplicate notifications on the same day. This template is specific to apprenticeship programmes and differs from individual learning resource/lesson notifications by emphasizing time management for comprehensive programme completion.'
		];

		$templates[] = [
			'name' => "Virtual meeting about to start",
			'subject' => "Virtual meeting about to start",
			'slug' => 'schedule_reminder',
			'body' => '
				<p>
					Dear %%USER_FNAME%%
				</p>
				<p>
					Please be aware that the following learning session will start shortly at %%EVENT_TIME%%. Please access this using the link below
				</p>
				<p>
					<a href="%%CONFIG_LMSUrl%%app/learner/resources/%%LESSON_ID%%">
						<strong>
							%%EVENT_NAME%%
						</strong>
					</a>
				</p>

				<p>
					Regards<br>
					%%MANAGER_FNAME%% %%MANAGER_LNAME%%
				</p>
			',
			'site_versions' => '"apprentix" "openelmsschools" "openelmscolleges" "openelmsuniversities" "openelmsbusiness" "smcrsolution"',
			'system_trigger' => 'Scheduled events (type: lesson or default) approach their start time based on advance notification intervals',
			'recipients' => 'Learners assigned to upcoming scheduled events who have not opted out of email notifications',
			'timings_recurrence' => 'Sent at configured advance notification intervals before event start time (eventReminderAdvanceNotificationInterval)',
			'developer_notes' => 'This template is triggered by Schedule::sendReminderAdvanceNotification() cron task. The system sends reminder emails based on eventReminderAdvanceNotificationInterval configuration (e.g., 60, 30, 15 minutes before event). Template selection is based on schedule type: lessons/default use this template (schedule_reminder), meetings use schedule_meeting_reminder. Only sends to users with ignore_email = 0 and for future events. Includes direct link to the learning resource for easy access.'
		];

		$templates[] = [
			'name' => "New meeting created",
			'subject' => "New meeting created",
			'slug' => 'schedule_meeting_created',
			'body' => '
				<p>
					Dear %%USER_FNAME%%
				</p>
				<p>
					You have been invited to the following meeting. Please add this to any external calendar (this has also been entered on your calendar in the system).
				</p>
				<p>
					<strong>%%EVENT_NAME%%<strong> at %%EVENT_TIME%% at %%EVENT_LOCATION%% with %%MANAGER_NAMES%%
				</p>
				<p>
					%%EVENT_DESCRIPTION%%
				</p>
				<p>
					Regards<br>
					%%MANAGER_NAMES%%
				</p>
			',
			'site_versions' => '"apprentix" "openelmsschools" "openelmscolleges" "openelmsuniversities" "openelmsbusiness" "smcrsolution"',
			'force_update' => false,
			'system_trigger' => 'Meeting schedule (type: meeting) created and learners are added to the event',
			'recipients' => 'Learners assigned to the scheduled meeting',
			'timings_recurrence' => 'Sent immediately when meeting schedule is created with learner assignments',
			'developer_notes' => 'This template is triggered by Schedule::sendEmailsToUsers() when a new schedule is created with type "meeting". The slug "schedule_meeting_created" maps to this template. Email queue deduplication prevents multiple emails for the same event (EmailQueue model removes duplicate schedule_meeting_created emails). Template differs from lesson notifications by emphasizing meeting format (location, description) rather than learning resources. Also used for rescheduled meetings with different template variant.'
		];

		$templates[] = [
			'name' => "New meeting created rescheduled",
			'subject' => "Revised event details - please make a note",
			'slug' => 'schedule_meeting_created_rescheduled',
			'body' => '
				<p>
					Dear %%USER_FNAME%%
				</p>
				<p>
					You have been invited to the following meeting. Please add this to any external calendar (this has also been entered on your calendar in the system).
				</p>
				<p>
					<strong>%%EVENT_NAME%%<strong> at %%EVENT_TIME%% at %%EVENT_LOCATION%% with %%MANAGER_FNAME%% %%MANAGER_LNAME%%
				</p>
				<p>
					%%EVENT_DESCRIPTION%%
				</p>
				<p>
					Regards<br>
					%%MANAGER_FNAME%% %%MANAGER_LNAME%%
				</p>
			',
			'site_versions' => '"apprentix" "openelmsschools" "openelmscolleges" "openelmsuniversities" "openelmsbusiness" "smcrsolution"',
			'force_update' => false,
			'system_trigger' => 'Meeting schedule (type: meeting) is rescheduled (time, date, or details changed)',
			'recipients' => 'Learners assigned to the rescheduled meeting',
			'timings_recurrence' => 'Sent immediately when meeting schedule is updated/rescheduled, only for future events',
			'developer_notes' => 'This template is triggered by Schedule::sendRescheduledMail() when an existing schedule is modified. The slug "schedule_meeting_created_rescheduled" maps to this template for meeting type schedules. Only sends for future events (past events are ignored). Template selection is based on schedule type: meeting uses this template, lesson uses schedule_created_rescheduled. Template emphasizes meeting details (location, description) rather than learning resources.'
		];

		$templates[] = [
			'name' => "Meeting about to start",
			'subject' => "Meeting about to start",
			'slug' => 'schedule_meeting_reminder',
			'body' => '
				<p>
					Dear %%USER_FNAME%%
				</p>
				<p>
					Please be aware that the following meeting will start shortly at %%EVENT_TIME%% at %%EVENT_LOCATION%% with %%MANAGER_FNAME%% %%MANAGER_LNAME%%.
				</p>
				<p>
					%%EVENT_DESCRIPTION%%
				</p>
				<p>
					Regards<br>
					%%MANAGER_FNAME%% %%MANAGER_LNAME%%
				</p>
			',
			'site_versions' => '"apprentix" "openelmsschools" "openelmscolleges" "openelmsuniversities" "openelmsbusiness" "smcrsolution"',
			'system_trigger' => 'Scheduled meetings (type: meeting) approach their start time based on advance notification intervals',
			'recipients' => 'Learners assigned to upcoming scheduled meetings who have not opted out of email notifications',
			'timings_recurrence' => 'Sent at configured advance notification intervals before meeting start time (eventReminderAdvanceNotificationInterval)',
			'developer_notes' => 'This template is triggered by Schedule::sendReminderAdvanceNotification() cron task. The system sends reminder emails based on eventReminderAdvanceNotificationInterval configuration (e.g., 60, 30, 15 minutes before event). Template selection is based on schedule type: meetings use this template (schedule_meeting_reminder), lessons/default use schedule_reminder. Only sends to users with ignore_email = 0 and for future events. Includes meeting location and description details.'
		];


		$templates[] = [
			'name' => "%%version_name%%: Please complete your user details",
			'subject' => "%%version_name%%: Please complete your user details",
			'slug' => 'user_field_alert_reminder',
			'body' => '
				<p>
					Dear %%USER_FNAME%%
				</p>
				<p>
					Please complete your user details. The following fields are currently empty:
				</p>
				<ul>
					%%INCOMPLETE_FIELDS%%
				</ul>

				<p>Yours sincerely</p>
			',
			'site_versions' => '',
			'system_trigger' => 'User has missing required profile fields and scheduled field alert check runs',
			'recipients' => 'Users with incomplete profile fields as configured in userFieldAlertSystemMonitoredFields',
			'timings_recurrence' => 'Sent based on userFieldAlertSystemInterval configuration (days between checks)',
			'developer_notes' => 'This template is triggered by User::userFieldAlert() cron method when enableUserFieldAlertSystem is enabled. The system checks all users for missing fields configured in userFieldAlertSystemMonitoredFields. Users with the same missing fields are grouped together for efficient email processing. Tracks userFieldAlertSystemIntervalRunTime to control timing. Supports both standard user fields and extended fields. Template variable INCOMPLETE_FIELDS contains formatted list of missing field names.'
		];

		$templates[] = [
			'name' => "Senior Manager Self-Attestation Alert",
			'subject' => "Senior Manager Self-Attestation Alert",
			'slug' => 'senior_manager_self_attestation_alert',
			'body' => '
				<p>
					Dear %%USER_FNAME%%
				</p>
				<p>
					Your SMCR records are now ready for you to sign-off.<br>
					Please visit the %%CONFIG_LMSName%% at %%CONFIG_LMSUrl%% and press the `Self-Attested` [Edit] button on your record to complete this process.<br>
					It is vital that you do this so the SMCR process can continue.
				</p>
			',
			'site_versions' => '"smcrsolution"',
		];

		$templates[] = [
			'name' => "Certification Staff Member Self-Attestation Alert",
			'subject' => "Certification Staff Member Self-Attestation Alert",
			'slug' => 'certification_staff_member_self_attestation_alert',
			'body' => '
				<p>
					Dear %%USER_FNAME%%
				</p>
				<p>
					Your SMCR records are now ready for you to sign-off.<br>
					Please visit the %%CONFIG_LMSName%% at %%CONFIG_LMSUrl%% and press the `Self-Attested` [Edit] button on your record to complete this process.<br>
					It is vital that you do this so the SMCR process can continue.
				</p>
			',
			'site_versions' => '"smcrsolution"',
		];
		$templates[] = [
			'name' => "Account Approval",
			'subject' => "Your account is approved",
			'slug' => 'account_approval',
			'body' => '
				<p>
					Dear %%USER_FNAME%%
				</p>
				<br>
				<p>Your account has been approved.</p>
					<br>
				<p>%%REGARDS%%</p>

			',
			'site_versions' => '',
			'system_trigger' => 'Administrator approves user account from pending status',
			'recipients' => 'User whose account has been approved',
			'timings_recurrence' => 'Sent immediately when administrator changes user approval status to "Approved"',
			'developer_notes' => 'This template is triggered in user.php routes when an administrator changes a user\'s approval_status to "Approved". Can be triggered for individual users via /user/approval_status or bulk users via /user/approval_status_bulk. The approval system allows administrators to review and approve pending user registrations. Works in conjunction with "Account Denied" template for rejected accounts.'
		];

		$templates[] = [
			'name' => "Account Denied",
			'subject' => "Your account is denied",
			'slug' => 'account_denied',
			'body' => '
				<p>
				Dear %%USER_FNAME%%
				</p>
				<br>
				<p>Apologies your account has not been approved.  Please feel free to contact the administrator of this system to find out why.</p>
				 <br>
				<p>%%REGARDS%%</p>

			',
			'site_versions' => '',
			'system_trigger' => 'Administrator rejects user account from pending status',
			'recipients' => 'User whose account has been rejected',
			'timings_recurrence' => 'Sent immediately when administrator changes user approval status to "Rejected"',
			'developer_notes' => 'This template is triggered in user.php routes when an administrator changes a user\'s approval_status to "Rejected". Can be triggered for individual users via /user/approval_status or bulk users via /user/approval_status_bulk. The approval system allows administrators to review and reject pending user registrations. Works in conjunction with "Account Approval" template for approved accounts.'
		];

		$templates[] = [
			'name' => "Competency Awarded",
			'subject' => "Competency Awarded",
			'slug' => 'competency_awarded',
			'body' => '
				<p>Dear %%USER_FNAME%% %%USER_LNAME%%,</p>
				<br>
				<p>Congratulations you have just achieved the %%COMPETENCY%% competency. You achieve these when you have successfully completed necessary learning. Do check on your personal progress for details.</p>
				<br>
				<p>%%REGARDS%%</p>
			',
			'site_versions' => '"apprentix" "openelmsschools" "openelmscolleges" "openelmsuniversities" "openelmsbusiness" "smcrsolution"',
			'system_trigger' => 'Learner completes learning that awards sufficient points to achieve a competency',
			'recipients' => 'Learner who has achieved the competency',
			'timings_recurrence' => 'Sent immediately when learner\'s competency points reach the required threshold',
			'developer_notes' => 'This template is triggered in LearningResult model when a user\'s competency points reach or exceed the required_points threshold for the first time. The system tracks when competencies are acquired (acquired_at timestamp) and only sends this email once per competency. Competencies are awarded based on accumulated points from completed learning activities. Template includes the competency name via COMPETENCY variable.'
		];

		$templates[] = [
			'name' => "Booking Undersubscribed",
			'subject' => "Booking Undersubscribed - %%BOOKING_SESSION%%",
			'slug' => 'booking_undersubscribed',
			'body' => '
			<p>Dear %%USER_FNAME%% %%USER_LNAME%%</p><br/>
			<p>Unfortunately the "%%BOOKING_SESSION%%" session on %%EVENT_DATE%% is undersubscribed. We suggest that you cancel this or arrange an alternative time.</p>
			<p>Please log in to edit the session else the session will go ahead.</p>
				<br>
				<p>%%REGARDS%%</p>
			',
      'site_versions' => '',
      'force_update' => false,
			'system_trigger' => 'Scheduled event passes booking deadline with no learners registered',
			'recipients' => 'Event managers/owners responsible for the undersubscribed session',
			'timings_recurrence' => 'Sent when booking deadline passes for events with zero learners',
			'developer_notes' => 'This template is triggered by Schedule::notifyWaitingUsersEventDeadlineAtPassed() cron task when a scheduled event passes its booking deadline (deadline_at) with no registered learners (users->count() <= 0). The system notifies the event managers to take action - either cancel the session or arrange an alternative time. Template includes session name and event date for context.'
		];

		$templates[] = [
			'name' => "Event Cancellation",
			'subject' => "Cancellation of %%EVENT_NAME%%",
			'slug' => 'event_cancellation',
			'body' => '
			<p>Dear %%USER_FNAME%% %%USER_LNAME%%</p><br/>
			<p>Unfortunately we have had to cancel the %%EVENT_NAME%% that you are booked on, sorry for the inconvenience.</p>
			<p>Please review your schedule and rebook.</p>
				<br>
				<p>%%REGARDS%%</p>
			',
			'site_versions' => '',
			'system_trigger' => 'Administrator cancels a scheduled event that has learners assigned',
			'recipients' => 'Learners who were assigned to the cancelled event',
			'timings_recurrence' => 'Sent immediately when event is cancelled, only for future events',
			'developer_notes' => 'This template is triggered in Schedule model when an event is cancelled and has assigned users. Only sends for future events (not past events). The EmailQueue model handles deduplication by removing unsent schedule_created/schedule_meeting_created emails for cancelled events and managing cancellation email recipients. Also removes associated user forms when events are cancelled.'
		];

		$templates[] = [
			'name' => "Enrollment Closed Alert",
			'subject' => 'Important Update: Enrollment for "%%EVENT_NAME%%" Has Closed',
			'slug' => 'enrolment_closed_alert',
			'body' => '
			<p>Dear %%USER_FNAME%% %%USER_LNAME%%</p><br/>
			<p>"%%EVENT_NAME%%" has ended, you remained on the waiting list.  We recommend you book another upcoming event that covers this learning.</p>
			<br>
			<p>%%REGARDS%%</p>
			',
			'site_versions' => '',
			'status' => false,
			'system_trigger' => 'Scheduled event passes booking deadline with learners still on waiting list',
			'recipients' => 'Learners who remained on the waiting list after enrollment deadline',
			'timings_recurrence' => 'Sent when booking deadline passes for events with waiting list users',
			'developer_notes' => 'This template is triggered by Schedule::notifyWaitingUsersEventDeadlineAtPassed() cron task (runs every 30 minutes) when a scheduled event passes its booking deadline (deadline_at) and there are still users on the waiting list. The system notifies these waiting users that they missed the enrollment window and should book another event. The template includes user name, event name, and regards message. The cron task uses the is_deadline_passed_alert_waiting_users flag to prevent duplicate notifications.'
		];

		$templates[] = [
			'name' => "Transaction Data",
			'subject' => "Pay360 Transaction Data",
			'slug' => 'civica_data',
			'body' => '
				<p>Hello,</p>
				<br>
				<p>This is the raw data of all payments through the Open eLMS system for auditing purposes.</p>
				<br>

			',
            'site_versions' => '',
            'force_update' => true,
			'system_trigger' => 'Scheduled export of payment transaction data for auditing purposes',
			'recipients' => 'Financial managers designated to receive payment audit reports',
			'timings_recurrence' => 'Sent every 6 days (8640 minutes) when new payment transactions exist',
			'developer_notes' => 'This template is triggered by UserPaymentTransaction::sendCivicaDataMail() via the sendCivicaData cron task (runs every 6 days). It exports all unsent payment transactions to a CSV file and emails it to the manager specified in CivicaManagerEmail configuration. The system filters out test transactions and previously sent records using the is_sent flag. The CSV includes comprehensive transaction details including user info, payment amounts, gateway data, and item details. Configuration flags allowSendCivicaData and CivicaManagerEmail must be set for this to function.'
		];

		$templates[] = [
			'name' => "Event Cancellation By Learner",
			'subject' => "%%EVENT_NAME%% Cancellation of Attendance",
			'body' => '
				<p>%%FIRSTNAME%% %%SURNAME%% has cancelled their attendance to this event for the following reason:</p>
				<p>%%CANCELLATION_MESSAGE%%</p>
			',
			'site_versions' => '',
			'force_update' => false,
			'system_trigger' => 'Learner cancels their attendance at a scheduled event',
			'recipients' => 'Event managers and owners responsible for the event',
			'timings_recurrence' => 'Sent immediately when learner cancels attendance',
			'developer_notes' => 'This template is triggered in the schedule/link/delete route when a learner cancels their attendance at an event. The system checks if the cancellation type is "cancelled_by_user" and the user was approved (approved == 1). Event managers linked to the schedule receive notification with the learner\'s name, event details, and the cancellation reason provided. The template includes firstname, surname, event name, cancellation message, and event date variables.'
		];

		$templates[] = [
			'name' => "Event Approval Request",
			'subject' => "Event Request",
			'body' => '
				<p>Dear %%USER_FNAME%% %%USER_LNAME%%,</p>
				<p>The follower learner requires approval to attend a training event.</p><br/>

				<p>Learner: %%TRAINEE_FNAME%% %%TRAINEE_LNAME%%</p>
				<p>Event: %%EVENT_NAME%%</p>
				<p>Location: %%EVENT_LOCATION%%</p>
				<p>Date: %%EVENT_DATE%%</p>
				<p>click <a href="%%CONFIG_LMSUrl%%%%MANAGER_APPROVAL_LINK%%">here</a> to approve</p>
				<p>%%REGARDS%%</p>
			',
			'site_versions' => '"apprentix" "openelmsschools" "openelmscolleges" "openelmsuniversities" "openelmsbusiness"',
			'force_update' => false,
			'system_trigger' => 'Learner enrolls in event that requires manager approval',
			'recipients' => 'Managers of the learner requesting event approval',
			'timings_recurrence' => 'Sent immediately when learner enrolls in approval-required event',
			'developer_notes' => 'This template is triggered in the schedule/link/new route when a learner enrolls in an event that requires approval (event.enrole_any_learner == 1 && event.approval == 1 && Auth::isLearner()). The system sends approval requests to all managers of the learner using the ManagerUser relationship. Each manager receives a personalized email with event details and a link to the approval interface. The template includes learner name, event details, location, date, and a direct link to the manager approval page.'
		];

		$templates[] = [
			'name' => "Event Approval",
			'subject' => "Event Approval Confirmation",
			'body' => '
				<p>Dear %%USER_FNAME%%,</p>
				<p>Your event request has been approved by your manager for the %%EVENT_NAME%% on the %%EVENT_DATE%% at %%EVENT_LOCATION%%</p>
				<p>Please click <a href="%%CONFIG_LMSUrl%%%%EVENT_APPROVAL_LINK%%">here</a> to follow next steps (if this event requires payment then you can use the link and follow the process for payment).</p><br/>

			   <p>%%REGARDS%%</p>
			',
			'site_versions' => '"apprentix" "openelmsschools" "openelmscolleges" "openelmsuniversities" "openelmsbusiness"',
			'force_update' => false,
			'system_trigger' => 'Manager approves learner\'s event attendance request',
			'recipients' => 'Learner who requested event approval',
			'timings_recurrence' => 'Sent immediately when manager approves event request',
			'developer_notes' => 'This template is triggered in the mylearning/scheduleapproval PUT route when a manager approves a learner\'s event attendance request (event.approval == 1 && schedulelink.type == users). The learner receives confirmation of approval with event details and next steps. If payment is required, the email includes a link to the payment process. The template includes learner name, event name, date, location, and direct link to learning resources.'
		];

		$templates[] = [
			'name' => "Event Not Approved",
			'subject' => "Your event request has not been approved",
			'body' => '
				<p>Dear %%USER_FNAME%% %%USER_LNAME%%,</p>
				<p>Unfortunately you have not been granted approval to access the following learning event at this time:</p><br/>

				<p>Learning: %%EVENT_NAME%%</p>
				<p>Location: %%EVENT_LOCATION%%</p>
				<p>Date: %%EVENT_DATE%%</p>
				<p>Reject Reason: %%REJECT_REASON%%</p>
				<p>Please contact your line manager for further information.</p>
				<p>%%REGARDS%%</p>
			',
			'site_versions' => '"apprentix" "openelmsschools" "openelmscolleges" "openelmsuniversities" "openelmsbusiness"',
			'force_update' => false,
			'system_trigger' => 'Manager removes/rejects learner\'s event attendance request',
			'recipients' => 'Learner whose event request was rejected',
			'timings_recurrence' => 'Sent immediately when manager removes unapproved learner from event',
			'developer_notes' => 'This template is triggered in the schedule/link/delete route when a manager removes a learner from an event who was not yet approved (link.approved == 0). The system only sends this notification when the action is performed by a manager (not by the learner themselves). The learner receives notification of rejection with event details and optional rejection reason. The template includes learner name, event name, location, date, rejection reason, and venue information.'
		];

		$templates[] = [
			'name' => "Event Waiting List",
			'subject' => "Event Waiting List",
			'body' => '
				<p>Dear %%USER_FNAME%% %%USER_LNAME%%,</p>
				<p>The following event is currently fully booked and you have been placed on the waiting list - you will be informed if a space becomes available:</p><br/>

				<p>Learning: %%EVENT_NAME%%</p>
				<p>Location: %%EVENT_LOCATION%%</p>
				<p>Date: %%EVENT_DATE%%</p>
				<p>Please contact your line manager for further information.</p>
				<p>%%REGARDS%%</p>
			',
			'site_versions' => '"apprentix" "openelmsschools" "openelmscolleges" "openelmsuniversities" "openelmsbusiness"',
			'force_update' => false,
			'system_trigger' => 'Manager approves learner\'s event request but event is at capacity',
			'recipients' => 'Learner who was placed on waiting list',
			'timings_recurrence' => 'Sent immediately when manager approves request for full event',
			'developer_notes' => 'This template is triggered in the mylearning/scheduleapproval PUT route when a manager approves a learner\'s event request but the learner is assigned to the waiting list (event.approval == 1 && schedulelink.type == users_queue). This happens when the event is at capacity. The learner receives notification that they are approved but on the waiting list until space becomes available. The template includes learner name, event details, and venue information.'
		];

		$templates[] = [
			'name' => "Resource Approval Request",
			'subject' => "Resource Request",
			'body' => '
				<p>Dear %%USER_FNAME%% %%USER_LNAME%%,</p>
				<p>The follower learner requires approval to access learning resource.</p><br/>

				<p>Learner:%%TRAINEE_FNAME%% %%TRAINEE_LNAME%%</p>
				<p>Event: %%RESOURCE_NAME%%</p>
				<p>click <a href="%%CONFIG_LMSUrl%%%%MANAGER_APPROVAL_LINK%%">here</a> to approve</p>
				<p>%%REGARDS%%</p>
			',
			'site_versions' => '"apprentix" "openelmsschools" "openelmscolleges" "openelmsuniversities" "openelmsbusiness"',
			'force_update' => false,
			'system_trigger' => 'Learner enrolls in learning resource that requires manager approval',
			'recipients' => 'Managers of the learner requesting resource approval',
			'timings_recurrence' => 'Sent immediately when learner enrolls in approval-required resource',
			'developer_notes' => 'This template is triggered in the myenroll/add PUT route when a learner enrolls in a learning resource that requires approval (learning_module.approval == true). The system sends approval requests to all managers of the learner using the ManagerUser relationship. Each manager receives notification with learner details, resource name, and a link to the approval interface. The learning result is marked as not approved until manager approval is granted.'
		];

		$templates[] = [
			'name' => "Resource Approval",
			'subject' => "Resource Approval Confirmation",
			'body' => '
				<p>Dear %%USER_FNAME%%,</p>
				<p>Your resource request has been approved by your manager for the %%RESOURCE_NAME%%</p>
				<p>Please click <a href="%%CONFIG_LMSUrl%%%%RESOURCE_APPROVAL_LINK%%">here</a> to follow next steps (if this resource requires payment then you can use the link and follow the process for payment).</p><br/>

			   <p>%%REGARDS%%</p>
			',
			'site_versions' => '"apprentix" "openelmsschools" "openelmscolleges" "openelmsuniversities" "openelmsbusiness"',
			'force_update' => false,
			'system_trigger' => 'Manager approves learner\'s learning resource access request',
			'recipients' => 'Learner who requested resource approval',
			'timings_recurrence' => 'Sent immediately when manager approves resource request',
			'developer_notes' => 'This template is triggered in the learning/{module_id}/{user_id}/{id}/approve PUT route when a manager approves a learner\'s learning resource access request (learning_module.approval == true). The learner receives confirmation of approval with resource details and next steps. If payment is required, the email includes a link to the payment process. The learning result is marked as approved and linked resources are automatically assigned if it\'s a course.'
		];

		$templates[] = [
			'name' => "Resource Not Approved",
			'subject' => "Your resource request has not been approved",
			'body' => '
				<p>Dear %%USER_FNAME%% %%USER_LNAME%%,</p>
				<p>Unfortunately you have not been granted approval to access the following learning event at this time:</p><br/>

				<p>Learning: %%RESOURCE_NAME%%</p>
				<p>Reject Reason: %%REJECT_REASON%%</p>
				<p>Please contact your line manager for further information.</p>
				<p>%%REGARDS%%</p>
			',
			'site_versions' => '"apprentix" "openelmsschools" "openelmscolleges" "openelmsuniversities" "openelmsbusiness"',
			'force_update' => false,
			'system_trigger' => 'Manager rejects learner\'s learning resource access request',
			'recipients' => 'Learner whose resource request was rejected',
			'timings_recurrence' => 'Sent immediately when manager rejects resource request',
			'developer_notes' => 'This template is triggered in the learning/{module_id}/{user_id}/{id}/enrol/reject PUT route when a manager rejects a learner\'s learning resource access request (learning_module.approval == 1). The learner receives notification of rejection with resource details and optional rejection reason. The learning result is marked as rejected and resources are unlinked from the learner. The system also stores rejection reasons via RejectRequestItems model for audit purposes.'
		];

		$templates[] = [
			'name' => "Confirmation of purchase Learner",
			'subject' => "Confirmation of Purchase - %%TYPE%%",
			'slug' => 'confirmation_of_purchase_learner',
			'body' => '
			<p>Dear %%USER_FNAME%%,</p>
			<p>The following is a receipt for the %%LEARNING_NAME%% purchased on %%TODAYSDATE%%.</p>
			<p>Fee paid %%FEE%%</p>
			<p>The receipt number is %%RECEIPT_NUMBER%%.</p>
			<p>%%REGARDS%%</p>
			',
			'site_versions' => '',
			'force_update'=>false,
			'system_trigger' => 'Learner successfully completes payment for learning content',
			'recipients' => 'Learner who made the payment',
			'timings_recurrence' => 'Sent immediately upon successful payment completion',
			'developer_notes' => 'This template is triggered in payment controllers (GlobalPaymentController, Payment360Controller, StripeController, GovUKPayController) when a payment transaction is successful (status == "00"). The learner receives a receipt with payment details, learning item name, fee amount, and receipt number. The template supports different types: events, learning modules, and programmes. Custom variables are populated based on the payment type and include event details, dates, locations, and transaction information.'
		];

		$templates[] = [
			'name' => "Confirmation of purchase Manager",
			'subject' => "Confirmation of purchase %%TYPE%%",
			'slug' => 'confirmation_of_purchase_manager',
			'body' => '
				<p>Dear %%USER_FNAME%%,</p>
				<p>The following is a receipt for %%LEARNER_NAME%% purchasing %%LEARNING_NAME%%.</p>
				<p>Fee paid %%FEE%%</p>
				<p>The receipt number is %%RECEIPT_NUMBER%%</p>
				<p>Purchased on %%TODAYSDATE%%</p>
			',
			'site_versions' => '',
			'force_update' => false,
			'system_trigger' => 'Manager completes payment on behalf of learner for learning content',
			'recipients' => 'Manager who made the payment on behalf of learner',
			'timings_recurrence' => 'Sent immediately upon successful payment completion',
			'developer_notes' => 'This template is triggered in payment controllers when a manager makes a payment on behalf of a learner (paid_for !== user_id). The manager receives a receipt showing they paid for the learner\'s learning content. The template includes learner name, learning item details, fee amount, receipt number, and transaction date. This occurs when the paid_for field differs from the user_id in the payment transaction, indicating a manager payment scenario.'
		];

		$templates[] = [
			'name' => "Schedule Custom Mail",
			'subject' => '%%SUBJECT%%',
			'slug' => 'schedule_custom_mail',
			'body' => '
				<p>
					Dear %%USER_FNAME%%
				</p>
				<br>
				<p>%%BODY%%</p>
					<br>
				<p>%%REGARDS%%</p>

			',
			'site_versions' => '',
			'system_trigger' => 'Administrator sends custom email to event participants',
			'recipients' => 'Selected event participants and optionally event managers',
			'timings_recurrence' => 'Sent immediately when administrator composes and sends custom email',
			'developer_notes' => 'This template is triggered in the schedule/send-custom-email POST route when an administrator with custom email permissions sends a personalized message to event participants. The template uses custom subject and body content provided by the administrator. Recipients include selected users and optionally event managers (if copy_manager option is enabled). The system validates that the user has roleAllowSendCustomEventEmail() permission before allowing the email to be sent.'
		];

		$templates[] = [
			'name' => "Actions required for Digital Form",
			'subject' => "Actions required for Digital Form",
			'body' => '
				<p>Dear %%USER_NAME%%,</p>
				<br>
				<p>The following form is now ready for your required information and/or signature:</p>
				<br>
				<p><a href="%%CONFIG_LMSUrl%%%%LINK_TO_FORM%%">Click here</a></p>
				<br>
				<p>Please follow the link to complete the process.</p>
				<br>
				<p>Regards</p>
			',
			'site_versions' => '',
			// 'site_versions' => '"apprentix" "openelmsschools" "openelmscolleges" "openelmsuniversities" "openelmsbusiness" "smcrsolution"',
			'system_trigger' => 'Digital form workflow requires user action or signature',
			'recipients' => 'Users in form signoff workflow requiring action (learners or managers)',
			'timings_recurrence' => 'Sent when form moves to next signoff role in workflow',
			'developer_notes' => 'This template is triggered in UserForm::sentMailToFirstSignOffRole() and UserFormSignoff workflow methods when a digital form requires action from users in the signoff chain. Recipients are determined by form signoff roles, user hierarchy, and permissions. For learners, LINK_TO_FORM is empty; for managers, it links to the management interface. The system handles complex role-based routing including manager relationships and "access all learners" permissions.'
		];

		// If meeting, add with WITH_MANAGER_FULL_NAME and MANAGER_REGARDS, if lesson, do not specify managers.
		$templates[] = [
			'name' => "Event Reminder Advance Notification",
			'subject' => "Event notification",
			'slug' => 'event_reminder_advance_notification',
			'body' => '
				<p>
					Dear %%USER_FNAME%%
				</p>
				<p>
					Please be aware that the following meeting will start at %%EVENT_TIME%% %%EVENT_LOCATION%% %%WITH_MANAGER_FULL_NAME%%.
				</p>
				<p>%%EVENT_DESCRIPTION%%</p>

				%%EVENT_URL%%

				%%ZOOM_LINK%%
				%%TEAMS_LINK%%

				%%MANAGER_REGARDS%%
			',
			'site_versions' => '',
			'force_update' => false,
			'system_trigger' => 'Scheduled event approaching based on advance notification settings',
			'recipients' => 'Learners assigned to upcoming events',
			'timings_recurrence' => 'Sent X days before event start date (configurable via eventReminderAdvanceNotificationInterval)',
			'developer_notes' => 'This template is triggered by Schedule::sendReminderAdvanceNotification() cron method when an event is approaching. The system checks eventReminderAdvanceNotificationInterval configuration to determine how many days before the event to send reminders. Only sends for visible learner events that haven\'t disabled advance notifications (do_not_send_advance_notifications = 0). The template includes event details, time, location, manager information, and meeting links (Zoom/Teams) if configured.'
		];

		$templates[] = [
			'name' => "Event Notification - Advance Warning",
			'subject' => "Advance Warning",
			'slug' => 'event_notification_advance_warning',
			'body' => '
				<p>
					Dear %%USER_FNAME%%
				</p>
				<p>
					Please be aware that the following learning - %%LESSON_NAME%% - will need to be refreshed on the date mentioned below by booking a related event scheduled before %%REFRESHED_EVENT_DATE%%.
				</p>
				<p>The last such event booked was -</p>
				<p>%%EVENT_TIME%%</p>
				<p>%%EVENT_LOCATION%% %%WITH_MANAGER_FULL_NAME%%.</p>
				<p>%%EVENT_DESCRIPTION%%</p>

				%%MANAGER_REGARDS%%
			',
			'site_versions' => '',
			'force_update' => false,
			'system_trigger' => 'Learning event approaching refresh deadline',
			'recipients' => 'Learners whose completed events need refreshing',
			'timings_recurrence' => 'Sent when event completion approaches refresh deadline (once per day)',
			'developer_notes' => 'This template is triggered by ScheduleLink::emailNotifyEventsDueRefresh() method when learning events need to be refreshed by a certain date. The system identifies completed events that require refreshing and notifies learners to book new events before the refresh deadline. The template includes the lesson name, refresh date, last event details, and manager information. The system prevents duplicate notifications by tracking refresh_notify_mail_send_at timestamp.'
		];

		$templates[] = [
			'name' => "Event Assigned from Waiting List",
			'subject' => 'Event Assigned from Waiting List',
			'slug' => 'event_assignment_from_waiting_list',
			'body' => '
				<p>
					Dear %%USER_FNAME%%,
				</p>
				<br>
				<p>
					You have now been assigned a place on the following session:
				</p>
				<br>
				<p>
					<a href="%%CONFIG_LMSUrl%%app/dashboard/manage-learning/manage-learning-resources/lessons-and-learning-resources">%%EVENT_NAME%% on %%EVENT_DATE%% at %%EVENT_TIME%%</a>
				</p>
				<br>
				<p>
					Venue details (if not online):
				</p>
				<br>
				<p>
					%%VENUE%%
				</p>
				<p>
					%%VENUE_ADDRESS%%
				</p>
				<p>
					%%VENUE_POSTCODE%%
				</p>
				<br>
				<p>
				<a href="%%PAYMENT_LINK%%">NB: If this event requires payment, please click here to follow next steps (you can use the link and follow the process for payment).</a>
				</p>
				<br>
				<p>
					Regards
				</p>
				<br>
			',
			'site_versions' => '',
			'variables' => [
				'Formatted Learner interface Event URL' => 'EVENT_URL',
				'Learner interface Event Plain URL' => 'EVENT_PLAIN_URL',
			],
			'system_trigger' => 'Space becomes available in event and waiting list user is assigned',
			'recipients' => 'Learner who was moved from waiting list to confirmed attendance',
			'timings_recurrence' => 'Sent immediately when user is assigned from waiting list',
			'developer_notes' => 'This template is triggered by ScheduleLink::sendEmailToAssignedUsers() method when a user is moved from waiting list to confirmed attendance. The system checks if the event is not in the past and includes venue details, payment links (if required), and event access URLs. For lessons, it provides direct access to learning resources; for other events, it links to the management interface. The template includes comprehensive event details, venue information, and payment processing if the event has a cost.'
		];

		$templates[] = [
			'name' => "Form Assigned to Learner",
			'subject' => "Form Assigned",
			'slug' => 'form_assigned',
			'body' => '
				<p>Dear %%USER_NAME%%,</p>
				<br>
				<p>The following form is now ready for your required information and/or signature:</p>
				<br>
				<p><a href="%%CONFIG_LMSUrl%%"> %%FORM_NAME%% </a></p>
				<br>
				<p>Please follow the link to complete the process.</p>
				<p>You can find the form in your bell icon on your learner page.</p>
				<br>
				<p>Regards</p>
			',
			'site_versions' => '',
			'system_trigger' => 'Digital form is assigned to learner',
			'recipients' => 'Learner who has been assigned the form',
			'timings_recurrence' => 'Sent immediately when form is assigned to learner',
			'developer_notes' => 'This template is triggered by UserForm::sendMailOnFormAssignment() method when a form is assigned to a learner. The system checks if the form should trigger notifications (excludes certain parent forms and specific form types). The template includes the form name and directs learners to access the form through their notification bell icon. The system handles SSO redirects if configured and includes proper authentication flow for form access.'
		];

		$templates[] = [
			'name' => "Payment Success",
			'subject' => "Payment Success",
			'slug' => 'payment_success',
			'body' => "
						<p>Dear %%USER_NAME%%,</p>
			<p>Thank you for your payment. Your transaction was successful, and you are now enrolled in the following product:</p>
			<ul>
			<li><strong>Product Name:</strong> %%PRODUCT_NAME%%</li>
			</ul>
			<p>Your transaction ID is: <strong>%%TRANSACTION_ID%%</strong></p>
			<p>If you have any questions or need further assistance, please don't hesitate to contact us.</p>
				<p>Regards</p>
			",
			'site_versions' => '',
			'system_trigger' => 'Payment transaction completes successfully',
			'recipients' => 'User who made the successful payment',
			'timings_recurrence' => 'Sent immediately upon successful payment processing',
			'developer_notes' => 'This template is used for generic payment success notifications. It provides confirmation of successful payment with transaction details and product enrollment information. The template includes user name, product name, and transaction ID for record-keeping purposes. This is a generic template that can be used across different payment scenarios within the system.'
		];

		$templates[] = [
			'name' => "Payment Failed",
			'subject' => "Payment Failed",
			'slug' => 'payment_failed',
			'body' => "
						<p>Dear %%USER_NAME%%,</p>
			<p>We regret to inform you that your recent payment attempt for the following product has failed:</p>
				<ul>
				<li><strong>Product Name:</strong> %%PRODUCT_NAME%%</li>
				</ul>
				<p>Your transaction ID is: <strong>%%TRANSACTION_ID%%</strong></p>
				<p>Please double-check your payment details and try again. If you continue to experience issues, please contact our support team for assistance.</p>
						<p>Regards</p>
			",
			'site_versions' => '',
			'system_trigger' => 'Payment transaction fails to complete successfully',
			'recipients' => 'User whose payment attempt failed',
			'timings_recurrence' => 'Sent immediately upon payment failure',
			'developer_notes' => 'This template is used for generic payment failure notifications. It informs users that their payment attempt was unsuccessful and provides guidance for resolution. The template includes user name, product name, and transaction ID for troubleshooting purposes. This is a generic template that can be used across different payment gateway failures within the system.'
		];

		$templates[] = [
			'name' => "Internal Server Error",
			'subject' => "Internal Server Error - LMS",
			'slug' => 'internal_server_error',
			'body' => '
				<p>A generic error message indicating an unexpected condition was encountered.:</p>
				<br>
				<p>URL: %%ERROR_URL%%</p>
				<p>IP: %%ERROR_IP%%</p>
				<p>METHOD: %%ERROR_METHOD%%</p>
				<p>CODE: %%ERROR_CODE%%</p>
				<p>TYPE: %%ERROR_TYPE%%</p>
				<p>Message: %%ERROR_MESSAGE%%</p>
				<br>
			',
			'site_versions' => '',
			'system_trigger' => 'System encounters 500-level internal server error',
			'recipients' => 'System administrators configured to receive error notifications',
			'timings_recurrence' => 'Sent immediately when 500 error is logged',
			'developer_notes' => 'This template is triggered by Log::addEntry() method when a 500-level error occurs. The system automatically sends error details to administrators configured in sendInternalErrorToEmails setting. The template includes comprehensive error information including URL, IP address, HTTP method, error code, error type, and error message. This helps administrators quickly identify and resolve system issues.'
		];

		$templates[] = [
			'name' => "Import Users CRON Fail Alert",
			'subject' => "Import User Data Cron Job Failed Alert",
			'slug' => 'import_user_cron_failed_alert',
			'body' => '
				<p>
					Dear %%TO_USER_NAME%%
				</p>
				<p>
					The system was unable to import the following user data:
				</p>
				  <table style="border-collapse: collapse; border: 1px solid #ddd;">
					<tr>
						<th style="border: 1px solid #ddd; padding: 8px;">Employee Code</th>
						<th style="border: 1px solid #ddd; padding: 8px;">First Name</th>
						<th style="border: 1px solid #ddd; padding: 8px;">Last Name</th>
						<th style="border: 1px solid #ddd; padding: 8px;">Role</th>
						<th style="border: 1px solid #ddd; padding: 8px;">Email</th>
					</tr>
					<tr>
						<td style="border: 1px solid #ddd; padding: 8px;">%%USER_EMPLOYEE_CODE%%</td>
						<td style="border: 1px solid #ddd; padding: 8px;">%%USER_FIRST_NAME%%</td>
						<td style="border: 1px solid #ddd; padding: 8px;">%%USER_LAST_NAME%%</td>
						<td style="border: 1px solid #ddd; padding: 8px;">%%USER_ROLE%%</td>
						<td style="border: 1px solid #ddd; padding: 8px;">%%USER_EMAIL%%</td>
					</tr>
				</table>
				<p>Encountered following error :</p>
				<p>%%IMPORT_EXCEPTION_MESSAGE%%</p>
				<br>
				<p>Regards</p>
			',
			'site_versions' => '',
			'force_update' => false,
			'system_trigger' => 'Automated user import cron job encounters errors processing user data',
			'recipients' => 'System administrators responsible for user data management',
			'timings_recurrence' => 'Sent when scheduled user import cron job fails to process user records',
			'developer_notes' => 'This template is designed for cron-based user import failures, particularly for Surrey HR data imports via ImportSurreyHrUsers method called from cronImportHrDataSurrey. However, the actual import functionality appears to use the import_user_error template instead. The template includes variables for user details (employee code, name, role, email) and error messages. The cron job processes HR data imports and logs failures to ImportLog and ImportLogError models.'
		];

		$templates[] = [
			'name' => "Enabled User Notification",
			'subject' => "Your account is reactivated",
			'slug' => 'enabled_user_notification',
			'body' => '
				<p>
					Dear %%USER_FNAME%%
				</p>
				<p>
					 You account has become enabled, please <a href="%%CONFIG_LMSUrl%%">log in</a> to the Learning Management System and check available learning.
				</p>
				<p>
					%%REGARDS%%
				</p>
			',
			'site_versions' => '"apprentix" "openelmsschools" "openelmscolleges" "openelmsuniversities" "openelmsbusiness" "smcrsolution"',
			'force_update' => false,
			'system_trigger' => 'Administrator manually enables disabled user account via user management interface',
			'recipients' => 'User whose account has been enabled',
			'timings_recurrence' => 'Sent immediately when user account is enabled',
			'developer_notes' => 'This template is triggered by User::notifyUserIsEnabled() method called from the enable user route in user.php (/user/enable/{id}). It can also be triggered during user imports when reEnableUsersOnImport config is enabled, or when EnableUserDepartmentOnImport config matches the user\'s department. The template notifies users that their previously disabled account has been reactivated and provides a direct login link.'
		];

		$templates[] = [
			'name' => "Import User Error",
			'subject' => "Unexpected condition was encountered",
			'slug' => 'import_user_error',
			'body' => '
			<p>%%GREEETINGS%%</p><br/>
			<p>Importing users from Excel/CSV some users were not successfully imported. You can review the Import Audit Log for details.</p>
			<p><a href="%%CONFIG_LMSUrl%%app/dashboard/system-setup/system-setup-audit/system-setup-import-logs">Audit Log</a></p>
				<br>
				<p>Regards</p>
			',
			'site_versions' => '',
			'force_update'=>false,
			'system_trigger' => 'User import process encounters errors while processing Excel/CSV files',
			'recipients' => 'System administrators responsible for user imports',
			'timings_recurrence' => 'Sent when user import detects rejected/failed user records',
			'developer_notes' => 'This template is triggered by ImportUserController::import() method when n_users_rejected > 0 after processing user imports. The template is also used in ImportUserController::sendEmail() method for cron import failures. It notifies administrators about failed user imports and provides a direct link to the import audit log for detailed error analysis. The template is sent to admin users or configured cronImportFailureEmail addresses.'
		];

		$templates[] = [
			'name' => "Form Still Awaiting Your Signature",
			'subject' => "Form Still Awaiting Your Signature",
			'slug' => 'form_awaiting_signature_reminder',
			'body' => '
				<p>Dear %%USER_FNAME%% %%USER_LNAME%%</p>
				<br>
				<p>The following form is still awaiting your required information and/or signature:</p>
				<br>
				<p><a href="%%FORM_URL%%">%%FORM_NAME%%</a></p>
				<br>
				<p>Please follow the link to complete the process.</p>
			',
			'site_versions' => '',
			'system_trigger' => 'System sends automated reminders for forms requiring sign-off that have not been completed',
			'recipients' => 'Users and managers responsible for signing off forms based on role hierarchy',
			'timings_recurrence' => 'Sent based on AwaitingSignOffFormReminder configuration (daily, every 3 days, weekly, monthly, or disabled)',
			'developer_notes' => 'This template is triggered by UserForm::sendAwaitingSignOffFormEmailReminder() method called from cron jobs. It sends reminders to users who have forms in USER_FORM_STATUS_AWAITING_SIGN_OFF status. The system respects form sign-off order (has_sign_off_order) and only sends reminders to the next person in line for ordered forms, or to all pending signers for unordered forms. Recipients include both direct users and managers based on role configuration.'
		];

		$templates[] = [
			'name' => "New Programme Assigned",
			'subject' => "New Programme Assigned",
			'slug' => 'new_programme_assigned',
			'body' => '
				<p>Dear %%LEARNER_NAME%%,</p>
				<br>
				<p>The following programme has been assigned to you:</p>
				<p>%%PROGRAMME_NAME%%</p>
				<p>Please <a href="%%CONFIG_LMSUrl%%login">login</a> to access the resources assigned to this programme.</p>
				<p>Regards</p>
				<br>
			',
			'site_versions' => '',
			'system_trigger' => 'Apprenticeship standard or learning programme is assigned to a user',
			'recipients' => 'Learner who has been assigned the programme',
			'timings_recurrence' => 'Sent immediately upon programme assignment',
			'developer_notes' => 'This template is triggered by ApprenticeshipStandardUser model when a new apprenticeship standard is assigned to a user. It provides the learner with programme details and a login link to access their assigned resources. The EmailQueue::deleteUnsentProgramAssignment() method can remove unsent programme assignment emails if the assignment is later removed before the email is processed.'
		];

		$templates[] = [
			'name' => "Multi Attempts at Quiz Notification",
			'subject' => "Multi Attempts at Quiz Notification",
			'slug' => 'multi_attempts_at_quiz_notification',
			'body' => '
				<p>Dear %%user%%,</p>
				<br>
				<p>This is to notify you that you have attempted %%resource_name%% quiz on multiple occasions but not achieve the pass mark.</p>
				<p>We recommend following up with the Administration Team to discuss further actions.</p>
				<p>Thanks</p>
				<br>
			',
			'site_versions' => '',
			'system_trigger' => 'Learner fails quiz multiple times beyond configured maximum attempts threshold',
			'recipients' => 'Learner who has exceeded maximum quiz attempts',
			'timings_recurrence' => 'Sent when quiz failure count reaches or exceeds MaxAttemptsatQuiz configuration',
			'developer_notes' => 'This template is triggered by Learning::multiAttemptsAtQuizNotification() method from cron jobs. It notifies users when their quiz_fails count reaches the MaxAttemptsatQuiz threshold for incomplete and non-passing learning modules. The system tracks last_queue_fail to avoid duplicate notifications and only sends emails when quiz_fails exceeds the last recorded notification count. Only affects active learning modules with track_progress enabled.'
		];

		$templates[] = [
			'name' => "File Download Ready Notification",
			'subject' => "Your Requested File is Ready for Download",
			'slug' => 'file_download_ready_notification',
			'body' => '
				<p>Dear %%USER%%,</p>
				<br>
				<p>Your requested file `%%FILE_NAME%%` from `%%REPORT_NAME%%` has been successfully generated and is now available for download.</p>
				<p>You can access your file by clicking the link below:</p>
				<p><a href="%%DOWNLOAD_LINK%%">Download File</a></p>
				<br>
				<p>If you encounter any issues, please contact support.</p>
				<br>
			',
			'site_versions' => '',
			'system_trigger' => 'Background file generation process completes successfully for user-requested downloads',
			'recipients' => 'User who originally requested the file generation',
			'timings_recurrence' => 'Sent immediately when file generation completes via processUserDownloads cron job',
			'developer_notes' => 'This template is triggered by UserDownload::processAll() method from the processUserDownloads cron job. It notifies users when their requested file downloads (typically Excel/CSV exports from reports) have been successfully generated in the background. The template includes a direct download link with authentication and respects SSO redirection settings. Only sent when file generation succeeds and the user account is still valid.'
		];

		$templates[] = [
			'name' => "Quiz Failed",
			'subject' => "Quiz Failed",
			'slug' => 'quiz_failed',
			'body' => '
				<p>Dear %%USER_FNAME%% %%USER_LNAME%%,</p>
				<p>The following learner has failed a quiz.</p><br/>
				<p>Learner: %%TRAINEE_FNAME%% %%TRAINEE_LNAME%%</p>
				<p>Learning Object: %%RESOURCE_NAME%%</p>
				<p>Attempt Number: %%ATTEMPT_NUMBER%%</p>
				<p>Score: %%SCORE%%</p>
				<p>You may want to review the learner\'s progress and provide assistance if needed.</p>
				<p>%%REGARDS%%</p>
			',
			'site_versions' => '',
			'system_trigger' => 'Learner fails a quiz on mandatory learning module but has not reached maximum attempts',
			'recipients' => 'Managers of the learner who failed the quiz',
			'timings_recurrence' => 'Sent immediately after quiz failure on mandatory modules with course_complete_status = 1',
			'developer_notes' => 'This template is triggered by Course class when a learner fails a quiz with passing_status = "failed" on a mandatory learning module (course_complete_status = 1). It notifies the learner\'s managers (from ManagerUser table) about the failure, providing details like attempt number, score, and resource name. The system differentiates between regular failures and max attempts reached using separate templates.'
		];

		$templates[] = [
			'name' => "Quiz Failed - Max Attempts Reached",
			'subject' => "Quiz Failed - Max Attempts Reached",
			'slug' => 'quiz_failed_max_attempts_reached',
			'body' => '
				<p>Dear %%USER_FNAME%% %%USER_LNAME%%,</p>
				<p>The following learner has failed the quiz and has reached the maximum allowed attempts.</p><br/>
				<p>Learner: %%TRAINEE_FNAME%% %%TRAINEE_LNAME%%</p>
				<p>Learning Object: %%RESOURCE_NAME%%</p>
				<p>Attempt Number: %%ATTEMPT_NUMBER%%</p>
				<p>Score: %%SCORE%%</p>
				<p>Maximum Attempts: %%MAX_ATTEMPTS%%</p>
				<p>The learner will need your intervention to proceed.</p>
				<p>%%REGARDS%%</p>
			',
			'site_versions' => '',
			'system_trigger' => 'Learner fails a quiz on mandatory learning module and has reached maximum allowed attempts',
			'recipients' => 'Managers of the learner who reached maximum quiz attempts',
			'timings_recurrence' => 'Sent immediately when learner reaches maximum quiz attempts on mandatory modules',
			'developer_notes' => 'This template is triggered by Course class when a learner reaches maximum quiz attempts (maxAttemptsReached = true) on a mandatory learning module. It uses the same logic as the regular quiz_failed template but provides additional context about maximum attempts reached. The system may also trigger lesson resets or resource unlinking when reset_failed_quiz is enabled for lessons containing failed resources.'
		];

		$templates[] = [
			'name' => 'Event added you may be interested in',
			'subject' => 'Event added you may be interested in',
			'slug' => 'event_added_you_may_be_interested_in',
			'body' => '
			<p>Dear %%LEARNER_NAME%%,</p>
			<br>
			<p>The following event has been added which is similar to an event you missed.  Please book the event quickly to ensure you do not miss out.</p>
			<a href="%%CONFIG_LMSUrl%%app/learner/resources/%%LESSON_ID%%-%%EVENT_ID%%">
				%%EVENT_NAME%% at %%EVENT_TIME%%
			</a>
			<p>Regards</p>
			<br>
			',
			'site_versions' => '',
			'system_trigger' => 'New event created similar to missed event',
			'recipients' => 'Learner who missed similar event',
			'timings_recurrence' => 'Sent immediately upon event creation',
			'developer_notes' => 'This template is triggered by UserScheduleWaitingList::sendEmail() method when a new event is created for a lesson. It notifies users who are on the waiting list for that lesson (in UserScheduleWaitingList table) and are not already assigned to the new event. The system checks that the event is in the future and excludes users already assigned to the event through ScheduleLink. After sending, it updates notification_sent = 1 and status = 0 for the waiting list entry.'
		];


		$templates[] = [
			'name' => "Confirmation of Purchase - Learner (Multi-item)",
			'subject' => "Confirmation of Purchase",
			'slug' => 'confirmation_of_purchase_learner_cart',
			'body' => '
				<p>Dear %%USER_FNAME%%,</p>
		
				<p>Thank you for your purchase made on %%TODAYSDATE%%.</p>
		
				<p><strong>Receipt Number:</strong> %%RECEIPT_NUMBER%%<br>
				<strong>Purchase Date:</strong> %%TODAYSDATE%%</p>
		
				<p><strong>Items Purchased:</strong></p>
				<ul>
					%%PURCHASE_SUMMARY%%
				</ul>
		
				<p><strong>Total Fee Paid:</strong> %%TOTAL_FEE%%</p>
		
				<p>%%REGARDS%%</p>
			',
			'site_versions' => '',
			'force_update' => false
		];

		$templates[] = [
			'name' => "Confirmation of Purchase - Manager (Multi-item)",
			'subject' => "Purchase Confirmation for %%LEARNER_NAME%%",
			'slug' => 'confirmation_of_purchase_manager_cart',
			'body' => '
				<p>Dear %%USER_FNAME%%,</p>
		
				<p>This is a confirmation for the items purchased on behalf of %%LEARNER_NAME%% on %%TODAYSDATE%%.</p>
		
				<p><strong>Receipt Number:</strong> %%RECEIPT_NUMBER%%<br>
				<strong>Purchase Date:</strong> %%TODAYSDATE%%</p>
		
				<p><strong>Items Purchased:</strong></p>
				<ul>
					%%PURCHASE_SUMMARY%%
				</ul>
		
				<p><strong>Total Fee Paid:</strong> %%TOTAL_FEE%%</p>
		
				<p>%%REGARDS%%</p>
			',
			'site_versions' => '',
			'force_update' => false
		];


		foreach ($templates as $key => $template) {
			// Handle template deletion if delete property is set to true
			if (isset($template['delete']) && $template['delete'] === true) {
				\Models\EmailTemplate::where('name', $template['name'])->delete();
				continue; // Skip processing this template further
			}

			EmailTemplate::where('name', 'Form Needs Attention')->delete();

			$query = \Models\EmailTemplate
				::firstOrCreate( // updateOrCreate, firstOrCreate
					['name' => $template['name']],
					[
						'subject' => $template['subject'],
						'body' => $template['body'],
						'site_versions' => $template['site_versions'] ? $template['site_versions'] : '',
						'slug' => ((isset($template['slug']) ? $template['slug'] : \APP\Tools::safeName($template['subject']))),
						'status' => isset($template['status']) ? $template['status'] : true,
					]
				);
			;
			// Push update if specified in templace configuration, overwrite.
			if (
				isset($template['force_update']) &&
				$template['force_update']
			) {
				$query->subject = $template['subject'];
				$query->body = $template['body'];
				$query->site_versions = $template['site_versions'] ? $template['site_versions'] : '';
				$query->slug = ((isset($template['slug']) ? $template['slug'] : \APP\Tools::safeName($template['subject'])));
				$query->save();
			}

			// If slug does not exists, generate that one!
			if (!$query->slug) {
				$query->slug = ((isset($template['slug']) ? $template['slug'] : \APP\Tools::safeName($template['subject'])));
				$query->save();
			}

            $extractedVariables = self::extractVariablesFromTemplate($template['subject'] . ' ' . $template['body']);

            // Merge predefined variables with extracted variables (predefined takes priority)
            $predefinedVariables = isset($template['variables']) && is_array($template['variables']) ? $template['variables'] : [];
            $mergedVariables = array_merge($extractedVariables, $predefinedVariables);

            $template['variables'] = json_encode($mergedVariables);
            $query->variables = $template['variables'];

            // Populate new fields for SCOR-5559 - UKROeD Amend Help File
            $query->system_trigger = isset($template['system_trigger']) ? $template['system_trigger'] : null;
            $query->recipients = isset($template['recipients']) ? $template['recipients'] : null;
            $query->timings_recurrence = isset($template['timings_recurrence']) ? $template['timings_recurrence'] : null;

            $query->save();


			// Set conditions for e-mail templates to show/hide on specific site versions.
			\Models\EmailTemplate
				::where('name', $template['name'])
				->where('site_versions', '!=', $template['site_versions'])
				->update(['site_versions' => $template['site_versions']]
                )
			;

		}
	}
    private static function extractVariablesFromTemplate($content) {
        $regex = '/%%([A-Z0-9_]+)%%/';
        preg_match_all($regex, $content, $matches);

        $extractedVars = [];
        if (!empty($matches[1])) {
            // Remove duplicates
            $matches[1] = array_unique($matches[1]);

            foreach ($matches[1] as $varName) {
                // Create a human-readable label from the variable name
                $label = str_replace('_', ' ', $varName);
                $label = ucwords(strtolower($label));

                if (strpos($varName, 'CONFIG_') === 0) {
                    $label = 'Config: ' . str_replace('Config: ', '', $label);
                }

                $extractedVars[$label] = $varName;
            }
        }


        return $extractedVars;
    }
}
