<?php

namespace DB;

use APP\Auth;
use Illuminate\Database\Capsule\Manager as DB;
use Models\ApprenticeshipStandardUser;
use Models\Assignment;
use Models\Department;
use Models\ScheduleLink;
use Models\User;
use Models\UserLearningModule;

class ResourceQuery
{
	public static function update()
	{
		$options = [
			"variables" =>
				[
					/*Learning Resource Status*/
					[
						"name" => "Learning Resource Status",
						"variable_key" => "learning_resource_status",
						"key" => "learning_results_completion_status",
						"search" => "learning_results__completion_status",
						"group" => "resources",
						"master_table" => "learning_results",
						"where" => "learning_results.learning_module_id = %group_id% AND learning_results.completion_status %condition% %parameter% AND learning_results.refreshed = 0",
						"select" => "learning_results.user_id",
						"option_group" => "Learning Resource",
						"has_optional_values" => true,
						"has_item" => true,
						"parameter_type" => "custom_defined",
						"parameter_display" => "value",
						"parameter_display" => "value",
						"left_join_on" => "learning_results.user_id = users.id",
						"conditions" =>
							[
								[
									"variable_key" => "learning_resource_status",
									"key" => "Equal",
									"value" => "=",
									"order" => "1",
								],
								[
									"variable_key" => "learning_resource_status",
									"key" => "Not Equal",
									"value" => "!=",
									"order" => "2",
								],
							],
						"parameters" =>
							[
								[
									"variable_key" => "learning_resource_status",
									"key" => "Completed",
									"value" => "'completed'",
									"order" => "1",
								],
								[
									"variable_key" => "learning_resource_status",
									"key" => "Not Started",
									"value" => "'not attempted'",
									"order" => "2",
								],
								[
									"variable_key" => "learning_resource_status",
									"key" => "In Progress",
									"value" => "'in progress'",
									"order" => "3",
								],
							]
					],

					/*Lesson Status*/
					[
						"name" => "Lesson Status",
						"variable_key" => "lesson_status",
						"key" => "learning_results_completion_status",
						"search" => "learning_results__completion_status",
						"group" => "lessons",
						"master_table" => "learning_results",
						"where" => "learning_results.learning_module_id = %group_id% AND learning_results.completion_status %condition% %parameter% AND learning_results.refreshed = 0",
						"select" => "learning_results.user_id",
						"option_group" => "Lessons",
						"has_optional_values" => true,
						"has_item" => true,
						"parameter_type" => "custom_defined",
						"parameter_display" => "value",
						"parameter_display" => "value",
						"left_join_on" => "learning_results.user_id = users.id",
						"conditions" =>
							[
								[
									"variable_key" => "lesson_status",
									"key" => "Equal",
									"value" => "=",
									"order" => "1",

								],
								[
									"variable_key" => "lesson_status",
									"key" => "Not Equal",
									"value" => "!=",
									"order" => "2",
								],
							],
						"parameters" =>
							[
								[
									"variable_key" => "lesson_status",
									"key" => "Completed",
									"value" => "'completed'",
									"order" => "1",
								],
								[
									"variable_key" => "lesson_status",
									"key" => "Not Started",
									"value" => "'not attempted'",
									"order" => "2",
								],
								[
									"variable_key" => "lesson_status",
									"key" => "In Progress",
									"value" => "'in progress'",
									"order" => "3",
								],
							]
					],
					/*Days since registered*/
					[
						"name" => "Days since registered",
						"variable_key" => "days_since_registered",
						"key" => "users",
						"search" => "users__registration_dt",
						"group" => "timing",
						// "master_table" => "users",
						"where" => "users.registration_dt %condition% %parameter%",
						"select" => "users.user_id",
						"option_group" => "Timing",
						"has_optional_values" => false,
						"has_date_add" => true,
						"has_item" => false,
						"parameter_type" => "custom_defined",
						"parameter_display" => "value",
						"parameter_display" => "value",
						"conditions" =>
						[
							[
								"variable_key" => "user_timing",
								"key" => "Equal",
								"value" => "=",
								"order" => "1",
							],
							[
								"variable_key" => "user_timing",
								"key" => "Less than",
								"value" => "<",
								"order" => "2",
							],
							[
								"variable_key" => "user_timing",
								"key" => "Greater than",
								"value" => ">",
								"order" => "3",
							],
							[
								"variable_key" => "user_timing",
								"key" => "Less than or equal to",
								"value" => "<=",
								"order" => "4",
							],
							[
								"variable_key" => "user_timing",
								"key" => "Greater than or equal to",
								"value" => ">=",
								"order" => "5",
							],
						]
					],
					/*Date registered*/
					[
						"name" => "Date registered",
						"variable_key" => "date_registered",
						"key" => "users",
						"search" => "users__registration_dt",
						"group" => "timing",
						// "master_table" => "users",
						"where" => "users.registration_dt %condition% %parameter%",
						"select" => "users.user_id",
						"option_group" => "Timing",
						"has_optional_values" => false,
						"has_date_values" => true,
						"has_item" => false,
						"parameter_type" => "custom_defined",
						"parameter_display" => "value",
						"parameter_display" => "value",
						"conditions" =>
						[
							[
								"variable_key" => "user_timing",
								"key" => "Equal",
								"value" => "=",
								"order" => "1",
							],
							[
								"variable_key" => "user_timing",
								"key" => "Less than",
								"value" => "<",
								"order" => "2",
							],
							[
								"variable_key" => "user_timing",
								"key" => "Greater than",
								"value" => ">",
								"order" => "3",
							],
							[
								"variable_key" => "user_timing",
								"key" => "Less than or equal to",
								"value" => "<=",
								"order" => "4",
							],
							[
								"variable_key" => "user_timing",
								"key" => "Greater than or equal to",
								"value" => ">=",
								"order" => "5",
							],
							[
								"variable_key" => "user_timing",
								"key" => "Between",
								"value" => "BETWEEN",
								"order" => "6",
								"multiple" => true,
							],
							[
								"variable_key" => "user_timing",
								"key" => "Not between",
								"value" => "NOT BETWEEN",
								"order" => "6",
								"multiple" => true,
							],
						]
					],
					/*Learning Resource Number of Days after Completed”*/
					[
						"name" => "Learning Resource Number of Days after Completed",
						"variable_key" => "resource_days_since_completed",
						"key" => "learning_results",
						"search" => "learning_results__completed_at",
						"group" => "resources",
						"master_table" => "learning_results",
						"where" => "learning_results.learning_module_id = %group_id% AND learning_results.completed_at <= NOW() - INTERVAL %parameter% DAY and learning_results.completed_at is not null AND learning_results.refreshed = 0 and learning_results.completion_status = 'completed'",
						"select" => "users.user_id",
						"option_group" => "Timing",
						"has_optional_values" => false,
						"has_item" => true,
						"parameter_type" => "custom_defined",
						"parameter_display" => "value",
						"left_join_on" => "learning_results.user_id = users.id",
						"conditions" =>
						[
							[
								"variable_key" => "user_timing",
								"key" => "Equal",
								"value" => "=",
								"order" => "1",
							],
						]
					],
					/*Lesson Number of Days after Completed”*/
					[
						"name" => "Lesson Number of Days after Completed",
						"variable_key" => "resource_days_since_completed",
						"key" => "learning_results",
						"search" => "learning_results__completed_at",
						"group" => "lessons",
						"master_table" => "learning_results",
						"where" => "learning_results.learning_module_id = %group_id% AND learning_results.completed_at <= NOW() - INTERVAL %parameter% DAY and learning_results.completed_at is not null AND learning_results.refreshed = 0 and learning_results.completion_status = 'completed'",
						"select" => "users.user_id",
						"option_group" => "Timing",
						"has_optional_values" => false,
						"has_item" => true,
						"parameter_type" => "custom_defined",
						"parameter_display" => "value",
						"left_join_on" => "learning_results.user_id = users.id",
						"conditions" =>
						[
							[
								"variable_key" => "user_timing",
								"key" => "Equal",
								"value" => "=",
								"order" => "1",
							],
						]
					],
					/*Programme Status*/
					[
						"name" => "Programme Status",
						"variable_key" => "programme_status",
						"key" => "apprenticeship_standards_users_completion_status",
						"search" => "apprenticeship_standards_users__completion_status",
						"group" => "programmes",
						"master_table" => "apprenticeship_standards_users",
						"where" => "apprenticeship_standards_users.standard_id = %group_id% AND apprenticeship_standards_users.completion_status %condition% %parameter%",
						"select" => "apprenticeship_standards_users.user_id",
						"option_group" => "Programmes",
						"has_optional_values" => true,
						"has_item" => true,
						"parameter_type" => "custom_defined",
						"parameter_display" => "value",
						"parameter_display" => "value",
						"left_join_on" => "apprenticeship_standards_users.user_id = users.id",
						"conditions" =>
							[
								[
									"variable_key" => "programme_status",
									"key" => "Equal",
									"value" => "=",
									"order" => "1",
								],
								[
									"variable_key" => "programme_status",
									"key" => "Not Equal",
									"value" => "!=",
									"order" => "2",
								],
							],
						"parameters" =>
							[
								[
									"variable_key" => "programme_status",
									"key" => "Completed",
									"value" => "'completed'",
									"order" => "1",
								],
								[
									"variable_key" => "programme_status",
									"key" => "Not Started",
									"value" => "'not attempted'",
									"order" => "2",
								],
								[
									"variable_key" => "programme_status",
									"key" => "In Progress",
									"value" => "'in progress'",
									"order" => "3",
								],
							]
					],
					/*Programme Time Completion (%)*/
					[
						"name" => "Programme Time Completion (%)",
						"variable_key" => "programme_time_completion",
						"key" => "apprenticeship_standards_users_percentage_time",
						"search" => "apprenticeship_standards_users__completion_status",
						"group" => "programmes",
						"master_table" => "apprenticeship_standards_users",
						"where" => "apprenticeship_standards_users.standard_id = %group_id% AND apprenticeship_standards_users.percentage_time %condition% %parameter%",
						"select" => "apprenticeship_standards_users.user_id",
						"option_group" => "Programmes",
						"has_optional_values" => false,
						"left_join_on" => "apprenticeship_standards_users.user_id = users.id",
						"conditions" =>
							[
								[
									"variable_key" => "programme_time_completion",
									"key" => "Equal",
									"value" => "=",
									"order" => "1",
								],
								[
									"variable_key" => "programme_time_completion",
									"key" => "Less than",
									"value" => "<",
									"order" => "2",
								],
								[
									"variable_key" => "programme_time_completion",
									"key" => "Greater than",
									"value" => ">",
									"order" => "3",
								],
								[
									"variable_key" => "programme_time_completion",
									"key" => "Less than or equal to",
									"value" => "<=",
									"order" => "4",
								],
								[
									"variable_key" => "programme_time_completion",
									"key" => "Greater than or equal to",
									"value" => ">=",
									"order" => "5",
								],
								[
									"variable_key" => "programme_time_completion",
									"key" => "Between",
									"value" => "BETWEEN",
									"order" => "6",
									"multiple" => true,
								],
								[
									"variable_key" => "programme_time_completion",
									"key" => "Not between",
									"value" => "NOT BETWEEN",
									"order" => "6",
									"multiple" => true,
								],
							],

					],
					/*Programme Resources Completion (%)*/
					[
						"name" => "Programme Resources Completion (%)",
						"variable_key" => "programme_resources_completion",
						"key" => "apprenticeship_standards_users_percentage",
						"search" => "apprenticeship_standards_users__percentage",
						"group" => "programmes",
						"master_table" => "apprenticeship_standards_users",
						"where" => "apprenticeship_standards_users.standard_id = %group_id% AND apprenticeship_standards_users.percentage %condition% %parameter%",
						"select" => "apprenticeship_standards_users.user_id",
						"option_group" => "Programmes",
						"has_optional_values" => false,
						"left_join_on" => "apprenticeship_standards_users.user_id = users.id",
						"conditions" =>
							[
								[
									"variable_key" => "programme_resources_completion",
									"key" => "Equal",
									"value" => "=",
									"order" => "1",
								],
								[
									"variable_key" => "programme_resources_completion",
									"key" => "Less than",
									"value" => "<",
									"order" => "2",
								],
								[
									"variable_key" => "programme_resources_completion",
									"key" => "Greater than",
									"value" => ">",
									"order" => "3",
								],
								[
									"variable_key" => "programme_resources_completion",
									"key" => "Less than or equal to",
									"value" => "<=",
									"order" => "4",
								],
								[
									"variable_key" => "programme_resources_completion",
									"key" => "Greater than or equal to",
									"value" => ">=",
									"order" => "5",
								],
								[
									"variable_key" => "programme_resources_completion",
									"key" => "Between",
									"value" => "BETWEEN ",
									"order" => "6",
									"multiple" => true,
								],
								[
									"variable_key" => "programme_resources_completion",
									"key" => "Not between",
									"value" => "NOT BETWEEN",
									"order" => "6",
									"multiple" => true,
								],
							],

					],
					/*CPD Points*/
					[
						"name" => "CPD Points",
						"variable_key" => "cpd_points",
						"key" => "user_competencies_points",
						"search" => "user_competencies__points",
						"group" => "competency",
						"master_table" => "user_competencies",
						"where" => "user_competencies.competency_id = %group_id% AND user_competencies.points %condition% %parameter%",
						"select" => "user_competencies.user_id",
						"left_join" => "user_competencies",
						"option_group" => "Competencies",
						"has_optional_values" => false,
						"left_join_on" => "user_competencies.user_id = users.id",
						"conditions" =>
							[
								[
									"variable_key" => "programme_time_completion",
									"key" => "Equal",
									"value" => "=",
									"order" => "2",
								],
								[
									"variable_key" => "programme_time_completion",
									"key" => "Less than",
									"value" => "<",
									"order" => "2",
								],
								[
									"variable_key" => "programme_time_completion",
									"key" => "Greater than",
									"value" => ">",
									"order" => "3",
								],
								[
									"variable_key" => "programme_time_completion",
									"key" => "Less than or equal to",
									"value" => "<=",
									"order" => "4",
								],
								[
									"variable_key" => "programme_time_completion",
									"key" => "Greater than or equal to",
									"value" => ">=",
									"order" => "5",
								],
								[
									"variable_key" => "programme_time_completion",
									"key" => "Between",
									"value" => "BETWEEN ",
									"order" => "6",
									"multiple" => true,
								],
								[
									"variable_key" => "programme_time_completion",
									"key" => "Not between",
									"value" => "NOT BETWEEN",
									"order" => "6",
									"multiple" => true,
								],
							],

					],
				   /*Country*/
					[
						"name" => "%%country%%",
						"variable_key" => "country",
						"key" => "users",
						"search" => "users__country_id",
						"group" => "organisation",
						"where" => "users.country_id %condition% %parameter%",
						"select" => "users.country_id",
						"option_group" => "Organisation",
						"has_optional_values" => true,
						"has_item" => false,
						"parameter_type" => "countries",
						"parameter_display" => "key",
						"conditions" =>
							[
								[
									"variable_key" => "country_id",
									"key" => "Equal",
									"value" => "=",
									"order" => "1",
								],
								[
									"variable_key" => "country_id",
									"key" => "Not Equal",
									"value" => "!=",
									"order" => "2",
								],
							],

					],
					/*City*/
					[
						"name" => "%%city%%",
						"variable_key" => "city",
						"key" => "users",
						"search" => "users__city_id",
						"group" => "organisation",
//                        "master_table" => "",
						"where" => "users.city_id %condition% %parameter%",
						"select" => "users.city_id",
						"option_group" => "Organisation",
						"has_optional_values" => true,
						"has_item" => false,
						"parameter_type" => "cities",
						"parameter_display" => "key",
						"left_join_on" => "",

						"conditions" =>
							[
								[
									"variable_key" => "city_id",
									"key" => "Equal",
									"value" => "=",
									"order" => "1",
								],
								[
									"variable_key" => "city_id",
									"key" => "Not Equal",
									"value" => "!=",
									"order" => "2",
								],
							],

					],
					/*Company*/
					[
						"name" => "%%company%%",
						"variable_key" => "company",
						"key" => "users",
						"search" => "users__company_id",
						"group" => "organisation",
						"where" => "users.company_id %condition% %parameter%",
						"select" => "users.company_id",
						"option_group" => "Organisation",
						"has_optional_values" => true,
						"has_item" => false,
						"parameter_type" => "companies",
						"parameter_display" => "key",
						"conditions" =>
							[
								[
									"variable_key" => "company_id",
									"key" => "Equal",
									"value" => "=",
									"order" => "1",
								],
								[
									"variable_key" => "company_id",
									"key" => "Not Equal",
									"value" => "!=",
									"order" => "2",
								],
							],

					],
					/*Department*/
					[
						"name" => "%%department%%",
						"variable_key" => "department",
						"key" => "users",
						"search" => "users__department_id",
						"group" => "organisation",
						"where" => "users.department_id %condition% %parameter%",
						"select" => "users.department_id",
						"option_group" => "Organisation",
						"has_optional_values" => true,
						"has_item" => false,
						"parameter_type" => "departments",
						"parameter_display" => "key",
						"conditions" =>
							[
								[
									"variable_key" => "department_id",
									"key" => "Equal",
									"value" => "=",
									"order" => "1",
								],
								[
									"variable_key" => "department_id",
									"key" => "Not Equal",
									"value" => "!=",
									"order" => "2",
								],
							],

					],
					/*Job*/
					[
						"name" => "%%job%%",
						"variable_key" => "designation",
						"key" => "users",
						"search" => "users__designation_id",
						"group" => "organisation",
						"where" => "users.designation_id %condition% %parameter%",
						"select" => "users.designation_id",
						"option_group" => "Organisation",
						"has_optional_values" => true,
						"has_item" => false,
						"parameter_type" => "designations",
						"parameter_display" => "key",
						"conditions" =>
							[
								[
									"variable_key" => "designation_id",
									"key" => "Equal",
									"value" => "=",
									"order" => "1",
								],
								[
									"variable_key" => "designation_id",
									"key" => "Not Equal",
									"value" => "!=",
									"order" => "2",
								],
							],

					],
					/*Location*/
					[
						"name" => "%%location%%",
						"variable_key" => "location",
						"key" => "users",
						"search" => "users__location_id",
						"group" => "organisation",
						"where" => "users.location_id %condition% %parameter%",
						"select" => "users.location_id",
						"option_group" => "Organisation",
						"has_optional_values" => true,
						"has_item" => false,
						"parameter_type" => "locations",
						"parameter_display" => "key",
						"conditions" =>
							[
								[
									"variable_key" => "location_id",
									"key" => "Equal",
									"value" => "=",
									"order" => "1",
								],
								[
									"variable_key" => "location_id",
									"key" => "Not Equal",
									"value" => "!=",
									"order" => "2",
								],
							],

					],
					/*Group*/
					[
						"name" => "%%group%%",
						"variable_key" => "group",
						"key" => "users",
						"search" => "group_users__group_id",
						"group" => "organisation",
						"where" => "group_users.group_id %condition% %parameter%",
						"select" => "group_users.group_id",
						"master_table" => "group_users",
						"option_group" => "Organisation",
						"has_optional_values" => true,
						"has_item" => false,
						"parameter_type" => "groups",
						"parameter_display" => "key",
						"left_join_on" => "group_users.user_id = users.id",
						"conditions" =>
							[
								[
									"variable_key" => "group_id",
									"key" => "Equal",
									"value" => "=",
									"order" => "1",
								],
								[
									"variable_key" => "group_id",
									"key" => "Not Equal",
									"value" => "!=",
									"order" => "2",
								],
							],

					],
					/*Role*/

					[
						"name" => "Role",
						"variable_key" => "role",
						"key" => "users",
						"search" => "users__role_id",
						"group" => "organisation",
						"where" => "users.role_id %condition% %parameter%",
						"select" => "users.role_id",
						"option_group" => "Organisation",
						"has_optional_values" => true,
						"has_item" => false,
						"parameter_type" => "roles",
						"parameter_display" => "key",
						"conditions" =>
							[
								[
									"variable_key" => "role_id",
									"key" => "Equal",
									"value" => "=",
									"order" => "1",
								],
								[
									"variable_key" => "role_id",
									"key" => "Not Equal",
									"value" => "!=",
									"order" => "2",
								],
							],

					],
               		[
                        "name" => "Custom Programme Status",
                        "variable_key" => "custom_programme_status",
                        "key" => "custom_programme_status",
                        "search" => "user_custom_programme_statuses__custom_programme_status_id",
                        "group" => "programmes",
                        "where" => "user_custom_programme_statuses.custom_programme_status_id %condition% %parameter%",
                        "select" => "user_custom_programme_statuses.user_id",
                        "master_table"=>"user_custom_programme_statuses",
                        "left_join_on"=>"user_custom_programme_statuses.user_id = users.id",
                        "option_group" => "Programmes",
                        "has_optional_values" => true,
                        "has_item" => false,
                        "parameter_type" => "custom_programme_statuses",
                        "parameter_display" => "key",
                        "conditions" =>
                            [
                                [
                                    "variable_key" => "custom_programme_status_id",
                                    "key" => "Equal",
                                    "value" => "=",
                                    "order" => "1",
                                ],
                                [
                                    "variable_key" => "custom_programme_status_id",
                                    "key" => "Not Equal",
                                    "value" => "!=",
                                    "order" => "2",
                                ],
                            ],

                    ],
					[
						"name" => "Union",
						"variable_key" => "union",
						"key" => "custom_field_values",
						"search" => "custom_field_values__value",
						"group" => "organisation",
						"where" => "custom_field_values.value %condition% '%parameter%'",
						"select" => "custom_field_values.type_id",
						"option_group" => "Organisation",
						"has_optional_values" => true,
						"has_item" => false,
						"parameter_type" => "union",
						"parameter_display" => "key",
						"left_join_on" =>"custom_field_values.type_id = users.id and type='users'",
						"conditions" =>
							[
								[
									"variable_key" => "union",
									"key" => "Equal",
									"value" => "=",
									"order" => "1",
								],
								[
									"variable_key" => "union",
									"key" => "Not Equal",
									"value" => "!=",
									"order" => "2",
								],
							],

					],
					 [
						"name" => "Job Role",
						"variable_key" => "job_role",
						"key" => "custom_field_values",
						"search" => "custom_field_values__value",
						"group" => "organisation",
						"where" => "custom_field_values.value %condition% '%parameter%'",
						"select" => "custom_field_values.type_id",
						"option_group" => "Organisation",
						"has_optional_values" => true,
						"has_item" => false,
						"parameter_type" => "job_role",
						"parameter_display" => "key",
						"left_join_on" =>"custom_field_values.type_id = users.id and type='users'",
						"conditions" =>
							[
								[
									"variable_key" => "job_role",
									"key" => "Equal",
									"value" => "=",
									"order" => "1",
								],
								[
									"variable_key" => "job_role",
									"key" => "Not Equal",
									"value" => "!=",
									"order" => "2",
								],
							],

					],
                    [
                        "name" => "Form status",
                        "variable_key" => "users_form_status",
                        "key" => "user_forms__user_form_status",
                        "search" => "user_forms__user_form_status",
                        "group" => "forms",
                        "master_table" => "user_forms",
                        "where" => "user_forms.form_id = %group_id% AND user_forms.user_form_status %condition% %parameter%",
                        "select" => "user_forms.user_id",
                        "option_group" => "Form",
                        "has_optional_values" => true,
                        "has_item" => true,
                        "parameter_type" => "custom_defined",
                        "parameter_display" => "value",
                        "left_join_on" => "user_forms.user_id = users.id",
                        "conditions" =>
                            [
                                [
                                    "variable_key" => "user_forms_status",
                                    "key" => "Equal",
                                    "value" => "=",
                                    "order" => "1",
                                ],
                                [
                                    "variable_key" => "user_forms_status",
                                    "key" => "Not Equal",
                                    "value" => "!=",
                                    "order" => "2",
                                ],
                            ],
                        "parameters" =>
                            [
                                [
                                    "variable_key" => "user_forms_status",
                                    "key" => "Completed",
                                    "value" => "'completed'",
                                    "order" => "1",
                                ],
                                [
                                    "variable_key" => "user_forms_status",
                                    "key" => "Not Started",
                                    "value" => "'not attempted'",
                                    "order" => "2",
                                ],
                                [
                                    "variable_key" => "user_forms_status",
                                    "key" => "In Progress",
                                    "value" => "'in progress'",
                                    "order" => "3",
                                ],
                            ]
                    ],
					[
						"name" => "Employee Status",
						"variable_key" => "employee_status",
						"key" => "custom_field_values",
						"search" => "custom_field_values__value",
						"group" => "organisation",
						"where" => "custom_field_values.value %condition% '%parameter%'",
						"select" => "custom_field_values.type_id",
						"option_group" => "Organisation",
						"has_optional_values" => true,
						"has_item" => false,
						"parameter_type" => "employee_status",
						"parameter_display" => "key",
						"left_join_on" =>"custom_field_values.type_id = users.id and type='users'",
						"conditions" =>
							[
								[
									"variable_key" => "employee_status",
									"key" => "Equal",
									"value" => "=",
									"order" => "1",
								],
								[
									"variable_key" => "employee_status",
									"key" => "Not Equal",
									"value" => "!=",
									"order" => "2",
								],
							],

					]

				]

		];


		\Models\ResourceQueryVariable::truncate();
		\Models\ResourceQueryVariableCondition::truncate();
		\Models\ResourceQueryVariableParameter::truncate();

		if (isset($options['variables'])) {
			foreach ($options["variables"] as $key => $option) {
				$query = new \Models\ResourceQueryVariable();
				$query->name = $option['name'];
				$query->description = isset($option['description']) ? $option['description'] : '';
				$query->group = isset($option['group']) ? $option['group'] : '';
				$query->variable_key = isset($option['variable_key']) ? $option['variable_key'] : '';
				$query->master_table = isset($option['master_table']) ? $option['master_table'] : '';
				$query->key = isset($option['key']) ? $option['key'] : '';
				$query->left_join = isset($option['left_join']) ? $option['left_join'] : '';
				$query->left_join_on = isset($option['left_join_on']) ? $option['left_join_on'] : '';
				$query->inner_join = isset($option['inner_join']) ? $option['inner_join'] : '';
				$query->search = isset($option['search']) ? $option['search'] : '';
				$query->join = isset($option['join']) ? $option['join'] : '';
				$query->select = isset($option['select']) ? $option['select'] : '';
				$query->select_raw = isset($option['select_raw']) ? $option['select_raw'] : '';
				$query->group_by = isset($option['group_by']) ? $option['group_by'] : '';
				$query->with = isset($option['with']) ? $option['with'] : '';
				$query->where = isset($option['where']) ? $option['where'] : '';
				$query->where_or = isset($option['where_or']) ? $option['where_or'] : '';
				$query->where_in = isset($option['where_in']) ? $option['where_in'] : '';
				$query->option_group = isset($option['option_group']) ? $option['option_group'] : '';
				$query->option_group_order = isset($option['option_group_order']) ? $option['option_group_order'] : 0;
				$query->order = isset($option['order']) ? $option['order'] : 999;
				$query->parameter_type = isset($option['parameter_type']) ? $option['parameter_type'] : "custom_defined"; // usualy sort is on by default, except you don't want it, then specify to disable it.
				$query->parameter_display = isset($option['parameter_display']) ? $option['parameter_display'] : "value"; // this define whether value or key to be shown in query ui.
				$query->has_item = isset($option['has_item']) ? $option['has_item'] : true; // if there is no item eg{city = 2}. used for straight condition
				$query->sort = isset($option['sort']) ? $option['sort'] : true; // usualy sort is on by default, except you don't want it, then specify to disable it.
				$query->has_optional_values = isset($option['has_optional_values']) ? $option['has_optional_values'] : true;
				$query->has_date_values = isset($option['has_date_values']) ? $option['has_date_values'] : false;
				$query->has_date_add = isset($option['has_date_add']) ? $option['has_date_add'] : false;
				$query->save();

				if (isset($option["conditions"]) && $query->id) {
					foreach ($option["conditions"] as $condition) {
						$resource_conditions = new \Models\ResourceQueryVariableCondition();
						$resource_conditions->resource_query_variable_id = $query->id;
						$resource_conditions->key = isset($condition['key']) ? $condition['key'] : '';
						$resource_conditions->variable_key = isset($condition['variable_key']) ? $condition['variable_key'] : '';
						$resource_conditions->value = isset($condition['value']) ? $condition['value'] : '';
						$resource_conditions->order = isset($condition['order']) ? $condition['order'] : '';
						$resource_conditions->multiple = isset($condition['multiple']) ? $condition['multiple'] : false;
						$resource_conditions->save();
					}
				}
				if (isset($option["parameters"]) && $query->id) {
					foreach ($option["parameters"] as $parameter) {
						$resource_parameter = new \Models\ResourceQueryVariableParameter();
						$resource_parameter->resource_query_variable_id = $query->id;
						$resource_parameter->key = isset($parameter['key']) ? $parameter['key'] : '';
						$resource_parameter->variable_key = isset($parameter['variable_key']) ? $parameter['variable_key'] : '';
						$resource_parameter->value = isset($parameter['value']) ? $parameter['value'] : '';
						$resource_parameter->order = isset($parameter['order']) ? $parameter['order'] : '';
						$resource_parameter->save();
					}
				}

			}
		}
	}

	public static function generateRawQuery($queries, $limit =null, $searchKey=null)
	{
		if (
			!empty($queries) &&
			is_array($queries)
		) {
			// Process special query variables first
			foreach ($queries as $key => &$query) {
				if($query['q_variable']=="Department")
				{
					$deparment= Department::find($query['q_parameter']);
					if($deparment && $deparment->parent_id)
					{
						$query['query_variable_master_table']='user_sub_departments';
						$query['query_variable_left_join_on']='user_sub_departments.user_id =users.id';
						$query['query_variable_where']='user_sub_departments.department_id %condition% %parameter%';
					}
				}
				if($query['q_variable']=='Union' || $query['q_variable']=='Job Role' || $query['q_variable']=='Employee Status'){
					$query['query_variable_master_table']="custom_field_values as custom_".$query['query_variable_variable_key'];
					$query['query_variable_left_join_on'] = "custom_".$query['query_variable_variable_key'].".type_id =  users.id";
					$query['query_variable_where'] = "custom_".$query['query_variable_variable_key'].'.value %condition% "%parameter%"';
				}
			}
			unset($query); // Clean up reference
			
			// Simple check: Do we have OR conditions between different tables?
			$hasOrAcrossTables = false;
			$tablesUsed = [];
			$orFound = false;
			
			foreach ($queries as $key => $query) {
				// Track tables used - be more specific about what constitutes a "table"
				$currentTable = 'users'; // Default
				if (!empty($query['query_variable_master_table'])) {
					$currentTable = $query['query_variable_master_table'];
				} elseif (isset($query['query_variable_where'])) {
					// Check if the WHERE clause references other tables
					if (strpos($query['query_variable_where'], 'learning_results.') !== false) {
						$currentTable = 'learning_results';
					} elseif (strpos($query['query_variable_where'], 'group_users.') !== false) {
						$currentTable = 'group_users';
					}
				}
				$tablesUsed[] = $currentTable;
				
				// Check for OR logic
				if (isset($query['logic']) && strtoupper($query['logic']) === 'OR') {
					$orFound = true;
				}
			}
			
			// Only use UNION if we have OR logic AND genuinely different tables
			$uniqueTables = array_unique($tablesUsed);
			$hasRealJoins = count($uniqueTables) > 1 && !in_array('users', $uniqueTables) || 
							(count($uniqueTables) > 1 && count(array_filter($uniqueTables, function($t) { return $t !== 'users'; })) > 0);
			
			// Check for AND operations between different tables - this disqualifies UNION optimization
			$hasAndAcrossTables = false;
			for ($i = 0; $i < count($queries) - 1; $i++) {
				$currentTable = 'users';
				$nextTable = 'users';
				
				// Determine current query table
				if (!empty($queries[$i]['query_variable_master_table'])) {
					$currentTable = $queries[$i]['query_variable_master_table'];
				} elseif (isset($queries[$i]['query_variable_where'])) {
					if (strpos($queries[$i]['query_variable_where'], 'learning_results.') !== false) {
						$currentTable = 'learning_results';
					} elseif (strpos($queries[$i]['query_variable_where'], 'group_users.') !== false) {
						$currentTable = 'group_users';
					}
				}
				
				// Determine next query table
				if (!empty($queries[$i + 1]['query_variable_master_table'])) {
					$nextTable = $queries[$i + 1]['query_variable_master_table'];
				} elseif (isset($queries[$i + 1]['query_variable_where'])) {
					if (strpos($queries[$i + 1]['query_variable_where'], 'learning_results.') !== false) {
						$nextTable = 'learning_results';
					} elseif (strpos($queries[$i + 1]['query_variable_where'], 'group_users.') !== false) {
						$nextTable = 'group_users';
					}
				}
				
				// Check if AND connects different tables
				$currentLogic = $queries[$i]['logic'] ?? 'OR';
				if (strtoupper($currentLogic) === 'AND' && $currentTable !== $nextTable) {
					$hasAndAcrossTables = true;
					break;
				}
			}
			
			if ($orFound && count($uniqueTables) > 1 && $hasRealJoins && !$hasAndAcrossTables) {
				$hasOrAcrossTables = true;
			}
			
			// Use UNION optimization only for safe, simple cases
			// Avoid UNION for complex mixed AND/OR scenarios that could have semantic differences
			if ($hasOrAcrossTables) {
				// Only use UNION for queries that are:
				// 1. Simple OR-only conditions across different tables
				// 2. Have no complex mixed logic that could be interpreted differently
				
				$hasComplexLogic = false;
				
				// Check for complex logic patterns that might be unsafe for UNION
				for ($i = 0; $i < count($queries) - 1; $i++) {
					$currentLogic = $queries[$i]['logic'] ?? 'OR';
					$nextLogic = isset($queries[$i + 1]['logic']) ? $queries[$i + 1]['logic'] : 'OR';
					
					// If we see AND followed by OR, this suggests operator precedence issues
					if (strtoupper($currentLogic) === 'AND' && strtoupper($nextLogic) === 'OR') {
						$hasComplexLogic = true;
						break;
					}
				}
				
				// Only proceed with UNION if:
				// - No complex AND/OR mixing
				// - Multiple tables involved  
				// - All conditions are simple OR operations
				$allSimpleOr = true;
				foreach ($queries as $i => $q) {
					if ($i > 0) { // Skip first query (no preceding logic)
						$logic = $q['logic'] ?? 'OR';
						if (strtoupper($logic) !== 'OR' && !empty($logic)) {
							$allSimpleOr = false;
							break;
						}
					}
				}
				
				if (!$hasComplexLogic && $allSimpleOr && count($uniqueTables) >= 2) {
					return self::generateSimpleUnionQuery($queries, $limit, $searchKey);
				}
			}
			
			// Otherwise, use the original logic
			// Use appropriate SELECT fields - for resources/add queries, use ID only for performance
			$base_query = "SELECT users.id FROM users";
			$join_table = [];
			$where = "";
			
			foreach ($queries as $key => $query) {
				if ($query['query_variable_master_table'] && !isset($join_table[$query['query_variable_master_table']])) {
					$join_table[$query['query_variable_master_table']] = "LEFT JOIN " . $query['query_variable_master_table'] . " ON " . $query['query_variable_left_join_on'];
				}
				if($query['q_variable']=="Job Role"){
					$join_table['fields as job_role'] ="LEFT JOIN fields as fields_job_role on fields_job_role.id = custom_".$query['query_variable_variable_key'].".field_id and fields_job_role.slug ='job_role'";
				}
				if($query['q_variable']=="Union"){
					$join_table['fields as fields_union'] ="LEFT JOIN fields as  fields_union on fields_union.id = custom_".$query['query_variable_variable_key'].".field_id and fields_union.slug ='union'";
				}
				if($query['q_variable']=="Employee Status"){
					$join_table['fields as fields_employee_status'] ="LEFT JOIN fields as  fields_employee_status on fields_employee_status.id = custom_".$query['query_variable_variable_key'].".field_id and fields_employee_status.slug ='employee_status'";
				}
				
				$wheretmp = str_replace('%group_id%', $query['raw_item_id'] ?? '', $query['query_variable_where']);
				$wheretmp = str_replace('%condition%', $query['condition_value'], $wheretmp);
				$wheretmp = str_replace('%parameter%', $query['parameter_value'], $wheretmp);
				
				if (empty($where)) {
					$where = "($wheretmp)";
				} else {
					$logic = isset($query['logic']) ? $query['logic'] : 'AND';
					$where .= " " . $logic . " ($wheretmp)";
				}
			}
			
			foreach ($join_table as $value) {
				$base_query .= " " . $value . " ";
			}
			
			// select all users with status = 1 here, what all where logic in brackets
			if(!empty($searchKey)){
				$base_query .= " WHERE (users.fname LIKE '%$searchKey%' OR users.fname LIKE '%$searchKey%' OR users.lname LIKE '%$searchKey%' OR users.email LIKE '%$searchKey%' OR users.usercode LIKE '%$searchKey%' OR users.id LIKE '%$searchKey%') AND";
				$base_query .= " users.status = 1 and ($where)  " . "GROUP BY users.id";
			}else{
				$base_query .= " where users.status = 1 and ($where)  " . "GROUP BY users.id";
			}
			
			if(!empty($limit)){
				$base_query .= " LIMIT $limit";
			}
			
			return $base_query;
		}
		
		return "SELECT users.id FROM users WHERE 1=0"; // Return empty result if no queries
	}

	/**
	 * Generate optimized UNION query for OR conditions across different tables
	 */
	private static function generateSimpleUnionQuery($queries, $limit = null, $searchKey = null)
	{
		// Build groups by analyzing the logic chain with proper operator precedence
		// The 'logic' field indicates the operator that follows this condition
		// We need to handle: A OR B AND C OR D -> (A) OR (B AND C) OR (D)
		$groups = [];
		$currentGroup = [$queries[0]]; // Start with first query
		
		for ($i = 0; $i < count($queries) - 1; $i++) {
			$currentQuery = $queries[$i];
			$nextQuery = $queries[$i + 1];
			
			// Check the logic of the CURRENT query to see how it connects to the NEXT
			$currentLogic = $currentQuery['logic'] ?? 'OR'; // Default to OR
			
			if (strtoupper($currentLogic) === 'AND') {
				// AND: add next query to current group
				$currentGroup[] = $nextQuery;
			} else {
				// OR: Check if we need to continue the current group due to upcoming AND
				$shouldContinueGroup = false;
				
				// Look ahead to see if the next query has AND logic
				if (isset($nextQuery['logic']) && strtoupper($nextQuery['logic']) === 'AND') {
					// The pattern is: ... OR B AND C
					// We need to group B AND C together
					$shouldContinueGroup = true;
				}
				
				if ($shouldContinueGroup) {
					// Start new group with next query, but don't close current group yet
					$groups[] = $currentGroup;
					$currentGroup = [$nextQuery];
				} else {
					// Simple OR: finish current group and start new one
					$groups[] = $currentGroup;
					$currentGroup = [$nextQuery];
				}
			}
		}
		
		// Add the final group
		if (!empty($currentGroup)) {
			$groups[] = $currentGroup;
		}
		
		// Build UNION parts for each group
		$unionParts = [];
		foreach ($groups as $group) {
			$unionParts[] = self::buildSingleQueryPart($group, $searchKey);
		}
		
		// Combine all parts with UNION
		if (empty($unionParts)) {
			return "SELECT users.id FROM users WHERE 1=0";
		}
		
		$finalQuery = implode("\nUNION\n", $unionParts);
		
		// Add limit if specified
		if (!empty($limit)) {
			$finalQuery .= " LIMIT $limit";
		}
		
		return $finalQuery;
	}
	
	/**
	 * Build a single query part for UNION
	 */
	private static function buildSingleQueryPart($queries, $searchKey = null)
	{
		// Use ID-only SELECT for consistency with original queries (resources/add pattern)
		$base_query = "SELECT DISTINCT users.id FROM users";
		$join_table = [];
		$where = "";
		
		foreach ($queries as $query) {
			// Process special query variables (same as before)
			if($query['q_variable']=="Department")
			{
				$deparment= Department::find($query['q_parameter']);
				if($deparment && $deparment->parent_id)
				{
					$query['query_variable_master_table']='user_sub_departments';
					$query['query_variable_left_join_on']='user_sub_departments.user_id =users.id';
					$query['query_variable_where']='user_sub_departments.department_id %condition% %parameter%';
				}
			}
			if($query['q_variable']=='Union' || $query['q_variable']=='Job Role' || $query['q_variable']=='Employee Status'){
				$query['query_variable_master_table']="custom_field_values as custom_".$query['query_variable_variable_key'];
				$query['query_variable_left_join_on'] = "custom_".$query['query_variable_variable_key'].".type_id =  users.id";
				$query['query_variable_where'] = "custom_".$query['query_variable_variable_key'].'.value %condition% "%parameter%"';
			}
			
			// Preserve the original JOIN type (LEFT JOIN) to maintain query semantics
			if (isset($query['query_variable_master_table']) && !empty($query['query_variable_master_table']) && !isset($join_table[$query['query_variable_master_table']])) {
				$join_table[$query['query_variable_master_table']] = "LEFT JOIN " . $query['query_variable_master_table'] . " ON " . $query['query_variable_left_join_on'];
			}
			
			// Add special field joins
			if($query['q_variable']=="Job Role" && !isset($join_table['fields as job_role'])){
				$join_table['fields as job_role'] ="INNER JOIN fields as fields_job_role on fields_job_role.id = custom_".$query['query_variable_variable_key'].".field_id and fields_job_role.slug ='job_role'";
			}
			if($query['q_variable']=="Union" && !isset($join_table['fields as fields_union'])){
				$join_table['fields as fields_union'] ="INNER JOIN fields as  fields_union on fields_union.id = custom_".$query['query_variable_variable_key'].".field_id and fields_union.slug ='union'";
			}
			if($query['q_variable']=="Employee Status" && !isset($join_table['fields as fields_employee_status'])){
				$join_table['fields as fields_employee_status'] ="INNER JOIN fields as  fields_employee_status on fields_employee_status.id = custom_".$query['query_variable_variable_key'].".field_id and fields_employee_status.slug ='employee_status'";
			}
			
			// Build where clause
			$wheretmp = str_replace('%group_id%', $query['raw_item_id'] ?? '', $query['query_variable_where']);
			$wheretmp = str_replace('%condition%', $query['condition_value'], $wheretmp);
			$wheretmp = str_replace('%parameter%', $query['parameter_value'], $wheretmp);
			
			if (empty($where)) {
				$where = "($wheretmp)";
			} else {
				// Within a single part, we only use AND
				$where .= " AND ($wheretmp)";
			}
		}
		
		// Add joins
		foreach ($join_table as $value) {
			$base_query .= " " . $value;
		}
		
		// Add where clause
		if(!empty($searchKey)){
			$base_query .= " WHERE (users.fname LIKE '%$searchKey%' OR users.lname LIKE '%$searchKey%' OR users.email LIKE '%$searchKey%' OR users.usercode LIKE '%$searchKey%' OR users.id LIKE '%$searchKey%') AND";
			$base_query .= " users.status = 1 AND ($where)";
		} else {
			$base_query .= " WHERE users.status = 1 AND ($where)";
		}
		
		return $base_query;
	}

	public static function queryItrator($query = null, $settings = [], $return_log = false, $output_log = false) {
		if (!$query) {
			$queries = \Models\ResourceQuery::get();
		} else {
			$queries = \Models\ResourceQuery
				::where('id', "=", $query->id)
				->where('type', '!=', 'events') // Events are not processed here, they are in: \Models\ResourceQuery::proccessUser($settings);
				->get()
			;
		}
		$users = [];
		$user_list = [];
		$data = [];
		$log = "\n queries: " . count($queries);
		if ($output_log) {
			echo $log;
		}

		// Group queries by type for batch processing
		$queryGroups = [];
		foreach ($queries as $queryItem) {
			$key = $queryItem->type . '_' . $queryItem->action;
			if (!isset($queryGroups[$key])) {
				$queryGroups[$key] = [];
			}
			$queryGroups[$key][] = $queryItem;
		}

		if ($output_log) {
			echo "\n Processing " . count($queryGroups) . " query groups";
		}

		foreach ($queryGroups as $groupKey => $groupQueries) {
			if ($output_log) {
				echo "\n Processing group: " . $groupKey . " with " . count($groupQueries) . " queries";
			}

			foreach ($groupQueries as $query) {
				$time_start_query = microtime(true);

				// If event and event is in past (or event not found), do not process query
				if (
					$query->type == 'events' &&
					$query->type_id
				) {
					$event = \Models\Schedule::find($query->type_id);
					if (
						!$event ||
						(
							$event->start_date &&
							\Carbon\Carbon::parse($event->start_date)->lessThan(\Carbon\Carbon::yesterday())
						)
					) {
						continue;
					}
				}

				// Execute query directly - no point caching since query builder runs once per hour
				$users = DB::select($query->raw_query);

				if ($output_log) {
					echo "\n Query with ID: " . $query->id;
					echo "\n Query: " . $query->raw_query;
					echo "\n User count: " . count($users);
				}

				$data['type_id'] = $query->type_id;
				$data['type'] = $query->type;
				$data['type_parent_id'] = $query->type_parent_id;
				if (
					(
						is_array($users) &&
						count($users) > 0
					) ||
					(
						isset($data['type']) &&
						$data['type'] == 'outcome'
					)
				) {

					// update user_ids
					// Convert $users objects to simple array
					$user_ids = array_map(function($obj) {
						return $obj->id;
					}, $users);
					$query->user_ids = implode(",", $user_ids);
					$query->timestamps = false;
					$query->save();

					$data["module_ids"] = [];
					if (isset($data['type_id'])) {
						array_push($data['module_ids'], $data['type_id']);
					}

					if ($query->action === 'add') {
						$user_list =  \DB\ResourceQuery::assignUsers($data, $users, $settings);
					} else if ($query->action === 'remove') {
						$user_list =  \DB\ResourceQuery::removeUsers($data, $users);
					} else if ($query->action === 'disable') {
						$user_list =  \DB\ResourceQuery::disableUsers($data, $users);
					} else if ($query->action === 'enable') {
						$user_list = \DB\ResourceQuery::enableUser($data,$users);
					}

				}
				if($query->type=='outcome' && $query->action === 'enable'){
					$user_list = \DB\ResourceQuery::enableUser($data,$users);
				}
				$time_end_query = microtime(true);
				$time_query = number_format(($time_end_query - $time_start_query), 2);
				if ($output_log) {
					echo "\n took: " . $time_query . " seconds.";
				}
				if ($time_query > 10) {
					// log any queries over 10 seconds
					$log = $log . "\n Query with ID: " . $query->id . " was slow: " . $time_query . " seconds.";
				}
			}
		}
		if ($return_log) {
			return $log;
		} else {
			return $user_list;
		}
	}
	/**************
	 *This function handles the disable of outcome/criteria/subcriteria to users.
	 *
	 */
	public static function enableUser($data, $users) {
		if (
			isset($data['type']) &&
			$data['type']==='outcome'
		) {
			foreach ($users as $user_c) {//
				$user = \Models\User::find($user_c->id);
				\Models\ApprenticeshipStandardUser::disableOutcome($user->id, $data['type_id'], $data['type_parent_id'],  0);
			}
			$users = json_decode(json_encode($users), true);
			$user_ids = array_column($users, 'id');
			$disableOthers = User::whereNotIn('id',$user_ids)->get(); // Updated to use $user_ids
			foreach ($disableOthers as $user) {//
				\Models\ApprenticeshipStandardUser::disableOutcome($user->id, $data['type_id'], $data['type_parent_id'],1);
			}
		}
	}

	/*This function handles the disable of outcome/criteria/subcriteria to users.
	When no parameter is passed it will get all queries and satrt assigning,
	This will manage the calling enviornment */
	public static function disableUsers($data, $users) {
		/*Disable Outcome for specefic users*/
		if (
			isset($data['type']) &&
			($data['type'] == "outcome")
		) {
			foreach ($users as $user_c) {//
				$user= \Models\User::find($user_c->id);
				\Models\ApprenticeshipStandardUser::disableOutcome($user->id, $data['type_id'], $data['type_parent_id'],  1);
			}
		}
		if (
			isset($data['type']) &&
			(
				$data['type'] == "criteria" ||
				$data['type'] == "subcriteria"
			)
		) {
			foreach ($users as $user_c) {//
				$user = \Models\User::find($user_c->id);
				if (
					!$user ||
					$user->status == false
				) {
					continue;
				}
				// Issue only part of standard user is assigned to
				$issue = \Models\ApprenticeshipIssues
					::where('id', $data['type_id'])
					->whereIn('issue_category_id',
						\Models\ApprenticeshipIssueCategories
							::select('id')
							->where('status', true)
							->whereIn('standard_id',
								\Models\ApprenticeshipStandard
									::select('id')
									->whereIn('id',
										\Models\ApprenticeshipStandardUser
											::select('standard_id')
											->where('user_id', $user->id)
											->get()
									)
									->get()
							)
							->get()
					)
					->first()
				;
				if ($issue) {
					$disable_not_allowed =  \Models\ApprenticeshipIssueCategoriesUser
						::where(
							[
								['user_id', $user->id],
								['issue_category_id', $issue->issue_category_id],
								['disable_not_allowed', 1]
							]
						)
						->first()
					;
					if ($disable_not_allowed) {
						continue;
					}
					 // Add entry that will indicate issue is disabled
					$disabled_issue = \Models\ApprenticeshipIssuesUserDisabled::firstOrCreate(
						[
							'apprenticeship_issues_id' => $issue->id,
							'user_id' => $user->id
						]
					);
					// Get modules assigned to issue that is disabled
					$issue_modules = \Models\ApprenticeshipIssuesLearningModules
						::where('apprenticeship_issues_id', '=', $issue->id)
						->get()
					;

					// Collect all modules belonging to standard, except not to disabled issue and other disabled issues
					$preserve_modules = \Models\ApprenticeshipIssuesLearningModules
						::where('apprenticeship_issues_id', '!=', $issue->id)
						->whereIn('apprenticeship_issues_id',
							\Models\ApprenticeshipIssues
								::whereIn('issue_category_id',
									\Models\ApprenticeshipIssueCategories
										::where('standard_id', '=', $data['type_parent_id'])
										->select('id')
										->get()
								)
								->where('status', '=', 1)
								->select('id')
								->get()
						)
						->whereNotIn('apprenticeship_issues_id',
							\Models\ApprenticeshipIssuesUserDisabled
								::where('user_id', '=', $user->id)
								->select('apprenticeship_issues_id')
								->get()
						)
						->get()
					;


					$remove_from_detach_modules = [];
					if ($preserve_modules) {
						foreach ($preserve_modules as $key => $preserve_module) {
							$remove_from_detach_modules[] = $preserve_module->learning_modules_id;
						}
						$remove_from_detach_modules = \APP\Learning::getAllModuleIds($remove_from_detach_modules);
						$remove_from_detach_modules = array_unique($remove_from_detach_modules);
					}


					$detach_modules = [];
					if ($issue_modules) {
						foreach ($issue_modules as $key => $issue_module) {
							$detach_modules[] = $issue_module->learning_modules_id;
						}

						$detach_modules = \APP\Learning::getAllModuleIds($detach_modules);
						$detach_modules = array_unique($detach_modules);

						// remove any modules that are found in other issues and remove them from list
						$detach_modules = array_diff($detach_modules, $remove_from_detach_modules);

						// I have to find out all modules that are attached to other issues in this standard and remove them from "$detach_modules".

						if (count($detach_modules) > 0) {
							\Models\UserLearningModule::unlinkResources($user->id, $detach_modules, 'resource query - remove resources from disabled outcomes/subcriterias');
							// Not exactly sure why this next line is here!
							//$modules_affected = $modules_affected + count($detach_modules);
						}
					}
				}
			}
		}
   }

	/*This function handles the Removing of resources/programmes from users.
	When no parameter is passed it will get all queries and satrt assigning,
	This will manage the calling enviornment */
	public static function removeUsers($data, $users) {
		$user_list = [];

		/*Remove Resources or Lessons*/
		if (
			isset($data['type']) &&
			(
				$data['type'] == "resources" ||
				$data['type'] == "lessons"||
				$data['type'] == "skill_library"
			)
		) {

			$module_ids = [];
			array_push($module_ids, $data['type_id']);
			$module_ids = \APP\Learning::getAllModuleIds($module_ids);

			// Check if resources are enabled
			$module_ids = \Models\LearningModule::returnValidResourcesIDs($module_ids);

			if (!empty($module_ids)) {

				// OPTIMIZATION: Batch process users to handle 20k+ users safely
				$user_ids = array_map(function($user) { return $user->id; }, $users);
				$assigned_lookup = [];
				
				// Process users in batches of 5000 to avoid MySQL IN() limits
				$batch_size = 5000;
				$user_batches = array_chunk($user_ids, $batch_size);
				
				foreach ($user_batches as $batch_user_ids) {
					$batch_assigned_ids = \Models\UserLearningModule
						::whereIn('user_id', $batch_user_ids)
						->where('learning_module_id', $data['type_id'])
						->whereNull('deleted_at')
						->pluck('user_id')
						->toArray()
					;
					
					// Merge into hash map for O(1) lookup
					foreach ($batch_assigned_ids as $user_id) {
						$assigned_lookup[$user_id] = true;
					}
				}

				foreach ($users as $user) {
					// Use hash map for O(1) lookup instead of database query
					if (isset($assigned_lookup[$user->id])) {
						\Models\UserLearningModule::unlinkResources($user->id, $module_ids, 'resource query - remove ' . $data['type']);
						array_push($user_list, $user->id);
					}
				}
			}
		}

		/*Remove Programmes*/
		/* Not sure this exists, ... yet*/

		if (
			isset($data['type']) &&
			$data['type'] == "programmes"
		) {
			$programme = \Models\ApprenticeshipStandard
				::where('id', $data['type_id'])
				->where('status', 1)
				->first()
			;
			if ($programme) {
				foreach ($users as $user) {
					$assigned_programme = \Models\ApprenticeshipStandardUser::where([['user_id', "=", $user->id], ['standard_id', "=", $data['type_id']]])->first();
					if ($assigned_programme) {
						$userObj = \Models\User::returnValidUser($user->id);
						if ($userObj) {
							ApprenticeshipStandardUser::unlinkUsers($assigned_programme);
						}
						array_push($user_list, $userObj);
					}
				}
			}
		}


        /* Remove Distributed Reports*/

        if (isset($data['type']) && ($data['type'] == "reports" )) {

            foreach ($users as $user) {
                $existing_entry = Assignment::where([['source_table_id', $user->id], ['source_table', 'users']], ['link_table', 'document_templates'], ['link_table_id', $data['type_id']])->first();
                $existing_entry->delete();
            }
        }
	}


	/*This function handles the assigning of resources/programmes to users.
	When no parameter is passed it will get all queries and satrt assigning,
	This will manage the calling enviornment */
	public static function assignUsers($data, $users, $settings=[]) {
		$user_list = [];

		/*Assign Resources or Lessons*/
		if (
			isset($data['type']) &&
			(
				$data['type'] == "resources" ||
				$data['type'] == "lessons"
			)
		) {

			$module_ids = isset($data["module_ids"]) ? $data["module_ids"] : [];
			$module_ids = \APP\Learning::getAllModuleIds($module_ids);
			$module_ids = \Models\LearningModule::returnValidResourcesIDs($module_ids);


			if (!empty($module_ids)) {

				// OPTIMIZATION: More efficient remove query logic
				$remove_query_builder = \Models\ResourceQuery
					::where('type', $data['type'])
					->where('type_id', $data['type_id'])
					->where('action', 'remove')
					->first()
				;
				if (
					$remove_query_builder &&
					$remove_query_builder->raw_query
				) {
					$remove_query_users = DB::select($remove_query_builder->raw_query);
					if (count($remove_query_users) > 0) {
						// Create hash map for O(1) lookup instead of O(n²) nested loops
						$remove_user_ids = array_flip(array_map(function($user) { 
							return $user->id; 
						}, $remove_query_users));

						// Filter users efficiently using hash map
						$users = array_filter($users, function($item) use ($remove_user_ids) {
							return !isset($remove_user_ids[$item->id]);
						});
					}
				}


				$RemoveNotApplicableNotCompletedLearning = \APP\Tools::getConfig('RemoveNotApplicableNotCompletedLearning');


				// Will put all applicable users in temporary table that will be used later
				if ($RemoveNotApplicableNotCompletedLearning) {
					DB::schema()->create('temporary_user_ids', function ($table) {
						$table->increments('id');
						$table->integer('user_id')->unsigned()->nullable();
						$table->timestamps();
						$table->temporary();

						$table->index('user_id');
					});
				}

				// OPTIMIZATION: Batch process users to handle 20k+ users safely
				$user_ids = array_map(function($user) { return $user->id; }, $users);
				$assigned_lookup = [];
				
				// Process users in batches of 5000 to avoid MySQL IN() limits
				$batch_size = 5000;
				$user_batches = array_chunk($user_ids, $batch_size);
				
				foreach ($user_batches as $batch_user_ids) {
					$batch_assigned_ids = \Models\UserLearningModule
						::whereIn('user_id', $batch_user_ids)
						->where('learning_module_id', $data['type_id'])
						->whereNull('deleted_at')
						->pluck('user_id')
						->toArray()
					;
					
					// Merge into hash map for O(1) lookup
					foreach ($batch_assigned_ids as $user_id) {
						$assigned_lookup[$user_id] = true;
					}
				}

				foreach ($users as $user) {
					if ($RemoveNotApplicableNotCompletedLearning) {
						DB::table('temporary_user_ids')->insert(['user_id' => $user->id]);
					}

					// Use hash map for O(1) lookup instead of database query
					if (isset($assigned_lookup[$user->id])) { // do not link resource if already assigned
						continue;
					}
					\Models\UserLearningModule::linkResources($user->id, $module_ids, 'query builder, assign ' . $data['type']);
					array_push($user_list, $user->id);

				}

				// Find all assigned learning to these resources that are not completed and NOT users that are part of this query builder, unassign them.
				if ($RemoveNotApplicableNotCompletedLearning) {

					$users_remove = \Models\LearningResult
						::select('learning_results.*')
						->where('refreshed', 0)
						->where('completion_status', '!=', 'completed')
						->whereIn('learning_results.learning_module_id', $module_ids)
						->leftJoin('temporary_user_ids', function($join) {
							$join
								->on('temporary_user_ids.user_id','learning_results.user_id')
							;
						})
						->join("user_learning_modules", function($join) {
							$join
								->on("user_learning_modules.user_id", "=", "learning_results.user_id")
								->on("user_learning_modules.learning_module_id", "=", "learning_results.learning_module_id")
								->whereNull('user_learning_modules.deleted_at')
							;
						})
						->whereNull('temporary_user_ids.user_id')
						->pluck('learning_results.user_id')
						->toArray()
					;
					foreach ($users_remove as $key => $user_remove) {
						// Remove resources ONLY if original lesson/resource is assigned.
						$assigned_resource = \Models\UserLearningModule
							::where([['user_id', "=", $user_remove], ['learning_module_id', "=", $data['type_id']]])
							->first()
						;
						if ($assigned_resource) {
							\Models\UserLearningModule::unlinkResources([$user_remove], $module_ids, 'resource query - Remove Not Applicable Learning in Queries that is not completed ' . $data['type'] . " - " . $data["type_id"]);
						}
					}


					DB::schema()->dropIfExists('temporary_user_ids');
				}

			}

		}

		/*
			lessons_refresh and resources_refresh were meant to allow resources to be refreshed to users who are part of this built query, it is set up in src/classes/APP/Refresh.php

			Also, any user who is not part of this query and have not started resource/lesson, should be unassigned from lesson, resource.

			Inverse query builder, specifically for GTAA
		*/
		if (
			isset($data['type']) &&
			(
				$data['type'] == "lessons_refresh" ||
				$data['type'] == "resources_refresh"
			)
		) {
			$module_ids = isset($data["module_ids"]) ? $data["module_ids"] : [];

			// below logic is to determine if main resource is active and has all the flags to proceed
			$module_ids = \Models\LearningModule
				::validresource()
				->whereIn('learning_modules.id', $module_ids)
				->where('learning_modules.refresh', 1)
				->where('learning_modules.refresh_period', '>', 0)
				->where('learning_modules.refresh_only_if_learning_meets_query', 1)
				->pluck('learning_modules.id')
				->toArray()
			;

			// Convert $users objects to simple array
			$user_ids = array_map(function($obj) {
				return $obj->id;
			}, $users);

			if (is_array($module_ids)) {
				foreach ($module_ids as $key => $module_id) {
					// Find all users who has this resource assigned, but are not part of query user list and resoure status is 'not attempted'
					$users_remove = \Models\User
						::select('users.*')
						->whereNotIn('users.id', $user_ids) // This might cause memory problems, will need to fix this in some way.
						->validuser()
						->join("user_learning_modules", function ($join) use ($module_id) {
							$join
								->on('user_learning_modules.user_id', 'users.id')
								->where('user_learning_modules.learning_module_id', $module_id)
								->whereNull('user_learning_modules.deleted_at')
							;
						})
						->join("learning_results", function ($join) {
							$join
								->on('learning_results.user_id', 'users.id')
								->on('learning_results.learning_module_id', 'user_learning_modules.learning_module_id')
								->where('learning_results.refreshed', 0)
								->where('learning_results.completion_status', 'not attempted')
								->whereNull('learning_results.deleted_at')
							;
						})
						->pluck('users.id')
						->toArray()
					;

					// Get all linked resources if lesson
					$module_ids = \APP\Learning::getAllModuleIds($module_ids);
					\Models\UserLearningModule::unlinkResources($users_remove, $module_ids, 'resource query - remove ' . $data['type'] . " - " . $data["type_id"]);
				}
			}
		}

		/** Assign User Skill Library */
		if (isset($data['type']) && $data['type'] == "skill_library") {

			$module_ids = isset($data["module_ids"]) ? $data["module_ids"] : [];
			$module_ids = \APP\Learning::getLinkedSkillIds($module_ids);
			$module_ids = \Models\LearningModule::returnValidResourcesIDs($module_ids);

			if (!empty($module_ids)) {
				foreach ($users as $user) {
					$userObj = \Models\User::returnValidUser($user->id);
					if (!$userObj) {
						continue;
					}

					$assigned_resource = \Models\UserLearningModule
						::where([['user_id', "=", $userObj->id], ['learning_module_id', "=", $data['type_id']]])
						->first()
					;

					if (!$assigned_resource) {
						$userObj->modules()->syncWithoutDetaching($module_ids);
					}
					array_push($user_list, $user->id);
				}
			}
		}

		/*Assign Programmes*/
		if (isset($data['type']) && ($data['type'] == "programmes" || $data['type'] == "skills_subjects")) {

			$programme = \Models\ApprenticeshipStandard
				::where('id', $data['type_id'])
				->where('status', 1)
				->first()
			;

			if ($programme) {
				foreach ($users as $user) {
					$assigned_programme = \Models\ApprenticeshipStandardUser::where([['user_id', "=", $user->id], ['standard_id', "=", $data['type_id']]])->first();
					if ($assigned_programme) {
						continue;
					}

					$userObj = \Models\User::returnValidUser($user->id);
					$now = \Carbon\Carbon::now();

					if ($userObj) {
						\Models\ApprenticeshipStandardUser::assignToStandard($userObj->id, $data['type_id'], $now);
						array_push($user_list, $userObj);
					}
				}
			}
		}

		/*Assign Forms*/
		if (isset($data['type']) && $data['type'] == "forms") {
			foreach ($users as $user) {
				$assigned_form = \Models\UserForm::where([['user_id', "=", $user->id], ['form_id', "=", $data['type_id']]])->first();
				if ($assigned_form) {
					continue;
				}
				$userObj = \Models\User::returnValidUser($user->id);
				if ($userObj) {
					$passData=['user_id'=>$user->id,
						'form_id'=>$data['type_id'],
						'type'=>'direct',
						'type_id'=>NULL
					];
					\Models\UserForm::assignUserToForms($passData, $settings);
					array_push($user_list, $userObj);
				}
			}
		}

		/*Assign Form Work Flow*/
		if (isset($data['type']) && $data['type'] == "form-workflow") {
			foreach ($users as $user) {

				$userObj = \Models\User::returnValidUser($user->id, ['role']);
				//Only assign learnesr
				if (
					$userObj // &&
					//$userObj->role &&
					//$userObj->role->is_learner=="true"
				) {
					\Models\FormWorkflow::assignUsersToWorkFlow($user->id, $data['type_id']);
					array_push($user_list, $userObj);
				}
			}
		}

        /*Distribute Reports*/

        if (isset($data['type']) && ($data['type'] == "reports" )) {
            foreach ($users as $user) {

$existing_entry = Assignment::where([
    ['source_table_id', $user->id],
    ['source_table', 'users'],
    ['link_table', 'document_templates'],
    ['link_table_id', $data['type_id']]
])->first();

                if (!$existing_entry) {
                    $new_assignment = new Assignment();
                    $new_assignment->source_table = 'users';
                    $new_assignment->source_table_id = $user->id;
                    $new_assignment->link_table = 'document_templates';
                    $new_assignment->link_table_id = $data['type_id'];
                    $new_assignment->created_by = 1;
                    $new_assignment->cron = 1;
                    $new_assignment->save();
                }
            }
        }

		return $user_list;
	}


}
