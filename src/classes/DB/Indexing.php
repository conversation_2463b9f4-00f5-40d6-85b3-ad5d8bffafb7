<?php
namespace DB;

use Illuminate\Database\Capsule\Manager as DB;


class Indexing {
	
	/**
	 * Check for and remove duplicate indexes with different names but same field/order
	 */
	private static function removeDuplicateIndexes($table_name, $new_index_name, $new_index_fields) {
		try {
			// Get all existing indexes for this table
			$existing_indexes = DB::select("SHOW INDEX FROM `$table_name`");
			
			// Group indexes by name and build field signatures
			$index_signatures = [];
			foreach ($existing_indexes as $index) {
				if ($index->Key_name === 'PRIMARY' || $index->Key_name === $new_index_name) {
					continue; // Skip primary keys and the index we're about to create
				}
				
				if (!isset($index_signatures[$index->Key_name])) {
					$index_signatures[$index->Key_name] = [];
				}
				
				$index_signatures[$index->Key_name][] = [
					'column' => $index->Column_name,
					'seq' => $index->Seq_in_index
				];
			}
			
			// Sort columns by sequence for each existing index
			foreach ($index_signatures as $index_name => $columns) {
				usort($index_signatures[$index_name], function($a, $b) {
					return $a['seq'] - $b['seq'];
				});
			}
			
			// Create signature for the new index
			$new_signature = implode('|', $new_index_fields);
			
			// Check for duplicates and remove them
			$duplicates_removed = 0;
			foreach ($index_signatures as $existing_index_name => $columns) {
				$existing_fields = array_map(function($col) { return $col['column']; }, $columns);
				$existing_signature = implode('|', $existing_fields);
				
				if ($existing_signature === $new_signature) {
					echo "    🗑️  Removing duplicate index: `$existing_index_name` (same fields as `$new_index_name`)\n";
					try {
						DB::statement("DROP INDEX `$existing_index_name` ON `$table_name`");
						$duplicates_removed++;
					} catch (\Exception $e) {
						echo "    ⚠️  Failed to drop duplicate index `$existing_index_name`: " . $e->getMessage() . "\n";
					}
				}
			}
			
			if ($duplicates_removed > 0) {
				echo "    ✅ Removed $duplicates_removed duplicate index(es) for `$new_index_name`\n";
			}
			
		} catch (\Exception $e) {
			echo "    ⚠️  Error checking for duplicate indexes on `$table_name`: " . $e->getMessage() . "\n";
		}
	}
	
	/**
	 * Extract field names from CREATE INDEX SQL statement
	 */
	private static function extractFieldsFromSQL($sql) {
		try {
			// Extract field list from SQL like: CREATE INDEX `name` ON `table` (`field1`, `field2`)
			if (preg_match('/\((.*)\)(?:\s*;?)\s*$/', $sql, $matches)) {
				$fields_part = $matches[1];
				// Remove backticks and split by comma
				$fields = explode(',', $fields_part);
				$clean_fields = [];
				
				foreach ($fields as $field) {
					// Remove backticks, quotes, and whitespace
					$clean_field = trim($field, " \t\n\r\0\x0B`'\"");
					// Handle cases like `field`(255) or `field` DESC
					if (preg_match('/^([^(\s]+)/', $clean_field, $field_matches)) {
						$clean_fields[] = $field_matches[1];
					}
				}
				
				return $clean_fields;
			}
		} catch (\Exception $e) {
			echo "    ⚠️  Error extracting fields from SQL: " . $e->getMessage() . "\n";
		}
		
		return [];
	}

	public static function update () {

		// Add some indexing
		$table_indexes = [
			'learning_results' => [
				['processing_manager_id'],
				['completion_date_custom'],
				['grace_at'],
				['due_at'],
				['approved'],
				['sign_off_trainee'],
				['sign_off_manager'],
				['qa'],
				['deleted_at'],
				['refreshed'],
				'idx_learning_results_d_u_l' => ['deleted_at', 'user_id', 'learning_module_id'],
				'idx_learning_results_u_l_r_d' => ['user_id', 'learning_module_id', 'refreshed', 'deleted_at'],
				'idx_learning_results_r_c_c_d' => ['refreshed', 'completion_status', 'completed_at', 'deleted_at'],
				['created_at'],
				['sign_off_trainee_at'],
				'idx_learning_results_u_l_r_d_c' => ['user_id', 'learning_module_id', 'refreshed', 'deleted_at', 'completion_status'],
				['refreshed', 'deleted_at'],
				['user_id', 'learning_module_id'],
				['user_id', 'refreshed'],
				'idx_learning_results_r_d_u_l' => ['refreshed', 'deleted_at', 'user_id', 'learning_module_id'],
				['completed_at'],
				'idx_learning_results_r_d_c' => ['refreshed', 'deleted_at', 'completed_at'],
				'idx_learning_results_r_d_u_l_c' => ['refreshed', 'deleted_at', 'user_id', 'learning_module_id', 'completed_at'],
				['refreshed', 'due_at'],
				// ResourceQuery performance indexes
				'idx_learning_results_resource_query_perf' => ['user_id', 'learning_module_id', 'completion_status', 'refreshed'],
				'idx_learning_results_completion_status' => ['completion_status'],
			],
			'users' => [
				['status'],
				['exclude_from_reports'],
				['company_id'],
				['department_id'],
				['expiration_dt'],
				['id', 'status'],
				['status', 'expiration_dt'],
				'idx_users_i_d_c_s' => ['id', 'department_id', 'country_id', 'status'],
				['status', 'exclude_from_reports'],
				['designation_id'],
				['country_id'],
				['city_id'],
				'idx_users_s_e_c' => ['status', 'exclude_from_reports', 'company_id'],
				// Performance indexes for recipient searches
				['fname', 'lname'],
				['email'],
				// ResourceQuery performance indexes
				'idx_users_resource_query_perf' => ['status', 'company_id', 'department_id'],
			],
			'learning_modules' => [
				['status', 'track_progress'],
				['deleted_at'],
				['id', 'type_id', 'status'],
				['deleted_at', 'category_id'],
				'idx_learning_modules_i_s_t_c' => ['id', 'status', 'type_id', 'category_id'],
				'idx_learning_modules_r_r_s_e' => ['refresh', 'refresh_period', 'status', 'expiration_date'],
				'idx_learning_modules_i_r_s_d' => ['id', 'reset_learning', 'status', 'deleted_at'],
				['evidence_type_id'],
				['refresh'],
				['refresh_period'],
				['status'],
				['guideline'],
				'idx_learning_modules_i_i_o_c_s_t_v_d' => ['id', 'is_course', 'open_in_events_only', 'created_by_event', 'status', 'track_progress', 'visible_learner', 'deleted_at'],
				'idx_learning_modules_i_s_t_v_d' => ['id', 'status', 'track_progress', 'visible_learner','deleted_at',],
				'idx_learning_modules_i_c_g_d_s' => ['id', 'category_id', 'group_department_code_id', 'delivery_provider_type_id', 'status'],
				'idx_learning_modules_i_i_s_e_d' => ['id', 'is_skill', 'status', 'expiration_date', 'deleted_at'],
				['created_by_event'],
				'idx_learning_modules_i_s_c_g_d' => ['id', 'status', 'category_id', 'group_department_code_id', 'delivery_provider_type_id'],
				['id', 'status'],
				['is_course'],
				['is_course', 'status'],
			],
			'user_learning_modules' => [
				//'idx_ulm_deleted_at_user_id_module_id' => ['deleted_at', 'user_id', 'learning_module_id'],
				'idx_ulm_user_module_deleted' => ['user_id', 'learning_module_id', 'deleted_at'],
				['deleted_at'],
			],
			'user_forms' => [
				['user_form_status'],
				['form_id', 'user_id'],
				['form_id'],
				['type'],
				['reference_type_id'],
				['reference_type'],
				['deleted_at'],
				'idx_user_forms_u_f_t_t_r_r' => ['user_id', 'form_id', 'type', 'type_id', 'reference_type_id', 'reference_type'],
			],
			'user_form_signoff' => [
				['user_id', 'user_form_id']
			],
			'assessment_tasks' => [
				['status'],
				'idx_assessment_tasks_user_course_q_a_r' =>['user_id', 'course_id', 'question_id', 'answer_id', 'reporter_id'],
			],
			'assessment_answers' => [
				['answer_id', 'course_id', 'question_id'],
			],
			'assessment_questions' => [
				['question_id', 'course_id'],
			],
			'apprenticeship_standards_users' => [
				'idx_apprenticeship_standards_users_d_u_s' => ['deleted_at', 'user_id', 'standard_id'],
				'idx_apprenticeship_standards_users_d_s_i' => ['deleted_at', 'standard_id', 'user_id'],
				['user_id'],
				'idx_apprenticeship_standards_users_u_s_c_d' => ['user_id', 'standard_id', 'completion_status', 'deleted_at'],
				'idx_apprenticeship_standards_users_u_s' => ['user_id', 'standard_id'],
			],
			'schedule_links' => [
				['link_id'],
				['status'],
				['link_id', 'status', 'type'],
				['type', 'status', 'link_id', 'deleted_at'],
				'idx_schedule_links_s_l_t_d_c' => ['schedule_id', 'link_id', 'type', 'deleted_at', 'completion_status'],
				'idx_schedule_links_t_s_d_s' => ['type', 'status', 'deleted_at', 'schedule_id'],
				['authorisation_notes_by'],
			],
			'schedules' => [
				['category_id'],
				['status'],
				['id', 'status', 'deleted_at'],
				'idx_schedules_i_c_e' => ['id', 'category_id', 'end_date'],
				['type', 'status'],
				'idx_schedules_i_s_c' => ['id', 'status', 'category_id'],
				// ResourceQuery performance indexes
				['start_date'],
				'idx_schedules_resource_query_perf' => ['start_date', 'status', 'deleted_at'],
			],
			'designations' => [
				['status'],
			],
			'quality_controls' => [
				['type'],
				['type_id'],
				['user_id'],
				['qa_user_id'],
				['is_new'],
				['type', 'type_id'],
			],
			'apprenticeship_issue_categories' => [
				['status'],
				['standard_id'],
				'idx_apprenticeship_issue_categories_s_s' => ['status', 'standard_id'],
			],
			'apprenticeship_issues' => [
				['status'],
				['issue_category_id'],
				'idx_apprenticeship_issues_s_i' => ['status', 'issue_category_id'],
			],
			'learning_module_last_access' => [
				['learning_module_id'],
				['user_id'],
				'idx_learning_module_last_access_l_u_c' => ['learning_module_id', 'user_id', 'created_at'],
			],
			'learning_module_types' => [
				['status'],
			],
			'custom_field_values'=>[
				['field_id'],
				['type'],
				['type_id'],
				['user_id'],
				'idx_custom_field_values_t_f_t' => ['type', 'field_id', 'type_id'],
			],
			'apprenticeship_standards' => [
				['status'],
				'idx_apprenticeship_standards_i_c' => ['id', 'category_id'],
				'idx_apprenticeship_standards_i_s_c' => ['id', 'status', 'category_id'],
			],
			'apprenticeship_issues_learning_modules' => [
				'idx_issues_learning_modules_i' => ['apprenticeship_issues_id'],
				'idx_issues_learning_modules_m' => ['learning_modules_id'],
				'idx_issues_learning_modules_i_m' => ['apprenticeship_issues_id', 'learning_modules_id'],
			],
			'apprenticeship_issues_user_learning_modules' => [
				'idx_issues_user_learning_modules_i_l_u' => ['apprenticeship_issues_id', 'learning_modules_id', 'user_id'],
			],
			'apprenticeship_issues_evidence' => [
				'idx_issues_evidence_i_l_u' => ['apprenticeship_issues_id', 'learning_modules_id', 'user_id'],
				['user_id'],
			],
			'email_history' => [
				['resend'],
				['created_at'],
				['user_id_from'],
				['email_to'],
				['converted'],
			],
			'email_queue' => [
				['approved'],
				['frequency_pattern'],
				['send_date'],
				['email_template_id'],
				['approved', 'send_date', 'email_template_id'],
				'idx_email_queue_a_p_d_e' => ['approved', 'processed', 'deleted_at', 'email_template_id'],
				// Performance indexes for search and filtering
				['processed'],
				['from'],
				['created_at'],
				// Note: id DESC and recipients_emails indexes require special handling
				// Use: CREATE INDEX idx_email_queue_recipients_emails ON email_queue (recipients_emails(255))
			],
			'resource_queries' => [
				['type_id'],
				['user_ids'],
				['type'],
				['type_id'],
				['deleted_at'],
				['type_id', 'type', 'deleted_at'],
				// ResourceQuery performance indexes
				'idx_resource_queries_perf' => ['type', 'deleted_at', 'created_at'],
				'idx_resource_queries_active' => ['deleted_at', 'type'],
			],
			'favorites' => [
				['relation_id'],
				['type'],
				['status'],
			],
			'learning_sessions' => [
				['completed'],
				['approved'],
			],
			'apprenticeship_issue_users' => [
				['issue_id'],
				['user_id'],
				['issue_id', 'user_id'],
			],
			'crons' => [
				['status'],
			],
			'roles_access' => [
				['role_id'],
				['access_id'],
				['role_id', 'access_id'],
			],
			'structure' => [
				['key'],
			],
			'role_structure' => [
				['structure_id', 'role_id', 'select'],
			],
			'table_extensions' => [
				['table_id', 'table', 'status'],
				'idx_table_extensions_table_table_id_name' => ['table', 'table_id', 'name'], // Performance: Speed up extension value lookups
			],
			'user_custom_form_values' => [
				['deleted_at'],
				['form_id'],
				['created_at'],
				['updated_at'],
				'idx_user_custom_form_values_f_d' => ['form_id', 'deleted_at'],
				'idx_user_custom_form_values_c_d' => ['created_at', 'deleted_at'],
			],
			'learning_module_versions' => [
				['learning_module_id'],
			],
			'learning_module_categories' => [
				'idx_learning_module_categories_i_s' => ['id', 'status'],
				['status'],
			],
			'table_history' => [
				['table_name'],
				['table_id'],
				['table_id', 'table_name'],
				'idx_table_history_t_c_d' => ['table_id', 'created_at'],
			],
			'role_structure' => [
				['role_id'],
				['structure_id'],
				['role_id', 'structure_id'],
			],
			'learning_module_evidences' => [
				['evidence_type'],
			],
			'ilr_learning_deliveries' => [
				['user_id', 'LearnStartDate'],
			],
			'group_users' => [
				['group_id'],
				['user_id'],
				['group_id', 'user_id'],
				'idx_group_users_g_u_s' => ['group_id', 'user_id', 'status'],
				['status'],
			],
			'picklists' => [
				['type'],
				['status'],
				['public'],
				['type', 'status', 'public',],
			],
			'learning_module_delivery_provider_types' => [
				['status'],
			],
			'learning_module_group_department_codes' => [
				['status'],
			],
			'table_extension_fields' => [
				['status'],
				['versions'],
				['show_learner'],
				['show_administration'],
			],
			'schedule_visit_types' => [
				['status'],
			],
			'evidence_types' => [
				['status'],
			],
			'form_workflow' => [
				['status'],
			],
			'cities' => [
				['status'],
			],
			'roles' => [
				['status'],
			],
			'fields' => [
				['slug'],
				['status'],
				['custom'],
				['status', 'custom'],
				['field_category_id'],
				['isApprentix'],
			],
			'learning_result_answers' => [
				['learning_module_id'],
				['learning_result_id'],
				['user_id'],
				['passing_status'],
			],
			'apprenticeship_standards_user_time_spent' => [
				['type'],
				['type_id'],
				['asu_id'],
				'idx_apprenticeship_standards_user_time_spent_t_t_a' => ['type', 'type_id', 'asu_id'],
			],
			'email_templates' => [
				['status'],
			],
			'javascript_error_logs' => [
				['source'],
				['line'],
				['column'],
				['message'],
				['stack'],
			],
			'configuration' => [
				['category_id'],
				['status'],
				['type'],
				'idx_configuration_key_status' => ['key', 'status'], // Performance: Speed up getConfig lookups
			],
			'user_competencies' => [
				['acquired_at'],
				'idx_user_competencies_u_c_a' => ['user_id', 'competency_id', 'acquired_at'],
			],
			'manager_users' => [
				'idx_managers_user_m_u' => ['manager_id', 'user_id'],
			],
			'learning_module_competencies' => [
				['points'],
			],
			'comments' => [
				['table_name'],
				['status'],
				['deleted_at'],
				['visible_learner'],
				['table_row_id'],
				['table_name', 'table_row_id'],
			],
			'user_form_template_workflow_relations' => [
				['type'],
				['type_id'],
				['status'],
				'idx_user_form_template_workflow_relations_t_t_s' => ['type', 'type_id', 'status'],
			],
		];
		foreach ($table_indexes as $table_name => $index_list) {
			foreach ($index_list as $index_key => $indexes) {
				$key = $index_key;
				if (is_numeric($index_key)) {
					$key = "idx_" . $table_name . "_" . implode("_", $indexes);
				}

				// Always check for and remove duplicate indexes on every site update
				self::removeDuplicateIndexes($table_name, $key, $indexes);

				// Check if the index already exists
				if (!DB::select("SHOW INDEX FROM `$table_name` WHERE Key_name = '" . $key . "'")) {
					// Escape each field name with backticks
					$indexes_escaped = array_map(function($field) {
						return "`$field`";
					}, $indexes);

					// Escape the table name with backticks
					$escaped_table_name = "`$table_name`";

					// Construct the CREATE INDEX statement with escaped identifiers
					$statement = "CREATE INDEX `$key` ON $escaped_table_name (" . implode(",", $indexes_escaped) . ");";
					echo "\n";
					echo "Creating index: $statement";
					echo "\n";
					DB::statement($statement);
				}
			}
		}

		// Special indexes that require custom SQL
		$special_indexes = [
			[
				'table' => 'email_queue',
				'name' => 'idx_email_queue_recipients_emails',
				'sql' => 'CREATE INDEX `idx_email_queue_recipients_emails` ON `email_queue` (`recipients_emails`(255))'
			],
			[
				'table' => 'email_queue',
				'name' => 'idx_email_queue_id_desc',
				'sql' => 'CREATE INDEX `idx_email_queue_id_desc` ON `email_queue` (`id` DESC)'
			],
		];

		foreach ($special_indexes as $special_index) {
			$table_name = $special_index['table'];
			$index_name = $special_index['name'];
			$sql = $special_index['sql'];

			// Always check for and remove duplicate indexes on every site update
			$fields = self::extractFieldsFromSQL($sql);
			if (!empty($fields)) {
				self::removeDuplicateIndexes($table_name, $index_name, $fields);
			}

			// Check if the index already exists
			if (!DB::select("SHOW INDEX FROM `$table_name` WHERE Key_name = '$index_name'")) {
				try {
					echo "\n";
					echo "Creating special index: $sql";
					echo "\n";
					DB::statement($sql);
				} catch (\Exception $e) {
					echo "\n";
					echo "Failed to create special index $index_name: " . $e->getMessage();
					echo "\n";
				}
			}
		}

		$full_index_list = [
			'email_history' => [
				['body_text_only'],
			],
		];
		foreach ($full_index_list as $table_name => $index_list) {
			foreach ($index_list as $indexes) {
				foreach ($indexes as $index) {
					// Always check for and remove duplicate indexes on every site update
					self::removeDuplicateIndexes($table_name, $index, [$index]);
					
					// Escape the table name with backticks
					$escaped_table_name = "`$table_name`";

					if (!DB::select("SHOW INDEX FROM $escaped_table_name WHERE Key_name = '" . $index . "'")) {
						$statement = "ALTER TABLE $escaped_table_name ADD FULLTEXT (" . $index . ");";
						echo "\n";
						echo "Creating fulltext index: $statement";
						echo "\n";
						DB::statement($statement);
					}
				}
			}
		}
	}
}