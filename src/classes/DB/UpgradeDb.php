<?php

namespace DB;

use APP\Controllers\CustomReportController;
use APP\Controllers\PowerBIController;
use APP\Tools;
use Carbon\Carbon;
use Illuminate\Database\Capsule\Manager as Capsule;
use Illuminate\Database\Capsule\Manager as DB;
use Doctrine\DBAL\Types\StringType;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Str;
use Models\LearningModuleVersion;
use Models\LearningResult;
use Models\Certificate;
use Models\CustomReport;
use Models\IlrLearningDeliveryFamCodes;
use Models\IlrLearningDeliveryFamTypes;
use Models\IlrLearningDeliveryWithdrawReasons;
use Models\IlrUserLearnerFamCodes;
use Models\IlrUserLearnerFamTypes;
use Models\IlrUserPriorAttainmentLevel;
use Models\Schedule;

class UpgradeDb
{
	public function upgrade()
	{
		//structural update

		// Table renaming must be handled before create DB is fired as create db will create new updated empty tables.
		$time_rename_start = microtime(true);
		echo "\n  renameTables";
		$this->renameTables();
		$time_rename_end = microtime(true);
		$time_rename = number_format(($time_rename_end - $time_rename_start), 2);
		echo " - ({$time_rename}s)";

		$time_createDB_start = microtime(true);
		echo "\n  createDB";
		$schema = new \DB\Schema(); // This will provide creation of new tables if they don't exist.
		$schema->createDB();
		$time_createDB_end = microtime(true);
		$time_createDB = number_format(($time_createDB_end - $time_createDB_start), 2);
		echo " - ({$time_createDB}s)";

		// Update table structure, if something have changed, add/remove fields.
		$time_updateTableDataPre_start = microtime(true);
		echo "\n  updateTableDataPre";
		$this->updateTableDataPre();
		$time_updateTableDataPre_end = microtime(true);
		$time_updateTableDataPre = number_format(($time_updateTableDataPre_end - $time_updateTableDataPre_start), 2);
		echo " - ({$time_updateTableDataPre}s)";

		$time_updateTablesStructure_start = microtime(true);
		echo "\n  updateTablesStructure";
		$this->updateTablesStructure();
		$time_updateTablesStructure_end = microtime(true);
		$time_updateTablesStructure = number_format(($time_updateTablesStructure_end - $time_updateTablesStructure_start), 2);
		echo " - ({$time_updateTablesStructure}s)";

		// rest of data update
		/*
		Not needed for now
		if ($this->settings["licensing"]["isApprentix"]) {
			$time_populateApprenticeShipRoutesAndStandards_start = microtime(true);
			$this->populateApprenticeShipRoutesAndStandards();
			$time_populateApprenticeShipRoutesAndStandards_end = microtime(true);
			$time_populateApprenticeShipRoutesAndStandards = number_format(($time_populateApprenticeShipRoutesAndStandards_end - $time_populateApprenticeShipRoutesAndStandards_start), 2);
			echo " \n --- populateApprenticeShipRoutesAndStandards in {$time_populateApprenticeShipRoutesAndStandards} seconds. \n";
		}
		*/

		// Populate sample data, if needed.
		$time_populateSampleData_start = microtime(true);
		$sample_data = new \DB\SampleData();
		$sample_data->populateCompanies();
		$sample_data->populateDepartments();
		$sample_data->populateRoles();
		$sample_data->populateUsers();
		$sample_data->populateLearningModuleTypes();
		$time_populateSampleData_end = microtime(true);
		$time_populateSampleData = number_format(($time_populateSampleData_end - $time_populateSampleData_start), 2);
		echo "\n  populateSampleData({$time_populateSampleData}s)";

		$time_indexes_start = microtime(true);
		echo "\n  updateTableIndexes";
		$indexes = new \DB\Indexing();
		$indexes->update();
		$time_indexes_end = microtime(true);
		$time_indexes = number_format(($time_indexes_end - $time_indexes_start), 2);
		echo " - ({$time_indexes}s)";

		$time_updateTableData_start = microtime(true);
		echo "\n  updateTableData";
		$this->updateTableData();
		$time_updateTableData_end = microtime(true);
		$time_updateTableData = number_format(($time_updateTableData_end - $time_updateTableData_start), 2);
		echo " - ({$time_updateTableData}s)";

		// $time_updateFutureEvents_start = microtime(true);
		// echo "\n  updateFutureEvents";
		// $updated_events = \Models\Schedule::updateFutureEvents();
		// $time_updateFutureEvents_end = microtime(true);
		// $time_updateFutureEvents = number_format(($time_updateFutureEvents_end - $time_updateFutureEvents_start), 2);
		// echo " - $updated_events - ({$time_updateFutureEvents}s)";




		// Reset Administrator role permission only!
		$time_populateRoles_start = microtime(true);
		echo "\n  updateAdminRoles";
		\Models\Role::updateAdminRoles();
		$time_populateRoles_end = microtime(true);
		$time_populateRoles = number_format(($time_populateRoles_end - $time_populateRoles_start), 2);
		echo " - ({$time_populateRoles}s)";

		$time_populateVersion_start = microtime(true);
		echo "\n  populateVersion";
		$this->populateVersion();
		$time_populateVersion_end = microtime(true);
		$time_populateVersion = number_format(($time_populateVersion_end - $time_populateVersion_start), 2);
		echo " - ({$time_populateVersion}s)";

		//Populate dashboard statics and dataviews and update them
		$timePopulateCertificatesStart = microtime(true);
		echo "\n  Populate Default Certificates Data";
		$this->populateDefaultCertificateData();
		$timePopulateCertificatesEnd = microtime(true);
		$timePopulateCertificates = number_format(($timePopulateCertificatesEnd - $timePopulateCertificatesStart), 2);
		echo " - ({$timePopulateCertificates}s)";

		// check and update uploaded evidence file sizes.
		$check_update_file_sizes_start = microtime(true);
		echo "\n  check and update uploaded file sizes";
		$processed_cnt = \Models\LearningModuleEvidence::checkAndUpdateSizes();
		echo ", processed: $processed_cnt";
		$check_update_file_size_end = microtime(true);
		$check_update_file_size = number_format(($check_update_file_size_end - $check_update_file_sizes_start), 2);
		echo " - ({$check_update_file_size}s)";

		// check and update uploaded file sizes.
		$check_update_file_sizes_start = microtime(true);
		echo "\n  check and update uploaded file sizes";
		$processed_cnt = \Models\File::checkAndUpdateSizes();
		echo ", processed: $processed_cnt";
		$check_update_file_size_end = microtime(true);
		$check_update_file_size = number_format(($check_update_file_size_end - $check_update_file_sizes_start), 2);
		echo " - ({$check_update_file_size}s)";
	}

	public function renameTables()
	{

		// Rename tables, proper naming convention!
		if (Capsule::schema()->hasTable('learning_modules_evidence')) {
			Capsule::schema()->rename('learning_modules_evidence', 'learning_module_evidences');
		}
		if (Capsule::schema()->hasTable('learning_modules_evidence_meetings')) {
			Capsule::schema()->rename('learning_modules_evidence_meetings', 'learning_module_evidence_meetings');
		}
        if (
			Capsule::schema()->hasTable('powerbi_course_set_learning_modules') &&
			!Capsule::schema()->hasTable('powerbi_course_set_links')
		) {
            Capsule::schema()->rename('powerbi_course_set_learning_modules', 'powerbi_course_set_links');
        }
	}


	// List of routes and pre-defined standards to populate funding.
	public function populateApprenticeShipRoutesAndStandards()
	{

		// Truncate existing data from both tables as this is static and does not link anywhere.
		Capsule::connection()->statement("SET foreign_key_checks = 0");
		\Models\ApprenticeshipRoutesStandard::truncate();
		\Models\ApprenticeshipRoute::truncate();
		Capsule::connection()->statement("SET foreign_key_checks = 1");

		// First level will populate standard routes, second level will populate predefined standards
		$routes = \DB\UpgradeRoutes::getList();

		foreach ($routes as $route_name => $route) {
			$route_query = new \Models\ApprenticeshipRoute;
			$route_query->name = $route_name;
			$route_query->status = true;
			$route_query->save();
			foreach ($route as $key => $standard) {
				$standard_query = new \Models\ApprenticeshipRoutesStandard;
				$standard_query->name = $standard[0];
				$standard_query->reference = $standard[1];
				$standard_query->route_id = $route_query->id;
				$standard_query->level = $standard[6];
				$standard_query->published = $standard[4];
				$standard_query->lars_code = $standard[11];
				$standard_query->funding_band = $standard[8];
				$standard_query->funding_band_maximum = $standard[7];
				$standard_query->link = $standard[12];
				$standard_query->status = true;
				$standard_query->save();
			}
		}
	}

	public function updateTablesStructure() {


		// Add fields in user table to reflect user progress.

		if (Capsule::schema()->hasColumn('users', 'completed')) {
			Capsule::schema()->table('users', function ($table) {
				$table->decimal('completed', 5, 2)->default(0)->change();
			});
		}

		if (!Capsule::schema()->hasColumn('users', 'sort_resource_order')) {
			Capsule::schema()->table('users', function ($table) {
				$table->string('sort_resource_order')->default('date_sort')->after('manager_percentage_behind');
			});
		}
		if (!Capsule::schema()->hasColumn('users', 'total_resources')) {
			Capsule::schema()->table('users', function ($table) {
				$table->smallInteger('total_resources')->unsigned()->default(0)->after('manager_percentage_behind');
			});
		}
		if (!Capsule::schema()->hasColumn('users', 'not_started_resources')) {
			Capsule::schema()->table('users', function ($table) {
				$table->smallInteger('not_started_resources')->unsigned()->default(0)->after('total_resources');
			});
		}
		if (!Capsule::schema()->hasColumn('users', 'in_progress_resources')) {
			Capsule::schema()->table('users', function ($table) {
				$table->smallInteger('in_progress_resources')->unsigned()->default(0)->after('not_started_resources');
			});
		}
		if (!Capsule::schema()->hasColumn('users', 'completed_resources')) {
			Capsule::schema()->table('users', function ($table) {
				$table->smallInteger('completed_resources')->unsigned()->default(0)->after('in_progress_resources');
			});
		}
		if (!Capsule::schema()->hasColumn('users', 'time_spent')) {
			Capsule::schema()->table('users', function ($table) {
				$table->decimal('time_spent', 5, 2)->default(0)->after('completed_resources');
			});
		}
		if (!Capsule::schema()->hasColumn('users', 'completed')) {
			Capsule::schema()->table('users', function ($table) {
				$table->decimal('completed', 5, 2)->default(0)->after('time_spent');
			});
		}


		// SCOR-2788 Increase file size limit for Open eLMS AI Editor
		if (Capsule::schema()->hasColumn('scorm_course', 'maxbytes')) {
			Capsule::schema()->table('scorm_course', function ($table) {
				$table->bigInteger('maxbytes')->default(1073741824)->change();
			});
		}

		/*Country table additional Fields*/
		if (!Capsule::schema()->hasColumn('countries', 'country_group_id')) {
			Capsule::schema()->table('countries', function ($table) {
				$table->integer('country_group_id')->unsigned()->nullable()->after('name');
			});
		}
		if (!Capsule::schema()->hasColumn('countries', 'display_order')) {
			Capsule::schema()->table('countries', function ($table) {
				$table->integer('display_order')->default(10)->after('country_group_id');
			});
		}


		// Add checkbox for e-learning resources that will show/hide refresh/customize learning options.
		if (!Capsule::schema()->hasColumn('learning_modules', 'refresh')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->boolean('refresh')->default(false)->after('company_id');
			});
			\Models\LearningModule::where('refresh_period', '>', 0)->update(['refresh' => true]);
		}
		// Add checkbox to customise refresh email for each module
		if (!Capsule::schema()->hasColumn('learning_modules', 'refresh_custom_email')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->boolean('refresh_custom_email')->default(false)->after('refresh_period');
			});
		}
		// Add Form Id for Type Form
		if (!Capsule::schema()->hasColumn('learning_modules', 'form_id')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->integer('form_id')->unsigned()->nullable()->after('refresh_period');
			});
		}
		// Add custom refresh email subject in module
		if (!Capsule::schema()->hasColumn('learning_modules', 'refresh_custom_email_subject')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->string('refresh_custom_email_subject')->default("Course Refresh Notification")->after('refresh_custom_email');
			});
		}
		// Add custom refresh email body
		if (!Capsule::schema()->hasColumn('learning_modules', 'refresh_custom_email_body')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->mediumText('refresh_custom_email_body')->nullable()->default(null)->after('refresh_custom_email_subject');
			});
		}

		// Add programme completion percentage field, derived from hours spent.
		if (!Capsule::schema()->hasColumn('apprenticeship_standards_users', 'percentage_time')) {
			Capsule::schema()->table('apprenticeship_standards_users', function ($table) {
				$table->decimal('percentage_time', 4, 2)->default(0)->after('percentage');
			});
		}

		// Add updated_by field for users table.
		if (!Capsule::schema()->hasColumn('users', 'updated_by')) {
			Capsule::schema()->table('users', function ($table) {
				$table->integer('updated_by')->unsigned()->nullable()->after('status');
			});
		}
		// Add approval_status field for users table.
		if (!Capsule::schema()->hasColumn('users', 'approval_status')) {
			Capsule::schema()->table('users', function ($table) {
				$table->string('approval_status')->default('Approved')->after('status');
			});
		}
		// Add password_force_reset field for users table.
		if (!Capsule::schema()->hasColumn('users', 'password_force_reset')) {
			Capsule::schema()->table('users', function ($table) {
				$table->boolean('password_force_reset')->default(false)->after('approval_status');
			});
		}
		// Add password_changed_at field for users table.
		if (!Capsule::schema()->hasColumn('users', 'password_changed_at')) {
			Capsule::schema()->table('users', function ($table) {
				$table->date('password_changed_at')->nullable()->after('password_force_reset');
			});
		}
		// Add password_attempts field for users table.
			if (!Capsule::schema()->hasColumn('users', 'password_attempts')) {
				Capsule::schema()->table('users', function ($table) {
					$table->integer('password_attempts')->unsigned()->default(0)->after('password_changed_at');
				});
			}

		// Add "is_jackdaw_team" boolean for table "groups".
		if (!Capsule::schema()->hasColumn('groups', 'is_jackdaw_team')) {
			Capsule::schema()->table('groups', function ($table) {
				$table->boolean('is_jackdaw_team')->default(false)->after('name');
			});
		}

		// Allow created_by to be nullable
		if (Capsule::schema()->hasColumn('learning_module_containers', 'created_by')) {
			Capsule::schema()->table('learning_module_containers', function ($table) {
				$table->integer('created_by')->unsigned()->nullable()->change();
			});
		}

		// Add field "jackdaw_type" for "roles" table
		if (!Capsule::schema()->hasColumn('roles', 'jackdaw_type')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->string('jackdaw_type')->after('is_manager');
			});
		}

		// Add "is_demo" boolean for table "roles". Demo user will have some specific permissions.
		if (!Capsule::schema()->hasColumn('roles', 'is_demo')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->boolean('is_demo')->default(false)->after('jackdaw_type');
			});
		}

		// Add "shadow_role_id" to "users", meant for managers and admins to try different roles.
		// Admin can try manager and trainee.
		// Manager can try trainee role.
		if (!Capsule::schema()->hasColumn('users', 'shadow_role_id')) {
			Capsule::schema()->table('users', function ($table) {
				$table->integer('shadow_role_id')->unsigned()->nullable()->after('role_id');
			});
		}

		// Add "hide_learner" to "apprenticeship_issues".
		if (!Capsule::schema()->hasColumn('apprenticeship_issues', 'hide_learner')) {
			Capsule::schema()->table('apprenticeship_issues', function ($table) {
				$table->boolean('hide_learner')->default(0)->after('status');
			});
		}
		// Add "visible_resource" to "apprenticeship_issues".
		if (!Capsule::schema()->hasColumn('apprenticeship_issues', 'visible_resource')) {
			Capsule::schema()->table('apprenticeship_issues', function ($table) {
				$table->boolean('visible_resource')->default(1);
			});
		}
		if (!Capsule::schema()->hasColumn('learning_module_types_parameter', 'position')) {
			Capsule::schema()->table('learning_module_types_parameter', function ($table) {
				$table->integer('position')->unsigned()->nullable();
			});
		}
		// Add "field" to "learning_module_types".
		if (!Capsule::schema()->hasColumn('learning_module_types', 'field')) {
			Capsule::schema()->table('learning_module_types', function ($table) {
				$table->text('field')->default(NULL)->after('status');
			});
		}
		if (!Capsule::schema()->hasColumn('learning_module_types', 'custom')) {
			Capsule::schema()->table('learning_module_types', function ($table) {
				$table->boolean('custom')->default(0)->after('field');
			});
		}
		if (!Capsule::schema()->hasColumn('learning_module_types', 'fit_for_evidence')) {
			Capsule::schema()->table('learning_module_types', function ($table) {
				$table->boolean('fit_for_evidence')->default(0)->after('custom');
			});
			$upload_module_types = \Models\LearningModuleType::where('slug', 'upload')->first();
			$upload_module_types->fit_for_evidence = true;
			$upload_module_types->save();
		}
		if (!Capsule::schema()->hasColumn('learning_module_types', 'attached_word_form')) {
			Capsule::schema()->table('learning_module_types', function ($table) {
				$table->text('attached_word_form')->default(NULL)->after('status');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_module_feedback', 'status')) {
			Capsule::schema()->table('learning_module_feedback', function ($table) {
				$table->boolean('status')->default(1)->after('anonymous');
			});
		}

		// Add "created_by_group" for table "learning_modules", when jackdaw team creates resource
		if (!Capsule::schema()->hasColumn('learning_modules', 'created_by_group')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->integer('created_by_group')->unsigned()->nullable()->after('created_by');
			});
		}


		if (!Capsule::schema()->hasColumn('learning_modules', 'require_management_signoff')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->boolean('require_management_signoff')->default(0)->after('evidence_type_id');
			});
		}

		if (!Capsule::schema()->hasColumn('custom_programme_statuses', 'colour_code')) {
			Capsule::schema()->table('custom_programme_statuses', function ($table) {
				$table->string('colour_code')->nullable()->after('description');
			});
		}

		// Add new ILR fields into user's table
		if (!Capsule::schema()->hasColumn('users', 'CampId')) {
			Capsule::schema()->table('users', function ($table) {
				$table->string('CampId', 8)->default("")->after('ULN')->comment = "Campus Identifier";
			});
		}

		if (!Capsule::schema()->hasColumn('users', 'OTJHours')) {
			Capsule::schema()->table('users', function ($table) {
				$table->integer('OTJHours')->unsigned()->nullable()->after('CampId')->comment = "Off-the-job training hours";
			});
		}



		// Add field "api_match" for learning_modules, string, anything can be added so that learning can be opened using that string as identificator
		if (!Capsule::schema()->hasColumn('learning_modules', 'api_match')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->string('api_match')->default('')->after('created_by_group');
			});
		}

		// Add rating(average) to learning_modules table.
		if (!Capsule::schema()->hasColumn('learning_modules', 'rating')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->tinyInteger('rating')->unsigned()->nullable()->after('api_match');
			});
		} else {
			if(DB::connection()->getDoctrineColumn('learning_modules', 'rating')->getType()->getName() != "decimal"){
				DB::statement("ALTER TABLE `learning_modules` CHANGE COLUMN `rating` `rating` decimal(5,1)");
			}
		}

		// Modify feedback table for rating
		if (!Capsule::schema()->hasColumn('learning_module_feedback', 'rating')) {
			Capsule::schema()->table('learning_module_feedback', function ($table) {
				$table->integer('rating')->unsigned()->after('feedback');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_module_feedback', 'anonymous')) {
			Capsule::schema()->table('learning_module_feedback', function ($table) {
				$table->boolean('anonymous')->default(false)->after('rating');
			});
		}

		// Add "api" field to session table to identify if hash was created by API login
		if (!Capsule::schema()->hasColumn('sessions', 'api')) {
			Capsule::schema()->table('sessions', function ($table) {
				$table->boolean('api')->default(false)->after('hash');
			});
		}

		// Add "api_access_count" field to sessions table to count how many times api calls have been mae
		if (!Capsule::schema()->hasColumn('sessions', 'api_access_count')) {
			Capsule::schema()->table('sessions', function ($table) {
				$table->integer('api_access_count')->unsigned()->default(0)->after('api');
			});
		}

		// Add "time_spent" for "apprenticeship_standards_users"
		if (!Capsule::schema()->hasColumn('apprenticeship_standards_users', 'time_spent')) {
			Capsule::schema()->table('apprenticeship_standards_users', function ($table) {
				$table->decimal('time_spent', 5, 2)->default(0)->after('percentage_behind');
			});
		}


		// Add "working_hours" for "apprenticeship_standards"
		if (!Capsule::schema()->hasColumn('apprenticeship_standards', 'working_hours')) {
			Capsule::schema()->table('apprenticeship_standards', function ($table) {
				$table->integer('working_hours')->unsigned()->nullable()->after('completion_months');
			});
		}

		// Email_templates, add status toggle
		if (!Capsule::schema()->hasColumn('email_templates', 'status')) {
			Capsule::schema()->table('email_templates', function ($table) {
				$table->boolean('status')->default(true)->after('is_temporary');
			});
		}

		// Increase size of name for standard categories/outcome
		if (Capsule::schema()->hasColumn('apprenticeship_issue_categories', 'name')) {
			Capsule::schema()->table('apprenticeship_issue_categories', function ($table) {
				$table->string('name', 800)->change();
			});
		}

		// Increase size of name for standard issues/criteria
		if (Capsule::schema()->hasColumn('apprenticeship_issues', 'name')) {
			Capsule::schema()->table('apprenticeship_issues', function ($table) {
				$table->string('name', 800)->change();
			});
		}


		// Add paid_for for get userid for  payment
		if (!Capsule::schema()->hasColumn('user_payment_transactions', 'paid_for')) {
			Capsule::schema()->table('user_payment_transactions', function ($table) {
				$table->integer('paid_for')->unsigned()->nullable()->after("type_id");
				$table->foreign('paid_for')->references('id')->on('users')->onDelete('cascade');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_modules', 'total_duration')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->integer('total_duration')->unsigned()->nullable()->after("visible_learner");
				$table->integer('users_count')->comment("Completed users count")->unsigned()->nullable()->after("visible_learner");
			});
		}
		if (!Capsule::schema()->hasColumn('learning_modules', 'average_duration')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->integer('average_duration')->unsigned()->nullable()->after("visible_learner");
			});
		}

		//resend in email history
		if (!Capsule::schema()->hasColumn('email_history', 'resend')) {
			Capsule::schema()->table('email_history', function ($table) {
				$table->boolean('resend')->default(false)->after('sent');
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_standards_users', 'expected_time')) {
			Capsule::schema()->table('apprenticeship_standards_users', function ($table) {
				$table->decimal('expected_time', 7, 2)->default(0)->after("time_behind");
			});
		}
		if(!Capsule::schema()->hasColumn('document_template_bindings','is_report'))
			{
			  Capsule::schema()->table('document_template_bindings',function($table){
				$table->boolean('is_report')->default(false);
				$table->integer('custom_report_id')->nullable();
				DB::statement("ALTER TABLE document_template_bindings CHANGE form_id form_id integer NULL;");
			  });
		}
	if(!Capsule::schema()->hasColumn('custom_report_fields','multi_select'))
	  {
		Capsule::schema()->table('custom_report_fields',function($table){
		  $table->boolean('multi_select')->default(false)->nullable();
		});
	  }
	if(!Capsule::schema()->hasColumn('custom_report_fields','type'))
	  {
		Capsule::schema()->table('custom_report_fields',function($table){
		  $table->enum('type',['text','boolean','date','array'])->default('text');
		});
	  }
	if(!Capsule::schema()->hasColumn('document_template_bindings','custom_report_field_id'))
	{
	  Capsule::schema()->table('document_template_bindings',function($table){
		  $table->integer('custom_report_field_id')->nullable();
		  $table->integer('report_row_count')->nullable();
	  });
	}

	if(!Capsule::schema()->hasColumn('document_template_bindings','report_type'))
	{
	  Capsule::schema()->table('document_template_bindings',function($table){
		  $table->enum('report_type',['summary_report','per_record_report'])->nullable();
	  });
	}
	if(!Capsule::schema()->hasColumn('forms','is_derived_form'))
	{
		Capsule::schema()->table('forms',function($table){
		  $table->boolean('is_derived_form')->default(false);
		});
	}
	/*ParentID to identify duplication*/
		if(!Capsule::schema()->hasColumn('forms','parent_id'))
		{
			Capsule::schema()->table('forms',function($table){
				$table->integer('parent_id')->nullable()->after("is_derived_form");
			});
		}
	/*Duplications that are not required to display in list*/
		if(!Capsule::schema()->hasColumn('forms','display_in_list'))
		{
			Capsule::schema()->table('forms',function($table){
				$table->boolean('display_in_list')->default(true)->after("parent_id");
			});
		}
		// Add new visit_type for manager_reviews table.

		// Rename learner's visit_types and update table data after enum change.
		$reviews = \Models\ManagerReview::where('visit_type', 'Reviews')
			->get();
		$coach_visits = \Models\ManagerReview::where('visit_type', 'Coach Visit')
			->get();
		$coach_progress_reviews = \Models\ManagerReview::where('visit_type', 'Coach Progress Review')
			->get();
		$monthly_reviews = \Models\ManagerReview::where('visit_type', 'Monthly Review')
			->get();


		if (Capsule::schema()->hasColumn('manager_reviews', 'visit_type')) {
			Capsule::schema()->table('manager_reviews', function ($table) {
				DB::statement("ALTER TABLE manager_reviews CHANGE COLUMN visit_type visit_type ENUM('Initial Assessment', 'Development', 'Training', 'General Admin', 'Progress Review', 'QA Report') NULL DEFAULT NULL");
			});
		}

		foreach ($reviews as $key => $review) {
			$update_review = \Models\ManagerReview::find($review->id);
			$update_review->visit_type = 'Monthly Review';
			$update_review->save();
		}
		foreach ($coach_visits as $key => $coach_visit) {
			$update_coach_visit = \Models\ManagerReview::find($coach_visit->id);
			$update_coach_visit->visit_type = 'Development';
			$update_coach_visit->save();
		}
		foreach ($coach_progress_reviews as $key => $coach_progress_review) {
			$update_coach_progress_review = \Models\ManagerReview::find($coach_progress_review->id);
			$update_coach_progress_review->visit_type = 'Training';
			$update_coach_progress_review->save();
		}
		foreach ($monthly_reviews as $key => $monthly_review) {
			$update_monthly_review = \Models\ManagerReview::find($monthly_review->id);
			$update_monthly_review->visit_type = 'Progress Review';
			$update_monthly_review->save();
		}
		// EOF update manager's reviews


		//Add standard_id for manager_reviews table
		if (!Capsule::schema()->hasColumn('manager_reviews', 'standard_id')) {
			Capsule::schema()->table('manager_reviews', function ($table) {
				$table->integer('standard_id')->unsigned()->nullable()->after('report_file');
			});
		}

		// Some DB's missing this.
		if (!Capsule::schema()->hasColumn('apprenticeship_standards', 'level')) {
			Capsule::schema()->table('apprenticeship_standards', function ($table) {
				$table->integer('level')->unsigned()->nullable()->after('category_id');
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_standards', 'funding')) {
			Capsule::schema()->table('apprenticeship_standards', function ($table) {
				$table->integer('funding')->unsigned()->nullable()->after('level');
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_standards', 'reference_code')) {
			Capsule::schema()->table('apprenticeship_standards', function ($table) {
				$table->string('reference_code')->after('funding');
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_standards', 'periodic_review')) {
			Capsule::schema()->table('apprenticeship_standards', function ($table) {
				$table->boolean('periodic_review')->default(false)->after('reference_code');
			});
		}
		// Increase size of name for standard categories/outcome


		if (!Capsule::schema()->hasColumn('apprenticeship_standards', 'review_interval')) {
			Capsule::schema()->table('apprenticeship_standards', function ($table) {
				$table->smallInteger('review_interval')->unsigned()->nullable()->default(null)->after('periodic_review');
			});
		}

		// Add 2 columns for apprenticeship_standards to add "Link to ILR Learning Delivery"
		if (!Capsule::schema()->hasColumn('apprenticeship_standards', 'ilr_learning_delivery')) {
			Capsule::schema()->table('apprenticeship_standards', function ($table) {
				$table->boolean('ilr_learning_delivery')->default(false)->after('review_interval');
			});
		}
		if (!Capsule::schema()->hasColumn('apprenticeship_standards', 'learning_delivery_type')) {
			Capsule::schema()->table('apprenticeship_standards', function ($table) {
				$table->string('learning_delivery_type')->after('ilr_learning_delivery');
			});
		}
		if (!Capsule::schema()->hasColumn('apprenticeship_standards', 'delivery')) {
			Capsule::schema()->table('apprenticeship_standards', function ($table) {
				$table->string('delivery')->after('learning_delivery_type');
			});
		}

		// For "isSMCR" add two fields in user's table.
		if (Capsule::schema()->hasColumn('users', 'staff_type')) {
			Capsule::schema()->table('users', function ($table) {
				$table->renameColumn('staff_type', 'staff_type_id');
			});
		}

		if (!Capsule::schema()->hasColumn('users', 'staff_type_id')) {
			Capsule::schema()->table('users', function ($table) {
				$table->integer('staff_type_id')->unsigned()->nullable()->after('updated_by');
			});
		}

		// Capsule::schema()->table('users', function ($table) {
		// 		if ($table->hasIndex('UQ_Email'))
		// 		  $table->dropUnique('UQ_Email');
		//  });

		Capsule::schema()->table('users', function ($table) {
			$sm =Capsule::schema()->getConnection()->getDoctrineSchemaManager();
			$indexesFound = $sm->listTableIndexes('users');

			if(array_key_exists("uq_email", $indexesFound))
				$table->dropUnique("uq_email");
			if (array_key_exists("users_email_unique", $indexesFound)) {
				$table->dropUnique("users_email_unique");
			}
		});

		if (!Capsule::schema()->hasColumn('users', 'report_to')) {
			Capsule::schema()->table('users', function ($table) {
				$table->integer('report_to')->unsigned()->nullable()->after('staff_type_id');
			});
		}

		if (!Capsule::schema()->hasColumn('designations', 'lesson_id')) {
			Capsule::schema()->table('designations', function ($table) {
				$table->integer('lesson_id')->unsigned()->nullable()->after('name');
			});
		}


		// Add Grade to signed off learning resources
		if (!Capsule::schema()->hasColumn('learning_results', 'grade')) {
			Capsule::schema()->table('learning_results', function ($table) {
				$table->enum('grade', ['Pass', 'Fail', 'Merit', 'Distinction'])->nullable()->default(null)->after('passing_status');
			});
		}

		// Move Start and End day to attached modules, criteria/issue start/end day will just be a default value for said modules
		if (!Capsule::schema()->hasColumn('apprenticeship_issues_learning_modules', 'sort')) {
			Capsule::schema()->table('apprenticeship_issues_learning_modules', function ($table) {
				$table->integer('sort')->unsigned()->nullable()->after('learning_modules_id');
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_issues_learning_modules', 'custom_work_window')) {
			Capsule::schema()->table('apprenticeship_issues_learning_modules', function ($table) {
				$table->boolean('custom_work_window')->default(0)->after('sort');
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_issues_learning_modules', 'start_day')) {
			Capsule::schema()->table('apprenticeship_issues_learning_modules', function ($table) {
				$table->integer('start_day')->unsigned()->nullable()->after('custom_work_window');
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_issues_learning_modules', 'end_day')) {
			Capsule::schema()->table('apprenticeship_issues_learning_modules', function ($table) {
				$table->integer('end_day')->unsigned()->nullable()->after('start_day');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_results', 'duration_hours')) {
			Capsule::schema()->table('learning_results', function ($table) {
				$table->smallInteger('duration_hours')->unsigned()->default(0)->after('score');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_results', 'duration_minutes')) {
			Capsule::schema()->table('learning_results', function ($table) {
				$table->tinyInteger('duration_minutes')->unsigned()->default(0)->after('duration_hours');
			});
		}

		// Reflective log, new resource type, needs new fields in learning_results:
		if (!Capsule::schema()->hasColumn('learning_results', 'log_learned')) {
			Capsule::schema()->table('learning_results', function ($table) {
				$table->text('log_learned')->nullable()->after('duration_minutes');
			});
		}
		if (!Capsule::schema()->hasColumn('learning_results', 'log_to_learn')) {
			Capsule::schema()->table('learning_results', function ($table) {
				$table->text('log_to_learn')->nullable()->after('log_learned');
			});
		}
		if (!Capsule::schema()->hasColumn('learning_results', 'log_used')) {
			Capsule::schema()->table('learning_results', function ($table) {
				$table->text('log_used')->nullable()->after('log_to_learn');
			});
		}

		// Add user's weeks working hours
		// https://bitbucket.org/emilrw/scormdata/issues/628/working-week-hours
		if (!Capsule::schema()->hasColumn('users', 'week_hours')) {
			Capsule::schema()->table('users', function ($table) {
				$table->decimal('week_hours', 5, 2)->nullable()->default(null)->after('completed');
			});
		}


		// Add checkbox for groups that will enable/disable assigning resources to added/removed users.
		if (!Capsule::schema()->hasColumn('groups', 'add_remove_resources')) {
			Capsule::schema()->table('groups', function ($table) {
				$table->boolean('add_remove_resources')->default(true)->after('is_jackdaw_team');
			});
		}

		// Add status to users table
		if (!Capsule::schema()->hasColumn('users', 'learning_status')) {
			Capsule::schema()->table('users', function ($table) {
				$table->enum('learning_status', ['Completed', 'In Progress', 'Not Started'])->default('Not Started')->after('week_hours');
			});
		}

		if (Capsule::schema()->hasColumn('graph_fields', 'measure')) {
			Capsule::schema()->table('graph_fields', function ($table) {
				DB::statement("ALTER TABLE graph_fields MODIFY COLUMN `measure` ENUM('average', 'sum', 'unique', 'total_count','individual') NULL DEFAULT NULL");
			});
		}

		// Add new items in resource_queries
		if (Capsule::schema()->hasColumn('resource_queries', 'type')) {
			Capsule::schema()->table('resource_queries', function ($table) {
                DB::statement("ALTER TABLE resource_queries MODIFY COLUMN `type` ENUM('programmes', 'resources', 'lessons', 'outcome', 'criteria', 'subcriteria', 'events', 'forms', 'form-workflow', 'skill_library', 'skills_subjects', 'resources_refresh', 'lessons_refresh', 'reports') NULL DEFAULT NULL");
			});
		}

		if (!Capsule::schema()->hasColumn('resource_queries', 'type_parent_id')) {
			Capsule::schema()->table('resource_queries', function ($table) {
				$table->integer('type_parent_id')->unsigned()->default(0)->after('type_id');
			});
		}
		//Action can be add(Assign)/remove/disable
		if (!Capsule::schema()->hasColumn('resource_queries', 'action')) {
			Capsule::schema()->table('resource_queries', function ($table) {
				$table->string('action')->default('add')->after('type_parent_id');
			});
		}

		// add completionm dates for smcr
		if (!Capsule::schema()->hasColumn('users', 'last_completion_date')) {
			Capsule::schema()->table('users', function ($table) {
				$table->date('last_completion_date')->nullable()->after('learning_status');
			});
		}
		if (Capsule::schema()->hasColumn('resource_query_variables', 'group')) {
			Capsule::schema()->table('resource_query_variables', function ($table) {
				DB::statement("ALTER TABLE resource_query_variables MODIFY COLUMN `group` ENUM('programmes', 'resources', 'lessons', 'competency','organisation', 'forms', 'reports') NULL DEFAULT NULL");
			});
		}

		if (Capsule::schema()->hasColumn('custom_reviews', 'group')) {
			Capsule::schema()->table('custom_reviews', function ($table) {
				DB::statement("ALTER TABLE custom_reviews MODIFY COLUMN `group` ENUM('programmes', 'resources', 'customreports') NULL DEFAULT NULL");
			});
		}

		if (!Capsule::schema()->hasColumn('resource_query_variables', 'parameter_type')) {
			Capsule::schema()->table('resource_query_variables', function ($table) {
				$table->string('parameter_type')->default('custom_defined')->after('group');
			});
		}
		if (!Capsule::schema()->hasColumn('resource_query_variables', 'has_item')) {
			Capsule::schema()->table('resource_query_variables', function ($table) {
				$table->boolean('has_item')->default(true)->after('parameter_type');
			});
		}
		if (!Capsule::schema()->hasColumn('resource_query_variables', 'has_date_values')) {
			Capsule::schema()->table('resource_query_variables', function ($table) {
				$table->boolean('has_date_values')->default(false)->after('parameter_type');
			});
		}
		if (!Capsule::schema()->hasColumn('resource_query_variables', 'has_date_add')) {
			Capsule::schema()->table('resource_query_variables', function ($table) {
				$table->boolean('has_date_add')->default(false)->after('parameter_type');
			});
		}
		if (!Capsule::schema()->hasColumn('resource_query_variables', 'parameter_display')) { //decide Displaying element in query builder
			Capsule::schema()->table('resource_query_variables', function ($table) {
				$table->string('parameter_display')->default('value')->after('has_item');
			});
		}
		if (!Capsule::schema()->hasColumn('users', 'next_completion_date')) {
			Capsule::schema()->table('users', function ($table) {
				$table->date('next_completion_date')->nullable()->after('last_completion_date');
			});
		}

		// Remove table, because, who would bother to rename table!
		Capsule::schema()->dropIfExists('user_staff_types');

		// Add new permission for roles, if apprentix
		// https://bitbucket.org/emilrw/scormdata/issues/584/12-permission-to-sign-off-learner-status
		if (!capsule::schema()->hascolumn('roles', 'sign_off_learner_status')) {
			capsule::schema()->table('roles', function ($table) {
				$table->boolean('sign_off_learner_status')->default(false)->after('access_all_learners');
			});
		}

		// Entries for manager_reviews with "QA Report" visit_type, need to be approved by manager.
		// Those entries can be added by 3rd party.
		if (!Capsule::schema()->hasColumn('manager_reviews', 'checked_by_coach_trainer')) {
			Capsule::schema()->table('manager_reviews', function ($table) {
				$table->boolean('checked_by_coach_trainer')->default(0)->after('standard_id');
			});
		}
		if (!Capsule::schema()->hasColumn('manager_reviews', 'checked_by')) {
			Capsule::schema()->table('manager_reviews', function ($table) {
				$table->integer('checked_by')->unsigned()->nullable()->after('checked_by_coach_trainer');
			});
		}


		// Add evidence type id column in learning_modules table for only when resource is evidence.
		if (!Capsule::schema()->hasColumn('learning_modules', 'evidence_type_id')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->integer('evidence_type_id')->unsigned()->nullable()->after('rating');
			});
		}

		// Missing field????
		if (!Capsule::schema()->hasColumn('apprenticeship_standards', 'sort')) {
			Capsule::schema()->table('apprenticeship_standards', function ($table) {
				$table->integer('sort')->unsigned()->nullable()->after('status');
			});
		}
		// Description field
		if (!Capsule::schema()->hasColumn('apprenticeship_standards', 'description')) {
			Capsule::schema()->table('apprenticeship_standards', function ($table) {
				$table->text('description')->nullable()->after('name');
			});
		}

		// Add type to apprenticeship standards, type will influence some functionality
		if (!Capsule::schema()->hasColumn('apprenticeship_standards', 'type')) {
			Capsule::schema()->table('apprenticeship_standards', function ($table) {
				$table->enum('type', ['Standards', 'Frameworks', 'Qualifications'])->default('Standards')->after('sort');
			});
		}

		// Add parent field, so that tree can be built!
		if (!Capsule::schema()->hasColumn('apprenticeship_issues', 'parent_id')) {
			Capsule::schema()->table('apprenticeship_issues', function ($table) {
				$table->integer('parent_id')->unsigned()->default(0)->after('start_day');
			});
		}

		// Subriteria guidelines description field and resource flag to hide resources in learning library.
		if (!Capsule::schema()->hasColumn('apprenticeship_issues', 'guidelines')) {
			Capsule::schema()->table('apprenticeship_issues', function ($table) {
				$table->string('guidelines', 2000)->after('parent_id');
			});
		}
		if (!Capsule::schema()->hasColumn('learning_modules', 'guideline')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->boolean('guideline')->default(false)->after('evidence_type_id');
			});
		}


		if (!Capsule::schema()->hasColumn('custom_reviews_filters', 'is_apprentix')) {
			Capsule::schema()->table('custom_reviews_filters', function ($table) {
				$table->boolean('is_apprentix')->default(0)->after('created_by');
			});
		}

		if (!Capsule::schema()->hasColumn('custom_reviews_filters', 'join')) {
			Capsule::schema()->table('custom_reviews_filters', function ($table) {
				$table->text('join')->after('inner_join');
			});
		}

		if (!Capsule::schema()->hasColumn('ilr_learning_deliveries', 'DPOutcome')) {
			Capsule::schema()->table('ilr_learning_deliveries', function ($table) {
				$table->text('DPOutcome')->nullable()->after('LearningDeliveryHE');
			});
		}

		if (!Capsule::schema()->hasColumn('custom_reviews_filters', 'complex_options')) {
			Capsule::schema()->table('custom_reviews_filters', function ($table) {
				$table->string('complex_options', 2000)->after('search_options');
			});
		}

		if (!Capsule::schema()->hasColumn('custom_reviews_filters', 'option_group')) {
			Capsule::schema()->table('custom_reviews_filters', function ($table) {
				$table->string('option_group', 100)->after('where');
			});
		}

		if (!Capsule::schema()->hasColumn('custom_reviews_filters', 'convert_to')) {
			Capsule::schema()->table('custom_reviews_filters', function ($table) {
				$table->string('convert_to', 100)->after('option_group');
			});
		}

		if (!Capsule::schema()->hasColumn('smcr_committees', 'live')) {
			Capsule::schema()->table('smcr_committees', function ($table) {
				$table->boolean('live')->default(0)->after('description');
			});
		}

		if (!Capsule::schema()->hasColumn('smcr_committees', 'total_count')) {
			Capsule::schema()->table('smcr_committees', function ($table) {
				$table->smallInteger('total_count')->unsigned()->after('live');
			});
		}

		if (!Capsule::schema()->hasColumn('smcr_committees', 'filled_count')) {
			Capsule::schema()->table('smcr_committees', function ($table) {
				$table->smallInteger('filled_count')->unsigned()->after('total_count');
			});
		}

		if (!Capsule::schema()->hasColumn('smcr_committees', 'missing_count')) {
			Capsule::schema()->table('smcr_committees', function ($table) {
				$table->smallInteger('missing_count')->unsigned()->after('filled_count');
			});
		}

		if (!Capsule::schema()->hasColumn('smcr_committees', 'percentage_filled')) {
			Capsule::schema()->table('smcr_committees', function ($table) {
				$table->smallInteger('percentage_filled')->unsigned()->after('missing_count');
			});
		}

		if (Capsule::schema()->hasColumn('smcr_committee_roles', 'personnel')) {
			Capsule::schema()->table('smcr_committee_roles', function ($table) {
				$table->dropColumn('personnel');
			});
		}
		if (Capsule::schema()->hasColumn('countries', 'position')) {
			Capsule::schema()->table('countries', function ($table) {
				$table->dropColumn('position');
			});
		}
		if (Capsule::schema()->hasColumn('countries', 'group_name')) {
			Capsule::schema()->table('countries', function ($table) {
				$table->dropColumn('group_name');
			});
		}

		if (!Capsule::schema()->hasColumn('roles', 'is_qa')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->boolean('is_qa')->default(false)->after('is_demo');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_results', 'qa')) {
			Capsule::schema()->table('learning_results', function ($table) {
				$table->enum('qa', ['Accepted', 'Rejected'])->nullable()->default(null)->after('completed_at');
			});
		}

		if (!Capsule::schema()->hasColumn('custom_reviews_filters', 'where_or')) {
			Capsule::schema()->table('custom_reviews_filters', function ($table) {
				$table->string('where_or', 1000)->after('where');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_results_comments', 'qa')) {
			Capsule::schema()->table('learning_results_comments', function ($table) {
				$table->boolean('qa')->default(false)->after('is_read_by_user_id');
			});
		}

		if (!Capsule::schema()->hasColumn('roles', 'exclude_from_form')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->boolean('exclude_from_form')->default(false)->after('hide_set_training_tab');
			});
		}

		if (!Capsule::schema()->hasColumn('manager_reviews', 'completion_status')) {
			Capsule::schema()->table('manager_reviews', function ($table) {
				$table->enum('completion_status', ['Not Started', 'In Progress', 'Completed'])->nullable()->default('Not Started')->after('checked_by');
			});
		}

		if (!Capsule::schema()->hasColumn('users', 'school')) {
			Capsule::schema()->table('users', function ($table) {
				$table->string('school', 255)->nullable()->after('phone');
			});
		}

		if (Capsule::schema()->hasColumn('smcr_reports', 'archive')) {
			Capsule::schema()->table('smcr_reports', function ($table) {
				$table->dropColumn('archive');
			});
		}

		if (!Capsule::schema()->hasColumn('smcr_reports', 'completion_status')) {
			Capsule::schema()->table('smcr_reports', function ($table) {
				$table->enum('completion_status', ['Not Started', 'In Progress', 'Completed', 'Archived'])->default('Not Started')->after('expire_at');
			});
		}

		if (!Capsule::schema()->hasColumn('smcr_reports', 'start_at')) {
			Capsule::schema()->table('smcr_reports', function ($table) {
				$table->date('start_at')->after('user_id');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_modules', 'project')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->boolean('project')->default(false)->after('jackdaw');
			});
		}

		if (Capsule::schema()->hasColumn('roles', 'is_jackdaw')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->dropColumn('is_jackdaw');
			});
		}

		if (!Capsule::schema()->hasColumn('roles', 'is_learner')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->boolean('is_learner')->default(false)->after('is_manager');
			});
		}

		if (!Capsule::schema()->hasColumn('users', 'e_signature')) {
			Capsule::schema()->table('users', function ($table) {
				$table->string('e_signature', 255)->nullable()->after('image');
			});
		}

		if (!Capsule::schema()->hasColumn('users', 'emergency_name')) {
			Capsule::schema()->table('users', function ($table) {
				$table->string('emergency_name', 255)->nullable()->after('manager_percentage_behind');
			});
		}
		if (!Capsule::schema()->hasColumn('users', 'emergency_relationship')) {
			Capsule::schema()->table('users', function ($table) {
				$table->string('emergency_relationship', 255)->nullable()->after('emergency_name');
			});
		}
		if (!Capsule::schema()->hasColumn('users', 'emergency_contact_numbers')) {
			Capsule::schema()->table('users', function ($table) {
				$table->string('emergency_contact_numbers', 1000)->nullable()->after('emergency_relationship');
			});
		}

		if (!Capsule::schema()->hasColumn('users', 'visa_length')) {
			Capsule::schema()->table('users', function ($table) {
				$table->enum('visa_length', ['Under 3 years', 'Over 3 years'])->nullable()->after('emergency_contact_numbers');
			});
		}
		if (!Capsule::schema()->hasColumn('users', 'visa_number')) {
			Capsule::schema()->table('users', function ($table) {
				$table->string('visa_number', 255)->nullable()->after('visa_length');
			});
		}
		if (!Capsule::schema()->hasColumn('users', 'visa_date')) {
			Capsule::schema()->table('users', function ($table) {
				$table->date('visa_date')->nullable()->after('visa_number');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_results', 'sign_off_manager_by')) {
			Capsule::schema()->table('learning_results', function ($table) {
				$table->integer('sign_off_manager_by')->unsigned()->nullable()->after('sign_off_manager');
			});
		}

		if (!Capsule::schema()->hasColumn('users', 'last_contact_date')) {
			Capsule::schema()->table('users', function ($table) {
				$table->date('last_contact_date')->nullable()->after('next_completion_date');
			});
		}

		if (!Capsule::schema()->hasColumn('users', 'LearnerDestinationandProgression')) {
			Capsule::schema()->table('users', function ($table) {
				$table->text('LearnerDestinationandProgression')->nullable()->after('LearningDelivery');
			});
		}

		if (!Capsule::schema()->hasColumn('email_queue', 'comment')) {
			Capsule::schema()->table('email_queue', function ($table) {
				$table->text('comment')->nullable()->after('get_users_url');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_results', 'sign_off_trainee_at')) {
			Capsule::schema()->table('learning_results', function ($table) {
				$table->date('sign_off_trainee_at')->nullable()->after('sign_off_trainee');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_results', 'sign_off_manager_at')) {
			Capsule::schema()->table('learning_results', function ($table) {
				$table->date('sign_off_manager_at')->nullable()->after('sign_off_manager');
			});
	}

		if(Capsule::schema()->hasColumn('custom_report_fields','type')) {
			DB::statement("ALTER TABLE custom_report_fields MODIFY COLUMN `type` ENUM('text', 'number', 'boolean', 'array', 'date','date-range') NULL DEFAULT NULL");
		}

		// Add is_paid
		if (!Capsule::schema()->hasColumn('schedule_links', 'is_paid')) {
			Capsule::schema()->table('schedule_links', function ($table) {
				$table->string('is_paid')->nullable()->after('completion_status')->comment = "is_paid is for identify paid status of schedule(0 for Non payment and 1 for paid and Null for no updates)";
			});
		}

		// Add is_paid
		if (!Capsule::schema()->hasColumn('learning_results', 'is_paid')) {
			Capsule::schema()->table('learning_results', function ($table) {
				$table->string('is_paid')->nullable()->after('grace_at')->comment = "is_paid is for identify paid status of schedule(0 for Non payment and 1 for paid and Null for no updates)";;
			});
		}

		// Add index for "scorm_scorm_scoes_track" field "timemodified".
		// LEAVE THIS ONE HERE AS LONG AS "scorm_scorm_scoes_track" TABLE EXISTS.
		$scorm_track_index = DB::select('SHOW KEYS FROM scorm_scorm_scoes_track WHERE Key_name= ?', ['scorm_scorm_scoes_track_timemodified_index']);
		if (count($scorm_track_index) == 0) {
			Capsule::schema()->table('scorm_scorm_scoes_track', function ($table) {
				$table->index('timemodified');
			});
		}

		//Remove existing graph tables
		Capsule::schema()->dropIfExists("custom_graph_fields");
		Capsule::schema()->dropIfExists("custom_graphs");
		// Considilation of tables, drop existing ones!
		Capsule::schema()->dropIfExists('smcr_staff_functions');
		Capsule::schema()->dropIfExists('smcr_staff_responsibilities');
		Capsule::schema()->dropIfExists('smcr_functions');
		Capsule::schema()->dropIfExists('smcr_responsibilities');

		if(!Capsule::schema()->hasColumn('graphs','data_limit'))
		{
		  Capsule::schema()->table('graphs',function($table){
			  $table->integer('data_limit')->unsigned()->nullable()->after('category');
		  });
		}

		//update statistics table - add data field with current values
		if (!Capsule::schema()->hasColumn('statistics', 'data')) {
			Capsule::schema()->table('statistics', function ($table) {
				$table->text('data')->nullable()->after('name');
			});
		}

		//update statistics table - add is_online flag
		if (!Capsule::schema()->hasColumn('statistics', 'is_online')) {
			Capsule::schema()->table('statistics', function ($table) {
				$table->boolean('is_online')->default(0)->after('data');
			});
		}

		//update dasboards table - add data field to denote staff type preference
		if (!Capsule::schema()->hasColumn('dashboards', 'staff_type_id')) {
			Capsule::schema()->table('dashboards', function ($table) {
				$table->integer('staff_type_id')->unsigned()->nullable()->after('designation_id');
			});
		}

		////update dasboards table - add data field to denote staff type preference
		if (!Capsule::schema()->hasColumn('user_workflow_form', 'user_id')) {
			Capsule::schema()->table('user_workflow_form', function ($table) {
				$table->integer('user_id')->unsigned()->after('id');
				$table->foreign('user_id')->references('id')->on('users');
			});
		}


		if (Capsule::schema()->hasColumn('user_workflow_form', 'user_form_id')) {
			Capsule::schema()->table('user_workflow_form', function ($table) {
				$table->dropColumn('user_form_id');
			});
		}

		//update dataviews table - add field to denote dataviews which are computed online
		if (!Capsule::schema()->hasColumn('dataviews', 'is_online')) {
			Capsule::schema()->table('dataviews', function ($table) {
				$table->boolean('is_online')->default(0)->after('name_id');
			});
		}

		if (!Capsule::schema()->hasColumn('dataviews', 'is_online')) {
			Capsule::schema()->table('dataviews', function ($table) {
				$table->boolean('is_online')->default(0)->after('name_id');
			});
		}

		// Is sign off params
		if (!Capsule::schema()->hasColumn('user_custom_form_values', 'sign_off_trainee')) {
			Capsule::schema()->table('user_custom_form_values', function ($table) {
				$table->boolean('sign_off_trainee')->default(false)->after('values');
				$table->date('sign_off_trainee_at')->nullable()->after('values');
				$table->boolean('sign_off_manager')->default(false)->after('values');
				$table->date('sign_off_manager_at')->nullable()->after('values');
				$table->integer('sign_off_manager_by')->unsigned()->nullable()->after('values');
			});
		}

		if (!Capsule::schema()->hasColumn('user_forms', 'user_custom_form_value_id')) {
			Capsule::schema()->table('user_forms', function ($table) {
				$table->integer('user_custom_form_value_id')->unsigned()->nullable()->after('form_id');
			});
			Capsule::schema()->table('user_forms', function ($table) {
				$table->foreign('user_custom_form_value_id')->references('id')->on('user_custom_form_values');
			});
		}
		if(!Capsule::schema()->hasColumn('user_forms','form_user_structure_id')){
			Capsule::schema()->table('user_forms',function ($table){
			   $table->integer('form_user_structure_id')->unsigned()->nullable()->after('user_custom_form_value_id');
			});
		}

		if(!Capsule::schema()->hasColumn('user_forms','form_user_structure_id')){
			Capsule::schema()->table('user_forms',function ($table){
				$table->foreign('form_user_structure_id')->references('id')->on('form_user_structures');
			});
		}

		if(!Capsule::schema()->hasColumn('user_forms','learner_completion_status'))
		{
		  Capsule::schema()->table('user_forms',function($table){
			  $table->boolean('learner_completion_status')
			  ->default('0')->after('status');
		  });
		}

		if(!Capsule::schema()->hasColumn('user_forms','manager_completion_status'))
		{
		  Capsule::schema()->table('user_forms',function($table){
			  $table->boolean('manager_completion_status')
			  ->default('0')->after('status');
		  });
		}

		if(!Capsule::schema()->hasColumn('user_forms','completion_status'))
		{
		  Capsule::schema()->table('user_forms',function($table){
			  $table->boolean('completion_status')
			  ->default('0')->after('status');
		  });
		}

		if (!Capsule::schema()->hasColumn('user_forms', 'type')) {
			Capsule::schema()->table('user_forms', function ($table) {
				$table->string('type',150)->default('direct')->after('form_id');
			});
			Capsule::schema()->table('user_forms', function ($table) {
				$table->integer('type_id')->unsigned()->nullable()->after('form_id');
			});
		}
		if(!Capsule::schema()->hasColumn('document_template_bindings','is_signature'))
	{
		Capsule::schema()->table('document_template_bindings',function($table){
		  $table->boolean('is_signature')->default(false)->after('binding_field');
		  $table->integer('form_signoff_role_id')->nullabe()->default(NULL)->after('binding_field');
	  });
	}
	if(Capsule::schema()->hasColumn('document_template_bindings','form_field_id'))
	{
		Capsule::schema()->table('document_template_bindings',function($table){
		  DB::statement("ALTER TABLE `document_template_bindings` CHANGE  `form_field_id` `form_field_id`  integer NULL;");
		  DB::statement("ALTER TABLE `document_template_bindings` CHANGE  `form_signoff_role_id` `form_signoff_role_id` integer NULL;");
	  });
	}
	if(Capsule::schema()->hasColumn('custom_field_values','value'))
	{
	  Capsule::schema()->table('custom_field_values',function(){
		DB::statement('ALTER TABLE `custom_field_values` CHANGE `value` `value` text NULL;');
	  });

	}
	if(!Capsule::schema()->hasColumn('fields', 'display_type')){
		Capsule::schema()->table('fields',function($table){
			$table->text('display_type')->comment("Display condtions")->default("[]");
		});
	}


		//update user_payment_transactions table - add data field to set mail sent status is_sent
		if (!Capsule::schema()->hasColumn('user_payment_transactions', 'is_sent')) {
			Capsule::schema()->table('user_payment_transactions', function ($table) {
				$table->boolean('is_sent')->default(false)->after('status');
			});
		}
		//update user_payment_transactions table - add data field to set test status is_test
		if (!Capsule::schema()->hasColumn('user_payment_transactions', 'is_test')) {
			Capsule::schema()->table('user_payment_transactions', function ($table) {
				$table->boolean('is_test')->default(false)->after('is_sent');
			});
		}
		//update user_payment_transactions table - add field payer_organisation_id to store organisation id of the payer.
		if (!Capsule::schema()->hasColumn('user_payment_transactions', 'payer_organisation_id')) {
			Capsule::schema()->table('user_payment_transactions', function ($table) {
				$table->integer('payer_organisation_id')->unsigned()->nullable()->after('user_id');
			});
		}
		if (!Capsule::schema()->hasColumn('form_fields', 'is_assigned')) {
			Capsule::schema()->table('form_fields', function ($table) {
				$table->boolean('is_assigned')->nullable()->after('is_mandatory')->comment ="Its for event and programme category";
			});
		}
		if (!Capsule::schema()->hasColumn('form_field_author_permissions', 'permission')) {
			Capsule::schema()->table('form_field_author_permissions', function ($table) {
				$table->enum('permission',['read','write','hide'])->after('status')->nullable()->comment ="Read Write Hide Access Permission";
			});
		}
		if (!Capsule::schema()->hasColumn('form_fields', 'is_read_write')) {
			Capsule::schema()->table('form_fields', function ($table) {
				$table->boolean('is_read_write')->nullable()->after('is_assigned')->comment ="Read Write Permission";
			});
		}

		if (!Capsule::schema()->hasColumn('form_fields', 'permission')) {
			Capsule::schema()->table('form_fields', function ($table) {
				$table->enum('permission',['read','write','hide'])->after('is_read_write')->nullable()->comment ="Read Write Hide Access Permission";
			});
		}
		if (!Capsule::schema()->hasColumn('form_fields', 'ilr_slug')) {
			Capsule::schema()->table('form_fields', function ($table) {
				$table->string('ilr_slug')->nullable()->after('is_mandatory')->comment ="Only available if ilr field is selected";
			});
		}

		if (!Capsule::schema()->hasColumn('forms', 'has_sign_off')) {
			Capsule::schema()->table('forms', function ($table) {
				$table->boolean('has_sign_off')->default(0)->after('slug')->comment ="check form needs sign off";
			});
		}

		if (!Capsule::schema()->hasColumn('forms', 'has_learner_to_assign')) {
			Capsule::schema()->table('forms', function ($table) {
				$table->boolean('has_learner_to_assign')->default(0)->after('has_sign_off')->comment ="check form has permission to allow learner to assign";
			});
		}

		if (!Capsule::schema()->hasColumn('fields', 'isApprentix')) {
			Capsule::schema()->table('fields', function ($table) {
				$table->boolean('isApprentix')->default(false)->after('custom');
			});
		}
		if (!Capsule::schema()->hasColumn('fields', 'model')) {
			Capsule::schema()->table('fields', function ($table) {
				$table->string('model')->nullable()->after('custom');
				$table->string('value')->nullable()->after('custom');
				$table->string('label')->nullable()->after('custom');
			});
		}

		if (!Capsule::schema()->hasColumn('fields', 'option_group')) {
			Capsule::schema()->table('fields', function ($table) {
				$table->string('option_group')->nullable()->after('isApprentix');
			});
		}
	if(!Capsule::schema()->hasColumn('user_workflow_form','type'))
	{
	  Capsule::schema()->table('user_workflow_form',function($table){
		  $table->string('type')->nullable()->after("form_workflow_id");
	  });
	}
	if (!Capsule::schema()->hasColumn('user_workflow_form', 'type_id')) {
				Capsule::schema()->table('user_workflow_form', function ($table) {
					$table->integer('type_id')->unsigned()->nullable()->after('type');
				});
			}

	if(Capsule::schema()->hasColumn('document_templates','template'))
	{
		DB::statement("ALTER TABLE document_templates MODIFY COLUMN template mediumtext;");
	}
	if(!Capsule::schema()->hasColumn('department_learning_modules','cron_status'))
	  {
		  Capsule::schema()->table('department_learning_modules',function($table){
			  $table->boolean('cron_status')->default(false); //for running cron task
			  $table->boolean('removed_status')->default(false); // check whether departments  need to show or not
		  });
	  }
	if(!Capsule::schema()->hasColumn('designation_learning_modules','cron_status'))
	  {
		  Capsule::schema()->table('designation_learning_modules',function($table){
			  $table->boolean('cron_status')->default(false); //for running cron task
			  $table->boolean('removed_status')->default(false); // check whether departments  need to show or not
		  });
	  }
	if(!Capsule::schema()->hasColumn('group_learning_modules','cron_status'))
	  {
		  Capsule::schema()->table('group_learning_modules',function($table){
			  $table->boolean('cron_status')->default(false); //for running cron task
			  $table->boolean('removed_status')->default(false); // check whether departments  need to show or not
		  });
	  }

		// Custom date, used for apprentix, to show in learner's calendar.
		if (!Capsule::schema()->hasColumn('learning_results', 'completion_date_custom')) {
			Capsule::schema()->table('learning_results', function ($table) {
				$table->date('completion_date_custom')->nullable()->after('due_at');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_results', 'form_data')) {
			Capsule::schema()->table('learning_results', function ($table) {
				$table->json('form_data')->nullable()->after('completion_date_custom');
			});
		}

		if (!Capsule::schema()->hasColumn('table_history', 'before')) {
			Capsule::schema()->table('table_history', function ($table) {
				$table->text('before')->after('table_name');
			});
		}

		if (!Capsule::schema()->hasColumn('table_history', 'after')) {
			Capsule::schema()->table('table_history', function ($table) {
				$table->text('after')->after('before');
			});
		}

		if (Capsule::schema()->hasColumn('table_history', 'table_data')) {
			Capsule::schema()->table('table_history', function ($table) {
				$table->dropColumn('table_data');
			});
		}

		if (!Capsule::schema()->hasColumn('email_queue', 'custom_data')) {
			Capsule::schema()->table('email_queue', function ($table) {
				$table->text('custom_data')->nullable()->after('comment');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_module_evidences', 'hash')) {
			Capsule::schema()->table('learning_module_evidences', function ($table) {
				$table->string('hash')->after('evidence');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_module_evidences', 'extension')) {
			Capsule::schema()->table('learning_module_evidences', function ($table) {
				$table->string('extension')->after('hash');
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_standards_users', 'completed_at')) {
			Capsule::schema()->table('apprenticeship_standards_users', function ($table) {
				$table->dateTime('completed_at')->nullable()->after('due_at');
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_standards_users', 'last_update')) {
			Capsule::schema()->table('apprenticeship_standards_users', function ($table) {
				$table->date('last_update')->nullable()->after('completed_at');
			});
		}

		if (!Capsule::schema()->hasColumn('roles', 'image')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->string('image')->after('sign_off_learner_status');
			});
		}
		if (!Capsule::schema()->hasColumn('roles', 'description')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->text('description')->after('image');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_results', 'is_rejected')) {
			Capsule::schema()->table('learning_results', function ($table) {
				$table->boolean('is_rejected')->after('is_paid');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_results', 'off_the_job_hours')) {
			Capsule::schema()->table('learning_results', function ($table) {
				$table->boolean('off_the_job_hours')->default(false)->after('duration_minutes');
			});
		}

		//Add skype id to users' profile
		if (!Capsule::schema()->hasColumn('users', 'skype_id')) {
			Capsule::schema()->table('users', function ($table) {
				$table->string('skype_id', 100)->nullable()->after("email");
			});
		}
		//Add zoom id to users' profile
		if (!Capsule::schema()->hasColumn('users', 'zoom_id')) {
			Capsule::schema()->table('users', function ($table) {
				$table->string('zoom_id', 100)->nullable()->after("skype_id");
			});
		}
		//Add teams id to users' profile
		if (!Capsule::schema()->hasColumn('users', 'teams_id')) {
			Capsule::schema()->table('users', function ($table) {
				$table->string('teams_id', 400)->nullable()->after("zoom_id");
			});
		}

		//Add alternative email to users' profile
		if (!Capsule::schema()->hasColumn('users', 'email2')) {
			Capsule::schema()->table('users', function ($table) {
				$table->string('email2', 100)->nullable()->after("teams_id");
			});
		}

		if (!Capsule::schema()->hasColumn('users', 'dynamic_fields')) {
			Capsule::schema()->table('users', function ($table) {
				$table->text('dynamic_fields')->default("[]")->after('email2');
			});
		}

		//Add alternative id code to users' profile
		if (!Capsule::schema()->hasColumn('users', 'altusercode')) {
			Capsule::schema()->table('users', function ($table) {
				$table->string('altusercode', 255)->nullable()->after("usercode");
			});
		}

		// Add two more roles! "Curriculum Developer" and "Financial Auditor"
		if (!Capsule::schema()->hasColumn('roles', 'is_cd')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->boolean('is_cd')->default(false)->after('is_qa');
			});
		}
		if (!Capsule::schema()->hasColumn('roles', 'is_fa')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->boolean('is_fa')->default(false)->after('is_cd');
			});
		}

		if (!Capsule::schema()->hasColumn('smcr_staff_types', 'refresh_assesment')) {
			Capsule::schema()->table('smcr_staff_types', function ($table) {
				$table->smallInteger('refresh_assesment')->unsigned()->nullable()->default(365)->after('name');
			});
		}

		// Few more fields for table "smcr_staff_functions_responsibilities".
		if (!Capsule::schema()->hasColumn('smcr_staff_functions_responsibilities', 'completion_date')) {
			Capsule::schema()->table('smcr_staff_functions_responsibilities', function ($table) {
				$table->date('completion_date')->nullable()->after('status');
			});
		}
		if (!Capsule::schema()->hasColumn('smcr_staff_functions_responsibilities', 'learner_sign_off')) {
			Capsule::schema()->table('smcr_staff_functions_responsibilities', function ($table) {
				$table->date('learner_sign_off')->nullable()->after('completion_date');
			});
		}
		if (!Capsule::schema()->hasColumn('smcr_staff_functions_responsibilities', 'rejected')) {
			Capsule::schema()->table('smcr_staff_functions_responsibilities', function ($table) {
				$table->date('rejected')->nullable()->after('learner_sign_off');
			});
		}
		if (!Capsule::schema()->hasColumn('smcr_staff_functions_responsibilities', 'rejected_by')) {
			Capsule::schema()->table('smcr_staff_functions_responsibilities', function ($table) {
				$table->integer('rejected_by')->unsigned()->after('rejected');
			});
		}
		if (!Capsule::schema()->hasColumn('smcr_staff_functions_responsibilities', 'accepted_by')) {
			Capsule::schema()->table('smcr_staff_functions_responsibilities', function ($table) {
				$table->integer('accepted_by')->unsigned()->after('rejected_by');
			});
		}


		if (Capsule::schema()->hasColumn('designations', 'lesson_id')) {
			Capsule::schema()->table('designations', function ($table) {
				$table->dropColumn('lesson_id');
			});
		}

		if (!Capsule::schema()->hasColumn('designations', 'order_resources')) {
			Capsule::schema()->table('designations', function ($table) {
				$table->boolean('order_resources')->after('name');
			});
		};

		//Add chart type to dashboard dataviews
		if (!Capsule::schema()->hasColumn('dashboard_dataviews', 'chart_type')) {
			Capsule::schema()->table('dashboard_dataviews', function ($table) {
				$table->string('chart_type')->default("pie")->after("display_size");
			});
		}

		// Add field in user's table to record date when designation/job/staff type was assigned to user.
		if (!Capsule::schema()->hasColumn('users', 'designation_assigned')) {
			Capsule::schema()->table('users', function ($table) {
				$table->date("designation_assigned")->nullable()->after("designation_id");
			});
		}
		if (!Capsule::schema()->hasColumn('users', 'staff_type_assigned')) {
			Capsule::schema()->table('users', function ($table) {
				$table->date("staff_type_assigned")->nullable()->after("staff_type_id");
			});
		}

		// Rename comments table's fields to more generic names.
		if (Capsule::schema()->hasColumn('comments', 'comment_by')) {
			Capsule::schema()->table('comments', function ($table) {
				$table->renameColumn('comment_by', 'added_by');
			});
		}
		if (Capsule::schema()->hasColumn('comments', 'comment_for')) {
			Capsule::schema()->table('comments', function ($table) {
				$table->renameColumn('comment_for', 'added_for');
			});
		}

		if (!Capsule::schema()->hasColumn('smcr_committee_role_personnel', 'completion_status')) {
			Capsule::schema()->table('smcr_committee_role_personnel', function ($table) {
				$table->enum('completion_status', ['Assigned', 'Rejected', 'Accepted'])->default('Assigned')->after("assigned");
			});
		}

		if (!Capsule::schema()->hasColumn('email_templates', 'copy_email_to_managers')) {
			Capsule::schema()->table('email_templates', function ($table) {
				$table->boolean('copy_email_to_managers')->default(false)->after("is_temporary");
			});
		}


		if (!Capsule::schema()->hasColumn('learning_modules', 'is_skillscan')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->boolean('is_skillscan')->default(false)->after("guideline");
			});
		}
		if (!Capsule::schema()->hasColumn('learning_modules', 'print_lesson')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->boolean('print_lesson')->default(true)->after("self_enroll");
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_standards_users', 'time_behind')) {
			Capsule::schema()->table('apprenticeship_standards_users', function ($table) {
				$table->decimal('time_behind', 6, 2)->default(0)->after("time_spent");
			});
		}

		if (!Capsule::schema()->hasColumn('custom_reviews', 'option_group')) {
			Capsule::schema()->table('custom_reviews', function ($table) {
				$table->string('option_group', 100)->after("table_state");
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_standards_users', 'actual_cost')) {
			Capsule::schema()->table('apprenticeship_standards_users', function ($table) {
				$table->integer('actual_cost')->nullable()->after("last_update");
			});
		}
		if (!Capsule::schema()->hasColumn('apprenticeship_standards_users', 'criteria_completion')) {
			Capsule::schema()->table('apprenticeship_standards_users', function ($table) {
				$table->decimal('criteria_completion', 6, 2)->default(0)->after('on_schedule');
			});
		}

		if (!Capsule::schema()->hasColumn('smcr_committees', 'accepted_count')) {
			Capsule::schema()->table('smcr_committees', function ($table) {
				$table->smallInteger('accepted_count')->unsigned()->after('percentage_filled');
			});
		}

		if (!Capsule::schema()->hasColumn('smcr_committees', 'percentage_accepted')) {
			Capsule::schema()->table('smcr_committees', function ($table) {
				$table->smallInteger('percentage_accepted')->unsigned()->after('accepted_count');
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_issue_categories', 'sort')) {
			Capsule::schema()->table('apprenticeship_issue_categories', function ($table) {
				$table->integer('sort')->unsigned()->nullable()->after('status');
			});
		}
		if (!Capsule::schema()->hasColumn('apprenticeship_issue_categories', 'exclude_outcome')) {
			Capsule::schema()->table('apprenticeship_issue_categories', function ($table) {
				$table->boolean('exclude_outcome')->default(false)->after('sort');
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_issue_categories', 'disabled')) {
			Capsule::schema()->table('apprenticeship_issue_categories', function ($table) {
				$table->boolean('disabled')->default(false)->after('exclude_outcome');
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_issues', 'hide_progressbar')) {
			Capsule::schema()->table('apprenticeship_issues', function ($table) {
				$table->boolean('hide_progressbar')->default(false)->after('hide_learner');
			});
		}
		if (!Capsule::schema()->hasColumn('apprenticeship_issue_categories', 'hide_progressbar')) {
			Capsule::schema()->table('apprenticeship_issue_categories', function ($table) {
				$table->boolean('hide_progressbar')->default(false)->after('exclude_outcome');
			});
		}
		if (!Capsule::schema()->hasColumn('apprenticeship_issue_categories', 'hide_progressbar')) {
			Capsule::schema()->table('apprenticeship_issue_categories', function ($table) {
				$table->boolean('hide_progressbar')->default(false)->after('exclude_outcome');
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_standards_users', 'ilr_start_at')) {
			Capsule::schema()->table('apprenticeship_standards_users', function ($table) {
				$table->date('ilr_start_at')->nullable()->after('start_at');
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_standards_users', 'ilr_end_at')) {
			Capsule::schema()->table('apprenticeship_standards_users', function ($table) {
				$table->date('ilr_end_at')->nullable()->after('ilr_start_at');
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_standards_users', 'ilr_completion_months')) {
			Capsule::schema()->table('apprenticeship_standards_users', function ($table) {
				$table->integer('ilr_completion_months')->nullable()->after('ilr_end_at');
			});
		}

		if (!Capsule::schema()->hasColumn('users', 'exclude_from_reports')) {
			Capsule::schema()->table('users', function ($table) {
				$table->boolean('exclude_from_reports')->default(false)->after('updated_by');
			});
		}

		if (!Capsule::schema()->hasColumn('custom_reviews_filters', 'left_join_extended')) {
			Capsule::schema()->table('custom_reviews_filters', function ($table) {
				$table->string('left_join_extended', 2000)->after('complex_options');
			});
		}

		if (Capsule::schema()->hasColumn('custom_reviews_filters', 'select_raw')) {
			Capsule::schema()->table('custom_reviews_filters', function ($table) {
				$table->text('select_raw')->change();
			});
		}

		if (!Capsule::schema()->hasColumn('custom_reviews_filters', 'date_selector')) {
			Capsule::schema()->table('custom_reviews_filters', function ($table) {
				$table->boolean('date_selector')->default(false)->after('is_apprentix');
			});
		}


		if (!Capsule::schema()->hasColumn('custom_reviews_filters', 'hide_print')) {
			Capsule::schema()->table('custom_reviews_filters', function ($table) {
				$table->boolean('hide_print')->default(false)->after('date_selector');
			});
		}

		if (!Capsule::schema()->hasColumn('custom_reviews_filters', 'disable_review')) {
			Capsule::schema()->table('custom_reviews_filters', function ($table) {
				$table->boolean('disable_review')->default(false)->after('hide_print');
			});
		}

		if (!Capsule::schema()->hasColumn('email_queue', 'frequency_pattern')) {
			Capsule::schema()->table('email_queue', function ($table) {
				$table->text('frequency_pattern')->nullable()->default(null)->after('frequency');
			});
		}


		if (!Capsule::schema()->hasColumn('custom_reviews', 'slug')) {
			Capsule::schema()->table('custom_reviews', function ($table) {
				$table->string('slug')->after('created_by');
			});
		}
		if (!Capsule::schema()->hasColumn('custom_reviews', 'export_fields')) {
			Capsule::schema()->table('custom_reviews', function ($table) {
				$table->text('export_fields')->after('slug');
			});
		}
		if (!Capsule::schema()->hasColumn('custom_reviews', 'download_file_name')) {
			Capsule::schema()->table('custom_reviews', function ($table) {
				$table->string('download_file_name')->after('export_fields');
			});
		}

		if (!Capsule::schema()->hasColumn('custom_reviews', 'relationships')) {
			Capsule::schema()->table('custom_reviews', function ($table) {
				$table->text('relationships')->after('export_fields_types');
			});
		}

		if (!Capsule::schema()->hasColumn('batch_reports', 'args')) {
			Capsule::schema()->table('batch_reports', function ($table) {
				$table->text('args')->nullable()->after('table_state_original');
			});
		}

		if (!Capsule::schema()->hasColumn('batch_report_data', 'unread')) {
			Capsule::schema()->table('batch_report_data', function ($table) {
				$table->boolean('unread')->default(true)->after('data');
			});
		}

		if (Capsule::schema()->hasColumn('batch_report_data', 'batch_slug')) {
			Capsule::schema()->table('batch_report_data', function ($table) {
				$table->dropForeign('batch_report_data_batch_slug_foreign');
				$table->renameColumn('batch_slug', 'batch_report_id');
				$table->foreign('batch_report_id')->references('id')->on('batch_reports')->onDelete('cascade');
			});
		}

		// Add index to batch_reports "title"
		$batch_reports_index = DB::select('SHOW KEYS FROM batch_reports WHERE Key_name= ?', ['batch_reports_title_index']);
		if (count($batch_reports_index) == 0) {
			Capsule::schema()->table('batch_reports', function ($table) {
				$table->index('title');
			});
		}

		// rename 'review' to 'report'
		$batch_reports = \Models\BatchReport::where('type', 'review')
			->get();
		if (Capsule::schema()->hasColumn('batch_reports', 'type')) {
			Capsule::schema()->table('batch_reports', function ($table) {
				DB::statement("ALTER TABLE batch_reports CHANGE COLUMN type type ENUM('report', 'email') NULL DEFAULT NULL COLLATE 'utf8_unicode_ci' AFTER `run_date`");
			});
		}
		foreach ($batch_reports as $key => $batch_report) {
			$update_report = \Models\BatchReport::find($batch_report->id);
			$update_report->type = 'report';
			$update_report->save();
		}


		// Add "status" to quality_controls
		if (!Capsule::schema()->hasColumn('quality_controls', 'is_new')) {
			Capsule::schema()->table('quality_controls', function ($table) {
				$table->boolean('is_new')->default(true)->after('judgement_reason');
			});
		}

//rename query to value
		if (Capsule::schema()->hasColumn('custom_field_queries', 'query')) {
			Capsule::schema()->table('custom_field_queries', function ($table) {
				$table->string('type')->after('query');
				$table->renameColumn('query', 'value');
				$table->renameColumn('form_fields', 'form_field_id');
			});
		}

//rename form_fields to form_field_id

		if (Capsule::schema()->hasColumn('custom_field_queries', 'form_fields')) {
			Capsule::schema()->table('custom_field_queries', function ($table) {
				$table->renameColumn('form_fields', 'form_field_id');
			});
		}

		if (!Capsule::schema()->hasColumn('form_fields', 'has_default_value')) {
			Capsule::schema()->table('form_fields', function ($table) {
				$table->boolean('has_default_value')->nullable()->after('is_mandatory')->comment ="Check default value or not";
			});
		}

		if (!Capsule::schema()->hasColumn('form_fields', 'position')) {
			Capsule::schema()->table('form_fields', function ($table) {
				$table->integer('position')->nullable()->after('has_default_value');
			});
		}

		if (!Capsule::schema()->hasColumn('user_forms', 'refreshed_date')) {
			Capsule::schema()->table('user_forms', function ($table) {
				$table->datetime('refreshed_date')->nullable()->after('completion_status');
			});
		}




		if (Capsule::schema()->hasColumn('quality_controls', 'is_new')) {

			Capsule::statement("create or replace view quality_controls_report_view AS SELECT
		DISTINCT qc.id,
		qc.user_id,
		qc.type,
		qc.type_id,
		qc.qa,
		qc.judgement_reason,
		qc.created_at,
		qc.updated_at,
		qc.qa_user_id,
		type_name,
		standard_name,
		standard_id,
		apprenticeship_issue_categories_id,
		CASE WHEN qc.type = 'apprenticeship_issue_categories' then 'Outcome' WHEN qc.type = 'apprenticeship_issues' then 'Criteria' else 'Subcriteria' end AS qctype,
		CASE WHEN qc.qa_favorite = 1 then 'yes' else 'no' end AS qa_favorite,
		CONCAT(
		  (
			SELECT
			  qctype
		  ),
		  ' ',
		  UPPER(qc.qa),
		  ' because ',
		  LOWER(qc.judgement_reason)
		) AS qa_details,
		CONCAT(ul.fname, ' ', ul.lname) AS learner_name,
		CONCAT(uq.fname, ' ', uq.lname) AS qa_user_name
	  FROM
		quality_controls qc
		JOIN users ul on ul.id = qc.user_id
		JOIN users uq on uq.id = qc.qa_user_id
		JOIN (
		  (
			SELECT
			  outcome.id as apprenticeship_issue_categories_id,
			  standard.id as standard_id,
			  standard.name as standard_name,
			  outcome.name AS type_name,
			  outcome.id,
			  'apprenticeship_issue_categories' AS type
			FROM
			  apprenticeship_issue_categories outcome
			  JOIN apprenticeship_standards standard ON outcome.standard_id = standard.id
			WHERE
			  outcome.status = 1
		  )
		  UNION
			(
			  SELECT
				aic.id as apprenticeship_issue_categories_id,
				standard.id as standard_id,
				standard.name as standard_name,
				issue.name AS type_name,
				issue.id,
				'apprenticeship_issues' AS type
			  FROM
				apprenticeship_issues issue
				JOIN apprenticeship_issue_categories aic ON issue.issue_category_id = aic.id
				JOIN apprenticeship_standards standard ON aic.standard_id = standard.id
			  WHERE
				issue.status = 1
			)
		  UNION
			(
			  SELECT
				aic.id as apprenticeship_issue_categories_id,
				standard.id as standard_id,
				standard.name as standard_name,
				sub_issue.name AS type_name,
				sub_issue.id,
				'apprenticeship_sub_issues' AS type
			  FROM
				apprenticeship_issues sub_issue
				JOIN apprenticeship_issue_categories aic ON sub_issue.issue_category_id = aic.id
				JOIN apprenticeship_standards standard ON aic.standard_id = standard.id
			  WHERE
				sub_issue.status = 1
			)
		) ids ON qc.type_id = ids.id
		AND qc.type = ids.type
		AND qc.is_new = 1
		");
		}

		// Add "status" to labels table.
		if (!Capsule::schema()->hasColumn('labels', 'status')) {
			Capsule::schema()->table('labels', function ($table) {
				$table->boolean('status')->default(true)->after('to_text');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_modules', 'track_progress')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->boolean('track_progress')->default(true)->after('is_skillscan');
			});
		}
		if (!Capsule::schema()->hasColumn('learning_modules', 'created_in_learner_interface')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->boolean('created_in_learner_interface')->default(false)->after('track_progress');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_results', 'learner_action')) {
			Capsule::schema()->table('learning_results', function ($table) {
				$table->boolean('learner_action')->default(false)->after('qa');
			});
		}
		if (!Capsule::schema()->hasColumn('learning_results', 'judgement_reason')) {
			Capsule::schema()->table('learning_results', function ($table) {
				$table->text('judgement_reason')->nullable()->after('qa');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_results', 'learner_action_date')) {
			Capsule::schema()->table('learning_results', function ($table) {
				$table->datetime('learner_action_date')->nullable()->after('learner_action');
			});
		}
		if (!Capsule::schema()->hasColumn('learning_results', 'manager_action')) {
			Capsule::schema()->table('learning_results', function ($table) {
				$table->boolean('manager_action')->default(false)->after('learner_action_date');
			});
		}
		if (!Capsule::schema()->hasColumn('learning_results', 'manager_action_date')) {
			Capsule::schema()->table('learning_results', function ($table) {
				$table->datetime('manager_action_date')->nullable()->after('learner_action_date');
			});
		}
		if (!Capsule::schema()->hasColumn('learning_results', 'processing_manager_id')) {
			Capsule::schema()->table('learning_results', function ($table) {
				$table->integer('processing_manager_id')->unsigned()->nullable()->after('manager_action_date');
			});
		}

		if (!Capsule::schema()->hasColumn('users', 'previous_last_login_dt')) {
			Capsule::schema()->table('users', function ($table) {
				$table->dateTime("previous_last_login_dt")->nullable()->after('last_login_dt');
			});
		}

		if (!Capsule::schema()->hasColumn('email_queue', 'custom_variables')) {
			Capsule::schema()->table('email_queue', function ($table) {
				$table->text('custom_variables')->nullable()->default(null)->after('learning_module_id');
			});
		}

		//add export field types to custom reviews
		if (!Capsule::schema()->hasColumn('custom_reviews', 'export_fields_types')) {
			Capsule::schema()->table('custom_reviews', function ($table) {
				$table->text('export_fields_types')->after('export_fields');
			});
		}
		if (!Capsule::schema()->hasColumn('custom_reviews_filters', 'filter_options_sort')) {
			Capsule::schema()->table('custom_reviews_filters', function ($table) {
				$table->string('filter_options_sort', 100)->after('filter_options');
			});
		}

		if (!Capsule::schema()->hasColumn('users', 'exclude_from_ilr_export')) {
			Capsule::schema()->table('users', function ($table) {
				$table->boolean('exclude_from_ilr_export')->default(false)->after('exclude_from_reports');
			});
		}

		if (!Capsule::schema()->hasColumn('ilr_learning_deliveries', 'PHours')) {
			Capsule::schema()->table('ilr_learning_deliveries', function ($table) {
				$table->unsignedSmallInteger('PHours')->nullable()->after('FundModel');
			});
		}

		if (!Capsule::schema()->hasColumn('ilr_learning_deliveries', 'LSDPostcode')) {
			Capsule::schema()->table('ilr_learning_deliveries', function ($table) {
				$table->string('LSDPostcode', 8)->nullable()->after('DelLocPostCode');
			});
		}

		// Change smcr_staff_functions_responsibilities "completion_status" to only two options.
		$smcr_staff_f_r = \Models\SmcrStaffFunctionResponsibility
			::where('completion_status', 'Assigned')
			->orWhere('completion_status', 'Rejected')
			->get();
		if (Capsule::schema()->hasColumn('smcr_staff_functions_responsibilities', 'completion_status')) {
			Capsule::schema()->table('smcr_staff_functions_responsibilities', function ($table) {
				$table->string('completion_status')->default('Not Accepted')->change();
			});
		}
		foreach ($smcr_staff_f_r as $key => $value) {
			$query = \Models\SmcrStaffFunctionResponsibility::find($value->id);
			if ($value->completion_status == 'Assigned') {
				$query->completion_status = 'Accepted';
			}
			if ($value->completion_status == 'Rejected') {
				$query->completion_status = 'Not Accepted';
			}
			$query->save();
		}

		if (!Capsule::schema()->hasColumn('users', 'staff_type_sign_off_approval')) {
			Capsule::schema()->table('users', function ($table) {
				$table->date("staff_type_sign_off_approval")->nullable()->after('report_to');
			});
		}

		if (!Capsule::schema()->hasColumn('smcr_committee_role_personnel', 'reminder_sent')) {
			Capsule::schema()->table('smcr_committee_role_personnel', function ($table) {
				$table->date('reminder_sent')->nullable()->after('assigned');
			});
		}

		if (!Capsule::schema()->hasColumn('smcr_staff_functions_responsibilities', 'reminder_sent')) {
			Capsule::schema()->table('smcr_staff_functions_responsibilities', function ($table) {
				$table->date('reminder_sent')->nullable()->after('rejected');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_modules', 'f_p_category_id')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->integer('f_p_category_id')->unsigned()->nullable()->after('category_id');
			});
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->foreign('f_p_category_id')->references('id')->on('smcr_f_p_categories');
			});
		}

		if (!Capsule::schema()->hasColumn('users', 'completed_smcr')) {
			Capsule::schema()->table('users', function ($table) {
				$table->decimal('completed_smcr', 5, 2)->default(0)->after('completed');
			});
		}

		if (!Capsule::schema()->hasColumn('smcr_f_p_categories', 'status_learner')) {
			Capsule::schema()->table('smcr_f_p_categories', function ($table) {
				$table->boolean('status_learner')->default(true)->after('status');
			});
		}



		if (!Capsule::schema()->hasColumn('daily_statistics', 'smcr_committee_total')) {
			Capsule::schema()->table('daily_statistics', function ($table) {
				$table->integer('smcr_committee_total')->unsigned()->default(0)->after('smcr_committee_accepted');
			});
		}

		if (!Capsule::schema()->hasColumn('daily_statistics', 'smcr_senior_manager_certified')) {
			Capsule::schema()->table('daily_statistics', function ($table) {
				$table->integer('smcr_senior_manager_certified')->unsigned()->default(0)->after('smcr_cs_functions_accepted_percentage');
			});
		}

		if (!Capsule::schema()->hasColumn('daily_statistics', 'smcr_senior_manager_not_certified')) {
			Capsule::schema()->table('daily_statistics', function ($table) {
				$table->integer('smcr_senior_manager_not_certified')->unsigned()->default(0)->after('smcr_senior_manager_certified');
			});
		}

		if (!Capsule::schema()->hasColumn('daily_statistics', 'smcr_certification_staff_certified')) {
			Capsule::schema()->table('daily_statistics', function ($table) {
				$table->integer('smcr_certification_staff_certified')->unsigned()->default(0)->after('smcr_senior_manager_not_certified');
			});
		}

		if (!Capsule::schema()->hasColumn('daily_statistics', 'smcr_certification_staff_not_certified')) {
			Capsule::schema()->table('daily_statistics', function ($table) {
				$table->integer('smcr_certification_staff_not_certified')->unsigned()->default(0)->after('smcr_certification_staff_certified');
			});
		}

		if (!Capsule::schema()->hasColumn('custom_reviews_filters', 'where_in')) {
			Capsule::schema()->table('custom_reviews_filters', function ($table) {
				$table->string('where_in', 1000)->after('where_or');
			});
		}

		if (Capsule::schema()->hasColumn('custom_reviews_filters', 'complex_options')) {
			Capsule::schema()->table('custom_reviews_filters', function ($table) {
				$table->text('complex_options')->change();
			});
		}

		if (!Capsule::schema()->hasColumn('custom_reviews_filters', 'is_smcr')) {
			Capsule::schema()->table('custom_reviews_filters', function ($table) {
				$table->boolean('is_smcr')->default(0)->after('is_apprentix');
			});
		}

		if (!Capsule::schema()->hasColumn('custom_reviews_filters', 'filter_mysql')) {
			Capsule::schema()->table('custom_reviews_filters', function ($table) {
				$table->text('filter_mysql')->after('filter_model');
			});
		}

		if (!Capsule::schema()->hasColumn('structure', 'name_template')) {
			Capsule::schema()->table('structure', function ($table) {
				$table->string('name_template')->nullable()->after('name');
			});
		}

		if (!Capsule::schema()->hasColumn('smcr_reports', 'sign_off_date')) {
			Capsule::schema()->table('smcr_reports', function ($table) {
				$table->date('sign_off_date')->nullable()->default(null)->after('expire_at');
			});
		}

		if (!Capsule::schema()->hasColumn('smcr_reports', 'approved_authority')) {
			Capsule::schema()->table('smcr_reports', function ($table) {
				$table->text('approved_authority')->nullabe()->after('sign_off_date');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_modules', 'refresh_repeat')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->integer('refresh_repeat')->unsigned()->nullable()->default(null)->after('refresh_period');
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_standards_users', 'on_schedule')) {
			Capsule::schema()->table('apprenticeship_standards_users', function ($table) {
				$table->decimal('on_schedule', 6, 2)->default(0)->after('time_behind');
			});
		}

		if (!Capsule::schema()->hasColumn('timings', 'start_date')) {
			Capsule::schema()->table('timings', function ($table) {
				$table->date('start_date')->nullable()->after('timing');
			});
		}

		if (!Capsule::schema()->hasColumn('users', 'self_attested')) {
			Capsule::schema()->table('users', function ($table) {
				$table->boolean('self_attested')->default(false)->after('status');
			});
		}

		if (!Capsule::schema()->hasColumn('users', 'certified')) {
			Capsule::schema()->table('users', function ($table) {
				$table->boolean('certified')->default(false)->after('self_attested');
			});
		}

		if (!Capsule::schema()->hasColumn('users', 'accessible_ui')) {
			Capsule::schema()->table('users', function ($table) {
				$table->boolean('accessible_ui')->default(false)->after('certified');
			});
		}

		if (!Capsule::schema()->hasColumn('users', 'total_resources_smcr')) {
			Capsule::schema()->table('users', function ($table) {
				$table->smallInteger('total_resources_smcr')->unsigned()->nullable()->after('total_resources');
			});
		}

		if (!Capsule::schema()->hasColumn('users', 'not_started_resources_smcr')) {
			Capsule::schema()->table('users', function ($table) {
				$table->smallInteger('not_started_resources_smcr')->unsigned()->nullable()->after('not_started_resources');
			});
		}

		if (!Capsule::schema()->hasColumn('users', 'in_progress_resources_smcr')) {
			Capsule::schema()->table('users', function ($table) {
				$table->smallInteger('in_progress_resources_smcr')->unsigned()->nullable()->after('in_progress_resources');
			});
		}

		if (!Capsule::schema()->hasColumn('users', 'completed_resources_smcr')) {
			Capsule::schema()->table('users', function ($table) {
				$table->smallInteger('completed_resources_smcr')->unsigned()->nullable()->after('completed_resources');
			});
		}

		if (!Capsule::schema()->hasColumn('users', 'time_spent_smcr')) {
			Capsule::schema()->table('users', function ($table) {
				$table->decimal('time_spent_smcr', 5, 2)->default(0)->after('time_spent');
			});
		}

		if (!Capsule::schema()->hasColumn('smcr_reports', 'comments')) {
			Capsule::schema()->table('smcr_reports', function ($table) {
				$table->text('comments')->nullabe()->after('approved_authority');
			});
		}

		if (!Capsule::schema()->hasColumn('custom_reviews_filters', 'range_selector')) {
			Capsule::schema()->table('custom_reviews_filters', function ($table) {
				$table->boolean('range_selector')->default(false)->after('date_selector');
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_standards_users', 'working_hours')) {
			Capsule::schema()->table('apprenticeship_standards_users', function ($table) {
				$table->integer('working_hours')->unsigned()->nullable()->after('actual_cost');
			});
		}

		if (!Capsule::schema()->hasColumn('roles', 'admin_interface')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->boolean('admin_interface')->default(false)->after('sign_off_learner_status');
			});
		}

		if (!Capsule::schema()->hasColumn('smcr_reports', 'type_id')) {
			Capsule::schema()->table('smcr_reports', function ($table) {
				$table->integer('type_id')->unsigned()->nullable()->default(null)->after('completion_status');
			});
			Capsule::schema()->table('smcr_reports', function ($table) {
				$table->foreign('type_id')->references('id')->on('smcr_report_types');
			});
		}


		if (!Capsule::schema()->hasColumn('smcr_reports', 'self_attested_date')) {
			Capsule::schema()->table('smcr_reports', function ($table) {
				$table->date('self_attested_date')->nullable()->default(null)->after('sign_off_date');
			});
		}

		if (!Capsule::schema()->hasColumn('smcr_reports', 'certified_by_id')) {
			Capsule::schema()->table('smcr_reports', function ($table) {
				$table->integer('certified_by_id')->unsigned()->nullable()->after('user_id');
			});
			Capsule::schema()->table('smcr_reports', function ($table) {
				$table->foreign('certified_by_id')->references('id')->on('users');
			});
		}

		if (Capsule::schema()->hasColumn('smcr_reports', 'certified_by')) {
			Capsule::schema()->table('smcr_reports', function ($table) {
				$table->dropForeign('smcr_reports_certified_by_foreign');
				$table->dropColumn('certified_by');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_modules', 'print_certificate')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->boolean('print_certificate')->default(true)->after('track_progress');
			});
		}

		if (!Capsule::schema()->hasColumn('smcr_reports', 'snapshot')) {
			Capsule::schema()->table('smcr_reports', function ($table) {
				$table->longText('snapshot')->nullable()->after('type_id');
			});
		}

		if (!Capsule::schema()->hasColumn('batch_reports', 'copy_manager')) {
			Capsule::schema()->table('batch_reports', function ($table) {
				$table->boolean('copy_manager')->default(false)->after('user_id_key');
			});
		}

		if (!Capsule::schema()->hasColumn('custom_reviews_filters', 'option_group_order')) {
			Capsule::schema()->table('custom_reviews_filters', function ($table) {
				$table->integer('option_group_order')->unsigned()->default(0)->after('option_group');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_modules', 'responsible_user')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->integer('responsible_user')->unsigned()->nullable()->default(null)->after('provider_id');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_modules', 'expiration_date')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->date('expiration_date')->nullable()->default(null)->after('due_after_period');
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_standards', 'periodic_repeat')) {
			Capsule::schema()->table('apprenticeship_standards', function ($table) {
				$table->boolean('periodic_repeat')->default(false)->after('completion_months');
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_standards', 'periodic_repeat_months')) {
			Capsule::schema()->table('apprenticeship_standards', function ($table) {
				$table->integer('periodic_repeat_months')->unsigned()->nullable()->after('periodic_repeat');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_module_competencies', 'created_at')) {
			Capsule::schema()->table('learning_module_competencies', function ($table) {
				$table->timestamp('created_at')->nullable()->default(NULL)->after('points');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_module_competencies', 'updated_at')) {
			Capsule::schema()->table('learning_module_competencies', function ($table) {
				$table->timestamp('updated_at')->nullable()->default(NULL)->after('created_at');
			});
		}

		if (!Capsule::schema()->hasColumn('user_learning_modules', 'created_at')) {
			Capsule::schema()->table('user_learning_modules', function ($table) {
				$table->timestamp('created_at')->nullable()->default(NULL)->after('learning_module_id');
			});
		}

		if (!Capsule::schema()->hasColumn('user_learning_modules', 'updated_at')) {
			Capsule::schema()->table('user_learning_modules', function ($table) {
				$table->timestamp('updated_at')->nullable()->default(NULL)->after('created_at');
			});
		}

		if (!Capsule::schema()->hasColumn('roles', 'email_disable_manager_notifications')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->boolean('email_disable_manager_notifications')->default(false)->after('admin_interface');
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_standards_users', 'ilr_link')) {
			Capsule::schema()->table('apprenticeship_standards_users', function ($table) {
				$table->boolean('ilr_link')->default(false)->after('working_hours');
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_standards_users', 'working_hours_custom')) {
			Capsule::schema()->table('apprenticeship_standards_users', function ($table) {
				$table->boolean('working_hours_custom')->default(false)->after('working_hours');
			});
		}

		if (!Capsule::schema()->hasColumn('companies', 'next_contact_date')) {
			Capsule::schema()->table('companies', function ($table) {
				$table->date("next_contact_date")->nullable()->after('max_users');
			});
		}
		if (!Capsule::schema()->hasColumn('companies', 'discount_percentage')) {
			Capsule::schema()->table('companies', function ($table) {
				$table->integer("discount_percentage")->nullable()->after('max_users');
			});
		}
		if (!Capsule::schema()->hasColumn('departments', 'discount_percentage')) {
			Capsule::schema()->table('departments', function ($table) {
				$table->integer("discount_percentage")->nullable()->after('status');
			});
		}
		if (!Capsule::schema()->hasColumn('departments', 'parent_id')) {
			Capsule::schema()->table('departments', function ($table) {
				$table->integer('parent_id')->unsigned()->nullable()->default(null)->after('phone');
				$table->foreign('parent_id')->references('id')->on('departments');
			});
		}
		if (!Capsule::schema()->hasColumn('users', 'discount_percentage')) {
			Capsule::schema()->table('users', function ($table) {
				$table->integer("discount_percentage")->nullable()->after('report_to');
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_standards_users', 'working_hours_ilr')) {
			Capsule::schema()->table('apprenticeship_standards_users', function ($table) {
				$table->boolean('working_hours_ilr')->default(false)->after('working_hours_custom');
			});
		}

		if (!Capsule::schema()->hasColumn('email_templates', 'batch_report_id')) {
			Capsule::schema()->table('email_templates', function ($table) {
				$table->integer('batch_report_id')->unsigned()->nullable()->default(null)->after('copy_email_to_managers');
			});
		}

		if (!Capsule::schema()->hasColumn('ilr_learning_deliveries', 'LearnAimRefTitle')) {
			Capsule::schema()->table('ilr_learning_deliveries', function ($table) {
				$table->string('LearnAimRefTitle')->nullable()->after('user_id');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_modules', 'highlight_image')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->string('highlight_image')->default('')->after('promo_image');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_module_types', 'slug')) {
			Capsule::schema()->table('learning_module_types', function ($table) {
				$table->string('slug')->after('name');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_results', 'created_by')) {
			Capsule::schema()->table('learning_results', function ($table) {
				$table->integer('created_by')->unsigned()->nullable()->default(null)->after('manager_action_date');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_results', 'off_the_job_training')) {
			Capsule::schema()->table('learning_results', function ($table) {
				$table->boolean('off_the_job_training')->default(false)->after('created_by');
			});
		}

		if (!Capsule::schema()->hasColumn('manager_reviews', 'off_the_job_training')) {
			Capsule::schema()->table('manager_reviews', function ($table) {
				$table->boolean('off_the_job_training')->default(false)->after('completion_status');
			});
		}

		if (!Capsule::schema()->hasColumn('users', 'time_spent_off_the_job_training')) {
			Capsule::schema()->table('users', function ($table) {
				$table->decimal('time_spent_off_the_job_training', 5, 2)->default(0)->after('time_spent_smcr');
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_standards_users', 'time_spent_off_the_job_training')) {
			Capsule::schema()->table('apprenticeship_standards_users', function ($table) {
				$table->decimal('time_spent_off_the_job_training', 5, 2)->default(0)->after('time_spent');
			});
		}

		if (!Capsule::schema()->hasColumn('department_learning_modules', 'created_at')) {
			Capsule::schema()->table('department_learning_modules', function ($table) {
				$table->timestamp('created_at')->nullable()->default(NULL)->after('learning_module_id');
			});
		}

		if (!Capsule::schema()->hasColumn('department_learning_modules', 'updated_at')) {
			Capsule::schema()->table('department_learning_modules', function ($table) {
				$table->timestamp('updated_at')->nullable()->default(NULL)->after('created_at');
			});
		}

		if (!Capsule::schema()->hasColumn('department_learning_modules', 'due_at')) {
			Capsule::schema()->table('department_learning_modules', function ($table) {
				$table->dateTime('due_at')->nullable()->default(null)->after('learning_module_id');
			});
		}

		if (!Capsule::schema()->hasColumn('department_learning_modules', 'start_at')) {
			Capsule::schema()->table('department_learning_modules', function ($table) {
				$table->dateTime('start_at')->nullable()->default(null)->after('due_at');
			});
		}

		if (Capsule::schema()->hasColumn('learning_results', 'completion_date_custom')) {
			Capsule::schema()->table('learning_results', function ($table) {
				$table->dateTime('completion_date_custom')->nullable()->change();
			});
		}

		if (!Capsule::schema()->hasColumn('email_templates', 'site_versions')) {
			Capsule::schema()->table('email_templates', function ($table) {
				$table->string('site_versions')->after('batch_report_id')->nullable();
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_issues_groups', 'start_date')) {
			Capsule::schema()->table('apprenticeship_issues_groups', function ($table) {
				$table->dateTime('start_date')->nullable()->default(null)->after('end_day');
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_issues_departments', 'start_date')) {
			Capsule::schema()->table('apprenticeship_issues_departments', function ($table) {
				$table->dateTime('start_date')->nullable()->default(null)->after('end_day');
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_issues_groups', 'lesson_duration')) {
			Capsule::schema()->table('apprenticeship_issues_groups', function ($table) {
				$table->integer('lesson_duration')->unsigned()->nullable()->default(null)->after('expected_completion_date');
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_issues_departments', 'lesson_duration')) {
			Capsule::schema()->table('apprenticeship_issues_departments', function ($table) {
				$table->integer('lesson_duration')->unsigned()->nullable()->default(null)->after('expected_completion_date');
			});
		}

		if (!Capsule::schema()->hasColumn('roles', 'show_all_resources')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->boolean('show_all_resources')->default(false)->after('admin_interface');
			});
		}

		if (Capsule::schema()->hasColumn('batch_report_data', 'data')) {
			Capsule::schema()->table('batch_report_data', function ($table) {
				DB::statement("ALTER TABLE `batch_report_data` CHANGE COLUMN `data` `data` LONGBLOB NULL AFTER `run_time`;");
			});
		}

		if (!Capsule::schema()->hasColumn('learning_course_modules', 'created_at')) {
			Capsule::schema()->table('learning_course_modules', function ($table) {
				$table->timestamp('created_at')->nullable()->default(NULL)->after('learning_module_id');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_course_modules', 'updated_at')) {
			Capsule::schema()->table('learning_course_modules', function ($table) {
				$table->timestamp('updated_at')->nullable()->default(NULL)->after('created_at');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_modules', 'player_width')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->integer('player_width')->unsigned()->nullable()->default(null)->after('created_in_learner_interface');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_modules', 'player_height')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->integer('player_height')->unsigned()->nullable()->default(null)->after('player_width');
			});
		}

		if (!Capsule::schema()->hasColumn('email_templates', 'slug')) {
			Capsule::schema()->table('email_templates', function ($table) {
				$table->string('slug')->after('id');
			});
		}

		if (!Capsule::schema()->hasColumn('schedules', 'reminder_sent')) {
			Capsule::schema()->table('schedules', function ($table) {
				$table->boolean('reminder_sent')->default(false)->after('cron_task');
			});
		}
		if (!Capsule::schema()->hasColumn('schedules', 'enrole_any_learner')) {
			Capsule::schema()->table('schedules', function ($table) {
				$table->boolean('enrole_any_learner')->default(false)->after('cron_task');
			});
		}
		if (!Capsule::schema()->hasColumn('schedules', 'approval')) {
			Capsule::schema()->table('schedules', function ($table) {
				$table->boolean('approval')->default(false)->after('cron_task');
			});
		}
		if (!Capsule::schema()->hasColumn('schedule_links', 'approved')) {
			Capsule::schema()->table('schedule_links', function ($table) {
				$table->boolean('approved')->default(true)->after('type');
			});
		}
		if (!Capsule::schema()->hasColumn('schedules', 'minclass')) {
			Capsule::schema()->table('schedules', function ($table) {
				$table->integer('minclass')->unsigned()->nullable()->default(null)->after('cron_task');
			});
		}
		if (!Capsule::schema()->hasColumn('schedules', 'maxclass')) {
			Capsule::schema()->table('schedules', function ($table) {
				$table->integer('maxclass')->unsigned()->nullable()->default(null)->after('cron_task');
			});
		}

		if (!Capsule::schema()->hasColumn('schedule_links', 'completion_status')) {
			Capsule::schema()->table('schedule_links', function ($table) {
				$table->enum('completion_status', ['Not Attempted', 'In Progress', 'Completed'])->default('Not Attempted')->after('type');
			});
		}

		if (Capsule::schema()->hasColumn('schedule_links', 'completion_status')) {
			Capsule::schema()->table('schedule_links', function ($table) {
				DB::statement("ALTER TABLE `schedule_links` CHANGE COLUMN `completion_status` `completion_status` ENUM('Completed', 'In Progress', 'Not Attempted', '%%event_completion_state_completed%%', '%%event_completion_state_in_progress%%', '%%event_completion_state_not_attempted%%') NULL DEFAULT NULL");
			});
		}

		if (Capsule::schema()->hasColumn('log_authentications', 'type')) {
			Capsule::schema()->table('log_authentications', function ($table) {
				DB::statement("ALTER TABLE `log_authentications` CHANGE COLUMN `type` `type` ENUM('login', 'logout', 'timeout', 'impersonate')");
			});
		}

		if (!Capsule::schema()->hasColumn('log_authentications', 'impersonate_by')) {
			Capsule::schema()->table('log_authentications', function ($table) {
				$table->integer('impersonate_by')->unsigned()->nullable()->default(null)->after('type');
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_standards_users', 'created_by')) {
			Capsule::schema()->table('apprenticeship_standards_users', function ($table) {
				$table->integer('created_by')->unsigned()->nullable()->after("ilr_link");
			});
		}
		if (!Capsule::schema()->hasColumn('apprenticeship_standards_users', 'deleted_by')) {
			Capsule::schema()->table('apprenticeship_standards_users', function ($table) {
				$table->integer('deleted_by')->unsigned()->nullable()->after("created_by");
			});
		}
		if (!Capsule::schema()->hasColumn('apprenticeship_standards_users', 'deleted_at')) {
			Capsule::schema()->table('apprenticeship_standards_users', function ($table) {
				$table->softDeletes();
			});
		}

		if (!Capsule::schema()->hasColumn('learning_results', 'completed_version')) {
			Capsule::schema()->table('learning_results', function ($table) {
				$table->integer('completed_version')->nullable()->default(null)->after('completed_at');
			});
		}
		if (!Capsule::schema()->hasColumn('learning_results', 'completed_by')) {
			Capsule::schema()->table('learning_results', function ($table) {
				$table->integer('completed_by')->nullable()->default(null)->after('completed_version');
			});
		}

		if (Capsule::schema()->hasColumn('quality_controls', 'type')) {
			Capsule::schema()->table('quality_controls', function ($table) {
				DB::statement("ALTER TABLE `quality_controls` CHANGE COLUMN `type` `type` ENUM('apprenticeship_issue_categories', 'apprenticeship_issues', 'apprenticeship_sub_issues', 'learning_results', 'feedback') NULL DEFAULT NULL");
			});
		}

		if (!Capsule::schema()->hasColumn('schedule_links', 'duration')) {
			Capsule::schema()->table('schedule_links', function ($table) {
				$table->integer('duration')->unsigned()->default(0)->after('completion_status');
			});
		}
		/*will be used to check if attempted status is authorised or not*/
		if (!Capsule::schema()->hasColumn('schedule_links', 'is_authorised')) {
			Capsule::schema()->table('schedule_links', function ($table) {
				$table->boolean('is_authorised')->nullable()->default(null)->after('completion_status');
			});
		}
		/*will be used to enter notes if attempted status is not authorised or authorised*/
		if (!Capsule::schema()->hasColumn('schedule_links', 'authorisation_notes')) {
			Capsule::schema()->table('schedule_links', function ($table) {
				$table->string('authorisation_notes', 70000)->nullable()->default(null)->after('is_authorised');
			});
		}

		// If I specify mediumtext from text, laravel will fail, but if i give string and specify lenght bigger than text, it will switch to mediumtext.
		if (Capsule::schema()->hasColumn('learning_modules', 'description')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->string('description', 70000)->nullable()->default(null)->change();
			});
		}

		if (!Capsule::schema()->hasColumn('learning_results', 'start_date')) {
			Capsule::schema()->table('learning_results', function ($table) {
				$table->dateTime('start_date')->nullable()->default(null)->after('off_the_job_training');
			});
		}
		if (!Capsule::schema()->hasColumn('learning_results', 'qa_date')) {
			Capsule::schema()->table('learning_results', function ($table) {
				$table->dateTime('qa_date')->nullable()->default(null)->after('qa');
			});
		}


		if (!Capsule::schema()->hasColumn('learning_results', 'qa_created_by')) {
			Capsule::schema()->table('learning_results', function ($table) {
				$table->integer('qa_created_by')->unsigned()->nullable()->default(null)->after('qa_date');
			});
		}

		if (Capsule::schema()->hasColumn('email_queue', 'send_date')) {
			Capsule::schema()->table('email_queue', function ($table) {
				$table->dateTime('send_date')->nullable()->change();
			});
		}

		if (!Capsule::schema()->hasColumn('learning_module_categories', 'landing_image')) {
			Capsule::schema()->table('learning_module_categories', function ($table) {
				$table->string('landing_image')->nullable()->default(NULL)->after('is_mandatory');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_results', 'homework')) {
			Capsule::schema()->table('learning_results', function ($table) {
				$table->boolean('homework')->default(false)->after('start_date');
			});
		}

		if (!Capsule::schema()->hasColumn('schedule_links', 'completion_date_custom')) {
			Capsule::schema()->table('schedule_links', function ($table) {
				$table->date('completion_date_custom')->nullable()->default(NULL)->after('link_id');
			});
		}

		if (!Capsule::schema()->hasColumn('schedules', 'description')) {
			Capsule::schema()->table('schedules', function ($table) {
				$table->text('description')->after('name');
			});
		}

		if (Capsule::schema()->hasColumn('schedules', 'type')) {
			Capsule::schema()->table('schedules', function ($table) {
				$table->string('type')->nullable()->default(NULL)->change();
			});
		}

		if (!Capsule::schema()->hasColumn('schedules', 'visit_type_id')) {
			Capsule::schema()->table('schedules', function ($table) {
				$table->integer('visit_type_id')->unsigned()->nullable()->default(NULL)->after('type');
			});
			Capsule::schema()->table('schedules', function ($table) {
				$table->foreign('visit_type_id')->references('id')->on('schedule_visit_types');
			});
		}

		if (!Capsule::schema()->hasColumn('schedules', 'location')) {
			Capsule::schema()->table('schedules', function ($table) {
				$table->text('location')->after('description');
			});
		}


		if (!Capsule::schema()->hasColumn('learning_modules', 'go1_id')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->integer('go1_id')->unsigned()->nullable()->default(null)->after('player_height');
			});
		}

		if (!Capsule::schema()->hasColumn('schedule_visit_types', 'off_the_job_training')) {
			Capsule::schema()->table('schedule_visit_types', function ($table) {
				$table->boolean('off_the_job_training')->default(false)->after('slug');
			});

			// Update entries to default ones that need to be tracked. "Development" and "Training"
			$visit_types = \Models\ScheduleVisitType::all();
			foreach ($visit_types as $key => $visit_type) {
				if (
					$visit_type->slug == 'Development' ||
					$visit_type->slug == 'Training'
				) {
					$visit_type->off_the_job_training = true;
					$visit_type->save();
				}
			}
		}

		if (Capsule::schema()->hasColumn('schedule_visit_types', 'track')) {
			Capsule::schema()->table('schedule_visit_types', function ($table) {
				$table->dropColumn('track');
			});
		}

		if (!Capsule::schema()->hasColumn('ilr_learning_deliveries', 'OTJActHours')) {
			Capsule::schema()->table('ilr_learning_deliveries', function ($table) {
				$table->unsignedSmallInteger('OTJActHours')->nullable()->after('PHours');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_modules', 'jackdaw_access_token')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->string('jackdaw_access_token')->default('')->after('jackdaw_resource');
			});
		}

		if (Capsule::schema()->hasColumn('custom_reviews_filters', 'created_by')) {
			Capsule::schema()->table('custom_reviews_filters', function ($table) {
				$table->integer('created_by')->unsigned()->nullable()->default(null)->change();
			});
		}

		if (Capsule::schema()->hasColumn('custom_reviews_filters', 'sort')) {
			Capsule::schema()->table('custom_reviews_filters', function ($table) {
				$table->boolean('sort')->default(true)->change();
			});
		}

		if (!Capsule::schema()->hasColumn('learning_results', 'duration_scorm')) {
			Capsule::schema()->table('learning_results', function ($table) {
				$table->smallInteger('duration_scorm')->unsigned()->default(0)->after('duration_minutes');
			});
		}

		if (!Capsule::schema()->hasColumn('ilr_learning_deliveries', 'WithdrawDate')) {
			Capsule::schema()->table('ilr_learning_deliveries', function ($table) {
				$table->date('WithdrawDate')->nullable()->after('WithdrawReason');
			});
		}

		if (!Capsule::schema()->hasColumn('picklists', 'public')) {
			Capsule::schema()->table('picklists', function ($table) {
				$table->boolean('public')->default(false)->after('order');
			});
		}

		if (Capsule::schema()->hasColumn('apprenticeship_issue_categories_users', 'percentage')) {
			Capsule::schema()->table('apprenticeship_issue_categories_users', function ($table) {
				$table->decimal('percentage', 5, 2)->default(0)->change();
			});
		}

		if (Capsule::schema()->hasColumn('apprenticeship_issue_categories_users', 'percentage_issues')) {
			Capsule::schema()->table('apprenticeship_issue_categories_users', function ($table) {
				$table->decimal('percentage_issues', 5, 2)->default(0)->change();
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_issue_categories_users', 'disable_not_allowed')) {
			Capsule::schema()->table('apprenticeship_issue_categories_users', function ($table) {
				$table->boolean('disable_not_allowed')->default(false)->after('status');
			});
		}

		if (!Capsule::schema()->hasColumn('table_extension_fields', 'default')) {
			Capsule::schema()->table('table_extension_fields', function ($table) {
				$table->string('default')->nullable()->default(null)->after('options');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_modules', 'event_type_id')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->integer('event_type_id')->unsigned()->nullable()->after('evidence_type_id');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_modules', 'visible_learner')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->boolean('visible_learner')->default(true)->after('go1_id');
			});
		}

		if (!Capsule::schema()->hasColumn('event_types', 'slug')) {
			Capsule::schema()->table('event_types', function ($table) {
				$table->string('slug')->after('name');
			});

			// Make sure all items have unique slug!

			$entries = \Models\EventType::all();
			foreach ($entries as $key => $entry) {
				if (!$entry->slug) {
					$entry->slug = \APP\Tools::safeName($entry->name) . '_' . \APP\Tools::unsecureRandom();
					$entry->save();
				}
			}

			Capsule::schema()->table('event_types', function ($table) {
				$table->unique('slug');
			});
		}

		if (!Capsule::schema()->hasColumn('event_types', 'system')) {
			Capsule::schema()->table('event_types', function ($table) {
				$table->boolean('system')->default(false)->after('status');
			});
		}

		if (!Capsule::schema()->hasColumn('schedules', 'created_by')) {
			Capsule::schema()->table('schedules', function ($table) {
				$table->integer('created_by')->unsigned()->nullable()->after('parent_id');
			});
		}

		if (!Capsule::schema()->hasColumn('schedules', 'created_for')) {
			Capsule::schema()->table('schedules', function ($table) {
				$table->integer('created_for')->unsigned()->nullable()->after('created_by');
			});
		}
		if (!Capsule::schema()->hasColumn('schedules', 'cost')) {
			Capsule::schema()->table('schedules', function ($table) {
				$table->float('cost')->nullable()->after('created_by');
			});
		}

		if (!Capsule::schema()->hasColumn('schedules', 'visible_learner')) {
			Capsule::schema()->table('schedules', function ($table) {
				$table->boolean('visible_learner')->default(true)->after('status');
			});
		}

		if (!Capsule::schema()->hasColumn('schedules', 'visible_schedule')) {
			Capsule::schema()->table('schedules', function ($table) {
				$table->boolean('visible_schedule')->default(true)->after('visible_learner');
			});
		}

		if (!Capsule::schema()->hasColumn('schedules', 'visible_learner_task')) {
			Capsule::schema()->table('schedules', function ($table) {
				$table->boolean('visible_learner_task')->default(true)->after('visible_schedule');
			});
		}

		if (!Capsule::schema()->hasColumn('schedule_links', 'manager_visitor')) {
			Capsule::schema()->table('schedule_links', function ($table) {
				$table->boolean('manager_visitor')->default(false)->after('cron_task');
			});
		}

		if (!Capsule::schema()->hasColumn('custom_reviews_filters', 'filter_options_id')) {
			Capsule::schema()->table('custom_reviews_filters', function ($table) {
				$table->string('filter_options_id', 100)->nullable()->default(null)->after('filter_options');
			});
		}

		if (Capsule::schema()->hasColumn('schedule_links', 'type')) {
			Capsule::schema()->table('schedule_links', function ($table) {
				$table->string('type')->nullable()->default(NULL)->change();
			});
		}

		if (!Capsule::schema()->hasColumn('schedules', 'category_id')) {
			Capsule::schema()->table('schedules', function ($table) {
				$table->integer('category_id')->unsigned()->nullable()->default(NULL)->after('type');
			});
		}

		//Add flag indicating whether Google 2FA is enabled for a particular user
		if (!Capsule::schema()->hasColumn('users', 'enabled_google_2FA')) {
			Capsule::schema()->table('users', function ($table) {
				$table->boolean('enabled_google_2FA')->default(false)->after('skype_id');
			});
		}

		if (!Capsule::schema()->hasColumn('users', 'google_2FA_secret')) {
			Capsule::schema()->table('users', function ($table) {
				$table->string('google_2FA_secret')->nullable()->default(NULL)->after('enabled_google_2FA');
			});
		}

		if (!Capsule::schema()->hasColumn('comments', 'visible_learner')) {
			Capsule::schema()->table('comments', function ($table) {
				$table->boolean('visible_learner')->default(1)->after('added_for');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_results_comments', 'visible_learner')) {
			Capsule::schema()->table('learning_results_comments', function ($table) {
				$table->boolean('visible_learner')->default(true)->after('qa');
			});
		}

		if (!Capsule::schema()->hasColumn('users', 'self_attested_reminder_sent')) {
			Capsule::schema()->table('users', function ($table) {
				$table->boolean('self_attested_reminder_sent')->default(false)->after('self_attested');
			});
		}

		if (Capsule::schema()->hasColumn('custom_reviews_filters', 'left_join_extended')) {
			Capsule::schema()->table('custom_reviews_filters', function ($table) {
				$table->text('left_join_extended')->change();
			});
		}

		if (Capsule::schema()->hasColumn('apprenticeship_standards_users', 'percentage')) {
			Capsule::schema()->table('apprenticeship_standards_users', function ($table) {
				$table->decimal('percentage', 5, 2)->default(0)->change();
			});
		}


		if (Capsule::schema()->hasColumn('apprenticeship_standards_users', 'percentage_behind')) {
			Capsule::schema()->table('apprenticeship_standards_users', function ($table) {
				$table->decimal('percentage_behind', 5, 2)->default(0)->change();
			});
		}

		if (Capsule::schema()->hasColumn('apprenticeship_standards_users', 'percentage_time')) {
			Capsule::schema()->table('apprenticeship_standards_users', function ($table) {
				$table->decimal('percentage_time', 5, 2)->default(0)->change();
			});
		}

		if (Capsule::schema()->hasColumn('learning_modules', 'promo_image')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->string('promo_image')->default('')->change();
			});
		}

		if (!Capsule::schema()->hasColumn('users', 'exclude_from_emails')) {
			Capsule::schema()->table('users', function ($table) {
				$table->boolean('exclude_from_emails')->default(false)->after('exclude_from_ilr_export');
			});
		}
		//Role table update
		if (!Capsule::schema()->hasColumn('roles', 'hide_set_training_tab')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->boolean('hide_set_training_tab')->default(false)->after('sign_off_learner_status');
			});
		}
		if (!Capsule::schema()->hasColumn('roles', 'hide_qa_tab')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->boolean('hide_qa_tab')->default(false)->after('hide_set_training_tab');
			});
		}
		if (!Capsule::schema()->hasColumn('roles', 'hide_set_pending_assessment_tab')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->boolean('hide_set_pending_assessment_tab')->default(false)->after('hide_qa_tab');
			});
		}
		if (!Capsule::schema()->hasColumn('roles', 'hide_sign_off_tab')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->boolean('hide_sign_off_tab')->default(false)->after('hide_set_pending_assessment_tab');
			});
		}
		if (!Capsule::schema()->hasColumn('roles', 'hide_approve_and_manage_booking')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->boolean('hide_approve_and_manage_booking')->default(false)->after('hide_sign_off_tab');
			});
		}

		if (!Capsule::schema()->hasColumn('schedule_links', 'order')) {
			Capsule::schema()->table('schedule_links', function ($table) {
				$table->integer('order')->unsigned()->default(0)->after('manager_visitor');
			});
		}

		if (!Capsule::schema()->hasColumn('roles', 'disable_edit_ilr_fields')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->boolean('disable_edit_ilr_fields')->default(false)->after('hide_sign_off_tab');
			});
		}
		if (!Capsule::schema()->hasColumn('roles', 'disable_mfa')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->boolean('disable_mfa')->default(false)->after('disable_edit_ilr_fields');
			});
		}

		// Change crons table last_output field from string to text
		if (Capsule::schema()->hasColumn('crons', 'last_output')) {
			Capsule::schema()->table('crons', function ($table) {
				$table->text('last_output')->change();
			});
		}
		if (!Capsule::schema()->hasColumn('crons', 'force_run')) {
			Capsule::schema()->table('crons', function ($table) {
				$table->boolean('force_run')->default(false)->after('last_output');
			});
		}

		// Add next_run_time field for improved cron scheduling
		if (!Capsule::schema()->hasColumn('crons', 'next_run_time')) {
			Capsule::schema()->table('crons', function ($table) {
				$table->timestamp('next_run_time')->nullable()->after('frequency');
			});

			// Initialize next_run_time for existing cron tasks
			DB::statement('UPDATE crons SET next_run_time = DATE_ADD(NOW(), INTERVAL frequency MINUTE) WHERE next_run_time IS NULL');
		}

		if (!Capsule::schema()->hasColumn('schedule_links', 'instructor_lead')) {
			Capsule::schema()->table('schedule_links', function ($table) {
				$table->boolean('instructor_lead')->default(false)->after('order');
			});
		}

		if (!Capsule::schema()->hasColumn('schedule_links', 'completed_at')) {
			Capsule::schema()->table('schedule_links', function ($table) {
				$table->dateTime('completed_at')->nullable()->default(NULL)->after('completion_status');
			});
		}

		// Change completion_date_custom field for table schedule_links to use full datetime format
		if (Capsule::schema()->hasColumn('schedule_links', 'completion_date_custom')) {
			Capsule::schema()->table('schedule_links', function ($table) {
				$table->dateTime('completion_date_custom')->nullable()->default(NULL)->change();
			});
		}

		/*Add version for Learning modules*/
		if (!Capsule::schema()->hasColumn('learning_modules', 'version')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->integer('version')->unsigned()->default(1)->after('material');
			});
		}
		/*Add version for Learning evidenced*/
		if (!Capsule::schema()->hasColumn('learning_module_evidences', 'version')) {
			Capsule::schema()->table('learning_module_evidences', function ($table) {
				$table->integer('version')->unsigned()->default(1)->after('id');
			});
		}
		//VERSION EXTRA FIELDS
		if (!Capsule::schema()->hasColumn('learning_module_versions', 'evidence_type_id')) {
			Capsule::schema()->table('learning_module_versions', function ($table) {
				$table->integer('evidence_type_id')->unsigned()->nullable()->after('material');
			});
		}
		if (!Capsule::schema()->hasColumn('learning_module_versions', 'require_management_signoff')) {
			Capsule::schema()->table('learning_module_versions', function ($table) {
				$table->boolean('require_management_signoff')->default(false)->after('evidence_type_id');
			});
		}
		if (!Capsule::schema()->hasColumn('learning_module_versions', 'player_width')) {
			Capsule::schema()->table('learning_module_versions', function ($table) {
				$table->integer('player_width')->unsigned()->nullable()->after('require_management_signoff');
			});
		}
		if (!Capsule::schema()->hasColumn('learning_module_versions', 'player_height')) {
			Capsule::schema()->table('learning_module_versions', function ($table) {
				$table->integer('player_height')->unsigned()->nullable()->after('player_width');
			});
		}
		if (!Capsule::schema()->hasColumn('learning_module_versions', 'is_skillscan')) {
			Capsule::schema()->table('learning_module_versions', function ($table) {
				$table->boolean('is_skillscan')->default(false)->after('player_height');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_modules', 'is_skill')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->boolean('is_skill')->default(false)->after('category_id');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_modules', 'repetition_period')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->string('repetition_period')->after('category_id');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_module_versions', 'created_by')) {
			Capsule::schema()->table('learning_module_versions', function ($table) {
				$table->integer('created_by')->unsigned()->nullable()->after('is_skillscan');
			});
		}
		if (!Capsule::schema()->hasColumn('learning_module_versions', 'updated_by')) {
			Capsule::schema()->table('learning_module_versions', function ($table) {
				$table->integer('updated_by')->unsigned()->nullable()->after('created_by');
			});
		}


		// Add boolean to comments table that will indicate that this comment ir URL and needs to be generated as clickable link
		if (!Capsule::schema()->hasColumn('comments', 'url')) {
			Capsule::schema()->table('comments', function ($table) {
				$table->boolean('url')->default(false)->after('visible_learner');
			});
		}

		DB::statement("ALTER TABLE custom_reviews_filters ROW_FORMAT=DYNAMIC;");
		DB::statement("ALTER TABLE resource_query_variables ROW_FORMAT=DYNAMIC;");

		// Increase size of teams_id
		if (Capsule::schema()->hasColumn('users', 'teams_id')) {
			Capsule::schema()->table('users', function ($table) {
				$table->string('teams_id', 400)->change();
			});
		}

		// Increase refresh_custom_email_body size
		if (Capsule::schema()->hasColumn('learning_modules', 'refresh_custom_email_body')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->mediumText('refresh_custom_email_body')->nullable()->default(null)->change();
			});
		}


		// add Badge field to user_competencies table
		if (!Capsule::schema()->hasColumn('user_competencies', 'badge')) {
			Capsule::schema()->table('user_competencies', function ($table) {
				$table->string('badge', 256)->nullable()->after('acquired_at');
			});
		} else if (Capsule::schema()->hasColumn('user_competencies', 'badge')) {
			Capsule::schema()->table('user_competencies', function ($table) {
				$table->string('badge', 256)->change();
			});
		}

		//Add badge field to competencies table
		if (!Capsule::schema()->hasColumn('competencies', 'badge')) {
			Capsule::schema()->table('competencies', function ($table) {
				$table->string('badge', 255)->nullable()->after("description");
			});
		}

		//Add cancellation reason in schedule links
		if (!Capsule::schema()->hasColumn('schedule_links', 'cancellation_reason')) {
			Capsule::schema()->table('schedule_links', function ($table) {
				$table->string('cancellation_reason', 255)->nullable()->after("deleted_by");
			});
		}



		// Rename PriorAttain to "PriorAttainLegacy", PriorAttain field will be new field with different data.
		if (
			!Capsule::schema()->hasColumn('users', 'PriorAttainLegacy') &&
			Capsule::schema()->hasColumn('users', 'PriorAttain')
		) {
			Capsule::schema()->table('users', function ($table) {
				$table->renameColumn('PriorAttain', 'PriorAttainLegacy');
			});
		}
		if (!Capsule::schema()->hasColumn('users', 'PriorAttain')) {
			Capsule::schema()->table('users', function ($table) {
				$table->text('PriorAttain')->nullable()->after('PriorAttainLegacy')->comment = "The learner's prior attainment when a new learning agreement has been agreed between the learner and the provider.";
			});
		}
		if (!Capsule::schema()->hasColumn('learning_modules', 'deleted_at')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->softDeletes();
			});
		}

		// Some minor changes to schedule tables to preserve date in case of deletion, not yet full audit!
		if (!Capsule::schema()->hasColumn('schedules', 'deleted_at')) {
			Capsule::schema()->table('schedules', function ($table) {
				$table->softDeletes();
			});
		}
		if (!Capsule::schema()->hasColumn('schedule_links', 'deleted_at')) {
			Capsule::schema()->table('schedule_links', function ($table) {
				$table->softDeletes();
			});
		}
		if (!Capsule::schema()->hasColumn('schedules', 'deleted_by')) {
			Capsule::schema()->table('schedules', function ($table) {
				$table->integer('deleted_by')->unsigned()->nullable()->after("status");
			});
		}
		if (!Capsule::schema()->hasColumn('schedule_links', 'deleted_by')) {
			Capsule::schema()->table('schedule_links', function ($table) {
				$table->integer('deleted_by')->unsigned()->nullable()->after("status");
			});
		}

		if (!Capsule::schema()->hasColumn('forums', 'deleted_at')) {
			Capsule::schema()->table('forums', function ($table) {
				$table->softDeletes();
			});
		}
		if (!Capsule::schema()->hasColumn('topics', 'deleted_at')) {
			Capsule::schema()->table('topics', function ($table) {
				$table->softDeletes();
			});
		}
		if (!Capsule::schema()->hasColumn('posts', 'deleted_at')) {
			Capsule::schema()->table('posts', function ($table) {
				$table->softDeletes();
			});
		}

		if (!Capsule::schema()->hasColumn('schedule_links', 'ignore_email')) {
			Capsule::schema()->table('schedule_links', function ($table) {
				$table->boolean('ignore_email')->default(false)->after("instructor_lead");
			});
		}
		if (!Capsule::schema()->hasColumn('schedule_links', 'learner_requirement')) {
			Capsule::schema()->table('schedule_links', function ($table) {
				$table->text('learner_requirement')->nullable()->after("instructor_lead");
			});
		}
		if (!Capsule::schema()->hasColumn('learning_results', 'deleted_at')) {
			Capsule::schema()->table('learning_results', function ($table) {
				$table->softDeletes();
			});
		}
		if (!Capsule::schema()->hasColumn('learning_results', 'deleted_by')) {
			Capsule::schema()->table('learning_results', function ($table) {
				$table->integer('deleted_by')->unsigned()->nullable()->after("homework");
			});
		}

		if (!Capsule::schema()->hasColumn('learning_results', 'imported_at')) {
			Capsule::schema()->table('learning_results', function ($table) {
				$table->datetime('imported_at')->nullable()->default(null)->after("deleted_by");
			});
		}

		if (!Capsule::schema()->hasColumn('log_export_imports', 'parameters')) {
			Capsule::schema()->table('log_export_imports', function ($table) {
				$table->text('parameters')->after('type');
			});
		}

		if (!Capsule::schema()->hasColumn('schedules', 'outlook_integration')) {
			Capsule::schema()->table('schedules', function ($table) {
				$table->boolean('outlook_integration')->default(false)->after('visible_learner_task');
			});
		}

		if (!Capsule::schema()->hasColumn('schedules', 'outlook_refresh_token')) {
			Capsule::schema()->table('schedules', function ($table) {
				$table->text('outlook_refresh_token')->nullable()->after('outlook_integration');
			});
		}

		if (!Capsule::schema()->hasColumn('schedules', 'outlook_event_id')) {
			Capsule::schema()->table('schedules', function ($table) {
				$table->text('outlook_event_id')->nullable()->after('outlook_refresh_token');
			});
		} else {
			if(DB::connection()->getDoctrineColumn('schedules', 'outlook_event_id')->getType()->getName() != "text"){
				DB::statement("ALTER TABLE `schedules` CHANGE COLUMN `outlook_event_id` `outlook_event_id` text");
			}
		}

		// Keep outlook event response in here, just in case!
		if (!Capsule::schema()->hasColumn('learning_modules', 'updated_by')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->integer('updated_by')->unsigned()->nullable()->after('created_by_group');
			});
		}

		if (!Capsule::schema()->hasColumn('schedules', 'outlook_event_response')) {
			Capsule::schema()->table('schedules', function ($table) {
				$table->json('outlook_event_response')->nullable()->after('outlook_event_id');
			});
		}

		if (!Capsule::schema()->hasColumn('resource_queries', 'deleted_by')) {
			Capsule::schema()->table('resource_queries', function ($table) {
				$table->integer('deleted_by')->unsigned()->nullable()->after("created_by");
			});
		}

		if (!Capsule::schema()->hasColumn('forms', 'system_form')) {
			Capsule::schema()->table('forms', function ($table) {
				$table->boolean('system_form')->deafult(false)->after("has_sign_off");
			});
		}



		if (!Capsule::schema()->hasColumn('resource_queries', 'deleted_at')) {
			Capsule::schema()->table('resource_queries', function ($table) {
				$table->softDeletes();
			});
		}

		// Change from text to longtext
		if (Capsule::schema()->hasColumn('resource_queries', 'query_variable')) {
			Capsule::schema()->table('resource_queries', function ($table) {
				$table->longText('query_variable')->nullable()->default(null)->change();
			});
		}
		if (Capsule::schema()->hasColumn('resource_queries', 'raw_query')) {
			Capsule::schema()->table('resource_queries', function ($table) {
				$table->longText('raw_query')->nullable()->default(null)->change();
			});
		}


		$schedule_visit_types_index = DB::select('SHOW KEYS FROM schedule_visit_types WHERE Key_name= ?', ['schedule_visit_types_slug_index']);
		if (count($schedule_visit_types_index) == 0) {
			Capsule::schema()->table('schedule_visit_types', function ($table) {
				$table->index('slug');
			});
		}

		$schedule_links_index = DB::select('SHOW KEYS FROM schedule_links WHERE Key_name= ?', ['schedule_links_type_index']);
		if (count($schedule_links_index) == 0) {
			Capsule::schema()->table('schedule_links', function ($table) {
				$table->index('type');
				$table->index('completion_status');
			});
		}

		$apprenticeship_standards_users_index = DB::select('SHOW KEYS FROM apprenticeship_standards_users WHERE Key_name= ?', ['apprenticeship_standards_users_start_at_index']);
		if (count($apprenticeship_standards_users_index) == 0) {
			Capsule::schema()->table('apprenticeship_standards_users', function ($table) {
				$table->index('start_at');
				$table->index('completion_status');
			});
		}

		if (Capsule::schema()->hasColumn('configuration', 'type')) {
			Capsule::schema()->table('configuration', function ($table) {
				DB::statement("ALTER TABLE `configuration` CHANGE COLUMN `type` `type` ENUM('integer','string','boolean','text','list','select-list', 'list-definition','rich-text', 'picklist') NOT NULL DEFAULT 'integer' COLLATE 'utf8_unicode_ci' AFTER `description`");
			});
		}

		if (!Capsule::schema()->hasColumn('comments', 'deleted_at')) {
			Capsule::schema()->table('comments', function ($table) {
				$table->softDeletes();
			});
		}

		if (!Capsule::schema()->hasColumn('learning_results_comments', 'deleted_at')) {
			Capsule::schema()->table('learning_results_comments', function ($table) {
				$table->integer('deleted_by')->unsigned()->nullable()->after("status");
				$table->softDeletes();
			});
		}

		if (!Capsule::schema()->hasColumn('learning_results', 'approved')) {
			Capsule::schema()->table('learning_results', function ($table) {
				$table->boolean('approved')->default(true)->after("log_used");
			});
		}

		// Upgrade some fields to allow bigger number
		if (Capsule::schema()->hasColumn('users', 'time_spent')) {
			Capsule::schema()->table('users', function ($table) {
				$table->decimal('time_spent', 7, 2)->default(0)->change();
			});
		}
		if (Capsule::schema()->hasColumn('users', 'time_spent_smcr')) {
			Capsule::schema()->table('users', function ($table) {
				$table->decimal('time_spent_smcr', 7, 2)->default(0)->change();
			});
		}
		if (Capsule::schema()->hasColumn('users', 'time_spent_off_the_job_training')) {
			Capsule::schema()->table('users', function ($table) {
				$table->decimal('time_spent_off_the_job_training', 7, 2)->default(0)->change();
			});
		}

		if (Capsule::schema()->hasColumn('apprenticeship_standards_users', 'time_spent')) {
			Capsule::schema()->table('apprenticeship_standards_users', function ($table) {
				$table->decimal('time_spent', 7, 2)->default(0)->change();
			});
		}
		if (Capsule::schema()->hasColumn('apprenticeship_standards_users', 'time_spent_off_the_job_training')) {
			Capsule::schema()->table('apprenticeship_standards_users', function ($table) {
				$table->decimal('time_spent_off_the_job_training', 7, 2)->default(0)->change();
			});
		}
		if (Capsule::schema()->hasColumn('apprenticeship_standards_users', 'time_behind')) {
			Capsule::schema()->table('apprenticeship_standards_users', function ($table) {
				$table->decimal('time_behind', 7, 2)->default(0)->change();
			});
		}


		// Role table, exclude specific is_manager role from listing them in schedule.
		if (!Capsule::schema()->hasColumn('roles', 'exclude_manager_from_schedule')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->boolean('exclude_manager_from_schedule')->default(false)->after("email_disable_manager_notifications");
			});
		}

		if (!Capsule::schema()->hasColumn('configuration', 'select_values')) {
			Capsule::schema()->table('configuration', function ($table) {
				$table->json('select_values')->nullable()->after('type');
			});
		}

		if (!Capsule::schema()->hasColumn('companies', 'login_bg')) {
			Capsule::schema()->table('companies', function ($table) {
				$table->string('login_bg')->nullable()->default(NULL)->after('logo');
			});
		}
		if (!Capsule::schema()->hasColumn('companies', 'learner_bg')) {
			Capsule::schema()->table('companies', function ($table) {
				$table->string('learner_bg')->nullable()->default(NULL)->after('login_bg');
			});
		}
		if (!Capsule::schema()->hasColumn('companies', 'e_learning_thumbnail')) {
			Capsule::schema()->table('companies', function ($table) {
				$table->string('e_learning_thumbnail')->nullable()->default(NULL)->after('learner_bg');
			});
		}
		if (!Capsule::schema()->hasColumn('companies', 'e_learning_button_style')) {
			Capsule::schema()->table('companies', function ($table) {
				$table->string('e_learning_button_style')->nullable()->default(NULL)->after('e_learning_thumbnail');
			});
		}

		if (!Capsule::schema()->hasColumn('files', 'group')) {
			Capsule::schema()->table('files', function ($table) {
				$table->string('group')->nullable()->default(null)->after('table_name');
			});
		}
		if (!Capsule::schema()->hasColumn('comments', 'group')) {
			Capsule::schema()->table('comments', function ($table) {
				$table->string('group')->nullable()->default(null)->after('table_name');
			});
		}

		if (!Capsule::schema()->hasColumn('roles', 'show_creator_menu')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->boolean('show_creator_menu')->default(false)->after('description');
			});
		}
		if (!Capsule::schema()->hasColumn('roles', 'slug')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->string('slug')->nullable()->default(null)->after('name');
			});
		}
		if (!Capsule::schema()->hasColumn('user_learning_modules', 'deleted_at')) {
			Capsule::schema()->table('user_learning_modules', function ($table) {
				$table->softDeletes();
			});
		}
		if (!Capsule::schema()->hasColumn('user_learning_modules', 'created_by')) {
			Capsule::schema()->table('user_learning_modules', function ($table) {
				$table->integer('created_by')->unsigned()->nullable()->after("learning_module_id");
			});
		}
		if (!Capsule::schema()->hasColumn('user_learning_modules', 'deleted_by')) {
			Capsule::schema()->table('user_learning_modules', function ($table) {
				$table->integer('deleted_by')->unsigned()->nullable()->after("created_by");
			});
		}
		if (!Capsule::schema()->hasColumn('user_learning_modules', 'comment_link')) {
			Capsule::schema()->table('user_learning_modules', function ($table) {
				$table->string('comment_link')->default('')->after("deleted_by");
			});
		}
		if (!Capsule::schema()->hasColumn('user_learning_modules', 'comment_unlink')) {
			Capsule::schema()->table('user_learning_modules', function ($table) {
				$table->string('comment_unlink')->default('')->after("comment_link");
			});
		}

		if (!Capsule::schema()->hasColumn('learning_modules', 'deleted_at')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->integer('deleted_by')->unsigned()->nullable()->after("require_management_signoff");
				$table->softDeletes();
			});
		}

		if (!Capsule::schema()->hasColumn('configuration', 'secure')) {
			Capsule::schema()->table('configuration', function ($table) {
				$table->boolean('secure')->default(false)->after("status");
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_standards', 'course_credits')) {
			Capsule::schema()->table('apprenticeship_standards', function ($table) {
				$table->boolean('course_credits')->default(false)->after('type');
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_issue_categories', 'minimum_required_credits')) {
			Capsule::schema()->table('apprenticeship_issue_categories', function ($table) {
				$table->smallInteger('minimum_required_credits')->unsigned()->nullable()->default(null)->after('exclude_outcome');
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_standards_users', 'optional_outcome')) {
			Capsule::schema()->table('apprenticeship_standards_users', function ($table) {
				$table->string('optional_outcome')->nullable()->default(null)->after('ilr_link');
			});
		}
		if (!Capsule::schema()->hasColumn('apprenticeship_standards_users', 'paused')) {
			Capsule::schema()->table('apprenticeship_standards_users', function ($table) {
				$table->boolean('paused')->default(false)->after("ilr_link");
				$table->date('paused_start')->nullable()->default(null)->after("paused");
				$table->date('paused_end')->nullable()->default(null)->after("paused_start");
			});
		}

		if (!Capsule::schema()->hasColumn('learning_results', 'completion_date_custom_days')) {
			Capsule::schema()->table('learning_results', function ($table) {
				$table->integer('completion_date_custom_days')->nullable()->default(null)->after("completion_date_custom");
			});
		}

		if (!Capsule::schema()->hasColumn('learning_results', 'credits')) {
			Capsule::schema()->table('learning_results', function ($table) {
				$table->smallInteger('credits')->unsigned()->nullable()->default(null)->after('deleted_by');
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_standards', 'number_of_evidence_expected')) {
			Capsule::schema()->table('apprenticeship_standards', function ($table) {
				$table->integer('number_of_evidence_expected')->unsigned()->nullable()->default(2)->after('name');
			});
		}

		if (!Capsule::schema()->hasColumn('schedule_links', 'completed_by')) {
			Capsule::schema()->table('schedule_links', function ($table) {
				$table->integer('completed_by')->unsigned()->nullable()->default(NULL)->after('completed_at');
			});
		}
		if (!Capsule::schema()->hasColumn('schedule_links', 'updated_by')) {
			Capsule::schema()->table('schedule_links', function ($table) {
				$table->integer('updated_by')->unsigned()->nullable()->default(NULL)->after('status');
			});
		}

		if (!Capsule::schema()->hasColumn('schedules', 'outlook_start_date')) {
			Capsule::schema()->table('schedules', function ($table) {
				$table->datetime('outlook_start_date')->nullable()->after('outlook_event_id');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_modules', 'scorm_popup')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->boolean('scorm_popup')->default(false)->after('require_management_signoff');
			});
		}

		/*Add badge to learning Module*/
		if (!Capsule::schema()->hasColumn('learning_modules', 'badge')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->string('badge', 255)->nullable()->after("scorm_popup");
			});
		}
		/*Add badge to Programs*/
		if (!Capsule::schema()->hasColumn('apprenticeship_standards', 'badge')) {
			Capsule::schema()->table('apprenticeship_standards', function ($table) {
				$table->string('badge', 255)->nullable()->after("working_hours");
			});
		}

		if (!Capsule::schema()->hasColumn('venues', 'deleted_at')) {
			Capsule::schema()->table('venues', function ($table) {
				$table->softDeletes();
			});
		}

		if (!Capsule::schema()->hasColumn('smcr_reports', 'deleted_at')) {
			Capsule::schema()->table('smcr_reports', function ($table) {
				$table->integer('deleted_by')->unsigned()->nullable()->after("status");
				$table->softDeletes();
			});
		}

		if (!Capsule::schema()->hasColumn('table_history', 'table_id')) {
			Capsule::schema()->table('table_history', function ($table) {
				$table->integer('table_id')->unsigned()->after("table_name");
			});
		}

		if (!Capsule::schema()->hasColumn('user_learning_recommendations', 'is_dismissed')) {
			Capsule::schema()->table('user_learning_recommendations', function ($table) {
				$table->boolean('is_dismissed')->default(false)->after('learning_module_id');
			});
		}

		if (!Capsule::schema()->hasColumn('user_form_signoff', 'signoff_role')) {
			Capsule::schema()->table('user_form_signoff', function ($table) {
				$table->integer('signoff_role')->nullable()->after('signoff_at');
			});
		}

		// Add progree status to user_forms table
		if (!Capsule::schema()->hasColumn('user_forms', 'user_form_status')) {
			Capsule::schema()->table('user_forms', function ($table) {
				$table->enum('user_form_status', ['Completed', 'In Progress','Awaiting Sign-off', 'Not Started'])->default('Not Started')
				->after('status');
			});
		}

		if (!Capsule::schema()->hasColumn('document_template_bindings', 'type')) {
			Capsule::schema()->table('document_template_bindings', function ($table) {
				$table->enum('type', ['is_form', 'is_signature','is_report', 'is_graph'])
				->default('is_form')
				->after('is_signature');
			});
		}

		if (!Capsule::schema()->hasColumn('table_history', 'linked_user_id')) {
			Capsule::schema()->table('table_history', function ($table) {
				$table->integer('linked_user_id')->unsigned()->after('table_id');
			});
		}

		if (!Capsule::schema()->hasColumn('schedules', 'deleted_reason')) {
			Capsule::schema()->table('schedules', function ($table) {
				$table->string('deleted_reason')->nullable()->default(NULL)->after('deleted_by');
			});
		}
		if (!Capsule::schema()->hasColumn('schedules', 'deleted_at_user')) {
			Capsule::schema()->table('schedules', function ($table) {
				$table->datetime('deleted_at_user')->nullable()->default(NULL)->after('deleted_reason');
			});
		}

		if (!Capsule::schema()->hasColumn('schedule_visit_types', 'is_enable_days_since_last_review')) {
			Capsule::schema()->table('schedule_visit_types', function ($table) {
				$table->boolean('is_enable_days_since_last_review')->default(false)->after("status");
			});
		}
		if (!Capsule::schema()->hasColumn('users', 'days_since_last_review')) {
			Capsule::schema()->table('users', function ($table) {
				$table->integer('days_since_last_review')->unsigned()->nullable()->default(NULL)->after('status');
			});
		}

		if (!Capsule::schema()->hasColumn('user_learning_modules', 'ignore_events')) {
			Capsule::schema()->table('user_learning_modules', function ($table) {
				$table->boolean('ignore_events')->default(false)->after('comment_unlink');
			});
		}
		if (!Capsule::schema()->hasColumn('user_learning_module_archives', 'ignore_events')) {
			Capsule::schema()->table('user_learning_module_archives', function ($table) {
				$table->boolean('ignore_events')->default(false)->after('comment_unlink');
			});
		}

		if (!Capsule::schema()->hasColumn('roles', 'show_assign_learning')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->boolean('show_assign_learning')->default(true)->after('show_creator_menu');
			});
		}

		if (!Capsule::schema()->hasColumn('sessions', 'ip')) {
			Capsule::schema()->table('sessions', function ($table) {
				$table->string('ip')->after('api_access_count');
			});
		}
		if (!Capsule::schema()->hasColumn('sessions', 'deleted_at')) {
			Capsule::schema()->table('sessions', function ($table) {
				$table->softDeletes();
			});
		}

		if (!Capsule::schema()->hasColumn('learning_modules', 'created_by_event')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->boolean('created_by_event')->nullable()->default(null)->after('is_course');
			});
		}

		if (!Capsule::schema()->hasColumn('users', 'created_by')) {
			Capsule::schema()->table('users', function ($table) {
				$table->integer('created_by')->unsigned()->nullable()->default(null)->after('certified');
			});
		}

		if (!Capsule::schema()->hasColumn('users', 'creation_notes')) {
			Capsule::schema()->table('users', function ($table) {
				$table->text('creation_notes')->nullable()->default(null)->after('created_by');
			});
		}

		// make value nullable
		if (Capsule::schema()->hasColumn('user_form_values', 'value')) {
			Capsule::schema()->table('user_form_values', function ($table) {
				$table->text('value')->nullable()->change();
			});
		}

		// add type column
		if (!Capsule::schema()->hasColumn('user_form_values', 'type')) {
			Capsule::schema()->table('user_form_values', function ($table) {
				$table->string('type')->nullable()->after('value');
			});
		}

		// add type_id column
		if (!Capsule::schema()->hasColumn('user_form_values', 'type_id')) {
			Capsule::schema()->table('user_form_values', function ($table) {
				$table->integer('type_id')->nullable()->after('type');
			});
		}

		if (Capsule::schema()->hasColumn('form_logs', 'form_field')) {
			Capsule::schema()->table('form_logs', function ($table) {
				$table->json('form_field')->nullable()->change();
			});
		}
		// add type_id column
		if (!Capsule::schema()->hasColumn('custom_field_query_bindings', 'fetch_value_type')) {
			Capsule::schema()->table('custom_field_query_bindings', function ($table) {
				$table->enum('fetch_value_type', ['current_value', 'previous_value'])->nullable()->after('type_id');
			});
		}
		if (!Capsule::schema()->hasColumn('custom_query_bindings', 'fetch_value_type')) {
			Capsule::schema()->table('custom_query_bindings', function ($table) {
				$table->enum('fetch_value_type', ['current_value', 'previous_value'])->nullable()->after('type_id');
			});
		}

		if (!Capsule::schema()->hasColumn('roles', 'hide_forms_sign_off_tab')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->boolean('hide_forms_sign_off_tab')->default(true)->after('hide_sign_off_tab');
			});
		}

		// Increase size of before and after
		if (Capsule::schema()->hasColumn('table_history', 'before')) {
			Capsule::schema()->table('table_history', function ($table) {
				$table->longText('before')->change();
			});
		}
		if (Capsule::schema()->hasColumn('table_history', 'after')) {
			Capsule::schema()->table('table_history', function ($table) {
				$table->longText('after')->change();
			});
		}

		if (Capsule::schema()->hasColumn('user_form_signoff', 'signoff_at')) {
			Capsule::schema()->table('user_form_signoff', function ($table) {
				$table->date('signoff_at')->nullable()->change();
			});
		}

		if (Capsule::schema()->hasColumn('user_form_signoff', 'e_signature')) {
			Capsule::schema()->table('user_form_signoff', function ($table) {
				$table->string('e_signature')->nullable()->change();
			});
		}
		if (!Capsule::schema()->hasColumn('user_form_signoff', 'status')) {
			Capsule::schema()->table('user_form_signoff', function ($table) {
				$table->string('status')->nullable()->after('e_signature');
			});
		}

		if (!Capsule::schema()->hasColumn('document_templates', 'icon')) {
			Capsule::schema()->table('document_templates', function ($table) {
				$table->string('icon')->default('glyphicon-book')->after('template');
			});
		}
		if (!Capsule::schema()->hasColumn('department_standards', 'cron_task')) {
			Capsule::schema()->table('department_standards', function ($table) {
				$table->boolean('cron_task')->default(false)->after('due_at');
			});
		}
		if (!Capsule::schema()->hasColumn('group_standards', 'cron_task')) {
			Capsule::schema()->table('group_standards', function ($table) {
				$table->boolean('cron_task')->default(false)->after('due_at');
			});
		}

		if (!Capsule::schema()->hasColumn('department_standards', 'removed_status')) {
			Capsule::schema()->table('department_standards', function ($table) {
				$table->boolean('removed_status')->default(false)->after('due_at');
			});
		}
		if (!Capsule::schema()->hasColumn('group_standards', 'removed_status')) {
			Capsule::schema()->table('group_standards', function ($table) {
				$table->boolean('removed_status')->default(false)->after('due_at');
			});
		}

		if (!Capsule::schema()->hasColumn('roles', 'admin_interface_manage_programmes')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->boolean('admin_interface_manage_programmes')->default(true)->after('admin_interface');
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_standards_users', 'time_required')) {
			Capsule::schema()->table('apprenticeship_standards_users', function ($table) {
				$table->decimal('time_required', 7, 2)->default(0)->after('time_spent_off_the_job_training');
			});
		}

		if (!Capsule::schema()->hasColumn('user_form_template_workflow_relations', 'reference_type')) {
			Capsule::schema()->table('user_form_template_workflow_relations', function ($table) {
				$table->string('reference_type')->nullable()->after('assigned_method');
			});
		}

		if (!Capsule::schema()->hasColumn('user_form_template_workflow_relations', 'reference_type_id')) {
			Capsule::schema()->table('user_form_template_workflow_relations', function ($table) {
				$table->integer('reference_type_id')->nullable()->after('assigned_method');
			});
		}

		if (!Capsule::schema()->hasColumn('user_forms', 'reference_type')) {
			Capsule::schema()->table('user_forms', function ($table) {
				$table->string('reference_type')->nullable()->after('type_id');
			});
		}

		if (!Capsule::schema()->hasColumn('user_forms', 'reference_type_id')) {
			Capsule::schema()->table('user_forms', function ($table) {
				$table->integer('reference_type_id')->nullable()->after('type_id');
			});
		}

		if (Capsule::schema()->hasColumn('user_form_template_workflow_relations', 'assigned_method')) {
			Capsule::schema()->table('user_form_template_workflow_relations', function ($table) {
				DB::statement("ALTER TABLE user_form_template_workflow_relations CHANGE COLUMN assigned_method assigned_method ENUM('form_direct_assign','form_schedule_assign','template_direct_assign','workflow_direct_assign','workflow_schedule_event_assign','workflow_schedule_visit_assign','workflow_lesson_assign','workflow_schedule_lesson_assign','workflow_forms_assign','workflow_programme_assign', 'workflow_schedule_programme_assign') NULL DEFAULT NULL");
			});
		}

		if (!Capsule::schema()->hasColumn('user_forms', 'deleted_at')) {
			Capsule::schema()->table('user_forms', function ($table) {
				$table->softDeletes();
			});
		}

		if (!Capsule::schema()->hasColumn('user_custom_form_values', 'deleted_at')) {
			Capsule::schema()->table('user_custom_form_values', function ($table) {
				$table->softDeletes();
			});
		}

		if (!Capsule::schema()->hasColumn('user_form_assigned_types', 'deleted_at')) {
			Capsule::schema()->table('user_form_assigned_types', function ($table) {
				$table->softDeletes();
			});
		}

		if (!Capsule::schema()->hasColumn('user_form_signoff', 'deleted_at')) {
			Capsule::schema()->table('user_form_signoff', function ($table) {
				$table->softDeletes();
			});
		}

		if (!Capsule::schema()->hasColumn('user_form_template_workflow_relations', 'deleted_at')) {
			Capsule::schema()->table('user_form_template_workflow_relations', function ($table) {
				$table->softDeletes();
			});
		}

		if (!Capsule::schema()->hasColumn('user_form_values', 'deleted_at')) {
			Capsule::schema()->table('user_form_values', function ($table) {
				$table->softDeletes();
			});
		}

		if (!Capsule::schema()->hasColumn('user_workflow_form', 'deleted_at')) {
			Capsule::schema()->table('user_workflow_form', function ($table) {
				$table->softDeletes();
			});
		}
		if (!Capsule::schema()->hasColumn('user_form_template_workflow_relations', 'user_workflow_id')) {
			Capsule::schema()->table('user_form_template_workflow_relations', function ($table) {
				$table->integer('user_workflow_id')->nullable()->after('workflow_id');
			});
		}

		if (Capsule::schema()->hasColumn('signoff_logs', 'type')) {
			Capsule::schema()->table('signoff_logs', function ($table) {
				$table->string('type')->change();
			});
		}

		if (Capsule::schema()->hasColumn('signoff_logs', 'boolean')) {
			Capsule::schema()->table('signoff_logs', function ($table) {
				$table->dropColumn('boolean');
			});
		}

		if (!Capsule::schema()->hasColumn('signoff_logs', 'status')) {
			Capsule::schema()->table('signoff_logs', function ($table) {
				$table->enum('status',['Not Attempted', 'In Progress', 'Completed'])->after('sign_off_role_id');
			});
		}

		if (!Capsule::schema()->hasColumn('signoff_logs', 'user_id')) {
			Capsule::schema()->table('signoff_logs', function ($table) {
				$table->integer('user_id')->after('type_id');
			});
		}

		if (!Capsule::schema()->hasColumn('roles', 'admin_interface_programmes_action')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->boolean('admin_interface_programmes_action')->default(true)->after('admin_interface');
			});
		}

		if (!Capsule::schema()->hasColumn('resource_queries', 'query_builder_type')) {
			Capsule::schema()->table('resource_queries', function ($table) {
				$table->enum('query_builder_type',['query_builder', 'sql_query'])->default('query_builder')->after('action');
			});
		}
		if (!Capsule::schema()->hasColumn('roles', 'admin_interface_edit_users')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->boolean('admin_interface_edit_users')->default(true)->after('admin_interface_programmes_action');
			});
		}
		if (!Capsule::schema()->hasColumn('roles', 'allow_assigning_manager_roles')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->boolean('allow_assigning_manager_roles')->default(false)->after('show_assign_learning');
			});
		}
		if (!Capsule::schema()->hasColumn('roles', 'admin_interface_missing_learners')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->boolean('admin_interface_missing_learners')->default(false)->after('admin_interface_edit_users');
			});
		}
		if (!Capsule::schema()->hasColumn('roles', 'admin_interface_assing_learners_programme')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->boolean('admin_interface_assing_learners_programme')->default(true)->after('admin_interface_missing_learners');
			});
		}

		if (!Capsule::schema()->hasColumn('forms', 'is_common_form')) {
			Capsule::schema()->table('forms', function ($table) {
				$table->boolean('is_common_form')->default(false)->after('system_form');
			});
		}

		if (!Capsule::schema()->hasColumn('form_workflow', 'icon')) {
			Capsule::schema()->table('form_workflow', function ($table) {
				$table->string('icon')->default('glyphicon-book')->after('name');
			});
		}
		if (!Capsule::schema()->hasColumn('roles', 'admin_interface_add_users')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->boolean('admin_interface_add_users')->default(true)->after('admin_interface_programmes_action');
			});
		}
		if (!Capsule::schema()->hasColumn('roles', 'admin_interface_add_users_assign')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->boolean('admin_interface_add_users_assign')->default(false)->after('admin_interface_add_users');
			});
		}

		if (!Capsule::schema()->hasColumn('roles', 'allow_change_department_self')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->boolean('allow_change_department_self')->default(true)->after('show_assign_learning');
			});
		}

		if (!Capsule::schema()->hasColumn('user_form_signoff', 'signoff_by')) {
			Capsule::schema()->table('user_form_signoff', function ($table) {
				$table->integer('signoff_by')->nullable()->after('signoff_role');
			});
		}
		if (!Capsule::schema()->hasColumn('roles', 'update_managers_assign_categories')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->boolean('update_managers_assign_categories')->default(true)->after('allow_change_department_self');
			});
		}
		if (!Capsule::schema()->hasColumn('roles', 'update_managers_assign_departments')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->boolean('update_managers_assign_departments')->default(true)->after('update_managers_assign_categories');
			});
		}
		if (!Capsule::schema()->hasColumn('roles', 'update_managers_assign_groups')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->boolean('update_managers_assign_groups')->default(true)->after('update_managers_assign_departments');
			});
		}
		if (!Capsule::schema()->hasColumn('roles', 'update_managers_assign_employees')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->boolean('update_managers_assign_employees')->default(true)->after('update_managers_assign_groups');
			});
		}
		if (!Capsule::schema()->hasColumn('roles', 'update_managers_assign_resources')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->boolean('update_managers_assign_resources')->default(true)->after('update_managers_assign_employees');
			});
		}

		if (!Capsule::schema()->hasColumn('email_history', 'duplicate_count')) {
			Capsule::schema()->table('email_history', function ($table) {
				$table->integer('duplicate_count')->unsigned()->default(0)->after('error');
			});
		}
		if (!Capsule::schema()->hasColumn('email_history', 'sent_count')) {
			Capsule::schema()->table('email_history', function ($table) {
				$table->integer('sent_count')->unsigned()->default(0)->after('sent');
			});
		}

		if (!Capsule::schema()->hasColumn('email_history', 'data_hash')) {
			Capsule::schema()->table('email_history', function ($table) {
				$table->string('data_hash')->default('')->after('sent_count');
				$table->index('data_hash');
				$table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_modules', 'accreditation_alternative_learning_name')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->string('accreditation_alternative_learning_name')->default('')->after('accreditation_description');
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_issues', 'week')) {
			Capsule::schema()->table('apprenticeship_issues', function ($table) {
				$table->integer('week')->nullable()->after('visible_resource');
			});
		}
		if (!Capsule::schema()->hasColumn('graphs', 'filter')) {
			Capsule::schema()->table('graphs', function ($table) {
				$table->json('filter')->default('[]')->after('category');
			});
		}
		if (!Capsule::schema()->hasColumn('schedule_links', 'created_by')) {
			Capsule::schema()->table('schedule_links', function ($table) {
				$table->integer('created_by')->unsigned()->nullable()->default(NULL)->after('status');
			});
		}
		if (!Capsule::schema()->hasColumn('graphs','has_dynamic_filter')) {
			Capsule::schema()->table('graphs',function($table) {
				$table->boolean('has_dynamic_filter')->default(false);
			});
		}
		if (Capsule::schema()->hasColumn('document_template_bindings','custom_report_id')) {
			Capsule::schema()->table('document_template_bindings',function($table) {
				$table->integer('custom_report_id')->nullable()->change();
			});
		}

		/**
		 * permission for edit after signoff
		 */
		if (!Capsule::schema()->hasColumn('form_signoff_roles', 'edit_after_sign_off_permission')) {
			Capsule::schema()->table('form_signoff_roles', function ($table) {
				$table->boolean('edit_after_sign_off_permission')->default(true)->after('form_id');
			});
		}

		if (!Capsule::schema()->hasColumn('graphs','has_dynamic_filter')) {
			Capsule::schema()->table('graphs',function($table){
				$table->boolean('has_dynamic_filter')->default(false);
			});
		}
		if (!Capsule::schema()->hasColumn('document_templates', 'is_custom_report')) {
			Capsule::schema()->table('document_templates', function ($table) {
				$table->boolean('is_custom_report')->default(false)->after('status');
			});
		}
		if (!Capsule::schema()->hasColumn('learning_modules', 'disable_upon_completion')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->boolean('disable_upon_completion')->default(false)->after('badge');
			});
		}

		if (!Capsule::schema()->hasColumn('available_modules', 'status')) {
			Capsule::schema()->table('available_modules', function ($table) {
				$table->boolean('status')->default(true)->after('name');
			});
		}

		if (!Capsule::schema()->hasColumn('roles', 'attach_files_to_comments')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->boolean('attach_files_to_comments')->default(false)->after('update_managers_assign_resources');
			});
		}

		if (!Capsule::schema()->hasColumn('roles', 'learner_can_delete_files_from_comments')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->boolean('learner_can_delete_files_from_comments')->default(false)->after('attach_files_to_comments');
			});
		}
		//\Models\Field::updateView();
		//\Models\Field::updateView();


		if (!Capsule::schema()->hasColumn('custom_report_fields', 'order')) {
			Capsule::schema()->table('custom_report_fields', function ($table) {
				$table->integer('order')->unsigned()->default(0)->after('column_name');
			});
		}
		if(!Capsule::schema()->hasColumn('graphs','user_specific_filter'))
		{
			Capsule::schema()->table('graphs',function($table){
				$table->string('user_specific_filter')->nullable()->after('data_limit');
			});
		}
		if (!Capsule::schema()->hasColumn('custom_reports', 'filters')) {
			Capsule::schema()->table('custom_reports', function ($table) {
				$table->json('filters')->nullable()->after('icon');
			});
		}
		if(!Capsule::schema()->hasColumn('custom_reports','user_specific_filter'))
		{
			Capsule::schema()->table('custom_reports',function($table){
				$table->string('user_specific_filter')->nullable()->after('filters');
			});
		}
		if (!Capsule::schema()->hasColumn('learning_modules', 'open_in_events_only')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->boolean('open_in_events_only')->default(false)->after('print_lesson');
			});
		}
		if (!Capsule::schema()->hasColumn('learning_modules', 'refresh_all_attached_learning_resources')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->boolean('refresh_all_attached_learning_resources')->default(true)->after('refresh_repeat');
			});
		}
		if (!Capsule::schema()->hasColumn('learning_modules', 'copy_refresher_emails_to_line_managers')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->boolean('copy_refresher_emails_to_line_managers')->default(false)->after('refresh_custom_email_body');
			});
		}

		if (!Capsule::schema()->hasColumn('schedules', 'refreshed')) {
			Capsule::schema()->table('schedules', function ($table) {
				$table->boolean('refreshed')->default(false)->after('outlook_start_date');
			});
		}

		if (!Capsule::schema()->hasColumn('schedule_links', 'refreshed')) {
			Capsule::schema()->table('schedule_links', function ($table) {
				$table->boolean('refreshed')->default(false)->after('deleted_by');
			});
		}
		if (Capsule::schema()->hasColumn('dataviews', 'name_id')) {
			Capsule::schema()->table('dataviews', function ($table) {
				$table->string('name_id')->nullable()->change();
			});
		}
		if (Capsule::schema()->hasColumn('apprenticeship_standards', 'type')) {
			Capsule::schema()->table('apprenticeship_standards', function ($table) {
				DB::statement("ALTER TABLE `apprenticeship_standards` CHANGE COLUMN `type` `type` ENUM('Standards','Frameworks','Qualifications','Skills Monitoring') NOT NULL DEFAULT 'Standards'");
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_standards', 'repetition_period')) {
			Capsule::schema()->table('apprenticeship_standards', function ($table) {
				$table->integer('repetition_period')->nullable()->default(null)->after('badge');
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_standards', 'default_start_month')) {
			Capsule::schema()->table('apprenticeship_standards', function ($table) {
				$table->string('default_start_month')->nullable()->default(null)->after('repetition_period');
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_standards', 'default_skill_repetition_period')) {
			Capsule::schema()->table('apprenticeship_standards', function ($table) {
				$table->integer('default_skill_repetition_period')->nullable()->default(null)->after('default_start_month');
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_standards_users', 'sign_off_trainee_at')) {
			Capsule::schema()->table('apprenticeship_standards_users', function ($table) {
				$table->boolean('sign_off_trainee')->default(false)->after('expected_time');
				$table->date('sign_off_trainee_at')->nullable()->after('expected_time');
				$table->boolean('sign_off_manager')->default(false)->after('expected_time');
				$table->date('sign_off_manager_at')->nullable()->after('expected_time');
				$table->integer('sign_off_manager_by')->unsigned()->nullable()->after('expected_time');
				$table->text('manager_refused_comment')->nullable()->after('expected_time');
				$table->datetime('manager_refused_time')->nullable()->after('expected_time');
				$table->integer('manager_refused_by')->unsigned()->nullable()->after('expected_time');
			});
		}

		if (!Capsule::schema()->hasColumn('schedules', 'do_not_send_advance_notifications')) {
			Capsule::schema()->table('schedules', function ($table) {
				$table->boolean('do_not_send_advance_notifications')->default(false)->after('outlook_start_date');
			});
		}

		if (!Capsule::schema()->hasColumn('manager_users', 'created_by')) {
			Capsule::schema()->table('manager_users', function ($table) {
				$table->integer('created_by')->unsigned()->nullable()->after('user_id');
				$table->string('comment_link')->default('');
				$table->boolean('ignore_events')->default(false);
			});
		}

		if (Capsule::schema()->hasColumn('users', 'account_type')) {
			Capsule::schema()->table('users', function ($table) {
				$table->dropColumn('account_type');
			});
		}

		if (!Capsule::schema()->hasColumn('users', 'account_type_id')) {
			Capsule::schema()->table('users', function ($table) {
				$table->integer('account_type_id')->unsigned()->nullable()->default(null)->after('teams_id');
			});
		}

		if (!Capsule::schema()->hasColumn('roles', 'show_skill_monitoring')) {
			Capsule::schema()->table('roles', function ($table) {
				$table->boolean('show_skill_monitoring')->default(true)->after('learner_can_delete_files_from_comments');
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_standards_users', 'time_spent_last_log')) {
			Capsule::schema()->table('apprenticeship_standards_users', function ($table) {
				$table->longText('time_spent_last_log')->nullable()->after('time_spent');
			});

		}
		//**************For Learning Modules***************
		// For Group Learning Modules
		if(!Capsule::schema()->hasColumn('group_learning_modules','is_assigner_admin')){
		  Capsule::schema()->table('group_learning_modules',function($table){
			  $table->boolean('is_assigner_admin')->default(false)->after('cron_status'); // Check assigner is admin or not
		  });
		}
		if(!Capsule::schema()->hasColumn('group_learning_modules','assigner_id')){
		  Capsule::schema()->table('group_learning_modules',function($table){
			$table->integer('assigner_id')->unsigned()->nullable()->default(null)->after('is_assigner_admin');
		  });
		}

			// For Departments Learning Modules
		if(!Capsule::schema()->hasColumn('department_learning_modules','is_assigner_admin')){
		  Capsule::schema()->table('department_learning_modules',function($table){
			  $table->boolean('is_assigner_admin')->default(false)->after('cron_status'); // Check assigner is admin or not
		  });
		}
		if(!Capsule::schema()->hasColumn('department_learning_modules','assigner_id')){
		  Capsule::schema()->table('department_learning_modules',function($table){
			$table->integer('assigner_id')->unsigned()->nullable()->default(null)->after('is_assigner_admin');
		  });
		}
		// For Designations/Jobs Learning Modules
		if(!Capsule::schema()->hasColumn('designation_learning_modules','is_assigner_admin')){
		  Capsule::schema()->table('designation_learning_modules',function($table){
			  $table->boolean('is_assigner_admin')->default(false)->after('cron_status'); // Check assigner is admin or not
		  });
		}
		if(!Capsule::schema()->hasColumn('designation_learning_modules','assigner_id')){
		  Capsule::schema()->table('designation_learning_modules',function($table){
			$table->integer('assigner_id')->unsigned()->nullable()->default(null)->after('is_assigner_admin');
		  });
		}
		//**************For Programs***************
		// For Group Programs
		if (!Capsule::schema()->hasColumn('group_standards', 'is_assigner_admin')) {
			Capsule::schema()->table('group_standards', function ($table) {
				$table->boolean('is_assigner_admin')->default(false)->after('cron_task'); // Check assigner is admin or not
			});
		}
		if (!Capsule::schema()->hasColumn('group_standards', 'assigner_id')) {
			Capsule::schema()->table('group_standards', function ($table) {
				$table->integer('assigner_id')->unsigned()->nullable()->default(null)->after('is_assigner_admin');
			});
		}

		if (!Capsule::schema()->hasColumn('graphs', 'slug')) {
			Capsule::schema()->table('graphs', function ($table) {
				$table->string('slug')->nullable()->after('name');//->unique();
			});
		}
		if (!Capsule::schema()->hasColumn('custom_reports', 'slug')) {
			Capsule::schema()->table('custom_reports', function ($table) {
				$table->string('slug')->nullable()->after('name');//->unique();
			});
		}
		if (!Capsule::schema()->hasColumn('form_workflow', 'slug')) {
			Capsule::schema()->table('form_workflow', function ($table) {
				$table->string('slug')->nullable()->after('name');//->unique();
			});
		}
		if (!Capsule::schema()->hasColumn('document_templates', 'slug')) {
			Capsule::schema()->table('document_templates', function ($table) {
				$table->string('slug')->nullable()->after('name');//->unique();
			});
		}

		// For Departments Programs
		if (!Capsule::schema()->hasColumn('department_standards', 'is_assigner_admin')) {
			Capsule::schema()->table('department_standards', function ($table) {
				$table->boolean('is_assigner_admin')->default(false)->after('cron_task'); // Check assigner is admin or not
			});
		}
		if (!Capsule::schema()->hasColumn('department_standards', 'assigner_id')) {
			Capsule::schema()->table('department_standards', function ($table) {
				$table->integer('assigner_id')->unsigned()->nullable()->default(null)->after('is_assigner_admin');
			});
		}

		if (!Capsule::schema()->hasColumn('schedules', 'all_day_event')) {
			Capsule::schema()->table('schedules', function ($table) {
				$table->boolean('all_day_event')->default(false)->after('end_date');
			});
		}
		if (!Capsule::schema()->hasColumn('configuration', 'default_value')) {
			Capsule::schema()->table('configuration', function ($table) {
				$table->string('default_value', 2000)->default(null)->nullable()->after('value');
			});
		}
		// status is true when creating new type.
		if (Capsule::schema()->hasColumn('evidence_types', 'status')) {
			Capsule::schema()->table('evidence_types', function ($table) {
				$table->boolean('status')->default(true)->change();
			});
		}

		if (!Capsule::schema()->hasColumn('users', 'startup_instructions_sent')) {
			Capsule::schema()->table('users', function ($table) {
				$table->boolean('startup_instructions_sent')->default(false)->after('status');
				$table->dateTime('startup_instructions_sent_at')->nullable()->default(null)->after('status');
			});
		}
		if(!Capsule::schema()->hasColumn('forms','enable_snooze_signoff_form'))
		{
			Capsule::schema()->table('forms',function($table){
				$table->boolean('enable_snooze_signoff_form')->default(false)->after('status');
			});
		}
		if(!Capsule::schema()->hasColumn('user_forms','snooze_target_date'))
		{
			Capsule::schema()->table('user_forms',function($table){
				$table->date('snooze_target_date')->nullable()->after('status');
			});
		}

		// Remove unique constrain from username field, easier said than done
		Capsule::schema()->table('users', function ($table) {
			$sm = Capsule::schema()->getConnection()->getDoctrineSchemaManager();
			$indexesFound = $sm->listTableIndexes('users');

			if (array_key_exists("UQ_Username", $indexesFound)) {
				$table->dropUnique("UQ_Username");
			}
			if (array_key_exists("users_username_unique", $indexesFound)) {
				$table->dropUnique("users_username_unique");
			}
		});
		// scorm_user as well
		Capsule::schema()->table('scorm_user', function ($table) {
			$sm = Capsule::schema()->getConnection()->getDoctrineSchemaManager();
			$indexesFound = $sm->listTableIndexes('scorm_user');

			if (array_key_exists("unique_key_sdklj3sask2", $indexesFound)) {
				$table->dropUnique("unique_key_sdklj3sask2");
			}
		});

		// Add 2 fields for lancs
		if (!Capsule::schema()->hasColumn('users', 'position_ref')) {
			Capsule::schema()->table('users', function ($table) {
				$table->string('position_ref')->nullable()->default(null)->after('account_type_id');
				$table->index('position_ref');
			});
		}
		if (!Capsule::schema()->hasColumn('users', 'watch')) {
			Capsule::schema()->table('users', function ($table) {
				$table->string('watch')->nullable()->default(null)->after('position_ref');
				$table->index('watch');
			});
		}


		if (Capsule::schema()->hasColumn('learning_modules', 'repetition_period')) {
			DB::statement("ALTER TABLE learning_modules CHANGE repetition_period repetition_period INT DEFAULT NULL");
		}
		if (!Capsule::schema()->hasColumn('venues', 'instructions')) {
			Capsule::schema()->table('venues', function (Blueprint $table) {
				$table->longText('instructions')->nullable()->after('status');
			});
		}

		if (!Capsule::schema()->hasColumn('user_custom_programme_statuses', 'attachments')) {
			Capsule::schema()->table('user_custom_programme_statuses', function (Blueprint $table) {
				$table->text('attachments')->nullable()->after('notes');
			});
		}

		if (!Capsule::schema()->hasColumn('venues', 'image')) {
			Capsule::schema()->table('venues', function (Blueprint $table) {
				$table->string('image')->nullable()->after('instructions');
			});
		}

		if (!Capsule::schema()->hasColumn('batch_report_data', 'file_name')) {
			Capsule::schema()->table('batch_report_data', function ($table) {
				$table->string('file_name')->nullable()->default(null)->after('run_time');
				$table->binary('data')->nullable()->default(null)->change();
			});
		}

		if (!Capsule::schema()->hasColumn('surveys', 'type')) {
			Capsule::schema()->table('surveys', function ($table) {
				$table->string('type')->default('likert');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_modules', 'is_survey')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->boolean('is_survey')->default(false)->after("is_skillscan");
			});
		}
		if (!Capsule::schema()->hasColumn('powerbi_reports', 'last_updated')) {
			DB::statement("ALTER TABLE powerbi_reports MODIFY powerbi_embed_url text NULL;");
			DB::statement("ALTER TABLE powerbi_reports MODIFY powerbi_report_id VARCHAR(255) NULL;");
			Capsule::schema()->table('powerbi_reports', function ($table) {
				$table->dateTime('last_updated')->nullable()->after('status');
				$table->boolean('queue')->default(0)->after('last_updated');
			});
		}

		if(!Capsule::schema()->hasColumn('learning_results','restored_by')){
			Capsule::schema()->table('learning_results',function($table){
				$table->integer('restored_by')->nullable()->after('deleted_by');
			});
		}

		if(!Capsule::schema()->hasColumn('learning_results','quiz_fails')){
			Capsule::schema()->table('learning_results',function($table){
				$table->integer('quiz_fails')->nullable()->after('score');
			});
		}

		if(!Capsule::schema()->hasColumn('learning_results','attempts_at_refresh')){
			Capsule::schema()->table('learning_results',function($table){
				$table->integer('attempts_at_refresh')->default(0)->after('score');
			});
		}

		// Add index for "apprenticeship_issue_categories_users" fields "issue_category_id,user_id".
		$issue_category_id__user_id = Capsule::select('SHOW KEYS FROM apprenticeship_issue_categories_users WHERE key_name = ?', ['issue_category_id__user_id']);
		if (count($issue_category_id__user_id) == 0) {
			Capsule::connection()->unprepared("ALTER TABLE `apprenticeship_issue_categories_users` ADD INDEX `issue_category_id__user_id` (`issue_category_id`, `user_id`);");
		}

		if (!Capsule::schema()->hasColumn('configuration', 'is_payment_configuration')) {
			Capsule::schema()->table('configuration', function ($table) {
				$table->boolean('is_payment_configuration')->default(false)->after('status');
			});
		}

		Capsule::statement("
				CREATE OR REPLACE VIEW apprenticeship_issues_learning_modules_combined AS
					SELECT
						apprenticeship_issues_learning_modules.apprenticeship_issues_id,
						apprenticeship_issues_learning_modules.learning_modules_id,
						NULL AS user_id
					FROM apprenticeship_issues_learning_modules

				UNION ALL

					SELECT
						apprenticeship_issues_user_learning_modules.apprenticeship_issues_id,
						apprenticeship_issues_user_learning_modules.learning_modules_id,
						apprenticeship_issues_user_learning_modules.user_id
					FROM apprenticeship_issues_user_learning_modules

				UNION ALL

					SELECT
						apprenticeship_issues_evidence.apprenticeship_issues_id,
						apprenticeship_issues_evidence.learning_modules_id,
						apprenticeship_issues_evidence.user_id
					FROM apprenticeship_issues_evidence
			")
		;

		Capsule::statement("
			CREATE OR REPLACE VIEW programme_user_resource AS
				SELECT
					apprenticeship_standards.id AS standard_id,
					users.id AS user_id,
					apprenticeship_issue_categories.id AS outcome_id,
					apprenticeship_issues.id AS criteria_id,
					apprenticeship_issues_learning_modules_combined.learning_modules_id AS learning_module_id

				FROM apprenticeship_standards

				JOIN apprenticeship_standards_users
					ON apprenticeship_standards_users.standard_id = apprenticeship_standards.id

				JOIN users
					ON users.id = apprenticeship_standards_users.user_id

				left JOIN apprenticeship_issue_categories
					ON apprenticeship_issue_categories.standard_id = apprenticeship_standards.id

				left JOIN apprenticeship_issues
					ON apprenticeship_issues.issue_category_id = apprenticeship_issue_categories.id

				left JOIN apprenticeship_issues_learning_modules_combined
					ON apprenticeship_issues_learning_modules_combined.apprenticeship_issues_id = apprenticeship_issues.id
					and (
						apprenticeship_issues_learning_modules_combined.user_id IS NULL
						OR apprenticeship_issues_learning_modules_combined.user_id = users.id
					)

				WHERE
					apprenticeship_standards.`status` = 1
				and
					apprenticeship_standards_users.deleted_at IS null
				and
					users.`status` = 1
				and
					apprenticeship_issue_categories.`status` = 1
				and
					apprenticeship_issues.`status` = 1
			")
		;

		if (!Capsule::schema()->hasColumn('forms','restricted_form')) {
			Capsule::schema()->table('forms',function($table) {
				$table->boolean('restricted_form')->default(false)->after('is_common_form');
			});
		}

		if (!Capsule::schema()->hasColumn('roles','edit_learning_resources')) {
			Capsule::schema()->table('roles',function($table) {
				$table->boolean('edit_learning_resources')->default(true)->after('show_skill_monitoring');
			});
		}

		if (!Capsule::schema()->hasColumn('roles','hide_not_assigned_learner_information_in_events')) {
			Capsule::schema()->table('roles',function($table) {
				$table->boolean('hide_not_assigned_learner_information_in_events')->default(false)->after('edit_learning_resources');
			});
		}
		if(!Capsule::schema()->hasColumn('custom_reports','custom_view')){
			Capsule::schema()->table('custom_reports',function($table){
				$table->string('custom_view')->default(null)->after('user_specific_filter');
			});
		}
		if(!Capsule::schema()->hasColumn('custom_reports','form_id'))
		{
			Capsule::schema()->table('custom_reports',function($table){
				$table->integer('form_id')->after('custom_view')->nullable();
			});
		}
		if(!DB::select("SHOW INDEX FROM fields WHERE Column_name = 'slug'")){
			DB::statement('ALTER TABLE fields ADD INDEX slug_index (slug)');
		}
		if(!DB::select("SHOW INDEX FROM form_fields WHERE Column_name = 'field_id'")){
			DB::statement('ALTER TABLE form_fields ADD INDEX field_index (field_id)');
		}
		if(!DB::select("SHOW INDEX FROM form_fields WHERE Column_name = 'form_id'")){
			DB::statement('ALTER TABLE form_fields ADD INDEX slug_index (form_id)');
		}
		if(!DB::select("SHOW INDEX FROM user_forms WHERE Column_name = 'form_id'")){
			DB::statement('ALTER TABLE user_forms ADD INDEX form_id_index (form_id)');
		}
		if(!DB::select("SHOW INDEX FROM user_forms WHERE Column_name = 'user_id'")){
			DB::statement('ALTER TABLE user_forms ADD INDEX user_id_index (user_id)');
		}
		if(!DB::select("SHOW INDEX FROM user_forms  WHERE Column_name = 'type_id'")){
			DB::statement('ALTER TABLE user_forms ADD INDEX type_id_index (type_id)');
		}
		if(!DB::select("SHOW INDEX FROM user_form_values WHERE Column_name = 'user_form_id'")){
			DB::statement('ALTER TABLE user_form_values ADD INDEX user_form_id_index (user_form_id)');
		}
	  if(!DB::select("SHOW INDEX FROM user_form_values WHERE Column_name = 'form_field_id'")){
			DB::statement('ALTER TABLE user_form_values ADD INDEX form_field_id_index (form_field_id)');
		}
	   if(!DB::select("SHOW INDEX FROM user_form_values WHERE Column_name = 'slug'")){
			DB::statement('ALTER TABLE user_form_values ADD INDEX slug_index (slug)');
		}


		if (!Capsule::schema()->hasColumn('learning_results','refresh_reason')) {
			Capsule::schema()->table('learning_results',function($table) {
				$table->string('refresh_reason')->nullable()->default(null)->after('refreshed');
			});
		}

		Capsule::schema()->table('user_sub_departments', function (Blueprint $table) {
			if (!Capsule::schema()->hasColumn('user_sub_departments', 'created_by')) {
				$table->integer('created_by')->unsigned()->nullable()->default(null)->after('department_id');
			}
			if (!Capsule::schema()->hasColumn('user_sub_departments', 'deleted_by')) {
				$table->integer('deleted_by')->unsigned()->nullable()->default(null)->after('created_by');
			}
		});
		if(!Capsule::schema()->hasColumn('schedules','room_email')){
			Capsule::schema()->table('schedules',function($table){
				$table->string('room_email')->default(null)->after('status');
			});
		}
		if(!Capsule::schema()->hasColumn('venues','room_email')){
			Capsule::schema()->table('venues',function($table){
				$table->string('room_email')->default(null)->after('status');
			});
		}

		Capsule::schema()->table('users', function (Blueprint $table) {
			if (Capsule::schema()->hasColumn('users', 'MAYTAS')) {
				$table->dropColumn('MAYTAS');
			}
		});

		Capsule::schema()->table('learning_results', function (Blueprint $table) {
			if (!Capsule::schema()->hasColumn('learning_results', 'refreshed_by_process')) {
				$table->tinyInteger('refreshed_by_process')
					->nullable()
					->default(null)
					->after('refresh_reason')
					->comment('1 - Learning Resource, 2 - Lesson, 3 - Learning Programme');
			}
			if (!Capsule::schema()->hasColumn('learning_results', 'refresh_notify_mail_send_at')) {
				$table->dateTime('refresh_notify_mail_send_at')->nullable()->after('refreshed_by_process'); // refresh learning resource notification mail send date time
			}
		});


		Capsule::schema()->table('email_queue', function (Blueprint $table) {
			if (!Capsule::schema()->hasColumn('email_queue', 'approved')) {
				$table->boolean('approved')->default(true)->after('processed');
			}
			if (!Capsule::schema()->hasColumn('email_queue', 'recipients_emails')) {
				$table->text('recipients_emails')->default('')->after('recipients');
			}
		});

		Capsule::schema()->table('roles', function (Blueprint $table) {
			if (!Capsule::schema()->hasColumn('roles', 'allow_impersonate_learners')) {
				$table->boolean('allow_impersonate_learners')->default(false)->after('hide_not_assigned_learner_information_in_events');
			}
		});


		if (Capsule::schema()->hasColumn('configuration', 'value')) {
			Capsule::schema()->table('configuration', function ($table) {
				$table->text('value')->change();
				$table->text('default_value')->nullable()->default(null)->change();
				$table->text('previousValue')->nullable()->default(null)->change();
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_standards', 'print_certificate')) {
			Capsule::schema()->table('apprenticeship_standards', function ($table) {
				$table->boolean('print_certificate')->default(true)->after('default_skill_repetition_period');
			});
		}
		if (Capsule::schema()->hasColumn('custom_reports', 'type')) {
			Capsule::schema()->table('custom_reports', function ($table) {
				DB::statement("ALTER TABLE custom_reports MODIFY COLUMN `type` ENUM('users','learning_resources','apprenticeship_standards','schedules','user_forms','career_paths','coupons','purchases', 'all_learning', 'quiz_analysis', 'user_schedule_waiting_lists') NULL DEFAULT NULL");
			});
		}
		if (!Capsule::schema()->hasColumn('apprenticeship_standards', 'self_enroll')) {
			Capsule::schema()->table('apprenticeship_standards', function ($table) {
				$table->boolean('self_enroll')->default(false);
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_standards', 'cost')) {
			Capsule::schema()->table('apprenticeship_standards', function ($table) {
				$table->float('cost')->nullable()->after('badge');
			});
		}

		if (!Capsule::schema()->hasColumn('coupon_usages', 'cost')) {
			Capsule::schema()->table('coupon_usages', function ($table) {
				$table->float('cost')->nullable()->after('has_used');
			});
		}

		if (!Capsule::schema()->hasColumn('coupon_usages', 'cost_after_coupon_apply')) {
			Capsule::schema()->table('coupon_usages', function ($table) {
				$table->float('cost_after_coupon_apply')->nullable()->after('cost');
			});
		}

		Capsule::schema()->table('schedules', function (Blueprint $table) {
			if (Capsule::schema()->hasColumn('schedules', 'start_date')) {
				$table->datetime('start_date')->nullable()->default(NULL)->change();
			}
		});

		if (!Capsule::schema()->hasColumn('apprenticeship_designations', 'order')) {
			Capsule::schema()->table('apprenticeship_designations', function ($table) {
				$table->integer('order')->nullable()->after('designation_id');
			});
		}

		if (!Capsule::schema()->hasColumn('apprenticeship_designations', 'status')) {
			Capsule::schema()->table('apprenticeship_designations', function ($table) {
				$table->boolean('status')->nullable()->after('order');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_results','started_version')) {
			Capsule::schema()->table('learning_results',function($table){
				$table->integer('started_version')->default(1)->after('completed_version');
			});

			\Models\LearningModule
				::where('version', '>', 1)
				->chunk(100, function($learning_modules) {
					foreach($learning_modules as $learning_module) {
						\Models\LearningResult
							::where('refreshed', 0)
							->where('learning_module_id', $learning_module->id)
							->chunkById(100, function($learning_results) use ($learning_module) {
								foreach($learning_results as $learning_result) {
									$learning_result->started_version = $learning_module->version;
									if ($learning_result->started_version > $learning_result->completed_version) {
										$learning_result->started_version = $learning_result->completed_version;
									}
									$learning_result->timestamps = false;
									$learning_result->saveWithoutEvents();
								}
							})
						;
					}
				})
			;
		}

		if (!Capsule::schema()->hasColumn('learning_modules', 'reset_learning')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->tinyInteger('reset_learning')->default(0)->after('disable_upon_completion');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_results', 'quiz_time')) {
			Capsule::schema()->table('learning_results', function ($table) {
				$table->time('quiz_time')->nullable()->default(null)->after('score');
			});
		}

		Capsule::schema()->table('companies', function (Blueprint $table) {
			if (!Capsule::schema()->hasColumn('companies', 'email_from')) {
				$table->string("email_from")->nullable()->after('reference_number');
			}
			if (!Capsule::schema()->hasColumn('companies', 'email_from_name')) {
				$table->string("email_from_name")->nullable()->after('email_from');
			}
			if (!Capsule::schema()->hasColumn('companies', 'email_footer')) {
				$table->mediumText("email_footer")->nullable()->after('email_from_name');
			}
		});
    $is_deadline_passed_alert_waiting_users = false;
		Capsule::schema()->table('schedules', function (Blueprint $table) use($is_deadline_passed_alert_waiting_users){
			if (!Capsule::schema()->hasColumn('schedules','deadline_at')) {
				$table->dateTime('deadline_at')->nullable()->after('all_day_event');
			}
			if (!Capsule::schema()->hasColumn('schedules','is_deadline_alert_mailed')) {
				$table->boolean('is_deadline_alert_mailed')->default(false)->after('deadline_at');
			}
			if (!Capsule::schema()->hasColumn('schedules', 'drop_off_deadline_at')) {
				$table->dateTime('drop_off_deadline_at')->nullable()->after('is_deadline_alert_mailed');
			}
			// alert waiting users that event deadline at passed
			if (!Capsule::schema()->hasColumn('schedules', 'is_deadline_passed_alert_waiting_users')) {
        $table->boolean('is_deadline_passed_alert_waiting_users')->default(false)->after('drop_off_deadline_at');
        $is_deadline_passed_alert_waiting_users = true;
			}
			// GTAA migration field
			if (!Capsule::schema()->hasColumn('schedules', 'session_code')) {
				$table->string('session_code')->nullable()->default(null)->after('do_not_send_advance_notifications');
			}
			// GTAA migration field
			if (!Capsule::schema()->hasColumn('schedules', 'legacy_course_id')) {
				$table->integer('legacy_course_id')->unsigned()->nullable()->after('session_code');
			}
		});
    if($is_deadline_passed_alert_waiting_users){
      $now =  Carbon::now();
      $thirtyDaysAgo = $now->subDays(30);
      Schedule::where(function ($query) use ($thirtyDaysAgo) {
        $query->whereNull('end_date')
        ->orWhere('end_date', '<=', $thirtyDaysAgo);
      })
      ->update(['is_deadline_passed_alert_waiting_users' => true]);
    }

		if (!Capsule::schema()->hasColumn('apprenticeship_standards_users', 'refresh_notify_mail_send_at')) {
			Capsule::schema()->table('apprenticeship_standards_users', function ($table) {
				$table->dateTime('refresh_notify_mail_send_at')->nullable()->after('time_spent_last_log'); // refresh learning resource notification mail send date time
			});
		}

		if (!Capsule::schema()->hasColumn('departments', 'code')) {
			Capsule::schema()->table('departments', function ($table) {
				$table->string('code')->nullable()->after('status');
			});
		}

		Capsule::schema()->table('learning_results', function (Blueprint $table) {
			if (!Capsule::schema()->hasColumn('learning_results', 'iteration')) {
				$table->smallInteger('iteration')->unsigned()->nullable()->default(1)->after('credits');
			}
			if (!Capsule::schema()->hasColumn('learning_results', 'delete_reason')) {
				$table->string('delete_reason')->nullable()->default(null)->after('deleted_by');
			}
			if (!Capsule::schema()->hasColumn('learning_results', 'failed_at')) {
				$table->datetime('failed_at')->nullable()->default(null)->after('completed_at');
			}
		});

		if (!Capsule::schema()->hasColumn('configuration', 'table_name')) {
			Capsule::schema()->table('configuration', function (Blueprint $table) {
				$table->string('table_name')->nullable()->after('select_values'); // stores the table name of the configuration value
			});
		}
		Capsule::schema()->table('roles', function (Blueprint $table) {
			if (!Capsule::schema()->hasColumn('roles', 'show_send_ilr_data_to_the_hub_or_fis')) {
				$table->boolean('show_send_ilr_data_to_the_hub_or_fis')->default(true)->after('hide_not_assigned_learner_information_in_events');
			}
			if (Capsule::schema()->hasColumn('roles', 'show_events_sub_tab')) {
				$table->dropColumn('show_events_sub_tab');
			}
			if (!Capsule::schema()->hasColumn('roles', 'show_programme_sub_tab')) {
				$table->boolean('show_programme_sub_tab')->default(true)->after('allow_impersonate_learners');
			}
			if (!Capsule::schema()->hasColumn('roles', 'show_resource_sub_tab')) {
				$table->boolean('show_resource_sub_tab')->default(true)->after('show_programme_sub_tab');
			}
			if (!Capsule::schema()->hasColumn('roles', 'show_user_sub_tab')) {
				$table->boolean('show_user_sub_tab')->default(true)->after('show_resource_sub_tab');
			}
			if (!Capsule::schema()->hasColumn('roles', 'allow_access_to_user_profile')) {
				$table->boolean('allow_access_to_user_profile')->default(true)->after('show_user_sub_tab');
			}
			if (!Capsule::schema()->hasColumn('roles', 'hide_events_tab')) {
				$table->boolean('hide_events_tab')->default(false)->after('hide_set_training_tab');
			}
			if (!Capsule::schema()->hasColumn('roles', 'allow_refresh_event_resources')) {
				$table->boolean('allow_refresh_event_resources')->default(false)->after('allow_access_to_user_profile');
			}
			if (!Capsule::schema()->hasColumn('roles', 'allow_refresh_forms')) {
				$table->boolean('allow_refresh_forms')->default(false)->after('allow_refresh_event_resources');
			}
			if (!Capsule::schema()->hasColumn('roles', 'show_open_elms_ai')) {
				$table->boolean('show_open_elms_ai')->default(false)->after('allow_refresh_forms');
			}
			if (!Capsule::schema()->hasColumn('roles', 'docs_bot__id')) {
				$table->string('docs_bot__id')->after('description');
			}
			if (!Capsule::schema()->hasColumn('roles', 'allow_refresh_programmes')) {
				$table->boolean('allow_refresh_programmes')->default(false)->after('allow_refresh_forms');
			}
			if (!Capsule::schema()->hasColumn('roles', 'show_user_role_in_qa_list')) {
				$table->boolean('show_user_role_in_qa_list')->default(true)->after('allow_refresh_programmes');
			}
			if (!Capsule::schema()->hasColumn('roles', 'allow_send_custom_event_email')) {
				$table->boolean('allow_send_custom_event_email')->default(true)->after('show_user_role_in_qa_list');
			}
			if (!Capsule::schema()->hasColumn('roles', 'lfp_show_edit_button')) {
				$table->boolean('lfp_show_edit_button')->default(true)->after('allow_send_custom_event_email');
			}
			if (!Capsule::schema()->hasColumn('roles', 'lfp_show_managers_button')) {
				$table->boolean('lfp_show_managers_button')->default(true)->after('lfp_show_edit_button');
			}
			if (!Capsule::schema()->hasColumn('roles', 'lfp_show_packandgo_button')) {
				$table->boolean('lfp_show_packandgo_button')->default(true)->after('lfp_show_managers_button');
			}
			if (!Capsule::schema()->hasColumn('roles', 'lfp_show_add_learning')) {
				$table->boolean('lfp_show_add_learning')->default(true)->after('lfp_show_packandgo_button');
			}
			if (!Capsule::schema()->hasColumn('roles', 'lfp_show_set_work')) {
				$table->boolean('lfp_show_set_work')->default(true)->after('lfp_show_add_learning');
			}
			if (!Capsule::schema()->hasColumn('roles', 'lfp_show_add_evidence')) {
				$table->boolean('lfp_show_add_evidence')->default(true)->after('lfp_show_set_work');
			}
			if (!Capsule::schema()->hasColumn('roles', 'lfp_show_reviews')) {
				$table->boolean('lfp_show_reviews')->default(true)->after('lfp_show_add_evidence');
			}
			if (!Capsule::schema()->hasColumn('roles', 'lfp_show_add_forms')) {
				$table->boolean('lfp_show_add_forms')->default(true)->after('lfp_show_reviews');
			}
			if (!Capsule::schema()->hasColumn('roles', 'lfp_show_learning_programmes')) {
				$table->boolean('lfp_show_learning_programmes')->default(true)->after('lfp_show_add_forms');
			}
			if (!Capsule::schema()->hasColumn('roles', 'lfp_show_learning_resources')) {
				$table->boolean('lfp_show_learning_resources')->default(true)->after('lfp_show_learning_programmes');
			}
			if (!Capsule::schema()->hasColumn('roles', 'lfp_show_learning_resources_archives')) {
				$table->boolean('lfp_show_learning_resources_archives')->default(true)->after('lfp_show_learning_resources');
			}
			if (!Capsule::schema()->hasColumn('roles', 'lfp_show_events')) {
				$table->boolean('lfp_show_events')->default(true)->after('lfp_show_learning_resources_archives');
			}
			if (!Capsule::schema()->hasColumn('roles', 'lfp_show_programme_status')) {
				$table->boolean('lfp_show_programme_status')->default(true)->after('lfp_show_events');
			}
			if (!Capsule::schema()->hasColumn('roles', 'lfp_show_reports')) {
				$table->boolean('lfp_show_reports')->default(true)->after('lfp_show_programme_status');
			}
			if (!Capsule::schema()->hasColumn('roles', 'lfp_show_leaderboard')) {
				$table->boolean('lfp_show_leaderboard')->default(true)->after('lfp_show_reports');
			}
			if (!Capsule::schema()->hasColumn('roles', 'lfp_show_badges')) {
				$table->boolean('lfp_show_badges')->default(true)->after('lfp_show_leaderboard');
			}
			if (!Capsule::schema()->hasColumn('roles', 'lfp_show_required_competencies')) {
				$table->boolean('lfp_show_required_competencies')->default(true)->after('lfp_show_badges');
			}
			if (!Capsule::schema()->hasColumn('roles', 'lfp_show_skill_scans')) {
				$table->boolean('lfp_show_skill_scans')->default(true)->after('lfp_show_required_competencies');
			}
			if (!Capsule::schema()->hasColumn('roles', 'lfp_show_qa_reports')) {
				$table->boolean('lfp_show_qa_reports')->default(true)->after('lfp_show_skill_scans');
			}
			if (!Capsule::schema()->hasColumn('roles', 'lfp_show_comment_log')) {
				$table->boolean('lfp_show_comment_log')->default(true)->after('lfp_show_qa_reports');
			}
			if (!Capsule::schema()->hasColumn('roles', 'lfp_show_assigned_forms')) {
				$table->boolean('lfp_show_assigned_forms')->default(true)->after('lfp_show_comment_log');
			}
			if (!Capsule::schema()->hasColumn('roles', 'lfp_show_assigned_workflow')) {
				$table->boolean('lfp_show_assigned_workflow')->default(true)->after('lfp_show_assigned_forms');
			}
			if (!Capsule::schema()->hasColumn('roles', 'lfp_show_email_history')) {
				$table->boolean('lfp_show_email_history')->default(true)->after('lfp_show_assigned_workflow');
			}
		});

		if (!Capsule::schema()->hasColumn('schedule_links','refresh_reason')) {
			Capsule::schema()->table('schedule_links',function($table) {
				$table->string('refresh_reason')->nullable()->default(null)->after('refreshed');
			});
		}

		if (!Capsule::schema()->hasColumn('schedule_links','refresh_notify_mail_send_at')) {
			Capsule::schema()->table('schedule_links',function($table) {
				$table->dateTime('refresh_notify_mail_send_at')->nullable()->after('refresh_reason'); // refresh event notification mail send date time
			});
		}
		if (!Capsule::schema()->hasColumn('countries', 'code')) {
			Capsule::schema()->table('countries', function ($table) {
				$table->string('code')->nullable()->after('name');
			});
		}
		if(!Capsule::schema()->hasColumn('users','ukg_employee_number')){
			Capsule::schema()->table('users',function ($table){
				$table->string('ukg_employee_number')->nullable()->after('status');
			});
		}

		Capsule::schema()->table('learning_modules', function (Blueprint $table) {
			// GTAA migration field
			if (!Capsule::schema()->hasColumn('learning_modules', 'legacy_course_id')) {
				$table->integer('legacy_course_id')->unsigned()->nullable()->after('disable_upon_completion');
			}

			if (!Capsule::schema()->hasColumn('learning_modules', 'delivery_provider_type_id')) {
				$table->integer('delivery_provider_type_id')->unsigned()->nullable()->default(null)->after('legacy_course_id');
			}
			if (!Capsule::schema()->hasColumn('learning_modules', 'group_department_code_id')) {
				$table->integer('group_department_code_id')->unsigned()->nullable()->default(null)->after('delivery_provider_type_id');
			}
			if (!Capsule::schema()->hasColumn('learning_modules', 'after_completion_do_not_reset_completion_state')) {
				$table->boolean('after_completion_do_not_reset_completion_state')->default(false)->after('group_department_code_id');
			}
			if (!Capsule::schema()->hasColumn('learning_modules', 'reset_failed_quiz')) {
				$table->boolean('reset_failed_quiz')->default(false)->after('after_completion_do_not_reset_completion_state');
			}
		});

		Capsule::schema()->table('companies', function (Blueprint $table) {
			if (!Capsule::schema()->hasColumn('companies', 'email_from')) {
				$table->string("email_from")->nullable()->after('reference_number');
			}
			if (!Capsule::schema()->hasColumn('companies', 'email_from_name')) {
				$table->string("email_from_name")->nullable()->after('email_from');
			}
			if (!Capsule::schema()->hasColumn('companies', 'email_footer')) {
				$table->mediumText("email_footer")->nullable()->after('email_from_name');
			}
			if(!Capsule::schema()->hasColumn('companies','access_token')){
				$table->text('access_token')->nullable()->after('email_footer');
				$table->text('refresh_token')->nullable()->after('email_footer');
			}
		});
		if (!capsule::schema()->hascolumn('roles', 'hide_assigning_manager')) {
			capsule::schema()->table('roles', function ($table) {
				$table->boolean('hide_assigning_manager')->default(false)->after('access_all_learners');
			});
		}
		if (!capsule::schema()->hascolumn('roles', 'hide_learning_setup')) {
			capsule::schema()->table('roles', function ($table) {
				$table->boolean('hide_learning_setup')->default(false)->after('access_all_learners');
			});
		}

		if(!Capsule::schema()->hasColumn('custom_report_fields','is_custom_field'))
		{
			Capsule::schema()->table('custom_report_fields',function($table){
				$table->boolean('is_custom_field')->default(false)->nullable();
			});
		}

		Capsule::schema()->table('user_learning_modules', function (Blueprint $table) {
			if (!Capsule::schema()->hasColumn('user_learning_modules', 'url_link')) {
				$table->string('url_link')->default('')->after('comment_link');
			}
			if (!Capsule::schema()->hasColumn('user_learning_modules', 'url_unlink')) {
				$table->string('url_unlink')->default('')->after('comment_unlink');
			}
		});

		Capsule::schema()->table('user_learning_module_archives', function (Blueprint $table) {
			if (!Capsule::schema()->hasColumn('user_learning_module_archives', 'url_link')) {
				$table->string('url_link')->default('')->after('comment_link');
			}
			if (!Capsule::schema()->hasColumn('user_learning_module_archives', 'url_unlink')) {
				$table->string('url_unlink')->default('')->after('comment_unlink');
			}
		});

		if (Capsule::schema()->hasColumn('powerbi_dashboards', 'include_data_for')) {
			Capsule::schema()->table('powerbi_dashboards', function ($table) {

				DB::statement("ALTER TABLE powerbi_dashboards CHANGE COLUMN include_data_for include_data_for ENUM('user', 'manager', 'company', 'all') NULL DEFAULT NULL");
			});
		}

        if (Capsule::schema()->hasColumn('powerbi_reports', 'type')) {
            Capsule::schema()->table('powerbi_reports', function ($table) {
                DB::statement("ALTER TABLE powerbi_reports CHANGE COLUMN type type ENUM('user', 'manager', 'company', 'all') NULL DEFAULT NULL");
            });
        }

		if (Capsule::schema()->hasColumn('powerbi_reports', 'powerbi_dataset_id')) {
			Capsule::schema()->table('powerbi_reports', function ($table) {
				$table->dropColumn('powerbi_dataset_id');
			});
		}

		if (!Capsule::schema()->hasColumn('powerbi_reports', 'dataset_id')) {
			Capsule::schema()->table('powerbi_reports', function ($table) {
				$table->integer('dataset_id')->unsigned()->nullable();
			});
		}

		if (!Capsule::schema()->hasColumn('powerbi_reports', 'initial_push')) {
			Capsule::schema()->table('powerbi_reports', function ($table) {
				$table->boolean('initial_push')->default(0)->before('queue');
			});
		}

		Capsule::schema()->table('users', function (Blueprint $table) {
			if (!Capsule::schema()->hasColumn('users', 'watch_id')) {
				$table->integer('watch_id')->unsigned()->nullable()->default(null)->after('watch');
			}
			if (!Capsule::schema()->hasColumn('users', 'openelms_ai_link')) {
				$table->string('openelms_ai_link')->nullable()->default(null)->comment("OpenElms AI user id encoded in sha256, by token")->after("watch_id");
			}
		});

		// Drop all credas tables
		Capsule::schema()->dropIfExists('role_credas_actors');
		Capsule::schema()->dropIfExists('credas_process_actors');
		Capsule::schema()->dropIfExists('credas_courses');
		Capsule::schema()->dropIfExists('credas_reports');
		Capsule::schema()->dropIfExists('credas_autofill_mappings');
		Capsule::schema()->dropIfExists('credas_processes');
		Capsule::schema()->dropIfExists('credas_actors');
		if (!Capsule::schema()->hasColumn('powerbi_datasets', 'initial_push')) {
			Capsule::schema()->table('powerbi_datasets', function ($table) {
				$table->boolean('initial_push')->default(0)->before('queue');
			});
		}

		if(DB::select("SHOW INDEX FROM structure WHERE Key_name = 'structure_key_unique'")){
			DB::statement('ALTER TABLE structure DROP INDEX structure_key_unique;');
		}

		if(!Capsule::schema()->hasColumn('assignments','cron')){
			Capsule::schema()->table('assignments',function($table){
				$table->boolean('cron')->default(false);
			});
		}

		Capsule::schema()->table('form_logs', function (Blueprint $table) {
			if (!Capsule::schema()->hasColumn('form_logs', 'form_field_file')) {
				$table->string('form_field_file')->nullable()->default(null)->after('form_field');
			}
		});

		if(Capsule::schema()->hasColumn('structure','description')) {
			Capsule::schema()->table('structure',function(){
				DB::statement('ALTER TABLE `structure` CHANGE `description` `description` text NULL;');
			});

		}

		Capsule::schema()->table('email_queue', function (Blueprint $table) {
			if (!Capsule::schema()->hasColumn('email_queue', 'custom_tpl')) {
				$table->text('custom_tpl')->nullable()->default(null)->after('custom_variables');
			}
		});

		if (!Capsule::schema()->hasColumn('user_forms', 'signoff_reminder_send_at')) {
			Capsule::schema()->table('user_forms', function ($table) {
				$table->datetime('signoff_reminder_send_at')->nullable()->after('snooze_target_date');
			});
		}

		Capsule::schema()->table('email_queue',function(Blueprint $table) {
			if(!Capsule::schema()->hasColumn('email_queue','deleted_at')){
				$table->softDeletes();
			}
		});

		Capsule::schema()->table('learning_modules',function(Blueprint $table) {
			if(!Capsule::schema()->hasColumn('learning_modules', 'refresh_only_if_learning_meets_query')){
				$table->boolean('refresh_only_if_learning_meets_query')->default(false)->after('refresh_repeat');
			}
			if(!Capsule::schema()->hasColumn('learning_modules', 'further_customise_this_query')){
				$table->boolean('further_customise_this_query')->default(false)->after('refresh_only_if_learning_meets_query');
			}
			if (!Capsule::schema()->hasColumn('learning_modules', 'refresh_date')){
				$table->dateTime('refresh_date')->nullable()->default(null)->after('refresh_period');
			}

			if (!Capsule::schema()->hasColumn('learning_modules', 'remove_paid_status')) {
				$table->boolean('remove_paid_status')->default(false)->after('refresh_date');
			}

			if (!Capsule::schema()->hasColumn('learning_modules', 'refresh_period_type')){
				$table->enum('refresh_period_type', ['day', 'month', 'year'])->default('day')->after('refresh_period');
			}

			if (!Capsule::schema()->hasColumn('learning_modules', 'scorm_full_screen')){
				$table->boolean('scorm_full_screen')->default(false)->after('scorm_popup');
			}
		});

		Capsule::schema()->table('email_templates',function(Blueprint $table) {
			if(!Capsule::schema()->hasColumn('email_templates', 'ignore_queue_approval')){
				$table->boolean('ignore_queue_approval')->default(false)->after('site_versions');
			}
		});
		//Create new coloumn for companies table if not exists column name is email_username its string type and nullable
		Capsule::schema()->table('companies', function (Blueprint $table) {
			if (!Capsule::schema()->hasColumn('companies', 'email_username')) {
				$table->string('email_username')->nullable()->after('email_footer');
			}
		});
		Capsule::schema()->table('schedules',function(Blueprint $table){
			if(!Capsule::schema()->hasColumn('schedules','previous_start_date')){
				$table->dateTime('previous_start_date')->nullable()->after('start_date')->default(null);
			}
		});
		Capsule::schema()->table('schedules',function(Blueprint $table){
			if(!Capsule::schema()->hasColumn('schedules','update_outlook')){
				//update outlook based on this enume if it set to all it will update all information if set to user only update attendee information if set to none it will not update anythig
				$table->enum('update_outlook',['all','user','none'])->default('none')->after('outlook_start_date');
			}
		});
		if (!Capsule::schema()->hasColumn('powerbi_dashboards', 'powerbi_course_set_id')) {
			Capsule::schema()->table('powerbi_dashboards', function ($table) {
				$table->integer('powerbi_course_set_id')->unsigned()->nullable()->before('status');
			});
		}
		if (Capsule::schema()->hasColumn('powerbi_datasets', 'type')) {
			Capsule::schema()->table('powerbi_datasets', function ($table) {
				DB::statement("ALTER TABLE powerbi_datasets CHANGE COLUMN type type ENUM('user', 'manager', 'company', 'all') NULL DEFAULT NULL");
			});
		}
		// if (!Capsule::schema()->hasColumn('powerbi_reports', 'powerbi_course_set_id')) {
		//     Capsule::schema()->table('powerbi_reports', function ($table) {
		//         $table->integer('powerbi_course_set_id')->unsigned()->nullable()->before('status');
		//     });
		// }
		if (!Capsule::schema()->hasColumn('powerbi_datasets', 'powerbi_course_set_id')) {
			Capsule::schema()->table('powerbi_datasets', function ($table) {
				$table->integer('powerbi_course_set_id')->unsigned()->nullable()->before('status');
			});
		}

		if (!Capsule::schema()->hasColumn('powerbi_dashboards', 'display_order')) {
			Capsule::schema()->table('powerbi_dashboards', function ($table) {
				$table->integer('display_order')->unsigned()->nullable()->after('status');
			});
		}

		if (Capsule::schema()->hasColumn('powerbi_dashboards', 'role_id')) {
			Capsule::schema()->table('powerbi_dashboards', function ($table) {
				$table->dropColumn('role_id');
			});
		}

		if (Capsule::schema()->hasColumn('powerbi_datasets', 'powerbi_dashboard_id')) {
			Capsule::schema()->table('powerbi_datasets', function ($table) {
				$table->dropColumn('powerbi_dashboard_id');
			});
		}

		Capsule::schema()->dropIfExists('powerbi_dashboard_learning_modules');
        // Create field for maximo start
        if(!Capsule::schema()->hasColumn('learning_modules','cron_task')){
        Capsule::schema()->table('learning_modules', function ($table) {
            $table->boolean('cron_task')->default(false)->after('status');
            $table->boolean('is_maximo_qualification')->default(false)->after('status');
        });
        }
        //Create cron_task for learning_results
        if(!Capsule::schema()->hasColumn('learning_results','cron_task')){
            Capsule::schema()->table('learning_results', function ($table) {
                $table->boolean('cron_task')->default(false)->after('passing_status');
            });
        }
        //for checking maximo need to update
        if(!Capsule::schema()->hasColumn('learning_modules','maximo_created')){
            Capsule::schema()->table('learning_modules', function ($table) {
                $table->boolean('maximo_created')->default(false)->after('is_maximo_qualification');
            });
        }
        //Need same for learning_results
        if(!Capsule::schema()->hasColumn('learning_results','maximo_created')){
            Capsule::schema()->table('learning_results', function ($table) {
            $table->boolean('maximo_created')->default(false)->after('cron_task');
            });
        }
        //Log maximo call api attempts to learning_modules and learning_results
        if(!Capsule::schema()->hasColumn('learning_modules','maximo_api_attempts')){
            Capsule::schema()->table('learning_modules', function ($table) {
            $table->integer('maximo_api_attempts')->default(0)->after('maximo_created');
            });
        }
        if(!Capsule::schema()->hasColumn('learning_results','maximo_api_attempts')){
            Capsule::schema()->table('learning_results', function ($table) {
            $table->integer('maximo_api_attempts')->default(0)->after('maximo_created');
            });
        }
        // Add maximo retry time for learning_modules and learning_results
        if(!Capsule::schema()->hasColumn('learning_modules','maximo_retry_time')){
            Capsule::schema()->table('learning_modules', function ($table) {
            $table->dateTime('maximo_retry_time')->nullable()->after('maximo_api_attempts');
            });
        }
        // Add maximo retry time for learning_results
        if(!Capsule::schema()->hasColumn('learning_results','maximo_retry_time')){
            Capsule::schema()->table('learning_results', function ($table) {
            $table->dateTime('maximo_retry_time')->nullable()->after('maximo_api_attempts');
            });
        }

        // Maximo Fields ending here

		if (!Capsule::schema()->hasColumn('learning_modules', 'complete_if_linked_events_completed')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->boolean('complete_if_linked_events_completed')->default(false)->after('open_in_events_only');
			});
		}

		if (!Capsule::schema()->hasColumn('role_structure', 'deleted_at')) {
			Capsule::schema()->table('role_structure', function ($table) {
				$table->softDeletes();
			});
        }
        Capsule::schema()->table('imports', function ($table) {
            DB::statement("ALTER TABLE imports CHANGE COLUMN type type ENUM('programme','wizard_import') NULL DEFAULT NULL");
        });
        if (!Capsule::schema()->hasColumn('roles', 'allow_managers_to_add_users')) {
            Capsule::schema()->table('roles', function ($table) {
                $table->boolean('allow_managers_to_add_users')->default(false)->after('allow_access_to_user_profile');
            });
        }
		if (!Capsule::schema()->hasColumn('user_custom_programme_statuses', 'deleted_at')) {
			Capsule::schema()->table('user_custom_programme_statuses', function (Blueprint $table) {
				$table->integer('deleted_by')->unsigned()->nullable();
				$table->softdeletes();
			});
		}

		Capsule::schema()->table('companies',function(Blueprint $table) {
			if (!Capsule::schema()->hasColumn('companies', 'slug')) {
				$table->string('slug')->default("")->after('name');
			}
		});

        if (!Capsule::schema()->hasColumn('stripe_subscriptions', 'is_cancellable')) {
            Capsule::schema()->table('stripe_subscriptions', function (Blueprint $table) {
                $table->boolean('is_cancellable')->default(false);
            });
        }
        if (!Capsule::schema()->hasColumn('credit_details', 'is_cancellable')) {
            Capsule::schema()->table('credit_details', function (Blueprint $table) {
                $table->boolean('is_cancellable')->default(false);
            });
        }
        Capsule::schema()->table('companies',function(Blueprint $table){
            if(!Capsule::schema()->hasColumn('companies','make_all_enrollable')){
                $table->boolean('make_all_enrollable')->default(false)->after('status');
			}
            if(!Capsule::schema()->hasColumn('companies','urlextension')){
                $table->string('urlextension')->nullable()->after('message');
            }

        });

        if (!Capsule::schema()->hasColumn('stripe_subscriptions', 'price')) {
            Capsule::schema()->table('stripe_subscriptions', function (Blueprint $table) {
                $table->string('price')->nullable();
            });
        }
        if (!Capsule::schema()->hasColumn('license_plans', 'product_price_id')) {
            Capsule::schema()->table('license_plans', function (Blueprint $table) {
                $table->string('product_price_id')->nullable();
            });
        }
        if (!Capsule::schema()->hasColumn('license_plans', 'tiers')) {
            Capsule::schema()->table('license_plans', function (Blueprint $table) {
                $table->longText('tiers')->default('[]');
            });
        }
        if (!Capsule::schema()->hasColumn('license_plans', 'marketing_features')) {
            Capsule::schema()->table('license_plans', function (Blueprint $table) {
                $table->longText('marketing_features')->default('[]');
            });
        }
        if (!Capsule::schema()->hasColumn('license_plans', 'description')) {
            Capsule::schema()->table('license_plans', function (Blueprint $table) {
                $table->text('description')->nullable()->after('name');
            });
        }
        if (!Capsule::schema()->hasColumn('license_plans', 'min')) {
            Capsule::schema()->table('license_plans', function (Blueprint $table) {
                $table->integer('min')->default(100);
            });
        }
        if (!Capsule::schema()->hasColumn('license_plans', 'max')) {
            Capsule::schema()->table('license_plans', function (Blueprint $table) {
                $table->integer('max')->default(5000);
            });
        }

        if (!Capsule::schema()->hasColumn('users', 'reset_password')) {
            Capsule::schema()->table('users', function ($table) {
                $table->boolean('reset_password')->default(false)->after('password_force_reset');
            });
        }
        if (!Capsule::schema()->hasColumn('configuration', 'category_id')) {
            Capsule::schema()->table('configuration', function ($table) {
                $table->integer('category_id')->nullable()->after('name');
            });
        }

		Capsule::schema()->table('user_payment_transactions',function(Blueprint $table) {
			if (!Capsule::schema()->hasColumn('user_payment_transactions', 'scpReference')){
				$table->string('scpReference')->default("")->after('system_generated_transaction_id');
			}
			if (!Capsule::schema()->hasColumn('user_payment_transactions', 'vat_code')){
				$table->string('vat_code')->default("")->after('fund_code');
			}
		});

		Capsule::schema()->table('ilr_learning_deliveries',function(Blueprint $table) {
			if(!Capsule::schema()->hasColumn('ilr_learning_deliveries', 'TLOut')){
				$table->unsignedTinyInteger('TLOut')->nullable()->after('SWSupAimId');
			}
        });
        if(!Capsule::schema()->hasColumn('configuration','visible_manager')){
            Capsule::schema()->table('configuration',function(Blueprint $table){
                $table->boolean('visible_manager')->default(false)->after('category_id');
                $table->boolean('editable_manager')->default(false)->after('visible_manager');
            });
        }
		if(!Capsule::schema()->hasColumn('configuration','hidden')){
            Capsule::schema()->table('configuration',function(Blueprint $table){
                $table->boolean('hidden')->default(false)->after('visible_manager');
            });
        }

		// Add 3 fields to apprenticeship_standards table https://openelms.atlassian.net/browse/SCOR-4578
		$update_existing_standards = false;
		Capsule::schema()->table('apprenticeship_standards',function(Blueprint $table) use (&$update_existing_standards) {
			if (!Capsule::schema()->hasColumn('apprenticeship_standards', 'completion_criteria')){
				$table->boolean('completion_criteria')->default(false)->after('course_credits');
				$update_existing_standards = true;
			}
			if (!Capsule::schema()->hasColumn('apprenticeship_standards', 'completion_resources')){
				$table->boolean('completion_resources')->default(false)->after('completion_criteria');
				$update_existing_standards = true;
			}
			if (!Capsule::schema()->hasColumn('apprenticeship_standards', 'completion_time')){
				$table->boolean('completion_time')->default(false)->after('completion_resources');
				$update_existing_standards = true;
			}
		});
		if ($update_existing_standards) {
			$standards  = \Models\ApprenticeshipStandard
				::where('status', true)
				->get()
			;
			foreach($standards as $standard) {
				if ($standard->type === 'Standards') {
					$standard->completion_resources = true;
					$standard->completion_time = true;
				}
				if ($standard->type === 'Qualifications') {
					$standard->completion_criteria = true;
				}
				$standard->save();
			}
		}
		// EOF SCOR-4578

		if (!Capsule::schema()->hasColumn('learning_modules', 'self_enroll_access')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->tinyInteger('self_enroll_access')->nullable()->default(null)->after('self_enroll')->comment('0- Allow to all companies, 1- Allow access to only selected companies, 2- Prevent access to only selected companies');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_modules', 'editing_access')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->tinyInteger('editing_access')->nullable()->default(null)->after('self_enroll_access')->comment('0- Allow to all companies, 1- Allow access to only selected companies, 2- Prevent access to only selected companies');
			});
		}

		if (Capsule::schema()->hasColumn('company_module_enrollment', 'type')) {
			Capsule::schema()->table('company_module_enrollment', function ($table) {
				$table->integer('type')->change()->comment('1- self_enroll_acesss, 2- management/curriculam developer editing access');
			});
		}

        if (!Capsule::schema()->hasColumn('forms', 'has_sign_off_order')) {
            Capsule::schema()->table('forms', function ($table) {
                $table->boolean('has_sign_off_order')->default(false);
            });
        }
        if (!Capsule::schema()->hasColumn('form_signoff_roles', 'order')) {
            Capsule::schema()->table('form_signoff_roles', function ($table) {
                $table->integer('order')->nullable();
            });
        }
        if(!Capsule::schema()->hasColumn('forms','is_archived')){
            Capsule::schema()->table('forms',function(Blueprint $table){
                $table->boolean('is_archived')->default(false)->after('status');
            });
        }
		if(!Capsule::schema()->hasColumn('learning_modules','payment_buys')){
		   Capsule::schema()->table('learning_modules',function(Blueprint $table){
			   $table->string('payment_buys')->after('cost')->default('annual_access');
		   });
		}

		if (!Capsule::schema()->hasColumn('learning_modules', 'access_duration')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->integer('access_duration')->default(null)->nullable();
			});
		} else {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->integer('access_duration')->default(null)->nullable()->change();
			});
		}

		if (!Capsule::schema()->hasColumn('learning_results', 'expiration_at')) {
			Capsule::schema()->table('learning_results', function ($table) {
				$table->dateTime('expiration_at')->nullable();
			});
		}

		if(!Capsule::schema()->hasColumn('learning_results','last_queue_fail')){
			Capsule::schema()->table('learning_results',function(Blueprint $table){
				$table->integer('last_queue_fail')->default(0);
			});
		}

		if (!Capsule::schema()->hasColumn('powerbi_course_set_links', 'type')) {
			Capsule::schema()->table('powerbi_course_set_links', function ($table) {
				$table->string('type')->default("learning_module");
			});
		}

		if (Capsule::schema()->hasColumn('powerbi_course_set_links', 'learning_module_id')) {
			$foreignKeys = DB::select("
				SELECT CONSTRAINT_NAME
				FROM information_schema.KEY_COLUMN_USAGE
				WHERE TABLE_NAME = 'powerbi_course_set_links'
				AND COLUMN_NAME = 'learning_module_id'
				AND CONSTRAINT_NAME = 'powerbi_course_set_learning_modules_learning_module_id_foreign'
				AND TABLE_SCHEMA = DATABASE()
			");

			if (count($foreignKeys) > 0) {
				Capsule::schema()->table('powerbi_course_set_links', function ($table) {
					$table->dropForeign('powerbi_course_set_learning_modules_learning_module_id_foreign');
				});
			}

			$foreignKeys = DB::select("
				SELECT CONSTRAINT_NAME
				FROM information_schema.KEY_COLUMN_USAGE
				WHERE TABLE_NAME = 'powerbi_course_set_links'
				AND COLUMN_NAME = 'learning_module_id'
				AND CONSTRAINT_NAME = 'powerbi_course_set_links_learning_module_id_foreign'
				AND TABLE_SCHEMA = DATABASE()
			");

			if (count($foreignKeys) > 0) {
				Capsule::schema()->table('powerbi_course_set_links', function ($table) {
					$table->dropForeign('powerbi_course_set_links_learning_module_id_foreign');
				});
			}

			Capsule::schema()->table('powerbi_course_set_links', function ($table) {
				$table->renameColumn('learning_module_id', 'type_id');
			});
		}

		if (!Capsule::schema()->hasColumn('learning_modules', 'retake_fee')) {
			Capsule::schema()->table('learning_modules', function (Blueprint $table) {
				$table->integer('retake_fee')->nullable();
			});
		}
		if (!Capsule::schema()->hasColumn('apprenticeship_standards', 'retake_fee')) {
			Capsule::schema()->table('apprenticeship_standards', function (Blueprint $table) {
				$table->integer('retake_fee')->nullable();
			});
		}

		Capsule::schema()->table('schedule_links', function (Blueprint $table) {
			if (!Capsule::schema()->hasColumn('schedule_links', 'creation_notes')) {
				$table->string('creation_notes')->nullable()->default(null)->after('refresh_reason');
			}
			if (!Capsule::schema()->hasColumn('schedule_links', 'authorisation_notes_by')) {
				$table->integer('authorisation_notes_by')->unsigned()->nullable()->default(NULL)->after('authorisation_notes');
			}
			if (!Capsule::schema()->hasColumn('schedule_links', 'authorisation_notes_at')) {
				$table->dateTime('authorisation_notes_at')->nullable()->default(NULL)->after('authorisation_notes_by');
			}
		});

		Capsule::schema()->table('schedule_links', function (Blueprint $table) {
			if (!Capsule::schema()->hasColumn('schedule_links', 'deletion_notes')) {
				$table->string('deletion_notes')->nullable()->default(null)->after('creation_notes');
			}
		});

		// For powerbi new work space
		if (!Capsule::schema()->hasColumn('powerbi_datasets', 'workspace_id')) {
			Capsule::schema()->table('powerbi_datasets', function ($table) {
				$table->string('workspace_id')->default("me")->after('powerbi_course_set_id');
			});
		}

		if (!Capsule::schema()->hasColumn('powerbi_reports', 'workspace_id')) {
			Capsule::schema()->table('powerbi_reports', function ($table) {
				$table->string('workspace_id')->default("me")->after('initial_push');
			});
		}


		Capsule::schema()->table('manager_users', function (Blueprint $table) {
			if (!Capsule::schema()->hasColumn('manager_users', 'deleted_at')) {
				$table->integer('deleted_by')->unsigned()->nullable();
				$table->string('comment_unlink')->default('')->after("comment_link");
				$table->softdeletes();
			}
		});

		if (Capsule::schema()->hasColumn('assignments', 'link_table_id')) {
			Capsule::schema()->table('assignments', function ($table) {
				$table->integer('link_table_id')->unsigned()->nullable()->change();
			});
		}

		$this->createApprentixApprenticeReportsView();
		$this->createLearningReportsView();
		$this->createEventView();
		$this->createFormAwaitingView();
		$this->createIncidentCommandFormView();
		$this->createManagerView();
		$this->createCustomProgrammeStatusView();
		$this->createUserView();
		if(!Capsule::schema()->hasColumn('roles','powerbi_access_all_data')){
			Capsule::schema()->table('roles',function($table){
				$table->boolean('powerbi_access_all_data')->default(false)->after('allow_refresh_programmes');
			});
		}

		Capsule::schema()->table('custom_reviews_filters', function (Blueprint $table) {
			if (!Capsule::schema()->hasColumn('custom_reviews_filters', 'select_type')) {
				$table->string('select_type', 100)->default('single')->after('disable_review');
			}
			if (!Capsule::schema()->hasColumn('custom_reviews_filters', 'filter_size')) {
				$table->string('filter_size', 100)->default('')->after('select_type');
			}
		});

		Capsule::schema()->table('apprenticeship_issues_user_learning_modules',function(Blueprint $table) {
			if(!Capsule::schema()->hasColumn('apprenticeship_issues_user_learning_modules', 'created_by')){
				$table->integer('created_by');
				$table->integer('deleted_by');
				$table->softDeletes();
			}
		});

		Capsule::schema()->table('apprenticeship_issues_evidence',function(Blueprint $table) {
			if(!Capsule::schema()->hasColumn('apprenticeship_issues_evidence', 'created_by')){
				$table->integer('created_by');
				$table->integer('deleted_by');
				$table->softDeletes();
			}
		});

		Capsule::schema()->table('resource_queries', function (Blueprint $table) {
			if (!Capsule::schema()->hasColumn('resource_queries', 'user_ids')) {
				$table->mediumText('user_ids')->default('')->after('raw_query');
			}
		});


		Capsule::schema()->table('role_structure', function (Blueprint $table) {
			if (!Capsule::schema()->hasColumn('role_structure', 'created_by')) {
				$table->integer('created_by')->unsigned()->nullable()->default(null)->after('disable');
			}
			if (!Capsule::schema()->hasColumn('role_structure', 'deleted_by')) {
				$table->integer('deleted_by')->unsigned()->nullable()->default(null)->after('created_by');
			}
			if (!Capsule::schema()->hasColumn('role_structure', 'deletion_reason')) {
				$table->string('deletion_reason')->default('')->after('deleted_by');
			}
		});

		if (!Capsule::schema()->hasColumn('forms', 'assign_to_user_attended_an_event')) {
			Capsule::schema()->table('forms', function ($table) {
				$table->boolean('assign_to_user_attended_an_event')->default(0)->after('has_sign_off_order')->comment ="Assign form automatically to users upon event attendance";
			});
		}

		if (!Capsule::schema()->hasColumn('forms', 'learning_module_feedback_form')) {
			Capsule::schema()->table('forms', function ($table) {
				$table->tinyInteger('learning_module_feedback_form')->default(0)->after('assign_to_user_attended_an_event');
			});
		}
		Capsule::schema()->table('learning_results_comments',function(Blueprint $table){
			if(!Capsule::schema()->hasColumn('learning_results_comments','comment_at')){
				$table->datetime('comment_at')->nullable();
			}
		});

		if (Capsule::schema()->hasColumn('signposts', 'learning_module_ids')) {
			Capsule::schema()->table('signposts', function ($table) {
				$table->dropColumn('learning_module_ids');
			});
		}

		if(!Capsule::schema()->hasColumn('signposts','domains')){
			Capsule::schema()->table('signposts',function(Blueprint $table){
				$table->string('domains')->nullable()->after('status');
			});
		}

		Capsule::schema()->table('learning_module_evidences',function(Blueprint $table){
			if(!Capsule::schema()->hasColumn('learning_module_evidences','file_size')) {
				$table->bigInteger('file_size')->nullable()->default(null)->after('evidence_type');
			}
			if(!Capsule::schema()->hasColumn('learning_module_evidences','openai_file_id')) {
				$table->string('openai_file_id')->nullable()->default(null)->after('file_size');
			}
			if(!Capsule::schema()->hasColumn('learning_module_evidences','openai_thread_id')) {
				$table->string('openai_thread_id')->nullable()->default(null)->after('openai_file_id');
			}
			if(!Capsule::schema()->hasColumn('learning_module_evidences','openai_message_id')) {
				$table->string('openai_message_id')->nullable()->default(null)->after('openai_thread_id');
			}
			if(!Capsule::schema()->hasColumn('learning_module_evidences','openai_run_id')) {
				$table->string('openai_run_id')->nullable()->default(null)->after('openai_message_id');
			}
			if(!Capsule::schema()->hasColumn('learning_module_evidences','openai_file_uploaded_at')) {
				$table->dateTime('openai_file_uploaded_at')->nullable()->default(null)->after('openai_file_id');
			}
        });

		Capsule::schema()->table('learning_module_evidences',function(Blueprint $table){
			if(!Capsule::schema()->hasColumn('learning_module_evidences','learning_result_id')){
				$table->integer('learning_result_id')->unsigned()->nullable()->after('added_by');
			}
        });

		Capsule::schema()->table('files',function(Blueprint $table){
			if(!Capsule::schema()->hasColumn('files','file_size')){
				$table->bigInteger('file_size')->nullable()->default(null)->after('added_for');
			}
		});
		if(!Capsule::schema()->hasColumn('govukpay_transactions','user_payment_transaction_id')){
			Capsule::schema()->table('govukpay_transactions',function(Blueprint $table){
				$table->integer('user_payment_transaction_id')->unsigned()->nullable()->after('id');
				$table->text('description')->change()->nullable();
			});
		}

		if (!Capsule::schema()->hasColumn('users', 'position_ref')) {
			Capsule::schema()->table('users', function ($table) {
				$table->string('position_ref')->nullable()->default(null)->after('account_type_id');
				$table->index('position_ref');
			});
        }
        if(!Capsule::schema()->hasColumn('import_log_errors','created_at')){
            Capsule::schema()->table('import_log_errors',function(Blueprint $table){
                            $table->timestamps();
            });
        }

		if (!Capsule::schema()->hasColumn('import_log_errors', 'updated_at')) {
			Capsule::schema()->table('import_log_errors', function ($table) {
				$table->timestamp('updated_at')->nullable()->default(NULL)->after('created_at');
			});
		}

		if(!Capsule::schema()->hasColumn('users','has_visited_learner_interface')){
			Capsule::schema()->table('users',function(Blueprint $table){
				$table->boolean('has_visited_learner_interface')->deafult(0)->after('status');
			});
		}

		Capsule::schema()->table('configuration', function(Blueprint $table) {
			if(!Capsule::schema()->hasColumn('configuration', 'public')) {
				$table->boolean('public')->default(false)->after('secure');
			}
		});

		Capsule::schema()->table('forms', function(Blueprint $table) {
			if(!Capsule::schema()->hasColumn('forms', 'thumbnail')) {
				$table->string('thumbnail')->default('')->after('word_file');
			}
		});

	        if (!Capsule::schema()->hasColumn('learning_modules', 'mandatory_certificate_upload')) {
	            Capsule::schema()->table('learning_modules', function ($table) {
	                $table->boolean('mandatory_certificate_upload')->default(0)->after('status')->comment ="If set to true, managers need to upload the certificate before a skill can be refreshed.";
	            });
	        }


	        if (!Capsule::schema()->hasColumn('email_templates', 'variables')) {
	            Capsule::schema()->table('email_templates', function ($table) {
	                $table->longText('variables')->nullable()->default('{}')->after('ignore_queue_approval');
	            });
	        }

	        // Add new fields for SCOR-5559 - UKROeD Amend Help File
	        if (!Capsule::schema()->hasColumn('email_templates', 'system_trigger')) {
	            Capsule::schema()->table('email_templates', function ($table) {
	                $table->text('system_trigger')->nullable()->after('variables');
	            });
	        }
	        if (!Capsule::schema()->hasColumn('email_templates', 'recipients')) {
	            Capsule::schema()->table('email_templates', function ($table) {
	                $table->text('recipients')->nullable()->after('system_trigger');
	            });
	        }
	        if (!Capsule::schema()->hasColumn('email_templates', 'timings_recurrence')) {
	            Capsule::schema()->table('email_templates', function ($table) {
	                $table->text('timings_recurrence')->nullable()->after('recipients');
	            });
	        }

		Capsule::schema()->table('javascript_error_logs', function (Blueprint $table) {
			if (!Capsule::schema()->hasColumn('javascript_error_logs', 'counter')) {
				$table->integer('counter')->default(1);
			};
		});
        Capsule::schema()->table('schedules',function($table){
            if(!Capsule::schema()->hasColumn('schedules','outlook_subscription_id')){
                $table->string('outlook_subscription_id')->nullable()->after('outlook_event_id');
            };
        });

		if (!Capsule::schema()->hasColumn('forms', 'learning_module_feedback_form')) {
		    Capsule::schema()->table('forms', function ($table) {
		        $table->tinyInteger('learning_module_feedback_form')->default(0)->after('assign_to_user_attended_an_event');
		    });
		}
		if (!Capsule::schema()->hasColumn('apprenticeship_standards', 'duration_updated')) {
		    Capsule::schema()->table('apprenticeship_standards', function ($table) {
		        $table->boolean('duration_updated')->default(0)->after('updated_at');
		    });
        }
        // ilr_user_prior_attainment
        Capsule::schema()->table('ilr_user_prior_attainment', function ($table) {
            $table->integer('user_id')->nullable()->change();
            $table->string('PriorLevel')->nullable()->change();
            $table->dateTime('DateLevelApp')->nullable()->change();
        });

        // ilr_user_learner_fam
        Capsule::schema()->table('ilr_user_learner_fam', function ($table) {
            $table->integer('user_id')->nullable()->change();
            $table->string('LearnFAMType')->nullable()->change();
            $table->string('LearnFAMCode')->nullable()->change();
        });

        // ilr_user_prior_attainment_level
        Capsule::schema()->table('ilr_user_prior_attainment_level', function ($table) {
            $table->integer('value')->nullable()->change();
            $table->string('name')->nullable()->change();
        });

        // ilr_user_learner_fam_types
        Capsule::schema()->table('ilr_user_learner_fam_types', function ($table) {
            $table->string('name')->nullable()->change();
            $table->string('value')->nullable()->change();
        });

        // ilr_user_learner_fam_codes
        Capsule::schema()->table('ilr_user_learner_fam_codes', function ($table) {
            $table->string('name')->nullable()->change();
            $table->integer('value')->nullable()->change();
        });

        // ilr_learning_delivery_fam
        Capsule::schema()->table('ilr_learning_delivery_fam', function ($table) {
            $table->integer('learning_delivery_id')->nullable()->change();
            $table->integer('user_id')->nullable()->change();
            $table->string('LearnDelFAMType')->nullable()->change();
            $table->string('LearnDelFAMCode')->nullable()->change();
            $table->dateTime('LearnDelFAMDateFrom')->nullable()->change();
            $table->dateTime('LearnDelFAMDateTo')->nullable()->change();
        });

        // ilr_learning_delivery_fam_types
        Capsule::schema()->table('ilr_learning_delivery_fam_types', function ($table) {
            $table->string('name')->nullable()->change();
            $table->string('value')->nullable()->change();
            $table->integer('limit')->nullable()->change();
        });

        // ilr_learning_delivery_fam_codes
        Capsule::schema()->table('ilr_learning_delivery_fam_codes', function ($table) {
            $table->string('name')->nullable()->change();
            $table->integer('value')->nullable()->change();
        });

        // Convert email_templates text columns to LONGTEXT for larger content support
        // This prevents base64 image truncation in email templates
        if (Capsule::schema()->hasColumn('email_templates', 'body')) {
            Capsule::schema()->table('email_templates', function ($table) {
                $table->longText('body')->change();
            });
        }

        if (Capsule::schema()->hasColumn('email_templates', 'variables')) {
            Capsule::schema()->table('email_templates', function ($table) {
                $table->longText('variables')->change();
            });
        }


        if(!Capsule::schema()->hasColumn('users','licence_type')){
            Capsule::schema()->table('users',function(Blueprint $table){
                $table->string('licence_type')->nullable()->after('updated_at');
            });
        }

		if (!Capsule::schema()->hasColumn('learning_modules', 'add_to_ai_chat')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->boolean('add_to_ai_chat')->default(false);
			});
        }
        if(!Capsule::schema()->hasColumn('user_payment_transactions','is_retake')){
            Capsule::schema()->table('user_payment_transactions',function(Blueprint $table){
                $table->boolean('is_retake')->default(false)->after('scpReference');
            });
        }


	}

	// Run table data change before changing structure of tables
	public function updateTableDataPre() {
		// Convert jackdaw user ownership of created resources to field "jackdaw_resource".
		// "created_by"(user_id) and "jackdaw_resource"(boolean) will indicate that this user created this rescource using Jackdaw interface.

		// Field needs to be created before is_jackdaw should be deleted as data needs to be updated.
		if (!Capsule::schema()->hasColumn('learning_modules', 'jackdaw_resource')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->boolean('jackdaw_resource')->default(false)->after('jackdaw');
			});
		}

		// Add library_item_id field to track the original library resource ID
		if (!Capsule::schema()->hasColumn('learning_modules', 'library_item_id')) {
			Capsule::schema()->table('learning_modules', function ($table) {
				$table->unsignedInteger('library_item_id')->nullable()->after('jackdaw_resource');
			});
		}



		// Old created resources
		if (Capsule::schema()->hasColumn('roles', 'is_jackdaw')) {
			$resources = \Models\LearningModule::where('status', true)
				->where('type_id', 1)
				->whereHas('CreatedBy', function ($query) {
					$query
						->where('status', true)
						->whereHas('Role', function ($query) {
							$query
								->where('is_jackdaw', true)
								->where('jackdaw_type', '!=', '');
						});
				})
				->get();
			foreach ($resources as $key => $resource) {
				$module = \Models\LearningModule::find($resource->id);
				$module->jackdaw_resource = true;
				$module->save();
			}
		}

		// Update/ADD SmcrReportTypes
		$smcr_report_types = [
			'Certificate of Completion',
			'Statement of Responsibilities',
		];
		foreach ($smcr_report_types as $key => $smcr_report_type) {
			$type = \Models\SmcrReportType::firstOrCreate(
				['id' => ($key + 1)],
				['name' => $smcr_report_type]
			);
			$type->status = true;
			$type->save();
		}

		// Change "Apprentix resources reminder" template name.
		$apprentix_resources_reminder = \Models\EmailTemplate
			::where('name', 'Apprentix resources reminder')
			->first();
		if ($apprentix_resources_reminder) {
			$apprentix_resources_reminder->name = '%%programme%% resources reminder';
			$apprentix_resources_reminder->save();
		}

		Capsule::schema()->table('email_history', function (Blueprint $table) {
			if (!Capsule::schema()->hasColumn('email_history', 'body_text_only')) {
				$table->text('body_text_only')->nullable()->after('body');
				//ALTER TABLE email_history DROP INDEX body;
				if(DB::select("SHOW INDEX FROM email_history WHERE Key_name = 'body'")){
					DB::statement('ALTER TABLE email_history DROP INDEX body;');
				}
			}
			if (!Capsule::schema()->hasColumn('email_history', 'converted')) {
				$table->boolean('converted')->default(false)->after('body_text_only');
			}

		});

	}


	// For adding missing records, altering, deleting, etc.
	public function updateTableData() {

		// Update configuration table with missing records, if needed
		\DB\Configuration::update($this->settings);

        // Update plans
        \DB\LicenseFeatures::update($this->settings);

		// Update cron table with missing records, if needed
		\DB\Cron::update($this->settings);

		// Update Timings table
		\DB\Timing::update($this->settings);

		\DB\FPCategories::update($this->settings);

		// Replace/update custom_reviews_filters
		\DB\CustomReviews::update();
		\DB\CustomReviews::updateFilters($this->settings);

		// All e-mail templates here!
		\DB\EmailTemplates::update();

		// Here is all actions that will be permanent, on each upgrade
		\DB\PermanentChanges::update($this->settings);

		// Reset/update permissions, permanent
		\DB\Permissions::update();
		\DB\Permissions::reset();

		\DB\Picklists::update();

		// Replace/update ResourceQuery
		\DB\ResourceQuery::update();
		Field::update();
		FieldCategory::update();
		// More or less put temporary things here that will be obsolete after one run

		// add free modules to free_jackdaw_modules table, if there is no information.
		$free_jackdaw_modules = \Models\FreeJackdawModule::count();
		if ($free_jackdaw_modules == 0) {
			$free_modules = ['Implementing the GDPR', 'First Aid', 'Functional Skills Maths Fractions', 'How to manage an Office Facility', 'Storyboarding for Media Creation', 'Microsoft Word in 15 Minutes', 'Guest Complaints', 'Drugs and Young People', 'CDM Contractor Management'];
			foreach ($free_modules as $key => $free_module) {
				$module = new \Models\FreeJackdawModule;
				$module->name = $free_module;
				$module->created_by = 0;
				$module->save();
			}
		}

		/*
		NOT NEEDED FOR NOW.
		// Learning results, update due_at if it is empty.
		$learning_results = \Models\LearningResult
			::whereNull('due_at')
			->get()
		;
		$x = 0;
		foreach ($learning_results as $key => $learning_result) {
			$result = \Models\LearningResult
				::where('id', $learning_result->id)
				->with('module')
				->first()
			;
			$timing = \APP\Tools::getTimings($result->module);

			if ($timing) {
				$result->timestamps = false;
				$result->due_at = \Carbon\Carbon::parse($result->created_at)->addDays($timing);
				$result->save();
				$x++;
			}
		}
		echo "Updated empty due_at fields in learning_results table: " . $x;
		*/

		// Add/Overwrite default Staff types, with ID's
		// Subject to change or might be editable per instance eventually.
		$staff_types = [
			1 => 'Standard (Conduct Rules)',
			2 => 'Certification Staff',
			3 => 'Senior Manager',
		];

		foreach ($staff_types as $key => $staff_type) {
			$type = \Models\SmcrStaffType
				::firstOrNew(
					['id' => $key]
				);
			$type->name = $staff_type;
			$type->save();
		}

		// Add new e-learning type/s
		$resource_types = [
			'Reflective Log', // Same as evidence, with additional fields.
			'Vimeo', //Vimeo video clips
			'Moodle Course', //An external Moodle course
			'Google Classroom',
			'Zoom Meeting',
			'Microsoft Teams',
			'Jamboard',
			'Event',
			'H5P',
			'Form',
			'Skill',
		];
		//add slugs
		$slugs = [
			"H5P" => "h5p",
			"Jamboard" => "jamboard",
			"Form" => "form"
		];
		//add fields
		$type_fields = [
			"H5P" => "",
			"Jamboard" => "",
		];

		// Load disableResourceTypes configuration parameter, do not update/create types mentioned in this parameter!
		$disableResourceTypes = \APP\Tools::getConfig('disableResourceTypes');
		if ($disableResourceTypes) {
			$disableResourceTypes = array_map('trim', explode(',', $disableResourceTypes));
		}

		foreach ($resource_types as $key => $resource_type) {
			$fields = ['name' => $resource_type];
			if (isset($slugs[$resource_type])){
				$fields['slug'] = $slugs[$resource_type];
			}
			if (isset($type_fields[$resource_type])){
				$fields['field'] = $type_fields[$resource_type];
			}

			$type = \Models\LearningModuleType::firstOrNew($fields);
			$type->status = 1;

			// create temporary slug
			$slug = strtolower(preg_replace('/[\W]/', '_', $resource_type));
			// If disabled resource type exists in array and current looped one is, do not save it.
			if (
				(
					is_array($disableResourceTypes) &&
					!in_array($slug, $disableResourceTypes)
				) ||
				!is_array($disableResourceTypes)
			) {
				$type->save();
			}

		}

		if (!Capsule::schema()->hasColumn('evidence_types', 'perishable')) {
			Capsule::schema()->table('evidence_types', function ($table) {
				$table->boolean('perishable')->default(false)->after('name');
			});
		}
		if (!Capsule::schema()->hasColumn('evidence_types', 'life_of_data')) {
			Capsule::schema()->table('evidence_types', function ($table) {
				$table->integer('life_of_data')->unsigned()->nullable()->default(NULL)->after('perishable');
			});
		}
		if (!Capsule::schema()->hasColumn('evidence_types', 'life_after')) {
			Capsule::schema()->table('evidence_types', function ($table) {
				$table->text('life_after')->nullable()->default(NULL)->after('life_of_data');
			});
		}

		if(!Capsule::schema()->hasColumn('resource_queries','user_ids')) {
			Capsule::schema()->table('resource_queries',function($table){
				$table->mediumText('user_ids')->nullable()->default(NULL)->after('raw_query');
			});
		}
		if(!Capsule::schema()->hasColumn('document_template_bindings','custom_graph_id'))
		{
			Capsule::schema()->table('document_template_bindings',function($table){
			$table->integer('custom_graph_id')->nullable();
		  });
		}
		if(!Capsule::schema()->hasColumn('document_template_bindings','is_graph'))
		{
			Capsule::schema()->table('document_template_bindings',function($table){
			  $table->boolean('is_graph')->default(false);
			});
		}
		if(!Capsule::schema()->hasColumn('learning_module_categories','order'))
		{
			Capsule::schema()->table('learning_module_categories',function($table){
				$table->integer('order')->nullable()->default(NULL)->after('name');
			});
		}

		if(!Capsule::schema()->hasColumn('form_fields','alternative_default_value'))
		{
			Capsule::schema()->table('form_fields',function($table){
			$table->string('alternative_default_value')->nullable()->default(NULL);
			});
		}
		// new field user_id added for surrey import
		if(!Capsule::schema()->hasColumn('users','user_id'))
		{
			Capsule::schema()->table('users',function($table){
				$table->unsignedBigInteger('user_id')->nullable()->default(NULL)->comment('Added for surrey import');
			});
		}

		if (Capsule::schema()->hasColumn('schedules', 'name')) {
			Capsule::schema()->table('schedules', function ($table) {
				$table->text('name')->change();
			});
		}


		// Add default SM Functions/Responsibilities and CS Functions
		// NOT NEEDED FOR NOW!!!!
		//\DB\FunctionsResponsibilities::update();

		// Add default evidence_types entries

		$evidence_types = [
			'Observation in the Workplace',
			'Professional Discussion',
			'Witness Testimony',
			'Case Study',
			'Evidence of Prior Learning',
			'Workplace Project',
			'Examples of work gathered',
			'Turnitin Assignment',
			'Other',
		];

		foreach ($evidence_types as $key => $evidence_type) {
			$type = \Models\EvidenceType::firstOrCreate(
				['name' => $evidence_type]
			);
		}

		// Delivery outcome table, initial rework of ILR, will be moved/imporoved, etc, proof of concept.
		foreach ($this->settings['ilr_fields']['LearningDelivery']['children']['Outcome']['choices'] as $key => $entry) {
			$query = \Models\IlrLearningDeliveryOutcome::firstOrNew(
				['id' => $entry['value']]
			);
			$query->name = $entry['name'];
			$query->save();
		}

		// LD Tailored Learning Outcome table.
		foreach ($this->settings['ilr_fields']['LearningDelivery']['children']['TLOut']['choices'] as $key => $entry) {
			$query = \Models\IlrLearningDeliveryTailoredLearningOutcome::firstOrNew(
				['id' => $entry['value']]
			);
			$query->name = $entry['name'];
			$query->save();
		}
		// Learning End - Completion Status
		foreach ($this->settings['ilr_fields']['LearningDelivery']['children']['CompStatus']['choices'] as $key => $entry) {
			$query = \Models\IlrLearningDeliveryCompletionStatus::firstOrNew(
				['id' => $entry['value']]
			);
			$query->name = $entry['name'];
			$query->save();
		}

		foreach ($this->settings['ilr_fields']['LearningDelivery']['children']['FundModel']['choices'] as $key => $entry) {
			$query = \Models\IlrLearningDeliveryFundingModel::firstOrNew(
				['id' => $entry['value']]
			);
			$query->name = $entry['name'];
			$query->save();
		}


		// Aim type table
		foreach ($this->settings['ilr_fields']['LearningDelivery']['children']['AimType']['choices'] as $key => $entry) {
			$query = \Models\IlrLearningDeliveryAimType::firstOrNew(
				['id' => $entry['value']]
			);
			$query->name = $entry['name'];
			$query->save();
		}

		// Programme type table
		foreach ($this->settings['ilr_fields']['LearningDelivery']['children']['ProgType']['choices'] as $key => $entry) {
			$query = \Models\IlrLearningDeliveryProgrammeType::firstOrNew(
				['id' => $entry['value']]
			);
			$query->name = $entry['name'];
			$query->save();
		}


		// Learning Delivery Financial record type
		foreach ($this->settings['ilr_fields']['LearningDelivery']['children']['AppFinRecord']['children']['AFinType']['choices'] as $key => $entry) {
			$query = \Models\IlrLearningDeliveryFinancialRecordType::firstOrNew(
				['code' => $entry['value']]
			);
			$query->name = $entry['name'];
			$query->save();
		}

		// Learning Delivery Financial record code
		foreach ($this->settings['ilr_fields']['LearningDelivery']['children']['AppFinRecord']['children']['AFinCode']['choices'] as $key => $entry) {
			$query = \Models\IlrLearningDeliveryFinancialRecordCode::firstOrNew(
				[
					'code' => $entry['value'],
					'type' => $entry['condition_value']
				]
			);
			$query->name = $entry['name'];
			$query->save();
		}

		// Employment status table for ILR
		foreach ($this->settings['ilr_fields']['LearnerEmploymentStatus']['children']['EmpStat']['choices'] as $key => $entry) {
			$query = \Models\IlrEmploymentStatus::firstOrNew(
				['id' => $entry['value']]
			);
			$query->name = $entry['name'];
			$query->save();
		}

		// DPOutcome code
		foreach ($this->settings['ilr_fields']['DPOutcome']['children']['OutCode']['choices'] as $key => $entry) {
			$query = \Models\IlrProgressionOutcomeCode::firstOrNew(
				[
					'value' => $entry['value'],
					'condition_key' => $entry['condition_key'],
					'condition_value' => $entry['condition_value'],
				]
			);
			$query->name = $entry['name'];
			$query->save();
		}
		// DPOutcome type
		foreach ($this->settings['ilr_fields']['DPOutcome']['children']['OutType']['choices'] as $key => $entry) {
			$query = \Models\IlrProgressionOutcomeType::firstOrNew(
				[
					'value' => $entry['value'],
				]
			);
			$query->name = $entry['name'];
			$query->save();
        }
        // Prior Attainment
        foreach($this->settings['ilr_fields']['PriorAttain']['children']['PriorLevel']['choices'] as $key=>$entry){
            $query =  IlrUserPriorAttainmentLevel::firstOrNew(
                [
                    'value' => $entry['value'],
                ]
            );
            $query->name = $entry['name'];
            $query->save();
        }
        foreach($this->settings['ilr_fields']['LearnerFAM']['children']['LearnFAMType']['choices'] as $key=>$entry){
            $query =  IlrUserLearnerFamTypes::firstOrNew(
            [
                'value' => $entry['value'],
            ]
            );
            $query->name = $entry['name'];
            $query->LLDD = $entry['LLDD'] ?? false;
            $query->code = $entry['code'] ?? 0;
            $query->codeKey = $entry['codeKey'] ?? null;
            $query->save();
        }
        foreach($this->settings['ilr_fields']['LearnerFAM']['children']['LearnFAMCode']['choices'] as $key=>$entry){
            $query =  IlrUserLearnerFamCodes::firstOrNew(
            [
            'value' => $entry['value'],
            ]
            );
            $query->name = $entry['name'];
            $query->condition_key = $entry['condition_key'] ?? null;
            $query->condition_value = $entry['condition_value'] ?? null;
            $query->save();
        }
        foreach($this->settings['ilr_fields']['LearningDelivery']['children']['LearningDeliveryFAM']['children']['LearnDelFAMType']['choices'] as $key=>$entry){
            $query =  IlrLearningDeliveryFamTypes::firstOrNew(
            [
                'value' => $entry['value'],
            ]
            );
            $query->name = $entry['name'];
            $query->limit = $entry['limit'] ?? null;
            $query->save();
        }
        foreach($this->settings['ilr_fields']['LearningDelivery']['children']['LearningDeliveryFAM']['children']['LearnDelFAMCode']['choices'] as $key=>$entry){
            $query =  IlrLearningDeliveryFamCodes::firstOrNew(
            [
                    'value' => $entry['value'],
                    'condition_value'=> $entry['condition_value'] ?? null,
            ]
            );
            $query->name = $entry['name'];
            $query->condition_key = $entry['condition_key'] ?? null;
            $query->condition_value = $entry['condition_value'] ?? null;
            $query->save();
        }
        foreach($this->settings['ilr_fields']['LearningDelivery']['children']['WithdrawReason']['choices'] as $key => $entry){
            $query = IlrLearningDeliveryWithdrawReasons::firstOrNew(
                [
                    'value' => $entry['value'],
                ]
            );
            $query-> name = $entry['name'];
            $query->save();
        }


		/*
		// Change name from Evidence to "Upload".
		$evidence = \Models\LearningModuleType::where('name', 'Evidence')
			->where('id', 7)
			->first()
		;
		if ($evidence) {
			$evidence->name = 'Upload';
			$evidence->save();
		}
		*/

		// Update all roles to learner role, ones that do not have admin/manager/qa/demo check.
		$update_roles = \Models\Role::where('status', true)
			->where('is_admin', false)
			->where('is_manager', false)
			->where('is_learner', false)
			->where('is_demo', false)
			->where('is_qa', false)
			->where('is_cd', false)
			->where('is_fa', false)
			->get();

		// Update only if column "is_learner" exists.
		if (Capsule::schema()->hasColumn('roles', 'is_learner')) {
			foreach ($update_roles as $key => $update_role) {
				$role = \Models\Role::find($update_role->id);
				$role->is_learner = true;
				$role->save();
			}
		}

		// Populates slug for each record in roles table.
		$roles = \Models\Role::whereNull('slug')->get();
		if (Capsule::schema()->hasColumn('roles', 'slug')) {
			foreach ($roles as $key => $role) {
				$role->update([
					'slug' => \Illuminate\Support\Str::slug($role->name)
				]);
			}
		}

		// Move Learner Destination and Progression from learning delivery into user->LearnerDestinationandProgression field
		$users = \Models\User::where('status', true)
			->where('LearningDelivery', '!=', '')
			->whereNull('LearnerDestinationandProgression')
			->get();
		foreach ($users as $key => $user) {
			$LDP = [
				'LearnRefNumber' => $user->LearnRefNumber,
				'ULN' => $user->ULN,
				'DPOutcome' => []
			];
			$LearningDeliveries = json_decode($user->LearningDelivery, true);
			foreach ($LearningDeliveries as $key => $LearningDelivery) {
				if (isset($LearningDelivery['DPOutcome']) && $LearningDelivery['DPOutcome']) {
					foreach ($LearningDelivery['DPOutcome'] as $key => $DPOutcome) {
						$LDP['DPOutcome'][] = $DPOutcome;
					}
				}
			}
			if (count($LDP['DPOutcome']) > 0) {
				$save_user = \Models\User::find($user->id);
				$save_user->LearnerDestinationandProgression = json_encode($LDP);
				$save_user->save();
			}
		}

		//  Create hash field for evidene file table and use that to download files.
		// also, move all files to private directory, remove horrible date appendix.
		$evidences = \Models\LearningModuleEvidence::where('evidence_type', 'file')
			->get();
		foreach ($evidences as $key => $evidence) {
			if (!$evidence->hash) {
				$update = \Models\LearningModuleEvidence::find($evidence->id);
				$old_file = $this->settings["LMSEvidencePathOld"] . $evidence->evidence;
				if (is_file($old_file)) {
					$update->hash = bin2hex(random_bytes(16));
					$update->evidence = preg_replace("/(_[0-9]{2}_[0-9]{2}_[0-9]{4}__[0-9]{2}_[0-9]{2}_[0-9]{2})/", "", $evidence->evidence);
					$update->extension = pathinfo($old_file, PATHINFO_EXTENSION);
					rename($old_file, $this->settings["LMSEvidencePath"] . $update->hash . '.' . $update->extension);

					$update->save();
				}
			}
		}


		// Update email_queue frequency to frequency_pattern
		$email_queue = \Models\EmailQueue::whereNotNull('frequency')
			->get();
		foreach ($email_queue as $key => $queue) {
			$query = \Models\EmailQueue::find($queue->id);
			$query->frequency_pattern = '{"frequency":' . $queue->frequency . ',"period":{"name":"Day","id":"every_day","$$hashKey":"object:4599"},"day":[],"date":[]}';
			$query->frequency = null;
			$query->save();
		}

		// Look into batch_reports and update missing custom_review_id
		$batch_reports = \Models\BatchReport::whereNull('custom_review_id')
			->get();
		foreach ($batch_reports as $key => $batch_reports) {
			// Get ID from slug
			$custom_review = \Models\CustomReview::where('slug', $batch_reports->slug)
				->first();
			if ($custom_review) {
				$report = \Models\BatchReport::find($batch_reports->id);
				$report->custom_review_id = $custom_review->id;
				$report->save();
			}
		}

		/*
		Needed to be ran only once!
		// Update version in configuration table, if oneis missing
		$version = \Models\Configuration::where('key', 'version')->first();
		if (
			$version &&
			empty($version->value)
		) {
			$version->value = 'openelms';
			if (
				$this->settings["licensing"]["isOpenElms"] &&
				$this->settings["licensing"]["isOpenElmsTMS"]
			) {
				$version->value = 'openelmstms';
			}
			if ($this->settings["licensing"]["isApprentix"]) {
				$version->value = 'apprentix';
			}
			if ($this->settings["licensing"]["isSMCR"]) {
				$version->value = 'smcrsolution';
			}
			if ($this->settings["licensing"]["isOmniPrez"]) {
				$version->value = 'omniprez';
			}
			$version->save();
		}
		*/

		// check if course 62 is bribery act
		$bribery = \Models\LearningModule::find(62);
		if (
			$bribery &&
			$bribery->name == 'Bribery Act'
		) {
			// Update question_type to exam - "assessment_questions" for course_id 62.
			\Models\Assessment\Question::where('course_id', 62)
				->where('question_type', '!=', 'exam')
				->update(['question_type' => 'exam']);

			// Delete any row in assessment_data for course_id 62
			\Models\Assessment\Data::where('course_id', 62)->delete();

			// Update quizXML.xml for course 62
			// From: QuestionType=""
			// To: QuestionType="exam"
			$quizfile = $this->settings["LMSPublicPath"] . 'scormdata/62/moddata/scorm/1/quizXML.xml';
			if (is_file($quizfile)) {
				$quizxml = file_get_contents($quizfile);
				$quizxml = str_replace('QuestionType=""', 'QuestionType="exam"', $quizxml);
				file_put_contents($quizfile, $quizxml);
			}
		}

		$gateway_readiness = [
			'Not all All Ready for Gateway',
			'Partially Ready for Gateway',
			'Moderately Ready for Gateway',
			'Mostly Ready for Gateway',
			'Completely Ready for Gateway',
		];

		foreach ($gateway_readiness as $key => $readiness_name) {
			$readiness = \Models\GatewayReadiness::firstOrNew(
				['name' => $readiness_name]
			);
			$readiness->status = true;
			$readiness->save();
		}

		// Update SMCrReports type to 1 if not present.
		\Models\SmcrReport::whereNull('type_id')
			->update(['type_id' => 1]);

		// Remove absolete e-mail templates
		\Models\EmailTemplate
			::where('name', 'You have been assigned a SMF under the SMCR')
			->orWhere('name', 'You have been assigned a certification function under the SMCR')
			->orWhere('name', 'You have been assigned a responsibility under the SMCR')
			->delete();

		// Fix typo in configuration table, Correct “e-mnail” to email
		// https://bitbucket.org/emilrw/scormdata/issues/1171/smcr-solution-typo-email-configuration
		$cfg_query = \Models\Configuration
			::where('key', 'enableEmailFunctionality')
			->where('name', 'LIKE', '%e-mnail%')
			->first();
		if ($cfg_query) {
			$cfg_query->name = str_replace("e-mnail", "email", $cfg_query->name);
			$cfg_query->save();
		}


		// Update learning_module_types slug, if missing
		$learning_module_types = \Models\LearningModuleType::get();
		foreach ($learning_module_types as $key => $learning_module_type) {
			if (!$learning_module_type->slug) {
				$learning_module_type->slug = strtolower(preg_replace('/[\W]/', '_', $learning_module_type->getOriginal('name')));
				$learning_module_type->save();
			}
		}

		// Update workflow slug, if missing
		$workflows = \Models\FormWorkflow::whereNull('slug')->get();
		foreach ($workflows as $workflow) {
			$slug =  \APP\Tools::safeName($workflow->name)."_$workflow->id";
			$workflow->slug = $slug;
			$workflow->save();
		}

		// Update graph slug, if missing
		$graphs = \Models\Graph::whereNull('slug')->get();
		foreach ($graphs as $graph) {
			$slug =  \APP\Tools::safeName($graph->name)."_$graph->id";
			$graph->slug = $slug;
			$graph->save();
		}

		// Update custom report slug, if missing
		$customReports = \Models\CustomReport::whereNull('slug')->get();
		foreach ($customReports as $customReport) {
			$slug =  \APP\Tools::safeName($customReport->name)."_$customReport->id";
			$customReport->slug = $slug;
			$customReport->save();
		}

		// Update document_template slug, if missing
		$documentTemplates = \Models\DocumentTemplate::whereNull('slug')->get();
		foreach ($documentTemplates as $documentTemplate) {
			$slug =  \APP\Tools::safeName($documentTemplate->name)."_$documentTemplate->id";
			$documentTemplate->slug = $slug;
			$documentTemplate->save();
		}

		// fix job and location field
		$jobField = \Models\Field::where('slug', 'job_id');
		$jobField->update(['type' => 'selectbox', 'model' => 'Models\Designation', 'label' => 'name', 'value' => 'id']);
		$locationField = \Models\Field::where('slug', 'location_id');
		$locationField->update(['type' => 'selectbox', 'model' => 'Models\Location', 'label' => 'name', 'value' => 'id']);

		// Update Dataview with translation strings
		$replace_dataviews = [
			"Learning Resources" => "%%learning_resources%%",
			"Company" => "%%company%%",
			"Apprentices" => "%%users%%",
			"Apprentice" => "%%user%%",
			"Coach/Assessor" => "%%manager%%",
			"Coach" => "%%manager%%",
		];

		$dataviews = \Models\Dataview::get();
		foreach ($dataviews as $key => $dataview) {
			foreach ($replace_dataviews as $replace_key => $replace_dataview) {
				if (strpos($dataview->name, $replace_key) !== false) {
					$dataview->name = str_replace($replace_key, $replace_dataview, $dataview->name);
					// $dataview->save();
				}
			}
		}

		// Update old variables in email.
		$old_variables = \Models\EmailTemplate
			::where('body', 'LIKE', '%\%USER.FNAME\%%')
			->orWhere('body', 'LIKE', '%\%USER.LNAME\%%')
			->get();
		foreach ($old_variables as $key => $old_variables) {
			$old_variables->body = str_replace('%USER.FNAME%', '%%USER_FNAME%%', $old_variables->body);
			$old_variables->body = str_replace('%USER.LNAME%', '%%USER_LNAME%%', $old_variables->body);
			$old_variables->save();
		}

		// Add default ScheduleVisitType
		// Value will be name, while key will be slug and remain the same!
		$schedule_visit_types = [
			'Initial Assessment' => 'Initial Assessment/ILP',
			'Development' => 'Development',
			'Training' => 'Training',
			'General Admin' => 'General Admin',
			'Progress Review' => 'Progress Review',
			'QA Report' => 'QA Report',
		];
		foreach ($schedule_visit_types as $schedule_visit_type_key => $schedule_visit_type) {

			// Attributes to search for
			$attributes = [
				'slug' => \APP\Tools::safeName($schedule_visit_type_key),

			];

			$off_the_job_training = true;
			if (
				$schedule_visit_type == 'Development' ||
				$schedule_visit_type == 'Training'
			) {
				$off_the_job_training = false;
			}

			// Values to set if a new record is created
			$values = [
				'name' => $schedule_visit_type,
				'off_the_job_training' => $off_the_job_training,
				'status' => true,
			];

			\Models\ScheduleVisitType::firstOrCreate($attributes, $values);
		}

		// Update all schedules where type is not defined
		$schedules = \Models\Schedule
			::where('status', true)
			->whereNull('type')
			->whereNull('parent_id')
			->get();
		foreach ($schedules as $key => $schedule) {
			$schedule->type = 'lesson';
			$schedule->save();
		}



		// Extendendable table fields, default ones.
		$extended_field_list = [
			'users' => [
				'Salutation' => [
					'type' => 'string',
					'name' => 'Title',
					'versions' => '"nras"',
					'show_learner' => true,
					'show_administration' => true,
					'options' => 'salutations',
				],
				'ContactID__c' => [
					'type' => 'string',
					'name' => 'Contact ID',
					'versions' => '"nras"',
					'show_administration' => true,
				],
				'npe01__Preferred_Email__c' => [
					'type' => 'string',
					'versions' => '"nras"',
				],
				'Gender__c' => [
					'type' => 'string',
					'name' => 'Gender',
					'versions' => '"nras"',
					'show_learner' => true,
					'show_administration' => true,
					'options' => 'genders',
				],
				'Contact_Relationship__c' => [
					'type' => 'string',
					'versions' => '"nras"',
				],
				'Year_of_Onset__c' => [
					'type' => 'string',
					'name' => 'Year of Diagnosis',
					'versions' => '"nras"',
					'show_learner' => true,
					'show_administration' => true,
				],
				'Contact_Source__c' => [
					'type' => 'string',
					'name' => 'How did you hear about NRAS E-Learning?',
					'versions' => '"nras"',
					'show_learner' => true,
					'default' => 'E-Learning',
					'show_administration' => true,
					'options' => 'contact_sources',
				],
				'opt_in_date__c' => [
					'type' => 'date',
					'versions' => '"nras"',
				],
				'Health_Consent__c' => [
					'type' => 'string',
					'name' => 'Consent is given to',
					'versions' => '"nras"',
					'description' => '
						<ul>
							<li>customise the information and services for you within SMILE-RA</li>
							<li>monitor the impact of our e-learning platform and services</li>
							<li>collect anonymised data to inform future service development and to market our e-learning resources to people with RA and the rheumatology community for the benefit of all</li>
						</ul>
					',
					'show_learner' => true,
					'show_administration' => true,
				],
				'Preferences_Last_Updated__c' => [
					'type' => 'date',
					'versions' => '"nras"',
				],
				'Employment_Status__c' => [
					'type' => 'string',
					'name' => 'Employment Status',
					'versions' => '"nras"',
					'show_learner' => true,
					'show_administration' => true,
					'options' => 'employment_status',
				],
				'RecordTypeID' => [
					'type' => 'string',
					'versions' => '"nras"',
				],
				'Do_not_contact__c' => [
					'type' => 'boolean',
					'versions' => '"nras"',
				],
				'Right_To_Be_Forgotten__c' => [
					'type' => 'boolean',
					'versions' => '"nras"',
				],
				'Name' => [
					'type' => 'string',
					'versions' => '"nras"',
				],
				'Contact_Name__c' => [
					'type' => 'string',
					'versions' => '"nras"',
				],
				'E_Learning_External_ID__c' => [
					'type' => 'integer',
					'versions' => '"nras"',
				],
				'X1st_Evaluation_Question__c' => [
					'type' => 'integer',
					'versions' => '"nras"',
				],
				'X2nd_Evaluation_Question__c' => [
					'type' => 'integer',
					'versions' => '"nras"',
				],
				'X3rd_Evaluation_Question__c' => [
					'type' => 'integer',
					'versions' => '"nras"',
				],
				'Pain_RAID__c' => [
					'type' => 'integer',
					'versions' => '"nras"',
				],
				'Functional_Disability_Assessment_RAID__c' => [
					'type' => 'integer',
					'versions' => '"nras"',
				],
				'Fatigue_RAID__c' => [
					'type' => 'integer',
					'versions' => '"nras"',
				],
				'Sleep_RAID__c' => [
					'type' => 'integer',
					'versions' => '"nras"',
				],
				'Physical_Well_Being_RAID__c' => [
					'type' => 'integer',
					'versions' => '"nras"',
				],
				'Emotional_Well_being_RAID__c' => [
					'type' => 'integer',
					'versions' => '"nras"',
				],
				'Coping_RAID__c' => [
					'type' => 'integer',
					'versions' => '"nras"',
				],
				'RAID_Calculation_Score__c' => [
					'type' => 'float',
					'versions' => '"nras"',
				],
				'Employer' => [
					'type' => 'integer',
					'name' => 'Employer',
					'options' => 'user_managers',
				],
				'Provider' => [
					'type' => 'integer',
					'name' => 'Provider',
					'options' => 'user_managers',
				],
				'DevelopmentCoach' => [
					'type' => 'integer',
					'name' => 'Development-Coach',
					'options' => 'user_managers',
				],
				'DoesNotRequireBookingApproval' => [
					'name' => 'Does not require booking approval from management',
					'type' => 'boolean',
					'default' => 0,
				],
				'SortResourceOrder' => [
					'type' => 'string',
					'versions' => '"nras"',
					'default'=>'module.sort_order'
				],
				'color_scheme' => [
					'type' => 'string',
					'name' => 'Learner interface color scheme',
				],
                'default_screen' => [
                    'name' => 'Learner interface default screen',
                    'type' => 'string',
                ],
                'background_image' => [
                    'name' => 'Learner interface background image',
                    'type' => 'string',
                ],
                'default_date_format' => [
                    'name' => 'Learner interface default date format',
                    'type' => 'string',
                ],
				'ios_first_login_date' => [
					'type' => 'date',
				],
				'ios_purchase_date' => [
					'type' => 'date',
				],
				//i_am
				'SMILE_Relationship__c' => [
					'type' => 'string',
					'name' => 'I am',
					'versions' => '"nras"',
					'show_learner' => false,
					'show_administration' => true,
					'options' => 'iam_list',
					'disabled' => true,
				],
				'contact_preference__post' => [
					'name' => 'Contact Preference by Post',
					'type' => 'boolean',
					'versions' => '"nras"',
					'default' => 0,
				],
				'contact_preference__telephone' => [
					'name' => 'Contact Preference by Telephone',
					'type' => 'boolean',
					'versions' => '"nras"',
					'default' => 0,
				],
				'contact_preference__email' => [
					'name' => 'Contact Preference by Email',
					'type' => 'boolean',
					'versions' => '"nras"',
					'default' => 0,
				],
				'MPC_Email_Opt_in__c' => [
					'type' => 'string',
					'versions' => '"nras"',
				],
			],
			'roles' => [
			],
			'apprenticeship_standards' => [
				'number_of_evidence_examples_expected_for_each_criteria' => [
					'type' => 'integer',
					'name' => 'Number of evidence examples expected for each criteria',
					'default' => 2,
				],
			],
			'companies' => [
				'DoesNotRequireBookingApproval' => [
					'name' => 'Does not require booking approval from management',
					'type' => 'boolean',
					'default' => 0,
				],
				'learner_theme' => [
					'name' => 'Learner interface color theme',
					'type' => 'string',
				],
			],
			'departments' => [
				'DoesNotRequireBookingApproval' => [
					'name' => 'Does not require booking approval from management',
					'type' => 'boolean',
					'default' => 0,
				],
			],
			'schedules' => [
				'venue_id' => [
					'type' => 'integer',
					'name' => 'Venue ID',
				],
			],
			'learning_results' => [
				'material' => [
					'type' => 'json',
					'name' => 'Material',
				],
			],
			'schedule_visit_types' => [
				'calculate_checkbox_updated' => [
					'type' => 'boolean',
					'name' => 'Marks entry as updated by system update',
				],
			],
		];
		// remove all entries, for now, unstill definitions will stabilize
		\Models\TableExtensionField::truncate();
		foreach ($extended_field_list as $key => $extended_field) {
			foreach ($extended_field as $extended_field_key => $field) {
				\Models\TableExtensionField::firstOrCreate(
					[
						'table' => $key,
						'field_key' => $extended_field_key
					],
					[
						'field_type' => $field['type'],
						'field_name' => isset($field['name']) ? $field['name'] : $extended_field_key,
						'conditions' => isset($field['conditions']) ? $field['conditions'] : '',
						'versions' => isset($field['versions']) ? $field['versions'] : '',
						'description' => isset($field['description']) ? $field['description'] : '',
						'show_administration' => isset($field['show_administration']) ? $field['show_administration'] : false,
						'show_learner' => isset($field['show_learner']) ? $field['show_learner'] : false,
						'options' => isset($field['options']) ? $field['options'] : null,
						'default' => isset($field['default']) ? $field['default'] : null,
					]
				);
			}
		}

		// Default event types that must be enabled by default!
		$event_types = [
			//'lesson_new' => 'Learning  (create new lessons, homework, projects etc.)',
			'lesson' => 'Lesson',
			'meeting' => 'Set up a meeting',
		];

		foreach ($event_types as $key => $event_type) {
			$entry = \Models\EventType::firstOrNew(
				['slug' => $key]
			);
			$entry->name = $event_type;
			$entry->status = true;
			$entry->system = true;
			$entry->save();
		}

		// Remove lesson_new from types!
		\Models\EventType::where('slug', 'lesson_new')->delete();


		// Update functions statuses
		$update_functions = \Models\SmcrStaffFunctionResponsibility
			::whereIn(
				'function_responsibility_id',
				\Models\SmcrFunctionResponsibility
					::select('id')
					->where('type', 'function')
					->where('status', true)
					->get()
			)
			->get();
		foreach ($update_functions as $key => $update_function) {
			if (
				$update_function->completion_status == 'Accepted' &&
				$update_function->completion_date
			) {
				$update_function->completion_status = 'Certified';
				$update_function->save();
			} elseif (
				$update_function->completion_status == 'Accepted' &&
				!$update_function->completion_date
			) {
				$update_function->completion_status = 'Not Completed';
				$update_function->save();
			} elseif (
				$update_function->completion_status == 'Not Accepted' &&
				!$update_function->completion_date
			) {
				$update_function->completion_status = 'Not Completed';
				$update_function->save();
			} elseif (
				$update_function->completion_status == 'Not Accepted' &&
				$update_function->rejected
			) {
				$update_function->completion_status = 'Failed';
				$update_function->save();
			} elseif (
				!$update_function->completion_status == 'Not Accepted'
			) {
				$update_function->completion_status = 'Not Completed';
				$update_function->save();
			}
		}

		// Update all lesson type events to visible_learner to true!
		\Models\Schedule
			::where('type', 'lesson')
			->where('visible_learner', false)
			->update(['visible_learner' => true])
		;


		// Loop trough all lessons and check if they need to be converted to information only, or back!
		$lessons = \Models\LearningModule
			::where('status', true)
			->where('is_course', 1)
			->get()
		;
		foreach ($lessons as $lesson_key => $lesson) {
			\Models\LearningCourseModule::updateTracking($lesson);
		}


		// Apprentix, ILR records, convert legacy PriorAttain to 2122 format.
		if ($this->settings["licensing"]["isApprentix"]) {
			$users_prior = \Models\User
				::where('PriorAttainLegacy', '>', 0)
				->whereNull('PriorAttain')
				->where('status', 1)
				->get()
			;
			foreach ($users_prior as $key => $user_prior) {
				$PriorAttainLegacy = false;
				$PriorAttain = false;
				foreach ($this->settings['ilr_fields']['PriorAttainLegacy']['choices'] as $key => $entry) {
					if ($entry['value'] == $user_prior->PriorAttainLegacy) {
						$PriorAttainLegacy = $entry['name'];
					}
				}
				if ($PriorAttainLegacy) {
					foreach ($this->settings['ilr_fields']['PriorAttain']['children']['PriorLevel']['choices'] as $key => $entry) {
						if (strtolower($PriorAttainLegacy) == strtolower($entry['name'])) {
							$PriorAttain = [];
							$PriorAttainEntry = new \stdClass ;
							$PriorAttainEntry->PriorLevel = $entry['value'];
							$PriorAttain[] = $PriorAttainEntry;
							$user_prior->PriorAttain = $PriorAttain;
						}
					}
				}
				if ($PriorAttain) {
					$user_prior->save();
				}

				// Find name in ilr_fields from PriorAttainLegacy
				// Check if that name exists in ilr_fields PriorAttain, then add entry to PriorAttain
			}

			// For reasons unknown(yet) PriorAttain during import was saved as object, not array containing objects, fix it there and then fix import logic!
			$users_prior = \Models\User
				::whereNotNull('PriorAttain')
				->where('status', 1)
				->get()
			;
			foreach ($users_prior as $key => $user_prior) {
				if ($user_prior->PriorAttain) {
					// Encode and decode to get true form!
					$convert = json_encode($user_prior->PriorAttain);
					$convert = json_decode($convert);
					if (!is_array($convert)) {
						$user_prior->PriorAttain = [$convert];
						$user_prior->save();
					}
				}
			}
		}

		// Disable "%%manager%% Training Impact" from custom_reviews table, neesd rewrite.
		\Models\CustomReview
			::where('key', 'coach-assessors-training-impact')
			->update(['status' => 0])
		;

		 //Update field for schedule_visit_types

		 $schedule_visit_types= \Models\ScheduleVisitType::whereIn('slug', ['InitialAssessment','ProgressReview'])
		->get();
		foreach($schedule_visit_types AS $schedule_visit_type){
			$checkValueEnable=\Models\TableExtension::getValue('schedule_visit_types', $schedule_visit_type->id,
			 'calculate_checkbox_updated');
			 if(!$checkValueEnable){
			\Models\ScheduleVisitType
			::where("id", "=", $schedule_visit_type->id)
			->update([
					"is_enable_days_since_last_review" => true
			]);
			\Models\TableExtension::updateField('schedule_visit_types', $schedule_visit_type->id, 'calculate_checkbox_updated', true);
		   }

		}

		// Get all lessons, check if they were created by event, if so, mark created_by_event flag as so.
		$all_lessons = \Models\LearningModule
			::where('is_course', 1)
			->whereNull('created_by_event')
			->with('ScheduleLessonLinks')
			->get()
		;

		foreach ($all_lessons as $key => $all_lesson) {
			$all_lesson->created_by_event = 0;
			foreach ($all_lesson->ScheduleLessonLinks as $key => $lesson_link) {
				$difference = \Carbon\Carbon::parse($lesson_link->created_at)->diffInMinutes(\Carbon\Carbon::parse($all_lesson->created_at));
				if ($difference < 2) {
					$all_lesson->created_by_event = 1;
				}
			}
			$all_lesson->save();
		}

		// update missing user_id field for email_history table
		$missing_user_ids = \Models\EmailHistory
			::whereNull('user_id')
			->get()
		;
		foreach ($missing_user_ids as $key => $missing_user_id) {
			$user_sent_to = \Models\User::where('email', trim($missing_user_id->email_to))->first();
			if ($user_sent_to) {
				$missing_user_id->user_id = $user_sent_to->id;
				$missing_user_id->name_to = $user_sent_to->fname . ' ' . $user_sent_to->lname;
				$missing_user_id->save();
			}
		}

		// hash all entries in email_history table
		$unhashed_sent_emails = \Models\EmailHistory
			::where('data_hash', '')
			->get()
		;
		foreach ($unhashed_sent_emails as $key => $unhashed_sent_email) {
			$unhashed_sent_email->data_hash = md5(trim($unhashed_sent_email->email_to) . $unhashed_sent_email->subject . $unhashed_sent_email->body);
			$unhashed_sent_email->timestamps = false;
			$unhashed_sent_email->save();
		}


		// Add missing permissions to roles, only add if entry is not found.
		$missing_permissions = [
			'custom-report-data' => [ // structure key
				[ // starts here
					'role_lookup' => [ // specify role settings you want
						'is_manager' => true,
						'is_admin' => false,
						'is_learner' => false,
					],
					'role_permissions' => ['select'], // and structure role permission that needs to be enabled
				], // ends here, replicate from start to end with different roles if needed
			],
		];
		foreach ($missing_permissions as $missing_permissions_key => $missing_permission) {
			foreach ($missing_permission as $missing_permission_role_key => $missing_permission_role) {
				$roles = \Models\Role
					::where('status', true)
				;
				foreach ($missing_permission_role['role_lookup'] as $role_lookup_key => $role_lookup) {
					$roles = $roles
						->where($role_lookup_key, $role_lookup)
					;
				}
				$roles = $roles
					->get()
				;
				foreach ($roles as $role_key => $role) {
					$structure = \Models\Structure
						::where('key', $missing_permissions_key)
						->first()
					;
					if ($structure) {
						$role_structure_entry = \Models\RoleStructure
							::where('structure_id', $structure->id)
							->where('role_id', $role->id)
							->first()
						;
						// If entry is not found, no one has removed it then, add it!
						if (!$role_structure_entry) {
							$role_structure_entry = new \Models\RoleStructure;
							$role_structure_entry->structure_id = $structure->id;
							$role_structure_entry->role_id = $role->id;
							foreach ($missing_permission_role['role_permissions'] as $role_permission_key => $role_permission) {
								$role_structure_entry->{$role_permission} = 1;
							}
							$role_structure_entry->save();
						}
					}
				}
			}
		}

		// loop batch_report_data and move data field into document and null it.
		$batch_report_entries = \Models\BatchReportData
			::whereNull('file_name')
			->whereNotNull('data')
			->with('BatchReport')
			->whereHas('BatchReport')
			->get()
		;
		foreach ($batch_report_entries as $key => $batch_report_entry) {
			$batch_report_entry->file_name = \APP\Tools::safeName($batch_report_entry->BatchReport->title, '_') . '_' . \Carbon\Carbon::parse($batch_report_entry->created_at)->format('d_m_Y__H_i_s') . '.json.gz';
			file_put_contents($GLOBALS["CONFIG"]->LMSBatchReportData . $batch_report_entry->file_name, $batch_report_entry->data);
			$batch_report_entry->data = null;
			$batch_report_entry->save();
		}


		$schedule_links = \Models\ScheduleLink
			::withTrashed()
			->whereIn('completion_status', ['Completed', 'In Progress', 'Not Attempted'])
			->get()
		;
		foreach ($schedule_links as $key => $schedule_link) {
			$schedule_link->timestamps = false;
			if ($schedule_link->getOriginal('completion_status') == 'Completed') {
				$schedule_link->completion_status = '%%event_completion_state_completed%%';
				$schedule_link->saveWithoutEvents();
			} elseif ($schedule_link->getOriginal('completion_status') == 'In Progress') {
				$schedule_link->completion_status = '%%event_completion_state_in_progress%%';
				$schedule_link->saveWithoutEvents();
			} elseif ($schedule_link->getOriginal('completion_status') == 'Not Attempted') {
				$schedule_link->completion_status = '%%event_completion_state_not_attempted%%';
				$schedule_link->saveWithoutEvents();
			}
		}

		// loop form_logs and move form_field field into document and null it.
		$form_log_entries = \Models\FormLog
			::whereNull('form_field_file')
			->whereNotNull('form_field')
			->get()
		;
		foreach ($form_log_entries as $key => $form_log_entry) {
			$form_log_entry->form_field_file = 'form_log_' . $form_log_entry->id . '.json';
			file_put_contents($GLOBALS["CONFIG"]->LMSFormLogsData . $form_log_entry->form_field_file, $form_log_entry->form_field);
			$form_log_entry->form_field = null;
			$form_log_entry->save();
		}


		$scormids = \Models\Scorm\Track::where('element', 'cmi.interactions_0.type')
			->where('value', 'likert')
			->distinct()
			->pluck('scormid'); // only retrieves the values as an array, much lighter

		\Models\LearningModule::whereIn('id', $scormids)->update(['is_survey' => true]);

		// convert free text watch field into related table
		$users_watch = \Models\User
			::where('watch', '>', '')
			->whereNull('watch_id')
			->get()
		;
		foreach ($users_watch as $key => $user_watch) {
			$watch = \Models\Watch
				::where('name', $user_watch->watch)
				->where('status', true)
				->first()
			;
			if (!$watch) {
				$watch = new \Models\Watch;
				$watch->name = $user_watch->watch;
				$watch->status = true;
				$watch->save();
			}
			$user_watch->watch_id = $watch->id;
			$user_watch->timestamps = false;
			$user_watch->saveWithoutEvents();
		}

		// Get all companies without slug/extension and create slug
		$company_slug_check = \Models\Company
			::where('slug', '')
			->get()
		;
		foreach ($company_slug_check as $key => $company) {
			$company->slug = \APP\Tools::safeName($company->name, '_', true) . '_' . uniqid();
			$company->save();
		}

		PowerBIController::powerBiSQLViewUpdate();


		$timne_email_convert_start = microtime(true);
		echo "\n  Email Body convert to text: ";

		$email_body_converted = 0;
		\Models\EmailHistory
			::where('converted', false)
			->chunkById(100, function ($emails) use (&$email_body_converted) {
				foreach ($emails as $email) {
					if ($email->body_text_only || !$email->body) {
						DB::table('email_history')
							->where('id', $email->id)
							->update([
								'converted' => true,
							])
						;
						continue;
					}

					$plain = \APP\Tools::stripEmailHtmlContent($email->body);

					DB::table('email_history')
						->where('id', $email->id)
						->update([
							'converted' => true,
							'body_text_only' => $plain,
						])
					;

					$email_body_converted++;
					if ($email_body_converted % 1000 === 0) {
						echo "Converted: $email_body_converted\n";
					}
				}
			})
		;
		echo "\n    Email history, body converted: $email_body_converted - ";
		$timne_email_convert_end = microtime(true);
		$timne_email_convert = number_format(($timne_email_convert_end - $timne_email_convert_start), 2);
		echo "({$timne_email_convert}s) \n";
	}



	/*
		======================================================================
	*/

	public function populateVersion()
	{
		$query = new \Models\Version;
		$query->save();
	}

	public function populateDashboards()
	{

		if (isset($this->settings) && isset($this->settings["licensing"])) {
			$stats = new \APP\Statistics(
				$this->settings["licensing"]["isApprentix"],
				$this->settings["licensing"]["isSMCR"]
			);
			$dts = new \APP\Dataviews(
				$this->settings["licensing"]["isApprentix"],
				$this->settings["licensing"]["isSMCR"]
			);
		} else {
			$stats = new \APP\Statistics(false, false);
			$dts = new \APP\Dataviews(false, false);
		}

		//Populate and dashboard statistics
		$stats->populate();

		//Set initial dates
		//Generate statistics history
		$stats->save_history();

		//Update dashboard statistics
		$stats->update();

		//Populate and update dashboard data views
		$dts->populate();
		$dts->update();
	}

	public function populateDefaultCertificateData()
	{
		$templateContent = file_get_contents($this->settings["LMSTplsPath"].'/html/certificate-template-default.html');

		if(empty($templateContent)) {
			echo "\n";
			echo "Error : Certificate Template not found";
			throw new \Exception('Certificate Template not found');
		}

		// checking if there is default template exists - if no create template
		$defaultTemplate = Certificate::query()->where('default', '=', Certificate::DEFAULT_TEMPLATE)->first();
		if(!$defaultTemplate) {
			Certificate::query()->create([
				'name' => 'Default Certificate',
				'template' => $templateContent,
				'status' => Certificate::STATUS_IS_ENABLED,
				'default' => Certificate::DEFAULT_TEMPLATE,
			]);
		}
	}

	public function __construct()
	{
		DB::connection()->getDoctrineSchemaManager()->getDatabasePlatform()->registerDoctrineTypeMapping('enum', 'string');
  }
  public function createApprentixApprenticeReportsView()
{
    $llddCatChoices = $GLOBALS["CONFIG"]->ilr_fields['LLDDandHealthProblem']['children']['LLDDCat']['choices'];
    $llddPrimaryChoice = addslashes($GLOBALS["CONFIG"]->ilr_fields['LLDDandHealthProblem']['children']['PrimaryLLDD']['choice']['name']);
    $llddCatMapping = [];
    foreach ($llddCatChoices as $choice) {
        $llddCatMapping[$choice['value']] = addslashes($choice['name']);
    }

    // Constructing CASE statements for each LLDD category
    $llddCaseStatements = [];
    foreach ($llddCatMapping as $value => $name) {
        $llddCaseStatements[] = "WHEN jt.LLDDCat = '$value' THEN '$name'";
    }
    $llddCase = implode("\n", $llddCaseStatements);

    // SQL query
    $query = "CREATE OR REPLACE VIEW apprentixapprenticereports AS
        SELECT
            `apprenticeship_standards_users`.`id`,
            `users`.`id` AS `user_id`,
            `usercode`,
            users.email,
            `fname`,
            `lname`,
            users.company_id,
            users.department_id,
            `designation_id`,
            `city_id`,
            `country_id`,
            `location_id`,
            `role_id`,
            `standard_id`,
            `apprenticeship_standards_users`.`status`,
            `apprenticeship_standards_users`.`completed_at`,
            `apprenticeship_standards_users`.`completion_status`,
            `apprenticeship_standards_users`.`percentage`,
            `apprenticeship_standards_users`.`percentage_time`,
            `apprenticeship_standards_users`.`criteria_completion`,
            `apprenticeship_standards_users`.`percentage_behind`,
            `apprenticeship_standards_users`.`expected_time`,
            `apprenticeship_standards_users`.`time_spent`,
            `apprenticeship_standards_users`.`time_behind`,
            `apprenticeship_standards_users`.`end_at_calculated`,
            `apprenticeship_standards_users`.`start_at`,
            `apprenticeship_standards_users`.`due_at`,
            `apprenticeship_standards_users`.`created_at`,
            apprenticeship_standards.name AS standard_name,
            apprenticeship_standards.level,
            ethnicities.name as ethnicity,
            companies.name as company,
            departments.name as department,
            CONCAT(fname, ' ', lname) AS full_name,
            CONCAT(FLOOR(apprenticeship_standards_users.time_spent), 'h, ', FLOOR((apprenticeship_standards_users.time_spent - FLOOR(apprenticeship_standards_users.time_spent)) * 60), 'min') AS time_spent_human_readable,
            CONCAT(FLOOR(apprenticeship_standards_users.time_behind), 'h, ', FLOOR((apprenticeship_standards_users.time_behind - FLOOR(apprenticeship_standards_users.time_behind)) * 60), 'min') AS time_behind_human_readable,
            CONCAT(expected_time, '%') AS expected_time_p,
            CONCAT(percentage, '%') AS percentage_p,
            CONCAT(criteria_completion, '%') AS criteria_completion_p,
            CONCAT(percentage_time, '%') AS percentage_time_p,
            CASE
                WHEN expected_time >= 100 THEN 'early'
                WHEN expected_time >= 50 AND expected_time <= 99 THEN 'on time'
                WHEN expected_time < 50 THEN 'late'
                ELSE NULL
            END AS time_status,
            CASE
                WHEN apprenticeship_standards.completion_months = 0 THEN 0
                ELSE apprenticeship_standards.funding / apprenticeship_standards.completion_months
            END AS monthly_income,
            DATEDIFF(NOW(), apprenticeship_standards_users.last_update) AS last_update_days,
            (NOW() > apprenticeship_standards_users.end_at_calculated) AS is_past_end_at,
            DATE_ADD(apprenticeship_standards_users.start_at, INTERVAL apprenticeship_standards.completion_months MONTH) AS finish_at,
            CASE
                LLDDHealthProb
                WHEN 1 THEN 'Learner considers himself or herself to have a learning difficulty and/or disability and/or health problem.'
                WHEN 2 THEN 'Learner does not consider himself or herself to have a learning difficulty and/or disability and/or health problem.'
                WHEN 9 THEN 'No information provided by the learner.'
                ELSE ''
            END AS disability,
            CASE
                WHEN TIMESTAMPDIFF(YEAR, DateOfBirth, CURDATE()) BETWEEN 16 AND 18 THEN '16-18'
                WHEN TIMESTAMPDIFF(YEAR, DateOfBirth, CURDATE()) BETWEEN 19 AND 25 THEN '19-25'
                WHEN TIMESTAMPDIFF(YEAR, DateOfBirth, CURDATE()) > 25 THEN '25+'
                ELSE ''
            END AS age_group,
            `learning_module_categories`.`name` AS `category`,
            (SELECT GROUP_CONCAT(
                CASE
                    $llddCase
                    WHEN jt.PrimaryLLDD = 1 THEN CONCAT('$llddPrimaryChoice')
                    ELSE ''
                END
                SEPARATOR ', '
            )
            FROM JSON_TABLE(users.LLDDandHealthProblem, '$[*]' COLUMNS(LLDDCat VARCHAR(255) PATH '$.LLDDCat', PrimaryLLDD INT PATH '$.PrimaryLLDD')) AS jt
    WHERE jt.LLDDCat IS NOT NULL) AS disability_categories,
    (SELECT GROUP_CONCAT(CONCAT(m.fname, ' ', m.lname) SEPARATOR ', ')
     FROM manager_users AS mu
     INNER JOIN users AS m ON mu.manager_id = m.id
     WHERE mu.user_id = apprenticeship_standards_users.user_id
     AND mu.deleted_at IS NULL
    ) AS manager_name

        FROM
            `apprenticeship_standards_users`
        INNER JOIN
            `users` ON `apprenticeship_standards_users`.`user_id` = `users`.`id`
            AND `users`.`status` = '1'
            AND `users`.`exclude_from_reports` = ''
        INNER JOIN
            `apprenticeship_standards` ON `apprenticeship_standards`.`id` = `apprenticeship_standards_users`.`standard_id`
        INNER JOIN
            `learning_module_categories` ON `learning_module_categories`.`id` = `apprenticeship_standards`.`category_id`
        LEFT JOIN companies
            ON companies.id = users.company_id
        LEFT JOIN departments
            ON departments.id = users.department_id
        LEFT JOIN ethnicities
            ON ethnicities.id = users.Ethnicity
        WHERE
            `apprenticeship_standards_users`.`deleted_at` IS NULL;";

    DB::statement($query);
}
	public function createLearningReportsView()
	{
		$days = Tools::getConfig('initialSkillDueDays');
		if (!$days) {
			$days = 30;
		}
		$queryBuilderCondition = Tools::getConfig('powerBIResourceQueryBuilder');
		if ($queryBuilderCondition) {
			$sql = " CREATE OR REPLACE VIEW learningreports AS
    SELECT
        lr.id,
        DATE(lr.completed_at) AS completed_date,
        lr.completion_status,
        lr.score,
        lr.created_at,
        lr.learning_module_id,
        lr.user_id,
        lm.is_course,
        lm.is_skill,
        lm.name AS module_name,
    lm.repetition_period,
        CASE
            WHEN lr.completion_status = 'completed' THEN ''
            ELSE DATEDIFF(NOW(), lr.due_at)
        END AS is_due,
        DATE_FORMAT(lr.due_at, '%d-%m-%Y') AS formatted_due_date,
        CASE
            WHEN lr.completion_status = 'completed' THEN DATE_FORMAT(lr.due_at, '%d-%m-%Y')
            ELSE ''
        END AS formatted_refreshed_date,
        (CASE
            WHEN lr.completion_status = 'completed'
            THEN (SELECT DATE(due_at)
                  FROM learning_results AS lr_sub
                  WHERE lr_sub.learning_module_id = lr.learning_module_id
                    AND lr_sub.refreshed = 1
                    AND lr_sub.user_id = lr.user_id
                  ORDER BY lr_sub.due_at DESC
                  LIMIT 1)
            ELSE DATE(lr.due_at)
        END) AS due_date,
        CASE
            WHEN lr.completed_at IS NOT NULL
            AND lm.refresh = 1
            AND lm.refresh_period IS NOT NULL
            THEN DATE(DATE_ADD(lr.completed_at, INTERVAL lm.refresh_period DAY))
            ELSE NULL
        END AS refreshed_at,
        CASE
            WHEN lr.completed_at IS NOT NULL
            AND lm.refresh = 1
            AND lm.refresh_period IS NOT NULL
            THEN
                CASE
                    WHEN DATE(DATE_ADD(lr.completed_at, INTERVAL lm.refresh_period DAY)) <= DATE_ADD(CURDATE(), INTERVAL 90 DAY)
                    THEN 1
                    ELSE 0
                END
            ELSE 0
        END AS is_renewal_available,
        CONCAT(u.fname, ' ', u.lname) AS full_name,
        lmc.name AS category,
        d.name AS department,
        c.name AS country,
        ci.name AS city,
        comp.name AS company,
        desig.name AS designation,
        -- Total employees
        (SELECT COUNT(*)
         FROM learning_results AS lr_total
         WHERE lr_total.learning_module_id = lr.learning_module_id
           AND lr_total.user_id = lr.user_id
           AND lr_total.refreshed = 0
           AND lr_total.deleted_at IS NULL) AS total_employees,
        -- Compliant employees
        (SELECT COUNT(*)
         FROM learning_results AS lr_compliant
         WHERE lr_compliant.learning_module_id = lr.learning_module_id
           AND lr_compliant.user_id = lr.user_id
           AND lr_compliant.completion_status = 'completed'
           AND lr_compliant.refreshed = 0
           AND lr_compliant.deleted_at IS NULL) AS compliant_employees,
        -- Compliance ratio as percentage
        (SELECT
            100 * COUNT(CASE WHEN lr_comp.completion_status = 'completed' THEN 1 ELSE NULL END)
            / COUNT(*)
         FROM learning_results AS lr_comp
         WHERE lr_comp.learning_module_id = lr.learning_module_id
           AND lr_comp.user_id = lr.user_id
           AND lr_comp.refreshed = 0
           AND lr_comp.deleted_at IS NULL
        ) AS compliance_ratio_percentage,
        -- Managers full names separated by commas
        (SELECT GROUP_CONCAT(CONCAT(m.fname, ' ', m.lname) SEPARATOR ', ')
         FROM manager_users AS mu
         INNER JOIN users AS m ON mu.manager_id = m.id
         WHERE mu.user_id = lr.user_id
         AND mu.deleted_at IS NULL
        ) AS manager_fullname,
        (SELECT
        GROUP_CONCAT(d.name SEPARATOR ', ')
     FROM user_sub_departments usd
     INNER JOIN departments d ON usd.department_id = d.id
     WHERE usd.user_id = u.id
       AND usd.deleted_at IS NULL
        ) AS sub_department_names,
        learning_comp.name AS learning_company,
        -- Additional condition to check resource_queries
        CASE
            WHEN rq.id IS NOT NULL THEN 1
            ELSE 0
        END AS is_resource_related,
	(select lrr.due_at from learning_results as lrr where lrr.learning_module_id=lr.learning_module_id and lrr.user_id=lr.user_id and lrr.refreshed=1 ORDER BY lrr.due_at DESC LIMIT 1) as previous_due_date,
	(select lrr.created_at from learning_results as lrr where lrr.learning_module_id=lr.learning_module_id and lrr.user_id=lr.user_id and lrr.refreshed=1 ORDER BY lrr.created_at DESC LIMIT 1) as previous_created_at,
	(select lrr.sign_off_manager_at from learning_results as lrr where lrr.learning_module_id= lr.learning_module_id and lrr.user_id=lr.user_id and lrr.refreshed=1 ORDER BY lrr.created_at DESC LIMIT 1) as previous_completed_at,
	(SELECT COUNT(*) FROM learning_results AS lrr WHERE lrr.learning_module_id = lr.learning_module_id AND lrr.user_id = lr.user_id AND lrr.refreshed = 1) AS refreshed_count,
	lr.sign_off_manager,
	lr.sign_off_manager_at,
	$days as initialSkillDueDays

    FROM learning_results AS lr
    INNER JOIN learning_modules AS lm ON lr.learning_module_id = lm.id
    INNER JOIN users AS u ON lr.user_id = u.id
    LEFT JOIN departments AS d ON d.id = u.department_id
    LEFT JOIN countries AS c ON c.id = u.country_id
    LEFT JOIN cities AS ci ON ci.id = u.city_id
    LEFT JOIN companies AS comp ON comp.id = u.company_id
    LEFT JOIN companies AS learning_comp ON learning_comp.id = lm.company_id
    LEFT JOIN designations AS desig ON desig.id = u.designation_id
    INNER JOIN learning_module_categories AS lmc ON lm.category_id = lmc.id
    INNER JOIN resource_queries AS rq ON rq.type_id = lm.id
      AND (rq.type = 'lessons' OR rq.type = 'resources')
      AND rq.deleted_at IS NULL
        AND FIND_IN_SET(lr.user_id, rq.user_ids) > 0
    WHERE EXISTS (
        SELECT 1
        FROM user_learning_modules
        WHERE user_learning_modules.user_id = lr.user_id
          AND user_learning_modules.learning_module_id = lr.learning_module_id
          AND user_learning_modules.deleted_at IS NULL
          AND EXISTS (
              SELECT 1
              FROM users
              WHERE users.id = user_learning_modules.user_id
                AND users.status = 1
                AND users.exclude_from_reports = 0
          )
          AND EXISTS (
              SELECT 1
              FROM learning_modules
              WHERE learning_modules.id = user_learning_modules.learning_module_id
                AND learning_modules.deleted_at IS NULL
                AND learning_modules.status = 1
                AND (
                    learning_modules.expiration_date IS NULL
                    OR learning_modules.expiration_date > NOW()
                )
          )
    )
    AND lr.refreshed = 0
    AND lr.deleted_at IS NULL;";
		} else {
			$sql = "
    CREATE OR REPLACE VIEW learningreports AS
SELECT
    lr.id,
    DATE(lr.completed_at) AS completed_date,
    lr.completion_status,
    lr.score,
    lr.created_at,
    lr.learning_module_id,
    lr.user_id,
    lm.is_course,
    lm.is_skill,
    lm.name AS module_name,
    lm.repetition_period,
    CASE
        WHEN lr.completion_status = 'completed' THEN ''
        ELSE DATEDIFF(NOW(), lr.due_at)
    END AS is_due,
    DATE_FORMAT(lr.due_at, '%d-%m-%Y') AS formatted_due_date,
          CASE
        WHEN lr.completion_status = 'completed' THEN DATE_FORMAT(lr.due_at, '%d-%m-%Y')
        ELSE ''
    END AS formatted_refreshed_date,
    (CASE
        WHEN lr.completion_status = 'completed'
        THEN (SELECT DATE(due_at)
              FROM learning_results AS lr_sub
              WHERE lr_sub.learning_module_id = lr.learning_module_id
                AND lr_sub.refreshed = 1
                AND lr_sub.user_id = lr.user_id
              ORDER BY lr_sub.due_at DESC
              LIMIT 1)
        ELSE DATE(lr.due_at)
    END) AS due_date,
    CASE
        WHEN lr.completed_at IS NOT NULL
        AND lm.refresh = 1
        AND lm.refresh_period IS NOT NULL
        THEN DATE(DATE_ADD(lr.completed_at, INTERVAL lm.refresh_period DAY))
        ELSE NULL
    END AS refreshed_at,
    CASE
        WHEN lr.completed_at IS NOT NULL
        AND lm.refresh = 1
        AND lm.refresh_period IS NOT NULL
        THEN
            CASE
                WHEN DATE(DATE_ADD(lr.completed_at, INTERVAL lm.refresh_period DAY)) <= DATE_ADD(CURDATE(), INTERVAL 90 DAY)
                THEN 1
                ELSE 0
            END
        ELSE 0
    END AS is_renewal_available,
    CONCAT(u.fname, ' ', u.lname) AS full_name,
    lmc.name AS category,
    d.name AS department,
    c.name AS country,
    ci.name AS city,
    comp.name AS company,
    desig.name AS designation,
    -- Total employees
    (SELECT COUNT(*)
     FROM learning_results AS lr_total
     WHERE lr_total.learning_module_id = lr.learning_module_id
       AND lr_total.user_id = lr.user_id
       AND lr_total.refreshed = 0
       AND lr_total.deleted_at IS NULL) AS total_employees,
    -- Compliant employees
    (SELECT COUNT(*)
     FROM learning_results AS lr_compliant
     WHERE lr_compliant.learning_module_id = lr.learning_module_id
       AND lr_compliant.user_id = lr.user_id
       AND lr_compliant.completion_status = 'completed'
       AND lr_compliant.refreshed = 0
       AND lr_compliant.deleted_at IS NULL) AS compliant_employees,
    -- Compliance ratio as percentage
    (SELECT
        100 * COUNT(CASE WHEN lr_comp.completion_status = 'completed' THEN 1 ELSE NULL END)
        / COUNT(*)
     FROM learning_results AS lr_comp
     WHERE lr_comp.learning_module_id = lr.learning_module_id
       AND lr_comp.user_id = lr.user_id
       AND lr_comp.refreshed = 0
       AND lr_comp.deleted_at IS NULL
    ) AS compliance_ratio_percentage,
    -- Managers full names separated by commas
    (SELECT GROUP_CONCAT(CONCAT(m.fname, ' ', m.lname) SEPARATOR ', ')
     FROM manager_users AS mu
     INNER JOIN users AS m ON mu.manager_id = m.id
     WHERE mu.user_id = lr.user_id
        AND mu.deleted_at IS NULL
    ) AS manager_fullname,
    (SELECT
    GROUP_CONCAT(d.name SEPARATOR ', ')
 FROM user_sub_departments usd
 INNER JOIN departments d ON usd.department_id = d.id
 WHERE usd.user_id = u.id
   AND usd.deleted_at IS NULL
    ) AS sub_department_names,
learning_comp.name AS learning_company,
(select lrr.due_at from learning_results as lrr where lrr.learning_module_id=lr.learning_module_id and lrr.user_id=lr.user_id and lrr.refreshed=1 ORDER BY lrr.due_at DESC LIMIT 1) as previous_due_date,
(select lrr.created_at from learning_results as lrr where lrr.learning_module_id=lr.learning_module_id and lrr.user_id=lr.user_id and lrr.refreshed=1 ORDER BY lrr.created_at DESC LIMIT 1) as previous_created_at,
(select lrr.sign_off_manager_at from learning_results as lrr where lrr.learning_module_id= lr.learning_module_id and lrr.user_id=lr.user_id and lrr.refreshed=1 ORDER BY lrr.created_at DESC LIMIT 1) as previous_completed_at,
(SELECT COUNT(*) FROM learning_results AS lrr WHERE lrr.learning_module_id = lr.learning_module_id AND lrr.user_id = lr.user_id AND lrr.refreshed = 1) AS refreshed_count,
lr.sign_off_manager,
lr.sign_off_manager_at,
$days as initialSkillDueDays
FROM learning_results AS lr
INNER JOIN learning_modules AS lm ON lr.learning_module_id = lm.id
INNER JOIN users AS u ON lr.user_id = u.id and u.status =1
LEFT JOIN departments AS d ON d.id = u.department_id
LEFT JOIN countries AS c ON c.id = u.country_id
LEFT JOIN cities AS ci ON ci.id = u.city_id
LEFT JOIN companies AS comp ON comp.id = u.company_id
LEFT JOIN companies as learning_comp ON learning_comp.id = lm.company_id
LEFT JOIN designations AS desig ON desig.id = u.designation_id
INNER JOIN learning_module_categories AS lmc ON lm.category_id = lmc.id
WHERE EXISTS (
    SELECT 1
    FROM user_learning_modules
    WHERE user_learning_modules.user_id = lr.user_id
      AND user_learning_modules.learning_module_id = lr.learning_module_id
      AND user_learning_modules.deleted_at IS NULL
      AND EXISTS (
          SELECT 1
          FROM users
          WHERE users.id = user_learning_modules.user_id
            AND users.status = 1
            AND users.exclude_from_reports = 0
      )
      AND EXISTS (
          SELECT 1
          FROM learning_modules
          WHERE learning_modules.id = user_learning_modules.learning_module_id
            AND learning_modules.deleted_at IS NULL
            AND learning_modules.status = 1
            AND (
                learning_modules.expiration_date IS NULL
                OR learning_modules.expiration_date > NOW()
            )
      )
)
AND lr.refreshed = 0
AND lr.deleted_at IS NULL AND u.status = 1 AND u.exclude_from_reports = 0;
				";
		}
		DB::statement($sql);
	}
  public function createEventView()
  {
    $sql = "CREATE OR REPLACE VIEW `user_event_list` AS
SELECT
    users.id AS user_id,
    user_links.id,
    CASE
        WHEN user_links.completion_status = '%%event_completion_state_completed%%' THEN 'Attended'
        WHEN user_links.completion_status = '%%event_completion_state_not_attempted%%' THEN 'Not Attended'
        WHEN user_links.completion_status = '%%event_completion_state_in_progress%%' THEN 'In-progress'
        ELSE user_links.completion_status
    END AS completion_status,
    DATE(user_links.completed_at) AS completed_at,
    learning_modules.name AS lesson_name,
    CONCAT(users.fname, ' ', users.lname) AS full_name,
    DATE(
        CASE
            WHEN user_links.completed_at IS NOT NULL
                 AND learning_modules.refresh = 1
                 AND learning_modules.refresh_period IS NOT NULL
            THEN DATE_ADD(user_links.completed_at, INTERVAL learning_modules.refresh_period DAY)
            ELSE NULL
        END
    ) AS refreshed_at
FROM
    `schedule_links` AS `user_links`
INNER JOIN
    `users`
    ON `user_links`.`link_id` = `users`.`id`
INNER JOIN
    `schedule_links` AS `lesson_links`
    ON `user_links`.`schedule_id` = `lesson_links`.`schedule_id`
INNER JOIN
    `learning_modules`
    ON `lesson_links`.`link_id` = `learning_modules`.`id`
WHERE
    `user_links`.`type` = 'users'
     AND `lesson_links`.`type` = 'lesson'
AND users.status = 1 AND users.exclude_from_reports=0
ORDER BY
    `user_links`.`id` ASC;";
    DB::statement($sql);
  }
  public function createFormAwaitingView()
  {
    $sql = "CREATE OR REPLACE VIEW `forms_awaiting_completion` AS
SELECT
    user_forms.id AS user_form_id,
    user_forms.*,
    forms.name,
    forms.name AS form_name,
    user_form_signoff.created_at AS submit_date,
    user_form_signoff.signoff_at AS signoff_date,
    user_form_signoff.status AS submit_status,
    user_forms.user_form_status AS user_current_status,
    departments.name AS department_name,
    CONCAT(users.fname, ' ', users.lname) AS trainee_name,
    (
        SELECT
            GROUP_CONCAT(roles.name SEPARATOR ', ')
        FROM
            form_signoff_roles
        INNER JOIN
            roles
        ON
            form_signoff_roles.role_id = roles.id
        WHERE
            form_signoff_roles.form_id = forms.id
    ) AS role_names
FROM
    `user_forms`
LEFT JOIN
    `forms`
    ON `forms`.`id` = `user_forms`.`form_id`
LEFT JOIN
    `users`
    ON `users`.`id` = `user_forms`.`user_id`
LEFT JOIN
    `user_form_signoff`
    ON `user_form_signoff`.`user_id` = `users`.`id`
    AND `user_form_signoff`.`user_form_id` = `user_forms`.`id`
LEFT JOIN
    `departments`
    ON `departments`.`id` = `users`.`department_id`
WHERE
    (
        (
            `user_forms`.`user_form_status` = 'Not Started'
            OR `user_forms`.`user_form_status` = 'In Progress'
            OR `user_forms`.`user_form_status` = 'Awaiting Sign-off'
            OR `user_forms`.`user_form_status` = 'Completed'
        )
        OR
        (
            `has_sign_off` = 0
            AND `user_forms`.`user_form_status` = 'Completed'
        )
    )
    AND `user_forms`.`deleted_at` IS NULL AND users.status = 1 AND users.exclude_from_reports=0
GROUP BY
    `user_forms`.`id`;";
    DB::statement($sql);
  }
  public function createIncidentCommandFormView()
{
    $query = "CREATE OR REPLACE VIEW incident_command_form_view_custom AS
    SELECT
        `uf`.`id` AS `user_form_id`,
        `uf`.`user_id` AS `user_id`,
        IFNULL(`uv`.`updated_at`, '') AS `updated_at`,
        MAX(CASE WHEN IFNULL(`uv`.`slug`, `f`.`slug`) = 'Attributedcommandhoursinminutesmax2hours' THEN IFNULL(`uv`.`value`, '') END) AS `Attributedcommandhoursinminutesmax2hours`,
        MAX(CASE WHEN IFNULL(`uv`.`slug`, `f`.`slug`) = 'EventType' THEN IFNULL(`uv`.`value`, '') END) AS `EventType`,
        MAX(CASE WHEN IFNULL(`uv`.`slug`, `f`.`slug`) = 'ICLevel' THEN IFNULL(`uv`.`value`, '') END) AS `ICLevel`,
        MAX(CASE WHEN IFNULL(`uv`.`slug`, `f`.`slug`) = 'IncidentDate' THEN IFNULL(`uv`.`value`, '') END) AS `IncidentDate`,
        MAX(CASE WHEN IFNULL(`uv`.`slug`, `f`.`slug`) = 'Incidentnumber' THEN IFNULL(`uv`.`value`, '') END) AS `Incidentnumber`,
        MAX(CASE WHEN IFNULL(`uv`.`slug`, `f`.`slug`) = 'Incidentstarttime' THEN IFNULL(`uv`.`value`, '') END) AS `Incidentstarttime`,
        MAX(CASE WHEN IFNULL(`uv`.`slug`, `f`.`slug`) = 'Rank' THEN IFNULL(`uv`.`value`, '') END) AS `Rank`,
        MAX(CASE WHEN IFNULL(`uv`.`slug`, `f`.`slug`) = 'RoleatincidentegSectorSafety' THEN IFNULL(`uv`.`value`, '') END) AS `RoleatincidentegSectorSafety`,
        MAX(CASE WHEN IFNULL(`uv`.`slug`, `f`.`slug`) = 'Selfreflectionegwhatwentwelllessonslearned' THEN IFNULL(`uv`.`value`, '') END) AS `Selfreflectionegwhatwentwelllessonslearned`,
        MAX(CASE WHEN IFNULL(`uv`.`slug`, `f`.`slug`) = 'Yourrole' THEN IFNULL(`uv`.`value`, '') END) AS `Yourrole`
    FROM
        (`user_forms` `uf`
        LEFT JOIN `user_form_values` `uv` ON (`uf`.`id` = `uv`.`user_form_id`))
	JOIN `fields` `f` ON (IFNULL(`uv`.`slug`, `f`.`slug`) = `f`.`slug`)
	JOIN users on users.id = uf.user_id
    WHERE
        `uf`.`form_id` IN (SELECT id FROM forms AS inci WHERE inci.parent_id = 17 OR inci.id = 17) AND  users.status = 1 AND users.exclude_from_reports=0
    GROUP BY
        `uf`.`id`";
    DB::statement($query);

    $query = "CREATE OR REPLACE VIEW incident_command_form_view AS
    SELECT
        `users`.`fname` AS `fname`,
        `users`.`lname` AS `lname`,
        `form_value_views`.`Rank` AS `custom_Rank`,
        `departments`.`name` AS `department_name`,
        `form_value_views`.`RoleatincidentegSectorSafety` AS `custom_RoleatincidentegSectorSafety`,
        `form_value_views`.`ICLevel` AS `custom_ICLevel`,
        `form_value_views`.`IncidentDate` AS `custom_IncidentDate`,
        `form_value_views`.`Incidentstarttime` AS `custom_Incidentstarttime`,
        `form_value_views`.`Incidentnumber` AS `custom_Incidentnumber`,
        `form_value_views`.`Attributedcommandhoursinminutesmax2hours` AS `custom_Attributedcommandhoursinminutesmax2hours`,
        `form_value_views`.`EventType` AS `custom_EventType`,
        `form_value_views`.`Selfreflectionegwhatwentwelllessonslearned` AS `custom_Selfreflectionegwhatwentwelllessonslearned`,
        `form_value_views`.`Yourrole` AS `custom_Yourrole`,
        `users`.`id` AS `user_id`
    FROM
        (((`user_forms`
        JOIN `users` `status_check` ON (`user_forms`.`user_id` = `status_check`.`id`))
        LEFT JOIN `users` ON (`user_forms`.`user_id` = `users`.`id`))
        JOIN `incident_command_form_view_custom` `form_value_views` ON (`user_forms`.`id` = `form_value_views`.`user_form_id`))
        LEFT JOIN `departments` ON (`users`.`department_id` = `departments`.`id`)
    WHERE
        `status_check`.`status` = '1' and status_check.exclude_from_reports=0
        AND `user_forms`.`deleted_at` IS NULL";
    DB::statement($query);
}
  public function generateViewFromQuery($query,$viewName){
    // Convert query to raw SQL
    $sql = $query->toSql();
    // Prepare the SQL to create or replace the view
    $createViewSQL = "CREATE OR REPLACE VIEW `{$viewName}` AS {$sql};";

    // Bind query parameters if there are bindings
    $bindings = $query->getBindings();
    foreach ($bindings as $key => $value) {
      $createViewSQL = preg_replace('/\?/', "'" . addslashes($value) . "'", $createViewSQL, 1);
    }

    // Execute the SQL to create the view
    DB::statement($createViewSQL);
  }
  public function  createManagerView(){
    $query = \APP\QueryBuilder\PowerBI\managers::generate('all', null, null);
    $this->generateViewFromQuery($query,'managers_view');
  }
  public  function createCustomProgrammeStatusView(){
    $query =  \APP\QueryBuilder\PowerBI\userCustomProgrammeStatus::generate('all', null, null);
    $this->generateViewFromQuery($query,'user_custom_programme_status_view');
  }
public function createUserView(){
		 $powerBIALNEventTypeSlugs = Tools::getConfig("powerBIALNEventTypeSlugs");

         //Check if the config is true if so then set the condition to empty, To skip the LLDDHealthProb condition
          $LLDDHealthProbCondition = Tools::getConfig("powerBIRemoveALNLLDDHealthProb") ? "" : 'AND lldd.name = "Learner considers himself or herself to have a learning difficulty and/or disability and/or health problem."';

		if(empty($powerBIALNEventTypeSlugs)){
			$powerBIALNEventTypeSlugs = '"ALNReviewVisit_b3f6e17b"';
		}else{
		$powerBIALNEventTypeSlugs =  array_map(function($item) {
    return '"' . $item . '"';
}, $powerBIALNEventTypeSlugs);
		$powerBIALNEventTypeSlugs = implode(',',$powerBIALNEventTypeSlugs);
		}
		$powerBILearnAimRef = Tools::getConfig('powerBILearnAimRef');
		// Decode JSON if $powerBILearnAimRef is not already an array
		if (!empty($powerBILearnAimRef) && is_string($powerBILearnAimRef)) {
			$powerBILearnAimRef = json_decode($powerBILearnAimRef, true);
		}
		if(!empty($powerBILearnAimRef)){
		$powerBILearnAimRefIDS = $names = array_map(function ($item) {
    return $item['name'];
		}, $powerBILearnAimRef);
			$powerBILearnAimRefIDS = '("' . implode('", "', $powerBILearnAimRefIDS) . '")';
		}else{
		if(empty($powerBILearnAimRefIDS)){
			$powerBILearnAimRefIDS = '("60346085", "60346061", "60348070", "60348082")';
			}
		}
		$sqlCase = "CASE\n";
if(empty($powerBILearnAimRef)){
			$sqlCase = "CASE\n    WHEN ild.LearnAimRef = \"60346085\" THEN \"60346085\"\n    WHEN ild.LearnAimRef = \"60346061\" THEN \"60346061\"\n    WHEN ild.LearnAimRef = \"60348070\" THEN \"60348070\"\n    WHEN ild.LearnAimRef = \"60348082\" THEN \"60348082\"\n    ELSE ild.LearnAimRef\nEND";
		}else{
foreach ($powerBILearnAimRef as $item) {
    $sqlCase .= "    WHEN ild.LearnAimRef = \"{$item['name']}\" THEN \"{$item['value']}\"\n";
}

			$sqlCase .= "    ELSE ild.LearnAimRef\nEND";
		}

    $query = \Models\User
            ::where("users.status", "=", 1)
            ->where('exclude_from_reports', 0)
            ->with('employeeStatus')
            ->with("Watch")
            ->with("country")
            ->with("city")
            ->with("company")
            ->with(['department' => function ($query) {
                $query->whereNull('departments.parent_id');
            }])
            ->with("designation")
            ->with("location")
            ->with("EthnicityObj");
    $query->selectRaw('
            users.*, users.time_spent / 60 as time_spent_hours,
            DATEDIFF(NOW(), last_login_dt) AS days_since_last_login,
            DATEDIFF(NOW(), last_contact_date) AS days_since_last_contact,

            (SELECT COUNT(user_forms.id)
                 FROM user_forms
                 WHERE user_forms.user_id = users.id
                   AND user_forms.deleted_at IS NULL
                   AND user_forms.status = 1
                   AND user_forms.user_form_status = "Awaiting Sign-off") AS awaiting_sign_off_forms_count,

            IFNULL(
                (SELECT DATEDIFF(NOW(), lr.completed_at)
                FROM learning_results lr
                JOIN lldd_health_problems lldd ON lldd.id = users.LLDDHealthProb
                JOIN schedule_links sl ON sl.link_id = users.id
                JOIN schedules s ON s.id = sl.schedule_id
                JOIN schedule_visit_types svt ON svt.id = s.visit_type_id
                WHERE lr.user_id = users.id
                AND lr.completion_status = "completed"
                '.$LLDDHealthProbCondition.'
                AND sl.type = "users"
                AND svt.slug IN ('.$powerBIALNEventTypeSlugs.')
                AND svt.status = 1
                ORDER BY lr.completed_at DESC
                LIMIT 1), 0) AS number_of_days_since_last_aln_visit,
            (SELECT ildcs.name
                FROM ilr_learning_deliveries ild
                JOIN ilr_learning_delivery_completion_status ildcs ON ildcs.id = ild.CompStatus
                WHERE ild.user_id = users.id
                AND ild.LearnAimRef IN '.$powerBILearnAimRefIDS.'
                AND ildcs.name IN ("Learner has temporarily withdrawn from the aim due to an agreed breakin learning", "The learner is continuing or intending to continue the learning activities leading to the learning aim")
                LIMIT 1) AS learning_end_completion_status,
            (SELECT llddh.name
                FROM ilr_learning_deliveries ild
                JOIN ilr_learning_delivery_completion_status ildcs ON ildcs.id = ild.CompStatus
                JOIN lldd_health_problems llddh ON llddh.id = users.LLDDHealthProb
                WHERE ild.user_id = users.id
                AND ild.LearnAimRef IN '.$powerBILearnAimRefIDS.'
                AND ildcs.name IN ("Learner has temporarily withdrawn from the aim due to an agreed breakin learning", "The learner is continuing or intending to continue the learning activities leading to the learning aim")
                LIMIT 1) AS lldd_health_problems,
            (SELECT
                 '.$sqlCase.' AS learning_aim_reference_number
                FROM ilr_learning_deliveries ild
                JOIN ilr_learning_delivery_completion_status ildcs ON ildcs.id = ild.CompStatus
                WHERE ild.user_id = users.id
                AND ild.LearnAimRef IN '.$powerBILearnAimRefIDS.'
                AND ildcs.name IN ("Learner has temporarily withdrawn from the aim due to an agreed breakin learning", "The learner is continuing or intending to continue the learning activities leading to the learning aim")
                LIMIT 1) AS learning_aim_reference_number,
            (SELECT COUNT(DISTINCT lr.id)
                    FROM learning_results lr
                    JOIN learning_modules lm ON lr.learning_module_id = lm.id
                    LEFT JOIN `learning_sessions` ON `learning_sessions`.`learning_module_id` = `lr`.`learning_module_id` AND `learning_sessions`.`user_id` = `lr`.`user_id` AND `learning_sessions`.`completed` = 0 AND `learning_sessions`.`approved` = 0
                    JOIN `user_learning_modules` ON `user_learning_modules`.`learning_module_id` = `lr`.`learning_module_id` AND `user_learning_modules`.`user_id` = `lr`.`user_id` AND `user_learning_modules`.`deleted_at` IS NULL
                    WHERE lr.user_id = users.id
                    AND lm.status = 1
                    AND lm.visible_learner = 1
                    AND lm.track_progress = 1
                    AND lr.refreshed = ""
                    AND lr.sign_off_trainee = 1
                    AND lr.sign_off_manager = 0
                    AND lr.`deleted_at` IS NULL
                    AND lm.`deleted_at` IS NULL
            ) AS number_of_resources_requiring_sign_off,
            (SELECT MAX(DATEDIFF(NOW(), lr.sign_off_trainee_at))
                    FROM learning_results lr
                    JOIN learning_modules lm ON lr.learning_module_id = lm.id
                    LEFT JOIN `learning_sessions` ON `learning_sessions`.`learning_module_id` = `lr`.`learning_module_id` AND `learning_sessions`.`user_id` = `lr`.`user_id` AND `learning_sessions`.`completed` = 0 AND `learning_sessions`.`approved` = 0
                    JOIN `user_learning_modules` ON `user_learning_modules`.`learning_module_id` = `lr`.`learning_module_id` AND `user_learning_modules`.`user_id` = `lr`.`user_id` AND `user_learning_modules`.`deleted_at` IS NULL
                    WHERE lr.user_id = users.id
                    AND lm.status = 1
                    AND lm.visible_learner = 1
                    AND lm.track_progress = 1
                    AND lr.refreshed = ""
                    AND lr.sign_off_trainee = 1
                    AND lr.sign_off_manager = 0
                    AND lr.`deleted_at` IS NULL
                    AND lm.`deleted_at` IS NULL
            ) AS days_since_submitted_work,
            cfv_employee_type.value AS employee_type,
            cfv_employee_status.value AS employee_status,
            cfv_last_hire_date.value AS date_of_hire
      ')
      ->leftJoin('custom_field_values as cfv_employee_type', function($join) {
          $join->on('cfv_employee_type.type_id', '=', 'users.id')
               ->where('cfv_employee_type.type', '=', 'user')
               ->join('fields as f_employee_type', 'f_employee_type.id', '=', 'cfv_employee_type.field_id')
               ->where('f_employee_type.slug', '=', 'employee_type');
      })
      ->leftJoin('custom_field_values as cfv_employee_status', function($join) {
          $join->on('cfv_employee_status.type_id', '=', 'users.id')
               ->where('cfv_employee_status.type', '=', 'user')
               ->join('fields as f_employee_status', 'f_employee_status.id', '=', 'cfv_employee_status.field_id')
               ->where('f_employee_status.slug', '=', 'employee_status');
      })
      ->leftJoin('custom_field_values as cfv_last_hire_date', function($join) {
          $join->on('cfv_last_hire_date.type_id', '=', 'users.id')
               ->where('cfv_last_hire_date.type', '=', 'user')
               ->join('fields as f_last_hire_date', 'f_last_hire_date.id', '=', 'cfv_last_hire_date.field_id')
               ->where('f_last_hire_date.slug', '=', 'last_hire_date');
      });
    $this->generateViewFromQuery($query, 'usersview');
}
}
