<?php

namespace DB;

use Illuminate\Support\Str;
use Models\Field as ModelsField;
use Models\FieldOption;
use Models\Role;

class Field
{
    public  static  function update()
    {
        $translator = new \App\TemplateTranslator();
        $form_type_fields = [
            // user
            ["id" => 1, 'name' => 'Employee ID', 'slug' => "usercode", 'type' => 'text', 'field_category_id' => 1, "option_group" => "Standard"], // usercode
            ["id" => 2, 'name' => 'Alternative ID', 'slug' => "altusercode", 'type' => 'text', 'field_category_id' => 1, "option_group" => "Standard"], //altusercode
            ["id" => 3, 'name' => 'First Name', 'slug' => 'fname', 'type' => 'text', 'field_category_id' => 1, "option_group" => "Standard"], // fname
            ["id" => 4, 'name' => 'Last Name', 'slug' => 'lname', 'type' => 'text', 'field_category_id' => 1, "option_group" => "Standard"], // lname
            ["id" => 5, 'name' => 'Contact Email', 'slug' => 'email', 'type' => 'email', 'field_category_id' => 1, "option_group" => "Standard"], // email
            ["id" => 6, 'name' => 'Company', 'slug' => 'company_id', 'type' => 'picklist', 'field_category_id' => 1, "option_group" => "Standard", "model" => 'Models\Company', "value" => "id", "label" => "name"], // company_id (DDL)
            ["id" => 7, 'name' => 'Department', 'slug' => 'department_id', 'type' => 'selectbox', 'field_category_id' => 1, "option_group" => "Standard", "model" => 'Models\Department', "value" => "id", "label" => "name"], // department_id (DDL)
            ["id" => 8, 'name' => 'Job', 'slug' => 'job_id', 'type' => 'number', 'field_category_id' => 1, "option_group" => "Standard"], // designation_id (DDL)
            ["id" => 9, 'name' => 'Location', 'slug' => "location_id", 'type' => 'text', 'field_category_id' => 1, "option_group" => "Standard"], // location_id (DDL)
            ["id" => 10, 'name' => 'Groups', 'slug' => 'group_id', 'type' => 'picklist', 'field_category_id' => 1, "option_group" => "Standard", "model" => 'Models\Group', "value" => "id", "label" => "name"], // (Multi-Select DDL)
            ["id" => 11, 'name' => 'Skype ID', 'slug' => "skype_id", 'type' => 'text', 'field_category_id' => 1, "option_group" => "Standard"], // skype_id
            ["id" => 12, 'name' => 'Zoom Personal Meeting URL', 'slug' => 'zoom_id', 'type' => 'text', 'field_category_id' => 1, "option_group" => "Standard"], // zoom_id
            ["id" => 13, 'name' => 'Teams Private Meeting URL', 'slug' => 'teams_id', 'type' => 'text', 'field_category_id' => 1, "option_group" => "Standard"], // teams_id
            ["id" => 14, 'name' => 'Image', 'slug' => 'image', 'type' => 'file_image', 'field_category_id' => 1, "option_group" => "Standard"], // image
            ["id" => 15, 'name' => 'Phone', 'slug' => 'phone', 'type' => 'text', 'field_category_id' => 1, "option_group" => "Standard"], // phone
            ["id" => 16, 'name' => 'Alternative Email', 'slug' => "email2", 'type' => 'email', 'field_category_id' => 1, "option_group" => "Standard"], // email2
            ["id" => 17, 'name' => $translator->replaceLabels("%%national_insurance_number%%"), 'slug' => "NINumber", 'type' => 'text', 'field_category_id' => 1, "option_group" => "Standard"], // NINumber
            ["id" => 18, 'name' => 'ULN', 'slug' => 'ULN', 'type' => 'number', 'field_category_id' => 1, "option_group" => "Standard"], // ULN
            //Make confusion .sonned to comment in SCOR-2644
            //["id"=>19,'name' => 'eSignature', 'slug' => 'e_signature', 'type' => 'file', 'field_category_id' => 1,"option_group"=>"Standard"], // e_signature
            ["id" => 20, 'name' => 'Description', 'slug' => 'description', 'type' => 'textarea', 'field_category_id' => 1, "option_group" => "Standard"], // description
            ["id" => 21, 'name' => 'Username', 'slug' => 'username', 'type' => 'text', 'field_category_id' => 1, "option_group" => "Standard"], // username
            ["id" => 22, 'name' => 'Expiry Date', 'slug' => 'expiration_dt', 'type' => 'date', 'field_category_id' => 1, "option_group" => "Standard"], // expiration_dt
            ["id" => 23, 'name' => 'Registration Date', 'slug' => 'registration_dt', 'type' => 'datetime', 'field_category_id' => 1, "option_group" => "Standard"], // registration_dt
            ["id" => 24, 'name' => $translator->replaceLabels("%%emergency_contant__details%%") . ' ' . $translator->replaceLabels("%%emergency_contant__name%%"), 'slug' => 'emergency_name', 'type' => 'text', 'field_category_id' => 1, "option_group" => "Standard"], //
            ["id" => 25, 'name' => $translator->replaceLabels("%%emergency_contant__details%%") . ' ' . $translator->replaceLabels("%%emergency_contant__relationship%%"), 'slug' => 'emergency_relationship', 'type' => 'text', 'field_category_id' => 1, "option_group" => "Standard"], //
            ["id" => 26, 'name' => $translator->replaceLabels("%%emergency_contant__details%%") . ' ' . $translator->replaceLabels("%%emergency_contant__number%%"), 'slug' => "emergency_contact_numbers", 'type' => 'textarea', 'field_category_id' => 1, "option_group" => "Standard"], //

            ["id"=>27,'name' => 'Date Of Birth', 'slug' => Str::slug('Date Of Birth', '-'), 'type' => 'date', 'field_category_id' => 1,"option_group"=>"Standard"], // DateOfBirth
            ["id"=>28,'name' => 'Sex', 'slug' => 'Sex', 'type' => 'selectbox', 'field_category_id' => 1,"option_group"=>"Standard","model"=>'Models\Sex',"value"=>"code","label"=>"name"], // Sex
            ["id"=>29,'name' => 'Ethnicity', 'slug' => 'Ethnicity', 'type' => 'selectbox', 'field_category_id' => 1,"option_group"=>"Standard","model"=>'Models\Ethnicity',"value"=>"id","label"=>"name"], // Ethnicity
            ["id"=>30,'name' => 'Address Line 1', 'slug' => 'AddLine1', 'type' => 'textarea', 'field_category_id' => 1,"option_group"=>"Standard"], // AddLine1
            ["id"=>31,'name' => 'Address Line 2', 'slug' => 'AddLine2', 'type' => 'textarea', 'field_category_id' => 1,"option_group"=>"Standard"], // AddLine2
            ["id"=>32,'name' => 'Address Line 3', 'slug' => 'AddLine3', 'type' => 'textarea', 'field_category_id' => 1,"option_group"=>"Standard"], // AddLine3
            ["id"=>33,'name' => 'Address Line 4', 'slug' => 'AddLine4', 'type' => 'textarea', 'field_category_id' => 1,"option_group"=>"Standard"], // AddLine4
            ["id"=>34,'name' => 'Postcode', 'slug' => 'Postcode', 'type' => 'text', 'field_category_id' => 1,"option_group"=>"Standard"], // Postcode
            ["id"=>35,'name' => 'Telephone Number', 'slug' => Str::slug('Telephone Number', '-'), 'type' => 'text', 'field_category_id' => 1,"option_group"=>"Standard"],

            // event
            //ToDO:Need to confirm session date field
            //            ["id"=>37,'name' => 'Session Date', 'slug' => Str::slug('Session Date', '-'), 'type' => 'date', 'field_category_id' => 2], // start_date
            ["id" => 38, 'name' => 'Status', 'slug' => 'status_event', 'type' => 'selectbox', 'field_category_id' => 2], // status
            ["id" => 39, 'name' => 'Completed At', 'slug' => 'completed_at', 'type' => 'date', 'field_category_id' => 2], // end_date
            ["id" => 40, 'name' => 'Absence Type', 'slug' => 'is_authorised', 'type' => 'selectbox', 'field_category_id' => 2], // is_authorised
            ["id" => 41, 'name' => 'Notes', 'slug' => 'authorisation_notes', 'type' => 'textarea', 'field_category_id' => 2], // authorisation_notes
            // programme
            ["id" => 42, 'name' => 'Start Date', 'slug' => 'start_at', 'type' => 'date', 'field_category_id' => 3],
            ["id" => 43, 'name' => 'Pause Programme', 'slug' => 'paused', 'type' => 'checkbox', 'field_category_id' => 3],
            ["id" => 44, 'name' => 'Start Of Pause', 'slug' => 'paused_start', 'type' => 'date', 'field_category_id' => 3],
            ["id" => 45, 'name' => 'End Of Pause', 'slug' => 'paused_end', 'type' => 'date', 'field_category_id' => 3],
            ["id" => 46, 'name' => 'Lock Access To Programme Resources', 'slug' => 'lock_access_to_programme_resources', 'type' => 'checkbox', 'field_category_id' => 3],
            ["id" => 47, 'name' => 'Status', 'slug' => 'status_programme', 'type' => 'selectbox', 'field_category_id' => 3],
            ['id' => 48, 'name' => 'Assigned Manager', 'slug' => 'assigned_manager', 'type' => 'fillable', 'field_category_id' => 2, 'custom' => 0],
            ['id' => 49, 'name' => 'Assigned Learner', 'slug' => 'assigned_learner', 'type' => 'fillable', 'field_category_id' => 2, 'custom' => 0],
            ["id" => 50, 'name' => \APP\Templates::translate('%%programme%%') . " Duration", 'slug' => 'completion_months', 'type' => 'number', 'field_category_id' => 3, 'custom' => 0],
            ["id" => 51, 'name' => 'Learning Hours Required', 'slug' => 'working_hours', 'type' => 'number', 'field_category_id' => 3, 'custom' => 0],
            ["id" => 52, 'name' => "Expected " . \APP\Templates::translate('%%completion_date%%'), 'slug' => 'due_at', 'type' => 'date', 'field_category_id' => 3, 'custom' => 0, 'option_group' => null, "status" => true],
            ["id" => 53, 'name' => 'Actual Learning Hours Completed', 'slug' => 'time_spent', 'type' => 'time', 'field_category_id' => 3, 'custom' => 0, 'option_group' => null, "status" => true],
            ["id" => 54, 'name' => 'Resource Completion (%)', 'slug' => 'percentage', 'type' => 'text', 'field_category_id' => 3, 'custom' => 0, 'option_group' => null, "status" => true],
            ["id" => 55, 'name' => 'Criteria Completion (%)', 'slug' => 'criteria_completion', 'type' => 'text', 'field_category_id' => 3, 'custom' => 0, 'option_group' => null, "status" => true]
        ];
        foreach ($form_type_fields as  $form_type_field) {
            $form_type_field_data =  \Models\Field::where('id', $form_type_field['id'])->first();
            if (!$form_type_field_data) {
                \Models\Field::create($form_type_field);
            } else {
                \Models\Field::where('id', $form_type_field['id'])->update($form_type_field);
            }
        }


        $form_options = [];
        //Seeder for static values
        //Insert authorised values in option table for field
        $is_authorised = \Models\Field::where("slug", "is_authorised")->first();
        $is_authorised_arr = [
            [
                "value" => "Authorised", "key" => "true"
            ],
            [
                "value" => "Not Authorised", "key" => "false"
            ]
        ];
        foreach ($is_authorised_arr as $is_authorised_val) {
            $form_options[] = [
                "field_id" => $is_authorised->id, "status" => '1', "key" => $is_authorised_val['key'], "value" => $is_authorised_val['value'],
            ];
        }

        //Insert Optional values for Status for events
        $status_event = \Models\Field::where("slug", "status_event")->first();
        $status_event_arr = [
            [
                "value" => "%%event_completion_state_not_attempted%%", "key" => "%%event_completion_state_not_attempted%%"
            ],
            [
                "value" => "%%event_completion_state_in_progress%%", "key" => "%%event_completion_state_in_progress%%"
            ],
            [
                "value" => "%%event_completion_state_completed%%", "key" => "%%event_completion_state_completed%%"
            ]
        ];
        foreach ($status_event_arr as $status_event_val) {
            $form_options[] = [
                "field_id" => $status_event->id, "status" => '1', "key" => $status_event_val['key'], "value" => $status_event_val['value'],
            ];
        }

        //Insert Optional values for Status for events
        $status_programme = \Models\Field::where("slug", "status_programme")->first();
        $status_programme_arr = [
            [
                "value" => "not attempted", "key" => "not attempted"
            ],
            [
                "value" => "in progress", "key" => "in progress"
            ],
            [
                "value" => "completed", "key" => "completed"
            ]
        ];
        foreach ($status_programme_arr as $status_programme_val) {
            $form_options[] = [
                "field_id" => $status_programme->id, "status" => '1', "key" => $status_programme_val['key'], "value" => $status_programme_val['value'],
            ];
        }

        foreach ($form_options as  $form_option) {
            \Models\FieldOption::updateOrCreate($form_option);
        }
        //self::gtaa_custom_fields();
    }
    public static function gtaa_custom_fields()
    {
        $roles = Role::get()->toArray();
        $roles =  array_map(function ($element) {
            return $element + ['ticked' => true];
        }, $roles);
        $fields = [
            ['name' => 'Union', 'slug' => 'union', 'type' => 'selectbox', 'field_category_id' => 1, 'custom' => 1, 'label' => null, 'value' => null, 'model' => null, 'option_group' => 'Custom', 'status' => 1, 'options' => [
                ['status' => true, 'key' => 'UNIFOR', 'value' => 'UNIFOR'],
                ['status' => true, 'key' => 'PAPFFA', 'value' => 'PAPFFA'],
                ['status' => true, 'key' => 'NONUNION', 'value' => 'NONUNION'],
            ]],
          ['name' => 'Employee Type', 'slug' => 'employee_type', 'type' => 'selectbox', 'field_category_id' => 1, 'custom' => 1, 'label' => null, 'value' => null, 'model' => null, 'option_group' => 'Custom', 'status' => 1, 'options' => [
            ['status' => true, 'key' => 'Permanent', 'value' => 'Permanent'],
            ['status' => true, 'key' => 'Term', 'value' => 'Term'],
            ['status' => true, 'key' => 'Seasonal', 'value' => 'Seasonal'],
            ['status' => true, 'key' => 'Student', 'value' => 'Student'],
          ]],
            ['name' => 'Job Role', 'slug' => 'job_role', 'type' => 'selectbox', 'field_category_id' => 1, 'custom' => 1, 'label' => null, 'value' => null, 'model' => null, 'option_group' => 'Custom', 'status' => 1, 'options' => [
                ['status' => true, 'key' => 'Coordinator', 'value' => 'Coordinator'],
                ['status' => true, 'key' => 'Manager', 'value' => 'Manager'],
                ['status' => true, 'key' => 'Supervisor', 'value' => 'Supervisor'],
                ['status' => true, 'key' => 'Associate', 'value' => 'Associate'],
                ['status' => true, 'key' => 'Plumber', 'value' => 'Plumber'],
                ['status' => true, 'key' => 'Specialist', 'value' => 'Specialist'],
                ['status' => true, 'key' => 'Technical Inspector', 'value' => 'Technical Inspector'],
                ['status' => true, 'key' => 'Assistant', 'value' => 'Assistant'],
                ['status' => true, 'key' => 'Analyst', 'value' => 'Analyst'],
                ['status' => true, 'key' => 'Associate Director', 'value' => 'Associate Director'],
                ['status' => true, 'key' => 'Director', 'value' => 'Director'],
                ['status' => true, 'key' => 'Technician', 'value' => 'Technician'],
                ['status' => true, 'key' => 'Captain', 'value' => 'Captain'],
                ['status' => true, 'key' => 'Firefighter', 'value' => 'Firefighter'],
                ['status' => true, 'key' => 'Representative', 'value' => 'Representative'],
                ['status' => true, 'key' => 'Engineer', 'value' => 'Engineer'],
                ['status' => true, 'key' => 'Millwright', 'value' => 'Millwright'],
                ['status' => true, 'key' => 'Electrician', 'value' => 'Electrician'],
                ['status' => true, 'key' => 'Administrator', 'value' => 'Administrator'],
                ['status' => true, 'key' => 'Chief', 'value' => 'Chief'],
                ['status' => true, 'key' => 'Carpenter', 'value' => 'Carpenter'],
                ['status' => true, 'key' => 'Advisor', 'value' => 'Advisor'],
                ['status' => true, 'key' => 'Legal Counsel', 'value' => 'Legal Counsel'],
                ['status' => true, 'key' => 'Operator', 'value' => 'Operator'],
                ['status' => true, 'key' => 'Technologist', 'value' => 'Technologist'],
                ['status' => true, 'key' => 'Mechanic', 'value' => 'Mechanic'],
                ['status' => true, 'key' => 'Sign Maker', 'value' => 'Sign Maker'],
                ['status' => true, 'key' => 'Clerk', 'value' => 'Clerk'],
                ['status' => true, 'key' => 'Architect', 'value' => 'Architect'],
                ['status' => true, 'key' => 'Sprinkler Fitter', 'value' => 'Sprinkler Fitter'],
                ['status' => true, 'key' => 'Instructor', 'value' => 'Instructor'],
                ['status' => true, 'key' => 'Cleaner', 'value' => 'Cleaner'],
                ['status' => true, 'key' => 'Project Manager', 'value' => 'Project Manager'],
                ['status' => true, 'key' => 'Accountant', 'value' => 'Accountant'],
                ['status' => true, 'key' => 'Planner', 'value' => 'Planner'],
                ['status' => true, 'key' => 'Recruiter', 'value' => 'Recruiter'],
                ['status' => true, 'key' => 'Welder', 'value' => 'Welder'],
                ['status' => true, 'key' => 'VP and Chief Operating Officer of Airport Op', 'value' => 'VP and Chief Operating Officer of Airport Op'],
                ['status' => true, 'key' => 'Business Partner', 'value' => 'Business Partner'],
                ['status' => true, 'key' => 'Apprentice', 'value' => 'Apprentice'],
                ['status' => true, 'key' => 'VP Governance and Corporate Safety and Securi', 'value' => 'VP Governance and Corporate Safety and Securi'],
                ['status' => true, 'key' => 'Graphic Designer', 'value' => 'Graphic Designer'],
                ['status' => true, 'key' => 'CEO', 'value' => 'CEO'],
                ['status' => true, 'key' => 'VP and Chief Human Resources Officer', 'value' => 'VP and Chief Human Resources Officer'],
                ['status' => true, 'key' => 'VP Stakeholder Relations and Communications', 'value' => 'VP Stakeholder Relations and Communications'],
                ['status' => true, 'key' => 'VP and Chief Financial Officer', 'value' => 'VP and Chief Financial Officer'],
                ['status' => true, 'key' => 'Chief Infrastructure Officer', 'value' => 'Chief Infrastructure Officer'],
                ['status' => true, 'key' => 'Chief Commercial Officer', 'value' => 'Chief Commercial Officer'],
                ['status' => true, 'key' => 'Editor', 'value' => 'Editor'],
                ['status' => true, 'key' => 'Student', 'value' => 'Student'],
                ['status' => true, 'key' => 'VP and Chief Information Officer', 'value' => 'VP and Chief Information Officer'],
            ]],

            ['name' => 'Employee Status', 'slug' => 'employee_status', 'type' => 'selectbox', 'field_category_id' => 1, 'custom' => 1, 'label' => null, 'value' => null, 'model' => null, 'option_group' => 'Custom', 'status' => 1, 'options' => [
                ['status' => true, 'key' => 'Active', 'value' => 'Active'],
                ['status' => true, 'key' => 'Leave of absence', 'value' => 'Leave of absence'],
                ['status' => true, 'key' => 'Terminated', 'value' => 'Terminated'],
            ]],

            ['name' => 'Proposed End Date', 'slug' => 'prop_end_date', 'type' => 'date', 'field_category_id' => 1, 'custom' => 1, 'label' => null, 'value' => null, 'model' => null, 'option_group' => 'Custom', 'status' => 1],
            ['name' => 'LastHire Date', 'slug' => 'last_hire_date', 'type' => 'date', 'field_category_id' => 1, 'custom' => 1, 'label' => null, 'value' => null, 'model' => null, 'option_group' => 'Custom', 'status' => 1],
            ['name'=>"People Group","slug"=>"people_group","type"=>"text","field_category_id"=>1,"custom"=>1,'label' => null, 'value' => null, 'model' => null, 'option_group' => 'Custom', 'status' => 1],
            ['name'=>"Org Level 4 GL Segment","slug"=>"Org_Level_4_GL_Segment","type"=>"text","field_category_id"=>1,"custom"=>1,'label' => null, 'value' => null, 'model' => null, 'option_group' => 'Custom', 'status' => 1],
            ['name'=>"Job Level","slug"=>"job_level","type"=>"text","field_category_id"=>1,"custom"=>1,'label' => null, 'value' => null, 'model' => null, 'option_group' => 'Custom', 'status' => 1],
        ];
        foreach ($fields as $field) {
            if (!ModelsField::where('slug', $field['slug'])->first()) {   //check field already exist
                $field['display_type'] = $roles;
                $fieldData = ModelsField::create($field); //create new field
                if ($field['type'] === 'selectbox') { //selectbox and multiselect need exta option for saving
                    $options =  $field['options'];
                    unset($field['options']);
                    foreach ($options as $option) {
                        $option['field_id'] = $fieldData->id;
                        FieldOption::create($option);
                    }
                }
            }
        }
    }
}
