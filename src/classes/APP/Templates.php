<?php
namespace APP;
class TemplateTranslator {
	//descend into a structure recursively, transling $text_field and using $descend_into_field to follow tree structure
	//the structure to be translated is passed by reference and couldb be an array of structures

	public function translateNestedStructures(&$structures, $text_field, $descend_into_field) {
		if (!is_array($structures)) {
			$structures = array($structures);
		}

		foreach($structures as $i => $structure) {
			if (isset($structure[$text_field])) {
				$structures[$i][$text_field] = $this->replaceLabels($structures[$i][$text_field]);
			}
			if (isset($structure[$descend_into_field])) {
				$this->translateNestedStructures(
					$structures[$i][$descend_into_field],
					$text_field,
					$descend_into_field
				);
			}
		}
	}

	public function replaceLabels($text) {
		$translated_text = $text;

		$translated_text = \APP\TemplateTranslator::replaceVersionLabels($translated_text);

		if (
			isset($this->translations) &&
			$this->translations
		) {
			$translated_text = str_replace(
				array_keys($this->translations),
				array_values($this->translations),
				$translated_text
			);
		}
		return $translated_text;
	}

	public function replaceVersionLabels($text) {
		$translated_text = $text;

		// Replace licensing labels
		if (
			isset($this->licensing_translations) &&
			is_array($this->licensing_translations)
		) {
			foreach ($this->licensing_translations as $key => $value) {
				$translated_text = str_replace(
					'%%' . $key . '%%',
					$value ?? "",
					$translated_text ?? ""
				);
			}
		}
		return $translated_text;
	}

	static public function translatedValue($text) {
		$translated_text = (new self)->replaceVersionLabels($text);
		return $translated_text;
	}

	private function processInnerText($text) {
		global $repls;
		$repls = [];

		$text = preg_replace_callback('/{{.*?}}/', function ($matches) {
			global $repls;
			$repl_id = "@@@@" . count($repls) . "@@@@";
			$repls[$repl_id] = $matches[0];
			return $repl_id;
		}, $text);

		$text = $this->replaceLabels($text);

		foreach($repls as $repl_id => $repl) {
			$text = str_replace($repl_id, $repl, $text);
		}


		return $text;
	}

	public function processTtemplate($html) {
		$tpl_dom = \APP\SimpleHtmlDom::StrGetHtml($html, true, true, DEFAULT_TARGET_CHARSET, false);

		$els = $tpl_dom->find('text');


		foreach ($els as $i => $el) {
			if (!empty(trim($el))) {
				$el = $this->processInnerText($el);
				$tpl_dom->find('text', $i)->innertext = $el;
			}
		}

		//proces placeholder values
		$els = $tpl_dom->find('input');
		foreach ($els as $i => $el) {
			if ($el->placeholder) {
				$tpl_dom->find('input', $i)->placeholder = $this->processInnerText($el->placeholder);;
			}
		}


		return $tpl_dom->save();
	}

	function __construct() {
		$labels = \Models\Label::where('status', true)
			->get()
		;
		foreach($labels as $label) {
			$this->translations[$label->from_text] = $label->to_text;
		}

		// Also load list from licensing version
		if (isset($GLOBALS["CONFIG"]->licensing['labels'])) {
			$this->licensing_translations = $GLOBALS["CONFIG"]->licensing['labels'];
			// Look into DefaultLabel and overwrite a
			$default_labels = \Models\DefaultLabel::where('overwrite', '>', '')->get();
			foreach ($default_labels as $key => $default_label) {
				$this->licensing_translations[$default_label->slug] = $default_label->overwrite;
			}
			foreach ($this->licensing_translations as $key => $value) {
				$this->licensing_translations[$key] = htmlspecialchars($value, ENT_QUOTES);

			}
		}
	}

}

class Templates {
	static private $translator = null;

	static public function getTranslator() {
		if (self::$translator == null) {
			self::$translator = new \APP\TemplateTranslator();
		}

		return self::$translator;
	}

	static public function translateTemplate($tpl_contents, $trans_symbol = "%%") {
		$labels = \Models\Label::where('status', true)
			->get()
		;

		foreach($labels as $label) {
			$tpl_contents = str_replace(
				"{$trans_symbol}{$label->from_text}{$trans_symbol}",
				"<?='{$label->to_text}'?>",
				$tpl_contents
			);
		}
		if (!empty($trans_symbol)) {
			$tpl_contents = str_replace($trans_symbol, "", $tpl_contents);
		}
		return $tpl_contents;
	}


	static public function updateTemplates($tpl_dir, $translator = false) {
		if ($translator == false) {
			$translator = new \APP\TemplateTranslator();
		}

		foreach (glob("{$tpl_dir}/*.html") as $filename) {
			//Back up template
			copy($filename, dirname($filename) . "/backup/" . basename($filename) . "." . uniqid("saved"));

			$tpl_html = file_get_contents($filename);

			$translated_tpl = $translator->processTtemplate($tpl_html);

			file_put_contents($filename, $translated_tpl);
		}

		$pages = \Models\Page::all();
		foreach($pages as $page) {
			$page->name = str_replace($label, $translation, $page->name);
			$page->save();
		}
	}

	static public function getVariables($settings) {
		if (isset($_SESSION["branded_company"])) {
			$company = \Models\Company::find($_SESSION["branded_company"]);
			if ($company) {
				if (
					!empty($company->logo) &&
					is_file($settings["CompanyLogosPath"] . $company->logo)
				) {
					$settings["DefaultLogo"] = $settings["LMSCompanyLogosUri"] . $company->logo;
				}
			}

		}

		$vars = [
			"LMSUrl" => $settings["LMSUrl"],
			"LMSUri" => $settings["LMSUri"],
			"LMSTitle" => \APP\Tools::getConfig('LMSTitle') ? \APP\Tools::getConfig('LMSTitle') : $settings["LMSTitle"],
			"updateLRS" => $settings["updateLRS"],
			"LMSAppUri" => $settings["LMSAppUri"],
			"licensing" => $settings["licensing"],
			"LMSTplsUri" => $settings["LMSTplsUri"],
			"LMSTplsUriCombined" => $settings["LMSTplsUriCombined"],
			"DefaultLogo" => $settings["DefaultLogo"],
			"LMSIconsUri" => $settings["LMSIconsUri"],
			"LMSTplsUriHTML" => $settings["LMSTplsUriHTML"],
			"LMSCompanyLogosUri" => $settings["LMSCompanyLogosUri"],
			"LMSCompanyLoginBgUri" => $settings["LMSCompanyLoginBgUri"],
			"LMSCompanyLearnerBgUri" => $settings["LMSCompanyLearnerBgUri"],
			"LMSCompanyThumbnailUri" => $settings["LMSCompanyThumbnailUri"],
			"CategoryLandingImageUri" => $settings["CategoryLandingImageUri"],
			"LMSPasswordPattern" => $settings["LMSPasswordPattern"],
			"demo" => \APP\Auth::isDemoEnabled(),
			"role_name" => \APP\Auth::roleName(),
			"role_name_slug" => \APP\Tools::safeName(\APP\Auth::roleName(), "_", true),
			"version" => \Models\Version::orderBy('created_at', 'desc')->first()->id,
			"defaultUKPRN" => \APP\Tools::getConfig('defaultUKPRN'),
			"loading_animation" => ($settings["licensing"]["isSMCR"] ? "smcr" : "openelms"),
			"colorScheme" => self::getUserColorScheme()!==""?self::getUserColorScheme():'light-theme',
			"colorSchemePre" => (!\APP\Tools::getConfig('isBlackColourScheme') && !\APP\Auth::isAdminInterface() ? '_dark' : ''),
			"olarkCode" => \APP\Tools::getConfig('enableOlark') ? \APP\Tools::getConfig('olarkCode') : '',
			"logoURL" => \APP\Auth::getRedirectUri($settings, true),
			"googleAnalyticsCode" => \APP\Tools::getConfig('enableGoogleAnalytics') ? \APP\Tools::getConfig('googleAnalyticsCode') : '',
			"mysqlDbName" => $settings["database"]["database"],
			"usernameLabel" => \APP\Tools::getConfig('defaultUsernameLabel') ? \APP\Tools::getConfig('defaultUsernameLabel') : 'Username',
			"showSocialLoginButtons" => \APP\Tools::getConfig('showSocialLoginButtons') ? 'true' : 'false',
			"schoolField" => \APP\Tools::getConfig('schoolField') ? 'true' : 'false',
			"registerShowCompany" => \APP\Tools::getConfig('registerShowCompany') ? 'true' : 'false',
			"registerMandatoryCompany" => \APP\Tools::getConfig('registerMandatoryCompany') ? 'true' : 'false',
			"registerShowCountry" => \APP\Tools::getConfig('registerShowCountry') ? 'true' : 'false',
			"RegisterCompanyText" => \APP\Tools::getConfig('RegisterCompanyText') ? 'true' : 'false',
			"certificate__this_certifies" => \APP\Templates::translate('%%certificate__this_certifies%%'),
			"certificate__has_completed" => \APP\Templates::translate('%%certificate__has_completed%%'),
			"certificate__has_achieved" => \APP\Templates::translate('%%certificate__has_achieved%%'),
			"certificate__percent" => \APP\Templates::translate('%%certificate__percent%%'),
			"certificate__date_of_completion" => \APP\Templates::translate('%%certificate__date_of_completion%%'),
            "certificate__has_completed_programme" => \APP\Templates::translate('%%certificate__has_completed_programme%%'),
			"docs_bot_tooltip" => \APP\Templates::translate('%%docs_bot_tooltip%%'),
		];



		if (\APP\Auth::isLoggedIn()) {
			$user = \APP\Auth::getUser();
			$user_fields = ["username", "email", "fname", "lname"];

			foreach($user_fields as $user_field) {
				$vars["USER_" . strtoupper($user_field)] = $user->$user_field;
			}

			$vars["USER_IS_ADMIN"] = \APP\Auth::isAdmin() ? 1 : 0;
			$vars["USER_IS_MANAGER"] = \APP\Auth::ismanager() ? 1 : 0;
			$vars["USER_IS_TRAINEE"] = !\APP\Auth::isAdmin() && !\APP\Auth::ismanager() ? 1 : 0; // There is no trainee role, but I assume if you are not manager or admin, you are trainee.

			// If user has company, brand logo as that company, not the session one
			if (\APP\Auth::getUserCompanyId()) {
				$company = \Models\Company::find(\APP\Auth::getUserCompanyId());
				if (
					$company &&
					!empty($company->logo) &&
					is_file($settings["CompanyLogosPath"] . $company->logo)
				) {
					$vars["DefaultLogo"] = $settings["LMSCompanyLogosUri"] . $company->logo;
				}
			}

		}

		return $vars;
	}

	public static function translate($text) {
		return (new \APP\TemplateTranslator())->replaceVersionLabels($text);
  }
  public static function translateOrReplace($text)
  {
    return ucwords(str_replace("%%","",(new \App\TemplateTranslator())->replaceVersionLabels($text)));
  }

	/**
	 * Get user-specific color scheme, with fallback to global configuration
	 * User preference overrides both global config and browser prefers-color-scheme
	 */
	private static function getUserColorScheme() {
		// For admin interface, use global config
		if (\APP\Auth::isAdminInterface()) {
			return '';
		}

		// For learners, check individual user preference first
		if (\APP\Auth::isLearner() && \APP\Auth::getUserId()) {
			$user_color_scheme = \Models\TableExtension::getValue('users', \APP\Auth::getUserId(), 'color_scheme');
			
			if ($user_color_scheme) {
				switch ($user_color_scheme) {
					case 'light':
						return 'light-theme';
					case 'dark':
						return 'dark-theme';
				}
			}
		}

		// Fallback to global configuration
		return (!\APP\Tools::getConfig('isBlackColourScheme') ? ' light-theme' : 'dark-theme');
	}
}
