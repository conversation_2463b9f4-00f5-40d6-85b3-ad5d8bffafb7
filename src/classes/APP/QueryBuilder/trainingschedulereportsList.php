<?php
namespace APP\QueryBuilder;

use Illuminate\Database\Capsule\Manager as DB;

class trainingschedulereportsList {
	static public function generate(&$params, $args) {
		$difference_in_days = \APP\Tools::differenceInDays($params);

		if (isset($params["chart"]) && $params["chart"] == 'true') {
			$raw_select = DB::raw('
				COUNT(learning_results.id) as entries,

				YEAR(learning_results.due_at) as year,
				MONTH(learning_results.due_at) as month,

				learning_results.*
			');
		} else {
			$raw_select = [
				'learning_results.*',
				DB::raw(
					'
						DATE_FORMAT((
							CASE
								WHEN
									learning_results.completion_date_custom IS NOT NULL
								THEN
									learning_results.completion_date_custom
								ELSE
									learning_results.grace_at
							END
						), "' . \APP\Tools::defaultDateFormatMYSQL() . '") AS expected_completion_date_uk
					'
				),
				DB::raw(
					'
						(
							CASE
								WHEN
									learning_results.completion_date_custom IS NOT NULL
								THEN
									learning_results.completion_date_custom
								ELSE
									learning_results.grace_at
							END
						) AS expected_completion_date
					'
				)
			];
		}

		if (isset($params["search"]["additionalSearchParams"])) {
			$additional_search_params = json_decode($params["search"]["additionalSearchParams"], true);
			unset($params["search"]["additionalSearchParams"]);
		}

		$query = \Models\LearningResult
			::select($raw_select)
			->with(["user" => function($query) {
				$query
					->select([
						"id",
						"fname",
						"lname",
						"department_id"
					])
					->with('department')
				;
			}])
			->with(["module" => function($query) {
				$query
					->with('type')
				;
			}])
			->where("refreshed", 0)
			->whereNotNull("learning_results.due_at")
			->join("learning_modules", function($join){
				$join
					->on("learning_modules.id", "learning_results.learning_module_id")
					->where("is_course", 0)
					->where('learning_modules.status', true)
				;

			})
			->join("users", function($join){
				$join
					->on("users.id", "learning_results.user_id")
					->where("users.status", 1)
				;
			})
			->where("users.exclude_from_reports", false)
			// Faster
			->join("user_learning_modules", function($join) {
				$join
					->on("user_learning_modules.user_id", "=", "learning_results.user_id")
					->on("user_learning_modules.learning_module_id", "=", "learning_results.learning_module_id")
					->whereNull('user_learning_modules.deleted_at')
				;
			})

		;
		//$query->setAppends(['standard_list']);


		// List only assigned learners if no permission to access all
		if (!\APP\Auth::accessAllLearners()) {
			$query->whereIn(
				"users.id",
				\Models\ManagerUser
					::where('manager_id', '=', \APP\Auth::getUserId())
					->select('user_id')
					->get()
			);
		}

		if (!\APP\Auth::accessAllCompanies()) 	{
			$query->where("users.company_id", "=", \APP\Auth::getUser()->company_id);
		}

		if (isset($additional_search_params["due_date_field"])) {
			if ($additional_search_params["due_date_field"] == 0) {

				if (isset($additional_search_params["period1_to"])) {
					$period1_to = \Carbon\Carbon::parse($additional_search_params["period1_to"])->startOfDay();
					if ($difference_in_days) {
						$period1_to->addDays($difference_in_days);
					}
				} else {
					$period1_to = \Carbon\Carbon::now()->subYears(100);
				}

				if (isset($additional_search_params["period2_to"])) {
					$period2_to = \Carbon\Carbon::parse($additional_search_params["period2_to"])->endOfDay();
					if ($difference_in_days) {
						$period2_to->addDays($difference_in_days);
					}
				} else {
					$period2_to = \Carbon\Carbon::now()->addYears(100);
				}


				$query
					->whereBetween("learning_results.due_at",[$period1_to, $period2_to])
				;
			} else {
				$n_days = $additional_search_params["n_days"];
				$date_now = \Carbon\Carbon::now()->startOfDay();
				$date_days = \Carbon\Carbon::now()->endOfDay()->addDays($n_days);
				$query
					//->where('learning_results.due_at', '>=', $date_now)
					->where('learning_results.due_at', '<=', $date_days)
				;
			}
		} 	else {
			$date_now = \Carbon\Carbon::now()->startOfDay();
			$date_days = \Carbon\Carbon::now()->endOfDay()->addDays(90);
			$query
				->where('learning_results.due_at', '>=', $date_now)
				->where('learning_results.due_at', '<=', $date_days)
			;
		}

		// Handle learning_modules__type_id filter directly in the query
		if (isset($additional_search_params["learning_modules__type_id"]) && !empty($additional_search_params["learning_modules__type_id"])) {
			$query->where("learning_modules.type_id", "=", $additional_search_params["learning_modules__type_id"]);
		}

		foreach(
			[
				'users__designation_id',
				'users__location_id',
				'users__country_id',
				'users__city_id',
				'users__company_id',
				'users__department_id',
				'learning_results__learning_module_id',
				'learning_modules__category_id',
				'learning_modules__id',
			] as $param_name) {

			if (isset($additional_search_params[$param_name]) && !empty($additional_search_params[$param_name])) {
				$params["search"][$param_name] = $additional_search_params[$param_name];
			}
		}

		if (isset($additional_search_params["group_id"]) && !empty($additional_search_params["group_id"])) 	{
			$query->join("group_users", function($join) use ($additional_search_params) {
				$join
					->on("learning_results.user_id", "=", "group_users.user_id")
					->where("group_users.group_id", "=", $additional_search_params["group_id"])
					->where("group_users.status", "=", 1)
				;
			});
		}

		// Filter users by Coach
		if (
			isset($additional_search_params["coach_trainer"]) &&
			$additional_search_params["coach_trainer"]
		) {
			$coach_trainer_id = $additional_search_params["coach_trainer"];
			$query
				->whereIn('users.id',
					\Models\ManagerUser
						::select('user_id')
						->where('manager_id', $additional_search_params["coach_trainer"])
						->get()
				)
			;
			// If this is true, classrooms designated trainer must be learners trainer also!
			$enableClassRoomManagerDropDown = \APP\Tools::getConfig('enableClassRoomManagerDropDown');
			if ($enableClassRoomManagerDropDown) {
				$manager = \Models\User::find($coach_trainer_id);
				$query = $query
					->where(function($query) use ($manager) {
						$query
							->where(function($query)  use ($manager) {
								$query = $query
									->where("learning_modules.type_id", 4)
									->where(function($query) use ($manager) {
										$query = $query
											->where("learning_modules.material", 'LIKE', '%"trainer":"' . $manager->fname . ' ' . $manager->lname . '"%')
											->orWhere("learning_modules.material", 'LIKE', '%{"id":' . $manager->id . ',%')
										;
									})
								;
							})
							->orWhere("learning_modules.type_id", "!=", 4)
						;
					});
				;
			}
		}

		// For Apprentix if standard_id is provided, show resources assigned to that standard only.
		if (
			isset($additional_search_params["standard_id"]) &&
			$additional_search_params["standard_id"]
		) {
			$query = $query
				->selectRaw($additional_search_params["standard_id"] . ' as standard_id')
				->whereIn(
					'learning_results.user_id',
					\Models\ApprenticeshipStandardUser
						::select('user_id')
						->where('standard_id', $additional_search_params["standard_id"])
						->get()
				)
				->where(function ($query) use ($additional_search_params) {
					$query
						->whereIn(
							'learning_results.learning_module_id',
							\Models\ApprenticeshipIssuesLearningModules
								::select('learning_modules_id')
								->whereIn('apprenticeship_issues_id',
									\Models\ApprenticeshipIssues
										::select('id')
										->where('status', true)
										->whereIn('issue_category_id',
											\Models\ApprenticeshipIssueCategories
												::select('id')
												->where('status', true)
												->where('standard_id', $additional_search_params["standard_id"])
												->get()
										)
										->get()
								)
								->get()
						)
						->orWhereIn(
							'learning_results.learning_module_id',
							\Models\ApprenticeshipIssuesEvidence
								::select('learning_modules_id')
								->where('user_id', 'learning_results.user_id')
								->whereIn('apprenticeship_issues_id',
									\Models\ApprenticeshipIssues
										::select('id')
										->where('status', true)
										->whereIn('issue_category_id',
											\Models\ApprenticeshipIssueCategories
												::select('id')
												->where('status', true)
												->where('standard_id', $additional_search_params["standard_id"])
												->get()
										)
										->get()
								)
								->get()
						)
						->orWhereIn(
							'learning_results.learning_module_id',
							\Models\ApprenticeshipIssuesUserLearningModules
								::select('learning_modules_id')
								->where('user_id', 'learning_results.user_id')
								->whereIn('apprenticeship_issues_id',
									\Models\ApprenticeshipIssues
										::select('id')
										->where('status', true)
										->whereIn('issue_category_id',
											\Models\ApprenticeshipIssueCategories
												::select('id')
												->where('status', true)
												->where('standard_id', $additional_search_params["standard_id"])
												->get()
										)
										->get()
								)
								->get()
						)
					;
				})

			;
		}


		return $query;
	}
}
