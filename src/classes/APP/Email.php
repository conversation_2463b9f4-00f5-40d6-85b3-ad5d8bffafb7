<?php
namespace APP;

use App\GetMSAuth;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use Models\BatchReport;
use Models\BatchReportManager;
use Models\User;
use Illuminate\Database\Capsule\Manager as DB;
use Models\Company;
use PHPMailer\PHPMailer\PHPMailer;
use Psr\Log\LoggerInterface;

class Email
{
	private $_vars = [];
	private $_tpl;
	private $_mail_settings;
	private $_n_emails = 0;
	private ?LoggerInterface $logger;

	public function __construct($email_template = false, $mail_settings = false) {
		$this->logger = \APP\LoggerHelper::getLogger();
		if ($email_template) {
			$this->_tpl = $email_template;
			$this->setTemplateVars();
		}
		$this->_mail_settings = $mail_settings;
	}

	public function getNumberOfSentEmail()
	{
		return $this->_n_emails;
	}

	private function evalVar($var, $obj)
	{
		$evaled_var = $obj;

		foreach(explode(".", $var) as $var_member)
		{
			$evaled_var = $evaled_var->$var_member;
		}

		return $evaled_var;
	}

	private function sendEmail($address, $subject, $body, $from_email = false, $from_name = false, $extra_vars=[], $resend = false) {

		$send_email = \APP\Tools::getConfig('enableEmailFunctionality');


		if (!$send_email) {
			return false;
		}
		if (is_array($address)) {
			for ($i = 0; $i < count($address); $i++) {
				$email = $address[$i];
				$user = User::where('email', $email)->with('Company')->first();
				if ($user && $user->Company && $user->Company->access_token) {
					$this->sendCompanyEmail($email, $subject, $body, $from_email = false, $from_name = false, $extra_vars = [], $resend = false, $user->Company->id);
					unset($address[$i]);
				}
			}
			$address = array_values($address);
		} else {
			$user = User::where('email', $address)->with('Company')->first();
			if ($user && $user->Company && $user->Company->access_token) {
				return $this->sendCompanyEmail($address, $subject, $body, $from_email = false, $from_name = false, $extra_vars = [], $resend = false, $user->Company->id);
			}
		}
		$testEmailAccount = \APP\Tools::getConfig('testEmailAccount');

		$mail = new PHPMailer();
		$mail->IsSMTP();
		$mail->SMTPDebug = 0;
		$mail->CharSet = 'UTF-8';
		$mail->Host = $this->_mail_settings["Host"];
		$mail->Port = $this->_mail_settings["Port"];
		$mail->SMTPAuth = $this->_mail_settings["Auth"];
		$mail->SMTPOptions = $this->_mail_settings["SMTPOptions"];
		if ($this->_mail_settings["Auth"]) {
			$mail->Username = $this->_mail_settings["Username"];
			$mail->Password = $this->_mail_settings["Password"];
			$mail->SMTPSecure = $this->_mail_settings["Secure"];
		}
		$mail->Subject = $subject;

        $defaultFromEmail = \APP\Tools::getConfig("defaultFromEmail");
        $defaultFromName = \APP\Tools::getConfig("defaultEmailFromName");

        if ($defaultFromEmail) {
            $from_email = $defaultFromEmail;
        } elseif (!$from_email) {
            $from_email = $this->_mail_settings["DefaultFromEmail"];
        }

        if ($defaultFromName) {
            $from_name = $defaultFromName;
        } elseif (!$from_name) {
            $from_name = $this->_mail_settings["DefaultFromName"];
        }


		$mail->SetFrom($from_email, $from_name);

		$adress_combined = '';
		if (
			!is_array($address)
		) {
			// Ignore some debug/test emails, do not overload system.
			if (
				(
					preg_match("/no-email\@xyz\.www/", $address) ||
					preg_match("/.*\@example\.com/", $address) ||
					preg_match("/.*\@randomopenelms\.no/", $address) ||
					!filter_var($address, FILTER_VALIDATE_EMAIL)
				) &&
				!$testEmailAccount
			) {
				$address = [];
			} else {
				$address = [$address];
			}
		}
		$n_emails = 0;
		$adress_combined = '';
		foreach($address as $a) {
			if ($a > '') {
				$n_emails++;
				if ($testEmailAccount) {
					$a = $testEmailAccount;
				}
				$mail->AddAddress($a);
				$adress_combined = $adress_combined . ' ' . $a;
			}
		}
		// Do not proceed if there is no email to send to.
		if ($adress_combined == '') {
			return;
		}
		// Check if any files are attached to templates and send them out!
		if ($this->_tpl) {
			$existing_files = \Models\File
				::where('table_name', 'email_templates')
				->where('table_row_id', $this->_tpl->id)
				->get()
			;
			foreach ($existing_files as $key => $existing_file) {
				$filename = $GLOBALS["CONFIG"]->LMSFilePath . $existing_file->hash . '.' . $existing_file->extension;
				if (is_file($filename)) {
					$mail->addAttachment($filename, $existing_file->file);
				}
			}

			// If this template is generated from batch report, check if any files are attached to batch report and load them too.
			if ($this->_tpl->batch_report_id) {
				$batch_report_files = \Models\File
					::where('table_name', 'batch_reports')
					->where('table_row_id', $this->_tpl->batch_report_id)
					->get()
				;
				if (count($batch_report_files) > 0) {
					foreach ($batch_report_files as $key => $batch_report_file) {
						$filename = $GLOBALS["CONFIG"]->LMSFilePath . $batch_report_file->hash . '.' . $batch_report_file->extension;
						if (is_file($filename)) {
							$mail->addAttachment($filename, $batch_report_file->file);
						}
					}
				} else {
					$batch_report = BatchReport::where('id',$this->_tpl->batch_report_id)->first();
					if ($batch_report->type == 'report') {
						$file_path = \App\Tools::generateReviewReport($this->_tpl->batch_report_id,$GLOBALS["CONFIG"]);
						if ($file_path && is_file($file_path)) {
							$fileDetails = pathinfo($file_path);
							$mail->addAttachment($file_path,$fileDetails['basename']);
						}
					}
				}
			}

			if ($extra_vars) {
				if (isset($extra_vars['attachments'])) {
					foreach($extra_vars['attachments'] AS $attachment){
						$filepath = $GLOBALS["CONFIG"]->LMSEventEmailAttachmentPath . $attachment;
						if (is_file($filepath)) {
							$fileDetails = pathinfo($filepath);
							$mail->addAttachment($filepath,$fileDetails['basename']);
						}
					}
				}
			}
		}


		// Record e-mails sent even if no success, in case google is not reachable, or something.
		\Models\EmailStatistic::updateCount($n_emails);

		$mail->MsgHTML($body);

		// check if EmailHistory such email exists sent today, if it does, do not resend!

		if (\APP\Tools::getConfig('duplicatedEmailCheck')) {
			$history_today = \Models\EmailHistory
				::where('data_hash', md5(trim($adress_combined) . $subject . $body))
				->whereDate('created_at', DB::raw('CURDATE()'))
				->first()
			;
		} else {
			$history_today = false;
		}

		if ($history_today) {
			$history_today->duplicate_count = $history_today->duplicate_count + 1;
			$history_today->save();
		}

		if (
			$history_today &&
			$history_today->duplicate_count > \APP\Tools::getConfig('duplicatedEmailLimit') &&
			!$resend
		) {
			if (
				$history_today->duplicate_count == 1 &&  // or == \APP\Tools::getConfig('duplicatedEmailLimit') ??
				\APP\Tools::getConfig('duplicatedEmailNotification')
			) {
				// send <NAME_EMAIL>, hardcoded, great!
				// To implement!
			}

			// Fake that email is sent, do not send it
			$result = true;
		} else {
			$result = $mail->send();
			if (!$history_today) { // record history only if not sent today same email
				$history = new \Models\EmailHistory;
				$history->email_to = trim($adress_combined);
				$history->email_from = trim($from_email);
				$history->name_from = $from_name;
				$history->subject = $subject;
				$history->body = $body;
				$history->body_text_only = \APP\Tools::stripEmailHtmlContent($body);
				$history->converted = true;
				$history->data_hash = md5(trim($adress_combined) . $subject . $body);
				$user_sent_to = \Models\User::where('email', trim($adress_combined))->first();
				if ($user_sent_to) {
					$history->user_id = $user_sent_to->id;
					$history->name_to = $user_sent_to->fname . ' ' . $user_sent_to->lname;
				}
				//$history->user_id_from = $email_to;
				//$history->email_template_id = $email_to;
				//$history->email_template_name = $email_to;

				//$history->debug = $debug;
			}

			if ($result) {
				// Succes
				$this->_n_emails += $n_emails;
				if (!$history_today) {
					$history->sent = true;
					$history->sent_count = 1;
				} else {
					if ($history_today) {
						$history_today->sent_count = $history_today->sent_count + 1;
					}
				}
			} else {
				// Fail
				if (!$history_today) {
					$history->sent = false;
				}
			}

			if ($mail->ErrorInfo) {
				if (
					!$history_today &&
					$history
				) {
					$history->error = $mail->ErrorInfo;
				} else {
					if ($history_today) {
						$history_today->error = $mail->ErrorInfo;
					}
				}
			}

			if (
				!$history_today &&
				$history
			) {
				$history->save();
			} else {
				if ($history_today) {
					$history_today->save();
				}
			}
		}

		return $result;
	}
	private function sendCompanyEmail($address, $subject, $body, $from_email = false, $from_name = false, $extra_vars=[], $resend = false,$company_id = false) {

		$send_email = \APP\Tools::getConfig('enableEmailFunctionality');
		if (!$send_email) {
			return false;
		}
		$testEmailAccount = \APP\Tools::getConfig('testEmailAccount');

		$mail = new PHPMailer();
		$mail->IsSMTP();
		$mail->SMTPDebug = 0;
		$mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
		$mail->Host = "smtp.office365.com";
		$mail->Port = 587;
		$mail->SMTPAuth = true;
		$mail->AuthType = 'XOAUTH2';
		$oauthTokenProvider = new \APP\GetMSAuth($company_id);
		$mail->setOAuth($oauthTokenProvider);
		$mail->Subject = $subject;
		$company = Company::where('id',$company_id)->first();
		$from_email = $company->email_from;
		$from_name = $company->email_from_name;
		$mail->SetFrom($company->email_from, $company->email_from_name);
		$adress_combined = '';
		if (
			!is_array($address)
		) {
			// Ignore some debug/test emails, do not overload system.
			if (
				(
					preg_match("/.*\@randomopenelms\.no/", $address) ||
					!filter_var($address, FILTER_VALIDATE_EMAIL)
				) &&
				!$testEmailAccount
			) {
				$address = [];
			} else {
				$address = [$address];
			}
		}
		$n_emails = 0;
		$adress_combined = '';
		foreach($address as $a) {
			if ($a > '') {
				$n_emails++;
				if ($testEmailAccount) {
					$a = $testEmailAccount;
				}
				$mail->AddAddress($a);
				$adress_combined = $adress_combined . ' ' . $a;
			}
		}
		// Do not proceed if there is no email to send to.
		if ($adress_combined == '') {
			return;
		}
		// Check if any files are attached to templates and send them out!
		if ($this->_tpl) {
			$existing_files = \Models\File
				::where('table_name', 'email_templates')
				->where('table_row_id', $this->_tpl->id)
				->get()
			;
			foreach ($existing_files as $key => $existing_file) {
				$filename = $GLOBALS["CONFIG"]->LMSFilePath . $existing_file->hash . '.' . $existing_file->extension;
				if (is_file($filename)) {
					$mail->addAttachment($filename, $existing_file->file);
				}
			}

			// If this template is generated from batch report, check if any files are attached to batch report and load them too.
			if ($this->_tpl->batch_report_id) {
				$batch_report_files = \Models\File
					::where('table_name', 'batch_reports')
					->where('table_row_id', $this->_tpl->batch_report_id)
					->get()
				;
				if (count($batch_report_files) > 0) {
					foreach ($batch_report_files as $key => $batch_report_file) {
						$filename = $GLOBALS["CONFIG"]->LMSFilePath . $batch_report_file->hash . '.' . $batch_report_file->extension;
						if (is_file($filename)) {
							$mail->addAttachment($filename, $batch_report_file->file);
						}
					}
				} else {
					$batch_report = BatchReport::where('id',$this->_tpl->batch_report_id)->first();
					if ($batch_report->type == 'report') {
						$file_path = \App\Tools::generateReviewReport($this->_tpl->batch_report_id,$GLOBALS["CONFIG"]);
						if ($file_path && is_file($file_path)) {
							$fileDetails = pathinfo($file_path);
							$mail->addAttachment($file_path,$fileDetails['basename']);
						}
					}
				}
			}

			if ($extra_vars) {
				if (isset($extra_vars['attachments'])) {
					foreach($extra_vars['attachments'] AS $attachment){
						$filepath = $GLOBALS["CONFIG"]->LMSEventEmailAttachmentPath . $attachment;
						if (is_file($filepath)) {
							$fileDetails = pathinfo($filepath);
							$mail->addAttachment($filepath,$fileDetails['basename']);
						}
					}
				}
			}
		}


		// Record e-mails sent even if no success, in case google is not reachable, or something.
		\Models\EmailStatistic::updateCount($n_emails);

		$mail->MsgHTML($body);

		// check if EmailHistory such email exists sent today, if it does, do not resend!

		if (\APP\Tools::getConfig('duplicatedEmailCheck')) {
			$history_today = \Models\EmailHistory
				::where('data_hash', md5(trim($adress_combined) . $subject . $body))
				->whereDate('created_at', DB::raw('CURDATE()'))
				->first()
			;
		} else {
			$history_today = false;
		}

		if ($history_today) {
			$history_today->duplicate_count = $history_today->duplicate_count + 1;
			$history_today->save();
		}

		if (
			$history_today &&
			$history_today->duplicate_count > \APP\Tools::getConfig('duplicatedEmailLimit') &&
			!$resend
		) {
			if (
				$history_today->duplicate_count == 1 &&  // or == \APP\Tools::getConfig('duplicatedEmailLimit') ??
				\APP\Tools::getConfig('duplicatedEmailNotification')
			) {
				// send <NAME_EMAIL>, hardcoded, great!
				// To implement!
			}

			// Fake that email is sent, do not send it
			$result = true;
		} else {
			$result = $mail->send();
			if (!$history_today) { // record history only if not sent today same email
				$history = new \Models\EmailHistory;
				$history->email_to = trim($adress_combined);
				$history->email_from = trim($from_email);
				$history->name_from = $from_name;
				$history->subject = $subject;
				$history->body = $body;
				$history->body_text_only = \APP\Tools::stripEmailHtmlContent($body);
				$history->converted = true;
				$history->data_hash = md5(trim($adress_combined) . $subject . $body);
				$user_sent_to = \Models\User::where('email', trim($adress_combined))->first();
				if ($user_sent_to) {
					$history->user_id = $user_sent_to->id;
					$history->name_to = $user_sent_to->fname . ' ' . $user_sent_to->lname;
				}
				//$history->user_id_from = $email_to;
				//$history->email_template_id = $email_to;
				//$history->email_template_name = $email_to;

				//$history->debug = $debug;
			}

			if ($result) {
				// Succes
				$this->_n_emails += $n_emails;
				if (!$history_today) {
					$history->sent = true;
					$history->sent_count = 1;
				} else {
					if ($history_today) {
						$history_today->sent_count = $history_today->sent_count + 1;
					}
				}
			} else {
				// Fail
				if (!$history_today) {
					$history->sent = false;
				}
			}

			if ($mail->ErrorInfo) {
				if (
					!$history_today &&
					$history
				) {
					$history->error = $mail->ErrorInfo;
				} else {
					if ($history_today) {
						$history_today->error = $mail->ErrorInfo;
					}
				}
			}

			if (
				!$history_today &&
				$history
			) {
				$history->save();
			} else {
				if ($history_today) {
					$history_today->save();
				}
			}
		}

		return $result;
	}

    public function sendPublicEmail($address, $subject, $body, $from_email = false, $from_name = false){
       return $this->sendEmail($address, $subject, $body, $from_email = false, $from_name = false);
    }

	public function sendToUser($user, $extra_vars = [], $from = false, $custom_tpl = false) {
		$users = [];
		if (is_array($user)) {
			$users = $user;
		} else {
			$users[] = $user;
		}

		$from_email = false;
		$from_name = false;

		if ($from) {
			//$from_email = $from->email;
			$from_name = "{$from->fname} {$from->lname}";
		}

		// If custom template, set template vars.
		if ($custom_tpl) {
			if (isset($custom_tpl['body'])) {
				$this->_tpl->body = $custom_tpl['body'];
			}
			if (isset($custom_tpl['subject'])) {
				$this->_tpl->subject = $custom_tpl['subject'];
			}
			if (isset($custom_tpl['copy_email_to_managers'])) {
				$this->_tpl->copy_email_to_managers = $custom_tpl['copy_email_to_managers'];
			}
			$this->setTemplateVars();
		}

		$translator = \APP\Templates::getTranslator();

		foreach($users as $user) {

		    // check if its a learner, if its learner then check learner's company
            // check if company has from email , from name , footer - use that
            $footer_html = null;
            if (
            	$user->role &&
            	$user->role->is_learner &&
            	$user->company
            ) {
                $from_email = $user->company->email_from;
                $from_name = $user->company->email_from_name;
                $footer_html = $user->company->email_footer;
            }

			// Fail if user is disabled, or expired
			if (
				$user->status == 0 ||
				$user->exclude_from_emails ||
				(
					$user->expiration_dt &&
					\Carbon\Carbon::parse($user->expiration_dt) < \Carbon\Carbon::now()
				)
			) {
				break;
			}

			$tpl_body = $this->_tpl->body;
			$tpl_name = $this->_tpl->name;
			$tpl_subject = $this->_tpl->subject;
			if($footer_html) {
                $tpl_body .= '<br/>'.$footer_html;
            }

			foreach($this->_vars as $var) {
				$var_name = "%%{$var}%%";
				if (in_array($var, array_keys($extra_vars))) {
					$var_value = "";
					if ($extra_vars[$var]) {
						$var_value = $extra_vars[$var];
					}
				} else {
					if (preg_match("/USER_(.+)/i", $var, $matches)) {
						$var_value = $this->evalVar(strtolower($matches[1]), $user);
					} else if (isset($GLOBALS["CONFIG"]) && preg_match("/CONFIG_(.+)/i", $var, $matches)) {
						$var_value = $this->evalVar($matches[1], $GLOBALS["CONFIG"]);
					} else {
						$var_value = $translator->replaceLabels('%%' . $var . '%%');
					}
				}
				$tpl_subject = str_replace($var_name, $var_value, $tpl_subject);
				$tpl_body = str_replace($var_name, $var_value, $tpl_body);

				$pattern = '/<img[^>]*src=[\'"][^\'"]*%%' . preg_quote($var, '/') . '%%[^\'"]*[\'"][^>]*>/';
                $tpl_body = preg_replace($pattern, '', $tpl_body);

                $var_pattern = '%%' . $var . '%%';
                if (strpos($tpl_body, $var_pattern) !== false) {
                    // Regular expression to find and remove the variable
                    $tpl_body = preg_replace('/' . preg_quote($var_pattern, '/') . '/', '', $tpl_body);
                }
			}
//
			if (
				$this->sendEmail(
					$user->email2 ? [$user->email, $user->email2] : $user->email,
					$tpl_subject, $tpl_body, $from_email, $from_name, $extra_vars)
			) {
				// If template has "copy_email_to_managers" check, send e-mail to all persons managers.
				if (
					$this->_tpl->copy_email_to_managers &&
					$user->id
				) {
					$managers = \Models\ManagerUser
						::select('manager_users.*')
						->where('manager_users.user_id', $user->id)
						->join("users", function($join) {
							$join
								->on("users.id", "=", "manager_users.manager_id")
								->where("users.status", 1)
							;
						})
						->with('Manager')
						->get()
					;
					foreach ($managers as $key => $manager) {
						//
						if (
							$manager->manager &&
							$manager->manager->role &&
							!$manager->manager->role->email_disable_manager_notifications
						) {
						$this->sendEmail($manager->manager->email, "Copy of email sent to your learner: '" . $user->email . "', with subject: '" . $tpl_subject . "'", $tpl_body, $from_email, $from_name);
						}
					}
				}
			} else {
				break;
			}
		}
		return true;
	}

	public function queueEmailToUserIds(
		array $user_ids,
		$from = false,
		$frequency_pattern = false,
		$get_users_parameters = '',
		$get_users_modified_parameters = '',
		$get_users_arguments = '',
		$get_users_url = '',
		$get_users_method = '',
		$user_id_key = '',
		$update = false
	) {
		if ($update && $update > 0) {
			$email_queue = \Models\EmailQueue::find($update);
		} else {
			$email_queue = new \Models\EmailQueue;
		}

		if ($from) {
			if (is_numeric($from)) {
				$email_queue->from = $from;
			} else {
				$email_queue->from = $from->id;
			}
		}

		$email_queue->recipients = $user_ids;

		if (!$update) {
			$email_queue->email_template_id = $this->_tpl?->id ?? null;
			$email_queue->processed = false;
			$email_queue->send_date = null;

			if ($frequency_pattern) {
				$email_queue->frequency_pattern = json_encode($frequency_pattern);

				$send_date = \APP\Tools::nextDateFromPattern($frequency_pattern);

				// Calculate next send_date using pattern.
				if ($send_date) {
					$email_queue->send_date = $send_date;
				}
			}

			$email_queue->get_users_parameters = $get_users_parameters;
			$email_queue->get_users_modified_parameters = $get_users_modified_parameters;
			$email_queue->get_users_arguments = $get_users_arguments;
			$email_queue->get_users_url = $get_users_url;
			$email_queue->get_users_method = $get_users_method;
			$email_queue->user_id_key = $user_id_key;
		}
		$email_queue->save();
	}

	private function processEmailQueueItem($e_q_item, $logger = false, $LMSUrl = false) {
		$recipients = [];

		foreach($e_q_item->recipients as $recipient_id) {
			$recipient = \Models\User
				::where('id', $recipient_id)
				->where('status', true)
				->first()
			;
			if ($recipient) {
				$manager_contacts = '';
				if (isset($recipient->managers) && count($recipient->managers) > 0) {
					foreach ($recipient->managers as $key => $manager) {
						//first name, last name, company, department, email, telephone number and Department address
						$manager_contacts = $manager_contacts . '
							<p>
								<strong>
									' . $manager->fname . ' ' . $manager->lname . '<br>
									' . (isset($manager->company) ? $manager->company->name : '') . '<br>
									' . (isset($manager->department) ? $manager->department->name : '') . '<br>
									' . $manager->email . '<br>
									' . $manager->phone . '<br>
									' . (isset($manager->department) ? $manager->department->address : '') . '<br>
								</strong>
							</p>
						';
					}
				}

				$extra_vars = [
					'USER_FNAME' => $recipient->fname,
					'USER_LNAME' => $recipient->lname,
					'USER_NAME' => $recipient->fname . ' ' . $recipient->lname,
					'MANAGER_CONTACTS' => $manager_contacts,
					'CONFIG_LMSUrl' => $GLOBALS["CONFIG"]->LMSUrl,
					'CONFIG_LMSName' => $GLOBALS["CONFIG"]->LMSName,
					'REGARDS' => $GLOBALS["CONFIG"]->LMSName,
				];
				if (\APP\Tools::getConfig('redirectAllLinksThroughSSO')) {
					$extra_vars['CONFIG_LMSUrl'] = $GLOBALS["CONFIG"]->LMSUrl . 'saml/?ReturnTo=' . urlencode($GLOBALS["CONFIG"]->LMSUrl);

					// Forgotten Password Link template should not use SSO!
					// This needs to be configurable somewhere.
					if ($e_q_item->email_template_id) {
						$template = \Models\EmailTemplate
							::where('id', $e_q_item->email_template_id)
							->where('name', 'Forgotten Password Link')
							->first()
						;
						if ($template) {
							$extra_vars['CONFIG_LMSUrl'] = $GLOBALS["CONFIG"]->LMSUrl;
						}
					}


				}
				if ($e_q_item->custom_variables) {
					$custom_variables = json_decode($e_q_item->custom_variables, true);
					if (isset($custom_variables['EVENT_ID'])) {
						$schedule_link = \Models\ScheduleLink
						::select('link_id')
						->where('schedule_id', $custom_variables['EVENT_ID'])
						->where('type', 'users')
						->where('link_id', $recipient_id)
						->first();
						if ($schedule_link) {
							$custom_variables['SPECIAL_REQUIREMENTS'] = "Special Requirements: ".$schedule_link->learner_requirement;
						}

					}
					if (is_array($custom_variables)) {
						$extra_vars = array_merge($extra_vars, $custom_variables);
					}
					if(isset($custom_variables['attachments'])){
						$extra_vars['ATTACHMENTS'] = $custom_variables['attachments'];
					}
				}

				if ($e_q_item->comment) {
					$extra_vars['COMMENT'] = nl2br($e_q_item->comment);

					// if "comment added" template is sent out, check if any files were added against this comment
					// Add files in comment and link them to resource details.
					if (
						isset($custom_variables['COMMENT_ID']) &&
						$e_q_item->learning_module_id
					) {
						$attached_files = \Models\File
							::where('table_name', 'learning_results_comments')
							->where('table_row_id', $extra_vars['COMMENT_ID'])
							->where('status', true)
							->get()
						;
						if (count($attached_files) > 0) {
							$extra_vars['COMMENT'] = $extra_vars['COMMENT'] . '<br><strong>Attached files:</strong>';
						}

						foreach ($attached_files as $attached_file_key => $attached_file) {
							if (
								isset($custom_variables['FROM']) &&
								$custom_variables['FROM'] == 'manager'
							) {
								$resource_details_url = $GLOBALS["CONFIG"]->LMSUrl . 'app/learner/resources/' . $e_q_item->learning_module_id . '/details';
								if (\APP\Tools::getConfig('redirectAllLinksThroughSSO')) {
									$resource_details_url = $GLOBALS["CONFIG"]->LMSUrl . 'saml/?ReturnTo=' . urlencode($resource_details_url);
								}
								$extra_vars['COMMENT'] = $extra_vars['COMMENT'] . '<br> <a href="' . $resource_details_url . '">' . $attached_file->file . '</a>';
							} else {
								$extra_vars['COMMENT'] = $extra_vars['COMMENT'] . '<br> ' . $attached_file->file;
							}
						}
					}

				}
				// Sending e-mail about learning module, approved, rejected, etc, extract vars from learning module and user sent from

				if (
					$e_q_item->learning_module_id
				) {
					$learning_module = \Models\LearningModule::find($e_q_item->learning_module_id);
					// If module is checked as no tracking, do not send e-mail out, break the loop!
					if (
						$learning_module &&
						$learning_module->track_progress
					) {

						$extra_vars['LEARNING_RESOURCE_NAME'] = $learning_module->name;
						$extra_vars['COURSE_ID'] = $learning_module->id;

						if ($e_q_item->from) {
							$learning_results_user = \Models\User::find($e_q_item->from);
							$extra_vars['LEARNER_FNAME'] = $learning_results_user->fname;
							$extra_vars['LEARNER_LNAME'] = $learning_results_user->lname;
							$extra_vars['LEARNER_ID'] = $learning_results_user->id;
						}
					}
				}

				// If custom_data decode it and check if usable
				if ($e_q_item->custom_data) {
					$custom_data = @json_decode($e_q_item->custom_data, true);
					if (
						$custom_data &&
						isset($custom_data['type']) &&
						$custom_data['type'] == 'reminders' &&
						isset($custom_data['resources']) &&
						is_array($custom_data['resources'])
					) {
						$REMINDER_RESOURCES = '';
						foreach ($custom_data['resources'] as $key => $value) {
							$due_in = $value['days_remaining'] > 0 ? 'due in ' . $value['days_remaining'] . ' days' : ' due now';

							$module_url = $GLOBALS["CONFIG"]->LMSUrl . 'app/learner/resources/' . $value['learning_module_id'];
							if (\APP\Tools::getConfig('redirectAllLinksThroughSSO')) {
								$module_url = $GLOBALS["CONFIG"]->LMSUrl . 'saml/?ReturnTo=' . urlencode($module_url);
							}

							$REMINDER_RESOURCES = $REMINDER_RESOURCES . '
							<p>
								Learning resource <a href="' . $module_url . '">' . $value['learning_module_name'] . '</a>: ' . $due_in . '
							</p>
							';
						}
						$extra_vars['REMINDER_RESOURCES'] = $REMINDER_RESOURCES;
						$extra_vars['FREQUENCY_DAYS'] = \APP\Tools::getConfig('apprentixEmailReminderFrequency');
					}
				}

				if (
					$recipient
					&& !$this->sendToUser(
						$recipient,
						$extra_vars,
						$e_q_item->fromUser,
						$e_q_item->custom_tpl ? json_decode($e_q_item->custom_tpl, true) : false
					)
				) {
					$this->logger->warning("failed to process email for user", [
						'recipient_id' => $recipient_id,
						'email' => $recipient->email,
					]);
				}
			}
		}

		if ($e_q_item->recipients_emails) {
			$recipients_emails = explode(",", $e_q_item->recipients_emails);
			foreach ($recipients_emails as $key => $recipients_email) {
				if (filter_var($recipients_email, FILTER_VALIDATE_EMAIL)) {
					$recipient = new \stdClass();
					$recipient->id = false;
					$recipient->status = 1;
					$recipient->exclude_from_emails = 0;
					$recipient->expiration_dt = false;
					$recipient->email = $recipients_email;
					$recipient->name = $recipients_email;
					$recipient->email2 = false;
					$recipient->role =false;

					$this->sendToUser(
						$recipient,
						json_decode($e_q_item->custom_variables, true),
						$e_q_item->fromUser
					);
				}
			}

		}

		if (
			$e_q_item->frequency_pattern != null &&
			$e_q_item->frequency_pattern
		) {
			//$e_q_item->send_date = \Carbon\Carbon::now()->addDays($e_q_item->frequency);
			$e_q_item->send_date = \APP\Tools::nextDateFromPattern(json_decode($e_q_item->frequency_pattern, TRUE), $e_q_item->send_date);
			$e_q_item->times_sent = $e_q_item->times_sent + 1;
		} else {
			$e_q_item->processed = true;
		}
		$e_q_item->save();
	}

	public static function processEmailQueue($mail_settings, $logger = false, $LMSUrl = false) {
		$date_now = \Carbon\Carbon::now();
		$n_emails = 0;
		$errors = "";

		// update recipent list from saved query if it is frequent email and needs to be sent
		$email_update = \Models\EmailQueue
			::select('email_queue.*')
			->whereNotNull("frequency_pattern")
			->where('approved', true)
			->where('send_date', '<', $date_now )
			->join("email_templates", function($join) {
				$join
					->on("email_templates.id", "=", "email_queue.email_template_id")
					->where("email_templates.status", true)
				;
			})
			->get()
		;

		foreach($email_update as $e_u_item) {

			// I need to log in user that created queue, to work with query generator
			if ($e_u_item->from) {
				\APP\Auth::loginCronUser($e_u_item->from);
				// And log out them afterwards.


				// get all saved parameters and arguments from database and turn them to something usable
				$get_users_parameters = json_decode($e_u_item->get_users_parameters, true);
				$get_users_modified_parameters = json_decode($e_u_item->get_users_modified_parameters, true);
				$get_users_arguments = json_decode($e_u_item->get_users_arguments, true);
				$user_id_key = json_decode($e_u_item->user_id_key, true);

				// build query
				if ($e_u_item->get_users_url) {
					$QueryBuilder = "\\APP\\QueryBuilder\\" . str_replace('list', 'List', strtolower($e_u_item->get_users_url));
					$query = $QueryBuilder::generate($get_users_parameters, $get_users_arguments);

					if (isset($query)) {

						// get results from query, all
						$data = \APP\SmartTable::searchPaginate($get_users_modified_parameters, $query, false, false);

						//This will tell what ID to update in email_queue
						$get_users_modified_parameters['email']['update'] = $e_u_item->id;

						// This will update recipients for specific email_queue
						\APP\Email::getRecipients (
							false,
							$data,
							$get_users_modified_parameters,
							$get_users_parameters,
							$mail_settings,
							'', // URL where this request has been ran
							'', // Template name
							$user_id_key, // where to look for user's ID in this query
							$get_users_arguments
						);
					}
				}

				\APP\Auth::logout();
			}
		}

		// re-fech them all! Why?
		$email_queue = \Models\EmailQueue
			::select('email_queue.*')
			->with("template")
			->where('approved', true)
			->with("fromUser")
			->join("email_templates", function($join) {
				$join
					->on("email_templates.id", "=", "email_queue.email_template_id")
					->where("email_templates.status", true)
				;
			})
			->where("processed", false)
			->get()
		;


		foreach($email_queue as $e_q_item) {
			try {
				// Ensure the email template exists
				if (!$e_q_item->template) {
					continue; // Skip this queue item
				}
				// if frequency is specified in e-mail then check against send date, if send date is in past, send it out
				if (
					(
						!$e_q_item->frequency_pattern &&
						(
							!$e_q_item->send_date ||
							$e_q_item->send_date < $date_now
						)
					) ||
					(
						$e_q_item->frequency_pattern &&
						$e_q_item->send_date < $date_now
					)
				) {
					$email = new Email($e_q_item->template, $mail_settings);
					$email->processEmailQueueItem($e_q_item, $logger, $LMSUrl);
					$n_emails += $email->getNumberOfSentEmail();
				}
			} catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
				$errors =  $errors . "\n  ModelNotFoundException for queue ID {$e_q_item->id}: " . $e->getMessage();
				continue;
			} catch (\Throwable $e) {
				$errors =  $errors . "\n  General error for queue ID {$e_q_item->id}: " . $e->getMessage();
				continue;
			}
		}

		$email_resend = \Models\EmailHistory::where("resend","1")->get();
		$email = new \APP\Email(false, $mail_settings);

		foreach($email_resend AS $resend_val){
			$email->sendEmail(trim($resend_val->email_to), $resend_val->subject, $resend_val->body, $resend_val->email_from, $resend_val->name_from, [], true);
			$resend_val->resend="0";
			$resend_val->save();
		}
		$return_data = $n_emails . " emails sent.";
		if ($errors > '') {
			$return_data = $return_data . "\n Errors:\n" . $errors;
		}
		return $return_data;
	}

	static public function getRecipients (
		$response = false, // response object to respond with it if available
		$data = '', // query results fetched from database
		$params = '', // modified/final parameters
		$get_users_parameters = '', // original parameters
		$email_settings = false, // email settings from config file
		$get_users_url = '', // URL used to instigate this action
		$tpl_name = '', // template name
		$user_id_key = '', // what properties to use for $data object, is aarray, can contain multiple vales ['user', 'id'] will generate $data->user->id
		$get_users_arguments = '', // arguments passe din URL
		$get_users_method = 'POST' // request method
	) {
		if (!empty($params["email"]["message"]) && !empty($params["email"]["subject"])) {
			$user_ids = [];


			foreach ($data as $record) {
				if (
					$user_id_key &&
					isset($user_id_key[0]) &&
					isset($user_id_key[1]) &&
					isset($record->{$user_id_key[0]}) &&
					isset($record->{$user_id_key[0]}->{$user_id_key[1]})
				) {
					$user_ids[] = $record->{$user_id_key[0]}->{$user_id_key[1]};
				} else {
					if (isset($record->{$user_id_key[0]})) {
						$user_ids[] = $record->{$user_id_key[0]};
					}
				}
			}

			$user_ids = array_values(array_unique($user_ids));


			//$params["email"]["update"] = 1;

			$update = isset($params["email"]["update"]) && $params["email"]["update"] ? $params["email"]["update"] : false;
			$message = $params["email"]["message"];
			$subject = $params["email"]["subject"];
			$frequency_pattern = isset($params["email"]["frequency"]['selected']['pattern']) &&$params["email"]["frequency"]['selected']['pattern'] ? $params["email"]["frequency"]['selected']['pattern'] : false;

			if (!$update) {
				$new_t_tpl = new \Models\EmailTemplate;
				$new_t_tpl->is_temporary = true;
				$new_t_tpl->name = uniqid($tpl_name);
				$new_t_tpl->subject = $subject;
				$new_t_tpl->body = $message;
			}


			// Frequent email set-up
			if (
				$frequency_pattern &&
				!$update
			) {
				$get_users_parameters = json_encode($get_users_parameters);
				$get_users_modified_parameters = json_encode($params);
				$get_users_arguments = json_encode($get_users_arguments);
				$user_id_key = json_encode($user_id_key);
				$get_users_url = $get_users_url;
			} else {
				$get_users_parameters = '';
				$get_users_modified_parameters = '';
				$get_users_arguments = '';
				$get_users_url = '';
				$get_users_method = '';
				$user_id_key = '';
			}
			// EOF Frequent email set-up

			try {
				if (!$update) {
					$new_t_tpl->save();
				}
				$email = new \APP\Email(!$update ? $new_t_tpl->name : false, $email_settings);
				$email->queueEmailToUserIds(
					$user_ids,
					\APP\Auth::getUserId(),
					$frequency_pattern,
					$get_users_parameters,
					$get_users_modified_parameters,
					$get_users_arguments,
					$get_users_url,
					$get_users_method,
					$user_id_key,
					$update
				);
			} catch(Exception $e) {
				if ($response) {
					return $response
						->withStatus(500)
						->write(json_encode($e->getMessage()))
					;
				}
			}
			if ($response) {
				return
					$response
						->withHeader('Content-Type', 'application/json')
						->write(json_encode(count($user_ids)))
				;
			}
		} else {
			if ($response) {
				return
					$response
						->withStatus(500)
						->write('Message and Subject must be present.')
				;
			}
		}
	}

	// Email set up from reports will be put in e-mail queue
	static public function sendBatchReport (
		$subject,
		$message,
		$entries,
		$copy_manager = false,
		$report_id = false,
		$report_data = false
	) {
		$new_t_tpl = new \Models\EmailTemplate;
		$new_t_tpl->is_temporary = true;
		$new_t_tpl->name = uniqid($subject);
		$new_t_tpl->subject = $subject;
		$new_t_tpl->body = $message;
		$new_t_tpl->copy_email_to_managers = $copy_manager;
		$user_ids = [];
		$batch_report = false;
		if ($report_id) {
			$new_t_tpl->batch_report_id = $report_id;
			$batch_report = \Models\BatchReport::find($report_id);
		}
		if ($report_data) {
			$batch_report = BatchReport
				::where('id', $report_data->batch_report_id)
				->first()
			;
		}
		if (
			$report_data &&
			$batch_report->type == 'report'
		) {
			$users = User
				::whereIn(
					'id',
					BatchReportManager
						::select('user_id')
						->where('batch_report_id', $report_data->batch_report_id)
				)
				->get()
			;
			foreach($users as $user) {
				if (!in_array($user->id,$user_ids)) {
					$user_ids[] = $user->id;
				}
			}
		}
		$new_t_tpl->save();

		$entries = json_decode($entries);
		foreach ($entries as $key => $entry) {
			if (isset($entry->user_id)) {
				$user_id = $entry->user_id;
			} else {
				$user_id = $entry->id;
			}

			if (
				!in_array($user_id, $user_ids) &&
				$batch_report &&
				$batch_report->type &&
				$batch_report->type == 'email'
			) {
				$user_ids[] = $user_id;
			}
		}

		$email = new \APP\Email($new_t_tpl, $GLOBALS["CONFIG"]->email);
		// Is email batch report has files attached to it, add  those files to active template.
		$email->queueEmailToUserIds(
			$user_ids,
			\APP\Auth::getUserId()
		);

	}

	private function setTemplateVars() {
		if (is_object($this->_tpl) && isset($this->_tpl->subject, $this->_tpl->body)) {
			if (preg_match_all("/%%([a-zA-Z0-9_\.]+)%%/i", $this->_tpl->subject . $this->_tpl->body, $matches, PREG_PATTERN_ORDER)) {
				$this->_vars = $matches[1];
			}
		} else {
			$this->logger->warning("Invalid email template: expected object with subject and body", [
				'type' => gettype($this->_tpl),
				'value' => $this->_tpl
			]);
		}
	}


	public static function createMailer($email_template_name, $mail_settings = false) {

		if (!\APP\Tools::getConfig('enableEmailFunctionality')) {
			return false;
		}

		if (!$mail_settings) {
			if (isset($GLOBALS["CONFIG"])) {
				$mail_settings = $GLOBALS["CONFIG"]->email;
			} else {
				throw new \Exception("Can't setup email settings");
			}
		}

		// Check if template is enabled
		$template = \Models\EmailTemplate
			::where("name", "=", $email_template_name)
			->where('status', true)
			->first()
		;
		if ($template && $template->status) {
			return new Email($template, $mail_settings);
		} else {
			return false;
		}
	}

	// Send user email notification if following reviews are added
	public static function sendReviewsStatusEmail($user_id, $review) {
		/*
			Development
			Training
			Progress Review
		*/
		if (
			$review->visit_type == 'Development' ||
			$review->visit_type == 'Training' ||
			$review->visit_type == 'Progress Review'
		) {
			$template_name = 'Visit set up';
			if ($review->completion_status == 'Completed') {
				$template_name = 'Visit Completed';
			}
			$template = \Models\EmailTemplate
				::where('name', $template_name)
				->where('status', true)
				->first()
			;

			if ($template) {
				$resource_list_str = $review->visit_type . ' by ' . $review->visitor->fname . ' ' . $review->visitor->lname .  '<br>';
				$email_queue = new \Models\EmailQueue;
				$email_queue->email_template_id = $template->id;
				$email_queue->recipients = [$user_id];
				$email_queue->custom_variables = json_encode([
					'ASSIGNED_TASKS' => $resource_list_str
				]);
				$email_queue->from = \APP\Auth::getUserId();
				$email_queue->save();
			}

		}
	}

	// Creates e-mail queue for sending notification that resource was assigned to user!
	public static function sendEmailReminder(
		$user_ids,
		$module_ids,
		$review_id = false,
		$signoff = false,
		$start_date = false // when to set up email to be sent out
	) {
		if (!is_array($user_ids)) {
			$user_ids = [$user_ids];
		}
		if (!is_array($module_ids)) {
			$module_ids = [$module_ids];
		}


		$template = \Models\EmailTemplate
			::where('name', 'New work assigned')
			->where('status', true)
			->first()
		;

		if (
			$signoff &&
			count($module_ids) == 1
		) {
			$template = \Models\EmailTemplate
				::where('name', '%%learning_resource%% Signed Off by %%manager%%')
				->where('status', true)
				->first()
			;
		}

		$modules = \Models\LearningModule
			::whereIn('id', $module_ids)
			->where('track_progress', true)
			->get()
		;
		$resource_list_str = '';
		$resource_list_str_name = '';


		// stop right there is there is no modules!
		if (count($modules) == 0) {
			return false;
		}
		foreach ($modules as $key => $module) {
			$module_url = $GLOBALS["CONFIG"]->LMSUrl . 'app/learner/resources/' . $module->id;
			if (\APP\Tools::getConfig('redirectAllLinksThroughSSO')) {
				$module_url = $GLOBALS["CONFIG"]->LMSUrl . 'saml/?ReturnTo=' . urlencode($module_url);
			}
			$resource_list_str = $resource_list_str . '<a href="' . $module_url . '">' . $module->name .  '</a><br>';
			$resource_list_str_name = $resource_list_str_name . $module->name .  '<br>';
		}


		if (
			$template &&
			$template->id &&
			(
				count($modules) > 0 ||
				$review
			) &&
			count($user_ids) > 0
		) {
			$email_queue = new \Models\EmailQueue;
			$email_queue->email_template_id = $template->id;
			$email_queue->recipients = $user_ids;
			$email_queue->learning_module_id = $modules[0]->id; // This will be valid only if single resource is signed off to user!
			$email_queue->custom_variables = json_encode([
				'ASSIGNED_TASKS' => $resource_list_str,
				'ASSIGNED_TASKS_NAME_ONLY' => $resource_list_str_name
			]);
			$email_queue->from = null;
			if (\APP\Auth::getUserId()) {
				$email_queue->from = \APP\Auth::getUserId();
			}

			if ($start_date) {
				$email_queue->send_date = \Carbon\Carbon::parse($start_date);
			}

			$email_queue->save();
		}
	}

	// Add instruction email to queue for given user, depending on role
	public static function sendInstructionEmail($user_id, $roles = []) {
		$user = \Models\User
			::with('Role')
			->find($user_id)
		;
		if (
			!is_array($roles)
		) {
			$roles = [];
		}

		// Possible roles/templates
		$role_templates = [
			'is_learner' => 'Welcome Message Trainee',
			'is_manager' => 'Welcome Message Manager',
			'is_admin' => 'Welcome message Admin',
		];
		$template_name = false;

		if (
			$user &&
			$user->Role
		) {
			//determine what email to send out.
			foreach ($role_templates as $key => $role_template) {
				if (
					// If no roles are passed send email to user depending on roles
					(
						$user->role[$key] &&
						count($roles) == 0
					) ||
					// If roles are passed, send email if user has passed role, in order supplied, manager overwrites learner and admin overwrites manager.
					(
						$user->role[$key] &&
						in_array($key, $roles)
					)

				) {
					$template_name = $role_template;
				}
			}

			if ($template_name) {
				$template = \Models\EmailTemplate
					::where('name', $template_name)
					->where('status', true)
					->first()
				;

				if ($template) {
					$email_queue = new \Models\EmailQueue;
					$email_queue->email_template_id = $template->id;
					$email_queue->recipients = [$user->id];
					$email_queue->from = NULL;
					if (\APP\Auth::getUserId()) {
						$email_queue->from = \APP\Auth::getUserId();
					}
					$email_queue->save();

					$user->startup_instructions_sent_at = \Carbon\Carbon::now();
					$user->startup_instructions_sent = true;
					$user->save();
					return true;
				}
			}
		}
		return false;
	}

    /**
     * @param string $to_email
     * @param array $extra_vars
     * @param null|string $from_email
     * @param null|string $from_name
     */
	public function sendAlertEmail($to_email, $extra_vars, $from_email = null, $from_name =  null) {
        $translator = \APP\Templates::getTranslator();
        $tpl_body = $this->_tpl->body;
//        $tpl_name = $this->_tpl->name;
        $tpl_subject = $this->_tpl->subject;

        if(empty($from_email)) {
            $from_email = data_get($GLOBALS["CONFIG"]->email, 'DefaultFromEmail');
        }

        if(empty($from_name)) {
            $from_name = data_get($GLOBALS["CONFIG"]->email, 'DefaultFromName');
        }

        if(empty($from_email)) {
            return;
        }

        foreach($this->_vars as $var) {
            $var_name = "%%{$var}%%";
            if (in_array($var, array_keys($extra_vars))) {
                $var_value = $extra_vars[$var];
            } else {
                $var_value = $translator->replaceLabels('%%' . $var . '%%');
            }
            $tpl_subject = str_replace($var_name, $var_value, $tpl_subject);
            $tpl_body = str_replace($var_name, $var_value, $tpl_body);
        }

        $this->sendEmail(
            $to_email,
            $tpl_subject, $tpl_body, $from_email, $from_name, $extra_vars);

    }

	public static function renewRefreshToken() {
		$companiesWithRefreshTokens = Company::whereNotNull('refresh_token')->get();
		$msClientId = Tools::getConfig('MicrosoftAppClientId');
		$msClientSecret = Tools::getConfig('MicrosoftAppClientSecret');
		$msTenantId = Tools::getConfig('MicrosoftTenantID');
		$errorLog = "";

		if (!empty($msClientId) && !empty($msClientSecret) && !empty($msTenantId)) {
			foreach ($companiesWithRefreshTokens as $company) {
				try {
					$client = new Client();
					$response = $client->post("https://login.microsoftonline.com/{$msTenantId}/oauth2/v2.0/token", [
						'form_params' => [
							'grant_type' => 'refresh_token',
							'client_id' => $msClientId,
							'client_secret' => $msClientSecret,
							'refresh_token' => $company->refresh_token,
							'scope' => 'offline_access https://outlook.office.com/SMTP.Send https://outlook.office.com/User.Read'
						]
					]);

					$data = json_decode($response->getBody(), true);

					if (isset($data["access_token"]) && $data["access_token"]) {
						$company->access_token = $data["access_token"];
					}

					if (isset($data["refresh_token"]) && $data["refresh_token"]) {
						$company->refresh_token = $data["refresh_token"];
					}

					$company->save();

				} catch (ClientException $e) {
					$responseBody = json_decode((string) $e->getResponse()->getBody(), true);
					if (isset($responseBody['error']) && $responseBody['error'] === 'invalid_grant') {
						// Log invalid_grant. In this case, the user needs to re-authenticate manually
						$errorLog .= "Company ID {$company->id}: Invalid grant. " . $e->getMessage() . "\n";
					}
				} catch (\Exception $e) {
					// Log unexpected exceptions
					$errorLog .= "Company ID {$company->id}: Unexpected error. " . $e->getMessage() . "\n";
				}
			}
		} else {
			$errorLog .= "Missing required configuration parameters.";
		}

		if (empty($errorLog)) {
			return true;
		} else {
			return $errorLog;
		}
	}
}
