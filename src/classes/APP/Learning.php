<?php

namespace APP;

use Illuminate\Database\Capsule\Manager as DB;

class Learning
{
	public static function getAllModuleIds($module_ids) {
		// Cache each course's modules separately for targeted invalidation
		$all_lesson_resource_ids = [];
		
		foreach ($module_ids as $course_id) {
			$cacheKey = 'course_modules_' . $course_id;
			$cached_modules = null;
			
			// Try to get from cache
			try {
				if (function_exists('cache')) {
					$cached_modules = cache()->get($cacheKey);
				}
			} catch (\Exception $e) {
				// Cache might not be available - continue without cache
			}
			
			// If not cached, fetch from database
			if ($cached_modules === null) {
				$course_modules = \Models\LearningCourseModule
					::select("learning_module_id")
					->where("learning_course_id", $course_id)
					->where("learning_module_id", "!=", $course_id) // Exclude self-reference
					->pluck("learning_module_id")
					->toArray()
				;
				
				// Cache for 24 hours (86400 seconds)
				try {
					if (function_exists('cache')) {
						cache()->put($cacheKey, $course_modules, 86400);
					}
				} catch (\Exception $e) {
					// Cache might not be available - continue without cache
				}
				
				$cached_modules = $course_modules;
			}
			
			$all_lesson_resource_ids = array_merge($all_lesson_resource_ids, $cached_modules);
		}
		
		return array_merge($module_ids, $all_lesson_resource_ids);
	}

	public static function getLinkedSkillIds($skill_id){
		$linked_skill_ids=\Models\LinkedSkill::where("learning_module_id",$skill_id)->get()
		->pluck("link_skill_id")
		->toArray();
		return array_merge($skill_id, $linked_skill_ids);
	}

	public static function launchScorm($id) {

		$user_id = \APP\Auth::getUserId();
		$user = \Models\User::findOrFail($user_id);
		$module = \Models\LearningModule::findOrFail($id);

		$result = \Models\LearningResult
			::where('user_id', $user_id)
			->where('learning_module_id', $module->id)
			->where('refreshed', 0)
			->first();
		;
		if ($result) {
			$result->touch();
		}

		$module->prerequisites = \APP\Learning::augmentPrerequisitesLearningResult(
			$user->id,
			$module->prerequisites
		);
		$error = false;

		if (
			$module->do_prerequisite &&
			!\APP\Auth::isAdminInterface() // Any learning started from administrator interface should ignore these conditions, instructor lead lesson
		) {
			foreach($module->prerequisites as $prerequisite) {
				if (
						!$prerequisite->learning_result
						||
						$prerequisite->learning_result->completion_status != "completed"
					)
				{
					$error = true;
					break;
				}
			}
		}

		// Any learning started from administrator interface should ignore these conditions, instructor lead lesson
		if (!\APP\Auth::isAdminInterface()) {
			$required_modules = \APP\Learning::getCourseRequiredModules($user->id, $module, true);
			foreach($required_modules as $required_module) {
				if (
					!$required_module->learning_result
					||
					$required_module->learning_result->completion_status != "completed"
				) {
					$error = true;
					break;
				}
			}
		}

		if ($error) 	{
			$data = ["error" => true];
		} else {
			$scorm_course = \APP\Course::get($module);
			$scorm_modules = $scorm_course->getCourseModules();


			$start_scorm_module_id = 0;

			foreach($scorm_modules as $scorm_module_id => $scorm_module_title) {
				if (!$scorm_course->isCourseModuleCompleted($user_id, $scorm_module_id)) {
					break;
				}
				$start_scorm_module_id = $scorm_module_id;
			}

			$data = [
				"user" => [
					"id" => $user->id,
					"fname" => $user->fname,
					"lname" => $user->lname,
					"email" => $user->email,
					"username" => $user->username,
				],
				"scorm" => [
					"id" => $module->id,
					"n_modules" => count($scorm_modules),
					"start_module_id" => $start_scorm_module_id,
					"width" => $module->player_width ? $module->player_width : 1070,
					"height" => $module->player_height ? $module->player_height : 650,
					"go1_id" => $module->go1_id,
				],
			];
		}

		return $data;
	}

	public static function signOffLearningResult($id, $user_id, $duration = false) {
            $response = false;

		if (
			!empty($id) &&
			!empty($user_id)
		) {
			$learningResults = \Models\LearningResult
				::where('id', $id)
				->where("user_id", $user_id)
				->first()
			;

			$learning = \Models\LearningModule::find($learningResults->learning_module_id);


			$allow_sign_off_types = [
				'webpage',
				'classroom',
				'book_cd_dvd',
				'on_the_job',
				'upload',
				'blog_entry',
				'reflective_log',
				'google_classroom',
				'zoom_meeting',
				'microsoft_teams',
				'form',
				'skill',
			];

			if (
				in_array($learning->type->slug, $allow_sign_off_types) ||
				$learning->type->custom
			) {
				// update this table field with true if everything is correct.
				$learningResults->sign_off_trainee = true;
				$learningResults->sign_off_trainee_at =  \Carbon\Carbon::now();

				if($learning->require_management_signoff){
					$learningResults->sign_off_manager = true;
					$learningResults->sign_off_manager_at =  \Carbon\Carbon::now();
					$learningResults->completion_status = 'completed';
				} else{
					$learningResults->completion_status = 'in progress';
				}
				//$learningResults->completed_at = \Carbon\Carbon::now();
				if ($duration && \APP\Tools::getConfig('mandatoryDuration')) {
					if ($duration['hours'] > 0 || $duration['minutes'] > 0) {
						$learningResults->duration_hours = $duration['hours'];
						$learningResults->duration_minutes = $duration['minutes'];
					}
				}
				$learningResults->save();
				$response = true;

				// Send Learning Resource Signed Off by Learner, if learner is signing this off
				if (\APP\Auth::isLearner()) {
					// Send out "Learning Resource Signed Off by Learner" to all managers of said user
					$template = \Models\EmailTemplate
						::where('name', '%%learning_resource%% Signed Off by %%user%%')
						->where('status', true)
						->first()
					;

					$user = \Models\User::find($user_id);

					// Get all managers that needs notification
					$manager_ids = [];
					foreach ($user->managers as $key => $manager) {
						if (
							!$manager->role->email_disable_manager_notifications &&
							$manager->status
						) {
							$manager_ids[] = $manager->id;
						}
					}

					if (
						$template &&
						$template->id &&
						count($manager_ids) > 0 &&
						$learning->track_progress &&
						!$learning->require_management_signoff
					) {
						$email_queue = new \Models\EmailQueue;
						$email_queue->email_template_id = $template->id;
						$email_queue->learning_module_id = $learning->id;
						$email_queue->recipients = $manager_ids;
						$email_queue->from = $user->id;
						$email_queue->save();
					}
				}
			}
		}
		return $response;
	}

	public static function syncUserResults($user_id, $param = null) {
		$user_ids = [];
		if (is_array($user_id)) {
			$user_ids = $user_id;
		} else {
			$user_ids[] = $user_id;
		}

		$user_modules_to_update = \Models\UserLearningModule
			::whereIn("user_id", $user_ids)
			->whereNull('user_learning_modules.deleted_at')
			->whereNotIn("user_id", function($query) {
				$query
					->select('user_id')
					->from("learning_results")
					->where("refreshed", "=", 0)
					->whereRaw("learning_module_id = user_learning_modules.learning_module_id")
				;
			})
			->get()
		;

		foreach($user_modules_to_update as $user_module) {
			$user_module->property = $param;
			$user_module->save();
		}
	}


	public static function addAllUsersToLearningModule($learning_module_id)
	{
		$unassigned_user_ids = \Models\User
			::select("users.id")
			->where('status', '=', 1)
			->whereNotIn('users.id', function($query) use($learning_module_id) {
				$query
					->select('user_id')
					->from('user_learning_modules')
					->where('learning_module_id', '=', $learning_module_id)
					->whereNull('user_learning_modules.deleted_at')
				;
			})
			->get()
			->pluck('id')
			->toArray();
		;
		$data = [];

		foreach($unassigned_user_ids as $unassigned_user_id)
		{
			$data[] = [
					'learning_module_id' => $learning_module_id,
					'user_id' => $unassigned_user_id,
			];
		}

		\Models\UserLearningModule::insert($data);

		self::syncUserResults($unassigned_user_ids);
	}

	// Retrieve only enabled resources with attached result
	public static function simpleLearningResult($query, $args) {
		$query = $query
			->select(
				'learning_modules.id',
				'learning_modules.name',
				'learning_modules.description',
				'learning_modules.do_prerequisite',
				'learning_modules.type_id',
				'learning_modules.material',
				'learning_modules.thumbnail',
				'learning_modules.track_progress',
				'learning_modules.self_enroll',
				'learning_modules.player_width',
				'learning_modules.player_height',
				'learning_modules.scorm_popup',
				'learning_modules.disable_upon_completion',
				'learning_modules.status',
				'learning_modules.jackdaw',
				'learning_modules.jackdaw_resource',
				'learning_modules.visible_learner'
			)
			->with(['LearningResult' => function ($query) use ($args) {
				$query
					->select(
						'id',
						'user_id',
						'learning_module_id',
						'completion_status',
						'passing_status',
						'attempts_at_refresh',
						'homework',
						'due_at',
						'grace_at',
						'completed_at',
						'completion_date_custom',
						'completed_version',
						'passing_status',
						'attempts_at_refresh',
						'refreshed'
					)
					->where('user_id', $args["user_id"])
					->where('refreshed', 0)
				;
			}])
			->with(['type' => function ($query) {
				$query = $query
					->withoutGlobalScope('type_filter')
				;
			}])
			->withCount(['UserLearningModule' => function ($query) use ($args) {
				$query
					->where('user_learning_modules.user_id', $args["user_id"])
				;
			}])
		;
		if (!\APP\Tools::getConfig('showUnassignedDisabledLearning')) {
			$query
				->where('learning_modules.status', true)
			;
		}
		if (\APP\Auth::isLearner()) {
			$query = $query
				->where('learning_modules.visible_learner', true)
			;
		}
		return $query;
	}

	public static function canResourceBeLaunched($resource, $args) {
		$resource->can_launch = false;
		if (
			$resource->LearningResult && // can't be launched if not assigned!
			isset($resource->type->slug) &&
			(
				$resource->type->slug == 'e_learning' ||
				$resource->type->slug == 'youtube' ||
				$resource->type->slug == 'vimeo' ||
				$resource->type->slug == 'webpage' ||
				$resource->type->slug == 'microsoft_teams' ||
				$resource->type->slug == 'zoom_meeting' ||
				$resource->type->slug == 'google_classroom' ||
				$resource->type->slug == 'h5p'
			)
		) {
			$resource->can_launch = true;
			$required_course_modules = \APP\Learning::getCourseRequiredModules(
				$args["user_id"],
				$resource,
				true // course status must be enabled to get list.
			);
			foreach($required_course_modules as $required_module) {
				if (
					!$required_module->LearningResult ||
					$required_module->LearningResult->completion_status != "completed"
				) {
					$resource->can_launch = false;
					break;
				}
			}
			// remove courses from output
			unset($resource->Courses);


			if ($resource->do_prerequisite) {
				// if any of prerequisites have not been completed, then false it!
				$prerequisites = \Models\LearningResult
					::where('user_id', $args['user_id'])
					->where('refreshed', 0)
					->whereIn('learning_module_id',
						\Models\LearningModulePrerequisite
							::select('prerequisite_id')
							->where('learning_module_id', $resource->id)
							->get()
					)
					->whereIn('learning_module_id',
						\Models\UserLearningModule
							::select('learning_module_id')
							->where('user_id', $args['user_id'])
							->get()
					)
					->where('completion_status', '!=', 'completed')
					->count()
				;
				if ($prerequisites > 0) {
					$resource->can_launch = false;
				}
			}

			// If disable_upon_completion and completed, can't be launched
			if (
				$resource->disable_upon_completion &&
				$resource->LearningResult->completion_status == "completed"
			) {
				$resource->can_launch = false;
			}

			if (
				$resource->type->slug == 'e_learning' &&
				$resource->LearningResult->passing_status == "failed" &&
				$resource->LearningResult->attempts_left < 1
			) {
				$resource->can_launch = false;
				if (\APP\Tools::getConfig('allowLearnerRefreshLearning')) {
					// If allowLearnerRefreshLearning is true, then we can launch it again.
					$resource->can_launch = true;
				}
			}

		}
		return $resource;

	}

	public static function augmentPrerequisitesLearningResult($user_id, $prerequisites) {
		if ($prerequisites) {
			foreach($prerequisites as $prerequisite) {
				$prerequisite_learning_result = \Models\LearningResult
					::where("learning_module_id", "=", $prerequisite->id)
					->where("user_id", "=", $user_id)
					->where("refreshed", "=", 0)
					->first()
				;

				$prerequisite->learning_result = $prerequisite_learning_result;
			}
		}

		return $prerequisites;
	}

	public static function getCourseRequiredModules($user_id, $learning_module = false, $status = false, $visited_modules = []) {
		$required_modules = [];

		if ($learning_module) {
			// Prevent circular dependencies by tracking visited modules
			if (in_array($learning_module->id, $visited_modules)) {
				// Circular dependency detected - return empty array to break the loop
				return [];
			}

			// Add current module to visited list
			$visited_modules[] = $learning_module->id;

			$courses = \Models\LearningModule
				::where('is_course', 1)
				->whereIn('id',
					\Models\LearningCourseModule
						::select('learning_course_id')
						->where('learning_module_id', $learning_module->id)
						->get()
				)
				->whereIn('id',
					\Models\UserLearningModule
						::select('learning_module_id')
						->where('user_id', $user_id)
						->get()
				)
				->with(["Modules" => function ($query) use ($user_id) {
					$query
						->select(
							'learning_modules.id',
							'learning_modules.name',
							'learning_modules.type_id'
						)
						->with(["LearningResult" => function ($query) use ($user_id) {
							$query
								->select(
									'learning_results.id',
									'learning_results.learning_module_id',
									'learning_results.completion_status'
								)
								->where('learning_results.refreshed', 0)
								->where('learning_results.user_id', $user_id)
							;
						}])
					;
					if (!\APP\Tools::getConfig('showUnassignedDisabledLearning')) {
						$query
							->validresource()
						;
					}
				}])
				->get()
			;

			// Group prerequisites by course to find conflicts
			$prerequisites_by_course = [];
			$all_prerequisite_modules = [];
			
			foreach($courses as $course) {
				if ($course->order_modules) {
					$course_prerequisites = [];
					foreach($course->Modules as $course_module) {
						if ($course_module->id != $learning_module->id) {
							// Check if this prerequisite module would create a circular dependency
							if (!in_array($course_module->id, $visited_modules)) {
								$course_module->RequiringCourseName = $course->name;
								if (!$status || ($status && $course->status)) {
									$course_prerequisites[] = $course_module;
									$all_prerequisite_modules[$course_module->id] = $course_module;
								}
							}
						} else {
							break; // Stop when we reach the target module
						}
					}
					$prerequisites_by_course[$course->id] = $course_prerequisites;
				}
			}

			// Smart conflict resolution: Use intersection of all course requirements
			// If a module is required by ALL courses, it's truly required
			// If it's only required by some courses, it may be due to conflicting orders
			if (count($prerequisites_by_course) > 1) {
				// Find prerequisites that are required by ALL courses with ordering
				$required_by_all = null;
				foreach($prerequisites_by_course as $course_id => $course_prereqs) {
					$prereq_ids = array_map(function($module) { return $module->id; }, $course_prereqs);
					if ($required_by_all === null) {
						$required_by_all = $prereq_ids;
					} else {
						$required_by_all = array_intersect($required_by_all, $prereq_ids);
					}
				}
				
				// Only include prerequisites that are required by ALL courses
				foreach($required_by_all as $module_id) {
					$required_modules[] = $all_prerequisite_modules[$module_id];
				}
			} else {
				// Single course or no conflicts - use all prerequisites
				$required_modules = array_values($all_prerequisite_modules);
			}
		}
		return self::augmentPrerequisitesLearningResult($user_id, $required_modules);
	}

	public static function resetApprenticeshipStandardsUsersStartDate() {
		$standard_users = \Models\ApprenticeshipStandardUser
			::where('start_at', '=', null)
			->get();
		;
		foreach ($standard_users as $key => $standard_user) {
			$standard_user->start_at = $standard_user->created_at;
			$standard_user->save();
		}
	}

	public static function returnAll ($module, $issue, $type) {
		$module_loop = new \stdClass(); //
		$module_loop->id = $module->id;
		$module_loop->is_course = $module->is_course;
		$module_loop->modules = $module->Modules;
		$module_loop->name = $module->name;
		$module_loop->pivot = $module->pivot;
		$module_loop->start_day = $issue->start_day;
		$module_loop->end_day = $issue->end_day;
		$module_loop->issue_id = $issue->id;
		$module_loop->type = $type;

		if (
			$type == 'common' &&
			$module->pivot->custom_work_window &&
			$module->pivot->end_day
		) {
			$module_loop->end_day = $module->pivot->end_day;
		}

		if (
			$type == 'common' &&
			$module->pivot->custom_work_window &&
			$module->pivot->start_day
		) {
			$module_loop->start_day = $module->pivot->start_day;
		}
		return $module_loop;
	}

	public static function returnModules ($module, $all_standard_modules) {
		// if module is course, get all assigned module id's to that course and return here.
		if ($module->is_course == 1) {
			$module_ids = \APP\Learning::getAllModuleIds([$module->id]); // potential problem
			if ($module_ids) {
				$all_standard_modules = array_merge($all_standard_modules, $module_ids);
			}
		} else {
			$all_standard_modules[] = $module->id;
		}
		return $all_standard_modules;
	}

	// Reassing all dates for assigned resources.
	public static function updateStandardStartDate ($apprenticeshipstandard, $standard_user, $user, $detach = false) {
		// loop all modules in standard and reshuffle due dates.
		$all_standard_modules = [];
		$all_standard_modules_common = [];
		$all_standard_modules_user = [];
		$all_standard_modules_evidence = [];
		$all_standard_modules_list = [];

		// loop all issues for current standard
		foreach ($apprenticeshipstandard->issues as $issue)  {
			// loop all modules in each issue and put Id's in array
			//
			foreach ($issue->modules as $module) {
				$all_standard_modules = \APP\Learning::returnModules($module, $all_standard_modules);
				$all_standard_modules_list[] = \APP\Learning::returnAll($module, $issue, 'common');
			}

			foreach ($issue->usermodules as $usermodule) {
				$all_standard_modules = \APP\Learning::returnModules($usermodule, $all_standard_modules);
				$all_standard_modules_list[] = \APP\Learning::returnAll($usermodule, $issue, 'user');
			}
			foreach ($issue->evidencemodule as $evidencemodule) {
				$all_standard_modules = \APP\Learning::returnModules($evidencemodule, $all_standard_modules);
				$all_standard_modules_list[] = \APP\Learning::returnAll($evidencemodule, $issue, 'evidence');
			}
		}

		$all_standard_modules = array_unique($all_standard_modules);
		sort($all_standard_modules);
		if ($detach && !empty($all_standard_modules)) {
			\Models\UserLearningModule::linkResources($user->id, $all_standard_modules, 'update Standard Start Date - assign any missing resources - ' . $apprenticeshipstandard->id);
			\APP\Learning::syncUserResults($user->id);
		}

		// Need to update due time for learning results and set grace period.
		$updated_results = [];
		// Step 1: Build the exclusion list using associative array for faster lookup
		$exclude_ids = [];

		foreach ($all_standard_modules_list as $mod) {
			$pivot_module = false;
			if (isset($mod->issue_id)) {
				$pivot_module = \Models\ApprenticeshipIssuesLearningModules
					::where('apprenticeship_issues_id', $mod->issue_id)
					->where('learning_modules_id', $mod->id)
					->where('custom_work_window', true)
					->orderBy('start_day')
					->first();
				;
			}

			if ($pivot_module) {
				$exclude_ids[$mod->id] = true;

			}

		}

		// Step 2: Modify your existing loop
		foreach ($all_standard_modules_list as $module) {
			if ($module->is_course) {
				// Get all course resources, loop them and set due at also.
				foreach ($module->modules as $key => $lesson_resource) {
					// Exclude entry if $lesson_resource->id is in $exclude_ids
					if (isset($exclude_ids[$lesson_resource->id])) {
						continue; // Skip this iteration
					}

					// Proceed if not excluded
					$lesson_resource->issue_id = $module->issue_id;
					$lesson_resource->start_day = $module->start_day;
					$lesson_resource->end_day = $module->end_day;
					$lesson_resource->type = $module->type;
					\APP\Learning::updateStandardResourceDueAt($apprenticeshipstandard, $user, $standard_user, $lesson_resource, $updated_results, $module);
				}
			}
			\APP\Learning::updateStandardResourceDueAt($apprenticeshipstandard, $user, $standard_user, $module, $updated_results);
		}


		\Models\ApprenticeshipStandardUser::pluckDisabledOutcome($apprenticeshipstandard->id, $user->id);
	}

	public static function updateStandardResourceDueAt ($apprenticeshipstandard, $user, $standard_user, $module, &$updated_results, $course = false) {

		// Define start date.
		$start_at = $standard_user->start_at;

		// If user is on pause, find out how many days he spent before pause on programme and how many days he was on pause
		// When re-calculating due dates, if start or end day is after days_till_pause_start, add length_of_pause to those days
		$paused = false;
		if (
			$standard_user->paused == 1 &&
			$standard_user->paused_start &&
			$standard_user->paused_end
		) {
			$paused = true;

			$paused_start = $standard_user->paused_start;
			$paused_end = $standard_user->paused_end;

			//Calculate difference between start and start of pause
			$days_till_pause_start = \Carbon\Carbon::parse($start_at)->diffInDays(\Carbon\Carbon::parse($paused_start));

			// Calculate length of pause
			$length_of_pause = \Carbon\Carbon::parse($paused_start)->diffInDays(\Carbon\Carbon::parse($paused_end));
		}

		$learning_result = \Models\LearningResult
			::where('learning_module_id', $module->id)
			->where('user_id', $user->id)
			->where('refreshed', 0)
			->first()
		;

		if ($learning_result) {

			if (isset($module->issue_id)) {
				$learning_modules_id = $course ? $course->id : $module->id;
				$pivot_module = \Models\ApprenticeshipIssuesLearningModules
					::where('apprenticeship_issues_id', $module->issue_id)
					->where('learning_modules_id', $learning_modules_id)
					->where('custom_work_window', true)
					->orderBy('start_day')
					->first();
				;
			}

			$start_day = $module->start_day;
			$end_day = $module->end_day;

			if (
				isset($module->issue_id) &&
				$pivot_module &&
				$pivot_module->start_day &&
				$pivot_module->end_day
			) {
				$start_day = $pivot_module->start_day;
				$end_day = $pivot_module->end_day;
			}

			// Check if completion_date_custom is present, if so, check if completion_date_custom_days is present, if not, calculate it.
			if (
				$learning_result->completion_date_custom &&
				!$learning_result->completion_date_custom_days
			) {
				$learning_result->completion_date_custom_days = \Carbon\Carbon::parse($start_at)->diffInDays(\Carbon\Carbon::parse($learning_result->completion_date_custom), false);
			}

			if ($paused) {
				if ($start_day > $days_till_pause_start) {
					$start_day = $start_day + $length_of_pause;
				}
				if ($end_day > $days_till_pause_start) {
					$end_day = $end_day + $length_of_pause;
				}

				// if completion_date_custom_days is bigger than days_till_pause_start
				// and completion_date_custom is inside pause, adjust completion_date_custom
				if (
					$learning_result->completion_date_custom_days > $days_till_pause_start
				) {
					// Add length of the pause to completion_date_custom, not to start_at
					$learning_result->completion_date_custom = \Carbon\Carbon::parse($learning_result->completion_date_custom)->addDays($length_of_pause);
				}
			// Reset completion_date_custom back to normal
			} else if (
				$learning_result->completion_date_custom &&
				\Carbon\Carbon::parse($start_at)->diffInDays(\Carbon\Carbon::parse($learning_result->completion_date_custom), false) > $learning_result->completion_date_custom_days
			) {
				$learning_result->completion_date_custom = \Carbon\Carbon::parse($start_at)->addDays($learning_result->completion_date_custom_days);
			}

			$due_at = \Carbon\Carbon::parse($start_at)->addDays($start_day);
			$grace_at = \Carbon\Carbon::parse($start_at)->addDays($end_day);



			// For due_at: Use earliest date (most restrictive) when resource is linked to multiple criteria
			if (
				(\Carbon\Carbon::parse($learning_result->due_at) > $due_at ||
					empty($learning_result->due_at) ||
				!in_array($learning_result->id, $updated_results)) && (!$learning_result->Module->is_skill)
			) {
				$learning_result->due_at = $due_at;
			}

			// For grace_at: Use latest date (most generous) when resource is linked to multiple criteria
			if (
				\Carbon\Carbon::parse($learning_result->grace_at) < $grace_at ||
				empty($learning_result->grace_at) ||
				!in_array($learning_result->id, $updated_results)
			) {
				$learning_result->grace_at = $grace_at;
			}


			// If type is not common, due_at and grace_at will be start of programme and end of programme.
			if ($module->type !== 'common') {
				$learning_result->due_at = \Carbon\Carbon::parse($start_at);
				$learning_result->grace_at = \Carbon\Carbon::parse($start_at)->addDays(($apprenticeshipstandard->completion_months * 30));
			}

			//$learning_result->custom_due_at = true; // switch to save custom due_at times.
			$learning_result->saveWithoutEvents();

			$updated_results[] = $learning_result->id;
		}
	}

	public static function multiAttemptsAtQuizNotification($settings)
	{
		$maxAttemptsAtQuiz = \APP\Tools::getConfig('MaxAttemptsatQuiz');
		if ($maxAttemptsAtQuiz > 0) {
			$notifications_sent = 0;
			$data = \Models\LearningModule
				::whereHas('LearningResults', $condition = function($query) use ($maxAttemptsAtQuiz) {
					$query
						->where('completion_status', '!=', 'completed')
						->where('passing_status', '!=', 'passed')
						->where('refreshed', 0)
						->where('quiz_fails', '>=', $maxAttemptsAtQuiz)
						->whereHas('User', function($query) {
							$query
								->where('status', 1)
							;
						})
						->whereExists(function ($query) {
							$query->select(DB::raw(1))
								->from('user_learning_modules')
								->whereRaw('user_learning_modules.user_id = learning_results.user_id')
								->whereRaw('user_learning_modules.learning_module_id = learning_results.learning_module_id')
								->whereRaw('user_learning_modules.deleted_at is null')
							;
						})
					;
				})
				->with(['LearningResults' => $condition,'LearningResults.User'])
				->where('learning_modules.status', true)
				->where('learning_modules.track_progress', true)
				->where(function ($query) {
					$query
						->whereNull('learning_modules.expiration_date')
						->orWhere('learning_modules.expiration_date', '>', \Carbon\Carbon::now())
					;
				})
				->get()
			;

			$template = \Models\EmailTemplate::getTemplate('multi_attempts_at_quiz_notification');
			if ($template) {
				foreach($data as $module) {
					foreach($module->LearningResults as $result) {
						if ($result->quiz_fails > $result->last_queue_fail) {
							$email_queue = new \Models\EmailQueue;
							$email_queue->email_template_id = $template->id;
							$email_queue->recipients = [intval($result->user_id)];
							$email_queue->approved = true;
							$email_queue->custom_variables = json_encode([
								'user' => $result->User->fname.' '.$result->User->lname,
								'resource_name' => $module->name,
							]);
							$email_queue->save();
							$result->last_queue_fail = $result->quiz_fails;
							$result->saveWithoutEvents();
							$notifications_sent++;
						}
					}
				}
			}
			return "Notifications sent: " . $notifications_sent;
		};
	}
}
