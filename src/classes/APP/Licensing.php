<?php

/*

	This file will determine branding and features for site, based on "version" value from configuration table.

*/

namespace APP;

use Illuminate\Database\Capsule\Manager as Capsule;

class Licensing
{

	static public function allowedVersions()
	{
		return ["openelms", "openelmstms", "omniprez", "smcrsolution", "apprentix", "openelmsschools", "openelmscolleges", "openelmsuniversities", "openelmsbusiness", "nras"];
	}

	public static function defaultCompanies()
	{
		return [
			"openelms" => [],
			"openelmstms" => [],
			"omniprez" => [],
			"smcrsolution" => [],
			"apprentix" => [],
			"openelmsschools" => [
				'Form A',
			],
			"openelmscolleges" => [
				'Form A',
			],
			"openelmsuniversities" => [
				'Maths',
			],
			"openelmsbusiness" => [],
			"nras" => [],
		];
	}

	public static function defaultJobs()
	{
		return [
			"openelms" => [],
			"openelmstms" => [],
			"omniprez" => [],
			"smcrsolution" => [],
			"apprentix" => [],
			"openelmsschools" => [
				'Year 1',
				'Year 2',
			],
			"openelmscolleges" => [
				'Year 1',
				'Year 2',
			],
			"openelmsuniversities" => [
				'Year 1',
				'Year 2',
			],
			"openelmsbusiness" => [],
			"nras" => [],
		];
	}

	public static function roleNames()
	{
		return [
			"openelms" => [
				[
					'name' => 'Manager',
					'status' => true,
				],
				[
					'name' => 'Quality Assurer',
					'status' => true,
				],
				[
					'name' => 'Financial Auditor',
					'status' => false,
				],
			],
			"openelmstms" => [
				[
					'name' => 'Manager',
					'status' => true,
				],
				[
					'name' => 'Quality Assurer',
					'status' => true,
				],
				[
					'name' => 'Financial Auditor',
					'status' => false,
				],

			],
			"omniprez" => [
				[
					'name' => 'Manager',
					'status' => true,
				],
				[
					'name' => 'Quality Assurer',
					'status' => true,
				],
				[
					'name' => 'Financial Auditor',
					'status' => false,
				],

			],
			"smcrsolution" => [
				[
					'name' => 'Compliance Manager',
					'status' => true,
				],
				[
					'name' => 'Inspector',
					'status' => true,
				],
				[
					'name' => 'Financial Auditor',
					'status' => false,
				],
			],
			"apprentix" => [
				[
					'name' => 'Coach/Trainer',
					'status' => true,
				],
				[
					'name' => 'Quality Assurer',
					'status' => true,
				],
				[
					'name' => 'Financial Auditor',
					'status' => true,
				],
			],
			"openelmsschools" => [
				[
					'name' => 'Teacher',
					'status' => true,
				],
				[
					'name' => 'QA/Inpsector',
					'status' => true,
				],
				[
					'name' => 'Bursar',
					'status' => true,
				],
			],
			"openelmscolleges" => [
				[
					'name' => 'Tutor',
					'status' => true,
				],
				[
					'name' => 'QA/Inpsector',
					'status' => true,
				],
				[
					'name' => 'Bursar',
					'status' => true,
				],
			],
			"openelmsuniversities" => [
				[
					'name' => 'Lecturer',
					'status' => true,
				],
				[
					'name' => 'Quality Assurer',
					'status' => true,
				],
				[
					'name' => 'Bursar',
					'status' => true,
				],
			],
			"openelmsbusiness" => [
				[
					'name' => 'Manager',
					'status' => true,
				],
				[
					'name' => 'Quality Assurer',
					'status' => true,
				],
				[
					'name' => 'Financial Auditor',
					'status' => false,
				],
			],
			"nras" => [
				[
					'name' => 'Coach/Trainer',
					'status' => true,
				],
				[
					'name' => 'Quality Assurer',
					'status' => true,
				],
				[
					'name' => 'Financial Auditor',
					'status' => true,
				],
			],
		];
	}

	static public function setUp($settings = false)
	{

		if (!$settings) {
			die('catasthropical error!');
		}
		$version = \Models\Configuration::where("key", "version")->first();

		$allowed_version = \APP\Licensing::allowedVersions();

		if (
			$version &&
			$version->value &&
			in_array($version->value, $allowed_version)
		) {
			$site_version = $version->value;
		} else {
			$site_version = 'openelmstms';
		}

		$isJackdawCloud					=	$settings['licensing']['isJackdawCloud'];
		$customLogo							=	false;
		$overwriteModuleType				=	[];

		$hide_resource_type = [1, 2, 3, 4, 5, 6, 7, 8, 9];
		// Hide by default, unhide for specific versions
		$hide_menu_items = [
			// API integration is mostly unfinished, no need to show except for specific versions.
			"system-setup-api-salesforce",
			"system-setup-custom-programme-statuses",
			"custom-learning-resources-types",

			"library-learning-programmes", // Hidden from new dashboard interface
			"review-learning-programmes", // same

			// SMCR stuff
			"system-setup-organisation-staff-type",
			"system-setup-organisation-functions",
			"system-setup-organisation-responsibilities",
			"system-setup-organisation-committees",
			"system-setup-f-p-categories",

			// Jackdaw cloud
			"system-setup-learning-distribution",
			"system-setup-learning-free-jackdaw-resources",

			// Holiday system for schools/col/uni
			"system-setup-defaults-holidays",
		];

		// Default labels, overwrite in specific site versions as needed!
		$initial_labels = [
			'version_name' => 'Open eLMS',
			'programme' => "Programme",
			'programmes' => "Programmes",
			'define_programmes' => "Define programmes of learning",
			'add_learners' => "Add Learners",
			'add_missing_learners' => "Add missing Learners",
			'add_learners_and_import' => "Add learners and import records",
			'add_learners_fa_description' => "Import Individual Learning Records ILRs) from the Hib or 3rd-party software",
			'lesson' => "Lesson",
			'lessons' => "Lessons",
			'lesson_resources' => "Lesson Resources",
			'learning_resource' => "Learning Resource",
			'learning_resources' => "Learning Resources",
			'check_managers' => "Check Managers",
			'review_managers' => "Review your managers",
			'assign_learning' => "Assign Learning",
			'match_learning' => "Match learning to learners",
			'add_to_learning_library' => "Add to your learning library",
			'sign_off' => "Sign off",
			'sign_off_description' => "Learning and uploads which require sign off",
			'sign_off_video_help' => "",
			'sign_off_button' => "Sign Off",
			'check_progress' => "Check Progress",
			'check_progress_description' => $settings['sharedClients'] ? "Attach learning and check progress" : "Check data and assign learning",
			"check_progress_video_help" => "",
			'qa' => "QA",
			'qa_description' => "Review detected quality issues",
			'approve_and_manage_booking_video_help' => 'https://www.youtube.com/embed/M6Lvww1y19Y?start=380',
			'users' => 'Users',
			'user' => 'User',
			'companies' => 'Companies',
			'company' => 'Company',
			'departments' => 'Departments',
			'department' => 'Department',
			'locations' => 'Locations',
			'location' => 'Location',
			'jobs' => 'Jobs',
			'job' => 'Job',
			'country' => 'Country',
			'countries' => 'Countries',
			'city' => 'City',
			'cities' => 'Cities',
			'managers' => 'Managers',
			'manager' => 'Manager',
			'quality_assurer' => 'Quality Assurer',
			'financial_auditor' => 'Financial Auditor',
			'curriculum_developer' => 'Curriculum Developer',
			'learner' => 'Learner',
			'manage_learning_desc' => 'Select any of the buttons below to manage learning across your organisation. All major day to day operations are carried out here.',
			'lessons_and_learning_resources' => 'Lessons and Learning Resources',
			'learners_name' => 'Learner\'s Name',
			'view_learning_resource' => 'View Learning Resource',
			'add_learning' => 'Add Learning',
			'add_learning_desc' => 'Select further learning resource(s) from the library specifically for this learner.',
			'reviews' => 'Reviews',
			'reviews_desc' => 'Record the results of any validation or coaching visits.',
			'existing_uploads__title' => 'Select an existing learning resource or add new',
			'existing_uploads__dropbox' => 'Select an existing learning resource to upload to ...',
			'existing_uploads__new' => 'Or add new learning resource',
			'existing_uploads__new_button' => 'Add New Upload',
			'coach_trainer_manager' => 'Coach/Trainer/Manager',
			'last_learning_access_date' => 'Last Learning Access Date',
			'last_learning_update_date' => 'Last Learning Update Date',
			'view_assessment_task' => 'View Assessment Task',
			'view_assessment' => 'View Assessment',
			'task_name' => 'Task Name',
			'assessment_task_status__not_started' => 'Not Started',
			'assessment_task_status__deferred' => 'Deferred',
			'assessment_task_status__in_progress' => 'In Progress',
			'assessment_task_status__completed' => 'Completed',
			'resource_type__upload' => 'Upload',
			'resource_type__webpage' => 'Webpage',
			'resource_type__webpage_launch_button' => 'Open link in new window',
			'resource_type__evidence' => 'Evidence',
			'resource_type__evidence_name' => 'Enter a name for the upload',
			'resource_type__evidence_type' => 'Select a type as shown',
			'competencies' => 'Competencies',
			'sign_off_manager' => 'Coach/Trainer',
			'expected_completion_data' => 'Lesson time or expected completion time',
			'assign_from_learning_library_small' => 'Assign lesson plans, homework and projects which are design for reuse.',
			'assign_one_off_project' => 'Assign One-Off Homework',
			'assessment_notification' => 'Assessment Notification',
			'registration_email' => 'Open eLMS registration',
			'schedule_add_lessons' => 'Add Training Session',
			'assign_work_bulk' =>  'To Learning Programme',
			'assign_work_bulk_title' =>  'Add Learning',
			'bulk_evidence' =>  'Import Evidence',
			'learner_menu__calendar' => 'Lessons',
			'learner_menu__homework' => 'Homework',
			'learner_menu__resources' => 'Learning',
			'learner_menu__progress' => 'Progress',
			'learner_menu__dashboard' => 'Dashboard',

			'emergency_contant__details' => 'Emergency contact details',
			'emergency_contant__name' => 'Name',
			'emergency_contant__relationship' => 'Relationship',
			'emergency_contant__number' => 'Contact Number/s',
			'missing_fields_in_your_profile' => 'Missing fields in your profile!',
			'resource_type__evidence' => 'Evidence',
			'resource_type__evidence_name' => 'Enter a name for the upload',
			'resource_type__evidence_type' => 'Select a type as shown',
			'learner__favorite_add' => 'Favourite learning',
			'learner__favorite_remove' => 'Remove Learning from favourites',
			'learner__favorite_show' => 'Show only Favorites',
			'mandatory_training' => 'Compulsory Training',
			'mandatory' => 'Compulsory',
			'duration' => 'Duration',
			'add_lessons'=>'Add Learning Activities',
			'event_add_managers' => 'Managers',
			'information_only' => 'information only',
			'event' => 'Event',
			'events' => 'Events',
			'learner_profile_programme_heading' => 'Learning Programmes (Apprenticeships, ESFA, ESF, Adult Skills, Education Qualifications etc.)',
			'learner_profile__events_description' => 'This is a list of all lessons, webinars and meetings the leaner has signed up for.',
			'learner_profile__leaderboard_description' => 'Compare how you are doing in terms of CPD points earned compared to your cohorts.',
			'learner_profile__badges_description' => 'These badges can be taken with each learner and posted on your LinkedIn account.',
			'learner_profile__comments_description' => 'All comments made by management are collated here for analysis. ',
			'learner_profile__forms_description' => 'These forms have been assigned to you.',
			'learner_profile__workflows_description' => 'Any reports connected with completed forms are listed below.',
			'event_completion_state_completed' => 'Attended',
			'event_completion_state_not_attempted' => 'Not Attended',
			'event_completion_state_in_progress' => 'In Progress',
			'learner__favorite_add' => 'Favourite learning',
			'learner__favorite_remove' => 'Remove Learning from favourites',
			'learner__favorite_show' => 'Show only Favorites',
			'event__import_attendees' => 'Import Attendees',
			'assign_lesson' => "Assign Learning",
			'cost' => 'Cost',
			'cost_currency' => 'Cost £',
			'currency' => '£',
			'completion_date' => 'Completion Date',

			'form' => 'Form',
			'forms' => 'Forms',
			'forms__sign_off' => 'Forms Sign Off',
			'forms__awaiting_sign_off' => 'Forms awaiting sign-off',

			'custom_reports' => 'Custom Reports',
			'custom_report' => 'Custom Reports',
			'distribute' => 'Distribute',
			'learner_menu__reports' => 'Reports',
			'administration_menu__reports' => 'Reports',
			'administration_menu__reports_description' => 'Assigned Reports',
			'certificate__this_certifies' => 'This certifies that',
			'certificate__has_completed' => 'Has completed the learning:',
			'certificate__has_completed_programme' => 'Has completed the programme:',
			'certificate__has_achieved' => 'and has achieved a score of:',
			'certificate__percent' => 'percent.',
			'certificate__date_of_completion' => 'Date of completion:',

			'learning_resource__disable_upon_completion' => 'Remove access to resource upon completion (e.g. to be used for exams)',
			'add_to_schedule_of_listed_learners_managers' => "Add to schedule of listed learners managers",
			'resource__periodically_repeat' => 'Periodically repeat',
			'resource__periodically_repeat_description' => '(e.g. refresher training or skill scans)',
			'resource__days_till_refresh' => 'Days until refresher required',
			'resource__days_till_refresh_description' => '(leave blank if not repreated)',
			'resource__period_till_refresh' => 'Period until refresher required',
			'resource__period_till_refresh_description' => '(leave blank if not repreated)',
			'resource__refresh_date' => 'Start Refreshing Date (Optional)',
			'resource__refresh_date_description' => '(Will start refreshing completed entries from this date onwards.)',
			'resource__number_of_times_refresher_training_should_be_repeated' => 'Number of times refresher training should be repeated',
			'resource__number_of_times_refresher_training_should_be_repeated_description' => '(leave blank if training should be repeated indefinitely)',
			'resource__customise_refresh_email' => 'Customise refresh email',
			'resource__customise_refresh_email_description' => '(do not check to send standard email)',
			'resource__customise_refresh_email__subject_line' => 'Subject line',
			'resource__customise_refresh_email__body' => 'Body',
			'resource__customise_refresh_email__unsaved_warning' => 'Email template is not yet saved in database against this resource, please save your changes when ready to use custom template.',
			'resource__copy_refresher_emails_to_line_managers' => 'Copy Refresher Emails to Line Managers',

			'lesson__periodically_repeat' => 'Periodically repeat',
			'lesson__periodically_repeat_description' => '(e.g. refresher training or skill scans)',
			'lesson__days_till_refresh' => 'Days until refresher required',
			'lesson__days_till_refresh_description' => '(leave blank if not repreated)',
			'lesson__period_till_refresh' => 'Period until refresher required',
			'lesson__period_till_refresh_description' => '(leave blank if not repreated)',
			'lesson__period_till_refresh_type' => 'Refreshed period type',
			'lesson__refresh_date' => 'Start Refreshing Date (Optional)',
			'lesson__refresh_date_description' => '(Will start refreshing completed entries from this date onwards.)',
			'lesson__number_of_times_refresher_training_should_be_repeated' => 'Number of times refresher training should be repeated',
			'lesson__number_of_times_refresher_training_should_be_repeated_description' => '(leave blank if training should be repeated indefinitely)',
			'lesson__refresh_all_attached_learning_resources' => 'Refresh all attached learning resources',
			'lesson__customise_refresh_email' => 'Customise refresh email',
			'lesson__customise_refresh_email_description' => '(do not check to send standard email)',
			'lesson__customise_refresh_email__subject_line' => 'Subject line',
			'lesson__customise_refresh_email__body' => 'Body',
			'lesson__copy_refresher_emails_to_line_managers' => 'Copy Refresher Emails to Line Managers',

			'password_pattern_error_message' => 'Password should be minimum 8 characters in length, should contain 1 uppercase letter, 1 lower case letter and 1 number.',

			'learner__event__start_date' => 'Starts',
			'learner__event__end_date' => 'Ends',
			'password_reset_anonymous_message' => 'We have emailed a password recovery link to the provided address, if it exists in our system. Please check your inbox for further instructions.',
			'open_introduction_in_new_window_message' => 'Open introduction in new window.',
			'reports__registration_date' => 'Registration date',
			'reports__expiry_date' => 'Expiry Date',
			'legacy_custom_reviews__load_report_description' => 'Please choose report available from the drop down list and press \'Load Report\' button to view.',
			'workflow' => 'Form Reporting',
			'launch_resource_text_enrol' => 'Enrol',
			'launch_resource_text__waiting_approval' => 'Waiting Approval',

			'target_catalogue' => 'Target Catalogue',
			'delivery_provider_type' => 'Delivery Provider Type',
			'group_department_code' => 'Group/Department Code',

			'lesson__do_not_reset_completion_state_if_completed' => 'Do not reset completion state if completed',
			'category' => 'Category',
			'categories' => 'Categories',
			'group' => 'Group',
			'groups' => 'Groups',
			'watch' => 'Watch',
			'payment_status' => 'Payment Status',
			'payment_status__pay' => 'Pay',
			'payment_status__pay_and_approve' => 'Pay and Approve',
			'payment_status__paid' => 'Paid',
			'payment_status__not_paid' => 'Not Paid',
			'payment_status__n_a' => 'N/A',
			'assing_learning' => 'Assign Learning',
			'organisation_menu__reports_description' => 'The system needs to be set up with your learner and coach/trainer population prior to commencing. Consider using import and single-sign on options to help with this.',
			'administer_learning_menu__reports_description' => 'Manage the day to day processes around setting up learners and trainers on the system and setting up and verifying training programmes.',
			'check_learning_menu__reports_description' => 'The Quality Assessor should check a sample of learning resources from each learning programme for assessment and schedule periodic QA Reports.',
			'learning_menu__reports_description' => 'These parameters effect how the learning is rolled out.',
			'defaults_menu__reports_description' => 'Review these default values to ensure they reflect your organisations requirements.',
			'api_menu__reports_description' => 'Integrate 3rd party Application programming interfaces.',
			'graph_menu__reports_description' => 'Data views have specific functions to export, report and alert learners and coach/trainers of actions or review progress. Select a predefined data view or create your own.',
			'report_builder_menu__reports_description' => 'Data views have specific functions to export, report and alert learners and coach/trainers of actions or review progress. Select a predefined data view or create your own.',
			'home_menu__reports_description' => 'Select the role you wish to carry out today ...',
			'learning_programmes_menu__reports_description' => 'Create, edit and assign learning programmes to your learner population here. Examples include apprenticeship standards and educational courses.',
			'refresh_only_if_learning_meets_query' => 'Refresh only if learning meets query below',
			'further_customise_this_query' => 'Further customise this query',

			'resource_completion_state__completed' => 'Completed',
			'resource_completion_state__not_started' => 'Not Started',
			'resource_completion_state__in_progress' => 'In Progress',
			'resource_completion_state__failed' => 'Failed',
			'learning_filter__prerequisite' => 'Requires precursor training to unlock',
			'learning_filter__enrollment' => 'Available for enrollment',
			'learning_filter__refreshed_training_scheduled' => 'Refresher training scheduled',
			'learning_filter__compulsory' => 'Compulsory',
			'learning_filter__favorites' => 'Show only Favorites',

			'employee__total_learning_resources' => 'Total Learning resources',
			'employee__days_since_contact' => 'Days since contact',
			'employee__days_since_review' => 'Days since review',
			'employee__days_since_submitted_work' => 'Days since Submitted Work',
			'employee__total_time_spent' => 'Total time spent',
			'employee__percentage_completed' => '% Completed',
			'sign_off_required_by_role' => 'Sign off required by role',
			'make_payment' => 'Make Payment',
			'transaction_completed_successfully' => 'Transaction Completed Successfully',
			'transaction_not_completed' => 'Transaction Not Completed',
			'learner__learning_reset__title' => 'This learning has already been completed.',
			'learner__learning_reset__description' => 'This learning has already been completed, do you wish to reset the status and clear your quiz score to record a new training record (e.g in the case of periodic refresher training)?',
			'learner__learning_reset__confirm' => 'Reset Status',
			'learner__learning_reset__existing' => 'No - Access Existing Record',
			'available_for_assigning_only' => 'Available for assigning only',
			'available_for_self_enrolment_only' => 'Available for self enrolment only',
			'learner__learning_reset__cancel' => 'No',
			'learner__learning_reset__accept' => 'Accept',
			'docs_bot_tooltip' => 'AI Powered Help Assistant',
			'learner__completed_version_mismatch__confirm' => 'This Completed version of the resource is no longer available. Would you like to reset your status and activate the new version?',
			'learner__completed_version_mismatch__warning' => 'This version of the resource no longer exists as a new version has been made active. Please contact your system administrator to gain access if required.',
			'user_could_not_be_retrieved' => 'Can\'t retrieve user',
			'user_could_not_be_retrieved_description' => 'You don\'t have permission to view this user.',
			'open_in_events_only' => 'Open in events only',
			'learner_sign_off__approval' => 'Your sign off is submitted, your Coach/Trainer will be notified to confirm.',
			'learner_sign_off__confirmed' => 'Your sign off is submitted.',
			'programme_status__learner_file' => 'Programme Status',
			'learner_event_enroll_linked_events' => 'This event has the following events linked to it, to which you will also be assigned',
			'learner_event_enroll_linked_events_alternative' => 'If you are unable to attend these linked sessions, please cancel your request and book an alternative event.',
			'national_insurance_number' => 'National Insurance Number',
			'national_insurance_pattern_error' => 'A valid national insurance number in the format XXnnnnnnX, where X is alphabetic and n is numeric. The first character of the NI number must not be D, F, I, Q, U or V, the second character must not be D, F, I, O, Q, U or V, characters 3 to 8 must be numeric and character 9 must be A, B, C, D.',
			'events__new_event_403_error' => 'You do not have permission to create a new event.',
			'user_profile__assign_403_error' => 'You do not have permission to assign resource.',
			'user_profile__create_assign_403_error' => 'You do not have permission to create and assign resource.',
			'events__venue_already_booked' => 'Venue Already booked. Please change time/date',
			'failed_learning__re_attempt' => 'Re-Attempt Learning',
			'events_do_not_send_advance_notifications' => 'Do not send Advance Notifications',
			'lesson_resorce__completed_by' => 'Completed on',
			'learner_interface__special_requirements' => 'Any special requirements? Please enter details here.',
			'grade' => 'Grade',
			'grade__loading_prompt' => 'Loading prompt...',
			'grade__loading_scheme_defaults' => 'Loading scheme defaults...',
			'grade__loading_all_schemes' => 'Loading all schemes...',
			'grade__generating_question' => 'Generating question...',
			'grade__generating_prompt' => 'Generating prompt...',
			'grade__address_criteria' => 'Address whether the content adequately addresses the criteria:',
			'grade__apply_grading_scheme' => 'Apply the following grading scheme:',
			'grade__error_generating_question' => 'Error generating question',
			'grade__error_loading_configuration' => 'Error loading configuration',
			'grade__evidence_is_being_graded' => 'Evidence is being graded, please wait...',
			'grade__error_generating_grade' => 'Error generating grade',
			'grade__confirm_marking_comments' => 'Confirm Marking Comments',
			'grade__grade_is_required' => 'Grade is required',
			'grade__confirm_grade_score' => 'Confirm Grade Score:',
			'grade__confirm_grade_level' => 'Confirm Grade Level:',
			'title__landing_page' => 'Landing Page',
			'title__login' => 'Login',
			'title__progress' => 'Progress',
			'title__my_profile' => 'My Profile',
			'events__authorised' => 'Authorised',
			'events__not_authorised' => 'Not Authorised',
			'user_timings__working_week_hours' => 'Working Week hours',
			'user_timings__expiry_date' => 'Expiry Date',
			'user_timings__registration_date' => 'Registration Date',
			'user_timings__created_at' => 'Created at',
			'user_timings__created_by' => 'Created by',
			'chat_bot__title' => 'Q&A',
			'chat_bot__box_subtitle' => 'Let me answer your questions on your organization\'s knowledge.',
			'chat_bot__suggestion_question_1' => 'What can I help you with?',
			'chat_bot__suggestion_question_2' => 'Not sure what to ask?',
			'chat_bot__suggestion_1' => 'Summarise the learning available in the learning library',
			'chat_bot__suggestion_2' => 'Tell me my HR responsibilities',
			'chat_bot__suggestion_3' => 'Tell me something interesting about the learning in this LMS',
			'learner_menu__cart' => 'Cart',
		];

		switch ($site_version) {
			case 'openelms':
				$isOpenElms					=	true;
				$isApprentix				=	false;
				$isOmniPrez					=	false;
				$isOpenElmsLibrary		=	true;
				$isOpenElmsTMS				=	false;
				$isVirtualClassrooms		=	false;
				$isSMCR						=	false;
				$manual						=	'//www.e-learningwmb.com/page/support-business';
				$welcomeMessage			=	'Welcome to Open eLMS - the business focussed Learning Content Management System from Open eLMS.';
				$learner_introduction	= '//www.e-learningwmb.com/videos/OpeneLMSLearnerBusiness.mp4';
				$labels = $initial_labels;

				break;

			case 'openelmstms':
				$isOpenElms					=	true;
				$isApprentix				=	false;
				$isOmniPrez					=	false;
				$isOpenElmsLibrary		=	true;
				$isOpenElmsTMS				=	true;
				$isVirtualClassrooms		=	true;
				$isSMCR						=	false;
				$manual						=	'//www.e-learningwmb.com/page/support-business';
				$welcomeMessage			=	'Welcome to Open eLMS TMS - the business focussed Learning Content Management System for managing learning both offline and online from Open eLMS.';
				$learner_introduction	= '//www.e-learningwmb.com/videos/OpeneLMSLearnerBusiness.mp4';
				$labels = [
					'registration_email' => 'Open eLMS for TMS registration',
				];
				$labels = array_merge($initial_labels, $labels);


				break;

			case 'omniprez':
				$isOpenElms					=	true;
				$isApprentix				=	false;
				$isOmniPrez					=	true;
				$isOpenElmsLibrary		=	true;
				$isOpenElmsTMS				=	false;
				$isVirtualClassrooms		=	true;
				$isSMCR						=	false;
				$manual						=	'//www.e-learningwmb.com/page/support-business';
				$welcomeMessage			=	'Welcome to Open eLMS Business - the business focussed Learning Content Management System for managing learning both offline and online from Open eLMS.';
				$learner_introduction	= '//www.e-learningwmb.com/videos/OpeneLMSLearnerBusiness.mp4';
				$labels = [
					'registration_email' => 'Open eLMS for Business registration',
				];
				$labels = array_merge($initial_labels, $labels);

				break;

			case 'smcrsolution':
				$isOpenElms					=	true;
				$isApprentix				=	false;
				$isOmniPrez					=	false;
				$isOpenElmsLibrary		=	true;
				$isOpenElmsTMS				=	true;
				$isVirtualClassrooms		=	true;
				$isSMCR						=	true;
				$manual						=	'//www.e-learningwmb.com/page/support-smcr-solution';
				$welcomeMessage			=	' Welcome to the SMCR Solution - a suite of cloud based learning and auditing solutions to enable compliance with the Senior Managers and Certification Regime (SMCR) - the system will help define your role and give you the proper training and assessment to match. <br><br>
					The SMCR Solution (and any information accessed through links in the solution) is provided for information purposes only and does not constitute legal advice. Professional legal advice should be obtained before taking or refraining from any action as a result of the contents of any element of the SMCR Solution.
				';
				$learner_introduction	= '//www.e-learningwmb.com/videos/OpeneLMSLearnerBusiness.mp4';
				$overwriteModuleType = [
					'Upload' => "Evidence"
				];
				$labels = [
					'version_name' => 'SMCR',
					'define_programmes' => "Define F&P Activity programmes",
					'add_learners' => "Add Individuals",
					'add_learners_and_import' => "Add conduct rule, certification staff and senior managers",
					'add_learners_fa_description' => "Import records from 3rd-party software",
					'learning_resource' => "F&P Activity",
					'learning_resources' => "F&P Activities",
					'check_managers' => "Check Performance",
					'review_managers' => "Review your Compliance Manager performance.",
					'assign_learning' => "Assign Users",
					'match_learning' => "Offer additional F&P Activity to staff.",
					'add_to_learning_library' => "Add and edit related F&P Activities",
					'sign_off' => "F&P Activities",
					'sign_off_description' => "F&P Activities which require sign off",
					'check_progress' => "Certifications",
					'check_progress_description' => "Check on individuals\' F&P Activity progress or send bulk emails to individuals falling behind",
					'qa' => "Committee Memberships",
					'qa_description' => "Ensure all SMCR positions are filled and accepted by those attending.",
					'users' => 'Individuals',
					'user' => 'Individual',
					'managers' => 'Compliance Managers',
					'manager' => 'Compliance Manager',
					'quality_assurer' => 'Inspector',
					'learner' => 'Individual',
					'manage_learning_desc' => 'Select any of the features below to assign and manage functions and responsibilities across your organisation.',
					'lessons_and_learning_resources' => 'Fitness and Propriety Assessment',
					'learners_name' => 'Name',
					'view_learning_resource' => 'View Assessment Result',
					'add_learning' => 'Add F&P Activity',
					'add_learning_desc' => 'Select a request for evidence or additional learning necessary for certification.',
					'reviews_desc' => 'Schedule and record any visits relating to certification.',
					'existing_uploads__title' => 'Select existing evidence or upload new',
					'existing_uploads__dropbox' => 'Select existing evidence to upload to ...',
					'existing_uploads__new' => 'Or add new evidence',
					'existing_uploads__new_button' => 'Add New evidence',
					'coach_trainer_manager' => 'Compliance Manager',
					'last_learning_access_date' => 'Last Activity Access Date',
					'view_assessment_task' => 'View Self Attestation Task',
					'view_assessment' => 'View Self Attestation',
					'task_name' => 'Identified Issue',
					'assessment_task_status__not_started' => 'Issue Identified',
					'assessment_task_status__deferred' => 'Referred to Another',
					'assessment_task_status__in_progress' => 'Outstanding Issue',
					'assessment_task_status__completed' => 'Resolved',
					'resource_type__upload' => 'Evidence',
					'competencies' => 'Category',
					'sign_off_manager' => 'Manager',
					'assessment_notification' => 'Attestation Notification',
					'registration_email' => 'Open eLMS for SMCR Solution registration',
					'learner_menu__resources' => 'Resources',
					'learner_menu__dashboard' => 'Dashboard',
					'assign_lesson' => "Assign Users",

					'resource__periodically_repeat' => 'Repeat for every certification',
					'resource__periodically_repeat_description' => '(i.e. status reset once certified)',

				];
				$labels = array_merge($initial_labels, $labels);

				break;

			case 'apprentix':
				$isOpenElms					=	true;
				$isApprentix				=	true;
				$isOmniPrez					=	false;
				$isOpenElmsLibrary		=	true;
				$isOpenElmsTMS				=	true;
				$isVirtualClassrooms		=	true;
				$isSMCR						=	false;
				$manual						=	'//www.e-learningwmb.com/page/support-apprenticeships';
				$welcomeMessage			=	'Welcome to Apprentix - the learning and e-portfolio management system for Apprenticeships from Open eLMS.';
				$learner_introduction	= '//www.e-learningwmb.com/videos/OpeneLMSLearnerApprenticeships.mp4';
				$labels = [
					'version_name' => 'Apprentix',
					'programme' => "Standard",
					'programmes' => "Standards",
					'add_learners_and_import' => "Import ILR records from Hub or Software",
					'review_managers' => "Review your coach/trainers",
					'sign_off_video_help' => "https://www.youtube.com/embed/0iG_w-JNuaI?start=85",
					'check_progress' => "Assign Work",
					'check_progress_description' => "Set Project work, check learning and ILRs",
					"check_progress_video_help" => "https://www.youtube.com/embed/0iG_w-JNuaI",
					'qa_description' => "These issues have been reviewed by a Quality Assessor and require attention.",
					'qa_video_help' => "https://www.youtube.com/embed/0iG_w-JNuaI?start=66",
					'users' => 'Apprentices',
					'user' => 'Apprentice',
					'managers' => 'Coach/Trainers',
					'manager' => 'Coach/Trainer',
					'expected_completion_data' => 'Expected Completion Date',
					'assign_from_learning_library_small' => 'Assign learning and predefined projects which are designed for reuse',
					'assign_one_off_project' => 'Assign One-Off Project',
					'registration_email' => 'Open eLMS for Apprentix registration',
					'assign_evidance_bulk' =>  'Bulk Evidence',
					'learner_menu__calendar' => 'Calendar',
					'learner_menu__homework' => 'Tasks',
				];
				$labels = array_merge($initial_labels, $labels);

				break;

			case 'openelmsschools':
				$isOpenElms					=	true;
				$isApprentix				=	true;
				$isOmniPrez					=	false;
				$isOpenElmsLibrary		=	true;
				$isOpenElmsTMS				=	true;
				$isVirtualClassrooms		=	true;
				$isSMCR						=	false;
				$customLogo 				=	true;
				$manual						=	'//www.e-learningwmb.com/page/support-schools';
				$welcomeMessage			=	'Welcome to Open eLMS for Schools - the complete education management system from Open eLMS.';
				$learner_introduction	= '//www.e-learningwmb.com/videos/OpeneLMSLearnerSchools.mp4';
				$overwriteModuleType = [
					"Upload" => "Homework",
					"Blog Entry" => "Revision"
				];
				$labels = [
					'version_name' => 'Open eLMS Schools',
					'programme' => "Curriculum",
					'programmes' => "Curricula",
					'define_programmes' => "Add learning resources and schedule your courses",
					'add_learners' => "Add Pupils",
					'add_learners_fa_description' => "Import or enter pupil records into the system against which to monitor costs.",
					'lesson' => "Lesson Plan",
					'lessons' => "Lessons Plans",
					'check_managers' => "Check Teachers",
					'review_managers' => "Review your teachers' performance",
					'sign_off' => "Review Homework",
					'sign_off_description' => "Review work that has been submitted for your attention.",
					'sign_off_video_help' => "https://www.youtube.com/embed/ay1R6jHJ614?start=78",
					'check_progress' => "Add Learning",
					'check_progress_description' => "Add Learning and check on your pupils' learning progress",
					"check_progress_video_help" => "https://www.youtube.com/embed/ay1R6jHJ614",
					'qa' => "Inspections",
					'qa_description' => "These issues have been reviewed by a QA or Inspector and require attention.",
					'qa_video_help' => "https://www.youtube.com/embed/ay1R6jHJ614?start=62",
					'users' => 'Pupils',
					'user' => 'Pupil',
					'companies' => 'Forms',
					'company' => 'Forms',
					'jobs' => 'Year',
					'job' => 'Year',
					'managers' => 'Teachers',
					'manager' => 'Teacher',
					'quality_assurer' => 'QA/Inpsector',
					'financial_auditor' => 'Bursar',
					'learner' => 'Pupil',
					'existing_uploads__title' => 'Submit homework or add new revision',
					'existing_uploads__dropbox' => 'Select the homework to upload ...',
					'existing_uploads__new' => 'Or add new revision',
					'existing_uploads__new_button' => 'Add new revision',
					'registration_email' => 'Open eLMS for Schools registration',
					'schedule_add_lessons' => 'Add',
					'learner_menu__calendar' => 'Timetable',
				];
				$labels = array_merge($initial_labels, $labels);

				$hide_menu_items = array_diff($hide_menu_items, [
					"system-setup-defaults-holidays",
				]);

				break;

			case 'openelmscolleges':
				$isOpenElms					=	true;
				$isApprentix				=	true;
				$isOmniPrez					=	false;
				$isOpenElmsLibrary		=	true;
				$isOpenElmsTMS				=	true;
				$isVirtualClassrooms		=	true;
				$isSMCR						=	false;
				$customLogo 				=	true;
				$manual						=	'//www.e-learningwmb.com/page/support-colleges';
				$welcomeMessage			=	'Welcome to Open eLMS for Colleges - the complete education management system from Open eLMS.';
				$learner_introduction	= '//www.e-learningwmb.com/videos/OpeneLMSLearnerColleges.mp4';
				$overwriteModuleType = [
					'Upload' => "Assignment"
				];
				$labels = [
					'version_name' => 'Open eLMS Colleges',
					'programme' => "Course",
					'programmes' => "Courses",
					'define_programmes' => "Add learning resources and schedule your courses",
					'add_learners' => "Add Students",
					'add_learners_fa_description' => "Import or enter student records into the system against which to monitor costs.",
					'lesson' => "Lesson plan",
					'lessons' => "Lessons plans",
					'check_managers' => "Check Tutors",
					'review_managers' => "Review your teachers' performance",
					'sign_off' => "Review Assignments",
					'sign_off_description' => "Review work that has been submitted for your attention.",
					'sign_off_video_help' => "https://www.youtube.com/embed/lj5Xu5gOVsU?start=75",
					'check_progress' => "Add Learning",
					'check_progress_description' => "Add Learning and check on your pupils' learning progress",
					"check_progress_video_help" => "https://www.youtube.com/embed/ay1R6jHJ614",
					'qa_description' => "These issues have been reviewed by a QA or Inspector and require attention.",
					'qa_video_help' => "https://www.youtube.com/embed/lj5Xu5gOVsU?start=58",
					'users' => 'Students',
					'user' => 'Student',
					'companies' => 'Forms',
					'company' => 'Form',
					'jobs' => 'Year',
					'job' => 'Year',
					'managers' => 'Tutors',
					'manager' => 'Tutor',
					'quality_assurer' => 'QA/Inpsector',
					'financial_auditor' => 'Bursar',
					'learner' => 'Student',
					'registration_email' => 'Open eLMS for Colleges registration',
					'schedule_add_lessons' => 'Add',
					'assign_evidance_bulk' =>  'Bulk Evidence',
					'learner_menu__calendar' => 'Timetable',
				];
				$labels = array_merge($initial_labels, $labels);

				$hide_menu_items = array_diff($hide_menu_items, [
					"system-setup-defaults-holidays",
				]);
				break;

			case 'openelmsuniversities':
				$isOpenElms					=	true;
				$isApprentix				=	true;
				$isOmniPrez					=	false;
				$isOpenElmsLibrary		=	true;
				$isOpenElmsTMS				=	true;
				$isVirtualClassrooms		=	true;
				$isSMCR						=	false;
				$customLogo 				=	true;
				$manual						=	'//www.e-learningwmb.com/page/support-universities';
				$welcomeMessage			=	'Welcome to Open eLMS for Universities - the complete education management system from Open eLMS.';
				$learner_introduction	= '//www.e-learningwmb.com/videos/OpeneLMSLearnerUniversities.mp4';
				$overwriteModuleType = [
					'Upload' => "Coursework"
				];
				$labels = [
					'version_name' => 'Open eLMS Universities',
					'programme' => "Course",
					'programmes' => "Courses",
					'define_programmes' => "Add learning resources and schedule your courses",
					'add_learners' => "Add Students",
					'add_learners_fa_description' => "Import or enter student records into the system against which to monitor costs.",
					'check_managers' => "Check Lecturers",
					'review_managers' => "Review your tutors' performance",
					'sign_off' => "Review Assignments",
					'sign_off_description' => "Review work that has been submitted for your attention.",
					'sign_off_video_help' => "https://www.youtube.com/embed/pa3eXLVyS7g?start=69",
					'check_progress' => "Add Learning",
					'check_progress_description' => "Add Learning and check on your pupils' learning progress",
					"check_progress_video_help" => "https://www.youtube.com/embed/ay1R6jHJ614",
					'qa_video_help' => "https://www.youtube.com/embed/pa3eXLVyS7g?start=55",
					'users' => 'Students',
					'user' => 'Student',
					'companies' => 'Departments',
					'company' => 'Department',
					'jobs' => 'Year',
					'job' => 'Year',
					'managers' => 'Lecturers',
					'manager' => 'Lecturer',
					'financial_auditor' => 'Bursar',
					'learner' => 'Student',
					'registration_email' => 'Open eLMS for Universities registration',
					'schedule_add_lessons' => 'Add',

					'assign_evidance_bulk' =>  'Bulk Evidence',
					'learner_menu__calendar' => 'Timetable',
				];
				$labels = array_merge($initial_labels, $labels);

				$hide_menu_items = array_diff($hide_menu_items, [
					"system-setup-defaults-holidays",
				]);
				break;

			case 'openelmsbusiness':
				$isOpenElms					=	true;
				$isApprentix				=	true;
				$isOmniPrez					=	false;
				$isOpenElmsLibrary			=	true;
				$isOpenElmsTMS				=	true;
				$isVirtualClassrooms		=	true;
				$isSMCR						=	false;
				$customLogo 				=	true;
				$manual						=	'//www.e-learningwmb.com/page/support-business';
				$welcomeMessage			=	'Welcome to Open eLMS Business- the business focussed Learning Content Management System for managing learning both offline and online from Open eLMS.';
				$learner_introduction	= '//www.e-learningwmb.com/videos/OpeneLMSLearnerBusiness.mp4';
				$labels = [
					'version_name' => 'Open eLMS Business',
					'sign_off' => "Sign off Training",
					'sign_off_video_help' => "https://www.youtube.com/embed/e1pOutr_rCU?start=73",
					'check_progress' => \APP\Tools::getConfig('sharedClients') ? "Assign Learning" : "Review Progress",
					"check_progress_video_help" => "https://www.youtube.com/embed/e1pOutr_rCU",
					'qa_video_help' => "https://www.youtube.com/embed/e1pOutr_rCU?start=55",
					'approve_and_manage_booking_video_help' => 'https://www.youtube.com/embed/e1pOutr_rCU?start=90',
					'expected_completion_data' => 'Expected Completion Date',
					'assign_from_learning_library_small' => 'Assign learning and predefined projects which are designed for reuse',
					'assign_one_off_project' => 'Assign One-Off Project',
					'registration_email' => 'Open eLMS for Business registration',
					'learner_menu__calendar' => 'Calendar',
					'learner_menu__homework' => 'Assignments',
				];
				$labels = array_merge($initial_labels, $labels);
				break;

			case 'nras':
				$isOpenElms					=	true;
				$isApprentix				=	false;
				$isOmniPrez					=	false;
				$isOpenElmsLibrary		=	true;
				$isOpenElmsTMS				=	false;
				$isVirtualClassrooms		=	false;
				$isSMCR						=	false;
				$manual						=	'//www.e-learningwmb.com/page/support-business';
				$welcomeMessage			=	'Welcome to Open eLMS - the business focussed Learning Content Management System from Open eLMS.';
				$learner_introduction	= '//www.e-learningwmb.com/videos/OpeneLMSLearnerBusiness.mp4';
				$labels = [
					'version_name' => 'NRAS',
					'missing_fields_in_your_profile' => 'Please Complete the Information in your Profile',
				];
				$labels = array_merge($initial_labels, $labels);

				$hide_menu_items = array_diff($hide_menu_items, [
					"system-setup-api",
					"system-setup-api-salesforce",
				]);
				break;
		}


		if ($isJackdawCloud) {
			$licensing_brand = 'jackdaw';
			$licensing_description = 'Welcome to Open eLMS AI Editor, the most powerful cloud based e-learning authoring system available today. If this is your first time using the system, please view the video for an overview of development possibilities.';
			$hide_menu_items = array_diff($hide_menu_items, [
				"system-setup-learning-distribution",
				"system-setup-learning-free-jackdaw-resources",
			]);
		}

		if ($isOmniPrez) {
			$licensing_brand = 'omniprez';
			$licensing_description = 'Welcome to Omniprez, now you can easily create better presentations for any device. If this is your first time using the system, please view the video for an overview of development possibilities.';
		}

		if ($isOpenElms) {
			$licensing_brand = 'openlms';
			$licensing_description = 'Welcome to Open Elms Pro version 8, the most comprehensive Training, Learning Management and Learning Creation System available. Please view the video for an overview of the system to get an insight into the opportunities available to you in using this management side of the system.';
			$hide_resource_type = array_diff($hide_resource_type, [1, 2, 3]);
		}

		if ($isApprentix) {
			$licensing_brand = 'apprentix';
			$licensing_description = 'Welcome to Apprentix, the most comprehensive system for dealing with apprenticeships combing e-portfolio capabilities with learning. Please view the video for an overview of the system to get an insight into the opportunities available to you in using Apprentix, either as a coach, manager, trainer or assessor.';
			$hide_resource_type = array_diff($hide_resource_type, [4, 5, 6, 7, 9]);
			$hide_menu_items = array_diff($hide_menu_items, [
				"library-learning-programmes",
				"review-learning-programmes",
			]);
		}


		if ($isSMCR) {
			$licensing_brand = 'smcr';
			$licensing_description = 'SMCR Range';
			$hide_resource_type = [];
			$hide_menu_items = array_diff($hide_menu_items, [
				"system-setup-organisation-staff-type",
				"system-setup-organisation-functions",
				"system-setup-organisation-responsibilities",
				"system-setup-organisation-committees",
				"system-setup-f-p-categories",
			]);
		}

		if (
			\APP\Tools::getConfig('showResponsibilitiesAndCommittees')
		) {
			$hide_menu_items = array_diff($hide_menu_items, [
				"system-setup-organisation-staff-type",
				"system-setup-organisation-responsibilities",
				"system-setup-organisation-committees",
			]);
		}


		if (
			\APP\Tools::getConfig('addCustomProgrammeStatus')
		) {
			$hide_menu_items = array_diff($hide_menu_items, [
				"system-setup-custom-programme-statuses",
			]);
		}

		if (\APP\Tools::getConfig('HideLearningProgrammesReporting')) {
			$hide_menu_items[] = 'review-learning-programmes';
		}

		if (\APP\Tools::getConfig('HideLearingResourcesReporting')) {
			$hide_menu_items[] = 'review-learning-resources';
		}
		// if(Tools::getConfig('useOutlookVenues')){
		// 	$hide_menu_items[] = 'system-setup-organisation-venues';
		// }

		if (!\APP\Tools::getConfig('showExtraResourceFields_TDG')) {
			$hide_menu_items[] = 'system-setup-learning-target-catalogue';
			$hide_menu_items[] = 'system-setup-learning-delivery-provider-type';
			$hide_menu_items[] = 'system-setup-learning-group-department-code';
		}

		if (!\APP\Tools::getConfig('ExtraUserFieldsWatchAndPosRef')) {
			$hide_menu_items[] = 'system-setup-organisation-watch';
		}

		if (!\APP\Tools::getConfig('isEmbeddedPowerBI')) {
			$hide_menu_items[] = 'powerbi-charts-main';
		}


		// Show Classroom, Book and On The Job, import ILR users
		if ($isOpenElmsTMS) {
			$hide_resource_type = array_diff($hide_resource_type, [4, 5, 6, 7, 8]);
		}

		if ($isOpenElmsLibrary) {
		}

		if ($isVirtualClassrooms) {
		}

		// Bring this into labels for translate
		$labels['manual_file_name'] = $manual;

		return [
			'isOpenElms' => $isOpenElms,
			'isApprentix' => $isApprentix,
			'isJackdawCloud' => $isJackdawCloud,
			'isOmniPrez' => $isOmniPrez,
			'isOpenElmsLibrary' => $isOpenElmsLibrary,
			'isOpenElmsTMS' => $isOpenElmsTMS,
			'isVirtualClassrooms' => $isVirtualClassrooms,
			'isSMCR' => $isSMCR,
			'hiddenMenuItems' => $hide_menu_items,
			'hiddenResourceTypes' => $hide_resource_type,
			'brand' => $licensing_brand,
			'description' => $licensing_description,
			'version' => $site_version,
			'logo' => $settings['LMSUrl'] . "images/licensing/" . $site_version . 'logo.png',
			'overwriteModuleType' => $overwriteModuleType,
			'manual' => \APP\Tools::getConfig('manualAdminURL') ?: $manual,
			'labels' => $labels,
			'welcomeMessage' => $welcomeMessage,
			'learner_introduction' => $learner_introduction,
		];
	}

	// When site version is changed, run processes to convert site.
	// Labels, Roles, something else.
	public static function updateVersion($from_version, $to_version, $licensing)
	{
		$allowed_version = \APP\Licensing::allowedVersions();
		if (
			!empty($from_version) &&
			!empty($to_version) &&
			$from_version != $to_version &&
			in_array($to_version, $allowed_version)
		) {
			// Change role names
			\APP\Licensing::updateRoles($from_version, $to_version, $licensing);

			// Add default values, not manage/remove them, not yet.
			\APP\Licensing::addDefaults($from_version, $to_version, $licensing);
		}
	}

	public static function addDefaults($from_version, $to_version, $licensing)
	{

		// Add default companies
		$add_companies = \APP\Licensing::defaultCompanies();
		foreach ($add_companies[$to_version] as $key => $value) {
			\Models\Company::firstOrCreate(
				['name' => $value],
				['status' => true]
			);
		}

		// Add default Jobs
		$add_jobs = \APP\Licensing::defaultJobs();
		foreach ($add_jobs[$to_version] as $key => $value) {
			\Models\Designation::firstOrCreate(
				['name' => $value],
				['status' => true]
			);
		}
	}

	public static function updateRoles($from_version, $to_version, $licensing)
	{
		$roles = \Models\Role::all();
		$role_names = \APP\Licensing::roleNames();

		$from_role_names = $role_names[$from_version];
		$to_role_names = $role_names[$to_version];


		foreach ($roles as $key => $role) {
			$counter = 0;
			foreach ($from_role_names as $key => $from_role_name) {
				if ($from_role_name['name'] == $role->name) {
					$role->name = $to_role_names[$counter]['name'];
					$role->status = $to_role_names[$counter]['status'];
					$role->save();
				}
				$counter++;
			}
		}
	}

	// Check if all backgrounds are present, if not, copy
	static public function checkBackgrounds($settings) {
		$allowed_versions = \APP\Licensing::allowedVersions();

		foreach ($allowed_versions as $key => $allowed_version) {
			$login_bg = $settings['LMSImagesPath'] . 'licensing/' . $allowed_version . 'bg.jpg';
			if (!is_file($login_bg)) {
				$replacement_file = $settings['LMSImagesPath'] . 'login-bg/' . $allowed_version . '.jpg';
				if (is_file($replacement_file)) {
					copy($replacement_file, $login_bg);
				}
			}
		}
	}

	static public function checkLogos($settings) {
		$allowed_versions = \APP\Licensing::allowedVersions();

		foreach ($allowed_versions as $key => $allowed_version) {
			$logo = $settings['LMSImagesPath'] . 'licensing/' . $allowed_version . 'logo.png';
			if (!is_file($logo)) {
				$replacement_file = $settings['LMSImagesPath'] . 'default-logo/' . $allowed_version . '.png';
				if (is_file($replacement_file)) {
					copy($replacement_file, $logo);
				}
			}
		}
	}

	public static function getTranslation($key = false) {
		$response = $key;
		if (
			$key &&
			isset($GLOBALS["CONFIG"]->licensing['labels'][$key])
		) {
			$response = $GLOBALS["CONFIG"]->licensing['labels'][$key];
		}
		return $response;
	}
}
