<?php
namespace APP;

class ConfigCache {
    private static $configCache = [];
    private static $bulkLoaded = false;

    /**
     * Frontend-safe configuration keys that can be exposed to users
     */
    private static $frontendSafeKeys = [
        // UI/UX configurations
        'schoolField', 'HideReflectiveLog', 'allowLearnerEditILR', 'signOffText',
        'showEmergencyContactDetails', 'showVisaEligibilityChecks', 'isMeetings',
        'isManageBookings', 'isLearnerQAFilter', 'disableVideoClicks', 'disableLazyLoading',
        'moodleLink', 'nextDueTrafficLight', 'learnerReferenceNumberId',
        'enableClassRoomManagerDropDown', 'showResponsibilitiesAndCommittees',
        'competenciesGamification', 'showLatestReleases', 'companyCrm', 'PaymentsEngine',
        'enableEmailAttachments', 'enableUploadType', 'enableJackdawHtml5',
        'powerbi_dashboard_url', 'enableSchedule', 'lessonDuration', 'enableFeedback',
        'enableFeedbackList', 'offTheJobHoursForReviews', 'enableLeaderBoardImages',
        'enableLeaderBoardList', 'importYoutubePlaylist', 'forceYoutubeTitleOverThumbnail',
        'hideCurriculum', 'hideCurriculumLearner', 'hideCurriculumMatching',
        'promoGradient', 'isBlackColourScheme', 'isCategroyFilter',
        'learnerLandingPageDescription', 'learnerLandingPageDescriptionFontSize',
        'thumbnailRedesign', 'thumbnailRedesignFont', 'disableLookOvers',
        'makeResourcesLinktoHome', 'isLearnerLandingPage', 'learnerCategoryLandingMaxColumns',
        'hideResourcesInLesson', 'startupCourseID', 'learnerLandingPageNameCentered',
        'showCriteriaCompletion', 'launchResourceText', 'learnerSkipUploadPrompt',
        'learnerProgressGauge', 'buttonStyle', 'isOpeneLMSClassroom', 'showCalendarTasksOnly',
        'submitAssessmentText', 'isGroupCountries', 'HelpVideoURL', 'isPeertoPeerVideo',
        'enableContactYourColleagues', 'enableProgrammeTitlesLearnerLandingPage',
        'badgesEnabled', 'badgrRegion', 'alternativeIdsEnabled', 'TasksShowByDefaultCalendarEntriesOnly',
        'TasksSortedbySoonestExpectedCompletionDate', 'TasksDefaultSelectedEventType',
        'TasksDefaultSelectedResourceType', 'addCustomProgrammeStatus',
        'hideProgrammeStatusFromLearnerProgressView', 'isApproveLearners',
        'PasswordExpiryDays', 'PasswordMaxAttempts', 'redirectBackToLesson',
        'addAllEventstoOutlook', 'enableGlobalOutlookIntegration', 'allowLearnerUploads',
        'isTrackLearningResourceTime', 'enableImpersonate', 'isCivicaPaymentsEngine',
        'civicaDefaultGeneralLedgerCode', 'CalendarStartTime', 'CalendarEndTime',
        'civicaPaymentsEngineRequestURL', 'showSevenDepartmentSubLevels', 'hideReviewButton',
        'enableMFA', 'optionalResourceProgrammeLink', 'hideMeetingAndZoom',
        'showRecommendations', 'isBrowsebyDefault', 'setSmallPromoImage',
        'showEditExpectedCompletionDateResource', 'daysSinceLastReviewWarningThreshold',
        'isSearchTabVisible', 'showWeekCountForOutcome', 'showGraphFilterForWrap',
        'formSignatureDisable', 'enableSnoozeSignOffForm', 'isShowCancellationsOnEvents',
        'enableCredlyBadge', 'SeparateDistributionAssignFormsandFormReports',
        'enableStripePayment', 'stripePublishableKey', 'defaultDateFormat',

        // Feature flags
        'allowLearnerRefreshLearning', 'defaultUKPRN', 'defaultPrevUKPRN',
        'allowAddBlogEntry', 'isDemoAccess', 'mandatoryDuration', 'randomString',
        'hideHomeButton', 'useOutlookVenues', 'isEditableMaxMinOutlookVenueLimts',
        'RolesNotOverwrittenDuringImport', 'hideDueBefore', 'hideRefreshAt',
        'hidePastEventsInLearningView', 'enableMaximo', 'stripeCreditPriceTableID',
        'stripeLicensePriceTableID', 'expandLearningCategories', 'WorkingWeekHours',
        'rolesAtPageHeader', 'googleTranslate', 'RetakeFeeLimit', 'WaitingListRecycleTime',
        'hideSkillsMonitoringDetails', 'ResetPasswordText', 'ResetPasswordLogo',
        'OpeneLMSCreatorMenu', 'hideManagerRefreshButton', 'initialSkillDueDays',
        'percentageChangeTrafficLight', 'criteriaProgressColor', 'resourceProgressColor',
        'timeProgressColor', 'showFormIDNumberLearnerInterface', 'showWorkflowInLearnerInterface',
        'rolesAtPageHeaderLearner', 'enableGovUKPay', 'tryTraineeReminderText',
        'tryTraineeReminder', 'showDetailsTabForUploadResource',
        'hideLearnerDueDate', 'hideLearnerStatus', 'hideLearnerDetails', 
        'hideLearnerSignOff', 'defaultLearnerTaskView', 'billingCurrencySymbol',
        'billingCurrencyCode', 'showUnassignedDisabledLearning', 'ShowLearningIDLearnerProfile',
        'isMultiSelectShoppingBasket',

        // Visibility configurations
        'isLearningOutsideProgrammeVisibleonFirstLogin', 'isCompletedLearningVisibleonFirstLogin',
        'isInProgressLearningVisibleonFirstLogin', 'isNotStartedLearningVisibleonFirstLogin',
        'isFailedLearningVisibleonFirstLogin', 'isLockedLearningVisibleonFirstLogin',
        'isEnrollableLearningVisibleonFirstLogin', 'isRefresherLearningVisibleonFirstLogin',
        'isMandatoryLearningVisibleonFirstLogin', 'isFavouriteLearningVisibleonFirstLogin',
        'isProgrammeFilterVisibleonFirstLogin', 'allowLearnersToDelete', 'allowSendCivicaData',
        'CivicaManagerEmail', 'CivicaFrequencyDays', 'uniqueEmailPerUser',
        'AttachManagerstoUploads', 'showSchedulePeriodicReview', 'HideQAReports',
        'RolesListHelpUrl', 'showEmailHistoryOnLearnerInterface', 'RegisterCompanyText',

        // Header button visibility
        'hideResourcesNeedAttentionHeaderButton', 'hideHelpHeaderButton',
        'hideLearningHeaderButton', 'hideCalendarHeaderButton', 'hideAssignmentsHeaderButton',
        'hideReportsHeaderButton', 'hideAddFormsInLearnerFile', 'showWorkflowOnLearnerInterface',
        'isCompletedLearningFilterVisible', 'isLearningOutsideProgrammeFilterVisible',
        'isInProgressLearningFilterVisible', 'isNotStartedLearningFilterVisible',
        'isLockedLearningFilterVisible', 'isEnrollableLearningFilterVisible',
        'isRefresherLearningFilterVisible', 'isMandatoryLearningFilterVisible',
        'isFavouriteLearningFilterVisible', 'isProgrammeFilterVisible',
        'showShareResourceSsoUrl', 'showShareResourceUrl', 'askForFeedbackOnComplete',
        'isLearnerSkillsSignOff', 'SignOffCompletedSkillsProgramme',
        'SignOffAutomatedSkillsMonitoring', 'globalHelpURL', 'showLegacyReportsManageButtons',
        'showLinkedAccountsInUserMenuAllTheTime', 'hideLaunchSkills', 'uniqueUsernamePerUser',
        'ExtraUserFieldsWatchAndPosRef', 'allowEmptyEmailImport', 'eventsListLessonsOnlyFromLibrary',
        'isGlobalPaymentsEngine', 'globalPaymentsEngineRequestURL', 'isEmbeddedPowerBI',
        'sortLearningCategoriesByOrder', 'formSignOffText', 'approveEmailQueueItems',
        'OnboardingSignoffFormWorkflow', 'showEmployeeIdInReports', 'AIChatLink',
        'SQLQueryHelpText', 'showExtraResourceFields_TDG', 'ShowEventsineLearningView',
        'RefresherNotificationTimings', 'HideCustomFieldLearningResource',

        // JSON configurations (safe to decode)
        'hide_menu_items', 'hide_resource_type',

        // Chat bot
        'isLearningAI', 'chatBotApiBaseUrl',
        'enablePay360','sharedClients'
    ];

    /**
     * Bulk load all configuration values into cache (internal use only)
     */
    public static function bulkLoadAll(): array {
        if (self::$bulkLoaded) {
            return self::$configCache;
        }

        // Use simple cache key for CLI compatibility
        $cacheKey = self::getCacheKey('config', 'all');

        // Skip cache in CLI contexts where it might not be available
        if (php_sapi_name() === 'cli' || !function_exists('cache')) {
            self::$configCache = self::loadConfigurationsFromDatabase();
        } else {
            try {
                self::$configCache = cache()->remember($cacheKey, 3600, function() {
                    return self::loadConfigurationsFromDatabase();
                });
            } catch (\Exception $e) {
                // Fallback for contexts where cache might not be available
                self::$configCache = self::loadConfigurationsFromDatabase();
            }
        }

        self::$bulkLoaded = true;
        return self::$configCache;
    }

    /**
     * Load configurations directly from database (used by cache and fallback)
     */
    private static function loadConfigurationsFromDatabase(): array {
        // Get all configurations with type information for proper casting
        $configObjects = \Models\Configuration::where('status', true)
            ->select('key', 'value', 'type', 'default_value')
            ->get();

        $configs = [];
        foreach ($configObjects as $config) {
            $value = $config->value;

            // Handle default values
            if (empty($value) && !empty($config->default_value)) {
                $value = $config->default_value;
            }

            // Apply type casting like the original getConfig method
            $configs[$config->key] = self::castConfigValue($value, $config->type);

            // Handle hardcoded logic for specific configurations (matching Tools::getConfig behavior)
            if ($config->key == 'isBlackColourScheme') {
                $configs[$config->key] = self::handleBlackColourSchemeLogic($configs[$config->key]);
            }
        }

        return $configs;
    }

    /**
     * Get only frontend-safe configurations for menu endpoint
     * @param int|null $userId Optional user ID to get company-specific overrides
     */
    public static function getFrontendSafeConfigs($userId = null): array {
        $allConfigs = self::bulkLoadAll();
        $safeConfigs = [];

        // Get company ID if user is provided
        $companyId = null;
        if ($userId) {
            $user = \Models\User::where('id', $userId)->first();
            if ($user) {
                $companyId = $user->company_id;
            }
        } elseif (\APP\Auth::getUser()) {
            $companyId = \APP\Auth::getUserCompanyId();
        }

        // Apply company overrides if we have a company
        if ($companyId) {
            $allConfigs = self::applyCompanyOverrides($allConfigs, $companyId);
        }

        foreach (self::$frontendSafeKeys as $key) {
            if (isset($allConfigs[$key])) {
                $safeConfigs[$key] = $allConfigs[$key];
            }
        }

        return $safeConfigs;
    }

    /**
     * Bulk load all configuration values (for backward compatibility)
     * @deprecated Use getFrontendSafeConfigs() for frontend exposure
     */
    public static function bulkLoad(): array {
        return self::bulkLoadAll();
    }

    /**
     * Get config value from cache with optional company override
     * @param string $key Configuration key
     * @param mixed $default Default value if not found
     * @param int|null $userId Optional user ID to get company-specific override
     */
    public static function get(string $key, $default = null, $userId = null) {
        if (!self::$bulkLoaded) {
            self::bulkLoadAll();
        }

        $value = self::$configCache[$key] ?? $default;
        
        // Check for company override
        $companyId = null;
        if ($userId) {
            $user = \Models\User::where('id', $userId)->first();
            if ($user) {
                $companyId = $user->company_id;
            }
        } elseif (\APP\Auth::getUser()) {
            $companyId = \APP\Auth::getUserCompanyId();
        }
        
        if ($companyId) {
            $configs = self::applyCompanyOverrides([$key => $value], $companyId);
            $value = $configs[$key];
        }
        
        // Handle special case for isBlackColourScheme
        if ($key == 'isBlackColourScheme') {
            $value = self::handleBlackColourSchemeLogic($value);
        }

        return $value;
    }

    /**
     * Cast configuration value based on its type
     * @param mixed $value The raw value to cast
     * @param string $type The configuration type
     * @return mixed The properly typed value
     */
    private static function castConfigValue($value, string $type) {
        switch ($type) {
            case 'boolean':
                return $value == '1' ? true : false;
                
            case 'integer':
                if (intval($value) == floatval($value)) {
                    return intval($value);
                } else {
                    return $value;
                }
                
            case 'list':
                return $value > "" ? explode(",", $value) : [];
                
            case 'list-definition':
                return $value > "" ? json_decode($value, true) : [];
                
            default:
                return isset($value) ? $value : '';
        }
    }

    /**
     * Handle hardcoded logic for isBlackColourScheme configuration
     * Replicates the custom logic from Tools::getConfig()
     */
    private static function handleBlackColourSchemeLogic($defaultValue) {
        // If learner and has color_scheme set, overwrite isBlackColourScheme!
        if (\APP\Auth::isLearner()) {
            $color_scheme = \Models\TableExtension::getValue('users', \APP\Auth::getUserId(), 'color_scheme');
            if ($color_scheme) {
                switch ($color_scheme) {
                    case 'light':
                        return false;
                    case 'dark':
                        return true;
                }
            }
        }

        // If user is logged in branded company site, then check for theme in company entry
        if (!empty($_SESSION["branded_company"])) {
            $company = \Models\Company::find($_SESSION["branded_company"]);
            if ($company) {
                \Models\TableExtension::returnAllFields('companies', $company->id, $company);
                if (!empty($company->extended->learner_theme)) {
                    if ($company->extended->learner_theme == 'light') {
                        return false;
                    } else {
                        return true;
                    }
                }
            }
        }

        // Return the default database value if no overrides apply
        return $defaultValue;
    }

    /**
     * Apply company-specific configuration overrides
     * @param array $configs Base configuration array
     * @param int $companyId Company ID
     * @return array Configuration array with company overrides applied
     */
    private static function applyCompanyOverrides(array $configs, int $companyId): array {
        // Cache company overrides separately with same TTL as main config (cache is cleared on changes)
        $cacheKey = self::getCacheKey('config', 'company', $companyId);
        
        // Skip cache in CLI contexts where it might not be available
        if (php_sapi_name() === 'cli' || !function_exists('cache')) {
            $companyOverrides = self::loadCompanyOverrides($companyId);
        } else {
            try {
                $companyOverrides = cache()->remember($cacheKey, 3600, function() use ($companyId) { // 1 hour, same as main config
                    return self::loadCompanyOverrides($companyId);
                });
            } catch (\Exception $e) {
                // Fallback for contexts where cache might not be available
                $companyOverrides = self::loadCompanyOverrides($companyId);
            }
        }
        
        // Apply overrides to base configs
        foreach ($companyOverrides as $key => $value) {
            if (isset($configs[$key])) {
                // Preserve the type casting from the original configuration
                $configObj = \Models\Configuration::where('key', $key)->first();
                if ($configObj) {
                    $configs[$key] = self::castConfigValue($value, $configObj->type);
                }
            }
        }
        
        return $configs;
    }

    /**
     * Load company overrides directly from database (used by cache and fallback)
     */
    private static function loadCompanyOverrides(int $companyId): array {
        // Get all company-specific configurations
        $overrides = \Models\CompanyConfiguration::where('company_id', $companyId)
            ->with('configuration')
            ->get();
        
        $result = [];
        foreach ($overrides as $override) {
            if ($override->configuration && $override->configuration->key) {
                $result[$override->configuration->key] = $override->value;
            }
        }
        return $result;
    }

    /**
     * Clear configuration cache
     */
    public static function clear() {
        // Skip cache operations in CLI contexts where it might not be available
        if (php_sapi_name() !== 'cli' && function_exists('cache')) {
            try {
                cache()->forget(self::getCacheKey('config', 'all'));
                // Clear all company-specific caches
                self::clearCompanyCaches();
            } catch (\Exception $e) {
                // Cache might not be available - that's ok
            }
        }
        self::$configCache = [];
        self::$bulkLoaded = false;
    }
    
    /**
     * Clear company-specific configuration caches
     * @param int|null $companyId Clear specific company cache, or null for all
     */
    public static function clearCompanyCaches($companyId = null) {
        // Skip cache operations in CLI contexts where it might not be available
        if (php_sapi_name() !== 'cli' && function_exists('cache')) {
            try {
                if ($companyId) {
                    cache()->forget(self::getCacheKey('config', 'company', $companyId));
                } else {
                    // In a production environment with Redis, you could use pattern deletion
                    // For now, we'll need to clear specific company caches when they're updated
                }
            } catch (\Exception $e) {
                // Cache might not be available - that's ok
            }
        }
    }

    /**
     * Generate cache key with database prefix for CLI compatibility
     * Falls back to simple key generation when container is not available
     */
    private static function getCacheKey(string $prefix, ...$parts): string {
        try {
            // Try to use CacheHelper if container is available
            return \APP\Cache\CacheHelper::key($prefix, ...$parts);
        } catch (\RuntimeException $e) {
            // Fallback for CLI/cron contexts where container isn't set
            global $MAIN_CFG;
            $dbName = $MAIN_CFG->LMSDBName ?? 'lms';
            $cleanDb = preg_replace('/[^a-zA-Z0-9_]/', '_', $dbName);
            return $cleanDb . ':' . $prefix . ':' . implode(':', $parts);
        }
    }
}
