<?php
namespace APP;
use DateTime;
use DateTimeZone;

/*
	Validates user if has correct fields and fields are correctly formated for ILR export and returns response wth what is wrong.
*/

class Export {

	// Add comment here
	public static function validateILRfields($user, $export_config = false) {

		$fields = $GLOBALS["CONFIG"]->ilr_fields;

		$response = new \stdClass;
		$response->valid = true;
		$response->errors = new \stdClass;
		$response->errors->missing = [];
		$response->errors->missing_sub_fields = [];
		$response->errors->missing_sub_sub_fields = [];
		$response->errors->validation = [];

		if (empty($export_config['data']['mandatory_ni'])) {
			$fields['NINumber']['required'] = false;
		}

		$ignore_decode = [
			'PriorAttain'
		];

		foreach ($fields as $fieldKey => $field) {
			if (in_array($fieldKey, $ignore_decode)) {
				$decoded_fields = $user->{$fieldKey};
			} else {
				$decoded_fields = is_array($user->{$fieldKey}) ? $user->{$fieldKey} : json_decode($user->{$fieldKey} ?? '', true);
			}
			// Sub field validation, in case main field exists, then spcific sub fields must exist also.
			if (!empty($field['children'])) {
				if (
					!empty($decoded_fields) &&
					(
						is_array($decoded_fields) ||
						is_object($decoded_fields)
					)
				) {
					foreach ($decoded_fields as $key => $decoded_field) {
						foreach ($field['children'] as $subFieldKey => $sub_field) {

							// sub sub field validation.
							// if sub field has children, check for sub-sub field validation

							if (!empty($sub_field['children'])) {
								if (
									!empty($decoded_field[$subFieldKey])
									&& (
										is_array($decoded_field[$subFieldKey])
										|| is_object($decoded_field[$subFieldKey])
									)
								) {

									// Workaround for problem that should not exist, this will take care of existing data, will fix import.
									// EmploymentStatusMonitoring should be array containing arrays of ESMType and ESMCode, NOT array with keys ESMCode and ESMType.
									if (
										$subFieldKey == 'EmploymentStatusMonitoring' &&
										(
											(
												isset($decoded_field[$subFieldKey]['ESMType']) &&
												$decoded_field[$subFieldKey]['ESMType']
											) ||
											(
												isset($decoded_field[$subFieldKey]['ESMCode']) &&
												$decoded_field[$subFieldKey]['ESMCode']
											)
										)
									) {
										$decoded_field[$subFieldKey] = [$decoded_field[$subFieldKey]];
									}
									// EOF problem workaround


									foreach ($decoded_field[$subFieldKey] as $key => $decoded_sub_field) {
										foreach ($sub_field['children'] as $subSubFieldKey => $sub_sub_field) {
											if (
												$sub_sub_field['required'] &&
												empty($decoded_sub_field[$subSubFieldKey])
											) {
												$response->valid = false;
												$missing_sub_sub_field = new \stdClass;
												$missing_sub_sub_field->grandparent = $fieldKey;
												$missing_sub_sub_field->parent = $subFieldKey;
												$missing_sub_sub_field->child = $subSubFieldKey;
												$response->errors->missing_sub_sub_fields[] = $missing_sub_sub_field;
											}
											// if sub sub field exists, lets validate it
											\App\Export::validateField($sub_sub_field, $decoded_sub_field, $subSubFieldKey, $response);
										}
									}
								}

							// if sub field is not array check just against it
							} elseif ($sub_field['required'] && empty($decoded_field[$subFieldKey])) {
								$response->valid = false;
								$missing_sub_field = new \stdClass;
								$missing_sub_field->parent = $fieldKey;
								$missing_sub_field->child = $subFieldKey;
								$response->errors->missing_sub_fields[] = $missing_sub_field;
							}

							// If sub field exists and pattern is defined, check against it
							\App\Export::validateField($sub_field, $decoded_field, $subFieldKey, $response);
						}
					}
				}

			// Main field validation
			} elseif ($field['required'] && empty($user->{$fieldKey})) {
				$response->valid = false;
				$response->errors->missing[] = $fieldKey;
			}

			// If field exists and pattern is defined, check against it
			\App\Export::validateField($field, $user, $fieldKey, $response);
		}

		return $response;
	}

	// description is needed
	private static function validateField($ilr_field = false, $user_field = false, $fieldKey = false, &$response = false) {
		// Check if user field is array or object and pull out value accordingly.
		if (is_array($user_field) && !empty($user_field[$fieldKey])) {
			$user_field_value = $user_field[$fieldKey];
		} elseif (is_object($user_field) && !empty($user_field->{$fieldKey})) {
			$user_field_value = $user_field->{$fieldKey};
		} else {
			$user_field_value = false;
		}

		if (
			!empty($ilr_field['pattern']) &&
			$user_field_value &&
			$user_field_value != null &&
			$user_field_value != "null" &&
			!empty($response) &&
			!empty($fieldKey)
		) {
			if (!empty($ilr_field['type'])) {

				// Remove line feeds!!!
				$user_field_value = preg_replace( "/\r|\n/", "", $user_field_value);

				//(int) (float) (double)
				switch ($ilr_field['type']) {
					case 'number':
						$user_field_value = (int)$user_field_value;
					break;
					/*
					case 'decimal':
						$user_field_value = (double)$user_field_value;
					break;
					*/
					case 'date':

						// In case something breaks here, as often it does!
						try {
							$user_field_value = \Carbon\Carbon::parse($user_field_value)->toDateString();
						}
						catch (\Exception $err) {
							$user_field_value = "";
						}

					break;
				}
			}

			/*
			debuggggggging
			echo "\n";
			echo "-----";
			echo "\n";
			echo $ilr_field['pattern'];
			echo "\n";
			echo $user_field_value;
			echo "\n";
			echo $fieldKey;
			echo "\n";
			echo "-----";
			*/

			if (
				!preg_match('/' . $ilr_field['pattern'] . '/', $user_field_value)
			) { // "/" is here because new RegExp in JS would add them also.
				$response->valid = false;
				$validator_failed = new \stdClass;
				$validator_failed->field = $fieldKey;
				$validator_failed->name = !empty($ilr_field['name']) ? $ilr_field['name'] : '';
				$validator_failed->error = $ilr_field['pattern_error'];
				$response->errors->validation[] = $validator_failed;
			}
		}
	}

	public static function ilrAddValueCheck($key, $value, &$element) {
		if (
			isset($value[$key]) &&
			(
				$value[$key] ||
				$value[$key] == 0
			) &&
			$value[$key] != null &&
			$value[$key] != 'null'
		) {
			// Also here, some processing can take place, like all date fields need to be in specific order, will have to improve/improvise here, at the same time.
			$date_fields = ['LearnStartDate', 'OrigLearnStartDate', 'LearnPlanEndDate', 'DateEmpStatApp', 'LearnActEndDate', 'AchDate', 'LearnDelFAMDateFrom', 'LearnDelFAMDateTo', 'WorkPlaceStartDate', 'WorkPlaceEndDate', 'AFinDate', 'OutStartDate', 'OutEndDate', 'OutCollDate', 'DateLevelApp'];
			if (in_array($key, $date_fields) !== false) {

				$pieces = explode("T", $value[$key]);
				if (isset($pieces[0]) && $pieces[0]) {
					$value[$key] = \Carbon\Carbon::parse($pieces[0])->format('Y-m-d');
				} else {
					$value[$key] = \Carbon\Carbon::parse($value[$key])->format('Y-m-d');
				}
			}

			if ($key == 'CompStatus') {
				$value[$key] = substr($value[$key], 0, 1);
			}

			// Eventually I will have to take rules or pattern from ilr_fields.php, though, there lies a tree, I need flat definition :/
			$element->addChild($key, htmlspecialchars($value[$key]));
		}
	}

	// \APP\Export::ilrAddValue(['AddHours'], $value, $element);
	public static function ilrAddValue($keys, $value, &$element) {
		if (is_array($keys)) {
			foreach ($keys as $key => $key_name) {
				\APP\Export::ilrAddValueCheck($key_name, $value, $element);
			}
		} else {
			\APP\Export::ilrAddValueCheck($keys, $value, $element);
		}
	}

	// Check if given data contains collection of objects or just one without collection
	// In this case, collection is expected [[object]] not [object]
	public static function collectionCheck (&$data, $fields) {
		$field_exists = false;
		foreach ($fields as $key => $field) {
			if (isset($data[$field]) && $data[$field]) {
				$field_exists = true;
			}
		}
		if ($field_exists) {
			$data = [$data];
		}
	}

	// Check individual learving deliver if exportable!
	public static function fundingYearsDeliveryCheck ($LearningDelivery, $funding_year, $collection_month, $start_date, $end_date) {
		if (!$funding_year) {
			return true;
		}
		$response = false;
		/*
			1. if LearnActEndDate exists and is in export year, export.
			2. if LearnActEndDate is missing, export.
			3. if AchDate is in export year, export.
			4. if LearnStartDate is less than export year final date, export.
			5. if CompStatus is Withdraw and WithdrawDate is present and in this year, export!

			1920

			01/08/2019 - 31/07/2020 - 1 2 3
			01/08/2020 - 31/07/2021 - 1 2 3 4 5 6
			01/08/2021 - 31/07/2022 - 1 2 3 4 5 6 7 8 9


			LearnActEndDate - 10/08/2020

			if LearnActEndDate > end_date
				//if CompStatus == 3
				CompStatus = 1
				remove WithdrawReason
				remove LearnActEndDate

		*/

		$WithdrawDate = false;
		if (isset($LearningDelivery['WithdrawDate'])) {
			$WithdrawDate = \Carbon\Carbon::parse($LearningDelivery['WithdrawDate']);
		}

		$LearnStartDate = false;
		if (isset($LearningDelivery['LearnStartDate'])) {
			if (
				is_array($LearningDelivery['LearnStartDate'])
			) {
				if (isset($LearningDelivery['LearnStartDate'][0])) {
					$LearningDelivery['LearnStartDate'] = $LearningDelivery['LearnStartDate'][0];
					$LearnStartDate = \Carbon\Carbon::parse($LearningDelivery['LearnStartDate']);
				}
			} else {
				$LearnStartDate = \Carbon\Carbon::parse($LearningDelivery['LearnStartDate']);
			}
		}
		$LearnActEndDate = false;
		if (isset($LearningDelivery['LearnActEndDate'])) {
			$LearnActEndDate = \Carbon\Carbon::parse($LearningDelivery['LearnActEndDate']);
		}
		$AchDate = false;
		if (isset($LearningDelivery['AchDate'])) {
			$AchDate = \Carbon\Carbon::parse($LearningDelivery['AchDate']);
		}


		if (
			(
				$LearnStartDate &&
				$LearnStartDate->lessThanOrEqualTo($end_date)
			) &&
			(

				(
					$LearnActEndDate &&
					$LearnActEndDate->greaterThan($start_date)
				) ||
				(
					$AchDate &&
					$AchDate->between($start_date, $end_date)
				) ||
				(
					isset($LearningDelivery['CompStatus']) &&
					$LearningDelivery['CompStatus'] == 3 &&
					$WithdrawDate &&
					$WithdrawDate->between($start_date, $end_date)
				) ||
				!$LearnActEndDate
			) &&
			(
				!$collection_month ||
				$LearnStartDate->lessThanOrEqualTo($collection_month)
			)
		) {
			$response = true;
		}
		return $response;
	}

	// If funding years are specified, filter out users
	// 18/19 are between 01/08/18(start) and 31/07/19(end)
	// 19/20 are between 01/08/19(start) and 31/07/20(end)
	// Check if end date is bigger than 01/08/18, start of funding year.
	// Check if start date is bigger than 01/08/18
	public static function fundingYears ($user, $funding_year, $collection_month, $start_date, $end_date) {

		// If $funding_year is not supplied, all can be exported!
		if (!$funding_year) {
			return true;
		}

		$response = false;
		if (
			$user->LearningDelivery
		) {
			foreach (json_decode($user->LearningDelivery, true) as $key => $LearningDelivery) {
				// Check all deliveries
				$response_temp = \APP\Export::fundingYearsDeliveryCheck($LearningDelivery, $funding_year, $collection_month, $start_date, $end_date);
				// If at least one of them are valid, user can be exporeted!
				if ($response_temp) {
					$response = true;
				}
			}
		}
		return $response;
	}

	// Export users ILR records
	public static function ilrUsers ($users, $export_config, $temp_path, $UUID = false, $params = []) {

		$export_response = new \stdClass;
		$export_response->users = new \stdClass;
		$export_response->file_name = '';
		$export_response->users->success = [];
		$export_response->users->error = [];

		$year_export = false;

		// Defaults!
		$year_export = intval(substr($export_config['data']['collection_year'], -2));
		$xmlns = 'ESFA/ILR/20' . ($year_export - 1) . '-' . $year_export;
		$schemaLocation = 'ESFA/ILR/20' . ($year_export - 1) . '-' . $year_export;

		$funding_year_end_date = false;
		if (isset($export_config['data']['funding_years'])) {
			$funding_year = intval($export_config['data']['funding_years']);
			$funding_year_end_date = \Carbon\Carbon::create(20 . substr($funding_year, 2, 4), 7, 31, 23, 59, 59);
		}

		if ($export_config['data']['collection_year'] == '1415') {
			$xmlns = 'http://www.theia.org.uk/ILR/2014-15/1';
			$schemaLocation = 'http://www.theia.org.uk/ILR/2014-15/1 ILR-2014-15-Structure.xsd';
			$year_export = 15;
		}

		if ($export_config['data']['collection_year'] == '1516') {
			$xmlns = 'SFA/ILR/2015-16';
			$schemaLocation = 'SFA/ILR/2015-16';
			$year_export = 16;
		}

		if ($export_config['data']['collection_year'] == '1617') {
			$xmlns = 'SFA/ILR/2016-17';
			$schemaLocation = 'SFA/ILR/2016-17';
			$year_export = 17;
		}

		if ($export_config['data']['collection_year'] == '1718') {
			$xmlns = 'SFA/ILR/2017-18';
			$schemaLocation = 'SFA/ILR/2017-18';
			$year_export = 18;
		}

		if ($export_config['data']['collection_year'] == '1819') {
			$xmlns = 'ESFA/ILR/2018-19';
			$schemaLocation = 'ESFA/ILR/2018-19';
			$year_export = 19;
		}

		if ($export_config['data']['collection_year'] == '1920') {
			$xmlns = 'ESFA/ILR/2019-20';
			$schemaLocation = 'ESFA/ILR/2019-20';
			$year_export = 20;
		}

		if ($export_config['data']['collection_year'] == '2021') {
			$xmlns = 'ESFA/ILR/2020-21';
			$schemaLocation = 'ESFA/ILR/2020-21';
			$year_export = 21;
		}

		if ($export_config['data']['collection_year'] == '2122') {
			$xmlns = 'ESFA/ILR/2021-22';
			$schemaLocation = 'ESFA/ILR/2021-22';
			$year_export = 22;
		}

		if ($export_config['data']['collection_year'] == '2223') {
			$xmlns = 'ESFA/ILR/2022-23';
			$schemaLocation = 'ESFA/ILR/2022-23';
			$year_export = 23;
		}

		if ($export_config['data']['collection_year'] == '2324') {
			$xmlns = 'ESFA/ILR/2023-24';
			$schemaLocation = 'ESFA/ILR/2023-24';
			$year_export = 24;
		}

		if ($export_config['data']['collection_year'] == '2425') {
			$xmlns = 'ESFA/ILR/2024-25';
			$schemaLocation = 'ESFA/ILR/2024-25';
			$year_export = 25;
		}

		$LearnerDestinationandProgressions = [];


		$xml = new \SimpleXMLElement('<?xml version="1.0" encoding="utf-8" ?><Message></Message>');

		$xml->addAttribute('xmlns:xmlns:xs', 'http://www.w3.org/2001/XMLSchema');
		$xml->addAttribute('xmlns', $xmlns);
		$xml->addAttribute('xmlns:xmlns:xsi', 'http://www.w3.org/2001/XMLSchema-instance');
		$xml->addAttribute('xsi:xsi:schemaLocation', $schemaLocation);

		$header = $xml->addChild('Header');
			$collectiondetails = $header->addChild('CollectionDetails');
				$collectiondetails->addChild('Collection', 'ILR');
				$collectiondetails->addChild('Year', $export_config['data']['collection_year']);
				$collectiondetails->addChild('FilePreparationDate', date('Y-m-d'));
			$source = $header->addChild('Source');
				$source->addChild('ProtectiveMarking', 'OFFICIAL-SENSITIVE-Personal');
				$source->addChild('UKPRN', $export_config['data']['ukprn']); //The UK provider reference number for the provider
				$source->addChild('SoftwareSupplier', 'Own Software');
				$source->addChild('SoftwarePackage', 'Open LMS');
				$source->addChild('Release', '8');
				$source->addChild('SerialNo', '01');
				$source->addChild('DateTime', \Carbon\Carbon::now('Europe/London')->format('Y-m-d\TH:i:s'));
				//<!-- This and the next element only appear in files generated by FIS -->
				//$source->addChild('ReferenceData', 'Version1.0, LARS 2015-06-01');
				//$source->addChild('ComponentSetVersion', '1');
		/*
		$sourcefiles = $xml->addChild('SourceFiles');
			//<!-- The SourceFiles group only appears in files generated by FIS -->

			$sourcefile = $xml->addChild('SourceFile');
				$sourcefile->addChild('SourceFileName', 'ILR-LLLLLLLL1718-20160229-144401-01.xml');
				$sourcefile->addChild('FilePreparationDate', '2016-02-29');
				$sourcefile->addChild('SoftwareSupplier', 'Software Systems Inc.');
				$sourcefile->addChild('SoftwarePackage', 'GreatStuffMIS');
				$sourcefile->addChild('Release', '1');
				$sourcefile->addChild('SerialNo', '01');
				$sourcefile->addChild('DateTime', '2016-02-29T11:14:05');
		*/
		$learningprovider = $xml->addChild('LearningProvider');
			$learningprovider->addChild('UKPRN', $export_config['data']['ukprn_cp']); //The UK provider reference number of the contracted provider

		$processed_ids = [];

		$funding_year = false;
		$collection_month = false;
		$start_date = false;
		$end_date = false;
		$funding_years = [
			1617,
			1718,
			1819,
			1920,
			2021,
			2122,
			2223,
			2324,
			2425,
		];
		if (
			isset($export_config['data']['funding_years']) &&
			$export_config['data']['funding_years'] &&
			in_array(intval($export_config['data']['funding_years']), $funding_years)
		) {
			$funding_year = intval($export_config['data']['funding_years']);
			$start_date = \Carbon\Carbon::create(20 . substr($funding_year, 0, 2), 8, 1, 0);
			$end_date = \Carbon\Carbon::create(20 . substr($funding_year, 2, 4), 7, 31, 23, 59, 59);
			if (isset($params['collection_month'])) {
				$collection_month = \Carbon\Carbon::parse($params['collection_month']);
			}
		}

		foreach ($users as $key => $user) {
			if (
				!in_array($user->id, $processed_ids) &&
				!$user->exclude_from_ilr_export &&
				!$user->exclude_from_reports &&
				\APP\Export::fundingYears($user, $funding_year, $collection_month, $start_date, $end_date)
			) {
				$processed_ids[] = $user->id;

				// add user to export response report.
				$current_user = new \stdClass;
				$current_user->id = $user->id;
				$current_user->fname = $user->fname;
				$current_user->lname = $user->lname;
				$current_user->ULN = $user->ULN;
				$validateUser = \APP\Export::validateILRfields($user, $export_config);

				if (
					$validateUser->valid
				) {
					$learner = $xml->addChild('Learner');
						$learner->addChild('LearnRefNumber', $user->LearnRefNumber);
						if ($user->PrevLearnRefNumber) {
							$learner->addChild('PrevLearnRefNumber', $user->PrevLearnRefNumber);
						}
						if ($user->PrevUKPRN) {
							$learner->addChild('PrevUKPRN', $user->PrevUKPRN);
						}
						if ($user->PMUKPRN) {
							$learner->addChild('PMUKPRN', $user->PMUKPRN);
						}
						if ($user->CampId && $year_export > 18) {
							$learner->addChild('CampId', $user->CampId);
						}
						$learner->addChild('ULN', $user->ULN);

						if ($user->lname) {
							$learner->addChild('FamilyName', $user->lname);
						}
						if ($user->fname) {
							$learner->addChild('GivenNames', $user->fname);
						}
						if ($user->DateOfBirth) {
							$learner->addChild('DateOfBirth', $user->DateOfBirth);
						}
						$learner->addChild('Ethnicity', $user->Ethnicity);
						$learner->addChild('Sex', $user->Sex);
						$learner->addChild('LLDDHealthProb', $user->LLDDHealthProb);

						if (
							$user->NINumber
						) {
							$learner->addChild('NINumber', $user->NINumber);
						}

						if (
							$user->PriorAttainLegacy &&
							$year_export < 22
						) {
							$learner->addChild('PriorAttain', $user->PriorAttainLegacy);
						}


						if ($user->Accom) {
							$learner->addChild('Accom', $user->Accom);
						}
						if ($user->ALSCost) {
							$learner->addChild('ALSCost', $user->ALSCost);
						}
						if ($user->PlanLearnHours) {
							$learner->addChild('PlanLearnHours', $user->PlanLearnHours);
						}
						if ($user->PlanEEPHours) {
							$learner->addChild('PlanEEPHours', $user->PlanEEPHours);
						}
						if ($user->MathGrade) {
							$learner->addChild('MathGrade', $user->MathGrade);
						}
						if ($user->EngGrade) {
							$learner->addChild('EngGrade', $user->EngGrade);
						}
						$learner->addChild('PostcodePrior', $user->PostcodePrior);
						$learner->addChild('Postcode', $user->Postcode);
						if ($user->AddLine1) {
							$learner->addChild('AddLine1', htmlspecialchars($user->AddLine1, ENT_XML1, 'UTF-8'));
						}
						if ($user->AddLine2) {
							$learner->addChild('AddLine2', htmlspecialchars($user->AddLine2, ENT_XML1, 'UTF-8'));
						}
						if ($user->AddLine3) {
							$learner->addChild('AddLine3', htmlspecialchars($user->AddLine3, ENT_XML1, 'UTF-8'));
						}
						if ($user->AddLine4) {
							$learner->addChild('AddLine4', htmlspecialchars($user->AddLine4, ENT_XML1, 'UTF-8'));
						}
						if ($user->phone) {
							$learner->addChild('TelNo', $user->phone);
						}
						if ($user->email) {
							$learner->addChild('Email', $user->email);
						}

						if (
							$user->PriorAttain &&
							$year_export > 21
						) {
							if (!is_array($user->PriorAttain)) {
								$user->PriorAttain = json_decode($user->PriorAttain, true);
							}
							foreach ($user->PriorAttain as $key => $value) {
								// check for DateLevelApp, if it is missing, add 1st LearnStartDate in its place.
								if (empty($value['DateLevelApp'])) {
									foreach (json_decode($user->LearningDelivery, true) as $ld_key => $ld_value) {
										if (
											$ld_value['AimType'] == 1 &&
											$ld_value['LearnStartDate']
										) {
											$value['DateLevelApp'] = $ld_value['LearnStartDate'];
											break;
										}
									}
								}
								$priorattain = $learner->addChild('PriorAttain');
									\APP\Export::ilrAddValue(
										[
											'PriorLevel', 'DateLevelApp'
										],
										$value,
										$priorattain
									);
							}
						}

						if (
							$user->OTJHours &&
							$year_export > 18 &&
							$year_export < 20
						) {
							$learner->addChild('OTJHours', $user->OTJHours);
						}

						if ($user->ContactPreference) {
							foreach (json_decode($user->ContactPreference, true) as $key => $value) {
								$contactpreference = $learner->addChild('ContactPreference');
									\APP\Export::ilrAddValue(
										[
											'ContPrefType', 'ContPrefCode'
										],
										$value,
										$contactpreference
									);
							}
						}

						if ($user->LLDDandHealthProblem) {
							foreach (json_decode($user->LLDDandHealthProblem, true) as $key => $value) {
								if (
									(!empty($value['LLDDCat']) && $value['LLDDCat']) ||
									(!empty($value['PrimaryLLDD']) && $value['PrimaryLLDD'])
								) {
									$llddandhealthproblem = $learner->addChild('LLDDandHealthProblem');
										\APP\Export::ilrAddValue(
											[
												'LLDDCat', 'PrimaryLLDD'
											],
											$value,
											$llddandhealthproblem
										);
								}
							}
						}

						if ($user->LearnerFAM) {
							foreach (json_decode($user->LearnerFAM, true) as $key => $value) {
								if (
									!empty($value['LearnFAMType']) &&
									$value['LearnFAMType'] &&
									!empty($value['LearnFAMCode']) &&
									$value['LearnFAMCode']
								) {
									$learnerfam = $learner->addChild('LearnerFAM');
										\APP\Export::ilrAddValue(
											[
												'LearnFAMType', 'LearnFAMCode'
											],
											$value,
											$learnerfam
										);
								}
							}
						}

						if ($user->ProviderSpecLearnerMonitoring) {
							foreach (json_decode($user->ProviderSpecLearnerMonitoring, true) as $key => $value) {
								$providerspeclearnermonitoring = $learner->addChild('ProviderSpecLearnerMonitoring');
									\APP\Export::ilrAddValue(
										[
											'ProvSpecLearnMonOccur', 'ProvSpecLearnMon'
										],
										$value,
										$providerspeclearnermonitoring
									);
							}
						}

						if ($user->LearnerEmploymentStatus) {

							foreach (json_decode($user->LearnerEmploymentStatus, true) as $key => $value) {
								if (
									$funding_year_end_date &&
									!empty($value['DateEmpStatApp'])
								) {
									$DateEmpStatApp = \Carbon\Carbon::parse($value['DateEmpStatApp']);
									if ($DateEmpStatApp->greaterThan($funding_year_end_date)) {
										continue;
									}
								}
								$learneremploymentstatus = $learner->addChild('LearnerEmploymentStatus');
									$add_fields = ['EmpStat', 'DateEmpStatApp', 'EmpId'];
									if (
										$year_export > 18 &&
										$year_export < 21
									) {
										$add_fields[] = 'AgreeId';
									}
									\APP\Export::ilrAddValue(
										$add_fields,
										$value,
										$learneremploymentstatus
									);

									if (
										isset($value['EmploymentStatusMonitoring']) &&
										$value['EmploymentStatusMonitoring'] &&
										(
											(
												isset($value['EmploymentStatusMonitoring']['ESMType']) &&
												$value['EmploymentStatusMonitoring']['ESMType']
											)
											||
											(
												isset($value['EmploymentStatusMonitoring']['ESMCode']) &&
												$value['EmploymentStatusMonitoring']['ESMCode']
											)
										)
									) {
										$value['EmploymentStatusMonitoring'] = [$value['EmploymentStatusMonitoring']];
									}

									if (
										isset($value['EmploymentStatusMonitoring']) &&
										(
											is_array($value['EmploymentStatusMonitoring']) ||
											is_object($value['EmploymentStatusMonitoring'])
										)
									) {
										foreach ($value['EmploymentStatusMonitoring'] as $key => $subValue) {

											// Do not include Other Employment Type in reports that are before 22 year report
											if (
												$subValue['ESMType'] == 'OET' &&
												$year_export < 22
											) {
												continue;
											}
											$employmentstatusmonitoring = $learneremploymentstatus->addChild('EmploymentStatusMonitoring');

												\APP\Export::ilrAddValue(
													[
														'ESMType', 'ESMCode'
													],
													$subValue,
													$employmentstatusmonitoring
												);
										}
									}

							}
						}

						if ($user->LearnerHE) {
							foreach (json_decode($user->LearnerHE, true) as $key => $value) {
								if (count($value) > 0) {
									$learnerhe = $learner->addChild('LearnerHE');

										\APP\Export::ilrAddValue(
											[
												'UCASPERID', 'TTACCOM'
											],
											$value,
											$learnerhe
										);


										if (isset($value['LearnerHEFinancialSupport'])) {
											foreach ($value['LearnerHEFinancialSupport'] as $key => $subValue) {
												$learnerhefinancialsupport = $learnerhe->addChild('LearnerHEFinancialSupport');

													\APP\Export::ilrAddValue(
														[
															'FINTYPE', 'FINAMOUNT'
														],
														$subValue,
														$learnerhefinancialsupport
													);
											}
										}
								}
							}
						}


						if (
							$user->LearningDelivery
						) {
							foreach (json_decode($user->LearningDelivery, true) as $key => $value) {
								if (
									(
										empty($value['exclude_export']) ||
										(
											isset($value['exclude_export']) &&
											$value['exclude_export'] == false
										)
									) //&&
									//\APP\Export::fundingYearsDeliveryCheck($value, $funding_year, $collection_month, $start_date, $end_date)
								) {

									// check if LearnActEndDate is after funding years, if so, reset CompStatus to 1 and remove LearnActEndDate and WithdrawReason
									if ($funding_year_end_date) {
										$LearnActEndDate = false;
										if (isset($value['LearnActEndDate'])) {
											$LearnActEndDate = \Carbon\Carbon::parse($value['LearnActEndDate']);
										}
										if (
											$LearnActEndDate &&
											$LearnActEndDate->greaterThan($funding_year_end_date)
										) {
											$value['LearnActEndDate'] = null;
											$value['WithdrawReason'] = null;
											$value['AchDate'] = null;
											$value['CompStatus'] = 1;
											$value['Outcome'] = null;
											$value['TLOut'] = null;

											// Loop LearningDeliveryFAM and remove LearnDelFAMDateTo if exists
											if (isset($value['LearningDeliveryFAM'])) {
												foreach ($value['LearningDeliveryFAM'] as $ldf_key => $ldf_value) {
													if (isset($ldf_value['LearnDelFAMDateTo'])) {
														$value['LearningDeliveryFAM'][$ldf_key]['LearnDelFAMDateTo'] = null;
													}
												}
											}
										}
									}


									$learningdelivery = $learner->addChild('LearningDelivery');

										$learningdelivery_fields = ['LearnAimRef', 'AimType', 'AimSeqNumber', 'LearnStartDate', 'OrigLearnStartDate', 'LearnPlanEndDate', 'FundModel', 'ProgType', 'FworkCode', 'PwayCode', 'StdCode', 'PartnerUKPRN', 'DelLocPostCode', 'LSDPostcode', 'AddHours', 'PriorLearnFundAdj', 'OtherFundAdj', 'ConRefNumber', 'EPAOrgID', 'EmpOutcome', 'CompStatus', 'LearnActEndDate', 'WithdrawReason', 'Outcome', 'AchDate', 'OutGrade'];

										if ($year_export > 19) {
											// Find the position of 'FundModel'
											$position = array_search('FundModel', $learningdelivery_fields);

											if ($position !== false) {
												// Insert 'PHours' after 'FundModel'
												array_splice($learningdelivery_fields, $position + 1, 0, 'PHours');
											}
										}

										if ($year_export > 20) {
											$position_PHours = array_search('PHours', $learningdelivery_fields);
											$position_FundModel = array_search('FundModel', $learningdelivery_fields);

											if ($position_PHours !== false) {
												array_splice($learningdelivery_fields, $position_PHours + 1, 0, 'OTJActHours');
											} else if ($position_FundModel !== false) {
												array_splice($learningdelivery_fields, $position_FundModel + 1, 0, 'OTJActHours');
											}
										}

										\APP\Export::ilrAddValue(
											$learningdelivery_fields,
											$value,
											$learningdelivery
										);

										if ($UUID) {
											$learningdelivery->addChild('SWSupAimId', $UUID);
										}

										if ($year_export > 24) {
											$learningdelivery_fields[] = 'TLOut';
										}

										if (isset($value['LearningDeliveryFAM']) && $value['LearningDeliveryFAM']) {
											$fields = ['LearnDelFAMType', 'LearnDelFAMCode', 'LearnDelFAMDateFrom', 'LearnDelFAMDateTo'];
											\APP\Export::collectionCheck($value['LearningDeliveryFAM'], $fields);
											foreach ($value['LearningDeliveryFAM'] as $key => $subValue) {

												// Those LearnDelFAMTypes are removed from 2021 and forward
												if (
													$year_export > 19 &&
													isset($subValue['LearnDelFAMType']) &&
													(
														$subValue['LearnDelFAMType'] == 'NSA' ||
														$subValue['LearnDelFAMType'] == 'WPP' ||
														$subValue['LearnDelFAMType'] == 'POD'
													)
												) {
													continue;
												}

												$learningdeliveryfam = $learningdelivery->addChild('LearningDeliveryFAM');

													\APP\Export::ilrAddValue(
														$fields,
														$subValue,
														$learningdeliveryfam
													);
											}
										}

										if (isset($value['LearningDeliveryWorkPlacement']) && $value['LearningDeliveryWorkPlacement']) {
											foreach ($value['LearningDeliveryWorkPlacement'] as $key => $subValue) {
												$learningdeliveryworkplacement = $learningdelivery->addChild('LearningDeliveryWorkPlacement');

													\APP\Export::ilrAddValue(
														[
															'WorkPlaceStartDate', 'WorkPlaceEndDate', 'WorkPlaceHours', 'WorkPlaceMode', 'WorkPlaceEmpId'
														],
														$subValue,
														$learningdeliveryworkplacement
													);
											}
										}


										if (isset($value['AppFinRecord']) && $value['AppFinRecord']) {
											$fields = ['AFinType', 'AFinCode', 'AFinDate', 'AFinAmount'];
											\APP\Export::collectionCheck($value['AppFinRecord'], $fields);
											foreach ($value['AppFinRecord'] as $key => $subValue) {
												$appfinrecord = $learningdelivery->addChild('AppFinRecord');
													\APP\Export::ilrAddValue(
														$fields,
														$subValue,
														$appfinrecord
													);
											}
										}

										if (isset($value['ProviderSpecDeliveryMonitoring']) && $value['ProviderSpecDeliveryMonitoring']) {
											foreach ($value['ProviderSpecDeliveryMonitoring'] as $key => $subValue) {
												if (
													isset($subValue['ProvSpecDelMonOccur']) &&
													isset($subValue['ProvSpecDelMon']) &&
													$subValue['ProvSpecDelMon']
												) {
													$providerspecdeliverymonitoring = $learningdelivery->addChild('ProviderSpecDeliveryMonitoring');

														\APP\Export::ilrAddValue(
															[
																'ProvSpecDelMonOccur', 'ProvSpecDelMon'
															],
															$subValue,
															$providerspecdeliverymonitoring
														);
												}
											}
										}

										if (
											isset($value['LearningDeliveryHE']) &&
											$value['LearningDeliveryHE']
										) {
											foreach ($value['LearningDeliveryHE'] as $key => $subValue) {
												$learningdeliveryhe_fields = ['SSN', 'QUALENT3', 'SOC2000', 'SEC', 'UCASAPPID', 'TYPEYR', 'MODESTUD', 'FUNDLEV', 'FUNDCOMP', 'STULOAD', 'YEARSTU', 'MSTUFEE', 'PCOLAB', 'PCFLDCS', 'PCSLDCS', 'PCTLDCS', 'SPECFEE', 'NETFEE', 'GROSSFEE', 'DOMICILE', 'ELQ', 'HEPostCode'];

												if ($year_export < 23) {
													array_unshift($learningdeliveryhe_fields , 'NUMHUS');
												}


												$learningdeliveryhe = $learningdelivery->addChild('LearningDeliveryHE');
													\APP\Export::ilrAddValue(
														$learningdeliveryhe_fields,
														$subValue,
														$learningdeliveryhe
													);

											}
										}

								}
							}
						}
					// user is valid
					$LearnerDestinationandProgressions[] = json_decode($user->LearnerDestinationandProgression ?? '', true);
					$export_response->users->success[] = $current_user;
				} else {
					// user did not pass validation, report!
					$current_user->errors = $validateUser->errors;
					$export_response->users->error[] = $current_user;
				}
			}
		}


		// if at least one user passed, generate file
		if (count($export_response->users->success) > 0) {

			// Add any LearnerDestinationandProgressions that passed validation
			foreach ($LearnerDestinationandProgressions as $key => $LearnerDestinationandProgression) {
				// loop success story, if ULN match, output LearnerDestinationandProgressions
				foreach ($export_response->users->success as $key => $success_user) {
					if (
						isset($LearnerDestinationandProgression['ULN']) &&
						$LearnerDestinationandProgression['ULN'] == $success_user->ULN &&
						count($LearnerDestinationandProgression['DPOutcome']) > 0
					) {
						foreach ($LearnerDestinationandProgression['DPOutcome'] as $key => $DPOutcome) {
							if (
								isset($DPOutcome['OutType']) &&
								isset($DPOutcome['OutCode']) &&
								isset($DPOutcome['OutStartDate']) &&
								isset($DPOutcome['OutCollDate']) &&
								$DPOutcome['OutType'] &&
								$DPOutcome['OutCode'] &&
								$DPOutcome['OutStartDate'] &&
								$DPOutcome['OutCollDate']
							) {
								$progression = $xml->addChild('LearnerDestinationandProgression');
								$progression->addChild('LearnRefNumber', $LearnerDestinationandProgression['LearnRefNumber']);
								$progression->addChild('ULN', $LearnerDestinationandProgression['ULN']);
								$XML_DPOutcome = $progression->addChild('DPOutcome');
								if (isset($DPOutcome['OutType'])) {
									$XML_DPOutcome->addChild('OutType', $DPOutcome['OutType']);
								}
								if (isset($DPOutcome['OutCode'])) {
									$XML_DPOutcome->addChild('OutCode', $DPOutcome['OutCode']);
								}

								// \Carbon\Carbon::parse($DPOutcome['OutStartDate'])->addDay(1)->format('Y-m-d')

								if (isset($DPOutcome['OutStartDate'])) {
									$XML_DPOutcome->addChild('OutStartDate', \Carbon\Carbon::parse($DPOutcome['OutStartDate'])->format('Y-m-d'));
								}
								if (isset($DPOutcome['OutEndDate'])) {
									$XML_DPOutcome->addChild('OutEndDate', \Carbon\Carbon::parse($DPOutcome['OutEndDate'])->format('Y-m-d'));
								}
								if (isset($DPOutcome['OutCollDate'])) {
									$XML_DPOutcome->addChild('OutCollDate', \Carbon\Carbon::parse($DPOutcome['OutCollDate'])->format('Y-m-d'));
								}
							}
						}
					}
				}
			}
			// $header = $xml->addChild('Header');

			$dom = new \DOMDocument("1.0");
			$dom->preserveWhiteSpace = false;
			$dom->formatOutput = true;
			$dom->loadXML($xml->asXML(), LIBXML_NOWARNING);
			$xml_content = $dom->saveXML();
			//die();

			//ILR-LLLLLLLL-YYYY-yyyymmdd-hhmmss-NN.XML
			$export_response->file_name = "ILR-" . $export_config['data']['ukprn'] . "-" . $export_config['data']['collection_year'] . "-" . \Carbon\Carbon::now('Europe/London')->format('Ymd') . "-" . \Carbon\Carbon::now('Europe/London')->format('His') . "-" . $export_config['data']['nn'] . ".xml";
			file_put_contents(
				$temp_path . $export_response->file_name, $xml_content
			);

			// log exported file and user who did it!
			\Models\LogExportImport::insertRecord($xml_content, '.xml', $export_response->file_name, false, 'exports', json_encode($params));
		}
		return $export_response;

		//print_r($LearnerDestinationandProgressions);
	}
}