<?php

namespace APP\Controllers;

use APP\Auth;
use APP\Form;
use APP\SmartTable;
use APP\Templates;
use APP\Tools;
use Carbon\Carbon;
use Error;
use Exception;
use Illuminate\Database\Capsule\Manager as DB;
use Models\ApprenticeshipStandardUser;
use Models\City;
use Models\Company;
use Models\Country;
use Models\CouponUsage;
use Models\CustomFieldQuery;
use Models\CustomFieldValue;
use Models\CustomReport;
use Models\CustomReportField;
use Models\CustomReportPermission;
use Models\Department;
use Models\Designation;
use Models\DocumentTemplate;
use Models\DocumentTemplateBinding;
use Models\Field;
use Models\FieldOption;
use Models\FormField;
use Models\FormsExportImportLog;
use Models\Graph;
use Models\GraphField;
use Models\Group;
use Models\LearningModule;
use Models\LearningModuleCategory;
use Models\LearningModuleDeliveryProviderType;
use Models\LearningModuleGroupDepartmentCode;
use Models\LearningModuleType;
use Models\LearningProvider;
use Models\LearningResult;
use Models\LearningResultAnswer;
use Models\Location;
use Models\Picklist;
use Models\PurchasePaymentTransaction;
use Models\Role;
use Models\Schedule;
use Models\ScheduleLink;
use Models\TargetCatalogue;
use Models\User;
use Models\UserCustomFormValue;
use Models\UserForm;
use Models\UserFormValue;
use Psr\Container\ContainerInterface;
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Svg\Document;
use stdClass;
use Models\UserScheduleWaitingList;

/**
 * Controller for handling Custom Reports(Data table in UI section)
 * All custom report API are handling here
 * @package APP\Controllers
 * @version 0.1
 */
class CustomReportController extends Controller
{
	public static $category_maping = [
		'user'=>'users',
		'event'=>'schedules',
		'programme'=>'apprenticeship_standards',
		'learning_resource'=>'learning_resources',
		'lesson'=>'learning_resources',
		'venue' => 'schedules',
	];
	private  $reportData = null;
    private $reportType = null;
    private $rawJoins = [];
	private $selectedFields = null;


	/**
	*Fields store all system fields and its relations
	* @param name    to display the table header
	* @param slug    used for accessing variable in user object
	* @param resource used for Eager loading
	* @param group_by Not using Custom Report section It will using for graph section
	* @param filter It will define how we can handle the filter
	* @param multi_select If we need to display multi select drop down in report need to set true
	* @param class If we set multi_select to true then we need to set Class name to set Drop Down value in front end
	*/
	public static $fields = [
		"users"=>[
			[
				"name"=>"%%user%% ID", // Employee ID is usercode, this might get confusing.
				"slug"=>"id",
				"select"=>"users.id",
				"resource"=>"users",
				"group_by"=>"id",
				"filter"=>"like",
				"sort"=>"users.id",
				"type"=>"number",
				"filter_key"=>["users.id"],
				"user_specific"=>true,
				"user_specific_key"=>"id",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.id"

			],
			[
				"name"=>"First Name",
				"slug"=>"fname",
				"select"=>"users.fname",
				"resource"=>"users",
				"group_by"=>"fname",
				"sort"=>"users.fname",
				"filter"=>"like",
				"filter_key"=>["users.fname"],
				"type"=>"text",
				"user_specific"=>true,
				"user_specific_key"=>"fname",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.fname"
			],
			[
				"name"=>"Last Name",
				"slug"=>"lname",
				"select"=>"users.lname",
				"resource"=>"users",
				"group_by"=>"lname",
				"sort"=>"users.lname",
				"filter"=>"like",
				"type"=>"text",
				"user_specific"=>true,
				"user_specific_key"=>"lname",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.lname",
				"filter_key"=>["users.lname"]
			],
			[
				"name"=>"Role",
				"slug"=>"role_name",
				"select"=>"roles.name as role_name",
				"resource"=>"Role",
				"group_by"=>"role_id",
				"type"=>"text",
				"multi_select"=>true,
				"filter"=>"in",
				"sort"=>"roles.name",
				"class"=>Role::class,
				"filter_key"=>"roles.id",
				"type"=>"text",
				"data_key"=>"id",
				"user_specific"=>true,
				"user_specific_column">"users.role_id",
				"user_specific_model"=>"User",
				"user_specific_key"=>"role_id"
			],
			[
				"name"=>"Email",
				"slug"=>"email",
				"select"=>"users.email",
				"resource"=>"users",
				"grpup_by"=>"email",
				"filter"=>"like",
				"type"=>"text",
				"sort"=>"users.email",
				"user_specific_key"=>"email",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.email",
				"user_specific"=>true,
				"filter_key"=>["users.email"]
			],
			[
				"name"=>"Phone",
				"slug"=>"phone",
				"select"=>"users.phone",
				"resource"=>"users",
				"group_by"=>"phone",
				"filter"=>"like",
				"type"=>"text",
				"sort"=>"users.phone",
				"user_specific_key"=>"phone",
				"user_specific"=>true,
				"user_specific_column"=>"users.phone",
				"user_specific_model"=>"User",
				"filter_key"=>["users.phone"]
			],
			[
				"name"=>"Username",
				"slug"=>"username",
				"select"=>"users.username",
				"resource"=>"users",
				"grpup_by"=>"username",
				"filter"=>"like",
				"type"=>"text",
				"sort"=>"users.username",
				"user_specific"=>true,
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.username",
				"user_specific_key"=>"username",
				"filter_key"=>["users.username"]
			],
			[
				"name"=>"Alternative Email",
				"slug"=>"email2",
				"select"=>"users.email2",
				"resource"=>"users",
				"group_by"=>"email2",
				"filter"=>"like",
				"type"=>"text",
				"sort"=>"users.email2",
				"filter_key"=>["users.email2"]
			],
			[
				"name"=>"%%national_insurance_number%% ",
				"slug"=>"NINumber",
				"select"=>"users.NINumber",
				"resource"=>"users",
				"group_by"=>"NINumber",
				"filter"=>"like",
				"type"=>"text",
				"sort"=>"users.NINumber",
				"filter_key"=>["users.NINumber"]
			],
			[
				"name"=>"ULN",
				"slug"=>"ULN",
				"select"=>"users.ULN",
				"resource"=>"users",
				"grpup_by"=>"ULN",
				"filter"=>"like",
				"type"=>"number",
				"sort"=>"users.ULN",
				"filter_key"=>["users.ULN"]
			],
			[
				"name"=>"%%country%%",
				"slug"=>"country_name",
				"select"=>"countries.name as country_name",
				"resource"=>"Country",
				"grpup_by"=>"country_id",
				"multi_select"=>true,
				"filter"=>"in",
				"class"=>Country::class,
				"filter_key"=>"countries.id",
				"type"=>"text",
				"data_key"=>"id",
				"sort"=>"countries.name",
				"user_specific"=>true,
				"user_specific_key"=>"country_id",
				"user_specific_column"=>"users.country_id",
				"user_specific_model"=>"User"
			],
			[
				"name"=>"%%city%%",
				"slug"=>"city_name",
				"select"=>"cities.name as city_name",
				"resource"=>"City",
				"group_by"=>"city_id",
				"multi_select"=>true,
				"filter"=>"in",
				"class"=>City::class,
				"filter_key"=>"cities.id",
				"type"=>"text",
				"data_key"=>"id",
				"sort"=>"cities.name",
				"user_specific"=>true,
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.city_id",
				"user_specific_key"=>"city_id"

			],
			[
				"name"=>"%%department%%",
				"slug"=>"department_name",
				"select"=>"departments.name as department_name",
				"resource"=>"Department",
				"group_by"=>"department_id",
				"multi_select"=>true,
				"filter"=>"in",
				"class"=>Department::class,
				"filter_key"=>"departments.id",
				"type"=>"text",
				"data_key"=>"id",
				"sort"=>"departments.name",
				"user_specific_key"=>"department_id",
				"user_specific_column"=>"users.department_id",
				"user_specific"=>true,
				"user_specific_model"=>"User"

			],
			[
				"name"=>"Designation",
				"slug"=>"designation_name",
				"select"=>"designations.name as designation_name",
				"resource"=>"Designation",
				"group_by"=>"designation_id",
				"type"=>"text",
				"filter"=>"in",
				"filter_key"=>"designations.id",
				"multi_select"=>true,
				"class"=>Designation::class,
				"data_key"=>"id",
				"sort"=>"designations.name",
				"user_specific_model"=>"User",
				"user_specific"=>true,
				"user_specific_column"=>"users.designation_id",
				"user_specific_key"=>"designation_id"
			],
			[
				"name"=>"%%company%%",
				"slug"=>"company_name",
				"select"=>"companies.name as company_name",
				"resource"=>"Company",
				"group_by"=>"company_id",
				"type"=>"text",
				"multi_select"=>true,
				"class"=>Company::class,
				"filter"=>"in",
				"filter_key"=>"companies.id",
				"data_key"=>"id",
				"sort"=>"companies.name",
				"user_specific"=>true,
				"user_specific_key"=>"company_id",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.company_id"
			],
			[
				"name"=>"%%location%%",
				"slug"=>"location_name",
				"select"=>"locations.name as location_name",
				"resource"=>"Location",
				"group_by"=>"location_id",
				"type"=>"text",
				"multi_select"=>true,
				"class"=>Location::class,
				"filter_key"=>"locations.id",
				"filter"=>"in",
				"data_key"=>"id",
				"sort"=>"locations.name",
				"user_specific"=>true,
				"user_specific_column"=>"users.location_id",
				"user_specific_model"=>"User",
				"user_specific_key"=>"location_id"
			],
			[
				"name"=>"%%groups%%",
				"slug"=>"groups.name",
				"resource"=>"Groups",
				"type"=>"array",
				"filter"=>"in",
				"filter_key"=>"groups.id",
				"data_key"=>"id",
				"multi_select"=>true,
				"class"=>Group::class
			],
			[
				"name"=>"Sex",
				"slug"=>"Sex",
				"select"=>"users.Sex",
				"resource"=>"users",
				"group_by"=>"Sex",
				"type"=>"text",
				"filter"=>"like",
				"filter_key"=>["users.Sex"]
			],
			[
				"name"=>"%%managers%%",
				"slug"=>"managers.fname+lname",
				"resource"=>"Managers",
				"type"=>"array",
				"multi_select"=>false,
				"fetch_only_enabled"=>true,
				"filter"=>"like",
				"filter_key"=>["fname","lname"],
				"user_specific"=>true,
				"user_specific_key"=>"id",
				"user_specific_model"=>"Managers",
				"user_specific_column"=>"manager_users.manager_id"
			],
			[
				"name" => "Sub "."%%department%%",
				"slug" => "sub_departments.department.name",
				"select" => "departments.name as department_name",
				"resource" => "SubDepartments.Department", //Relationship
				"group_by" => "department_id",
				"multi_select" => true,
				"filter" => "in",
				"class" => Department::class,
				"conditional_clauses" => [
					[
						'clause' => 'where',
						'condition' => [
							[
								'parent_id', '!=', null
							],
						],
					],
				],
				"filter_key" => "departments.id",
				"type" => "array",
				"data_key" => "id",
				"user_specific_key" => "department_id",
				"user_specific_column" => "users.department_id",
				"user_specific" => true,
				"user_specific_model" => "User"
			],
			[
				"name"=>"Registration Date",
				"slug"=>'user_registration_dt',
				"resource"=>"users",
				"group_by"=>"registration_dt",
				"filter"=>"between",
				"type"=>"date-range",
				"select"=>"users.registration_dt as user_registration_dt",
				"sort"=>"users.registration_dt",
				"filter_key"=> "users.registration_dt"
			],
			[
				"name"=>"Agreement ID",
				"slug"=>"AgreeId",
				"select"=>"ilr_learner_employment_statuses.AgreeId as AgreeId",
				"resource"=>"employmentStatus",
				"type"=>"number",
				"sort"=>"ilr_learner_employment_statuses.AgreeId",
				"filter_key"=>"ilr_learner_employment_statuses.AgreeId",
				"filter"=>"like",
			],
			[
				"name"=>"Employer ID",
				"slug"=>"EmpId",
				"select"=>"ilr_learner_employment_statuses.EmpId as EmpId",
				"resource"=>"employmentStatus",
				"type"=>"number",
				"sort"=>"ilr_learner_employment_statuses.EmpId",
				"filter_key"=>"ilr_learner_employment_statuses.EmpId",
				"filter"=>"like",
			],
			[
			"name" => "Apprenticeship standard code",
			"slug" => "StdCode",
			"select"=> "ilr_learning_deliveries.StdCode as StdCode",
			"resource"=> "ilrLearningDeliveries",
			"type"=> "number",
			"sort"=> "ilr_learning_deliveries.StdCode",
			"filter_key"=> ["ilr_learning_deliveries.StdCode"],
			"filter" => "like"
			],
		[
			"name" => "Prior Attainment",
			"slug" => "PriorAttainment",
			"select"=> "ilr_user_prior_attainment_level.name as PriorAttainment",
			"resource" => "priorAttainment",
			"type"=> "text",
			"sort"=> "ilr_user_prior_attainment_level.name",
			"filter_key"=> ["ilr_user_prior_attainment_level.name"],
			"filter" => "like"
		]
		],

		"schedules"=>[
			[
				"name"=>"Employee ID",
				"slug"=>"user_id",
				"select"=>"users.id as user_id",
				"resource"=>"User",
				"group_by"=>"id",
				"filter"=>"like",
				"sort"=>"users.id",
				"type"=>"number",
				"filter_key"=>["users.id"],
				"user_specific"=>true,
				"user_specific_key"=>"id",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.id"
			],
			[
				"name"=>"Name",
				"slug"=>"schedule_name",
				"resource"=>"getSchedule",
				"type"=>"text",
				"filter"=>"like",
				"select"=>"schedules.name as schedule_name",
				"sort"=>"schedules.name",
				"filter_key"=>["schedules.name"],
				"join"=>["schedules.id","=","schedule_links.schedule_id"]
			],
			[
				"name" => "%%event%% Role",
				"slug" => "event_role",
				"resource" => "schedules",
				"type"=>"text",
				"filter"=>"like",
				"select"=>"schedule_links.type as event_role",
				"filter_key"=>["schedule_links.type"],
				"sort"=>"schedule_links.type"
			],
			[
				"name"=>"Username",
				"slug"=>"user_username",
				"resource"=>"User",
				"type"=>"text",
				"filter"=>"like",
				"select"=>"users.username as user_username",
				"sort"=>"users.username",
				"filter_key"=>["users.username"],
				"user_specific"=>true,
				"user_specific_model"=>"User",
				"user_specific_key"=>"username",
				"user_specific_column"=>"users.username"
			],
			[
				"name"=>"Manager Approval Status",
				"slug"=>"approved",
				"resource"=>"schedules",
				"type"=>"status",
				"filter"=>"like",
				"drop_down"=>[
						0=>"Awaiting Approval",
						1=>"Approved"
				],
				"select"=>"schedule_links.approved as approved",
				"sort"=>"schedule_links.approved",
				"filter_key"=>["schedule_links.approved"]
			],
			[
				"name"=>"Payment Status",
				"slug"=>"is_paid",
				"resource"=>"schedules",
				"type"=>"status",
				"filter"=>"like",
				"drop_down"=>[
					"NaN"=>"Not Applicable",
					"0"=>"Awaiting Payment",
					"1"=>"Paid"
				],
				"select"=>"schedule_links.is_paid",
				"sort"=>"schedule_links.is_paid",
				"filter_key"=>["schedule_links.is_paid"]
			],
			[
				"name"=>"User Waiting List Status",
				"slug"=>"waiting_list",
				"resource"=>"schedules",
				"type"=>"status",
				"filter"=>"like",
				"drop_down"=>[
					"users"=>"Assigned",
					"users_queue"=>"Waiting List",
					"managers"=>"Not Applicable"
				],
				"select"=>"schedule_links.type as waiting_list",
				"sort"=>"schedule_links.type",
				"filter_key"=>["schedule_links.type"],
			],
			[
				"name"=>"First Name",
				"slug"=>'fname',
				"resource"=>"User",
				"type"=>"text",
				"filter"=>"like",
				"filter_key"=>["users.fname"],
				"select"=>"users.fname",
				"sort"=>"users.fname",
				"user_specific"=>true,
				"user_specific_key"=>"fname",
				"user_specific_column"=>"users.fname",
				"user_specific_model"=>"User"
			],
			[
				"name"=>"Last Name",
				"slug"=>'lname',
				"resource"=>"User",
				"type"=>"text",
				"filter"=>"like",
				"filter_key"=>["users.lname"],
				"select"=>"users.lname",
				"sort"=>"users.lname",
				"user_specific"=>true,
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.lname",
				"user_specific_key"=>"lname"
			],
			[
				"name"=>"Email",
				"slug"=>'email',
				"resource"=>"User",
				"type"=>"text",
				"filter"=>"like",
				"select"=>"users.email",
				"sort"=>"users.email",
				"filter_key"=>["users.email"],
				"user_specific"=>true,
				"user_specific_column"=>"users.email",
				"user_specific_model"=>"User",
				"user_specific_key"=>"email"
			],
			[
				"name"=>"%%department%%",
				"resource"=>"User.Department",
				"slug"=>"department_name",
				"type"=>"text",
				"group_by"=>"department_id",
				"multi_select"=>true,
				"filter"=>"in",
				"class"=>Department::class,
				"filter_key"=>"departments.id",
				"select"=>"departments.name as department_name",
				"data_key"=>"id",
				"sort"=>"departments.name",
				"user_specific"=>true,
				"user_specific_key"=>"department_id",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.department_id"
			],
			[
				"name"=>"Role",
				"resource"=>"User.Role",
				"slug"=>"role_name",
				"type"=>"text",
				"group_by"=>"role_id",
				"multi_select"=>true,
				"filter"=>"in",
				"class"=>Role::class,
				"filter_key"=>"roles.id",
				"select"=>"roles.name as role_name",
				"data_key"=>"id",
				"sort"=>"roles.name",
				"user_specific"=>true,
				"user_specific_column"=>"users.role_id",
				"user_specific_model"=>"User",
				"user_specific_key"=>"role_id"
			],
			[
				"name"=>"Job Title",
				"resource"=>"User.Designation",
				"slug"=>"designation_name",
				"type"=>"text",
				"group_by"=>"designation_id",
				"multi_select"=>true,
				"filter"=>"in",
				"class"=>Designation::class,
				"filter_key"=>"designations.id",
				"select"=>"designations.name as designation_name",
				"data_key"=>"id",
				"sort"=>"designations.name",
				"user_specific"=>true,
				"user_specific_key"=>"designation_id",
				"user_specific_model">"User",
				"user_specific_column"=>"users.designation_id"
			],
			[
				"name"=>"%%location%%",
				"resource"=>"User.Location",
				"slug"=>"location_name",
				"type"=>"text",
				"group_by"=>"location_id",
				"multi_select"=>true,
				"filter"=>"in",
				"class"=>Location::class,
				"filter_key"=>"locations.id",
				"select"=>"locations.name as location_name",
				"data_key"=>"id",
				"sort"=>"locations.name",
				"user_specific"=>true,
				"user_specific_column"=>'users.location_id',
				"user_specific_key"=>"location_id",
				"user_specific_model"=>"User"
			],
			[
				"name"=>"%%group%%",
				"slug"=>"user.groups.name",
				"resource"=>"User.Groups",
				"type"=>"array",
				"filter"=>"in",
				"filter_key"=>"groups.id",
				"data_key"=>"id",
				"multi_select"=>true,
				"class"=>Group::class
			],
			[
				"name"=>"%%company%%",
				"slug"=>'company_name',
				"resource"=>"User.Company",
				"type"=>"text",
					"multi_select"=>true,
				"class"=>Company::class,
				"filter"=>"in",
				"filter_key"=>"id",
				"select"=>"companies.name as company_name",
				"sort"=>"companies.name",
				"user_specific"=>true,
				"user_specific_key"=>"company_id",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.company_id"
			],
			[
				"name"=>"%%country%%",
				"slug"=>"country_name",
				"resource"=>"User.Country",
				"grpup_by"=>"user.country_id",
				"multi_select"=>true,
				"filter"=>"in",
				"class"=>Country::class,
				"filter_key"=>"countries.id",
				"type"=>"text",
				"select"=>"countries.name as country_name",
				"data_key"=>"id",
				"sort"=>"countries.name",
				"user_specific"=>true,
				"user_specific_key"=>'country_id',
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.country_id"
			],
			[
				"name"=>"%%city%%",
				"slug"=>"city_name",
				"resource"=>"User.City",
				"group_by"=>"user.city_id",
				"multi_select"=>true,
				"filter"=>"in",
				"class"=>City::class,
				"filter_key"=>"cities.id",
				"type"=>"text",
				"select"=>"cities.name as city_name",
				"data_key"=>"id",
				"sort"=>"cities.name",
				"user_specific"=>true,
				"user_specific_key"=>"city_id",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.city_id"
			],
			[
				"name"=>"Status",
				"slug"=>"user_status",
				"resource"=>"User",
				"type"=>"status",
				"drop_down"=>[
					1=>"Enabled",
					0=>"Disabled"
				],
				"filter"=>"like",
				"select"=>"users.status as user_status",
				"sort"=>"users.status",
				"filter_key"=>"users.status",
				"user_specific"=>true,
				"user_specific_column"=>"users.status",
				"user_specific_model"=>"User",
				"user_specific_key"=>"status"
			],
			[
				"name"=>"User Managers",
				"slug"=>"user.managers.fname+lname",
				"resource"=>"User.Managers",
				"type"=>"array",
				"multi_select"=>false,
				"filter"=>"like",
				"filter_key"=>["fname","lname"],
				"user_specific"=>true,
				"user_specific_key"=>"id",
				"user_specific_model"=>"Managers",
				"user_specific_column"=>"manager_users.manager_id"
			],
			[
				"name"=>"%%event%% Type",
				"slug"=>"schedule_type",
				"resource"=>"getSchedule",
				"type"=>"text",
				"filter"=>"like",
				"select"=>"schedules.type as schedule_type",
				"sort"=>"schedules.type",
				"filter_key"=>["schedules.type"]
			],
			[
				"name"=>"Description",
				"slug"=>"schedule_description",
				"resource"=>"getSchedule",
				"type"=>"text",
				"filter"=>"like",
				"select"=>"schedules.description as schedule_description",
				"sort"=>"schedules.description",
				"filter_key"=>["schedules.description"]
			],
			[
				"name"=>"Category",
				"slug"=>"learning_module_categories_name",
				"resource"=>"getSchedule.Category",
				"type"=>"text",
				"filter"=>"like",
				"select"=>"learning_module_categories.name as learning_module_categories_name",
				"sort"=>"learning_module_categories.name",
				"filter_key"=>["learning_module_categories.name"]
			],
			[
				"name"=>"%%lesson%%",
				"slug"=>"learning_module_name",
				"resource"=>"getSchedule.Lesson",
				"type"=>"text",
				"filter"=>"in",
				"filter_key"=>"learning_modules.id",
				"multi_select"=>true,
				"class"=>LearningModule::class,
				"data_key"=>"id",
				"select"=>"learning_modules.name as learning_module_name"
			],
			[
				"name"=>"Venue",
				"slug"=>"venue_name",
				"resource"=>"getSchedule.VenueDeatils",
				"type"=>"text",
				"filter"=>"like",
				"select"=>"venues.name as venue_name",
				"sort"=>"venues.name",
				"filter_key"=>["venues.name"]
			],
			[
				"name"=>"Date",
				"slug"=>"schedule_start_date",
				"resource"=>"getSchedule",
				"type"=>"date-range",
				"select"=>"schedules.start_date as  schedule_start_date",
				"sort"=>"schedules.start_date",
				"filter"=>"between",
				"filter_key"=>"schedules.start_date"
			],
			[
				"name"=>"Duration",
				"slug"=>"schedule_duration",
				"resource"=>"getSchedule",
				"type"=>"number",
				"filter"=>"like",
				"select"=>"schedules.duration as schedule_duration",
				"sort"=>"schedules.duration",
				"filter_key"=>["schedules.duration"]
			],
			[
				"name"=>"Cost",
				"slug"=>"schedule_cost",
				"resource"=>"getSchedule",
				"type"=>"number",
				"filter"=>"like",
				"select"=>"schedules.cost as schedule_cost",
				"sort"=>"schedules.cost",
				"filter_key"=>["schedules.cost"]
			],
			[
				"name"=>"Allow user to enrol",
				"slug"=>"schedule_enrole_any_learner",
				"resource"=>"getSchedule",
				"type"=>"boolean",
				"select"=>"schedules.enrole_any_learner as schedule_enrole_any_learner",
				"sort"=>"schedules.enrole_any_learner",
				"filter_key"=>["schedules.enrole_any_learner"]
			],
			[
				"name"=>"Max Class size",
				"slug"=>"schedule_maxclass", /*The slug should be similar to what we select in Select*/
				"resource"=>"getSchedule",
				"type"=>"number",
				"filter"=>"like",
				"select"=>"schedules.maxclass as  schedule_maxclass", /*Here the schedule_maxclass is similar to schedule_maxclass*/
				"sort"=>"schedules.maxclass",
				"filter_key"=>["schedules.maxclass"]
			],
			[
				"name"=>"Min class size",
				"slug"=>"schedule_minclass",
				"resource"=>"getSchedule",
				"type"=>"number",
				"filter"=>"like",
				"select"=>"schedules.minclass as schedule_minclass",
				"sort"=>"schedules.minclass",
				"filter_key"=>["schedules.minclass"]
			],
			[
				"name"=>"Learning Resources",
				"slug"=>"get_schedule.resources.name",
				"resource"=>"getSchedule.Resources",
				"multi_select"=>false,
				"class"=>LearningModule::class,
				"type"=>"array",
				"filter"=>"in",
				"filter_key"=>"id",
				"data_key"=>"id"
			],
			[
				"name"=>"%%group_department_code%%",
				"slug"=>"group_department_code_name",
				"resource"=>"getSchedule.Resources.LearningModuleGroupDepartmentCode",
				"multi_select"=>true,
				"class"=>LearningModuleGroupDepartmentCode::class,
				"type"=>"text",
				"filter"=>"in",
				"filter_key"=>"learning_module_group_department_codes.id",
				"select"=>"learning_module_group_department_codes.name as group_department_code_name",
				"sort"=>"learning_module_group_department_codes.name",
				"data_key"=>"id"
			],
			[
				"name"=>"%%event%% Managers",
				"slug"=>"get_schedule.managers.fname+lname",
				"resource"=>"getSchedule.Managers",
				"type"=>"array",
				"multi_select"=>false,
				"filter"=>"like",
				"filter_key"=>["fname","lname"]
			],
			[
				"name"=>"%%event%% Status",
				"slug"=>"completion_status",
				"resource"=>"schedules",
				"type"=>"text",
				"filter"=>"like",
				"select"=>"schedule_links.completion_status",
				"sort"=>'schedule_links.completion_status',
				"filter_key"=>["schedule_links.completion_status"]
			],
			[
				"name"=>"Authorised Absence",
				"slug"=>"is_authorised",
				"resource"=>"schedules",
				"type"=>"boolean",
				"select"=>"schedule_links.is_authorised",
				"sort"=>"schedule_links.is_authorised",
				"filter_key"=>["is_authorised"]
			],
			[
				"name"=>"Absence Notes",
				"slug"=>"authorisation_notes",
				"resource"=>"schedules",
				"type"=>"text",
				"filter"=>"like",
				"select"=>"schedule_links.authorisation_notes",
				"sort"=>"schedule_links.authorisation_notes",
				"filter_key"=>"schedule_links.authorisation_notes"
			],
			[
				"name" => "Sub "."%%Department%%",
				"slug" => "user.sub_departments.department.name",
				"select" => "departments.name as department_name",
				"resource" => "User.SubDepartments.Department", //Relationship
				"group_by" => "department_id",
				"multi_select" => true,
				"filter" => "in",
				"class" => Department::class,
				"conditional_clauses" => [[
						'clause' => 'where',
						'condition' => [
								['parent_id', '!=', null],
						],
				],],
				"filter_key" => "id",
				"type" => "array",
				"data_key" => "id",
			],
			[
				"name"=>"Cancellation Reason",
				"slug"=>"cancellation_reason",
				"resource"=>"schedules",
				"type"=>"text",
				"filter"=>"like",
				"select"=>"schedule_links.cancellation_reason",
				"sort"=>"schedule_links.cancellation_reason",
				"filter_key"=>"schedule_links.cancellation_reason"
			],
			[
				"name"=>"Agreement ID",
				"slug"=>"AgreeId",
				"select"=>"ilr_learner_employment_statuses.AgreeId as AgreeId",
				"resource"=>"User.employmentStatus",
				"type"=>"number",
				"sort"=>"ilr_learner_employment_statuses.AgreeId",
				"filter_key"=>"ilr_learner_employment_statuses.AgreeId",
				"filter"=>"like",
			],
			[
				"name"=>"Employer ID",
				"slug"=>"EmpId",
				"select"=>"ilr_learner_employment_statuses.EmpId as EmpId",
				"resource"=>"User.employmentStatus",
				"type"=>"number",
				"sort"=>"ilr_learner_employment_statuses.EmpId",
				"filter_key"=>"ilr_learner_employment_statuses.EmpId",
				"filter"=>"like",
			],
			[
			"name" => "Apprenticeship standard code",
			"slug" => "StdCode",
			"select"=> "ilr_learning_deliveries.StdCode as StdCode",
			"resource"=> "User.ilrLearningDeliveries",
			"type"=> "number",
			"sort"=> "ilr_learning_deliveries.StdCode",
			"filter_key"=> ["ilr_learning_deliveries.StdCode"],
			"filter" => "like"
			],
		[
			"name" => "Prior Attainment",
			"slug" => "PriorAttainment",
			"select"=> "ilr_user_prior_attainment_level.name as PriorAttainment",
			"resource" => "User.priorAttainment",
			"type"=> "text",
			"sort"=> "ilr_user_prior_attainment_level.name",
			"filter_key"=> ["ilr_user_prior_attainment_level.name"],
			"filter" => "like"
		]
		],

		"learning_resources"=>[
			[
				"name"=>"Employee ID",
				"slug"=>"user_id",
				"resource"=>"User",
				"type"=>"number",
				"filter"=>"like",
				"select"=>"users.id as user_id",
				"sort"=>"users.id",
				"filter_key"=>["users.id"],
				"user_specific"=>true,
				"user_specific_key"=>"id",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.id"
			],
			[
				"name"=>"Username",
				"slug"=>'users_username',
				"resource"=>"User",
				"type"=>"text",
				"filter"=>"like",
				"select"=>"users.username as users_username",
				"sort"=>"users.username",
				"filter_key"=>["users.username"],
				"user_specific"=>true,
				"user_specific_key"=>"username",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.username"
			],
			[
				"name"=>"First Name",
				"slug"=>'user_fname',
				"resource"=>"User",
				"type"=>"text",
				"filter"=>"like",
				"select"=>"users.fname as user_fname",
				"sort"=>"users.fname",
				"filter_key"=>["users.fname"],
				"user_specific"=>true,
				"user_specific_key"=>"fname",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.fname"
			],
			[
				"name"=>"Last Name",
				"slug"=>'user_lname',
				"resource"=>"User",
				"type"=>"text",
				"filter"=>"like",
				"select"=>"users.lname as user_lname",
				"sort"=>"users.lname",
				"filter_key"=>["users.lname"],
				"user_specific"=>true,
				"user_specific_key"=>"lname",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.lname"
			],
			[
				"name"=>"User Managers",
				"slug"=>"user.managers.fname+lname",
				"resource"=>"User.Managers",
				"type"=>"array",
				"multi_select"=>false,
				"filter"=>"like",
				"filter_key"=>["fname","lname"],
				"user_specific"=>true,
				"user_specific_key"=>"id",
				"user_specific_model"=>"Managers",
				"user_specific_column"=>"manager_users.manager_id"
			],
			[
				"name"=>"Role",
				"slug"=>'role_name',
				"resource"=>"User.Role",
				"type"=>"text",
				"multi_select"=>true,
				"class"=>Role::class,
				"filter"=>"in",
				"select"=>"roles.name as role_name",
				"sort"=>"roles.name",
				"filter_key"=>"roles.name",
				"user_specific"=>true,
				"user_specific_key"=>"role_id",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.role_id"
			],
			[
				"name"=>"Email",
				"slug"=>'user_email',
				"resource"=>"User",
				"type"=>"text",
				"filter"=>"like",
				"select"=>"users.email as user_email",
				"sort"=>"users.email",
				"filter_key"=>["users.email"],
				"user_specific"=>true,
				"user_specific_key"=>"email",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.email"
			],
			[
				"name"=>"Job Title",
				"slug"=>'designation_name',
				"resource"=>"User.Designation",
				"type"=>"text",
				"select"=>"designations.name as designation_name",
				"sort"=>"designations.name",
				"multi_select"=>true,
				"class"=>Designation::class,
				"data_key"=>"id",
				"filter"=>"in",
				"filter_key"=>"designations.id",
				"user_specific"=>true,
				"user_specific_key"=>"designation_id",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.designation_id"
			],
			[
				"name"=>"%%location%%",
				"slug"=>'location_name',
				"resource"=>"User.Location",
				"type"=>"text",
				"multi_select"=>true,
				"class"=>Location::class,
				"filter_key"=>"locations.id",
				"filter"=>"in",
				"select"=>"locations.name as location_name",
				"sort"=>"locations.name",
				"data_key"=>"id",
				"user_specific"=>true,
				"user_specific_key"=>"location_id",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.location_id"
			],
			[
				"name"=>"%%group%%",
				"slug"=>"user.groups.name",
				"resource"=>"User.Groups",
				"type"=>"array",
				"filter"=>"in",
				"filter_key"=>"groups.id",
				"multi_select"=>true,
				"class"=>Group::class,
				"data_key"=>"id"
			],
			[
				"name"=>"%%company%%",
				"slug"=>'company_name',
				"resource"=>"User.Company",
				"type"=>"text",
				"multi_select"=>true,
				"class"=>Company::class,
				"filter"=>"in",
				"filter_key"=>"companies.id",
				"select"=>"companies.name as company_name",
				"sort"=>"companies.name",
				"data_key"=>"id",
				"user_specific"=>true,
				"user_specific_key"=>"company_id",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.company_id"
			],
			[
				"name"=>"%%department%%",
				"slug"=>'department_name',
				"resource"=>"User.Department",
				"type"=>"text",
				"multi_select"=>true,
				"filter"=>"in",
				"class"=>Department::class,
				"filter_key"=>"departments.id",
				"select"=>"departments.name as department_name",
				"sort"=>"departments.name",
				"data_key"=>"id",
				"user_specific"=>true,
				"user_specific_key"=>"department_id",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.department_id"
			],
			[
				"name"=>"%%country%%",
				"slug"=>'country_name',
				"resource"=>"User.Country",
				"type"=>"text",
				"select"=>"countries.name as country_name",
				"sort"=>"countries.name",
				"filter"=>"like",
				"filter_key"=>["countries.name"],
				"user_specific"=>true,
				"user_specific_key"=>"country_id",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.country_id"
			],
			[
				"name"=>"%%city%%",
				"slug"=>'city_name',
				"resource"=>"User.City",
				"type"=>"text",
				"select"=>"cities.name as city_name",
				"sort"=>"cities.name",
				"filter_key"=>["cities.name"],
				"filter"=>"like",
				"user_specific"=>true,
				"user_specific_key"=>"city_id",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.city_id"
			],
			[
				"name"=>"User Status",
				"slug"=>'user_status',
				"resource"=>"User",
				"type"=>"status",
				"drop_down"=>[
					1=>"Enabled",
					0=>"Disabled"
				],
				"filter"=>"like",
				"filter_key"=>"users.status",
				"select"=>"users.status as user_status",
				"sort"=>"users.status",
				"user_specific"=>true,
				"user_specific_key"=>"status",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.status"
			],
			[
				"name"=>"Code",
				"slug"=>'code',
				"resource"=>"Module",
				"type"=>"text",
				"filter"=>"like",
				"filter_key"=>"learning_modules.code",
				"select"=>"learning_modules.code",
				"sort"=>"learning_modules.code"
			],
			[
				"name"=>"%%learning_resource%%",
				"slug"=>'module_name',
				"resource"=>"Module",
				"multi_select"=>true,
				"class"=>LearningModule::class,
				"type"=>"text",
				"filter"=>"in",
				"filter_key"=>"learning_modules.id",
				"data_key"=>"id",
				"select"=>"learning_modules.name as module_name",
				"sort"=>"learning_modules.name"
			],
			[
				"name"=>"Type",
				"slug"=>"type_name",
				"resource"=>"Module.Type",
				"multi_select"=>true,
				"class"=>LearningModuleType::class,
				"type"=>"text",
				"filter"=>"in",
				"filter_key"=>"learning_module_types.id",
				"data_key"=>"id",
				"select"=>"learning_module_types.name as type_name",
				"sort"=>"learning_module_types.name"
			],
			[
				"name"=>"Learning Category",
				"slug"=>'category_name',
				"resource"=>"Module.Category",
				"multi_select"=>true,
				"class"=>LearningModuleCategory::class,
				"type"=>"text",
				"filter"=>"in",
				"filter_key"=>"learning_module_categories.id",
				"select"=>"learning_module_categories.name as category_name",
				"sort"=>"learning_module_categories.name",
				"data_key"=>"id"
			],
			[
				"name"=>"%%group_department_code%%",
				"slug"=>"group_department_code_name",
				"resource"=>"Module.LearningModuleGroupDepartmentCode",
				"multi_select"=>true,
				"class"=>LearningModuleGroupDepartmentCode::class,
				"type"=>"text",
				"filter"=>"in",
				"filter_key"=>"learning_module_group_department_codes.id",
				"select"=>"learning_module_group_department_codes.name as group_department_code_name",
				"sort"=>"learning_module_group_department_codes.name",
				"data_key"=>"id"
			],
			[
				"name"=>"Date Completed",
				"slug"=>'learning_result_completed_at',
				"resource"=>"learning_resources",
				"group_by"=>"completed_at",
				"filter"=>"between",
				"type"=>"date-range",
				"select"=>"learning_results.completed_at as learning_result_completed_at",
				"sort"=>"learning_results.completed_at",
				"filter_key"=> "completed_at"
			],
			[
				"name"=>"Score",
				"slug"=>'learning_results_score',
				"resource"=>"learning_resources",
				"group_by"=>"score",
				"filter"=>"like",
				"type"=>"number",
				"select"=>"learning_results.score as learning_results_score",
				"sort"=>"learning_results.score",
				"filter_key"=>["learning_results.score"]
			],
			[
				"name"=>"Quiz Time",
				"slug"=>'learning_results_quiz_time',
				"resource"=>"learning_resources",
				"group_by"=>"quiz_time",
				"filter"=>"like",
				"type"=>"number",
				"select"=>"learning_results.quiz_time as learning_results_quiz_time",
				"sort"=>"learning_results.quiz_time",
				"filter_key"=>["learning_results.quiz_time"]
			],
			[
				"name"=>"Time Spent in SCORM Player",
				"slug"=>'learning_results_duration_scorm',
				"resource"=>"learning_resources",
				"group_by"=>"duration_scorm",
				"filter"=>"like",
				"type"=>"number",
				"select"=>"learning_results.duration_scorm as learning_results_duration_scorm",
				"sort"=>"learning_results.duration_scorm",
				"filter_key"=>["learning_results.duration_scorm"]
			],
			[
				"name"=>"Resource Status",
				"slug"=>'completion_status',
				"resource"=>"learning_resources",
				"group_by"=>"completion_status",
				"type"=>"text",
				"multi_select"=>true,
				"filter"=>"in",
				"filter_key"=>"completion_status",
				"select" => "completion_status",
				"data_key" => "slug"
			],
			[
				"name"=>"Audit Item",
				"slug"=>'learning_results_refreshed',
				"resource"=>"learning_resources",
				"type"=>"status",
				"drop_down"=>[
						"1"=>"Yes",
						"0"=>"No"
				],
				"filter"=>"like",
				"select"=>"learning_results.refreshed as learning_results_refreshed",
				"sort"=>"learning_results.refreshed",
				"filter_key"=>["learning_results.refreshed"]
			],
			[
				"name"=>"Last Access",
				"slug"=>"learning_module_last_access_created_at",
				"resource"=>"latestAccess",
				"type"=>"date-range",
				"filter"=>"between",
				"select"=>"learning_module_last_access.created_at as learning_module_last_access_created_at",
				"sort"=>"learning_module_last_access.created_at",
				"filter_key"=>"learning_module_last_access.created_at"
			],
			[
				"name" => "Sub "."%%Department%%",
				"slug" => "user.sub_departments.department.name",
				"select" => "departments.name as department_name",
				"resource" => "User.SubDepartments.Department", //Relationship
				"group_by" => "department_id",
				"multi_select" => true,
				"filter" => "in",
				"class" => Department::class,
				"conditional_clauses" => [[
					'clause' => 'where',
					'condition' => [
						['parent_id', '!=', null],
					],
				],],
				"filter_key" => "id",
				"type" => "array",
				"data_key" => "id",
			],
			[
				"name"=>"Expected Completion Date",
				"slug"=>'learning_result_expected_completed_at',
				"resource"=>"learning_resources",
				"group_by"=>"completion_date_custom",
				"filter"=>"between",
				"type"=>"date-range",
				"select"=>"learning_results.completion_date_custom as learning_result_expected_completed_at",
				"sort"=>"learning_results.completion_date_custom",
				"filter_key"=> "completion_date_custom"
			],
			[
				"name"=>"Print Certificate",
				"resource"=>"Module",
				"slug"=>"learning_modules_print_certificate",
				"type"=>"status",
				"drop_down"=>[
					1=>"Yes",
					0=>"No",
				],
				"filter"=>"like",
				"select"=>"learning_modules.print_certificate as learning_modules_print_certificate",
				"sort"=>"learning_modules.print_certificate",
				"filter_key"=>"learning_modules.print_certificate"
			],
			[
				"name"=>"%%target_catalogue%%",
				"slug"=>"module.target_catalogues.name",
				"resource"=>"Module.TargetCatalogues",
				"type"=>"array",
				"filter"=>"in",
				"filter_key"=>"target_catalogues.id",
				"data_key"=>"id",
				"multi_select"=>true,
				"class"=>TargetCatalogue::class
			],
			[
				"name"=>"%%delivery_provider_type%%",
				"slug"=>"provider_types_name",
				"select" => "learning_module_delivery_provider_types.name as provider_types_name",
				"resource"=>"Module.LearningModuleDeliveryProviderType",
				"type"=>"text",
				"multi_select"=>true,
				"filter"=>"in",
				"sort"=>"learning_module_delivery_provider_types.name",
				"class"=>LearningModuleDeliveryProviderType::class,
				"filter_key"=>"learning_module_delivery_provider_types.id",
				"data_key"=>"id",
			],
			[
				"name" => "Alternative ID",
				"slug" => "altusercode",
				"resource" => "User",
				"type" => "number",
				"filter" => "like",
				"select" => "users.altusercode as altusercode",
				"sort" => "users.altusercode",
				"filter_key" => ["users.altusercode"],

			],
			[
				"name" => "Skype ID",
				"slug" => "skype_id",
				"resource" => "User",
				"type" => "text",
				"filter" => "like",
				"select" => "users.skype_id as skype_id",
				"sort" => "users.skype_id",
				"filter_key" => ["users.skype_id"],

			],
			[
				"name" => "Zoom Personal Meeting URL",
				"slug" => "zoom_id",
				"resource" => "User",
				"type" => "text",
				"filter" => "like",
				"select" => "users.zoom_id as zoom_id",
				"sort" => "users.zoom_id",
				"filter_key" => ["users.zoom_id"],

			],
			[
				"name" => "Phone",
				"slug" => "phone",
				"resource" => "User",
				"type" => "number",
				"filter" => "like",
				"select" => "users.phone as phone",
				"sort" => "users.phone",
				"filter_key" => ["users.phone"],
			],
			[
				"name" => "Alternative Email",
				"slug" => "email2",
				"resource" => "User",
				"type" => "text",
				"filter" => "like",
				"select" => "users.email2 as email2",
				"sort" => "users.email2",
				"filter_key" => ["users.email2"],

			],
			[
				"name" => "National Insurance Number",
				"slug" => "NINumber",
				"resource" => "User",
				"type" => "number",
				"filter" => "like",
				"select" => "users.NINumber as NINumber",
				"sort" => "users.NINumber",
				"filter_key" => ["users.NINumber"],
			],
			[
				"name" => "ULN",
				"slug" => "ULN",
				"resource" => "User",
				"type" => "number",
				"filter" => "like",
				"select" => "users.ULN as ULN",
				"sort" => "users.ULN",
				"filter_key" => ["users.ULN"],
			],
			[
				"name" => "Description",
				"slug" => "description",
				"resource" => "User",
				"type" => "text",
				"filter" => "like",
				"select" => "users.description as description",
				"sort" => "users.description",
				"filter_key" => ["users.description"],
			],
			[
				"name" => "Working Hours",
				"slug" => "week_hours",
				"resource" => "User",
				"type" => "number",
				"filter" => "like",
				"select" => "users.week_hours as week_hours",
				"sort" => "users.week_hours",
				"filter_key" => ["users.week_hours"],
			],
			[
				"name" => "User Account Expiry Date",
				"slug" => "expiration_dt",
				"resource" => "User",
				"type" => "date-range",
				"filter" => "between",
				"select" => "users.expiration_dt as expiration_dt",
				"sort" => "users.expiration_dt",
				"filter_key" => "users.expiration_dt",
			],
			[
				"name" => "%%user_timings__registration_date%%",
				"slug" => "registration_dt",
				"resource" => "User",
				"type" => "date-range",
				"filter" => "between",
				"select" => "users.registration_dt as registration_dt",
				"sort" => "users.registration_dt",
				"filter_key" => "users.registration_dt",
			],
			[
				"name" => "User Exclude From Reports Status",
				"slug" => "exclude_from_reports",
				"resource" => "User",
				"type" => "boolean",
				"filter" => "like",
				"select" => "users.exclude_from_reports as exclude_from_reports",
				"sort" => "users.exclude_from_reports",
				"filter_key" => ["users.exclude_from_reports"],
			],
			[
				"name" => "User Exclude from Emails Status",
				"slug" => "exclude_from_emails",
				"resource" => "User",
				"type" => "boolean",
				"filter" => "like",
				"select" => "users.exclude_from_emails as exclude_from_emails",
				"sort" => "users.exclude_from_emails",
				"filter_key" => ["users.exclude_from_emails"],
			],
			[
				"name"=>"Discount Percentage Added For User",
				"slug"=>"discount_percentage",
				"resource"=>"User",
				"type"=>"number",
				"filter"=>"like",
				"select"=>"users.discount_percentage as discount_percentage",
				"sort"=>"users.discount_percentage",
				"filter_key"=>["users.discount_percentage"],
			],
			[
				"name"=>"User Account Type",
				"slug"=>"account_type",
				"resource"=>"User.AccountTypeVal",
				"type"=>"text",
				"filter"=>"like",
				"class"=>Picklist::class,
				"filter_key"=>"picklists.id",
				"select"=>"picklists.value as account_type",
				"data_key"=>"id",
				"sort"=>"picklists.value",
			],
			[
				"name" => "User Account Created Date",
				"slug" => "created_at",
				"resource" => "User",
				"type" => "date-range",
				"filter" => "between",
				"select" => "users.created_at as created_at",
				"sort" => "users.created_at",
				"filter_key" => "users.created_at",
			],
			[
				"name" => "User's Manager Name",
				"slug" => "emergency_name",
				"resource" => "User",
				"type" => "text",
				"filter" => "like",
				"select" => "users.emergency_name as emergency_name",
				"sort" => "users.emergency_name",
				"filter_key" => ["users.emergency_name"],
			],
			[
				"name" => "User's Manager Job Title",
				"slug" => "emergency_relationship",
				"resource" => "User",
				"type" => "text",
				"filter" => "like",
				"select" => "users.emergency_relationship as emergency_relationship",
				"sort" => "users.emergency_relationship",
				"filter_key" => ["users.emergency_relationship"],
			],
			[
				"name" => "User's Manager Email/Phone",
				"slug" => "emergency_contact_numbers",
				"resource" => "User",
				"type" => "text",
				"filter" => "like",
				"select" => "users.emergency_contact_numbers as emergency_contact_numbers",
				"sort" => "users.emergency_contact_numbers",
				"filter_key" => ["users.emergency_contact_numbers"],
	],
	/*ILR Fields*/

			/*Learner - Learner Information*/
			[
				"name"=>"Learner reference number",
				"slug"=>"LearnRefNumber",
				"resource"=>"User",
				"type"=>"number",
				"filter"=>"like",
				"select"=>"users.LearnRefNumber as LearnRefNumber",
				"sort"=>"users.LearnRefNumber",
				"filter_key"=>["users.LearnRefNumber"],
			],
			[
				"name"=>"Unique learner number",
				"slug"=>"ULN",
				"resource"=>"User",
				"type"=>"number",
				"filter"=>"like",
				"select"=>"users.ULN as ULN",
				"sort"=>"users.ULN",
				"filter_key"=>["users.ULN"],
			],
			[
				"name"=>"Postcode",
				"slug"=>"Postcode",
				"resource"=>"User",
				"type"=>"number",
				"filter"=>"like",
				"select"=>"users.Postcode as Postcode",
				"sort"=>"users.Postcode",
				"filter_key"=>["users.Postcode"],
			],
			[
				"name"=>"Date Of Birth",
				"slug"=>"DateOfBirth",
				"resource"=>"User",
				"type"=>"date-range",
				"filter"=>"between",
				"select"=>"users.DateOfBirth as DateOfBirth",
				"sort"=>"users.DateOfBirth",
				"filter_key"=>"users.DateOfBirth",
			],

			[
				"name"=>"Sex",
				"slug"=>"Sex",
				"resource"=>"User",
				"type"=>"text",
				"filter"=>"like",
				"select"=>"users.Sex as Sex",
				"sort"=>"users.Sex",
				"filter_key"=>["users.Sex"],
			],
			[
				"name"=>"Ethnicity",
				"slug"=>"Ethnicity",
				"resource"=>"User",
				"type"=>"text",
				"filter"=>"like",
				"select"=>"users.Ethnicity as Ethnicity",
				"sort"=>"users.Ethnicity",
				"filter_key"=>["users.Ethnicity"],
			],
			[
				"name"=>"User Destination and Progression",
				"slug"=>"user.progression_outcome_types.name",
				"resource"=>"User.progressionOutcomeTypes",
				"type"=>"array",
				"filter"=>"like",
				"filter_key"=>["name"],
			],
			[
				"name"=>"Learner's employment status",
				"slug"=>"user.employment_statuses.name",
				"resource"=>"User.employmentStatuses",
				"type"=>"array",
				"filter"=>"like",
				"filter_key"=>["name"],
			],
			[
				"name"=>"Employment status application date",
				"slug"=>"DateEmpStatApp",
				"select"=>"ilr_learner_employment_statuses.DateEmpStatApp as DateEmpStatApp",
				"resource"=>"User.employmentStatus",
				"type"=>"date-range",
				"sort"=>"ilr_learner_employment_statuses.DateEmpStatApp",
				"filter_key"=>"ilr_learner_employment_statuses.DateEmpStatApp",
				"filter"=>"between",
			],



			/*ILR Delivery Fields*/
			[
				"name" => "ILR Delivery Aim Types",
				"slug" => "aim_types", // Adjust if nested relationship is different
				"resource" => "User.aimTypes",   // Eloquent relationship path
                "type" => "text",               // Because it's a many-to-many or related array
                "select" => "ilr_learning_delivery_aim_types.name as aim_types",
                "sort" => "ilr_learning_delivery_aim_types.name",
				"filter" => "like",              // or 'in' depending on your filter UI
				"filter_key" => ["ilr_learning_delivery_aim_types.name"],        // the column used for filtering
			],
			[
				"name" => "ILR Delivery Aim Sequence Number",
				"slug" => "AimSeqNumber",
				"resource" => "User.ilrLearningDeliveries",
                "type" => "text",
                "select" => "ilr_learning_deliveries.AimSeqNumber as AimSeqNumber",
                "sort" => "ilr_learning_deliveries.AimSeqNumber",
				"filter" => "like", // or "in" if dropdowns needed
				"filter_key" => ["ilr_learning_deliveries.AimSeqNumber"],
			],
			[
				"name" => "ILR Delivery Learn Start Date",
				"slug" => "ilr_learning_deliveries_LearnStartDate",
				"resource" => "User.ilrLearningDeliveries",
                "type" => "date-range",
                "select" => "ilr_learning_deliveries.LearnStartDate as ilr_learning_deliveries_LearnStartDate",
                "sort" => "ilr_learning_deliveries.LearnStartDate",
                "filter" => "between", // Use "like" for exact match or "between" for date range
                "filter_key" => "ilr_learning_deliveries.LearnStartDate",
			],
			[
				"name" => "ILR Delivery Learn Planned End Date",
				"slug" => "ilr_learning_deliveries_LearnPlanEndDate",
				"resource" => "User.ilrLearningDeliveries",
                "type" => "date-range",
                "select" => "ilr_learning_deliveries.LearnPlanEndDate as ilr_learning_deliveries_LearnPlanEndDate",
                "sort" => "ilr_learning_deliveries.LearnPlanEndDate",
                "filter" => "between", // Use "like" for exact match or "between" for date range
                "filter_key" => "ilr_learning_deliveries.LearnPlanEndDate",
			],


			[
				"name" => "ILR Delivery Learn Aim Reference",
				"slug" => "ilr_learning_deliveries_LearnAimRef",
				"resource" => "User.ilrLearningDeliveries",
                "type" => "text",
                "select" => "ilr_learning_deliveries.LearnAimRef as ilr_learning_deliveries_LearnAimRef",
                "sort" => "ilr_learning_deliveries.LearnAimRef",
				"filter" => "like", // or "in" if filtering by dropdown
				"filter_key" => ["ilr_learning_deliveries_LearnAimRef"],
			],
			[
				"name" => "ILR Delivery Planned Learning Hours",
                "slug" => "ilr_learning_deliveries_PHours",
                "select" => "ilr_learning_deliveries.PHours as ilr_learning_deliveries_PHours",
                "sort" => "ilr_learning_deliveries.PHours",
				"resource" => "User.ilrLearningDeliveries",
				"type" => "text",
				"filter" => "like",
				"filter_key" => ["ilr_learning_deliveries.PHours"],
			],
			[
				"name" => "ILR Delivery EPA Organisation ID",
                "slug" => "ilr_learning_deliveries_EPAOrgID",
                "select" => "ilr_learning_deliveries.EPAOrgID as ilr_learning_deliveries_EPAOrgID",
                "resource" => "User.ilrLearningDeliveries",
                "sort" => "ilr_learning_deliveries.EPAOrgID",
				"type" => "text",
				"filter" => "like",
				"filter_key" => ["ilr_learning_deliveries.EPAOrgID"],
            ],
            [
                "name"=> "ILR  Funding model",
                "slug"=> "ilr_learning_delivery_funding_models_name",
                "resource"=> "User.funcdingModel",
                "type"=> "text",
                "select"=> "ilr_learning_delivery_funding_models.name as ilr_learning_delivery_funding_models_name",
                "sort"=> "ilr_learning_delivery_funding_models.name",
                "filter" => "like",
                "filter_key" => ["ilr_learning_delivery_funding_models.name"],
            ],
            [
                "name" => "ILR  Programme type",
                "slug"=>"ilr_learning_delivery_programme_types_name",
                "resource"=>"User.programmeType",
                "type"=>"text",
                "select"=>"ilr_learning_delivery_programme_types.name as ilr_learning_delivery_programme_types_name",
                "sort"=>"ilr_learning_delivery_programme_types.name",
                "filter"=>"like",
                "filter_key"=>["ilr_learning_delivery_programme_types.name"],
            ],
			[
				"name" => "ILR Completion Status",
				"slug" => "ilr_learning_delivery_completion_status_name",
				"resource" => "User.ilrCompletionStatuses",
				"type" => "text",
                "filter" => "like", // or "in" for dropdown support
                "select" => "ilr_learning_delivery_completion_status.name as ilr_learning_delivery_completion_status_name",
                "sort" => "ilr_learning_delivery_completion_status.name",
				"filter_key" => ["ilr_learning_delivery_completion_status.name"],
			],
			[
				"name" => "ILR Delivery Learning Actual End Date",
                "slug" => "ilr_learning_deliveries_LearnActEndDate",
                "select" => "ilr_learning_deliveries.LearnActEndDate as ilr_learning_deliveries_LearnActEndDate",
                "sort" => "ilr_learning_deliveries.LearnActEndDate",
				"resource" => "User.ilrLearningDeliveries",
				"type" => "date-range",
                "filter" => "between", // Use "like" for exact match or "between" for date range
				"filter_key" => "ilr_learning_deliveries.LearnActEndDate",
			],
			[
				"name" => "ILR Delivery Outcome",
				"slug" => "ilr__delivery_outcomes",
                "resource" => "User.ilroutcomes",
                "select" => "ilr_learning_delivery_outcomes.name as ilr__delivery_outcomes",
				"type" => "text",
				"filter" => "like", // or "in" for dropdown filtering
                "filter_key" => ["ilr_learning_delivery_outcomes.name"],
                "sort" => "ilr_learning_delivery_outcomes.name",
			],
			[
				"name" => "ILR Delivery Achievement Date",
                "slug" => "ilr_learning_deliveries_LearnActEndDate",
                "select" => "ilr_learning_deliveries.LearnActEndDate as ilr_learning_deliveries_LearnActEndDate",
                "sort" => "ilr_learning_deliveries.LearnActEndDate",
				"resource" => "User.ilrLearningDeliveries",
				"type" => "date-range",
				"filter" => "between", // Use "like" for exact match or "between" for date range
				"filter_key" => "ilr_learning_deliveries.LearnActEndDate",
			],
			[
				"name" => "ILR Delivery Withdraw Reason",
                "slug" => "ilr_learning_delivery_withdraw_reasons_name",
                "select" => "ilr_learning_delivery_withdraw_reasons.name as ilr_learning_delivery_withdraw_reasons_name",
				"resource" => "User.WithdrawReason",
				"type" => "text",
                "filter" => "like",
                "sort" => "ilr_learning_delivery_withdraw_reasons.name",
				"filter_key" => ["ilr_learning_delivery_withdraw_reasons.name"],
            ],
            [
                "name" => "Tailored Learning Outcome",
                "slug" => "ilr_learning_delivery_tailored_learning_outcomes_name",
                "resource" => "User.TailoredLearningOutcome",
                "type" => "text",
                "select" => "ilr_learning_delivery_tailored_learning_outcomes.name as ilr_learning_delivery_tailored_learning_outcomes_name",
                "sort" => "ilr_learning_delivery_tailored_learning_outcomes.name",
                "filter" => "like",
                "filter_key" => ["ilr_learning_delivery_tailored_learning_outcomes.name"],
            ],
			[
				"name" => "ILR Delivery AFin Type",
				"slug" => "ilr_learning_delivery_financial_record_types_name",
				"resource" => "User.ilrFinancialRecordTypes",
                "type" => "text",
                "select" => "ilr_learning_delivery_financial_record_types.name as ilr_learning_delivery_financial_record_types_name",
                "sort" => "ilr_learning_delivery_financial_record_types.name",
				"filter" => "like",
				"filter_key" => ["ilr_learning_delivery_financial_record_types.name"],
			],
			[
				"name" => "ILR Delivery AFin Code",
                "slug" => "ilr_learning_delivery_financial_record_codes_name",
                "select" => "ilr_learning_delivery_financial_record_codes.name as ilr_learning_delivery_financial_record_codes_name",
				"resource" => "User.ilrFinancialRecordCodes",
				"type" => "text",
				"filter" => "like",
                "filter_key" => ["ilr_learning_delivery_financial_record_codes.name"],
                "sort" => "ilr_learning_delivery_financial_record_codes.name",
            ],
            [
                "name" => "ILR Delivery AFin Amount",
                "slug" => "ilr_learning_delivery_financial_record_amounts",
                "select" => "ilr_learning_delivery_financial_records.AFinAmount as ilr_learning_delivery_financial_record_amounts",
                "resource" => "User.ilrFinancialRecords",
                "type" => "number",
                "filter" => "like",
                "filter_key" => ["ilr_learning_delivery_financial_records.AFinAmount"],
                "sort" => "ilr_learning_delivery_financial_records.AFinAmount",
            ],
            [
                "name" => "ILR Delivery AFin Date",
                "slug" => "ilr_learning_delivery_financial_record_dates",
                "select" => "ilr_learning_delivery_financial_records.AFinDate as ilr_learning_delivery_financial_record_dates",
                "resource" => "User.ilrFinancialRecords",
                "type" => "date-range",
                "filter" => "between",
                "filter_key" => "ilr_learning_delivery_financial_records.AFinDate",
                "sort" => "ilr_learning_delivery_financial_records.AFinDate",
            ],
		    [
				"name"=>"Agreement ID",
				"slug"=>"AgreeId",
				"select"=>"ilr_learner_employment_statuses.AgreeId as AgreeId",
				"resource"=>"User.employmentStatus",
				"type"=>"number",
				"sort"=>"ilr_learner_employment_statuses.AgreeId",
				"filter_key"=>"ilr_learner_employment_statuses.AgreeId",
				"filter"=>"like",
			],
			[
				"name"=>"Employer ID",
				"slug"=>"EmpId",
				"select"=>"ilr_learner_employment_statuses.EmpId as EmpId",
				"resource"=>"User.employmentStatus",
				"type"=>"number",
				"sort"=>"ilr_learner_employment_statuses.EmpId",
				"filter_key"=>"ilr_learner_employment_statuses.EmpId",
				"filter"=>"like",
			],
			[
			"name" => "Apprenticeship standard code",
			"slug" => "StdCode",
			"select"=> "ilr_learning_deliveries.StdCode as StdCode",
			"resource"=> "User.ilrLearningDeliveries",
			"type"=> "number",
			"sort"=> "ilr_learning_deliveries.StdCode",
			"filter_key"=> ["ilr_learning_deliveries.StdCode"],
			"filter" => "like"
			],
			[
			"name" => "Prior Attainment",
			"slug" => "PriorAttainment",
			"select"=> "ilr_user_prior_attainment_level.name as PriorAttainment",
			"resource" => "User.priorAttainment",
			"type"=> "text",
                "sort"=> "ilr_user_prior_attainment_level.name",
			"filter_key"=> ["ilr_user_prior_attainment_level.name"],
			"filter" => "like"
            ],
            [
                "name" => "LLDD and health problem",
                "slug"=> "lldd_health_problem",
                "select"=> "lldd_health_problems.name as lldd_health_problem",
                "resource" => "User.llddHealthProblems",
                "type"=> "text",
                "sort"=> "lldd_health_problems.name",
                "filter_key"=> ["lldd_health_problems.name"],
                "filter" => "like"
            ],
            [
                "name" => "LLDD and Health Problem Entity Definition",
                "slug"=> "lldd_health_problem_entity_definition",
                "select"=> "lldd_health_problems_categories.name as lldd_health_problem_entity_definition",
                "resource" => "User.llddHealthProblemEntityDefinition",
                "type"=> "text",
                "sort"=> "lldd_health_problems_categories.name",
                "filter_key"=> ["lldd_health_problems_categories.name"],
                "filter" => "like"
            ],
            [
                "name" => "Learner Destination and Progression - Outcome type",
                "slug"=> "learner_destination_progression_outcome_type",
                "select"=> "ilr_progression_outcome_types.name as learner_destination_progression_outcome_type",
                "resource" => "User.learnerDestinationProgressionOutcomeType",
                "type"=> "text",
                "sort"=> "ilr_progression_outcome_types.name",
                "filter_key"=> ["ilr_progression_outcome_types.name"],
                "filter" => "like"
            ],
            [
                "name" => "Learner Destination and Progression - Outcome code",
                "slug"=> "progression_outcome_code_name",
                "select"=>"ilr_progression_outcome_codes.name as progression_outcome_code_name",
                "resource"=>"User.progressionOutcomeCode",
                "type"=>"text",
                "sort"=>"ilr_progression_outcome_codes.name",
                "filter_key"=>["ilr_progression_outcome_codes.name"],
                "filter"=>"like"
            ],
            [
                "name" => "Learner Destination and Progression - Outcome start date",
                "slug" => "learner_destination_and_progression_outcome_start_date",
                "select" => "latest_progression.OutStartDate as learner_destination_and_progression_outcome_start_date",
                "resource" => "User.learnerDestinationAndProgressionOutcome",
                "type" => "date-range",
                "sort" => "latest_progression.OutStartDate",
                "filter_key" => "latest_progression.OutStartDate",
                "filter" => "between",
            ],
            [
                "name" => "Learner Destination and Progression - Outcome end date",
                "slug" => "learner_destination_and_progression_outcome_end_date",
                "select" => "latest_progression.OutEndDate as learner_destination_and_progression_outcome_end_date",
                "resource" => "User.learnerDestinationAndProgressionOutcome",
                "type" => "date-range",
                "sort" => "latest_progression.OutEndDate",
                "filter_key" => "latest_progression.OutEndDate",
                "filter" => "between",
            ],
            [
                "name" => "Learner Destination and Progression - Outcome collection date",
                "slug" =>  "learner_destination_and_progression_outcome_collection_date",
                "select" => "latest_progression.OutCollDate as learner_destination_and_progression_outcome_collection_date",
                "resource" => "User.learnerDestinationAndProgressionOutcome",
                "type" => "date-range",
                "sort" => "latest_progression.OutCollDate",
                "filter_key" => "latest_progression.OutCollDate",
                "filter" => "between",
            ],

		],

		'apprenticeship_standards'=>[
			[
				"name"=>"Employee ID",
				"slug"=>"user_id",
				"resource"=>"User",
				"type"=>"number",
				"filter"=>"like",
				"select"=>"users.id as user_id",
				"sort"=>"users.id",
				"filter_key"=>["users.id"],
				"user_specific"=>true,
				"user_specific_key"=>"id",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.id"
			],
			[
				"name"=>"Username",
				"slug"=>'username',
				"resource"=>"User",
				"type"=>"text",
				"filter"=>"like",
				"select"=>"users.username as username",
				"sort"=>"users.username",
				"filter_key"=>["users.username"],
				"user_specific"=>true,
				"user_specific_key"=>"username",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.username"
			],
			[
				"name"=>"First Name",
				"slug"=>'fname',
				"resource"=>"User",
				"type"=>"text",
				"filter"=>"like",
				"select"=>"users.fname",
				"sort"=>"users.fname",
				"filter_key"=>["users.fname"],
				"user_specific"=>true,
				"user_specific_key"=>"fname",
				"user_specific_column"=>"users.fname",
				"user_specific_model"=>"User"
			],
			[
				"name"=>"Last Name",
				"slug"=>'lname',
				"resource"=>"User",
				"type"=>"text",
				"filter"=>"like",
				"select"=>"users.lname",
				"sort"=>"users.lname",
				"filter_key"=>["users.lname"],
				"user_specific"=>true,
				"user_specific_key"=>"lname",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.lname"
			],
			[
				"name"=>"Role",
				"slug"=>'role_name',
				"resource"=>"User.Role",
				"type"=>"text",
				"filter"=>"in",
				"select"=>"roles.name as role_name",
				"sort"=>"roles.name",
				"multi_select"=>true,
				"class"=>Role::class,
				"filter_key"=>"roles.id",
				"data_key"=>"id",
				"user_specific"=>true,
				"user_specific_key"=>"role_id",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.role_id"
			],
			[
				"name"=>"Email",
				"slug"=>'user_email',
				"resource"=>"User",
				"type"=>"text",
				"filter"=>"like",
				"select"=>"users.email as user_email",
				"sort"=>"users.email",
				"filter_key"=>["users.email"],
				"user_specific"=>true,
				"user_specific_key"=>"email",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.email"
			],
			[
				"name"=>"Job Title",
				"slug"=>'designation_name',
				"resource"=>"User.Designation",
				"type"=>"text",
				"select"=>"designations.name as designation_name",
				"sort"=>"designations.name",
				"multi_select"=>true,
				"class"=>Designation::class,
				"filter_key"=>"designations.id",
				"data_key"=>"id",
				"filter"=>"in",
				"user_specific"=>true,
				"user_specific_key"=>"designation_id",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.designation_id"
			],
			[
				"name"=>"%%location%%",
				"slug"=>'location_name',
				"resource"=>"User.Location",
				"type"=>"text",
				"multi_select"=>true,
				"class"=>Location::class,
				"filter_key"=>"locations.id",
				"data_key"=>"id",
				"filter"=>"in",
				"select"=>"locations.name as location_name",
				"sort"=>"locations.name",
				"user_specific"=>true,
				"user_specific_key"=>"location_id",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.location_id"
			],
			[
				"name"=>"%%group%%",
				"slug"=>"user.groups.name",
				"resource"=>"User.Groups",
				"type"=>"array",
				"filter"=>"in",
				"filter_key"=>"groups.id",
				"data_key"=>"id",
				"multi_select"=>true,
				"class"=>Group::class
			],
			[
				"name"=>"%%company%%",
				"slug"=>'company_name',
				"resource"=>"User.Company",
				"type"=>"text",
					"multi_select"=>true,
				"class"=>Company::class,
				"filter"=>"in",
				"filter_key"=>"companies.id",
				"data_key"=>"id",
				"select"=>"companies.name as company_name",
				"sort"=>"companies.name",
				"user_specific"=>true,
				"user_specific_key"=>"company_id",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.company_id"
			],
			[
				"name"=>"%%department%%",
				"slug"=>'department_name',
				"resource"=>"User.Department",
				"type"=>"text",
				"multi_select"=>true,
				"filter"=>"in",
				"class"=>Department::class,
				"filter_key"=>"departments.id",
				"data_key"=>"id",
				"select"=>"departments.name as department_name",
				"sort"=>"departments.name",
				"user_specific"=>true,
				"user_specific_column"=>"users.department_id",
				"user_specific_key"=>"department_id",
				"user_specific_model"=>"User"
			],
			[
				"name"=>"%%country%%",
				"slug"=>'country_name',
				"resource"=>"User.Country",
				"type"=>"text",
				"select"=>"countries.name as country_name",
				"sort"=>"countries.name",
				"filter"=>"like",
				"filter_key"=>["countries.name"],
				"user_specific">true,
				"user_specific_key"=>"country_id",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.country_id"
			],
			[
				"name"=>"%%city%%",
				"slug"=>'city_name',
				"resource"=>"User.City",
				"type"=>"text",
				"select"=>"cities.name as city_name",
				"sort"=>"cities.name",
				"filter"=>"like",
				"filter_key"=>["cities.name"],
				"user_specific"=>true,
				"user_specific_model"=>"User",
				"user_specific_key"=>"city_id",
				"user_specific_column"=>"users.city_id"
			],
			[
				"name"=>"User Status",
				"slug"=>'user_status',
				"resource"=>"User",
				"type"=>"status",
				"drop_down"=>[
					"1"=>"Enabled",
					"0"=>"Disabled"
				],
				"filter"=>"like",
				"select"=>"users.status as user_status",
				"sort"=>"users.status",
				"filter_key"=>"users.status"
			],
			[
				"name"=>"User Managers",
				"slug"=>"user.managers.fname+lname",
				"resource"=>"User.Managers",
				"type"=>"array",
				"multi_select"=>false,
				"filter"=>"like",
				"filter_key"=>["fname","lname"],
				"user_specific"=>true,
				"user_specific_key"=>"id",
				"user_specific_model"=>"Managers",
				"user_specific_column"=>"manager_users.manager_id"
			],
			[
				"name"=>"%%programme%%",
				"resource"=>"Standard",
				"slug"=>"standard_name",
				"type"=>"text",
				"filter"=>"like",
				"select"=>"apprenticeship_standards.name as standard_name",
				"sort"=>"apprenticeship_standards.name",
				"filter_key"=>["apprenticeship_standards.name"]
			],
			[
				"name"=>"Date Completed",
				"resource"=>"apprenticeship_standards",
				"slug"=>"standard_comaplted_at",
				"type"=>"date-range",
				"select"=>"apprenticeship_standards_users.completed_at as standard_comaplted_at",
				"sort"=>"apprenticeship_standards_users.completed_at",
				"filter"=>"between",
				"filter_key"=>"apprenticeship_standards_users.completed_at"
			],
			[
				"name"=>"%%programme%% Status",
				"resource"=>"apprenticeship_standards",
				"slug"=>"standard_completion_status",
				"type"=>"text",
				"drop_down"=>[
					"completed"=>"Completed",
					"in progress"=>"In Progress",
					"not attempted"=>"Not Attempted"
				],
				"filter"=>"like",
				"select"=>"apprenticeship_standards_users.completion_status as standard_completion_status",
				"sort"=>"apprenticeship_standards_users.completion_status",
				"filter_key"=>"apprenticeship_standards_users.completion_status"
			],
			[
				"name"=>"Time spent",
				"resource"=>"apprenticeship_standards",
				"slug"=>"time_spent",
				"type"=>"number",
				"filter"=>"like",
				"select"=>"apprenticeship_standards_users.time_spent",
				"sort"=>"apprenticeship_standards_users.time_spent",
				"filter_key"=>["apprenticeship_standards_users.time_spent"]
			],
			[
				"name"=>"Time behind",
				"resource"=>"apprenticeship_standards",
				"slug"=>"time_behind",
				"type"=>"number",
				"filter"=>"like",
				"select"=>"apprenticeship_standards_users.time_behind",
				"sort"=>"apprenticeship_standards_users.time_behind",
				"filter_key"=>["apprenticeship_standards_users.time_behind"]
			],
			[
				"name"=>"% Resource Completion",
				"resource"=>"apprenticeship_standards",
				"slug"=>"percentage",
				"type"=>"number",
				"filter"=>"like",
				"select"=>"apprenticeship_standards_users.percentage",
				"sort"=>"apprenticeship_standards_users.percentage",
				"filter_key"=>["apprenticeship_standards_users.percentage"]
			],
			[
				"name"=>"% Duration Completion",
				"resource"=>"apprenticeship_standards",
				"slug"=>"percentage_time",
				"type"=>"number",
				"filter"=>"like",
				"select"=>"apprenticeship_standards_users.percentage_time",
				"sort"=>"apprenticeship_standards_users.percentage_time",
				"filter_key"=>["apprenticeship_standards_users.percentage_time"]
			],
			[
				"name"=>"On Target - Time completed",
				"resource"=>"apprenticeship_standards",
				"slug"=>"expected_time",
				"type"=>"number",
				"filter"=>"like",
				"select"=>"apprenticeship_standards_users.expected_time",
				"sort"=>"apprenticeship_standards_users.expected_time",
				"filter_key"=>["apprenticeship_standards_users.expected_time"]
			],
			// [
			//   "name"=>"Managers",
			//   "slug"=>"schedule.managers.name",
			//   "resource"=>"Schedule.Managers",
			//   "type"=>"array",
			//   "multi_select"=>false,
			//   "filter"=>"like",
			//   "filter_key"=>["fname","lname"]
			// ],
			[
				"name" => "Sub "."%%Department%%",
				"slug" => "user.sub_departments.department.name",
				"select" => "departments.name as department_name",
				"resource" => "User.SubDepartments.Department", //Relationship
				"group_by" => "department_id",
				"multi_select" => true,
				"filter" => "in",
				"class" => Department::class,
				"conditional_clauses" => [[
					'clause' => 'where',
					'condition' => [
							['parent_id', '!=', null],
					],
				],],
				"filter_key" => "id",
				"type" => "array",
				"data_key" => "id",
			],
			[
				"name"=>"Planned End Date",
				"slug"=>'planned_end_date',
				"resource"=>"apprenticeship_standards",
				"group_by"=>"due_at",
				"filter"=>"between",
				"type"=>"date-range",
				"select"=>"apprenticeship_standards_users.due_at as planned_end_date",
				"sort"=>"apprenticeship_standards_users.due_at",
				"filter_key"=> "due_at"
			],
			[
				"name" => "% Criteria Completion",
				"resource" => "apprenticeship_standards",
				"slug" => "criteria_completion",
				"type" => "number",
				"filter" => "like",
				"select" => "apprenticeship_standards_users.criteria_completion",
				"sort" => "apprenticeship_standards_users.criteria_completion",
				"filter_key" => ["apprenticeship_standards_users.criteria_completion"]
			],
			[
				"name"=>"Print Certificate",
				"resource"=>"Standard",
				"slug"=>"apprenticeship_standards_print_certificate",
				"type"=>"status",
				"drop_down"=>[
						1=>"Yes",
						0=>"No",
				],
				"filter"=>"like",
				"select"=>"apprenticeship_standards.print_certificate as apprenticeship_standards_print_certificate",
				"sort"=>"apprenticeship_standards.print_certificate",
				"filter_key"=>"apprenticeship_standards.print_certificate"
			],
			 /*More user Fields*/
			 [
				"name" => "Alternative ID",
				"slug" => "altusercode",
				"resource" => "User",
				"type" => "number",
				"filter" => "like",
				"select" => "users.altusercode as altusercode",
				"sort" => "users.altusercode",
				"filter_key" => ["users.altusercode"],

			],
			[
				"name" => "Skype ID",
				"slug" => "skype_id",
				"resource" => "User",
				"type" => "text",
				"filter" => "like",
				"select" => "users.skype_id as skype_id",
				"sort" => "users.skype_id",
				"filter_key" => ["users.skype_id"],

			],
			[
				"name" => "Zoom Personal Meeting URL",
				"slug" => "zoom_id",
				"resource" => "User",
				"type" => "text",
				"filter" => "like",
				"select" => "users.zoom_id as zoom_id",
				"sort" => "users.zoom_id",
				"filter_key" => ["users.zoom_id"],

			],
			[
				"name" => "Phone",
				"slug" => "phone",
				"resource" => "User",
				"type" => "number",
				"filter" => "like",
				"select" => "users.phone as phone",
				"sort" => "users.phone",
				"filter_key" => ["users.phone"],
			],
			[
				"name" => "Alternative Email",
				"slug" => "email2",
				"resource" => "User",
				"type" => "text",
				"filter" => "like",
				"select" => "users.email2 as email2",
				"sort" => "users.email2",
				"filter_key" => ["users.email2"],

			],
			[
				"name" => "National Insurance Number",
				"slug" => "NINumber",
				"resource" => "User",
				"type" => "number",
				"filter" => "like",
				"select" => "users.NINumber as NINumber",
				"sort" => "users.NINumber",
				"filter_key" => ["users.NINumber"],
			],
			[
				"name" => "ULN",
				"slug" => "ULN",
				"resource" => "User",
				"type" => "number",
				"filter" => "like",
				"select" => "users.ULN as ULN",
				"sort" => "users.ULN",
				"filter_key" => ["users.ULN"],
			],
			[
				"name" => "Description",
				"slug" => "description",
				"resource" => "User",
				"type" => "text",
				"filter" => "like",
				"select" => "users.description as description",
				"sort" => "users.description",
				"filter_key" => ["users.description"],
			],
			[
				"name" => "Working Hours",
				"slug" => "week_hours",
				"resource" => "User",
				"type" => "number",
				"filter" => "like",
				"select" => "users.week_hours as week_hours",
				"sort" => "users.week_hours",
				"filter_key" => ["users.week_hours"],
			],
			[
				"name" => "User Account Expiry Date",
				"slug" => "expiration_dt",
				"resource" => "User",
				"type" => "date-range",
				"filter" => "between",
				"select" => "users.expiration_dt as expiration_dt",
				"sort" => "users.expiration_dt",
				"filter_key" => "users.expiration_dt",
			],
			[
				"name" => "User Account Registration Date",
				"slug" => "registration_dt",
				"resource" => "User",
				"type" => "date-range",
				"filter" => "between",
				"select" => "users.registration_dt as registration_dt",
				"sort" => "users.registration_dt",
				"filter_key" => "users.registration_dt",
			],
			[
				"name" => "User Exclude From Reports Status",
				"slug" => "exclude_from_reports",
				"resource" => "User",
				"type" => "boolean",
				"filter" => "like",
				"select" => "users.exclude_from_reports as exclude_from_reports",
				"sort" => "users.exclude_from_reports",
				"filter_key" => ["users.exclude_from_reports"],
			],
			[
				"name" => "User Exclude from Emails Status",
				"slug" => "exclude_from_emails",
				"resource" => "User",
				"type" => "boolean",
				"filter" => "like",
				"select" => "users.exclude_from_emails as exclude_from_emails",
				"sort" => "users.exclude_from_emails",
				"filter_key" => ["users.exclude_from_emails"],
			],
			[
				"name"=>"Discount Percentage Added For User",
				"slug"=>"discount_percentage",
				"resource"=>"User",
				"type"=>"number",
				"filter"=>"like",
				"select"=>"users.discount_percentage as discount_percentage",
				"sort"=>"users.discount_percentage",
				"filter_key"=>["users.discount_percentage"],
			],
			[
				"name"=>"User Account Type",
				"slug"=>"account_type",
				"resource"=>"User.AccountTypeVal",
				"type"=>"text",
				"filter"=>"like",
				"class"=>Picklist::class,
				"filter_key"=>"picklists.id",
				"select"=>"picklists.value as account_type",
				"data_key"=>"id",
				"sort"=>"picklists.value",
			],
			[
				"name" => "User Account Created Date",
				"slug" => "created_at",
				"resource" => "User",
				"type" => "date-range",
				"filter" => "between",
				"select" => "users.created_at as created_at",
				"sort" => "users.created_at",
				"filter_key" => "users.created_at",
			],
			[
				"name" => "User's Manager Name",
				"slug" => "emergency_name",
				"resource" => "User",
				"type" => "text",
				"filter" => "like",
				"select" => "users.emergency_name as emergency_name",
				"sort" => "users.emergency_name",
				"filter_key" => ["users.emergency_name"],
			],
			[
				"name" => "User's Manager Job Title",
				"slug" => "emergency_relationship",
				"resource" => "User",
				"type" => "text",
				"filter" => "like",
				"select" => "users.emergency_relationship as emergency_relationship",
				"sort" => "users.emergency_relationship",
				"filter_key" => ["users.emergency_relationship"],
			],
			[
				"name" => "User's Manager Email/Phone",
				"slug" => "emergency_contact_numbers",
				"resource" => "User",
				"type" => "text",
				"filter" => "like",
				"select" => "users.emergency_contact_numbers as emergency_contact_numbers",
				"sort" => "users.emergency_contact_numbers",
				"filter_key" => ["users.emergency_contact_numbers"],
			],

			/*ILR Fields*/

			/*Learner - Learner Information*/
			[
				"name"=>"Learner reference number",
				"slug"=>"LearnRefNumber",
				"resource"=>"User",
				"type"=>"number",
				"filter"=>"like",
				"select"=>"users.LearnRefNumber as LearnRefNumber",
				"sort"=>"users.LearnRefNumber",
				"filter_key"=>["users.LearnRefNumber"],
			],
			[
				"name"=>"Unique learner number",
				"slug"=>"ULN",
				"resource"=>"User",
				"type"=>"number",
				"filter"=>"like",
				"select"=>"users.ULN as ULN",
				"sort"=>"users.ULN",
				"filter_key"=>["users.ULN"],
			],
			[
				"name"=>"Postcode",
				"slug"=>"Postcode",
				"resource"=>"User",
				"type"=>"number",
				"filter"=>"like",
				"select"=>"users.Postcode as Postcode",
				"sort"=>"users.Postcode",
				"filter_key"=>["users.Postcode"],
			],
			[
				"name"=>"Date Of Birth",
				"slug"=>"DateOfBirth",
				"resource"=>"User",
				"type"=>"date-range",
				"filter"=>"between",
				"select"=>"users.DateOfBirth as DateOfBirth",
				"sort"=>"users.DateOfBirth",
				"filter_key"=>"users.DateOfBirth",
			],

			[
				"name"=>"Sex",
				"slug"=>"Sex",
				"resource"=>"User",
				"type"=>"text",
				"filter"=>"like",
				"select"=>"users.Sex as Sex",
				"sort"=>"users.Sex",
				"filter_key"=>["users.Sex"],
			],
			[
				"name"=>"Ethnicity",
				"slug"=>"Ethnicity",
				"resource"=>"User",
				"type"=>"text",
				"filter"=>"like",
				"select"=>"users.Ethnicity as Ethnicity",
				"sort"=>"users.Ethnicity",
				"filter_key"=>["users.Ethnicity"],
			],
			[
				"name"=>"User Destination and Progression",
				"slug"=>"user.progression_outcome_types.name",
				"resource"=>"User.progressionOutcomeTypes",
				"type"=>"array",
				"filter"=>"like",
				"filter_key"=>["name"],
			],
			[
				"name"=>"Learner's employment status",
				"slug"=>"user.employment_statuses.name",
				"resource"=>"User.employmentStatuses",
				"type"=>"array",
				"filter"=>"like",
				"filter_key"=>["name"],
			],
			[
				"name"=>"Employment status application date",
				"slug"=>"DateEmpStatApp",
				"select"=>"ilr_learner_employment_statuses.DateEmpStatApp as DateEmpStatApp",
				"resource"=>"User.employmentStatus",
				"type"=>"date-range",
				"sort"=>"ilr_learner_employment_statuses.DateEmpStatApp",
				"filter_key"=>"ilr_learner_employment_statuses.DateEmpStatApp",
				"filter"=>"between",
			],



			/*ILR Delivery Fields*/
			[
				"name" => "ILR Delivery Aim Types",
				"slug" => "aim_types", // Adjust if nested relationship is different
				"resource" => "User.aimTypes",   // Eloquent relationship path
                "type" => "text",               // Because it's a many-to-many or related array
                "select" => "ilr_learning_delivery_aim_types.name as aim_types",
                "sort" => "ilr_learning_delivery_aim_types.name",
				"filter" => "like",              // or 'in' depending on your filter UI
				"filter_key" => ["ilr_learning_delivery_aim_types.name"],        // the column used for filtering
			],
			[
				"name" => "ILR Delivery Aim Sequence Number",
				"slug" => "AimSeqNumber",
				"resource" => "User.ilrLearningDeliveries",
                "type" => "text",
                "select" => "ilr_learning_deliveries.AimSeqNumber as AimSeqNumber",
                "sort" => "ilr_learning_deliveries.AimSeqNumber",
				"filter" => "like", // or "in" if dropdowns needed
				"filter_key" => ["ilr_learning_deliveries.AimSeqNumber"],
			],
			[
				"name" => "ILR Delivery Learn Start Date",
				"slug" => "ilr_learning_deliveries_LearnStartDate",
				"resource" => "User.ilrLearningDeliveries",
                "type" => "date-range",
                "select" => "ilr_learning_deliveries.LearnStartDate as ilr_learning_deliveries_LearnStartDate",
                "sort" => "ilr_learning_deliveries.LearnStartDate",
                "filter" => "between", // Use "like" for exact match or "between" for date range
                "filter_key" => "ilr_learning_deliveries.LearnStartDate",
			],
			[
				"name" => "ILR Delivery Learn Planned End Date",
				"slug" => "ilr_learning_deliveries_LearnPlanEndDate",
				"resource" => "User.ilrLearningDeliveries",
                "type" => "date-range",
                "select" => "ilr_learning_deliveries.LearnPlanEndDate as ilr_learning_deliveries_LearnPlanEndDate",
                "sort" => "ilr_learning_deliveries.LearnPlanEndDate",
                "filter" => "between", // Use "like" for exact match or "between" for date range
                "filter_key" => "ilr_learning_deliveries.LearnPlanEndDate",
			],


			[
				"name" => "ILR Delivery Learn Aim Reference",
				"slug" => "ilr_learning_deliveries_LearnAimRef",
				"resource" => "User.ilrLearningDeliveries",
                "type" => "text",
                "select" => "ilr_learning_deliveries.LearnAimRef as ilr_learning_deliveries_LearnAimRef",
                "sort" => "ilr_learning_deliveries.LearnAimRef",
				"filter" => "like", // or "in" if filtering by dropdown
				"filter_key" => ["ilr_learning_deliveries_LearnAimRef"],
			],
			[
				"name" => "ILR Delivery Planned Learning Hours",
                "slug" => "ilr_learning_deliveries_PHours",
                "select" => "ilr_learning_deliveries.PHours as ilr_learning_deliveries_PHours",
                "sort" => "ilr_learning_deliveries.PHours",
				"resource" => "User.ilrLearningDeliveries",
				"type" => "text",
				"filter" => "like",
				"filter_key" => ["ilr_learning_deliveries.PHours"],
			],
			[
				"name" => "ILR Delivery EPA Organisation ID",
                "slug" => "ilr_learning_deliveries_EPAOrgID",
                "select" => "ilr_learning_deliveries.EPAOrgID as ilr_learning_deliveries_EPAOrgID",
                "resource" => "User.ilrLearningDeliveries",
                "sort" => "ilr_learning_deliveries.EPAOrgID",
				"type" => "text",
				"filter" => "like",
				"filter_key" => ["ilr_learning_deliveries.EPAOrgID"],
            ],
            [
                "name"=> "ILR  Funding model",
                "slug"=> "ilr_learning_delivery_funding_models_name",
                "resource"=> "User.funcdingModel",
                "type"=> "text",
                "select"=> "ilr_learning_delivery_funding_models.name as ilr_learning_delivery_funding_models_name",
                "sort"=> "ilr_learning_delivery_funding_models.name",
                "filter" => "like",
                "filter_key" => ["ilr_learning_delivery_funding_models.name"],
            ],
            [
                "name" => "ILR  Programme type",
                "slug"=>"ilr_learning_delivery_programme_types_name",
                "resource"=>"User.programmeType",
                "type"=>"text",
                "select"=>"ilr_learning_delivery_programme_types.name as ilr_learning_delivery_programme_types_name",
                "sort"=>"ilr_learning_delivery_programme_types.name",
                "filter"=>"like",
                "filter_key"=>["ilr_learning_delivery_programme_types.name"],
            ],
			[
				"name" => "ILR Completion Status",
				"slug" => "ilr_learning_delivery_completion_status_name",
				"resource" => "User.ilrCompletionStatuses",
				"type" => "text",
                "filter" => "like", // or "in" for dropdown support
                "select" => "ilr_learning_delivery_completion_status.name as ilr_learning_delivery_completion_status_name",
                "sort" => "ilr_learning_delivery_completion_status.name",
				"filter_key" => ["ilr_learning_delivery_completion_status.name"],
			],
			[
				"name" => "ILR Delivery Learning Actual End Date",
                "slug" => "ilr_learning_deliveries_LearnActEndDate",
                "select" => "ilr_learning_deliveries.LearnActEndDate as ilr_learning_deliveries_LearnActEndDate",
                "sort" => "ilr_learning_deliveries.LearnActEndDate",
				"resource" => "User.ilrLearningDeliveries",
				"type" => "date-range",
                "filter" => "between", // Use "like" for exact match or "between" for date range
				"filter_key" => "ilr_learning_deliveries.LearnActEndDate",
			],
			[
				"name" => "ILR Delivery Outcome",
				"slug" => "ilr__delivery_outcomes",
                "resource" => "User.ilroutcomes",
                "select" => "ilr_learning_delivery_outcomes.name as ilr__delivery_outcomes",
				"type" => "text",
				"filter" => "like", // or "in" for dropdown filtering
                "filter_key" => ["ilr_learning_delivery_outcomes.name"],
                "sort" => "ilr_learning_delivery_outcomes.name",
			],
			[
				"name" => "ILR Delivery Achievement Date",
                "slug" => "ilr_learning_deliveries_LearnActEndDate",
                "select" => "ilr_learning_deliveries.LearnActEndDate as ilr_learning_deliveries_LearnActEndDate",
                "sort" => "ilr_learning_deliveries.LearnActEndDate",
				"resource" => "User.ilrLearningDeliveries",
				"type" => "date-range",
				"filter" => "between", // Use "like" for exact match or "between" for date range
				"filter_key" => "ilr_learning_deliveries.LearnActEndDate",
			],
			[
				"name" => "ILR Delivery Withdraw Reason",
                "slug" => "ilr_learning_delivery_withdraw_reasons_name",
                "select" => "ilr_learning_delivery_withdraw_reasons.name as ilr_learning_delivery_withdraw_reasons_name",
				"resource" => "User.WithdrawReason",
				"type" => "text",
                "filter" => "like",
                "sort" => "ilr_learning_delivery_withdraw_reasons.name",
				"filter_key" => ["ilr_learning_delivery_withdraw_reasons.name"],
            ],
            [
                "name" => "Tailored Learning Outcome",
                "slug" => "ilr_learning_delivery_tailored_learning_outcomes_name",
                "resource" => "User.TailoredLearningOutcome",
                "type" => "text",
                "select" => "ilr_learning_delivery_tailored_learning_outcomes.name as ilr_learning_delivery_tailored_learning_outcomes_name",
                "sort" => "ilr_learning_delivery_tailored_learning_outcomes.name",
                "filter" => "like",
                "filter_key" => ["ilr_learning_delivery_tailored_learning_outcomes.name"],
            ],
			[
				"name" => "ILR Delivery AFin Type",
				"slug" => "ilr_learning_delivery_financial_record_types_name",
				"resource" => "User.ilrFinancialRecordTypes",
                "type" => "text",
                "select" => "ilr_learning_delivery_financial_record_types.name as ilr_learning_delivery_financial_record_types_name",
                "sort" => "ilr_learning_delivery_financial_record_types.name",
				"filter" => "like",
				"filter_key" => ["ilr_learning_delivery_financial_record_types.name"],
			],
			[
				"name" => "ILR Delivery AFin Code",
                "slug" => "ilr_learning_delivery_financial_record_codes_name",
                "select" => "ilr_learning_delivery_financial_record_codes.name as ilr_learning_delivery_financial_record_codes_name",
				"resource" => "User.ilrFinancialRecordCodes",
				"type" => "text",
				"filter" => "like",
                "filter_key" => ["ilr_learning_delivery_financial_record_codes.name"],
                "sort" => "ilr_learning_delivery_financial_record_codes.name",
            ],
            [
                "name" => "ILR Delivery AFin Amount",
                "slug" => "ilr_learning_delivery_financial_record_amounts",
                "select" => "ilr_learning_delivery_financial_records.AFinAmount as ilr_learning_delivery_financial_record_amounts",
                "resource" => "User.ilrFinancialRecords",
                "type" => "number",
                "filter" => "like",
                "filter_key" => ["ilr_learning_delivery_financial_records.AFinAmount"],
                "sort" => "ilr_learning_delivery_financial_records.AFinAmount",
            ],
            [
                "name" => "ILR Delivery AFin Date",
                "slug" => "ilr_learning_delivery_financial_record_dates",
                "select" => "ilr_learning_delivery_financial_records.AFinDate as ilr_learning_delivery_financial_record_dates",
                "resource" => "User.ilrFinancialRecords",
                "type" => "date-range",
                "filter" => "between",
                "filter_key" => "ilr_learning_delivery_financial_records.AFinDate",
                "sort" => "ilr_learning_delivery_financial_records.AFinDate",
            ],
		    [
				"name"=>"Agreement ID",
				"slug"=>"AgreeId",
				"select"=>"ilr_learner_employment_statuses.AgreeId as AgreeId",
				"resource"=>"User.employmentStatus",
				"type"=>"number",
				"sort"=>"ilr_learner_employment_statuses.AgreeId",
				"filter_key"=>"ilr_learner_employment_statuses.AgreeId",
				"filter"=>"like",
			],
			[
				"name"=>"Employer ID",
				"slug"=>"EmpId",
				"select"=>"ilr_learner_employment_statuses.EmpId as EmpId",
				"resource"=>"User.employmentStatus",
				"type"=>"number",
				"sort"=>"ilr_learner_employment_statuses.EmpId",
				"filter_key"=>"ilr_learner_employment_statuses.EmpId",
				"filter"=>"like",
			],
			[
			"name" => "Apprenticeship standard code",
			"slug" => "StdCode",
			"select"=> "ilr_learning_deliveries.StdCode as StdCode",
			"resource"=> "User.ilrLearningDeliveries",
			"type"=> "number",
			"sort"=> "ilr_learning_deliveries.StdCode",
			"filter_key"=> ["ilr_learning_deliveries.StdCode"],
			"filter" => "like"
			],
			[
			"name" => "Prior Attainment",
			"slug" => "PriorAttainment",
			"select"=> "ilr_user_prior_attainment_level.name as PriorAttainment",
			"resource" => "User.priorAttainment",
			"type"=> "text",
                "sort"=> "ilr_user_prior_attainment_level.name",
			"filter_key"=> ["ilr_user_prior_attainment_level.name"],
			"filter" => "like"
            ],
            [
                "name" => "LLDD and health problem",
                "slug"=> "lldd_health_problem",
                "select"=> "lldd_health_problems.name as lldd_health_problem",
                "resource" => "User.llddHealthProblems",
                "type"=> "text",
                "sort"=> "lldd_health_problems.name",
                "filter_key"=> ["lldd_health_problems.name"],
                "filter" => "like"
            ],
            [
                "name" => "LLDD and Health Problem Entity Definition",
                "slug"=> "lldd_health_problem_entity_definition",
                "select"=> "lldd_health_problems_categories.name as lldd_health_problem_entity_definition",
                "resource" => "User.llddHealthProblemEntityDefinition",
                "type"=> "text",
                "sort"=> "lldd_health_problems_categories.name",
                "filter_key"=> ["lldd_health_problems_categories.name"],
                "filter" => "like"
            ],
            [
                "name" => "Learner Destination and Progression - Outcome type",
                "slug"=> "learner_destination_progression_outcome_type",
                "select"=> "ilr_progression_outcome_types.name as learner_destination_progression_outcome_type",
                "resource" => "User.learnerDestinationProgressionOutcomeType",
                "type"=> "text",
                "sort"=> "ilr_progression_outcome_types.name",
                "filter_key"=> ["ilr_progression_outcome_types.name"],
                "filter" => "like"
            ],
            [
                "name" => "Learner Destination and Progression - Outcome code",
                "slug"=> "progression_outcome_code_name",
                "select"=>"ilr_progression_outcome_codes.name as progression_outcome_code_name",
                "resource"=>"User.progressionOutcomeCode",
                "type"=>"text",
                "sort"=>"ilr_progression_outcome_codes.name",
                "filter_key"=>["ilr_progression_outcome_codes.name"],
                "filter"=>"like"
            ],
            [
                "name" => "Learner Destination and Progression - Outcome start date",
                "slug" => "learner_destination_and_progression_outcome_start_date",
                "select" => "latest_progression.OutStartDate as learner_destination_and_progression_outcome_start_date",
                "resource" => "User.learnerDestinationAndProgressionOutcome",
                "type" => "date-range",
                "sort" => "latest_progression.OutStartDate",
                "filter_key" => "latest_progression.OutStartDate",
                "filter" => "between",
            ],
            [
                "name" => "Learner Destination and Progression - Outcome end date",
                "slug" => "learner_destination_and_progression_outcome_end_date",
                "select" => "latest_progression.OutEndDate as learner_destination_and_progression_outcome_end_date",
                "resource" => "User.learnerDestinationAndProgressionOutcome",
                "type" => "date-range",
                "sort" => "latest_progression.OutEndDate",
                "filter_key" => "latest_progression.OutEndDate",
                "filter" => "between",
            ],
            [
                "name" => "Learner Destination and Progression - Outcome collection date",
                "slug" =>  "learner_destination_and_progression_outcome_collection_date",
                "select" => "latest_progression.OutCollDate as learner_destination_and_progression_outcome_collection_date",
                "resource" => "User.learnerDestinationAndProgressionOutcome",
                "type" => "date-range",
                "sort" => "latest_progression.OutCollDate",
                "filter_key" => "latest_progression.OutCollDate",
                "filter" => "between",
            ],

		],
		"user_forms"=> [
			[
				"name"=>"Employee ID",
				"slug"=>"user_id",
				"select"=>"users.id as user_id",
				"resource"=>"User",
				"group_by"=>"id",
				"filter"=>"like",
				"sort"=>"users.id",
				"type"=>"number",
				"filter_key"=>["users.id"],
				"user_specific"=>true,
				"user_specific_key"=>"id",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.id"
			],
			[
				"name"=>"Name",
				"resource"=>"Form",
				"slug"=>"form_name",
				"type"=>"text",
				"filter"=>"like",
				"select"=>"forms.name as form_name",
				"sort"=>"forms.name",
				"filter_key"=>["forms.name"]
			],
			[
				"name"=>"Username",
				"resource"=>"User",
				"slug"=>"user_username",
				"type"=>"text",
				"filter"=>"like",
				"select"=>"users.username as user_username",
				"sort"=>"users.username",
				"filter_key"=>["users.username"],
				"user_specific"=>true,
				"user_specific_key"=>"username",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.username"
			],
			[
				"name"=>"First Name",
				"resource"=>"User",
				"slug"=>"fname",
				"type"=>"text",
				"filter"=>"like",
					"filter_key"=>["users.fname"],
				"select"=>"users.fname as fname",
				"sort"=>"users.fname",
				"user_specific"=>true,
				"user_specific_key"=>"fname",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.fname"
			],
			[
				"name"=>"Last Name",
				"resource"=>"User",
				"slug"=>"lname",
				"type"=>"text",
				"filter"=>"like",
					"filter_key"=>["users.lname"],
				"select"=>"users.lname",
				"sort"=>"users.lname",
				"user_specific"=>true,
				"user_specific_key"=>"lname",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.lname"
			],
			[
				"name"=>"%%department%%",
				"resource"=>"User.Department",
				"slug"=>"department_name",
				"type"=>"text",
				"group_by"=>"department_id",
				"multi_select"=>true,
				"filter"=>"in",
				"class"=>Department::class,
				"filter_key"=>"departments.id",
				"data_key"=>"id",
				"select"=>"departments.name as department_name",
				"sort"=>"departments.name",
				"user_specific"=>true,
				"user_specific_key"=>"department_id",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.department_id"
			],
			[
				"name"=>"%%designation%%",
				"resource"=>"User.Designation",
				"slug"=>"designation_name",
				"type"=>"text",
				"filter"=>"in",
				"filter_key"=>"designations.id",
				"data_key"=>"id",
				"multi_select"=>true,
				"class"=>Designation::class,
				"sort"=>"designations.name",
				"select"=>"designations.name as designation_name",
				"user_specific"=>true,
				"user_specific_key"=>"designation_id",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.designation_id"
			],
			[
				"name"=>"%%role%%",
				"resource"=>"User.Role",
				"slug"=>"role_name",
				"type"=>"text",
				"multi_select"=>true,
				"filter"=>"in",
				"class"=>Role::class,
				"filter_key"=>"roles.id",
				"data_key"=>"id",
				"select"=>"roles.name as role_name",
				"sort"=>"roles.name",
				"user_specific"=>true,
				"user_specific_key"=>"role_id",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.role_id"
			],
			[
				"name"=>"Type",
				"resource"=>"user_forms",
				"slug"=>"user_form_type",
				"type"=>"text",
				"filter"=>"like",
				"select"=>"user_forms.type as user_form_type",
				"sort"=>"user_forms.type",
				"filter_key"=>["user_forms.type"]
			],
			[
				"name"=>"Status",
				"resource"=>"user_forms",
				"slug"=>"user_form_status",
				"type"=>"status",
				"drop_down"=>[
						UserForm::USER_FORM_STATUS_AWAITING_SIGN_OFF=>"Awaiting Sign-off",
						UserForm::USER_FORM_STATUS_NOT_STARTED=>"Not Started",
						UserForm::USER_FORM_STATUS_IN_PROGRESS=>"In Progress",
						UserForm::USER_FORM_STATUS_COMPLETED=>"Completed",
				],
				"filter"=>"like",
				"select"=>"user_forms.user_form_status as user_form_status",
				"sort"=>"user_forms.user_form_status",
				"filter_key"=>["user_forms.user_form_status"]
			],
			[
				"name"=>"Associated Form-Program",
				"resource"=>"FormProgramme",
				"slug"=>"standard_name",
				"type"=>"text",
				"filter"=>"like",
				"select"=>"form_apprenticeship_standards.name as standard_name",
				"sort"=>"form_apprenticeship_standards.name",
				"filter_key"=>["form_apprenticeship_standards.name"]
			],
			[
				"name"=>"Associated Form-Event",
				"resource"=>"FormSchedule",
				"slug"=>"schedule_name",
				"type"=>"text",
				"filter"=>"like",
				"select"=>"form_schedules.name  as schedule_name",
				"sort"=>"form_schedules.name",
				"filter_key"=>["form_schedules.name"]
			],
			[
				"name"=>"%%event%% Name",
				"resource"=>"CustomEvent",
				"slug"=>"custom_schedule_name",
				"type"=>"text",
				"filter"=>"like",
				"select"=>"custom_schedule.name as custom_schedule_name",
				"sort"=>"custom_schedule.name",
				"filter_key"=>["custom_schedule.name"]
			],
			[
				"name"=>"%%event%% Type",
				"resource" => "CustomEvent",
				"slug"=>"custom_schedule_type",
				"type"=>"text",
				"filter"=>"like",
				"select"=>"custom_schedule.type as custom_schedule_type",
				"sort"=>"custom_schedule.name",
				"filter_key"=>["custom_schedule.type"]
			],
			[
				"name"=>"Programme Name",
				"resource"=>"CustomProgramme",
				"slug"=>"custom_standard_name",
				"type"=>"text",
				"filter"=>"like",
				"select"=>"custom_standard.name as custom_standard_name",
				"sort"=>"custom_standard.name",
				"filter_key"=>["custom_standard.name"]
			],
			[
				"name"=>"%%event%% Assigned Type",
				"resource"=>"CustomEvent",
				"slug"=>"custom_form_fields_is_assigned",
				"type"=>"status",
				"drop_down"=>[
					0=>"Unassigned",
					1=>"Assigned"
				],
				"filter"=>"like",
				"select"=>"custom_form_fields.is_assigned as custom_form_fields_is_assigned",
				"sort"=> "custom_form_fields.is_assigned",
				"filter_key"=>["custom_form_fields.is_assigned"]
			],
			[
				"name"=>"Program Assigned Type",
				"resource"=>"CustomEvent",
				"slug"=>"custom_form_fields_programme_is_assigned",
				"type"=>"status",
				"drop_down"=>[
					0=>"Unassigned",
					1=>"Assigned"
				],
				"filter"=>"like",
				"select"=>"custom_form_fields_programme.is_assigned as custom_form_fields_programme_is_assigned",
				"sort"=> "custom_form_fields_programme.is_assigned",
				"filter_key"=>["custom_form_fields_programme.is_assigned"]
			],
			[
				"name" => "Sub "."%%Department%%",
				"slug" => "user.sub_departments.department.name",
				"select" => "departments.name as department_name",
				"resource" => "User.SubDepartments.Department", //Relationship
				"group_by" => "department_id",
				"multi_select" => true,
				"filter" => "in",
				"class" => Department::class,
				"conditional_clauses" => [[
					'clause' => 'where',
					'condition' => [
							['parent_id', '!=', null],
					],
				],],
				"filter_key" => "id",
				"type" => "array",
				"data_key" => "id",
			],
			[
				"name"=>"Associated Form-Learning Resource/Lesson",
				"resource"=>"formLearningModule",
				"slug"=>"resource_name",
				"type"=>"text",
				"filter"=>"like",
				"select"=>"form_resource.name  as resource_name",
				"sort"=>"form_resource.name",
				"filter_key"=>["form_resource.name"]
			],
			[
				"name"=>"User Managers",
				"slug"=>"user.managers.fname+lname",
				"resource"=>"User.Managers",
				"type"=>"array",
				"multi_select"=>false,
				"filter"=>"like",
				"filter_key"=>["fname","lname"],
				"user_specific"=>true,
				"user_specific_key"=>"id",
				"user_specific_model"=>"Managers",
				"user_specific_column"=>"manager_users.manager_id"
			],
			[
				"name"=>"Agreement ID",
				"slug"=>"AgreeId",
				"select"=>"ilr_learner_employment_statuses.AgreeId as AgreeId",
				"resource"=>"User.employmentStatus",
				"type"=>"number",
				"sort"=>"ilr_learner_employment_statuses.AgreeId",
				"filter_key"=>"ilr_learner_employment_statuses.AgreeId",
				"filter"=>"like",
			],
			[
				"name"=>"Employer ID",
				"slug"=>"EmpId",
				"select"=>"ilr_learner_employment_statuses.EmpId as EmpId",
				"resource"=>"User.employmentStatus",
				"type"=>"number",
				"sort"=>"ilr_learner_employment_statuses.EmpId",
				"filter_key"=>"ilr_learner_employment_statuses.EmpId",
				"filter"=>"like",
			],
			[
			"name" => "Apprenticeship standard code",
			"slug" => "StdCode",
			"select"=> "ilr_learning_deliveries.StdCode as StdCode",
			"resource"=> "User.ilrLearningDeliveries",
			"type"=> "number",
			"sort"=> "ilr_learning_deliveries.StdCode",
			"filter_key"=> ["ilr_learning_deliveries.StdCode"],
			"filter" => "like"
			],
		[
			"name" => "Prior Attainment",
			"slug" => "PriorAttainment",
			"select"=> "ilr_user_prior_attainment_level.name as PriorAttainment",
			"resource" => "User.priorAttainment",
			"type"=> "text",
			"sort"=> "ilr_user_prior_attainment_level.name",
			"filter_key"=> ["ilr_user_prior_attainment_level.name"],
			"filter" => "like"
		]
		],
		"coupons" => [
			[
				"name"=>"Employee ID",
				"slug"=>"user_id",
				"select"=>"users.id as user_id",
				"resource"=>"User",
				"group_by"=>"id",
				"filter"=>"like",
				"sort"=>"users.id",
				"type"=>"number",
				"filter_key"=>["users.id"],
				"user_specific"=>true,
				"user_specific_key"=>"id",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.id"
			],
			[
				"name" => "Coupon Code",
				"slug" => "coupon_code",
				"select" => "coupons.code as coupon_code",
				"resource" => "Coupon",
				"group_by" => "code",
				"filter" => "like",
				"sort" => "coupon.code",
				"type" => "number",
				"filter_key" => ["coupon.code"],
				// "user_specific" => true,
				// "user_specific_key" => "id",
				// "user_specific_model" => "Coupon",
				// "user_specific_column" => "coupons.id"
			],
			[
				"name" => "Coupon Name",
				"resource" => "Coupon",
				"slug" => "coupon_name",
				"type" => "text",
				"filter" => "like",
				"select" => "coupons.name as coupon_name",
				"sort" => "coupons.name",
				"filter_key" => ["coupons.name"]
			],
			[
				"name" => "Email",
				"resource" => "User",
				"slug" => "user_email",
				"type" => "text",
				"filter" => "like",
				"select" => "users.email as user_email",
				"sort" => "users.email",
				"filter_key" => ["users.email"]
			],
			[
				"name" => "Date Used",
				"resource" => "coupons",
				"slug" => "coupon_created_at",
				"type" => "text",
				"filter" => "like",
				"select" => "coupon_usages.created_at as coupon_created_at",
				"sort" => "coupon_usages.created_at",
				"filter_key" => ["coupon_usages.created_at"]
			],
			[
				"name"=>"User Managers",
				"slug"=>"user.managers.fname+lname",
				"resource"=>"User.Managers",
				"type"=>"array",
				"multi_select"=>false,
				"filter"=>"like",
				"filter_key"=>["fname","lname"],
				"user_specific"=>true,
				"user_specific_key"=>"id",
				"user_specific_model"=>"Managers",
				"user_specific_column"=>"manager_users.manager_id"
			]
		],
		"career_paths" => [
			[
				"name" => "CareerPath ID",
				"slug" => "career_path_id",
				"select" => "career_paths.id as career_path_id",
				"resource" => "career_path_programme",
				"group_by" => "id",
				"filter" => "like",
				"sort" => "career_path.id",
				"type" => "number",
				"filter_key" => ["career_path.id"],
				"user_specific" => true,
				"user_specific_key" => "id",
				"user_specific_model" => "CareerPath",
				"user_specific_column" => "career_paths.id"
			],
			[
				"name" => "CareerPath Name",
				"resource" => "career_path_programme",
				"slug" => "career_path_name",
				"type" => "text",
				"filter" => "like",
				"select" => "career_paths.name as career_path_name",
				"sort" => "career_paths.name",
				"filter_key" => ["career_paths.name"]
			],
			[
				"name" => "Designation Name",
				"resource" => "career_path_programme",
				"slug" => "designation_name",
				"type" => "text",
				"filter" => "like",
				"select" => "designations.name as designation_name",
				"sort" => "designations.name",
				"filter_key" => ["designations.name"]
			],
			[
				"name" => "Programme Name",
				"resource" => "career_path_programme",
				"slug" => "programme_name",
				"type" => "text",
				"filter" => "like",
				"select" => "apprenticeship_standards.name as programme_name",
				"sort" => "appreticeship_standards.name",
				"filter_key" => ["apprenticeship_standards.name"]
			],
			[
				"name" => "Programme Criteria",
				"slug" => "apprenticeship_issues_name",
				"resource" => "career_path_programme",
				"type" => "text",
				"filter" => "like",
				"select" => "apprenticeship_issues.name as apprenticeship_issues_name",
				"sort" => "appreticeship_issues.name",
				"filter_key" => ["apprenticeship_issues.name"]
			],
			[
				"name" => "Learning Resource",
				"slug" => "learning_module_name",
				"resource" => "career_path_programme",
				"type" => "text",
				"filter" => "like",
				"select" => "learning_modules.name as learning_module_name",
				"sort" => "learning_modules.name",
				"filter_key" => ["learning_modules.name"]
			],
		],
		'purchases' => [
			[
				"name" => "Product",
				"slug" => "product_name",
				"select" => "CASE WHEN schedules.name IS NOT NULL THEN schedules.name WHEN learning_modules.name IS NOT NULL THEN learning_modules.name END AS product_name",
				"type" => "text",
				"selectRaw" => true,
				"resource" => "purchases_data",
				"filter" => "like",
				"filter_key" => ["schedules.name", "learning_modules.name"]
			],
			[
				"name" => "Cost",
				"slug" => "product_cost",
				"select" => "purchase_payment_transactions.item_cost as product_cost",
				"resource" => "purchases_data",
				"filter" => "like",
				"type" => "number",
				"filter_key" > ["purchase_payment_transactions.item_cost"],
				"sort" => "purchase_payment_transactions.item_cost"
			],
			[
				"name" => "Type",
				"slug" => "type",
				"select" => "purchase_payment_transactions.type",
				"resource" => "purchases_data",
				"filter" => "like",
				"type" => "text",
				"filter_key" => ["purchase_payment_transactions.type"],
				"filter_key" => "purchase_payment_transactions.type",
				"sort" => "purchase_payment_transactions.type"
			],
			[
				"name" => "Purchaser",
				"slug" => "user_name",
				"select" => "CONCAT(users.fname,' ',users.lname) as user_name",
				"selectRaw" => true,
				"filter" => "like",
				"type" => "text",
				"resource" => "purchases_data",
				"filter_raw" => true,
				"filter_key" => ["CONCAT(users.fname,' ',users.lname)"],
				"sort" => "users.name"
			],
			[
				"name" => "%%company%%",
				"slug" => "company_name",
				"select" => "companies.name as company_name",
				"resource" => "purchases_data",
				"group_by" => "company_id",
				"type" => "text",
				"multi_select" => true,
				"class" => Company::class,
				"filter" => "in",
				"filter_key" => "users.company_id",
				"data_key" => "id",
				"sort" => "companies.name",
				"user_specific" => true,
				"user_specific_key" => "company_id",
				"user_specific_model" => "User",
				"user_specific_column" => "users.company_id"
			],
			[
				'name' => 'Learning Provider',
				'slug' => 'provider_name',
				"select"=>"learning_providers.company as provider_name",
				'resource' => 'purchases_data',
				"multi_select"=>true,
				"filter"=>"in",
				"filter_key"=>"learning_modules.provider_id",
				"type"=>"text",
				"class" => LearningProvider::class,
				"data_key" => "id",
				"sort" => "provider_name",
			]
		],

		"all_learning" => [
			[
				"name"=>"%%company%%",
				"slug"=>'company_name',
				"resource"=>"User.Company",
				"type"=>"text",
				"multi_select"=>true,
				"class"=>Company::class,
				"filter"=>"in",
				"filter_key"=>"companies.id",
				"data_key"=>"id",
				"select"=>"companies.name as company_name",
				"sort"=>"companies.name",
				"user_specific"=>true,
				"user_specific_key"=>"company_id",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.company_id"
			],
			[
				"name"=>"Department",
				"resource"=>"User.Department",
				"slug"=>"department_name",
				"type"=>"text",
				"group_by"=>"department_id",
				"multi_select"=>true,
				"filter"=>"in",
				"class"=>Department::class,
				"filter_key"=>"departments.id",
				"data_key"=>"id",
				"select"=>"departments.name as department_name",
				"sort"=>"departments.name",
				"user_specific"=>true,
				"user_specific_key"=>"department_id",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.department_id"
			],
			[
				"name"=>"%%group%%",
				"slug"=>"user.groups.name",
				"resource"=>"User.Groups",
				"type"=>"array",
				"filter"=>"in",
				"filter_key"=>"groups.id",
				"data_key"=>"id",
				"multi_select"=>true,
				"class"=>Group::class
			],
			[
				"name"=>"Name",
				"slug"=>"name",
				"resource"=>"all_learning",
				"type"=>"text",
				"filter"=>"like",
				"sort"=>"name",
				"filter_key"=>["name"],
				'models' => [
					'schedule_links' => [
						'filter_key' => [
							'name' => 'schedules.name'
						]
					],
					'learning_results' => [
						'filter_key' => [
							'name' => 'learning_modules.name'
						]
					],
					'apprenticeship_standards_users' => [
						'filter_key' => [
							'name' => 'apprenticeship_standards.name'
						]
					]
				]
			],
			[
				"name"=>"Type",
				"slug"=>"type",
				"resource"=>"all_learning",
				"type"=>"text",
				"filter"=>"like",
				"drop_down"=>[
					'events'=>"Events",
					'learning_results'=>"Learning Resource",
					'standards' => 'Learning Programme'
				],
				"filter_key"=>["type"],
				"filterable" => false
			],
			[
				"name"=>"Completion Status",
				"slug"=>"completion_status",
				"resource"=>"all_learning",
				"type"=>"text",
				"filter"=>"like",
				"drop_down"=>[
					'in_progress'=>"In Progress",
					'not_attempted'=>"Not Attempted",
					'completed' => 'Completed'
				],
				"sort"=>"completion_status",
				"filter_key"=>["completion_status"],
				'models' => [
					'schedule_links' => [
						'filter_key' => [
							'completion_status' => 'schedule_links.completion_status'
						]
					],
					'learning_results' => [
						'filter_key' => [
							'completion_status' => 'learning_results.completion_status'
						]
					],
					'apprenticeship_standards_users' => [
						'filter_key' => [
							'completion_status' => 'apprenticeship_standards_users.completion_status'
						]
					]
				]
			],
			[
				"name"=>"Completion Date",
				"slug"=>"completion_date",
				"resource"=>"all_learning",
				"type"=>"date-range",
				"filter"=>"between",
				"sort"=>"completion_date",
				"filter_key"=>["completion_date"],
				'models' => [
					'schedule_links' => [
						'filter_key' => [
							'completion_date' => null
						]
					],
					'learning_results' => [
						'filter_key' => [
							'completion_date' => 'learning_results.completed_at'
						]
					],
					'apprenticeship_standards_users' => [
						'filter_key' => [
							'completion_date' => 'apprenticeship_standards_users.completed_at'
						]
					]
				]
			],
			[
				"name"=>"%%group_department_code%%",
				"slug"=>"group_department_code_name",
				"resource"=>"all_learning",
				"type"=>"text",
				"multi_select"=>true,
				"filter"=>"in",
				"sort"=>"group_department_code_name",
				"class"=>LearningModuleGroupDepartmentCode::class,
				"filter_key"=>"group_department_code_name",
				"data_key"=>"id",
				"models"=>[
					"schedule_links" =>[
						'filter_key'=>[
							'group_department_code_name' => null
						]
					],
					'learning_results'=>[
						'filter_key'=>[
							'group_department_code_name'=>'learning_module_group_department_codes.id'
						]
					],
					'apprenticeship_standards_users'=>[
						'filter_key'=>[
							'group_department_code_name'=>null
						]
					]
				]
			],
			[
				"name"=>"Learning Category",
				"slug"=>"learning_category_name",
				"resource"=>"all_learning",
				"type"=>"text",
				"multi_select"=>true,
				"filter"=>"in",
				"sort"=>"learning_category_name",
				"class"=>LearningModuleCategory::class,
				"filter_key"=>"learning_category_name",
				"data_key"=>"id",
				"models"=>[
					"schedule_links" =>[
						'filter_key'=>[
							'learning_category_name' => 'learning_module_categories.id'
						]
					],
					'learning_results'=>[
						'filter_key'=>[
							'learning_category_name'=>'learning_module_categories.id'
						]
					],
					'apprenticeship_standards_users'=>[
						'filter_key'=>[
							'learning_category_name'=>'learning_module_categories.id'
						]
					]
				]
			],
			[
				"name"=>"Duration",
				"slug"=>"duration",
				"resource"=>"all_learning",
				"type"=>"text",
				"filter"=>"like",
				"sort"=>"duration",
				"filter_key"=>["duration"],
				'models' => [
					'schedule_links' => [
						'filter_key' => [
							'duration' => 'schedules.duration'
						]
					],
					'learning_results' => [
						'filter_key' => [
							'duration' => null
						]
					],
					'apprenticeship_standards_users' => [
						'filter_key' => [
							'duration' => null
						]
					]
				]
			],
			[
				"name"=>"Employee ID",
				"slug"=>"user_id",
				"resource"=>"User",
				"type"=>"number",
				"filter"=>"like",
				"select"=>"users.id as user_id",
				"sort"=>"users.id",
				"filter_key"=>["users.id"],
				"user_specific"=>true,
				"user_specific_key"=>"id",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.id"
			],
			[
				"name"=>"Username",
				"slug"=>'users_username',
				"resource"=>"User",
				"type"=>"text",
				"filter"=>"like",
				"select"=>"users.username as users_username",
				"sort"=>"users.username",
				"filter_key"=>["users.username"],
				"user_specific"=>true,
				"user_specific_key"=>"username",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.username"
			],
			[
				"name"=>"First Name",
				"slug"=>'user_fname',
				"resource"=>"User",
				"type"=>"text",
				"filter"=>"like",
				"select"=>"users.fname as user_fname",
				"sort"=>"users.fname",
				"filter_key"=>["users.fname"],
				"user_specific"=>true,
				"user_specific_key"=>"fname",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.fname"
			],
			[
				"name"=>"Last Name",
				"slug"=>'user_lname',
				"resource"=>"User",
				"type"=>"text",
				"filter"=>"like",
				"select"=>"users.lname as user_lname",
				"sort"=>"users.lname",
				"filter_key"=>["users.lname"],
				"user_specific"=>true,
				"user_specific_key"=>"lname",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.lname"
			],
			[
				"name"=>"User Managers",
				"slug"=>"user.managers.fname+lname",
				"resource"=>"User.Managers",
				"type"=>"array",
				"multi_select"=>false,
				"filter"=>"like",
				"filter_key"=>["fname","lname"],
				"user_specific"=>true,
				"user_specific_key"=>"id",
				"user_specific_model"=>"Managers",
				"user_specific_column"=>"manager_users.manager_id"
			],
			[
				"name"=>"Role",
				"slug"=>'role_name',
				"resource"=>"User.Role",
				"type"=>"text",
				"multi_select"=>true,
				"class"=>Role::class,
				"filter"=>"in",
				"select"=>"roles.name as role_name",
				"sort"=>"roles.name",
				"filter_key"=>"roles.name",
				"data_key" => "name",
				"user_specific"=>true,
				"user_specific_key"=>"role_id",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.role_id"
			],
			[
				"name"=>"Email",
				"slug"=>'user_email',
				"resource"=>"User",
				"type"=>"text",
				"filter"=>"like",
				"select"=>"users.email as user_email",
				"sort"=>"users.email",
				"filter_key"=>["users.email"],
				"user_specific"=>true,
				"user_specific_key"=>"email",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.email"
			],
			[
				"name"=>"Job Title",
				"slug"=>'designation_name',
				"resource"=>"User.Designation",
				"type"=>"text",
				"select"=>"designations.name as designation_name",
				"sort"=>"designations.name",
				"multi_select"=>true,
				"class"=>Designation::class,
				"data_key"=>"id",
				"filter"=>"in",
				"filter_key"=>"designations.id",
				"user_specific"=>true,
				"user_specific_key"=>"designation_id",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.designation_id"
			],
			[
				"name"=>"%%location%%",
				"slug"=>'location_name',
				"resource"=>"User.Location",
				"type"=>"text",
				"multi_select"=>true,
				"class"=>Location::class,
				"filter_key"=>"locations.id",
				"filter"=>"in",
				"select"=>"locations.name as location_name",
				"sort"=>"locations.name",
				"data_key"=>"id",
				"user_specific"=>true,
				"user_specific_key"=>"location_id",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.location_id"
			],
			[
				"name"=>"%%country%%",
				"slug"=>'country_name',
				"resource"=>"User.Country",
				"type"=>"text",
				"select"=>"countries.name as country_name",
				"sort"=>"countries.name",
				"filter"=>"like",
				"filter_key"=>["countries.name"],
				"user_specific"=>true,
				"user_specific_key"=>"country_id",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.country_id"
			],
			[
				"name"=>"%%city%%",
				"slug"=>'city_name',
				"resource"=>"User.City",
				"type"=>"text",
				"select"=>"cities.name as city_name",
				"sort"=>"cities.name",
				"filter_key"=>["cities.name"],
				"filter"=>"like",
				"user_specific"=>true,
				"user_specific_key"=>"city_id",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.city_id"
			],
			[
				"name"=>"User Status",
				"slug"=>'user_status',
				"resource"=>"User",
				"type"=>"status",
				"drop_down"=>[
					1=>"Enabled",
					0=>"Disabled"
				],
				"filter"=>"like",
				"filter_key"=>"users.status",
				"select"=>"users.status as user_status",
				"sort"=>"users.status",
				"user_specific"=>true,
				"user_specific_key"=>"status",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.status"
			],
			[
				"name" => "Sub "."%%Department%%",
				"slug" => "user.sub_departments.department.name",
				"select" => "departments.name as department_name",
				"resource" => "User.SubDepartments.Department", //Relationship
				"group_by" => "department_id",
				"multi_select" => true,
				"filter" => "in",
				"class" => Department::class,
				"conditional_clauses" => [
					[
						'clause' => 'where',
						'condition' => [
							['parent_id', '!=', null],
						],
					],
				],
				"filter_key" => "id",
				"type" => "array",
				"data_key" => "id",
			],
			[
				"name"=>"%%target_catalogue%%",
				"slug"=>"module.target_catalogues.name",
				"resource"=>"Module.TargetCatalogues",
				"type"=>"array",
				"filter"=>"in",
				"filter_key"=>"target_catalogues.id",
				"data_key"=>"id",
				"multi_select"=>true,
				"class"=>TargetCatalogue::class
			],
			[
				"name"=>"%%delivery_provider_type%%",
				"slug"=>"delivery_provider_type_name",
				"resource"=>"all_learning",
				"type"=>"text",
				"multi_select"=>true,
				"filter"=>"in",
				"sort"=>"delivery_provider_type_name",
				"class"=>LearningModuleDeliveryProviderType::class,
				"filter_key"=>"delivery_provider_type_name",
				"data_key"=>"id",
				"models"=>[
					"schedule_links" =>[
						'filter_key'=>[
							'delivery_provider_type_name' => null
						]
					],
					'learning_results'=>[
						'filter_key'=>[
							'delivery_provider_type_name'=>'learning_module_delivery_provider_types.id'
						]
					],
					'apprenticeship_standards_users'=>[
						'filter_key'=>[
							'delivery_provider_type_name'=>null
						]
					]
				]
			]
		],
		"quiz_analysis" => [
			[
				"name"=>"Employee ID",
				"slug"=>"user_id",
				"select"=>"users.id as user_id",
				"resource"=>"User",
				"group_by"=>"id",
				"filter"=>"like",
				"sort"=>"users.id",
				"type"=>"number",
				"filter_key"=>["users.id"],
				"user_specific"=>true,
				"user_specific_key"=>"id",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.id"
			],
			[
				"name"=>"%%learning_resource%%",
				"slug"=>'module_name',
				"resource"=>"Module",
				"multi_select"=>true,
				"class"=>LearningModule::class,
				"type"=>"text",
				"filter"=>"in",
				"filter_key"=>"learning_modules.id",
				"data_key"=>"id",
				"select"=>"learning_modules.name as module_name",
				"sort"=>"learning_modules.name"
			],
			[
				"name"=>"Employee Name",
				"slug"=>"employee_name",
				"select"=>"CONCAT(users.fname,' ',users.lname) as employee_name",
				"selectRaw"=>true,
				"filter"=>"like",
				"type"=>"text",
				"resource"=>"User",
				"filter_raw"=>true,
				"filter_key"=>["CONCAT(users.fname,' ',users.lname)"],
				"sort"=>"users.name"
			],
			[
				"name" => "Question",
				"resource" => "learning_result_answers",
				"slug" => "question",
				"type" => "text",
				"filter" => "like",
				"select" => "learning_result_answers.question as question",
				"sort" => "learning_result_answers.question",
				"filter_key" => ["learning_result_answers.question"]
			],
			[
				"name" => "Answer",
				"resource" => "learning_result_answers",
				"slug" => "answer",
				"type" => "text",
				"filter" => "like",
				"select" => "learning_result_answers.answer as answer",
				"sort" => "learning_result_answers.answer",
				"filter_key" => ["learning_result_answers.answer"]
			],
			[
				"name" => "Submitted date",
				"resource" => "learning_result_answers",
				"slug" => "submitted_date",
				"type" => "text",
				"filter" => "between",
				"type"=>"date-range",
				"select" => "learning_result_answers.submitted_date as submitted_date",
				"sort" => "learning_result_answers.submitted_date",
				"filter_key" => "learning_result_answers.submitted_date" // date filter need to change
			],
			[
				"name"=>"User Managers",
				"slug"=>"user.managers.fname+lname",
				"resource"=>"User.Managers",
				"type"=>"array",
				"multi_select"=>false,
				"filter"=>"like",
				"filter_key"=>["fname","lname"],
				"user_specific"=>true,
				"user_specific_key"=>"id",
				"user_specific_model"=>"Managers",
				"user_specific_column"=>"manager_users.manager_id"
			],
            [
                "name"=>"%%company%%",
                "slug"=>"company_name",
                "select"=>"companies.name as company_name",
                "resource"=>"User.Company",
                "group_by"=>"company_id",
                "type"=>"text",
                "multi_select"=>true,
                "class"=>Company::class,
                "filter"=>"in",
                "filter_key"=>"companies.id",
                "data_key"=>"id",
                "sort"=>"companies.name",
                "user_specific"=>true,
                "user_specific_key"=>"company_id",
                "user_specific_model"=>"User",
                "user_specific_column"=>"users.company_id"
            ],
        [
            "name"=>"%%department%%",
            "resource"=>"User.Department",
            "slug"=>"department_name",
            "type"=>"text",
            "group_by"=>"department_id",
            "multi_select"=>true,
            "filter"=>"in",
            "class"=>Department::class,
            "filter_key"=>"departments.id",
            "select"=>"departments.name as department_name",
            "data_key"=>"id",
            "sort"=>"departments.name",
            "user_specific"=>true,
            "user_specific_key"=>"department_id",
            "user_specific_model"=>"User",
            "user_specific_column"=>"users.department_id"
        ],
        [
            "name" => "Sub "."%%Department%%",
            "slug" => "user.sub_departments.department.name",
            "select" => "departments.name as department_name",
            "resource" => "User.SubDepartments.Department", //Relationship
            "group_by" => "department_id",
            "multi_select" => true,
            "filter" => "in",
            "class" => Department::class,
            "conditional_clauses" => [[
                'clause' => 'where',
                'condition' => [
                    ['parent_id', '!=', null],
                ],
            ],],
            "filter_key" => "id",
            "type" => "array",
            "data_key" => "id",
        ],
        [
            "name"=>"%%group%%",
            "slug"=>"user.groups.name",
            "resource"=>"User.Groups",
            "type"=>"array",
            "filter"=>"in",
            "filter_key"=>"groups.id",
            "data_key"=>"id",
            "multi_select"=>true,
            "class"=>Group::class
        ],
        [
            "name"=>"Watch",
            "slug"=>"user.watch.name",
            "resource"=>"User.Watch",
            "type"=>"array",
            "filter"=>"in",
            "filter_key"=>"watch.id",
            "data_key"=>"id",
            "multi_select"=>true,
            "class"=>Group::class
        ],
        [
            "name"=>"%%location%%",
            "resource"=>"User.Location",
            "slug"=>"location_name",
            "type"=>"text",
            "group_by"=>"location_id",
            "multi_select"=>true,
            "filter"=>"in",
            "class"=>Location::class,
            "filter_key"=>"locations.id",
            "select"=>"locations.name as location_name",
            "data_key"=>"id",
            "sort"=>"locations.name",
            "user_specific"=>true,
            "user_specific_column"=>'users.location_id',
            "user_specific_key"=>"location_id",
            "user_specific_model"=>"User"
        ],
        [
            "name"=>"Email",
            "slug"=>"email",
            "select"=>"users.email",
            "resource"=>"User",
            "grpup_by"=>"email",
            "filter"=>"like",
            "type"=>"text",
            "sort"=>"users.email",
            "user_specific_key"=>"email",
            "user_specific_model"=>"User",
            "user_specific_column"=>"users.email",
            "user_specific"=>true,
            "filter_key"=>["users.email"]
        ],
        [
            "name"=>"Job Title",
            "slug"=>'designation_name',
            "resource"=>"User.Designation",
            "type"=>"text",
            "select"=>"designations.name as designation_name",
            "sort"=>"designations.name",
            "multi_select"=>true,
            "class"=>Designation::class,
            "data_key"=>"id",
            "filter"=>"in",
            "filter_key"=>"designations.id",
            "user_specific"=>true,
            "user_specific_key"=>"designation_id",
            "user_specific_model"=>"User",
            "user_specific_column"=>"users.designation_id"
        ],
        [
            "name"=>"Learning Category",
            "slug"=>'category_name',
            "resource"=>"Module.Category",
            "multi_select"=>true,
            "class"=>LearningModuleCategory::class,
            "type"=>"text",
            "filter"=>"in",
            "filter_key"=>"learning_module_categories.id",
            "select"=>"learning_module_categories.name as category_name",
            "sort"=>"learning_module_categories.name",
            "data_key"=>"id"
        ],
        [
            "name"=>"Username",
            "slug"=>"username",
            "select"=>"users.username",
            "resource"=>"User",
            "grpup_by"=>"username",
            "filter"=>"like",
            "type"=>"text",
            "sort"=>"users.username",
            "user_specific"=>true,
            "user_specific_model"=>"User",
            "user_specific_column"=>"users.username",
            "user_specific_key"=>"username",
            "filter_key"=>["users.username"]
        ]
        ],

		'user_schedule_waiting_lists' => [
			[
				"name"=>"%%user%% ID", // Employee ID is usercode, this might get confusing.
				"slug"=>"id",
				"select"=>"users.id",
				"resource"=>"User",
				"group_by"=>"id",
				"filter"=>"like",
				"sort"=>"users.id",
				"type"=>"number",
				"filter_key"=>["users.id"],
				"user_specific"=>true,
				"user_specific_key"=>"id",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.id"

			],
			[
				"name"=>"First Name",
				"slug"=>"fname",
				"select"=>"users.fname",
				"resource"=>"User",
				"group_by"=>"fname",
				"sort"=>"users.fname",
				"filter"=>"like",
				"filter_key"=>["users.fname"],
				"type"=>"text",
				"user_specific"=>true,
				"user_specific_key"=>"fname",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.fname"
			],
			[
				"name"=>"Last Name",
				"slug"=>"lname",
				"select"=>"users.lname",
				"resource"=>"User",
				"group_by"=>"lname",
				"sort"=>"users.lname",
				"filter"=>"like",
				"type"=>"text",
				"user_specific"=>true,
				"user_specific_key"=>"lname",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.lname",
				"filter_key"=>["users.lname"]
			],
			[
				"name"=>"Role",
				"slug"=>"role_name",
				"select"=>"roles.name as role_name",
				"resource"=>"User.Role",
				"group_by"=>"role_id",
				"type"=>"text",
				"multi_select"=>true,
				"filter"=>"in",
				"sort"=>"roles.name",
				"class"=>Role::class,
				"filter_key"=>"roles.id",
				"type"=>"text",
				"data_key"=>"id",
				"user_specific"=>true,
				"user_specific_column">"users.role_id",
				"user_specific_model"=>"User",
				"user_specific_key"=>"role_id"
			],
			[
				"name"=>"Email",
				"slug"=>"email",
				"select"=>"users.email",
				"resource"=>"User",
				"grpup_by"=>"email",
				"filter"=>"like",
				"type"=>"text",
				"sort"=>"users.email",
				"user_specific_key"=>"email",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.email",
				"user_specific"=>true,
				"filter_key"=>["users.email"]
			],
			[
				"name"=>"Phone",
				"slug"=>"phone",
				"select"=>"users.phone",
				"resource"=>"User",
				"group_by"=>"phone",
				"filter"=>"like",
				"type"=>"text",
				"sort"=>"users.phone",
				"user_specific_key"=>"phone",
				"user_specific"=>true,
				"user_specific_column"=>"users.phone",
				"user_specific_model"=>"User",
				"filter_key"=>["users.phone"]
			],
			[
				"name"=>"Username",
				"slug"=>"username",
				"select"=>"users.username",
				"resource"=>"User",
				"grpup_by"=>"username",
				"filter"=>"like",
				"type"=>"text",
				"sort"=>"users.username",
				"user_specific"=>true,
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.username",
				"user_specific_key"=>"username",
				"filter_key"=>["users.username"]
			],
			[
				"name"=>"Alternative Email",
				"slug"=>"email2",
				"select"=>"users.email2",
				"resource"=>"User",
				"group_by"=>"email2",
				"filter"=>"like",
				"type"=>"text",
				"sort"=>"users.email2",
				"filter_key"=>["users.email2"]
			],
			[
				"name"=>"National Insurance number",
				"slug"=>"NINumber",
				"select"=>"users.NINumber",
				"resource"=>"User",
				"group_by"=>"NINumber",
				"filter"=>"like",
				"type"=>"text",
				"sort"=>"users.NINumber",
				"filter_key"=>["users.NINumber"]
			],
			[
				"name"=>"ULN",
				"slug"=>"ULN",
				"select"=>"users.ULN",
				"resource"=>"User",
				"grpup_by"=>"ULN",
				"filter"=>"like",
				"type"=>"number",
				"sort"=>"users.ULN",
				"filter_key"=>["users.ULN"]
			],
			[
				"name"=>"%%country%%",
				"slug"=>"country_name",
				"select"=>"countries.name as country_name",
				"resource"=>"User.Country",
				"grpup_by"=>"country_id",
				"multi_select"=>true,
				"filter"=>"in",
				"class"=>Country::class,
				"filter_key"=>"countries.id",
				"type"=>"text",
				"data_key"=>"id",
				"sort"=>"countries.name",
				"user_specific"=>true,
				"user_specific_key"=>"country_id",
				"user_specific_column"=>"users.country_id",
				"user_specific_model"=>"User"
			],
			[
				"name"=>"%%city%%",
				"slug"=>"city_name",
				"select"=>"cities.name as city_name",
				"resource"=>"User.City",
				"group_by"=>"city_id",
				"multi_select"=>true,
				"filter"=>"in",
				"class"=>City::class,
				"filter_key"=>"cities.id",
				"type"=>"text",
				"data_key"=>"id",
				"sort"=>"cities.name",
				"user_specific"=>true,
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.city_id",
				"user_specific_key"=>"city_id"

			],
			[
				"name"=>"%%department%%",
				"slug"=>"department_name",
				"select"=>"departments.name as department_name",
				"resource"=>"User.Department",
				"group_by"=>"department_id",
				"multi_select"=>true,
				"filter"=>"in",
				"class"=>Department::class,
				"filter_key"=>"departments.id",
				"type"=>"text",
				"data_key"=>"id",
				"sort"=>"departments.name",
				"user_specific_key"=>"department_id",
				"user_specific_column"=>"users.department_id",
				"user_specific"=>true,
				"user_specific_model"=>"User"

			],
			[
				"name"=>"Designation",
				"slug"=>"designation_name",
				"select"=>"designations.name as designation_name",
				"resource"=>"User.Designation",
				"group_by"=>"designation_id",
				"type"=>"text",
				"filter"=>"in",
				"filter_key"=>"designations.id",
				"multi_select"=>true,
				"class"=>Designation::class,
				"data_key"=>"id",
				"sort"=>"designations.name",
				"user_specific_model"=>"User",
				"user_specific"=>true,
				"user_specific_column"=>"users.designation_id",
				"user_specific_key"=>"designation_id"
			],
			[
				"name"=>"%%company%%",
				"slug"=>"company_name",
				"select"=>"companies.name as company_name",
				"resource"=>"User.Company",
				"group_by"=>"company_id",
				"type"=>"text",
				"multi_select"=>true,
				"class"=>Company::class,
				"filter"=>"in",
				"filter_key"=>"companies.id",
				"data_key"=>"id",
				"sort"=>"companies.name",
				"user_specific"=>true,
				"user_specific_key"=>"company_id",
				"user_specific_model"=>"User",
				"user_specific_column"=>"users.company_id"
			],
			[
				"name"=>"%%location%%",
				"slug"=>"location_name",
				"select"=>"locations.name as location_name",
				"resource"=>"User.Location",
				"group_by"=>"location_id",
				"type"=>"text",
				"multi_select"=>true,
				"class"=>Location::class,
				"filter_key"=>"locations.id",
				"filter"=>"in",
				"data_key"=>"id",
				"sort"=>"locations.name",
				"user_specific"=>true,
				"user_specific_column"=>"users.location_id",
				"user_specific_model"=>"User",
				"user_specific_key"=>"location_id"
			],
			[
				"name"=>"%%groups%%",
				"slug"=>"groups.name",
				"resource"=>"User.Groups",
				"type"=>"array",
				"filter"=>"in",
				"filter_key"=>"groups.id",
				"data_key"=>"id",
				"multi_select"=>true,
				"class"=>Group::class
			],
			[
				"name"=>"Sex",
				"slug"=>"Sex",
				"select"=>"users.Sex",
				"resource"=>"User",
				"group_by"=>"Sex",
				"type"=>"text",
				"filter"=>"like",
				"filter_key"=>["users.Sex"]
			],
			[
				"name"=>"%%managers%%",
				"slug"=>"managers.fname+lname",
				"resource"=>"User.Managers",
				"type"=>"array",
				"multi_select"=>false,
				"filter"=>"like",
				"filter_key"=>["fname","lname"],
				"user_specific"=>true,
				"user_specific_key"=>"id",
				"user_specific_model"=>"Managers",
				"user_specific_column"=>"manager_users.manager_id"
			],
			[
				"name" => "Sub "."%%department%%",
				"slug" => "sub_departments.department.name",
				"select" => "departments.name as department_name",
				"resource" => "User.SubDepartments.Department", //Relationship
				"group_by" => "department_id",
				"multi_select" => true,
				"filter" => "in",
				"class" => Department::class,
				"conditional_clauses" => [[
					'clause' => 'where',
					'condition' => [
						['parent_id', '!=', null],
					],
				],],
				"filter_key" => "departments.id",
				"type" => "array",
				"data_key" => "id",
				"user_specific_key" => "department_id",
				"user_specific_column" => "users.department_id",
				"user_specific" => true,
				"user_specific_model" => "User"
			],
			[
				"name"=>"Registration Date",
				"slug"=>'user_registration_dt',
				"resource"=>"User",
				"group_by"=>"registration_dt",
				"filter"=>"between",
				"type"=>"date-range",
				"select"=>"users.registration_dt as user_registration_dt",
				"sort"=>"users.registration_dt",
				"filter_key"=> "users.registration_dt"
			],
			[
				"name"=>"Lesson",
				"slug"=>"lesson_name",
				"select"=>"learning_modules.name as lesson_name",
				"resource"=>"learningModule",
				"group_by"=>"learning_module_id",
				"type"=>"text",
				"filter"=>"in",
				"filter_key"=>"learning_modules.id",
				"multi_select"=>true,
				"class"=>LearningModule::class,
				"data_key"=>"id",
				"sort"=>"learning_modules.name"
			],
			[
				"name"=>"Lesson Category",
				"slug"=>"lesson_category_name",
				"select"=>"learning_module_categories.name as lesson_category_name",
				"resource"=>"learningModule.Category",
				"group_by"=>"learning_module_categories.id",
				"type"=>"text",
				"filter"=>"in",
				"filter_key"=>"learning_module_categories.id",
				"multi_select"=>true,
				"class"=>LearningModuleCategory::class,
				"data_key"=>"id",
				"sort"=>"learning_module_categories.name"
			],
			[
				"name"=>"Alert Status",
				"slug"=>"alert_status",
				"resource"=>"user_schedule_waiting_lists",
				"type"=>"status",
				"filter"=>"like",
				"drop_down"=>[
					0=>"Inactive",
					1=>"Active"
				],
				"select"=>"user_schedule_waiting_lists.status as alert_status",
				"sort"=>"user_schedule_waiting_lists.status",
				"filter_key"=>["user_schedule_waiting_lists.status"]
			],
		]
	];

    function __construct(ContainerInterface $containerInterface)
		{
			$showExtraResourceFields_TDG = Tools::getConfig('showExtraResourceFields_TDG');
			$hideLearningProgrammes = Tools::getConfig('HideCustomFieldLearningProgrammes');
			$hideLearningResource = Tools::getConfig('HideCustomFieldLearningResource');
			$hideLessons = Tools::getConfig('HideCustomFieldLessons');

			static $existingColumns;
			static $customFieldColumns;

			if (!isset($existingColumns)) {
				$existingColumns = DB::getSchemaBuilder()->getColumnListing('form_value_views');
			}
			if(!isset($customFieldColumns)){
				$customFieldColumns = DB::getSchemaBuilder()->getColumnListing('custom_field_value_views');
			}

			$this->container = $containerInterface;
			self::$fields['coupons'] [] =[
				"name"=>"Cost saving applied",
				"resource"=>"Coupon",
				"slug"=>"cost_saving_applied",
				"type"=>"number",
				"filter"=>"like",
				"select"=>DB::raw("(coupon_usages.cost-coupon_usages.cost_after_coupon_apply) as cost_saving_applied"),
				"filter_key"=>[DB::raw("(coupon_usages.cost-coupon_usages.cost_after_coupon_apply)")]
			];
			self::$fields['quiz_analysis'] [] = [
				"name" => "Correct",
				"resource" => "learning_result_answers",
				"slug" => "result",
				"type" => "text",
				"filter" => "like",
				"select" => DB::raw("(CASE WHEN learning_result_answers.result = 1 THEN 'Yes' ELSE 'No' END) as result"),
				"sort" => "learning_result_answers.result",
				"filter_key" => ["learning_result_answers.result"]
			];

			$rawFields = cache()->remember('raw_custom_fields', 86400, function () {
				return \Models\Field
					::with('FieldCategory')
					->where('status', 1)
					->where('custom', 1)
					->get()
        			->toArray()
				;
			});




			foreach ($rawFields as  $field) {

				$type=$slug=$resource=null;
				$field_type = $field['type']=="number"?"number":"text";
				$field_type = $field['type']=="date"?"date-range":$field_type;

				$data = [
					"name"=>$field['name'],
					"resource"=>"custom",
					"slug"=>"custom_".$field['slug'],
					"type"=>$field_type,
					"extra"=>true,
					"field"=>$field,
					"select"=>"form_value_views.".$field['slug']." as custom_".$field['slug'],
					"sort"=>"form_value_views.".$field['slug'],
					"filter_key"=>["form_value_views.".$field['slug']],
					"data_key"=>"value"
				];

				if($field['type'] == 'selectbox'){
					$data["multi_select"] = true;
					$data["filter"] = 'in';
					$data["class"]= FieldOption::class;
					$data["filter_key"] = "form_value_views.".$field['slug'];
					$data['data_key']= "value";

				}else{
					$data["filter"] = 'like';
				}

				if($field['type']=="date")
				{
					$data['filter_key']="form_value_views.".str_replace('_custom','',$field['slug']);
					$data['filter']="between";
				}

				if (in_array($field['slug'], $existingColumns)) {
					self::$fields['user_forms'][] = $data;
				}
				if(in_array($field['slug'], $customFieldColumns) && $field['field_category_id']==1 && !empty($field['display_type'])){
                    $other_data = $data;
                    $other_data['resource'] = 'custom_user_fields';
                    $other_data['name'] = $other_data['name']." (custom)";
                    $other_data["select"]="custom_field_value_views.".$field['slug']." as custom_".$field['slug'];
                    $other_data["sort"] = "custom_field_value_views.".$field['slug'];
                    $other_data["filter_key"] = ["custom_field_value_views.".$field['slug']];
                    if($other_data['filter'] == 'between'){
                        $other_data['filter_key'] = "custom_field_value_views.".str_replace('_custom','',$field['slug']);
                    }
                    self::$fields['apprenticeship_standards'][] = $other_data;
                    self::$fields['learning_resources'][]=$other_data;
                }

				$data['filter_key'] = ["field_".$field['slug'].".value"];
				if($field['type'] == 'selectbox'){
					$data["filter_key"] = "field_".$field['slug'].".value";
				}
				if($field['type']=="date")
				{
					$data['filter_key']="field_".$field['slug'].".value";
				}
				$data['resource'] = "fields";
				$data['slug'] = "field_".$field['slug'];
				$data['select'] = "field_".$field['slug'].".value as field_".$field['slug'];
				$data['sort'] = "field_".$field['slug'].".value";
				$category = $field['field_category'];
				if($showExtraResourceFields_TDG){
					if ($field['slug'] == "job_role" || $field['slug'] == "employee_status" || $field['slug'] == "union") {
							self::$fields['learning_resources'][] = $data;
							self::$fields['all_learning'][] = $data;
							self::$fields['apprenticeship_standards'][]=$data;
							self::$fields['schedules'][]=$data;
					}
				}

				if(isset($category)  && isset(self::$category_maping[$category['slug']])){


					if (($hideLearningProgrammes && $category['slug'] == "programme") ||
						($hideLearningResource && $category['slug'] == "learning_resource") ||
						($hideLessons && $category['slug'] == "lesson")) {
						continue;
                    }
                    $data['name'] = $data['name']." (custom)";

					self::$fields[self::$category_maping[$category['slug']]][]=$data;
				}
			}

			foreach(self::$fields as $field_category=>$fields)
			{
				foreach($fields as $key=>$field)
				{
					self::$fields[$field_category][$key]['name']=Templates::translateOrReplace(self::$fields[$field_category][$key]['name']);
				}
			}


			$dateField = Tools::getConfig('refreshCompletedAt') ? 'completed_at' : 'created_at';

			$data = [
					"name" => 'Refreshed At',
					"resource" => "Module",
					"slug" => "refreshed_at",
					"type" => "date-range",
					"filter" => "between",
					"select" => DB::raw("
							CASE
									WHEN learning_results.$dateField IS NOT NULL
											AND learning_modules.refresh = 1
											AND learning_modules.refresh_period IS NOT NULL
											AND learning_modules.refresh_period_type IS NOT NULL
									THEN DATE_FORMAT(
											CASE
													WHEN learning_modules.refresh_period_type = 'day' THEN DATE_ADD(learning_results.$dateField, INTERVAL learning_modules.refresh_period DAY)
													WHEN learning_modules.refresh_period_type = 'month' THEN DATE_ADD(learning_results.$dateField, INTERVAL learning_modules.refresh_period MONTH)
													WHEN learning_modules.refresh_period_type = 'year' THEN DATE_ADD(learning_results.$dateField, INTERVAL learning_modules.refresh_period YEAR)
											END, '%Y-%m-%d'
									)
									ELSE NULL
							END as refreshed_at"
					),
					"sort" => "refreshed_at",
					"filter_key" => DB::raw("
							CASE
									WHEN learning_results.$dateField IS NOT NULL
											AND learning_modules.refresh = 1
											AND learning_modules.refresh_period IS NOT NULL
											AND learning_modules.refresh_period_type IS NOT NULL
									THEN DATE_FORMAT(
											CASE
													WHEN learning_modules.refresh_period_type = 'day' THEN DATE_ADD(learning_results.$dateField, INTERVAL learning_modules.refresh_period DAY)
													WHEN learning_modules.refresh_period_type = 'month' THEN DATE_ADD(learning_results.$dateField, INTERVAL learning_modules.refresh_period MONTH)
													WHEN learning_modules.refresh_period_type = 'year' THEN DATE_ADD(learning_results.$dateField, INTERVAL learning_modules.refresh_period YEAR)
											END, '%Y-%m-%d'
									)
									ELSE NULL
							END"
					)
			];

			// Add to the respective fields
			self::$fields['learning_resources'][] = $data;

			// Adjust and add for 'schedules'
			$data['resource'] = "Resource";
			unset($data['filter']);
			self::$fields['schedules'][] = $data;

		}
		/**
		 * Get api for fetching system configuration
		 * @param  $request
		 * @param  $response
		 * @return object
		 * @api
		 */
		public function fieldList(Request $request, Response $response, $type) {
			$response->getBody()->write(gzencode(json_encode(static::$fields)));
			return $response
				->withHeader('Content-Encoding', 'gzip')
				->withHeader('Content-Type', 'application/json')
			;
		}

		/**
		 * Create Custom Report
		 * @param  $request
		 * @param  $response
		 * @return object
		 * @api
		 */
		public function create(Request $request, Response $response)
		{
				$data = $request->getParsedBody();

        // DB::beginTransaction();
				try {
						if (array_key_exists('report_type', $data) && array_key_exists('report_name', $data)) {
							$filters=isset($data['filters'])?$data['filters']: [""];
							if(!isset($data['user_specific_filter']))
							{
							 $data['user_specific_filter']=null;
							}
								$customReport = CustomReport::create([
										"name" => $data["report_name"],
										"type" => $data["report_type"],
										'filters' => $filters,
										'user_specific_filter' => $data['user_specific_filter']
								]);
								if (array_key_exists("selectedFields", $data) && is_array($data['selectedFields']) && count($data['selectedFields']) > 0) {
										foreach ($data['selectedFields'] as $field) {
												$multi_select=isset($field['multi_select'])?$field['multi_select']:false;

												CustomReportField::create([
														'custom_report_id' => $customReport->id,
														'name' => $field['name'],
														'column_name' => $field['slug'],
														'table_name' => $field['resource'],
														'multi_select' => $multi_select,
														'type' => $field['type']
												]);
										}
								} else {
										throw new Error("Invalid Field selection");
								}
								if($customReport->type=='user_forms' && isset($data['form'])){
									CustomReportController::generateView($customReport,$data['selectedFields'],$data['form']);
								}
								if (array_key_exists('selectedVisibility', $data) && is_array($data['selectedVisibility'])) {
                    foreach ($data['selectedVisibility'] as $key=>$value) {
												foreach ($value as $id) {
														CustomReportPermission::create(['type'=>$key,'type_id'=>$id,'custom_report_id'=>$customReport->id]);
												}
										}
								} else {
										throw new Exception("Invalid set visiblilty field");
								}
						} else {
								throw new Exception("Invalid Report Name or Report type");
						}
            // DB::commit();

            $response->getBody()->write(json_encode(["status"=>true,"message"=>"Custom Report Created",'id'=>$customReport->id]));
            return $response->withHeader('Content-Type', 'application/json')->withStatus(201);

				} catch(Exception $ex) {
            CustomReport::find($customReport->id)->delete();
            CustomReportField::where('custom_report_id', $customReport->id)->delete();
            CustomReportPermission::where('custom_report_id', $customReport->id)->delete();
            // DB::rollBack();
            $response->getBody()->write(json_encode(["status"=>false,"message"=>$ex->getMessage()]));
            return $response->withHeader('Content-Type', 'application/json')->withStatus(422);
				}
		}
		/**
		 * addFilter function add filter for dynamic custom reports
		 * all filters are added based on $fileds filter section value
		 * Currently we only support two type of filter we can add other
		 * filters also if that filter support by mysql
		 *
		 * @param  $query mysql query genrated by generateReport function
		 * @param  $field this is sub array of $fields
		 * @param  $filterValue value given by user
		 * @return boolean|void return false if the $filterValue value is empty or there is no $filter exist for $fields
		 */
		private function addFilter($type,$query,$field,$filterValue){ //Add filter for custom report section
				$field_name = $field['column_name'];  //get where condtion column_name in db we store full path
				$field = $this->getField(self::$fields[$type],$field_name);
			 if(empty($filterValue) && $filterValue!="0")
				{
					return;
				}
		if($type == 'all_learning' && isset($field['models'])) {
						$fieldData = $field['models'][$query->getModel()->getTable()]['filter_key'][$field['slug']];
						if(isset($field['type']) && $field['type'] == 'text' && $field['filter']!='in') {
								if(!empty($fieldData)) {
										$field['filter_key'] = [$fieldData];
								} else {
										$field['filter_key'] = null;
								}
						} else {
								$field['filter_key'] = $fieldData;
						}
		}

		if (!empty($field) && isset($field['slug']) && $type === 'quiz_analysis') {
			if ($field['slug'] === 'result') {
				$normalizedValue = strtolower(trim($filterValue));
				if ($normalizedValue === 'yes') {
					$filterValue = 1;
				} elseif ($normalizedValue === 'no') {
					$filterValue = 0;
				}
			}
		}

		if(!isset($field['filter_key'])){
			return;
		}

				if($field['type']=='date')
				{
					$filterValue = Carbon::parse($filterValue)->format("Y-m-d");
				}
				if(strpos($field_name,'.')!==false)
				{
					$field_name = explode(".",$field_name);
					$field_name = $field_name[count($field_name)-1];
				}
				if($field['filter']=='like') //For like oprator
				{
					if(isset($field['filter_key']) && is_array($field['filter_key']))
						{
						$query->where(function($query)use($field,$filterValue){
						foreach ($field['filter_key'] as  $key=> $fieldname) {
							if($key==0)
							{
							if($filterValue=='NaN')
							{
								$query->whereNull($fieldname);
						}else{
								if(isset($field['filter_raw']))
														{
																$query->whereRaw($fieldname.' like '.' "%'.$filterValue.'%"');
														}else{
																$query->where($fieldname,'like','%'.$filterValue.'%');
														}

							}
							}else
							{
								$query->OrWhere($fieldname,'like','%'.$filterValue.'%');
							}
						}
});
					}else{
						if(isset($field['drop_down'])){
							if(isset($field['filter_key']))
							{
								$field_name = $field['filter_key'];
							}
							$query->where($field_name,$filterValue);
						}else{
								if(!empty($field['filter_key'])) {
										$query->where($field_name,'like','%'.$filterValue.'%');
								}
						}

					}
				}
				elseif($field['filter']=='in')
				{

						$data_key = isset($field['data_key'])?$field['data_key']:$field['filter_key'];
						if($field['filter_key'] == 'key'){

								if(!UserFormValue::where('slug',$field_name)->exists()){
									return $query->where('id', 0);
									// return false;
				}
							$query->whereIn($field_name, collect($filterValue)->pluck($data_key));

			}else{
							$query->whereIn($field['filter_key'],collect($filterValue)->pluck($data_key));
						}

				}elseif($field['filter'] == 'between'){
						if ($field['type'] == 'date-range' && is_array($filterValue) && $filterValue['from'] != null && $filterValue['to'] != null && !empty($field['filter_key'])) {
								$from = Carbon::parse($filterValue['from'])->format("Y-m-d");
								$to = Carbon::parse($filterValue['to'])->format("Y-m-d");
								$query->whereBetween($field['filter_key'], [$from, $to]);
						}
				}


		}
		/**
		 * Common function for generate custom report based on user selection
		 *
		 * @param  $reportData
		 * @return Query
		 */
		public function generateReport($reportData, &$params = false)
    {


			$query = "";
			$filter=[];
			$currentClass = $this;
			$select = [];
			$queriesUn = [];
			$learningResults = $learningResultSelect = $scheduleSelect = $schedules = $standards = $standardsSelect = null;

			if(isset($reportData['id']))
			{
					$this->reportData =  CustomReport::find($reportData['id']);
			}
			$this->reportType = $reportData['type'];

			if($reportData['type']==="user_forms"){
				$selectedFields = [];
				if(!isset($reportData['selectedFields']))
				{
					foreach($reportData['fields'] as $field){
						$field_data = $field;
						if (substr($field['column_name'], 0, strlen('custom_')) === 'custom_') {

							$field_data['field']=Field::where('slug',str_replace('custom_', '', $field['column_name']))->first();
						}
						$selectedFields [] = $field_data;
					}

				}
				else{
					$selectedFields = $reportData['selectedFields'];
				}
				$formId = $this->reportData?$this->reportData->form_id:$reportData['form'];
				$formChildrenIds = \Models\Form::where('parent_id', $formId)->pluck('id')->toArray();
				$formChildrenIds[] = $formId;
				$sql_in_array =  implode(', ', $formChildrenIds);
				$this->reportData = self::generateView($this->reportData,$selectedFields,$sql_in_array,true);
			}

			if(isset($reportData['filters']))
			{
				$filter=$reportData['filters'];
			}

			if(isset($reportData['filter']))
			{
				$filter=$reportData['filter'];
			}

			if ($reportData['type']=='users') {
				$select [] = "users.id";
				$query = User::query()->where('users.status',1);
			}
			elseif($reportData['type']==='schedules')
			{
				$query = ScheduleLink
					::whereIn('schedule_links.type',['users', 'managers','users_queue'])
					->join('users as status_check', 'schedule_links.link_id', '=', 'status_check.id')
					->where('status_check.status',1)
				;

				$select = ["schedule_links.schedule_id", "schedule_links.link_id"];
			}
			elseif($reportData['type']==='learning_resources')
			{
				$query = LearningResult
					::where('learning_results.refreshed',0)
					->join('user_learning_modules',function($query){
						$query
							->on('learning_results.user_id','user_learning_modules.user_id')
							->where('learning_results.learning_module_id', DB::raw('user_learning_modules.learning_module_id'))
							->whereNull('user_learning_modules.deleted_at')
						;
					})
					->join('users as status_check', 'learning_results.user_id', '=', 'status_check.id')
					->where('status_check.status',1)
				;

				$select = ['learning_results.user_id','learning_results.learning_module_id'];
			}
			elseif($reportData['type']=='apprenticeship_standards')
			{
				$query = ApprenticeshipStandardUser::query()->join('users as status_check', 'apprenticeship_standards_users.user_id', '=', 'status_check.id')->where('status_check.status',1);
				$select = ['apprenticeship_standards_users.user_id'];
			} elseif ($reportData['type'] == 'user_forms') {
				$query = UserForm::query()->join('users as status_check', 'user_forms.user_id', '=', 'status_check.id')->where('status_check.status',1);
			} elseif ($reportData['type'] == 'coupons') {
				$query = CouponUsage::query();
			}elseif($reportData['type']=="purchases")
			{
				$query = PurchasePaymentTransaction::query();
			} else if ($reportData['type'] == 'all_learning') {

				$scheduleSelect = [
					'schedule_links.link_id as link_id',
					'schedules.name as name',
					DB::raw("'Event' as type"),
					'schedule_links.completion_status as completion_status',
					DB::raw('CASE
					WHEN schedule_links.completion_status IN ("Completed", "%%event_completion_state_completed%%") THEN schedules.end_date
					ELSE NULL
					END AS completion_date'
									),
					'schedules.duration as duration',
					DB::raw('"event" as field_type'),
					DB::raw("NULL as group_department_code_name"),
					'learning_module_categories.name as learning_category_name',
					DB::raw('NULL as learning_module_id'),
					DB::raw("NULL as delivery_provider_type_name")
				];

				$schedules = ScheduleLink::query()
					->whereIn('schedule_links.type', ['users', 'managers', 'users_queue'])
					->join('schedules', 'schedules.id', '=', 'schedule_links.schedule_id')
					->join('learning_module_categories',function($query){
						$query->on('learning_module_categories.id','=','schedules.category_id')->where('learning_module_categories.status',true);
					})
					->where('schedule_links.status', 1)
					->where('schedules.status', 1)
				;

				if (
					isset($params['page']) &&
					isset($params['nPage'])
				) {
					// $schedules->skip(($params['page'] * $params['nPage']) - $params['nPage']);
					$schedules->take($params['page'] * $params['nPage']);
				}

				if (
					isset($params["sort"]) &&
					isset($params["sort"]["predicate"])
				) {
					$sortBy = str_replace("__", ".", $params["sort"]["predicate"]);
					$schedules->orderBy($sortBy, $params["sort"]["reverse"] ? "DESC" : "ASC");
				}


				$learningResultSelect = [
					'learning_results.user_id as user_id',
					'learning_modules.name as name',
					DB::raw("'Learning Resource' as type"),
					'learning_results.completion_status as completion_status',
					'learning_results.completed_at as completion_date',
					DB::raw('NULL AS duration'),
					DB::raw('"learning_resource" as field_type'),
					DB::raw("learning_module_group_department_codes.name as group_department_code_name"),
					'learning_module_categories.name as learning_category_name',
					'learning_results.learning_module_id',
					'learning_module_delivery_provider_types.name as delivery_provider_type_name'
				];

				$subQuery = LearningResult
					::query()
					->select('learning_results.id')
					->where('refreshed', 0)
					->whereNull('learning_results.deleted_at')
					->join('user_learning_modules', function ($query) {
						$query->on('learning_results.user_id', 'user_learning_modules.user_id')
							->where('learning_results.learning_module_id', DB::raw('user_learning_modules.learning_module_id'))
							->whereNull('user_learning_modules.deleted_at')
						;
					})
				;
				if (
					isset($params['page']) &&
					isset($params['nPage'])
				) {
					// $subQuery->skip(($params['page'] * $params['nPage']) - $params['nPage']);
					$subQuery->take($params['page'] * $params['nPage']);
				}


				// Custom logic for sorting only in subquery, not in main query
				$sub_query_sort = false;
				if (
					isset($params["sort"]) &&
					isset($params["sort"]["predicate"])
				) {
					$sortBy = str_replace("__", ".", $params["sort"]["predicate"]);
					if ($sortBy == 'completion_date') {
						$sortBy = 'completed_at';
						$sub_query_sort = true;
					}
					if ($sortBy == 'completion_status') {
						$sub_query_sort = true;
					}
					if ($sub_query_sort) {
						$subQuery->orderBy($sortBy, $params["sort"]["reverse"] ? "DESC" : "ASC");
					}
				}

				$learningResults = LearningResult
					::where('learning_results.refreshed', 0)
					->joinSub($subQuery, 'limited_lr', function ($join) {
						$join->on('learning_results.id', '=', 'limited_lr.id');
					})

					->join('learning_modules', 'learning_modules.id', '=', 'learning_results.learning_module_id')
					->where('learning_modules.status', 1)
					->leftJoin('learning_module_group_department_codes','learning_modules.group_department_code_id','learning_module_group_department_codes.id')
					->join('learning_module_categories',function($query){
							$query->on('learning_module_categories.id','=','learning_modules.category_id')->where('learning_module_categories.status',true);
						})
					->leftJoin('learning_module_delivery_provider_types','learning_module_delivery_provider_types.id','learning_modules.delivery_provider_type_id')
				;

				if (
					isset($params["sort"]) &&
					isset($params["sort"]["predicate"]) &&
					!$sub_query_sort
				) {
					$sortBy = str_replace("__", ".", $params["sort"]["predicate"]);
					$learningResults->orderBy($sortBy, $params["sort"]["reverse"] ? "DESC" : "ASC");
				}

				$standardsSelect = [
					'apprenticeship_standards_users.user_id as user_id',
					'apprenticeship_standards.name as name',
					DB::raw("'Learning Programme' as type"),
					'apprenticeship_standards_users.completion_status as completion_status',
					'apprenticeship_standards_users.completed_at as completion_date',
					DB::raw('NULL AS duration'),
					DB::raw('"programme" as field_type'),
					DB::raw('NULL as group_department_code_name'),
					'learning_module_categories.name as learning_category_name',
					DB::raw("NULL as learning_module_id"),
					DB::raw("NULL as delivery_provider_type_name")
				];

				$standards = ApprenticeshipStandardUser::query()
					->join('apprenticeship_standards', 'apprenticeship_standards.id', '=', 'apprenticeship_standards_users.standard_id')
					->where('apprenticeship_standards.status', 1)
					->leftJoin('users', 'users.id', '=', 'apprenticeship_standards_users.user_id')
					->join('learning_module_categories',function($query){
						$query->on('learning_module_categories.id','=','apprenticeship_standards.category_id')
							->where('learning_module_categories.status',true);
					})
				;

				if (
					isset($params['page']) &&
					isset($params['nPage'])
				) {
					// $standards->skip(($params['page'] * $params['nPage']) - $params['nPage']);
					$standards->take($params['page'] * $params['nPage']);
				}

				if (
					isset($params["sort"]) &&
					isset($params["sort"]["predicate"])
				) {
					$sortBy = str_replace("__", ".", $params["sort"]["predicate"]);
					$standards->orderBy($sortBy, $params["sort"]["reverse"] ? "DESC" : "ASC");

					unset($params["sort"]);
				}

				if (Tools::getConfig('showExtraResourceFields_TDG')) {
					$schedules = $schedules->leftJoin(DB::raw("(SELECT custom_field_values.value,custom_field_values.type_id,custom_field_values.user_id FROM `custom_field_values` inner join fields on fields.id = custom_field_values.field_id WHERE custom_field_values.type = 'user' and slug = 'union') as field_union"),function($query){
						$query->on('schedule_links.link_id','field_union.type_id');
					});

					$schedules = $schedules->leftJoin(DB::raw("(SELECT custom_field_values.value,custom_field_values.type_id,custom_field_values.user_id FROM `custom_field_values` inner join fields on fields.id = custom_field_values.field_id WHERE custom_field_values.type = 'user' and slug = 'job_role') as field_job_role"),function($query){
						$query->on('schedule_links.link_id','field_job_role.type_id');
					});

					$schedules = $schedules->leftJoin(DB::raw("(SELECT custom_field_values.value,custom_field_values.type_id,custom_field_values.user_id FROM `custom_field_values` inner join fields on fields.id = custom_field_values.field_id WHERE custom_field_values.type = 'user' and slug = 'employee_status') as field_employee_status"),function($query){
						$query->on('schedule_links.link_id','field_employee_status.type_id');
					});

					$standards = $standards->leftJoin(DB::raw("(SELECT custom_field_values.value,custom_field_values.type_id,custom_field_values.user_id FROM `custom_field_values` inner join fields on fields.id = custom_field_values.field_id WHERE custom_field_values.type = 'user' and slug = 'union') as field_union"),function($query){
						$query->on('apprenticeship_standards_users.user_id','field_union.type_id');
					});

					$standards = $standards->leftJoin(DB::raw("(SELECT custom_field_values.value,custom_field_values.type_id,custom_field_values.user_id FROM `custom_field_values` inner join fields on fields.id = custom_field_values.field_id WHERE custom_field_values.type = 'user' and slug = 'job_role') as field_job_role"),function($query){
						$query->on('apprenticeship_standards_users.user_id','field_job_role.type_id');
					});

					$standards = $standards->leftJoin(DB::raw("(SELECT custom_field_values.value,custom_field_values.type_id,custom_field_values.user_id FROM `custom_field_values` inner join fields on fields.id = custom_field_values.field_id WHERE custom_field_values.type = 'user' and slug = 'employee_status') as field_employee_status"),function($query){
						$query->on('apprenticeship_standards_users.user_id','field_employee_status.type_id');
					});

					$learningResults = $learningResults->leftJoin(DB::raw("(SELECT custom_field_values.value,custom_field_values.type_id,custom_field_values.user_id FROM `custom_field_values` inner join fields on fields.id = custom_field_values.field_id WHERE custom_field_values.type = 'user' and slug = 'union') as field_union"),function($query){
						$query->on('learning_results.user_id','field_union.type_id');
					});

					$learningResults = $learningResults->leftJoin(DB::raw("(SELECT custom_field_values.value,custom_field_values.type_id,custom_field_values.user_id FROM `custom_field_values` inner join fields on fields.id = custom_field_values.field_id WHERE custom_field_values.type = 'user' and slug = 'job_role') as field_job_role"),function($query){
						$query->on('learning_results.user_id','field_job_role.type_id');
					});

					$learningResults = $learningResults->leftJoin(DB::raw("(SELECT custom_field_values.value,custom_field_values.type_id,custom_field_values.user_id FROM `custom_field_values` inner join fields on fields.id = custom_field_values.field_id WHERE custom_field_values.type = 'user' and slug = 'employee_status') as field_employee_status"),function($query){
						$query->on('learning_results.user_id','field_employee_status.type_id');
					});

					$scheduleSelect [] = "field_union.value as field_union";
					$scheduleSelect [] = "field_employee_status.value as field_employee_status";
					$scheduleSelect [] = "field_union.value as field_job_role";
					$learningResultSelect [] = "field_union.value as field_union";
					$learningResultSelect [] = "field_employee_status.value as field_employee_status";
					$learningResultSelect [] = "field_union.value as field_job_role";
					$standardsSelect [] = "field_union.value as field_union";
					$standardsSelect [] = "field_employee_status.value as field_employee_status";
					$standardsSelect [] = "field_union.value as field_job_role";

				}

				$queriesUn = [
					['query' => &$schedules, 'select' => &$scheduleSelect],
					['query' => &$learningResults, 'select' => &$learningResultSelect],
					['query' => &$standards, 'select' => &$standardsSelect]
				];

			}
			else if ($reportData['type'] == 'quiz_analysis') {
				$query = LearningResultAnswer::query();
            			$select = ['learning_result_answers.user_id'];
			}elseif($reportData['type'] == 'user_schedule_waiting_lists'){
		            $query = UserScheduleWaitingList::query();
		        }

			foreach ($reportData['fields'] as  $value) {
				$value = $this->getField(self::$fields[$reportData['type']],$value['column_name']);
				$value['column_name']=$value['slug'];
				$value['table_name']=$value['resource'];

				if ($reportData['type'] == 'all_learning') {
						$query = $this->generateAllLearningReport($reportData, $value, $queriesUn, $filter, $query,$value);
				} else {
					if ($value['table_name'] != $reportData['type']) {
						if ($value['type'] != 'array') {
							if(isset($value['selectRaw'])){
								$select[] = DB::raw($value['select']);
							}else{
								$select[] = $value['select'];
							}
							$query = $this->convertToEagerLoadingToJoin($query,$value['table_name'],null,$value['slug'],$value);
							if(isset($filter[$value['column_name']])) {
								$this->addFilter($reportData['type'],$query,$value,$filter[$value['column_name']]);
							}
										} else {
												$query->with([$value['table_name'] => function ($sub_query) use ($value) {
								if (isset($value['fetch_only_enabled']) && $value['fetch_only_enabled']) {
									$sub_query->where('status', 1);
								}
							}]);

							if(isset($filter[$value['column_name']]) && (!empty($filter[$value['column_name']]) || $filter[$value['column_name']]=='0' )){
								$query->whereHas($value['table_name'],function($sub_query)use($currentClass,$filter,$value,$reportData){
										$currentClass->addFilter($reportData['type'], $sub_query, $value, $filter[$value['column_name']]);
								});
							}
						}
					} else {
						$select[] = $value['select'];
						if (isset($filter[$value['column_name']])) {
							$this->addFilter($reportData['type'], $query, $value, $filter[$value['column_name']]);
						}
					}
				}
				}
        if(!$this->checkUserRole('admin'))
    {
    if($this->checkUserRole('manager')){
        if($reportData['type'] == 'users') {
            $reportData['custom_user_filter'] = 'managers.fname+lname';
        }elseif($reportData['type'] == 'schedules') {
            $reportData['custom_user_filter'] = 'user.managers.fname+lname';
        }elseif($reportData['type'] == 'learning_resources') {
            $reportData['custom_user_filter'] = 'user.managers.fname+lname';
        }elseif($reportData['type'] == 'apprenticeship_standards') {
            $reportData['custom_user_filter'] = 'user.managers.fname+lname';
        }elseif($reportData['type'] == 'user_forms') {
            $reportData['custom_user_filter'] = 'user.managers.fname+lname';
        }elseif($reportData['type'] == 'all_learning'){
            $reportData['custom_user_filter'] = 'user.managers.fname+lname';
        }elseif($reportData['type'] == 'purchases') {
            $query->leftjoin('manager_users','manager_users.user_id','=','purchase_payment_transactions.user_id')
                  ->where('manager_users.manager_id', Auth::getUserId());
        }elseif($reportData['type'] == 'coupons') {
            $reportData['custom_user_filter'] = 'user.managers.fname+lname';
        }elseif($reportData['type'] == 'quiz_analysis') {
            $reportData['custom_user_filter'] = 'user.managers.fname+lname';
        }
    }elseif($this->checkUserRole('learner')){
        $user = Auth::getUser();
        if($reportData['type'] == 'users') {
            $query->where('users.id', $user->id);
        }elseif($reportData['type'] == 'schedules') {
            $reportData['custom_user_filter'] = 'user_id';
        }elseif($reportData['type'] == 'learning_resources') {
            $reportData['custom_user_filter'] = 'user_id';
        }elseif($reportData['type'] == 'apprenticeship_standards') {
            $reportData['custom_user_filter'] = 'user_id';
        }elseif($reportData['type'] == 'user_forms') {
            $reportData['custom_user_filter'] = 'user_id';
        }elseif($reportData['type'] == 'all_learning'){
            $reportData['custom_user_filter'] = 'user_id';
        }elseif($reportData['type'] == 'purchases') {
            $query->where('purchase_payment_transactions.user_id', $user->id);
        }elseif($reportData['type'] == 'coupons') {
            $reportData['custom_user_filter'] = 'user_id';
        }elseif($reportData['type'] == 'quiz_analysis') {
            $reportData['custom_user_filter'] = 'user_id';
        }
    }
}

// user specific filters
if(isset($reportData['custom_user_filter']) && !$this->checkUserRole('admin'))
{
    if($reportData['type'] == 'all_learning') {
        $this->filterUserSpecificAllLearningReport($reportData, $queriesUn);
    } else {
        $field = $this->getField(self::$fields[$reportData['type']], $reportData['custom_user_filter']);
        if ($field) {
            $data = [];
            if (
                isset($field['user_specific_model']) &&
                $field['user_specific_model'] == 'User'
            ) {
                $data = Auth::getUser();
            }

            if (
                isset($field['user_specific_model']) &&
                $field['user_specific_model'] == 'Managers'
            ) {
                $user = Auth::getUser();
                $query->whereHas($field['resource'], function ($sub_query) use ($field, $user) {
                    $sub_query->where($field['user_specific_column'], $user->id);
                });
            }

            if (isset($data[$field['user_specific_key']])) {
                if ($reportData['type'] != $field['resource']) {
                    $query = $this->convertToEagerLoadingToJoin($query, $field['resource']);
                }

                if (
                    !empty($field['user_specific_column']) &&
                    isset($field['user_specific_key'], $data[$field['user_specific_key']])
                ) {
                    $query->where($field['user_specific_column'], $data[$field['user_specific_key']]);
                }
            }
        }
    }
}
			// user specific filters
				if(isset($reportData['user_specific_filter']) &&  !$this->checkUserRole('admin') )
			{
				if($reportData['type'] == 'all_learning') {
					$this->filterUserSpecificAllLearningReport($reportData, $queriesUn);
				} else {
					$field = $this->getField(self::$fields[$reportData['type']],$reportData['user_specific_filter']);
					if ($field) {

						$data = [];
						if(
							isset($field['user_specific_model']) &&
							$field['user_specific_model']=='User'
						)
						{
							$data =Auth::getUser();
						}

						if(
							isset($field['user_specific_model']) &&
							$field['user_specific_model']=='Managers'
						){
							$user = Auth::getUser();
							$query->whereHas($field['resource'],function($sub_query)use($field,$user){
								$sub_query->where($field['user_specific_column'],$user->id);
							});
						}

						if(isset($data[$field['user_specific_key']]))
						{
							if($reportData['type']!=$field['resource'])
							{
								$query = $this->convertToEagerLoadingToJoin($query,$field['resource']);
											}
							if (!empty($field['user_specific_column']) && isset($field['user_specific_key'], $data[$field['user_specific_key']])) {
								$query->where($field['user_specific_column'], $data[$field['user_specific_key']]);
							}
						}
					}
				}
			}

			// all learning report
			if($reportData['type'] == 'all_learning') {
				$learningResults = $learningResults->select($learningResultSelect);
				$schedules = $schedules->select($scheduleSelect);
				$standards = $standards->select($standardsSelect);
				if(isset($filter['type'])) {
					switch ($filter['type']) {
						case 'events': $query = $schedules;
							break;
						case 'learning_results': $query = $learningResults;
							break;
						case 'standards': $query = $standards;
							break;
						default:  $query = $schedules
							->unionall($learningResults)
							->unionall($standards);
					}

					if(isset($filter['group_department_code_name']) || (isset($filter['delivery_provider_type_name']) && count($filter['delivery_provider_type_name'])>0) || (isset($filter['module.target_catalogues.name']) && count($filter['module.target_catalogues.name'])>0)){
						$query = $learningResults;
					}
				} else {
					$query = $schedules
						->unionall($learningResults)
						->unionall($standards);

					if(isset($filter['group_department_code_name']) || (isset($filter['delivery_provider_type_name']) && count($filter['delivery_provider_type_name'])>0) || (isset($filter['module.target_catalogues.name']) && count($filter['module.target_catalogues.name'])>0)){
						$query = $learningResults;
					}
				}
			}

			if(empty($select) && $reportData['type'] != 'all_learning'){
					$select[] = "*";
			}

			if($reportData['type'] != 'all_learning') {
					$query->select($select);
		}
			return $query;
    }
    /**
     * @param APP\Controllers\type
     * @return string|void
     */
    public function checkUserRole($type){
        $user = Auth::getUser();
        $role = $user->role;
        $shadowRole = $user->shadowrole;
        if(is_object($shadowRole)){
        return $this->checkRole($shadowRole,$type);
        }else{
            return $this->checkRole($role,$type);
        }
    }
    public function checkRole($role,$type){
        if($role->is_admin || $role->access_all_learners){
                return 'admin' == $type;
            }elseif($role->is_manager){
                return 'manager' == $type;
            }else{
                return 'learner' == $type;
            }
    }
		public function filterUserSpecificAllLearningReport($reportData, $queriesList)
		{
				$field = $this->getField(self::$fields[$reportData['type']], $reportData['user_specific_filter']);
				$data = [];
				if ($field['user_specific_model'] == 'User') {
						$data = Auth::getUser();
				}

				if ($field['user_specific_model'] == 'Managers') {
						$user = Auth::getUser();
						foreach ($queriesList as $query) {
								$query = $query['query'];
								$query->whereHas($field['resource'], function ($sub_query) use ($field, $user) {
										$sub_query->where($field['user_specific_column'], $user->id);
								});
						}
				}

				if (isset($data[$field['user_specific_key']])) {
						foreach ($queriesList as $query) {
								$query = $query['query'];
								if ($reportData['type'] != $field['resource']) {
										$query = $this->convertToEagerLoadingToJoin($query, $field['resource']);
								}
								$query->where($field['user_specific_column'], $data[$field['user_specific_key']]);
						}
				}
		}

		public function generateAllLearningReport($reportData, $fieldData, $queriesList, $filter, $query,$value)
		{
				$currentClass = $this;
				if ($fieldData['table_name'] != $reportData['type']) {
						if ($fieldData['type'] != 'array') {
								foreach ($queriesList as $query) {
										$query['select'][] = $fieldData['select'];
										$query = $query['query'];
										$query = $this->convertToEagerLoadingToJoin($query, $fieldData['table_name'],null,null,$value);
										if (isset($filter[$fieldData['column_name']])) {
												$this->addFilter($reportData['type'], $query, $fieldData, $filter[$fieldData['column_name']]);
										}
								}
						} else {
				foreach ($queriesList as $query) {
					$query = $query['query'];
										if($fieldData['slug']=="module.target_catalogues.name"){
										if(get_class($query->getModel())!="Models\LearningResult"){ //this  condition need some resources so only need to check if the slug match
												continue;
											}
										}
										$query->with($fieldData['table_name']);
										if (isset($filter[$fieldData['column_name']]) && (!empty($filter[$fieldData['column_name']]) || $filter[$fieldData['column_name']] == '0')) {
												$query->whereHas($fieldData['table_name'], function ($sub_query) use ($currentClass, $filter, $fieldData, $reportData) {
														$currentClass->addFilter($reportData['type'], $sub_query, $fieldData, $filter[$fieldData['column_name']]);
												});
										}
						}
						}
				} else {
						if (isset($fieldData['select'])) {
								$select[] = $fieldData['select'];
						}
						if (isset($filter[$fieldData['column_name']])) {
								$filterable = $fieldData['filterable'] ?? true;

								if ($filterable) {
										foreach ($queriesList as $query) {
												$query = $query['query'];
												$this->addFilter($reportData['type'], $query, $fieldData, $filter[$fieldData['column_name']]);
										}
								}
						}
				}
				return $query;
		}

    public function convertToEagerLoadingToJoin($query,$relationship,$related=null,$field=null,$field_info=null){

		$relationshipParts = explode('.', $relationship); // split the relationship into parts
        $relationshipName = array_shift($relationshipParts);// get the first part as the relationship name
        if(!$this->hasDuplicateJoin($query,'ilr_learning_deliveries') && $relationshipName == 'ilrLearningDeliveries'){
                $query->leftJoin('ilr_learning_deliveries',function($query){
                    $query->on('users.id','ilr_learning_deliveries.user_id')->where('ilr_learning_deliveries.AimType',1);
                });
                return $query;
        }
        if (!$this->hasDuplicateJoin($query, 'ilr_learner_employment_statuses') && $relationshipName == 'employmentStatus') {
            $query->leftJoin(
                'ilr_learner_employment_statuses',
                function ($join) {
                    $join->on('users.id', '=', 'ilr_learner_employment_statuses.user_id')
                        ->where(function ($q) {
                            $q->whereNotNull('ilr_learner_employment_statuses.EmpStat')
                                ->orWhereNotNull('ilr_learner_employment_statuses.DateEmpStatApp')
                                ->orWhere('ilr_learner_employment_statuses.EmpId', '!=', '')
                                ->orWhere('ilr_learner_employment_statuses.AgreeId', '!=', '')
                                ->orWhere('ilr_learner_employment_statuses.EmploymentStatusMonitoring', '!=', '[]');
                        });
                }
            );
            return $query;
        }
        if(!$this->hasDuplicateJoin($query,'ilr_user_prior_attainment') && $relationshipName == 'priorAttainment'){
            $query->leftJoin('ilr_user_prior_attainment',function($query){
                $query->on('ilr_user_prior_attainment.user_id','users.id')->whereRaw("ilr_user_prior_attainment.DateLevelApp =  (
        SELECT MAX(ilr_user_prior_attainment.DateLevelApp)
        FROM ilr_user_prior_attainment
        WHERE ilr_user_prior_attainment.user_id = users.id
    )");
            })->leftJoin('ilr_user_prior_attainment_level',function($query){
                $query->on('ilr_user_prior_attainment_level.value','ilr_user_prior_attainment.PriorLevel');
            });
            return $query;
        }
        if($query->getModel() instanceof ApprenticeshipStandardUser && !$this->hasDuplicateJoin($query,'custom_field_value_views') && $relationshipName == 'custom_user_fields'){
            $query->leftJoin('custom_field_value_views',function($join)use($query){
                $join->on('custom_field_value_views.type_id',$query->getModel()->getTable().'.user_id')->where('custom_field_value_views.type','user');
            });
            return $query;
        }
        if($query->getModel() instanceof LearningResult && !$this->hasDuplicateJoin($query,'custom_field_value_views') && $relationshipName == 'custom_user_fields'){
            $query->leftJoin('custom_field_value_views',function($join)use($query){
                $join->on('custom_field_value_views.type_id',$query->getModel()->getTable().'.user_id')->where('custom_field_value_views.type','user');
            });
            return $query;
        }
        if($relationshipName == 'custom_user_fields'){
            return $query;
        }

        if(!$this->hasDuplicateJoin($query,'lldd_health_problems') && $relationshipName == 'llddHealthProblems'){
            $query->leftJoin('lldd_health_problems',function($query){
                $query->on('lldd_health_problems.id','users.LLDDHealthProb');
            });
            return $query;
        }
        if(!$this->hasDuplicateJoin($query,'lldd_health_problems_categories') && $relationshipName == 'llddHealthProblemEntityDefinition'){
            $query->leftJoin(DB::raw("JSON_TABLE(users.LLDDandHealthProblem, '$[*]'
            COLUMNS (
                LLDDCat INT PATH '$.LLDDCat',
                PrimaryLLDD VARCHAR(10) PATH '$.PrimaryLLDD'
            )
                ) AS json_ldd"),function($join){
                    $join->on(DB::raw("json_ldd.PrimaryLLDD"), '=', DB::raw("'1'"));
                })->leftJoin('lldd_health_problems_categories',function($query){
                    $query->on('lldd_health_problems_categories.id','json_ldd.LLDDCat');
                });
            return $query;
        }
        if (!isset($this->rawJoins['latest_progression']) && ($relationshipName == 'learnerDestinationAndProgressionOutcome' || $relationshipName=='learnerDestinationProgressionOutcomeType' || $relationshipName == 'progressionOutcomeCode')) {
        $query->leftJoin(DB::raw('
        (
            SELECT idap.id, idap.user_id, idap.OutType, idap.OutCode,idap.OutStartDate,idap.OutEndDate,idap.OutCollDate,idap.status
            FROM ilr_learner_destination_and_progressions AS idap
            INNER JOIN (
            SELECT user_id, MAX(OutStartDate) AS latest_date
            FROM ilr_learner_destination_and_progressions
            GROUP BY user_id
            ) AS latest_idap
            ON idap.user_id = latest_idap.user_id
            AND idap.OutStartDate = latest_idap.latest_date
            ) AS latest_progression
        '), function ($join) {
                $join->on('users.id', '=', 'latest_progression.user_id');
            });
           $this->rawJoins['latest_progression'] = true;
        }
        if($relationshipName == 'learnerDestinationAndProgressionOutcome'){
            return $query;
        }
        if(!$this->hasDuplicateJoin($query,'ilr_progression_outcome_types') && $relationshipName == 'learnerDestinationProgressionOutcomeType'){
            $query->leftJoin('ilr_progression_outcome_types',function($query){
                $query->on('ilr_progression_outcome_types.value','latest_progression.OutType');
            });
        }
        if($relationshipName == 'progressionOutcomeCode' && !$this->hasDuplicateJoin($query,'ilr_progression_outcome_codes')){
            $query->leftJoin('ilr_progression_outcome_codes',function($query){
                $query->on('ilr_progression_outcome_codes.id','latest_progression.OutCode');
            });
        }
        if($relationshipName == 'progressionOutcomeCode' || $relationshipName == 'learnerDestinationProgressionOutcomeType'){
            return $query;
        }
        if (!$this->hasDuplicateJoin($query,'ilr_learning_deliveries') && ($relationshipName == 'aimTypes' || $relationshipName =="ilrLearningDeliveries" || $relationshipName == 'funcdingModel' || $relationshipName == 'programmeType' || $relationshipName=="ilrCompletionStatuses" | $relationshipName=="ilroutcomes" || $relationshipName == "WithdrawReason" || $relationshipName=="TailoredLearningOutcome" || $relationshipName=="ilrFinancialRecordTypes" || $relationshipName=="ilrFinancialRecordCodes" || $relationshipName == "ilrFinancialRecords") ) {
            $query->leftJoin(DB::raw('(
        SELECT user_id, MAX(AimSeqNumber) as max_aim_seq
        FROM ilr_learning_deliveries
        WHERE AimType = 1
        GROUP BY user_id
    ) as max_aims'), 'max_aims.user_id', '=', 'users.id')
                ->leftJoin('ilr_learning_deliveries', function ($join) {
                    $join->on('ilr_learning_deliveries.user_id', '=', 'users.id')
                        ->on('ilr_learning_deliveries.AimSeqNumber', '=', 'max_aims.max_aim_seq')
                        ->where('ilr_learning_deliveries.AimType', '=', 1);
                });
        }
        if($relationshipName == 'aimTypes'){
            $query->leftJoin('ilr_learning_delivery_aim_types',function($query){
                $query->on('ilr_learning_delivery_aim_types.id','ilr_learning_deliveries.AimType');
            });
            return $query;
        }
        if($relationshipName == 'funcdingModel'){
            $query->leftJoin('ilr_learning_delivery_funding_models',function($query){
                $query->on('ilr_learning_delivery_funding_models.id','ilr_learning_deliveries.FundModel');
            });
            return $query;
        }
        if($relationshipName == "programmeType"){
            $query->leftJoin('ilr_learning_delivery_programme_types',function($query){
                $query->on('ilr_learning_delivery_programme_types.id','ilr_learning_deliveries.ProgType');
            });
            return $query;
        }
        if($relationshipName == "ilrCompletionStatuses"){
            $query->leftJoin('ilr_learning_delivery_completion_status',function($query){
                $query->on('ilr_learning_delivery_completion_status.id','ilr_learning_deliveries.CompStatus');
            });
            return $query;
        }
        if($relationshipName == "ilroutcomes"){
            $query->leftJoin('ilr_learning_delivery_outcomes',function($query){
                $query->on('ilr_learning_delivery_outcomes.id','ilr_learning_deliveries.Outcome');
            });
            return $query;
        }
        if($relationshipName == "WithdrawReason"){
            $query->leftJoin('ilr_learning_delivery_withdraw_reasons',function($query){
                $query->on('ilr_learning_delivery_withdraw_reasons.value','ilr_learning_deliveries.WithdrawReason');
            });
            return $query;
        }
        if($relationshipName == "TailoredLearningOutcome"){
            $query->leftJoin('ilr_learning_delivery_tailored_learning_outcomes',function($query){
                $query->on('ilr_learning_delivery_tailored_learning_outcomes.id','ilr_learning_deliveries.TLOut');
            });
            return $query;
        }
        if(!$this->hasDuplicateJoin($query,'ilr_learning_delivery_financial_records')  && ($relationshipName == "ilrFinancialRecordTypes" || $relationshipName =="ilrFinancialRecordCodes" || $relationshipName == "ilrFinancialRecords") ){
           $query->leftJoin(DB::raw('(
        SELECT  ilr_learning_delivery_id,MAX(AFinDate) as latest_afin_date
        FROM ilr_learning_delivery_financial_records
        GROUP BY ilr_learning_delivery_id
    ) as latest_records'), 'latest_records.ilr_learning_delivery_id', '=', 'ilr_learning_deliveries.id')
    ->leftJoin('ilr_learning_delivery_financial_records', function ($join) {
                    $join->on('ilr_learning_delivery_financial_records.AFinDate', 'latest_records.latest_afin_date');
    });
        }
        if($relationshipName == "ilrFinancialRecordTypes"){
            $query->leftJoin('ilr_learning_delivery_financial_record_types',function($query){
                $query->on('ilr_learning_delivery_financial_record_types.code','ilr_learning_delivery_financial_records.AFinType');
            });
            return $query;
        }
        if($relationshipName == "ilrFinancialRecordCodes"){
            $query->leftJoin('ilr_learning_delivery_financial_record_codes',function($query){
                $query->on('ilr_learning_delivery_financial_record_codes.id','ilr_learning_delivery_financial_records.AFinCode');
            });
            return $query;
        }


            if($relationship=="fields"){
				$type = array_search($this->reportType,self::$category_maping);
					$slug = str_replace("field_","",$field_info['slug']);
					$field = Field::where('slug',$slug)->first();
			$category = $field->FieldCategory;
			if($this->reportType=='all_learning')
			{
				return $query;
            }
						if (($type == "learning_resource"    || $this->reportType=='apprenticeship_standards' || $this->reportType=='schedules') && ($field->slug == "job_role" || $field->slug == "employee_status" || $field->slug == "union")) {
				$type = "user";
				$field_key = 'learning_results.user_id';
				if($this->reportType=="apprenticeship_standards"){
					$field_key = "apprenticeship_standards_users.user_id";
				}elseif($this->reportType=='schedules'){
					$field_key = "schedule_links.link_id";
				}
								if(!$this->hasDuplicateJoin($query,'users')) {
										$query->leftjoin('users', function ($query)use($field_key) {
												$query->on('users.id', $field_key);
										});
								}
					}
					$type = $type=="event" && $category->slug=="venue" ? "venue":$type;
					$type = $type=='learning_resource' && $category->slug=='lesson'?'lesson':$type;
					$rawQuery = "(SELECT custom_field_values.value,custom_field_values.type_id,custom_field_values.user_id FROM `custom_field_values` inner join fields on fields.id = custom_field_values.field_id WHERE custom_field_values.type = '$type' and slug = '$slug') as ".$field_info['slug'];
					if($category->slug=="venue"){
						 if(!$this->hasDuplicateJoin($query,'table_extensions as venu_extention')){
							 $query->leftjoin('table_extensions as venu_extention',function($query){
								 $query->on('venu_extention.table_id','schedule_links.schedule_id');
								 $query->where('venu_extention.table','schedules');
						});
						$query->leftjoin('venues',function($query){
							$query->on('venues.id','venu_extention.value');
							$query->where('venu_extention.name','venue_id');
						});
					}
					}
					$query->leftJoin(DB::raw($rawQuery),function($query)use($type,$field_info,$category){
					if($type=="learning_resource" || $type=='lesson')
					{
						$query->on("learning_results.learning_module_id","=",$field_info['slug'].".type_id");
					}
					if($type=="event"){
							$query->on("schedule_links.schedule_id","=",$field_info['slug'].".type_id");
					}
					if($type=="venue"){
							$query->on('venues.id','=',$field_info['slug'].".type_id");
					}
					if($type=="user")
					{
						$query->on("users.id","=",$field_info['slug'].".type_id");
					}
					if($type=="programme"){
						$query->on("apprenticeship_standards_users.standard_id","=",$field_info['slug'].".type_id");
					}
					});
					return $query;
				}
				if($relationship==="VenueDeatils"){
					if(!$this->hasDuplicateJoin($query,'table_extensions as venu_extention')){
						$query->leftjoin('table_extensions as venu_extention',function($query){
							$query->on('venu_extention.table_id','schedule_links.schedule_id');
							$query->where('venu_extention.table','schedules');
						});
						$query->leftjoin('venues',function($query){
							$query->on('venues.id','venu_extention.value');
							$query->where('venu_extention.name','venue_id');
						});
					}
				}
				if($relationship==="purchases_data")
				{
						if($this->hasDuplicateJoin($query,'users')){
								return $query;
						}
						$query->leftjoin('schedules',function($join){
								$join->on('schedules.id','purchase_payment_transactions.type_id')->where('purchase_payment_transactions.type','schedules');
						})->leftjoin('learning_modules',function($join){
								$join->on('learning_modules.id','purchase_payment_transactions.type_id')->where('purchase_payment_transactions.type','learning_modules');
						})->leftjoin('learning_providers', function ($join) {
								$join->on('learning_modules.provider_id', 'learning_providers.id');
						})->leftjoin('users',function($join){
								$join->on('users.id','purchase_payment_transactions.user_id');
						})->leftjoin('companies',function($join){
								$join->on('users.company_id','companies.id');
						});
						return $query;
				}

				if($relationship === 'Resource') {
						if ($this->hasDuplicateJoin($query, 'learning_results as base_value')) {
								return $query;
						}
						$tableName = $query->getModel()->getTable();
						if($tableName == 'schedule_links') {
                // $query->orWhere(function ($query) {
                //     $query->whereIn('schedule_links.type', ['lesson']);
                // });
								$builder = $query->getQuery();
								$joins = $builder->joins;
								$tables = [];
								if(!empty($joins)) {
										foreach ($joins as $join) {
												$table = $join->table;
												$tables[] = $table;
										}
										if(in_array('schedule_links as lessons', $tables)) {
												return $query->leftjoin('learning_results', "learning_results.learning_module_id", "learning_modules.id");
										}
								}

								if ($this->hasDuplicateJoin($query, 'learning_modules as base_value')) {
										return $query;
								}

								return $query->leftjoin('learning_modules', function ($join) {
										$join->on('schedule_links.link_id', '=', 'learning_modules.id')
												->where('schedule_links.type', '=', 'lesson')
												->where('schedule_links.status', '=', true);
								})
										->leftjoin('learning_results', "learning_results.learning_module_id", "learning_modules.id");


						}
				}

			if($relationship==='CustomEvent' || $relationship === 'CustomProgramme') //Form assigned and unassigned event name
			{
				if($this->hasDuplicateJoin($query,'user_custom_form_values as base_value'))
				{
					return $query;
				}
				$query->leftjoin('user_custom_form_values as base_value',"base_value.id","user_forms.user_custom_form_value_id")->leftjoin('schedules as custom_schedule',function($join){
					$join->where(function($query){
						$query->where(DB::raw("JSON_EXTRACT(`base_value`.`values`,'$.assigned_event_fields')"),DB::raw("custom_schedule.id"))
						->orWhere(DB::raw("JSON_EXTRACT(`base_value`.`values`,'$.unassigned_event_fields')"),DB::raw("custom_schedule.id"));
					});
				})
				->leftjoin(DB::raw("(SELECT form_fields.form_id,form_fields.is_assigned FROM form_fields JOIN fields ON fields.id = form_fields.field_id where fields.field_category_id=2  group by form_fields.form_id) as custom_form_fields"),function($join){
					$join->on('custom_form_fields.form_id','user_forms.form_id');
				})
				->leftjoin(DB::raw("(SELECT form_fields.form_id,form_fields.is_assigned FROM form_fields JOIN fields ON fields.id = form_fields.field_id where fields.field_category_id=3  group by form_fields.form_id) as custom_form_fields_programme"),function($join){
					$join->on('custom_form_fields_programme.form_id','user_forms.form_id');
				})

			 ->leftJoin("apprenticeship_standards as custom_standard",function($join){
					$join->where(function($query){
					$query->where(DB::raw("JSON_EXTRACT(`base_value`.`values`,'$.assigned_programme_fields')"),DB::raw("custom_standard.id"))
				->orWhere(DB::raw("JSON_EXTRACT(`base_value`.`values`,'$.unassigned_programme_fields')"),DB::raw("custom_standard.id"));
				});
				});
				return $query;
			}else if($relationship == "career_path_programme"){
					//FIXME: need to optimise the code
					$query = DB::table('apprenticeship_standards')
							->select(
									'career_paths.id',
									'career_paths.name',
									'apprenticeship_standards.id',
									'apprenticeship_standards.name',
									'designations.id',
									'designations.name',
									'apprenticeship_issues.name',
									'learning_modules.name'
							)
							->rightJoin('apprenticeship_designations', 'apprenticeship_standards.id', '=', 'apprenticeship_designations.standard_id')
							->leftJoin('designations', 'apprenticeship_designations.designation_id', '=', 'designations.id')
							->leftJoin('career_path_designations', 'designations.id', '=', 'career_path_designations.designation_id')
							->rightJoin('career_paths', 'career_path_designations.career_path_id', '=', 'career_paths.id')
							->leftJoin('apprenticeship_issue_categories', function ($join) {
									$join->on('apprenticeship_issue_categories.standard_id', '=', 'apprenticeship_standards.id')->where('apprenticeship_issue_categories.status', '1');
							})->leftJoin('apprenticeship_issues', 'apprenticeship_issues.issue_category_id', '=', 'apprenticeship_issue_categories.id')
							->leftJoin('apprenticeship_issues_learning_modules', 'apprenticeship_issues.id', '=', 'apprenticeship_issues_learning_modules.apprenticeship_issues_id')
							->leftJoin('learning_modules', 'apprenticeship_issues_learning_modules.learning_modules_id', '=', 'learning_modules.id');

					return $query;
			}
			else if ($relationship == "learning_result_answers") {
				return $query;
			}

				if(strpos($relationship,"customUser") !== false){
						$customField = str_replace("custom_", "", $field);
						if($relationship!="customUser")
						{
								if(!$this->hasDuplicateJoin($query,'users'))
								{
										if($query->getQuery()->from=="apprenticeship_standards_users")
										{
												$query->leftjoin('users',function($query){
														$query->on('users.id','apprenticeship_standards_users.user_id');
												});
										}else{
												$query->leftjoin('users',function($query){
														$query->on('schedule_links.link_id','users.id');
												});
										}
								}
						}
						$query->leftJoin(DB::raw("(SELECT user_form_values.value,user_forms.user_id from user_form_values JOIN user_forms ON user_forms.id = user_form_values.user_form_id join users on users.id=user_forms.user_id where user_form_values.slug='$customField'   ORDER BY user_form_values.updated_at DESC LIMIT 1 ) as ".$field),function($query)use($field){
								$query->on($field.".user_id","=",DB::raw("`users`.`id`"));
						});
						return $query;
				//  if(! $this->hasDuplicateJoin($query,'user_custom_forms as ucf')){
					//    $query->leftJoin('user_custom_forms as ucf');
					//}
			}
			if($related)
			{
				$relationshipMethod = $related->getRelation($relationshipName); // get the relationship method

			}else
			{
				$relationshipMethod = $query->getRelation($relationshipName); // get the relationship method
			}
			$relatedTable = $relationshipMethod->getRelated()->getTable();
			$custom_view_name =  ($this->reportData && $this->reportData->custom_view)?$this->reportData->custom_view:'form_value_views';
			$custom_view_name = $custom_view_name.' as form_value_views';
			if($relatedTable=='form_value_views'){
				$relatedTable = $custom_view_name;
			}
			if(!$this->hasDuplicateJoin($query,$relatedTable))
			{
			$relatedKey = $relationshipMethod->getQualifiedForeignKeyName(); // get the qualified foreign key name
				if($relatedTable==$custom_view_name)
				{
				 $query->join($custom_view_name, "user_forms.id", '=', "form_value_views.user_form_id"); // add the join clause
				}elseif($relationship =="Lesson")
				{
					$query->leftJoin('schedule_links as lessons',function($query){
						$query->on('lessons.schedule_id','schedules.id')->where('lessons.type','lesson')->join('learning_modules','lessons.link_id','learning_modules.id');
					});
				}elseif($relationshipName == 'latestAccess')
				{
					$query->leftJoin('learning_module_last_access',function($query){
						$query->on('learning_module_last_access.learning_module_id','learning_results.learning_module_id')
									->whereRaw('learning_module_last_access.user_id = learning_results.user_id')->whereRaw('learning_module_last_access.created_at=(select max(lmla.created_at) from learning_module_last_access as lmla where lmla.user_id=learning_results.user_id and lmla.learning_module_id=learning_results.learning_module_id)');
					});
				}
				elseif($relationshipName==="FormProgramme")
				{
					$query->leftjoin('apprenticeship_standards as form_apprenticeship_standards',function($join){
						$join->on('form_apprenticeship_standards.id','user_forms.type_id')->where('user_forms.type','programme');
					});
				}
				elseif($relationshipName==="FormSchedule")
				{
					$query->leftjoin('schedules as form_schedules',function($join){
						$join->on('form_schedules.id','user_forms.type_id')->where('user_forms.type','schedule');
					});
				}
				elseif($relationshipName==="formLearningModule")
				{
					$query->leftjoin('learning_modules as form_resource',function($join){
						$join->on('form_resource.id','user_forms.type_id')->where('user_forms.type','learning_module');
					});
				}
				else{
					$query->leftJoin($relatedTable, $relatedKey, '=', "{$relatedTable}.id"); // add the join clause
				}
			}
			if(count($relationshipParts))
			{
				$related = $relationshipMethod->getRelated()->newQuery();
				$query = $this->convertToEagerLoadingToJoin($query,implode(".",$relationshipParts),$related);
			}
			return $query;
		}
		function hasDuplicateJoin($query, $table)
		{
			// Get the joins in the query
			$joins = $query->getQuery()->joins;
			if(!$joins){ //if there is no join it will return null value . it will fail on foreach
				return false;
			}
			// Loop through the joins and check if there is a duplicate join for the table
			foreach ($joins as $join) {
					if ($join->table === $table) {
							return true;
					}
			}

			return false;
		}

		/**
		 * For displaying table based on user selection it will use pagination and this function convert user inputs to
		 * $reportData both for preview and already created reports
		 *
		 * @param  $request
		 * @param  $response
		 * @param  $args
		 * @return object
		 * @api
		 */
		public function table(Request $request, Response $response, $args)
		{
			$params = $request->getParsedBody();

			$customReport=null;
			if(isset($params['search']) && isset($params['search']['additionalSearchParams']) && isset($params['search']['additionalSearchParams']['type'])){
				$reportType = $params['search']['additionalSearchParams']['type'];
			}

			if (array_key_exists('id', $args)) {
				$customReport = CustomReport::with('fields')->find($args['id'])->toArray();
				$reportType =$customReport['type'];

				if(isset($params['search']) && isset($params['search']['additionalSearchParams']) && isset($params['search']['additionalSearchParams']['fields'])){
					$this->selectedFields = $params['search']['additionalSearchParams']['fields'];
				}

				if(isset($params['search']) && isset($params['search']['additionalSearchParams']) && isset($params['search']['additionalSearchParams']['filter']))
				{
					$customReport['filter']=$params['search']['additionalSearchParams']['filter'];
				}
				unset($params['search']['additionalSearchParams']);
			} else {
				$customReport = $params['search']['additionalSearchParams'];
				unset($params['search']['additionalSearchParams']);
			}

			if (isset($params['search']['additionalSearchParams']) && $params['search']['additionalSearchParams']['type']){
				$customReport= \Models\Field::where('field_category_id',$reportType)->where("status","1")->toArray();
			}

			if ($customReport) {
				if (isset($args["download"])) {


					// Save data in UserDownload to be processed in the background
					// \Models\UserDownload::processAll()

					$user_download = new \Models\UserDownload();
					
					// Ensure we have a valid name for the download
					$downloadName = 'Custom Report Export';
					if (isset($customReport['name']) && !empty($customReport['name'])) {
						$downloadName = $customReport['name'];
					} elseif (isset($reportType)) {
						$downloadName = 'Custom Report - ' . ucfirst($reportType);
					}
					
					$user_download->name = $downloadName;
					$user_download->user_id = \APP\Auth::getUserId();
					$user_download->type = 'Data Tables';
					$user_download->args = json_encode($customReport);
					$user_download->params = json_encode($params);
					$user_download->save();

					return $response;

				}

				$reports = $this->generateReport($customReport, $params); //generate report using selected fields or already saved data

				$reports = SmartTable::searchPaginate($params, $reports,false,true,true);   //Paginate the data
				$reports = $reports->toArray(); //convert report to array for setting default value
				foreach($reports['data'] as $key => $report ) //Loop the generated report data and then set default values;
				{
					if($customReport['type']=='user_forms') //check the report type is form
					{
						$user_id = data_get($report,'user_id'); //get user id form report data
						$user_form_id = data_get($report,'id'); //get user form id from report
						$form_id = data_get($report,'form_id'); //get form id from report
						foreach($customReport['fields'] as $field) //loop the fields to check data
						{
							$value = data_get($report,$field['column_name']);
							$columns = explode('.',$field['column_name']);
							$field_slug = $columns[count($columns)-1];

							if(isset($report['custom']) && !$report['custom']){
								$report['custom']=[];
							}

							$form_field = FormField::select('form_fields.*')->join('fields','fields.id','form_fields.field_id')->where(['fields.slug'=>$field_slug,'form_id'=>$form_id])->first();

							if($form_field)
							{
								$custom_field_query = CustomFieldQuery::where('form_field_id',$form_field->id)->first();
								if($custom_field_query)
								{
									$default_value = Form::fetchFromSqlDefaultValue($user_id,$custom_field_query->id,$form_field->id,$user_form_id,true);
									if(!$reports['data'][$key]['custom'])
									{
										$reports['data'][$key]['custom'] = [];
									}
									$reports['data'][$key]['custom'][$field_slug]=$default_value;
								}
							}
						}
					}
				}
            $response->getBody()->write(json_encode($reports));
            return $response->withHeader('Content-Type', 'application/json');

			} else {
          $response->getBody()->write(['status'=>false,'message'=>"Report Not Found"]);
          return $response->withHeader('Content-Type', 'application/json')->withStatus(404);
			}
    $response->getBody()->write(json_encode($customReport));
    return $response->withHeader('Content-Type', 'application/json');
		}


	public function setArray($pathArray,$array,$value)
	{
		$refrence = &$array;
		foreach($pathArray as $key)
		{
			if(!array_key_exists($key,$refrence))
			{
				$refrence[$key]=[];
			}
			$refrence = &$refrence[$key];
		}
		$refrence = $value;
		unset($refrence);
	}
		public function getCustomUserArray($userArray, $array_count,$type = 'user')
		{
			$tableType= \Models\FieldCategory::where('slug','user')->first();
				$addedFields= \Models\Field::where('field_category_id',$tableType->id)->where("status","1")->where('custom',1)->latest('updated_at')->limit(1)->get()->toArray();

				foreach ($addedFields as $key => $value) {

								$userArray['users'][$array_count + $key]["column_name"] = 'user_form_values.slug';
								$userArray['users'][$array_count + $key]["resource"] = 'UserFormValues';
								$userArray['users'][$array_count + $key]["name"] =  $value['slug'];
								$userArray['users'][$array_count + $key]["filter"] = 'like';
								$userArray['users'][$array_count + $key]["multi_select"] = false;
								$userArray['users'][$array_count + $key]["filter_key"] = 'like';
								$userArray['users'][$array_count + $key]["type"] = $value['type'];
                $userArray['users'][$array_count + $key]["class"] = UserFormValue::class;
				}
				return $userArray;
				$customReport['filter']['user_form_values.slug'] = [];
				return $customReport;
		}

		public function getCustomFieldArray($customReport, $array_count,$type)
		{
			$tableType= \Models\FieldCategory::where('slug','user')->first();
				$addedFields= \Models\Field::where('field_category_id',$tableType->id)->where("status","1")->where('custom',1)->get()->toArray();




				foreach ($addedFields as $key => $value) {

								$customReport['fields'][$array_count + $key]["column_name"] = 'user_form_values.slug';
								$customReport['fields'][$array_count + $key]["table_name"] = 'UserFormValues';
								$customReport['fields'][$array_count + $key]["name"] =  $value['slug'];
								$customReport['fields'][$array_count + $key]["filter"] = 'like';
								$customReport['fields'][$array_count + $key]["multi_select"] = false;
								$customReport['fields'][$array_count + $key]["filter_key"] = 'like';
								$customReport['fields'][$array_count + $key]["type"] = $value['type'];
				}
				$customReport['filter']['user_form_values.slug'] = [];
				return $customReport;
		}
		/**
		 * TODO:need to add edit section for custom report
		 *
		 * @param  $request
		 * @param  $response
		 * @return void
		 * @api
		 */
		public function edit(Request $request, Response $response)
		{
				$data = $request->getParsedBody();
				return $response;
		}
		/**
		 * Get Custom report configuration based on passed params
		 *
		 * @param  $request
		 * @param  $response
		 * @param  $args
		 * @return object
		 * @api
		 */
		public function get(Request $request, Response $response, $args)
		{
				$reportData = CustomReport::with(['fields' => function ($query) {
						$query->orderBy('order');
				}])->where('id', $args['id'])->first();
				$data=$reportData->toArray();

				foreach ($data['fields'] as $key => $value) {
						if ($value['is_custom_field']) {
								$columnName = $value['column_name'];
						} else {
								$field = $this->getField(self::$fields[$reportData['type']], $value['column_name']);
								$field['column_name'] = $field['slug'];
								$field['table_name'] = $field['resource'];
								$field['order'] = $value['order'];
								$data['fields'][$key] = $field;
						}
				}
        $response->getBody()->write(json_encode($data));
        return $response->withHeader('Content-Type', 'application/json');
		}
		/**
		 * Get All User created Report
		 *
		 * @param  $request
		 * @param  $response
		 * @return Array
		 * @api
		 */
		public function list(Request $request, Response $response)
		{
				$reports = CustomReport::with('fields')->get();
        $response->getBody()->write(json_encode($reports));
        return $response->withHeader('Content-Type', 'application/json');
		}
		/**
		 * It will same like table function but it will not using pagination
		 *
		 * @param  $request
		 * @param  $response
		 * @param  $args
		 * @return Array
		 * @api
		 */
		public function allTableData(Request $request, Response $response, $args)
		{
				$customReport = CustomReport::with('fields')->find($args['id']);
				if ($customReport) {
						$report = $this->generateReport($customReport);

            if (isset($args['limit']) && $args['limit']!=0) {
								$report = $report->limit($args['limit']);
						}

            $response->getBody()->write(json_encode(['report' => $report->get(), 'header' => $customReport->fields]));
            return $response->withHeader('Content-Type', 'application/json')
                            ->withStatus(200);
				} else {
            $response->getBody()->write(json_encode(['status' => false, 'message' => 'Report Not Found']));
            return $response->withHeader('Content-Type', 'application/json')
                            ->withStatus(404);
				}
		}

		public function editData(Request $request,Response $response, $args)
		{
				$reportData = CustomReport::with('fields')->where('id', $args['id'])->first();
				$data = $request->getParsedBody();
				DB::beginTransaction();

				try {
						if (array_key_exists('report_type', $data) && array_key_exists('report_name', $data)) {
								$filters=isset($data['filters'])? json_encode($data['filters']): [];
								if(!isset($data['user_specific_filter']))
								{
									$data['user_specific_filter']=null;
								}
								$customReport = CustomReport::where('id', $args['id'])->update(["name"=>$data["report_name"],"type"=>$data["report_type"], 'filters'=>$filters,'user_specific_filter'=>$data['user_specific_filter']]);
								if (array_key_exists("selectedFields", $data) && is_array($data['selectedFields']) && count($data['selectedFields']) > 0) {
										$columns = [];
										foreach ($data['selectedFields'] as $index => $field) {
												$columns [] = $field['slug'];
												$multi_select=isset($field['multi_select'])?$field['multi_select']:false;
												CustomReportField::updateOrCreate([
														'custom_report_id' => $args['id'],
														'column_name' => $field['slug']
												], [
														'custom_report_id' => $args['id'],
														'name' => $field['name'],
														'column_name' => $field['slug'],
														'order' => $index ?? 0,
														'table_name' => $field['resource'],
														'multi_select' => $multi_select,
														'type' => $field['type'],
												]);
										}
					$delete = CustomReportField::where('custom_report_id',$args['id'])->whereNotIn('column_name',$columns)->delete();
									$customReport = CustomReport::where('id', $args['id'])->first();
										if(isset($data['form']) && $customReport->type=='user_forms')
										{
											self::generateView($customReport,$data['selectedFields'],$data['form']);
										}
								} else {
										throw new Error("Invalid Field selection");
								}
								if (array_key_exists('selectedVisibility', $data) && is_array($data['selectedVisibility'])) {
										$permissions=[];
										foreach ($data['selectedVisibility'] as $key=>$value) {
												foreach ($value as $id) {
														$permissions[] = $id;
														//CustomReportPermission::create(['type'=>$key,'type_id'=>$id,'custom_report_id'=>$customReport->id]);
														$customReportPermission=CustomReportPermission::updateOrCreate(['type_id'=>$id,'custom_report_id'=>$args['id']],['type'=>$key,'type_id'=>$id,'custom_report_id'=>$args['id']]);
												}
												$delete = CustomReportPermission::where('custom_report_id',$args['id'])->whereNotIn('type_id',$permissions)->delete();
										}
								} else {
										throw new Exception("Invalid set visiblilty field");
								}
						} else {
								throw new Exception("Invalid Report Name or Report type");
						}
						DB::commit();
            $response->getBody()->write(json_encode(["status"=>true,"message"=>"Custom Report Created"]));
            return $response->withHeader('Content-Type', 'application/json')
              ->withStatus(201)
            ;

				} catch(Exception $ex) {
						DB::rollBack();
            $response->getBody()->write(json_encode([
							"status"=>false,
							"message"=>$ex->getMessage()
            ]));
            return $response->withHeader('Content-Type', 'application/json')
              ->withStatus(422)
            ;
				}

		}
		public function getField($fields,$slug)
		{
			if (isset($fields)) {
				foreach ($fields as $field) {
					if(isset($field['slug']) && $slug===$field['slug']){
						return $field;
					}
				}
			}
		}
		public function getDropDown(Request $request,Response $response,$args) //getting drop down value for custom report filter
		{
			 $field = $this->getField(self::$fields[$args['type']],$args['column_name']);
				$orderBy = 'name';
				$select = [];
			 if($field['name'] == 'Learning Provider') {
					 $orderBy = 'company';
					 $select = ['*','company as name'];
			 }
				if($field['name'] == "Resource Status" ){
          $response->getBody()->write(json_encode([
                [
                    "id" => 1,
                    "name" => "Awaiting Approval",
                    "slug" => "awaiting approval"
                ], [
                    "id" => 2,
                    "name" => "In Progress",
                    "slug" => "in progress"
                ],[
                    "id" => 3,
                    "name" => "Not Attempted",
                    "slug" => "not attempted"
                ],[
                    "id" => 4,
                    "name" => "Completed",
                    "slug" => "completed"
                ]
          ]));
          return $response->withHeader('Content-Type', 'application/json');
				}

       if(!isset($field['class'])) {
        $response->getBody()->write(json_encode([]));
        return $response->withHeader('Content-Type', 'application/json');
       }

			 if(str_contains($args['column_name'], 'custom') || str_contains($args['column_name'],'field_')){
				 if(str_contains($args['column_name'],'custom')){
					 $customfield = str_replace('custom_', '', $args['column_name']);
				 }else{
					 $customfield = str_replace('field_','',$args['column_name']);
				 }
				$customFieldId = Field::where('slug', $customfield)->first()->id;
				$model = $field['class'];
				$data = $model::select(['id','value','key as name', 'key'])->orderBy('key')->where('field_id', $customFieldId)->get();
        $response->getBody()->write(json_encode([]));
        return $response->withHeader('Content-Type', 'application/json');

			 }

			 $model = $field['class']::query();
			 if(!empty($select)) {
					 $model = $model->select($select);
			 }
				$data = $model
					->where('status',true)
					->orderBy($orderBy)
				;
				/*Conditional Clauses*/
				$conditionalClauses = $field['conditional_clauses'] ?? [];
				foreach ($conditionalClauses as $conditionalClause) {
						$clause = $conditionalClause['clause'] ?? null;
						$condition = $conditionalClause['condition'] ?? null;
						if ($clause && $condition) {
								$data->where(function ($query) use ($condition) {
										foreach ($condition as $conditionItem) {
												[$column, $operator, $value] = $conditionItem;
												$query->where($column, $operator, $value);
										}
								});
						}
				}
        $response->getBody()->write(json_encode($data->get()));
        return $response->withHeader('Content-Type', 'application/json');
	}
  public function export(Request $request,Response $response,$args){
    $documentTemplate = DocumentTemplate::with(['document_template_bindings'=>function($query){
      $query->with(['reports'=>function($query){
        $query->with('fields');
      }]);
      $query->with(['graphs'=>function($query){
        $query->with('dimensions');
        $query->with('metrics');
      }]);
		}])->find($args['id']);

    if ($documentTemplate) {
    $hash = hash('sha256',$documentTemplate->name.$documentTemplate->id.time());
        $exportData = FormsExportImportLog::create([
            'hash' => $hash,
            'type' => 'custom-report',
            'type_id' => $documentTemplate->id,
            'export_data' => $documentTemplate
        ]);

        $filename = $documentTemplate->name . '.json';
        $response->getBody()->write(json_encode($exportData));

        return $response->withHeader('Content-Type', 'application/json')
                        ->withHeader('Content-Disposition', 'attachment; filename=' . $filename)
                        ->withStatus(200);
    } else {
        $response->getBody()->write(json_encode(['status' => false, 'message' => 'Document Template Not Found']));
        return $response->withHeader('Content-Type', 'application/json')
                        ->withStatus(404);
  }
}

	public function import(Request $request, Response $response)
	{
    $body = $request->getParsedBody();
    if (isset($_FILES['importFile'])) {
      $storage = new \Upload\Storage\FileSystem($this->container->settings["LMSTempPath"]);
      $import_file = new \Upload\File('importFile', $storage);
      $mimeType = $import_file->getMimetype();

      if (($mimeType == "text/plain" || $mimeType == "application/json") && strtolower($import_file->getExtension()) == 'json') {
        try {
          $uuid = uniqid();
          $import_file->upload($uuid);
        } catch (Exception $ex) {
          $errors = $import_file->getErrors();
                  $response->getBody()->write(json_encode(['message' => implode("\n", $errors)]));
                  return $response->withHeader('Content-Type', 'application/json')->withStatus(500);
        }

        $json_data = file_get_contents($this->container->settings["LMSTempPath"] . $import_file->getNameWithExtension());
        try {
          $json_data = json_decode($json_data, true);
        } catch (Exception $ex) {
                  $response->getBody()->write(json_encode(['message' => 'Invalid json format']));
                  return $response->withHeader('Content-Type', 'application/json')->withStatus(500);
        }

        $formExportImportLog = FormsExportImportLog::where('hash', $json_data['hash'])->first();
        if ($body['import_and_update'] != "true" && $formExportImportLog) {
                  $response->getBody()->write(json_encode(['message' => 'Duplicate entry']));
                  return $response->withHeader('Content-Type', 'application/json')->withStatus(422);
        }

        try {
          $result = $this->importData($json_data, $formExportImportLog);
                  if ($result) {
                      $response->getBody()->write(json_encode(['message' => 'Imported successfully']));
                      return $response->withHeader('Content-Type', 'application/json')->withStatus(200);
                  } else {
                      $response->getBody()->write(json_encode(['message' => 'Invalid json format']));
                      return $response->withHeader('Content-Type', 'application/json')->withStatus(500);
          }
        } catch (Exception $ex) {
                  $response->getBody()->write(json_encode(['message' => 'Invalid json format']));
                  return $response->withHeader('Content-Type', 'application/json')->withStatus(500);
        }
      } else {
              $response->getBody()->write(json_encode(['message' => "Incorrect file format, please provide JSON file."]));
              return $response->withHeader('Content-Type', 'application/json')->withStatus(415);
      }
    }
    return $response;
	}


		public function delete(Request $request,Response $response,$args)
		{
			\Models\CustomReport::where('id', $args['id'])->delete();
			if(\Models\CustomReportField::where('custom_report_id', $args['id'])->exists()){
				\Models\CustomReportField::where('custom_report_id', $args['id'])->delete();
			}
      $response->getBody()->write(json_encode(["status"=>true,"message"=>"Custom Report deleted successfully"]));
      return $response->withHeader('Content-Type', 'application/json')->withStatus(200);
		}

public static function getSqlWithBindings($query)
{
		return vsprintf(str_replace('?', '%s', $query->toSql()), collect($query->getBindings())->map(function ($binding) {
				return is_numeric($binding) ? $binding : "'{$binding}'";
		})->toArray());
}

	public function importData($jsonData, $formExportImportLog = null)
	{
		try {
			DB::beginTransaction();
			$bindings_id = [];
			$update = $formExportImportLog ? true : false;
			$templateData = $jsonData['export_data'];
			$document_template_bindings = $templateData['document_template_bindings'];
			unset($templateData['document_template_bindings']);
			$id = $templateData['id'];
			unset($templateData['id']);
			if($update)
			{
				$template = DocumentTemplate::updateOrCreate(['id'=>$id],$templateData);
			}else{
				$template = DocumentTemplate::create($templateData);
			}
			foreach ($document_template_bindings as $document_template_binding) {
				if (isset($document_template_binding['reports'])) {
					$report = $document_template_binding['reports'];
					unset($document_template_binding['reports']);
					$fields = $report['fields'];
					unset($report['fields']);
					$id = $report['id'];
					unset($report['id']);
					if ($update) {
						$customReport = CustomReport::updateOrCreate(['id' => $id], $report);
					} else {
						$customReport = CustomReport::create($report);
					}
					$id = $document_template_binding['id'];
					$bindings_id [] =$id;
					unset($document_template_binding['id']);
					$document_template_binding['document_template_id']=$template->id;
					$document_template_binding['custom_report_id']=$customReport->id;
					if($update)
					{
							DocumentTemplateBinding::updateOrCreate(['id'=>$id],$document_template_binding);
					}else{
						$bindings = DocumentTemplateBinding::create($document_template_binding);
					}

					$field_ids = [];
					foreach ($fields as $field) {
						$id = $field['id'];
						unset($field['id']);
						if ($update) {
							$field_ids[] = $id;
							CustomReportField::updateOrCreate(['id' => $id], $field);
						} else {
							$field['custom_report_id'] = $customReport->id;
							CustomReportField::create($field);
						}
					}
					if ($update) {
						CustomReportField::whereNotIn('id', $field_ids)->where('custom_report_id', $customReport->id)->delete();
					}
				} elseif (isset($document_template_binding['graphs'])) {
					$graph = $document_template_binding['graphs'];
					unset($document_template_binding['graphs']);
					$metrics = $graph['metrics'];
					$dimensions = $graph['dimensions'];
					unset($graph['metrics']);
					unset($graph['dimensions']);
					$id = $graph['id'];
					unset($graph['id']);
					if ($update) {
						$graph = Graph::updateOrCreate(['id' => $id], $graph);
					} else {
						$graph = Graph::create($graph);
					}
					$field_ids = [];
					foreach ($dimensions as $dimension) {
						$id = $dimension['id'];
						unset($dimension['id']);
						if ($update) {
							$field_ids[] = $id;
							GraphField::updateOrCreate(['id' => $id], $dimension);
						} else {
							$dimension['graph_id'] = $graph->id;
							GraphField::create($dimension);
						}
					}
					foreach ($metrics as $metric) {
						$id = $metric['id'];
						unset($metric['id']);
						if ($update) {
							$field_ids[] = $id;
							GraphField::updateOrCreate(['id' => $id], $metric);
						} else {
							$metric['graph_id'] = $graph->id;
							GraphField::create($metric);
						}
					}
					if ($update) {
						GraphField::whereNotIn('id', $field_ids)->where('graph_id', $graph->id)->delete();
					}
					$id = $document_template_binding['id'];
					$bindings_id [] =$id;
					unset($document_template_binding['id']);
					$document_template_binding['custom_graph_id']=$graph->id;
					$document_template_binding['document_template_id']=$template->id;
					if($update)
					{
						DocumentTemplateBinding::updateOrCreate(['id'=>$id],$document_template_binding);
					}else{
						DocumentTemplateBinding::create($document_template_binding);
					}
				}
			}
			if($update)
			{
				DocumentTemplateBinding::whereNotIn('id',$bindings_id)->where('document_template_id',$template->id)->delete();
			}
			DB::commit();
			return true;
		} catch (Exception $ex) {
			DB::rollBack();
			return false;
		}
	}
	public static function fixSlug($slug){
		if(strlen($slug)>60)
		{
			$limitedString = substr($slug,0,50);
		 // Add a timestamp to the limited string
			$timestamp = time(); // Current timestamp
			return $limitedString . '_' . $timestamp;

		}
		else
		{
			return $slug;
		}
	}
	public function fixSlugIssue(Request $request, Response $response)
		{
				try {
						DB::beginTransaction();
						Field::where('type','!=','header')->chunk(100, function ($fields) {
								foreach ($fields as $field) {
										$fixedSlug = $this->fixSlug($field->slug);
										if ($fixedSlug !== $field->slug) {
												$userCustomFormValues = UserCustomFormValue::whereRaw("JSON_EXTRACT(`values`, '$.".$field->slug."') IS NOT NULL")->get();
												foreach($userCustomFormValues as $userCustomFormValue)
												{
														$values = $userCustomFormValue->values;
														$value = $values[$field->slug];
														unset($values[$field->slug]);
														$values[$fixedSlug]=$value;
														$userCustomFormValues->values = $values;
														$userCustomFormValue->save();
												}
												$field->slug = $fixedSlug;
												$field->save();
										}
								}
						});
						DB::commit();
            $response->getBody()->write(json_encode(['message' => 'done']));
            return $response->withHeader('Content-Type', 'application/json')->withStatus(200);
				}
				catch (Exception $ex)
				{
						DB::rollBack();
            $response->getBody()->write(json_encode(['message' => $ex->getMessage()]));
            return $response->withHeader('Content-Type', 'application/json')->withStatus(200);
				}
	}
	public static function generateView($customReport,$fields,$form,$temp=false){
		$slugs = [];
		if (is_array($form)){
				$form = implode(",", $form);
		}
		foreach($fields as $field)
		{
			if(isset($field['field'])){
				if(isset($field['field']['custom']) && $field['field']['custom']==1)
				{
				 $slugs [] = '"'.$field['field']['slug'].'"';
				}
			}
		}
		if($customReport)
		{
			if($customReport->custom_view)
			{
				$name = $customReport->custom_view;
			}else{
				$name = Tools::safeName($customReport->name)."_".time();
				}
		}else
		{
			$name = "temp_".time();
		}
		$fields_list = implode(",",$slugs);

    $fields_condition = empty($slugs) ? '': 'and f.slug IN (' . implode(',', $slugs) . ')';

if(!$temp){
$sql = <<<EOF
		SET SESSION group_concat_max_len = 1000000;

SET @sql = NULL;

SELECT
	GROUP_CONCAT(DISTINCT
	CONCAT(
		'max(case when IFNULL(uv.slug, f.slug) = ''',
		f.slug,
		''' then IFNULL(uv.value, "") end) `',
		f.slug,
		'`'
	)
	) INTO @sql
FROM
	fields f
	LEFT JOIN user_form_values uv ON uv.slug = f.slug
WHERE
	f.custom = 1
	AND f.type != 'header'
	AND f.status = 1
	AND uv.slug IS NOT NULL and f.slug in($fields_list);

SET @sql = CONCAT('SELECT uf.id as user_form_id, uf.user_id, IFNULL(uv.updated_at, "") as updated_at, ', @sql, '
					FROM user_forms uf
					LEFT JOIN user_form_values uv ON uf.id = uv.user_form_id
					JOIN fields f ON IFNULL(uv.slug, f.slug) = f.slug
					WHERE uf.form_id in ($form)
					GROUP BY uf.id');

SET @sql = CONCAT('CREATE OR REPLACE VIEW $name AS ', @sql);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
EOF;
		DB::unprepared($sql);
		$customReport->custom_view = $name;
		$customReport->form_id = $form;
      $customReport->save();
}else
{
$sql = <<<EOF

SET SESSION group_concat_max_len = 1000000;
SET @sql = NULL;

SELECT
	GROUP_CONCAT(DISTINCT
	CONCAT(
		'max(case when IFNULL(uv.slug, f.slug) = ''',
		f.slug,
		''' then IFNULL(uv.value, "") end) `',
		f.slug,
		'`'
	)
	) INTO @sql
FROM
	fields f
	LEFT JOIN user_form_values uv ON uv.slug = f.slug
WHERE
	f.custom = 1
	AND f.type != 'header'
	AND f.status = 1
	AND uv.slug IS NOT NULL AND  f.slug in($fields_list);

SET @sql = CONCAT('SELECT uf.id as user_form_id, uf.user_id, IFNULL(uv.updated_at, "") as updated_at, ', @sql, '
					FROM user_forms uf
					LEFT JOIN user_form_values uv ON uf.id = uv.user_form_id
					JOIN fields f ON IFNULL(uv.slug, f.slug) = f.slug
					WHERE uf.form_id in ($form)
					GROUP BY uf.id');

SET @sql = CONCAT('CREATE OR REPLACE VIEW $name AS ', @sql);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
EOF;
		DB::unprepared($sql);
			$customReport = new stdClass();
			$customReport->type = "user_forms";
			$customReport->custom_view = $name;
			return $customReport;

}
	}
}
