<?php

namespace APP\Controllers;

use APP\Auth;
use APP\Tools;
use Carbon\Carbon;
use DB\LicenseFeatures;
use GlobalPayments\Api\Entities\Enums\AccountType;
use GuzzleHttp\Client;
use Illuminate\Database\Capsule\Manager as DB;
use Illuminate\Support\Str;
use Models\Import;
use Models\ImportLog;
use Models\ImportLogError;
use Models\LearningModule;
use Models\LearningModuleCategory;
use Models\LearningModuleType;
use Models\LearningResult;
use Models\Picklist;
use Models\Schedule;
use Models\ScheduleLink;
use Models\User;
use Models\UserLearningModule;
use Models\UserSubDepartment;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Psr\Container\ContainerInterface;
use Slim\Factory\AppFactory;
use League\Flysystem\Filesystem;
use League\Flysystem\Local\LocalFilesystemAdapter;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;

class ImportController extends Controller
{
    protected $container;
    public static $settings = [];
    function __construct(ContainerInterface $container)
    {
        $this->container = $container;
    }
    public function upload(Request $request, Response $response)
    {
        $data = $request->getParsedBody();
        if (!isset($data['type'])) {
            return \APP\Tools::returnCode($request, $response, 400, 'Missing file type parameter');
        }

        $validationResponse = $this->validateUploadPermissions($request, $response, $data['type']);
        if ($validationResponse !== null) {
            return $validationResponse;
        }

        if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
            return \APP\Tools::returnCode($request, $response, 400, 'No valid file uploaded');
        }

        $mimeTypes = \APP\Tools::documentMime('excel');
        $allowedExtensions = \APP\Tools::allowExtensions('excel');
        $storagePath = $this->container->get('settings')["LMSTempPath"];

        try {
            // Initialize Flysystem
            $adapter = new LocalFilesystemAdapter($storagePath);
            $filesystem = new Filesystem($adapter);

            $file = $_FILES['file'];
            $originalName = $file['name'];
            $extension = pathinfo($originalName, PATHINFO_EXTENSION);
            $importFileId = uniqid('', true) . '.' . $extension;
            $uploadedFilePath = $storagePath . '/' . $importFileId;

            // Validate file extension
            if (!in_array(strtolower($extension), $allowedExtensions)) {
                return \APP\Tools::returnCode($request, $response, 400, 'Invalid file extension');
            }

            // Validate file MIME type
            $finfo = new \finfo(FILEINFO_MIME_TYPE);
            $fileMimeType = $finfo->file($file['tmp_name']);
            if (!in_array($fileMimeType, $mimeTypes)) {
                return \APP\Tools::returnCode($request, $response, 400, 'Invalid file type');
            }

            // Move file using Flysystem
            $stream = fopen($file['tmp_name'], 'r+');
            $filesystem->writeStream($importFileId, $stream);
            fclose($stream);

            // Process the uploaded file
            $header = $this->getExcelHeaderFieldName($uploadedFilePath);
            $responseData = [
                'file_name' => $importFileId,
                'original_name' => $originalName,
                'header' => $header,
                'fields' => $this->getRequiredFieldBasedOnType($data['type'],$data['how_to_import'])
            ];

            $response->getBody()->write(json_encode($responseData));
            return $response->withHeader('Content-Type', 'application/json');

        } catch (\Exception $e) {
            return \APP\Tools::returnCode($request, $response, 500, 'Error uploading file: '. $e->getMessage());
        }

        return $response;
    }

    public function getExcelHeaderFieldName($filePath)
    {
        $excel = IOFactory::load($filePath);
        $sheet = $excel->getActiveSheet();
        $highestRow = $sheet->getHighestRow();
        $highestColumn = $sheet->getHighestColumn();
        $highestColumnIndex = Coordinate::columnIndexFromString($highestColumn);
        $header = [];

        for ($col = 1; $col <= $highestColumnIndex; ++$col) {
            $columnLetter = Coordinate::stringFromColumnIndex($col);
            $cellValue = $sheet->getCell($columnLetter . '1')->getValue();

            if ($cellValue) {
                $header[] = [
                    'column_name' => $columnLetter,
                    'header_value' => $cellValue,
                    'preview' => $sheet->getCell($columnLetter . '2')->getCalculatedValue()
                ];
            }
        }

        return $header;
    }

    public function import(Request $request, Response $response)
    {
        $params = $request->getParsedBody();
        $validationResponse = $this->validateUploadPermissions($request, $response, $params['type']);
        if ($validationResponse !== null) {
            return $validationResponse;
        }

        $params['auth_user_id'] = Auth::getUserId();
        $import = Import::create([
            'file' => $params['file'],
            'params' => $params,
            'type' => 'wizard_import',
            'cron_run' => 1
        ]);
        if(\APP\Tools::getConfig('sharedClients') && isset($params['type']) && $params['type']=='user'){
            $temp_path = $this->container->get('settings')["LMSTempPath"];
            $logs = $this->userImport($import, $temp_path);
            $response->getBody()->write(json_encode(['message' => 'Import has been processed.', 'logs' => $logs]));
            return $response->withHeader('Content-Type', 'application/json');
        }else{
            $response->getBody()->write(json_encode(['message' => 'Import has been added to the queue.  This will be processed within the next 5 minutes.']));
            return $response->withHeader('Content-Type', 'application/json');
        }
    }
    public function getRequiredFieldBasedOnType($type,$slug=false)
    {
        if($type == 'user' && $slug=='delete_accounts_listed')
        {
            return [
                'username' => ['name' => 'User Name', 'required' => false, "pre_select" => ["username", "user name", "user"]],
            ];
        }
        if ($type == 'user') {
            return [

                'fname' => ['name' => 'First Name', 'required' => false, "pre_select" => ["first name", "firstname", "fname"]],

                'lname' => ['name' => 'Last Name', 'required' => false, "pre_select" => ["last name", "lastname", "lname"]],

                'username' => ['name' => 'User Name', 'required' => false, "pre_select" => ["username", "user name", "user"]],

                'email' => ['name' => 'Email', 'required' => true, "pre_select" => ["email", "e-mail", "mail", "user email", "useremail"]],

                'phone' => ['name' => 'Phone', 'required' => false, "pre_select" => ["phone", "phone number", "phone no", "phone number", "mobile", "mobile no", "mobile number", "tel", "telephone", "user phone", "user mobile"]],

                'designation' => ['name' => 'Designation', 'required' => false, "pre_select" => ["designation", "job", "user job", "user designation", "job title"]],

                'country' => ['name' => 'Country', 'required' => false, "pre_select" => ["country", "user country"]],

                'company' => ['name' => 'Company', 'required' => false, "pre_select" => ["company", "user company"]],

                'department' => ['name' => 'Department', 'required' => false, "pre_select" => ["department", "user department"]],

                'location' => ['name' => 'Location', 'required' => false, "pre_select" => ["location", "user location"]],

                'city' => ['name' => 'City', 'required' => false, "pre_select" => ["city", "user city", "user town", "town"]],

                'role' => ['name' => 'Role', 'required' => false, "pre_select" => ["role", "user role"]],
                'description' => ['name' => 'Description', 'required' => false, "pre_select" => ["description", "user description", "user desc"]],
                'staff_type' => ['name' => 'Staff Type', 'required' => false, "pre_select" => ["staff type", "user staff type"]],

                'staff_type_assigned' => ['name' => 'Staff Type Assigned', 'required' => false, "pre_select" => ["staff type assigned", "staff_type_assigned"]],

                'report_to' => ['name' => 'Report To', 'required' => false, "pre_select" => ["report to", "report_to"]],

                'discount_percentage' => ['name' => 'Discount Percentage', 'required' => false, "pre_select" => ["discount percentage", "discount_percentage"]],

                'staff_type_sign_off_approval' => ['name' => 'Staff Type Sign Off Approval', 'required' => false, "pre_select" => ["staff type sign off approval", "staff_type_sign_off_approval"]],

                'emergency_name' => ['name' => 'Emergency Name', 'required' => false, "pre_select" => ["emergency name", "emergencyname", "emergency name", "emergency name"]],

                'emergency_relationship' => ['name' => 'Emergency Relationship', 'required' => false, "pre_select" => ["emergency relationship", "emergencyrelationship"]],

                'emergency_contact_numbers' => ['name' => 'Emergency Contact Numbers', 'required' => false, "pre_select" => ["emergency contact numbers", "emergencycontactnumbers", "emergency contact number", "emergency contact no", "emergency contact"]],

                'visa_length' => ['name' => 'Visa Length(Under 3 years,Over 3 years)', 'required' => false, "pre_select" => ["visa length", "visa_length"]],

                'visa_number' => ['name' => 'Visa Number', 'required' => false, "pre_select" => ["visa number", "visa_number"]],

                'visa_date' => ['name' => 'Visa Date', 'required' => false, "pre_select" => ["visa date", "visa_date"]],

                'UKPRN' => ['name' => 'UKPRN', 'required' => false, "pre_select" => ["ukprn", "UK Register of Learning Providers", "united kingdom Register of Learning Providers"]],

                'LearnRefNumber' => ['name' => 'Learn Ref Number', 'required' => false, "pre_select" => ["learn ref number", "learnrefnumber", "Learn Refrence Number"]],

                'PrevLearnRefNumber' => ['name' => 'Previous Learn Ref Number', 'required' => false, "pre_select" => ["previous learn ref number", "prevlearnrefnumber", "Previous Learn Refrence Number"]],

                'ULN' => ['name' => 'ULN', 'required' => false, "pre_select" => ["uln", "Unique Learner Number"]],

                'CampId' => ['name' => 'Camp ID', 'required' => false, "pre_select" => ["camp id", "campid"]],

                'OTJHours' => ['name' => 'OTJ Hours', 'required' => false, "pre_select" => ["otj hours", "otjhours"]],

                'DateOfBirth' => ['name' => 'Date Of Birth', 'required' => false, "pre_select" => ["date of birth", "dob", "dateofbirth"]],

                'Ethnicity' => ['name' => 'Ethnicity', 'required' => false, "pre_select" => ["Ethnicity"]],

                'Sex' => ['name' => 'Sex', 'required' => false, "pre_select" => ["Gender", "sex"]],

                'LLDDHealthProb' => ['name' => 'LLDD Health Problem', 'required' => false, "pre_select" => ["LLDD Health Problem", "LLDDHealthProb"]],

                'NINumber' => ['name' => 'NI Number', 'required' => false, "pre_select" => ["NI Number", "NINumber"]],

                'PriorAttainLegacy' => ['name' => 'Prior Attain Legacy', 'required' => false, "pre_select" => ["Prior Attain Legacy", "PriorAttainLegacy"]],

                'PriorAttain' => ['name' => 'Prior Attain', 'required' => false, "pre_select" => ["Prior Attain", "PriorAttain"]],

                'Accom' => ['name' => 'Accom', 'required' => false, "pre_select" => ["Accom"]],

                'ALSCost' => ['name' => 'ALS Cost', 'required' => false, "pre_select" => ["ALS Cost", "ALSCost"]],

                'PlanLearnHours' => ['name' => 'Plan Learn Hours', 'required' => false, "pre_select" => ["Plan Learn Hours", "PlanLearnHours"]],

                'PlanEEPHours' => ['name' => 'Plan EEP Hours', 'required' => false, "pre_select" => ["Plan EEP Hours", "PlanEEPHours"]],

                'MathGrade' => ['name' => 'Math Grade', 'required' => false, "pre_select" => ["Math Grade", "MathGrade"]],

                'EngGrade' => ['name' => 'Eng Grade', 'required' => false, "pre_select" => ["Eng Grade", "EngGrade"]],

                'PostcodePrior' => ['name' => 'Postcode Prior', 'required' => false, "pre_select" => ["Postcode Prior", "PostcodePrior"]],

                'Postcode' => ['name' => 'Postcode', 'required' => false, "pre_select" => ["Postcode", "Post Code"]],

                'AddLine1' => ['name' => 'Address Line 1', 'required' => false, "pre_select" => ["Address Line 1", "AddLine1", "Address", "Address1"]],

                'AddLine2' => ['name' => 'Address Line 2', 'required' => false, "pre_select" => ["Address Line 2", "AddLine2", "Address2"]],

                'AddLine3' => ['name' => 'Address Line 3', 'required' => false, "pre_select" => ["Address Line 3", "AddLine3", "Address3"]],

                'AddLine4' => ['name' => 'Address Line 4', 'required' => false, "pre_select" => ["Address Line 4", "AddLine4", "Address4"]],

                'email2' => ['name' => 'Email 2', 'required' => false, "pre_select" => ["Email 2", "email2", "Secondary Email", "Alt email", "Alternate Email"]],

                'dynamic_fields' => ['name' => 'Dynamic Fields', 'required' => false, "pre_select" => ["Dynamic Fields", "DynamicFields"]],

                'enabled_google_2FA' => ['name' => 'Enabled Google 2FA', 'required' => false, "pre_select" => ["Enabled Google 2FA", "EnabledGoogle2FA"]],

                'google_2FA_secret' => ['name' => 'Google 2FA Secret', 'required' => false, "pre_select" => ["Google 2FA Secret", "Google2FASecret"]],

                'ContactPreference' => ['name' => 'Contact Preference', 'required' => false, "pre_select" => ["Contact Preference", "ContactPreference"]],

                'LLDDandHealthProblem' => ['name' => 'LLDD and Health Problem', 'required' => false, "pre_select" => ["LLDD and Health Problem", "LLDDandHealthProblem"]],

                'LearnerFAM' => ['name' => 'Learner FAM', 'required' => false, "pre_select" => ["Learner FAM", "LearnerFAM"]],

                'ProviderSpecLearnerMonitoring' => ['name' => 'Provider Spec Learner Monitoring', 'required' => false, "pre_select" => ["Provider Spec Learner Monitoring", "ProviderSpecLearnerMonitoring"]],

                'LearnerEmploymentStatus' => ['name' => 'Learner Employment Status', 'required' => false, "pre_select" => ["Learner Employment Status", "LearnerEmploymentStatus"]],

                'LearnerHE' => ['name' => 'Learner HE', 'required' => false, "pre_select" => ["Learner HE", "LearnerHE"]],

                'LearningDelivery' => ['name' => 'Learning Delivery', 'required' => false, "pre_select" => ["Learning Delivery", "LearningDelivery"]],

                'LearnerDestinationandProgression' => ['name' => 'Learner Destination and Progression', 'required' => false, "pre_select" => ["Learner Destination and Progression", "LearnerDestinationandProgression"]],

                'DelLocPostCode' => ['name' => 'Del Loc Post Code', 'required' => false, "pre_select" => ["Del Loc Post Code", "DelLocPostCode"]],

                'PrevUKPRN' => ['name' => 'Previous UKPRN', 'required' => false, "pre_select" => ["Previous UKPRN", "PrevUKPRN"]],

                'PMUKPRN' => ['name' => 'PM UKPRN', 'required' => false, "pre_select" => ["PM UKPRN", "PMUKPRN"]],
                'usercode' => ['name' => 'User Code', 'required' => false, "pre_select" => ["usercode", "user code", "id", "user id"]],
                'altusercode' => ['name' => 'Alter Code', 'required' => false, "pre_select" => ["altusercode", "alt user code", "alt id", "alt user id"]],
                'account_type' => ['name' => 'Account Type', 'required' => false, "pre_select" => ["account type", "accounttype"]],
                'group' => ['name' => 'Groups', 'required' => false, "pre_select" => ["group", "groups"]],
                'managers' => ['name' => 'Managers', 'required' => false, "pre_select" => ["managers", "manager"]],
                'department_sub_1' => ['name' => 'Department Sub 1', 'required' => false, "pre_select" => ["department sub 1", "department_sub_1"]],
                'department_sub_2' => ['name' => 'Department Sub 2', 'required' => false, "pre_select" => ["department sub 2", "department_sub_2"]],
                'department_sub_3' => ['name' => 'Department Sub 3', 'required' => false, "pre_select" => ["department sub 3", "department_sub_3"]],
                'department_sub_4' => ['name' => 'Department Sub 4', 'required' => false, "pre_select" => ["department sub 4", "department_sub_4"]],
                'department_sub_5' => ['name' => 'Department Sub 5', 'required' => false, "pre_select" => ["department sub 5", "department_sub_5"]],
                'department_sub_6' => ['name' => 'Department Sub 6', 'required' => false, "pre_select" => ["department sub 6", "department_sub_6"]],
                'password' => ['name' => 'Password', 'required' => false, "pre_select" => ["password", "pass", "user password"]],
                'skype_id' => ['name' => 'Skype ID', 'required' => false, "pre_select" => ["skype id", "skypeid", "skype"]],
                'zoom_id' => ['name' => 'Zoom ID', 'required' => false, "pre_select" => ["zoom id", "zoomid", "zoom"]],
                'teams_id' => ['name' => 'Team ID', 'required' => false, "pre_select" => ["team id", "teamid", "team"]],
                'expiration_dt' => ['name' => 'Expiration Date', 'required' => false, "pre_select" => ["expiration date", "expiration_dt"]],
                'registration_dt' => ['name' => 'Registration Date', 'required' => false, "pre_select" => ["registration date", "registration_dt"]],
                'staff_type_id' => ['name' => 'Staff Type ID', 'required' => false, "pre_select" => ["staff type id", "staff_type_id"]],
                'report_to' => ['name' => 'Report To', 'required' => false, "pre_select" => ["report to", "report_to"]],
                'school' => ['name' => 'School', 'required' => false, "pre_select" => ["school", "college", "university"]],
                'exclude_from_reports' => ['name' => 'Exclude From Reports', 'required' => false, "pre_select" => ["exclude from reports", "exclude_from_reports"]],
                'exclude_from_ilr_export' => ['name' => 'Exclude From ILR Reports', 'required' => false, "pre_select" => ["exclude from ilr reports", "exclude_from_ilr_export"]],
                'exclude_from_emails' => ['name' => 'Exclude From Emails', 'required' => false, "pre_select" => ["exclude from emails", "exclude_from_emails"]],
                'accessible_ui' => ['name' => 'Accessible UI', 'required' => false, "pre_select" => ["accessible ui", "accessible_ui"]],
                'account_type_id' => ['name' => 'Account Type ID', 'required' => false, "pre_select" => ["account type id", "account_type_id"]],
                'startup_instructions_sent' => ['name' => 'Startup Instructions Sent', 'required' => false, "pre_select" => ["startup instructions sent", "startup_instructions_sent"]],
                'position_ref' => ['name' => 'Position Ref', 'required' => false, "pre_select" => ["position ref", "position_ref"]],
                'watch' => ['name' => 'Watch', 'required' => false, "pre_select" => ["watch"]],
                'watch_id' => ['name' => 'Watch_id', 'required' => false, "pre_select" => ["watch_id"]],
            ];
        } else if ($type == 'lr') {
            return [
                'learning_resource_code' => ['name' => 'Learning Resource Code', "pre_select" => ["learning resource code", "code", "lesson code", "course code"]],
                'name' => ['name' => 'Learning Resouce', 'required' => true, "pre_select" => ["name", "learning resource", "lr", "lesson", "learning lesson", "course", "course name", "resource name", "lesson_name lesson name" ,"title"]],
                'type' => ['name' => 'Type', 'required' => true, "pre_select" => ["type", "lesson type", "lesson type", "course type", "Method ID"]],
                'category' => ['name' => 'Category', 'required' => false, "pre_select" => ["category", "lesson category", "course category"]],
                'keywords' => ['name' => 'Keywords', "pre_select" => ["keywords", "lesson keywords", "course keywords"]],
                'cost' => ['name' => 'Cost', "pre_select" => ["cost", "lesson cost", "course cost", "amount"]],
                'duration_hours' => ['name' => 'Hours', "pre_select" => ["hours", "lesson hours", "course hours"]],
                'duration_minutes' => ['name' => 'Minutes', "pre_select" => ["minutes", "lesson minutes", "course minutes"]],
                'pass_mark' => ['name' => 'Passmark', "pre_select" => ["passmark", "lesson passmark", "course passmark"]],
                'require_management_signoff' => ['name' => 'Require Sign Off', "pre_select" => ["require sign off", "require_management_signoff"]],
                'attached_file' => ['name' => 'Attached file', "pre_select" => ["attached file", "attached_file"]],
                'active' => ['name' => 'Active', "pre_select" => ["active", "status"]],
                'created_date' => ['name' => 'Created Date', "pre_select" => ["created date", "created at", "Create Dateá"]],
                'description' => ['name' => 'Description', "pre_select" => ["Description"]],
                'duration' => ['name' => 'Duration', "pre_select" => ["duration", "lesson duration", "course duration"]],
            ];
        } else if ($type == 'venue') {
            return [
                'name' => ['name' => 'Name', 'required' => true, "pre_select" => ["name", "venue name", "venue"]],
                'address' => ['name' => 'Address', 'required' => false, "pre_select" => ["address", "venue address"]],
                'postcode' => ['name' => 'Postcode', 'required' => false, "pre_select" => ["postcode", "venue postcode", "post code"]],
                'contact_number' => ['name' => 'Contact Number', 'required' => false, "pre_select" => ["contact number", "venue contact number", "mobile", "phone", "mobile number", "mobile no", "phone no"]],
                'contact_name' => ['name' => 'Contact Name', 'required' => false, "pre_select" => ["contact name", "venue contact name"]],
                'rating' => ['name' => 'Rating', 'required' => false, "pre_select" => ["rating", "venue rating"]],
                'capacity' => ['name' => 'Capacity', 'required' => false, "pre_select" => ["capacity", "venue capacity"]],
                'instructions' => ['name' => 'Instructions', 'required' => false, "pre_select" => ["instructions", "venue instructions"]],
            ];
        } else if ($type == 'learning_evidence') {
            return [
                'name' => ['name' => 'Name', 'required' => true, "pre_select" => ["name", "evidence name", "evidence", "schedule", "event", "event name", "schedule name"]],
                'type' => ['name' => "Event Type ('Lesson' or 'Meeting')", 'required' => false, "pre_select" => ["type", "event type", "evidence type", "schedule type"]],
                'lesson_name' => ['name' => "Linked lesson name or ID (will look up existing lesson with this name/ID or create new with same name as event)", 'required' => false, "pre_select" => ["lesson name", "lesson", "linked lesson", "linked lesson name"]],
                'start_date' => ['name' => 'From', 'required' => true, "pre_select" => ["start date", "from", "start", "from date"]],
                'end_date' => ['name' => 'To', 'required' => false, "pre_select" => ["end date", "to", "end", "to date"]],
                'description' => ['name' => 'Description', 'required' => false, "pre_select" => ["description", "event description", "lesson description", "schedule description"]],
                'category' => ['name' => 'Category', 'required' => true, "pre_select" => ["category", "event category", "lesson category", "schedule category"]],
                'venue' => ['name' => 'Venue', 'required' => false, "pre_select" => ["venue", "event venue", "schedule venue"]],
                'linked_programme' => ['name' => 'Linked programme', 'required' => false, "pre_select" => ["linked programme", "linked programme", "programme", "programmes"]],
                'linked_programme_citeria' => ['name' => 'Linked Programme Criteria (outcome > criteria; if multiple then divide by comma)', 'required' => false, "pre_select" => ["linked programme criteria", "linked programme citeria", "programme criteria"]],
                'zoom_teams_meeting' => ['name' => 'Zoom/Teams Meeting Address', 'required' => false, "pre_select" => ["zoom/teams meeting address", "zoom meeting", "teams meeting", "zoom", "teams"]],
                'cost' => ['name' => 'Cost', 'required' => false, "pre_select" => ["cost", "event cost", "schedule cost", "amount"]],
                'enrole_any_learner' => ['name' => 'Allow learner Enrollment', 'required' => false, "pre_select" => ["allow learner enrollment", "enrole any learner", "enrole learner"]],
                'manager_usernames' => ['name' => 'Trainer Usernames (divide by comma)', 'required' => false, "pre_select" => ["trainer usernames", "trainers", "trainer", "trainers usernames", "manager", "manager_username"]],
                'minclass' => ['name' => 'Min class size', 'required' => false, "pre_select" => ["min class size", "minclass"]],
                'maxclass' => ['name' => 'Max class size', 'required' => false, "pre_select" => ["max class size", "maxclass"]],
                'trainee_username' => ['name' => 'Trainee Username (create multiple records per course for user data)', 'required' => false, "pre_select" => ["trainee username", "trainee", "trainee_username", "learner", "learner username"]],
                'trainee_account' => ['name' => 'Trainee Account (Primary, Secondary, Tertiary - divide by comma)', 'required' => false, "pre_select" => ["trainee account", "trainee_account"]],
                'trainee_completion_status' => ['name' => 'Completion Status (Completed, Not Started or In Progress)', 'required' => false, "pre_select" => ["completion status", "trainee_completion_status", "status"]],
                'trainee_completed_at' => ['name' => 'Completed at', 'required' => false, "pre_select" => ["completed at", "trainee_completed_at", "completed", "completed date"]],
                'groups' => ['name' => 'Groups, separate by comma', 'required' => false, "pre_select" => ["groups", "group", "group name"]],
                'visit_type' => ['name' => 'Visit type (for meetings only)', 'required' => false, "pre_select" => ["visit type", "visit_type", "meeting type"]],
            ];
        }
    }
    public  static function cron($settings)
    {
        $app = AppFactory::create(); // Creating the Slim App instance
        $container = $app->getContainer();
        $TEMP_PATH = $settings["LMSTempPath"];
        $settings = $settings;
        self::$settings = $settings;
        $selfClass = new ImportController($container);
        Import::wizard()->cron()->chunk(50, function ($imports) use ($TEMP_PATH, $settings, $selfClass) {
            foreach ($imports as $import) {
                switch ($import->params['type']) {
                    case 'user':
                        $selfClass->userImport($import, $TEMP_PATH);
                        break;
                    case 'lr':
                        $selfClass->learningResourceImport($import, $settings);
                        break;
                    case 'venue':
                        $selfClass->venueImport($import, $settings);
                        break;
                    case 'learning_evidence':
                        $selfClass->learningEvidenceImport($import, $settings);
                        break;
                }
                $import->cron_run = 0;
                $import->save();
            }
        });
    }
    public static function userImport($import, $base_path)
    {
        $file = $base_path . $import->file;
        $php_excel = \PhpOffice\PhpSpreadsheet\IOFactory::load($file);
        $sheet = $php_excel->getActiveSheet();
        $rows = $sheet->getRowIterator(1);
        $fields = $import->params['maping'];
        $data = [];
        foreach ($rows as $row) {
            $row_i = $row->getRowIndex();
            $cells = $row->getCellIterator();
            $cells->setIterateOnlyExistingCells($row_i == 1);
            $rowData = [];
            foreach ($cells as $cell_i => $cell) {
                if ($row_i == 1) {
                    //$fields[$cell_i] = $cell->getCalculatedValue();
                } else {
                    if (!empty($fields[$cell_i])) {
                        $rowData[$fields[$cell_i]] = $cell->getCalculatedValue();
                    }
                }
            }
            // Check if all values in the row are empty
            if (!array_filter($rowData)) {
                continue; // Skip this iteration if all values are empty
            }
            $data[$row_i] = $rowData;
        }
        $importLog = new ImportLog();
        $importLog->file_path = $file;
        $importLog->file_name = $import->params['original_file'];
        $importLog->params = $import->params;
        $importLog->status = true;
        $importLog->is_processed = true;
        $importLog->save();
        $update_log = ['created' => 0, 'updated' => 0, 'skipped' => 0, 'skip_reason' => [],'deleted'=>0];
        $auth_user_id = $import->params['auth_user_id'];
        $user = User::find($auth_user_id);
        if($import->params['how_to_import'] == 'delete_accounts_listed' && !Tools::getConfig('sharedClients')){
            foreach ($data as $record) {
					$delete_user = \Models\User
						::where("username",$record['username'])
						->with('role')
                    ->first();
                if($delete_user){
						if (
							$delete_user->role &&
							!$delete_user->role->is_admin
						) {
							\Models\User::deleteUser($delete_user->id, self::$settings);
							$update_log['deleted']++;
                    }else{
                        $update_log['skipped']++;
                        //$update_log['skip_reason'][] = 'User is admin';
                    }
                }else{
                    $update_log['skipped']++;
                    //$update_log['skip_reason'][] = 'User not found';
                }
            }
            $importLog->logs = json_encode($update_log);
            $importLog->save();
            return $update_log;
        }

        if ($user->role->is_admin == 0 && $user->role->access_all_companies == 0 && $user->company_id) {
            $company_id = $user->company_id;
        }
        foreach ($data as $record) {
            if (LicenseFeatures::checkFeatureLimitReached($user, 'users') && \APP\Tools::getConfig('sharedClients')) {
                $update_log['skip_reason'][] = 'limit exceeded';
                $importLog->logs = json_encode($update_log);
                $importLog->save();
            } else {
                if ($user->role->is_manager && !isset($record['managers'])) {
                    $record['managers'] = $user->username;
                }
                if (isset($company_id)) {
                    $record['company_id'] = $company_id;
                    unset($record['company']);
                }

                    $log = self::insertUser($record, $import, $importLog);


                $update_log = [
                    'created' => $update_log['created'] + $log['created'],
                    'updated' => $update_log['updated'] + $log['updated'],
                    'skipped' => $update_log['skipped'] + $log['skipped'],
                    'skip_reason' => $update_log['skip_reason']
                ];
            }
            if (isset($log['skip_reason']) && $log['skip_reason']) {

                array_push($update_log['skip_reason'], $log['skip_reason']);
            }
        }
        $importLog->logs = json_encode($update_log);
        $importLog->save();
        return $update_log;
    }
    public static function logImportError($importLog, $record, $reason)
    {
        if ($importLog && $reason) {
            \Models\ImportLogError::create([
                'import_log_id' => $importLog->id,
                'data' => $record,
                'error_log' => $reason,
            ]);
        }
    }
    public static function insertUser($record, $import, $importLog = null)
    {
        if (!isset($record['email'])) {
            $reason = 'Email is required';
            self::logImportError($importLog, $record, $reason);
            return ['created' => 0, 'updated' => 0, 'skipped' => 1, 'skip_reason' => 'Email is required'];
        }
        if (isset($record['email']) && !filter_var($record['email'], FILTER_VALIDATE_EMAIL)) {
            $reason = 'Invalid Email';
            self::logImportError($importLog, $record, $reason);
            return ['created' => 0, 'updated' => 0, 'skipped' => 1, 'skip_reason' => 'Invalid Email'];
        }
        if (isset($record['postcode'])) {
            if (strlen($record['postcode']) > 10 || strlen($record['postcode']) < 4) {
                $reason = 'Invalid Postcode';
                self::logImportError($importLog, $record, $reason);
                return ['created' => 0, 'updated' => 0, 'skipped' => 1, 'skip_reason' => 'Invalid Postcode'];
            }
        }
        if (isset($record['phone'])) {
            if(!self::validatePhoneNumber($record['phone'])) {
                $reason = 'Invalid Phone Number';
                self::logImportError($importLog, $record, $reason);
                return ['created' => 0, 'updated' => 0, 'skipped' => 1, 'skip_reason' => 'Invalid Phone Number'];

            }
        }
        if (empty($record["username"])) {
            if (isset($record["email"])) {
                $record["username"] = $record["email"];
            }
            if (
                isset($record["usercode"]) &&
                \APP\Tools::getConfig('importUserCodeForMissingUsername')
            ) {
                $record["username"] = $record["usercode"];
            }
        }


        /**
         * uniqueUsernamePerUser and uniqueEmailUsers now can be switched on and off
         */
        $existing_user = false;
        if (isset($record['usercode'])) {
            $existing_user = User::where('id', '>', 0)
                ->where(function($query) use (&$record) {
                    $query
                        ->where('usercode', $record['usercode'])
                    ;
                    if (isset($record['email'])) {
                        $query = $query
                            ->orWhere('email', $record['email'])
                        ;
                    }
                    if (\APP\Tools::getConfig('userImportReplaceUsernameWithEmplyeeId')) {
                        $query = $query
                            ->orWhere("username", $record["usercode"])
                        ;
                        $record["username"] = $record["usercode"];
                    }
                })
            ;
        } else if (isset($record['username'])) {
            $existing_user = \Models\User
                ::where("username", $record["username"]);
        } else if (isset($record['email'])) {
            $existing_user = \Models\User
                ::where("email", $record["email"]);
        } else {
            // uniqueEmailPerUser and uniqueUsernamePerUser is set to false, user can be added regardless
        }



        if ($existing_user) {
            if (
                isset($record['company_id']) &&
                \APP\Tools::getConfig('sharedClients')
            ) {
                $existing_user = $existing_user
                    ->where('company_id', $record['company_id'])
                ;
            }
            $existing_user = $existing_user
                ->get();
        }

        $user = new \Models\User;
        $user->creation_notes = 'user created using import file function, either by nightly import or manual';
        $is_existing_user = false;

        if ($existing_user) {
            foreach ($existing_user as $e_user) {

                if ($e_user && $import->params['how_to_import'] == "create_new_only") {
                    $reason = 'User already exists';
                    self::logImportError($importLog, $record, $reason);
                    return ['created' => 0, 'updated' => 0, 'skipped' => 1, 'skip_reason' => 'User already exists'];
                }
                if (!$e_user && $import->params['how_to_import'] == 'update_only') {
                    $reason = 'User does not exist';
                    self::logImportError($importLog, $record, $reason);
                    return ['created' => 0, 'updated' => 0, 'skipped' => 1, "skip_reason" => "User does not exist"];
                }
                if (!$e_user && $import->params['how_to_import'] == 'disable_accounts_listed') {
                    $reason = 'User does not exist';
                    self::logImportError($importLog, $record, $reason);
                    return ['created' => 0, 'updated' => 0, 'skipped' => 1, "skip_reason" => "User does not exist"];
                }
                if ($e_user && $import->params['how_to_import'] == 'disable_accounts_not_listed') {
                    $e_user->status = 0;
                    $e_user->save();
                    $reason = 'User disabled';
                    self::logImportError($importLog, $record, $reason);
                    return ['created' => 0, 'updated' => 1, 'skipped' => 0, "skip_reason" => false];
                }

                $user = $e_user;
                $is_existing_user = true;
                break;
            }
        }


        $foreign_fields = [
            "designation" => "\Models\Designation",
            "country" => "\Models\Country",
            "company" => "\Models\Company",
            "department" => "\Models\Department",
            "location" => "\Models\Location",
            "city" => "\Models\City",
            "role" => "\Models\Role",
            "group" => "\Models\Group",
            "staff_type" => "\Models\SmcrStaffType",
            "account_type" => "\Models\Picklist",
        ];


        foreach ($foreign_fields as $foreign_field => $foreign_field_model) {
            if (isset($record[$foreign_field])) {

                if ($foreign_field == 'role') {
                    $foreign_field_obj = $foreign_field_model
                        ::whereRaw('TRIM(name) = ?', [trim($record[$foreign_field])])
                        ->where('status', true);
                } else if ($foreign_field == "department") {
                    if (isset($record['company_id'])) {
                        $foreign_field_obj = $foreign_field_model::where("name", $record[$foreign_field])->where('company_id', $record['company_id']);
                    } else {
                        $foreign_field_model = null;
                    }
                } else if ($foreign_field == 'account_type') {
                    $foreign_field_obj = $foreign_field_model
                        ::where("value", $record[$foreign_field])
                        ->where('type', 'account_type');
                } else {
                    $foreign_field_obj = $foreign_field_model
                        ::where("name", $record[$foreign_field]);
                }

                $foreign_field_obj = $foreign_field_obj
                    ->first();



                if ($foreign_field_obj) {
                    $record[$foreign_field . "_id"] = $foreign_field_obj->id;
                } else {
                    // Does not exists, create new, but not for role, managers and staff_type, and account_type!
                    if (
                        $foreign_field != 'role' &&
                        $foreign_field != 'managers' &&
                        $foreign_field != 'staff_type' &&
                        $foreign_field != 'account_type'
                    ) {
                        $foreign_field_obj = new $foreign_field_model;
                        $foreign_field_obj->name = $record[$foreign_field];
                        $foreign_field_obj->status = true;
                        if ($foreign_field == 'department' && isset($record["company_id"])) {
                            $foreign_field_obj->company_id = $record["company_id"];
                        }
                        if ($foreign_field == 'city' && isset($record["country_id"])) {
                            $foreign_field_obj->country_id = $record["country_id"];
                        }
                        $foreign_field_obj->save();
                        $record[$foreign_field . "_id"] = $foreign_field_obj->id;
                    }
                }
            }
        }

        if ($user->id && isset($record['employee_status']) && $record['employee_status'] == 'Terminated') {
            UserSubDepartment::where('user_id', $user->id)->delete();
        }

        if (
            isset($record["username"]) ||
            isset($record["email"])
            || isset($record["usercode"])
        ) {

            // if no e-mail is given and configuration option "allowEmptyEmailImport" is enabled, generate e-mail.

            if (
                empty($record["email"]) &&
                \APP\Tools::getConfig('allowEmptyEmailImport') &&
                $user->email == ""
            ) {
                $record["email"] = \APP\Tools::setUniqueEmail();
            }
            $fields = [
                "username", "usercode", "fname", "lname", "email", "phone",
                "designation_id", "country_id", "company_id", "department_id",
                "location_id", "city_id", "role_id", "description",
                "expiration_dt", "registration_dt", "ULN", "staff_type_id",
                "status", "account_type_id", "emergency_name", "emergency_relationship",
                "emergency_contact_numbers", "visa_length", "visa_number", "total_resources",
                "total_resources_smcr", "altusercode", "skype_id", "zoom_id", "teams_id",
                "email2", "report_to", "week_hours", "school", "visa_date",
                "exclude_from_reports", "exclude_from_ilr_export", "exclude_from_emails",
                "discount_percentage", "UKPRN", "LearnRefNumber", "PrevLearnRefNumber",
                "PrevUKPRN", "PMUKPRN", "DateOfBirth", "Ethnicity", "Sex", "LLDDHealthProb",
                "NINumber", "PriorAttainLegacy", "PriorAttain", "Accom", "ALSCost",
                "PlanLearnHours", "PlanEEPHours", "MathGrade", "EngGrade", "PostcodePrior",
                "Postcode", "AddLine1", "AddLine2", "AddLine3", "AddLine4",
                "ContactPreference", "LLDDandHealthProblem", "LearnerFAM",
                "ProviderSpecLearnerMonitoring", "LearnerHE", "LearningDelivery", "CampId",
                "OTJHours", "accessible_ui", "startup_instructions_sent", "position_ref",
                "watch", "watch_id"
            ];
            if (!isset($record['PrevUKPRN'])) {
                $record['PrevUKPRN'] = null;
                $user->PrevUKPRN = null;
            }
            if (
                empty($record['ULN']) ||
                $record['ULN'] == 'null'
            ) {
                $record['ULN'] = null;
                $user->ULN = null;
            }
            // Default role to learner, if user has no role
            if (!$user->role_id) {
                $user->role_id = \APP\Tools::getConfig('defaultRegisterRole');
            }


            foreach ($fields as $field) {
                if (isset($record[$field])) {
                    $user->$field = $record[$field];
                }
            }
            $user->LLDDandHealthProblem = \APP\Tools::ilrJsonEncod($record, 'LLDDandHealthProblem');
            // encode json object to json string, for ILR entries
            //$user->ContactPreference = \APP\Tools::ilrJsonEncod($data, 'ContactPreference');
            $user->LLDDandHealthProblem = \APP\Tools::ilrJsonEncod($record, 'LLDDandHealthProblem');
            $user->LearnerFAM = \APP\Tools::ilrJsonEncod($record, 'LearnerFAM');

            $user->ProviderSpecLearnerMonitoring = \APP\Tools::ilrJsonEncod($record, 'ProviderSpecLearnerMonitoring');
            $user->LearnerEmploymentStatus = \APP\Tools::ilrJsonEncod($record, 'LearnerEmploymentStatus');
            $user->LearnerHE = \APP\Tools::ilrJsonEncod($record, 'LearnerHE');
            $user->LearningDelivery = \APP\Tools::ilrJsonEncod($record, 'LearningDelivery');
            $user->LearnerDestinationandProgression = \APP\Tools::ilrJsonEncod($record, 'LearnerDestinationandProgression');


            // Convert date to safe date
            if (!empty($record["DateOfBirth"])) {
                $user->DateOfBirth = \Carbon\Carbon::parse($record["DateOfBirth"]);
            }

            if (!empty($record["visa_date"])) {
                $user->visa_date = \Carbon\Carbon::parse($record["visa_date"]);
            }

            if (!empty($record["expiration_dt"])) {
                $user->expiration_dt = \Carbon\Carbon::parse($record["expiration_dt"]);
            } else {
                $user->expiration_dt = null;
            }

            if (!empty($record["registration_dt"])) {
                $user->registration_dt = \Carbon\Carbon::parse($record["registration_dt"]);
            } else {
                $user->registration_dt = null;
            }


            if (isset($record['usercode'])) {
                $user->ukg_employee_number = $record['usercode'];
            }

            if ($user->staff_type_id) {
                $user->staff_type_assigned = \Carbon\Carbon::now();
            }

            if (isset($record["password"]) && $record["password"]) {
                $user->password = password_hash($record["password"], PASSWORD_BCRYPT, ['cost' => 12]);
            } else {
                if (!$is_existing_user) {
                    $user->password = "";
                }
            }

            if (
                $is_existing_user &&
                $user->status == 0
            ) {
                $user->status = \APP\Tools::getConfig('reEnableUsersOnImport');
            } else {
                $user->status = 1;
            }

            // perform custom actions if department name matches ones specified in configuration options.
            if (
                $user->department_id
            ) {
                $user_department = \Models\Department::find($user->department_id);
                // Set user status to 0 if department name for imported user is the same as DisableUserDepartmentOnImport in configuration options!
                if ($user_department) {
                    if (
                        \APP\Tools::getConfig('DisableUserDepartmentOnImport') &&
                        \APP\Tools::getConfig('DisableUserDepartmentOnImport') == $user_department->name
                    ) {
                        $user->status = 0;
                        Schedule::userStatusUpdate($user->id, NULL, false);
                    }

                    // Set user status to 1 if department name for imported user is the same as EnableUserDepartmentOnImport in configuration options!
                    if (
                        \APP\Tools::getConfig('EnableUserDepartmentOnImport') &&
                        \APP\Tools::getConfig('EnableUserDepartmentOnImport') == $user_department->name
                    ) {
                        $user->status = 1;
                    }
                }
            }


            // Add default UKPRN
            if (!$user->UKPRN) {
                $user->UKPRN = \APP\Tools::getConfig('defaultUKPRN');
            }
            if (!$user->PrevUKPRN) {
                $user->PrevUKPRN = \APP\Tools::getConfig('defaultPrevUKPRN');
            }
            if (!$user->LearnRefNumber) {
                $user->LearnRefNumber = \APP\Tools::getLearnerRefNum();
            }
            $user->saveWithoutEvents();

            // If sub departments are given in spreadsheet, import them!
            $parent_dep_id = $user->department_id;
            foreach ($record as $k => $v) {
                if (
                    strpos($k, 'department_sub_2') !== false && // only import Organization Name as subdepartment
                    $user &&
                    $user->id &&
                    $user->company_id &&
                    $user->company_id > 0 &&
                    $parent_dep_id &&
                    $parent_dep_id > 0
                ) {
                    $sub_dep_id = \Models\Department::addSubItem($v, $parent_dep_id, $user->company_id);
                    // add UserSubDepartment entry, if does not exist!
                    if (
                        $sub_dep_id &&
                        $sub_dep_id > 0
                    ) {
                        // delete previous subdepartment links!
                        \Models\UserSubDepartment
                            ::where('user_id', $user->id)
                            ->where('department_id', '!=', $sub_dep_id)
                            ->delete();
                        \Models\UserSubDepartment::firstOrCreate(
                            [
                                'user_id' => $user->id,
                                'department_id' => $sub_dep_id
                            ]
                        );
                    }
                }
            }


            if (
                isset($record['send_instruction_email']) &&
                $record['send_instruction_email']
            ) {
                \APP\Email::sendInstructionEmail($user->id);
            }



            if (isset($record["group_id"])) {
                \Models\GroupUser::firstOrCreate(
                    [
                        'user_id' => $user->id,
                        'group_id' => $record["group_id"]
                    ],
                    [
                        'status' => true
                    ]
                );
                // Assign user all the modules in group
                \Models\GroupLearningModule::AssingToUser($record["group_id"], $user);
            }

            if (
                isset($record["managers"]) &&
                $record["managers"]
            ) {
                $record["managers"] = preg_replace('/\s+/', '', $record["managers"]);
                $managers_usernames = explode(',', $record["managers"]);
                if (is_array($managers_usernames)) {
                    $managers_list = \Models\User
                        ::where('status', true)
                        ->whereIn('username', $managers_usernames)
                        ->whereIn(
                            'role_id',
                            \Models\Role
                                ::select('id')
                                ->where('status', true)
                                ->where('is_manager', true)
                                ->get()
                        )
                        ->get();
                    // Delete managers of user when deleteAndLinkManagersOnUserImport is true
                    if (\APP\Tools::getConfig('deleteAndLinkManagersOnUserImport') == true) {
                        $arrUserManagers = \Models\ManagerUser::where("user_id", $user->id)->get();
                        if ($arrUserManagers) {
                            \Models\ManagerUser::where("user_id", $user->id)->delete();
                        }
                    }
                    foreach ($managers_list as $key => $assign_manager) {
                        $flight = \Models\ManagerUser::firstOrCreate(
                            [
                                'user_id' => $user->id,
                                'manager_id' => $assign_manager->id
                            ]
                        );
                    }
                }
            }
            if (!$is_existing_user) {
                return ['updated' => 0, 'created' => 1, 'skipped' => 0];

                \APP\Tools::updateLearnerRefNum();
            } else {
                return ['updated' => 1, 'created' => 0, 'skipped' => 0];
            }
        } else {
            $reason = 'Username or email is required / Missing required user identifiers';
            self::logImportError($importLog, $record, $reason);
            return ['updated' => 0, 'created' => 0, 'skipped' => 1];
        }
    }



private function learningResourceImport($import, $settings)
{
    $file = $settings["LMSTempPath"] . $import->file;
    $ext = pathinfo($file, PATHINFO_EXTENSION);
    $php_excel = \PhpOffice\PhpSpreadsheet\IOFactory::load($file);
    $sheet = $php_excel->getActiveSheet();
    $rows = $sheet->getRowIterator(1);
    $fields = $import->params['maping'];
    $data = [];

    foreach ($rows as $key => $row) {
        $row_i = $row->getRowIndex();
        $cells = $row->getCellIterator();
        $cells->setIterateOnlyExistingCells($row_i == 1);
        $rowData = [];

        foreach ($cells as $cell_i => $cell) {
            if ($row_i == 1) {
                // Header row (skip)
            } else {
                if (!empty($fields[$cell_i])) {
                    $value = $cell->getCalculatedValue();
                    if ($fields[$cell_i] === 'created_date' && $ext !== 'csv') {
                        if (is_numeric($value)) {
                            $value = date('Y-m-d', \PhpOffice\PhpSpreadsheet\Shared\Date::excelToTimestamp($value));
                        } else {
                            $value = date('Y-m-d', strtotime($value));
                        }
                    }
                    $rowData[$fields[$cell_i]] = $value;
                }
            }
        }

        if (!array_filter($rowData)) continue;
        $data[$row_i] = $rowData;
    }

    // Save initial import log
    $importLog = new \Models\ImportLog();
    $importLog->file_path = $file;
    $importLog->file_name = $import->params['original_file'];
    $importLog->params = $import->params;
    $importLog->status = true;
    $importLog->is_processed = true;
    $importLog->save();

    $update_log = ['created' => 0, 'updated' => 0, 'skipped' => 0, 'skip_reason' => []];
    $total_inserted = 0;
    $total_skipped = 0;
    $skipped_entries = [];

    $recordCount = 0;
    $startMemory = memory_get_usage(true);
    
    foreach ($data as $record) {
        if (!empty($record)) {
            $recordCount++;
            
            // Memory monitoring for large imports
            if ($recordCount % 10 === 0) {
                $currentMemory = memory_get_usage(true);
                $memoryUsageMB = round($currentMemory / (1024 * 1024), 2);
                $peakMemoryMB = round(memory_get_peak_usage(true) / (1024 * 1024), 2);
                
                \Models\Log::addEntry(false, false, 200, [
                    'type' => 'import-memory',
                    'message' => "Processing record {$recordCount}: Memory {$memoryUsageMB}MB (Peak: {$peakMemoryMB}MB)"
                ]);
                
                // Force garbage collection every 50 records for memory cleanup
                if ($recordCount % 50 === 0) {
                    gc_collect_cycles();
                    
                    $afterGcMemory = memory_get_usage(true);
                    $memoryFreedMB = round(($currentMemory - $afterGcMemory) / (1024 * 1024), 2);
                    
                    if ($memoryFreedMB > 0) {
                        \Models\Log::addEntry(false, false, 200, [
                            'type' => 'import-memory',
                            'message' => "Garbage collection freed {$memoryFreedMB}MB after record {$recordCount}"
                        ]);
                    }
                }
            }
            
            $log = self::insertLearningResource($record, $settings, $import->params['auth_user_id'], $import);

            $update_log['created'] += $log['created'] ?? 0;
            $update_log['updated'] += $log['updated'] ?? 0;
            $update_log['skipped'] += $log['skipped'] ?? 0;

            $total_inserted += $log['created'] ?? 0;
            $total_skipped += $log['skipped'] ?? 0;

            if (!empty($log['skip_reason'])) {
                $update_log['skip_reason'][] = $log['skip_reason'];
                $skipped_entries[] = [
                    'code' => $log['code'] ?? 'N/A',
                    'reason' => $log['skip_reason']
                ];

                // ✅ Insert into import_log_errors table
                \Models\ImportLogError::create([
                    'import_log_id' => $importLog->id,
                    'data' => $record,
                    'error_log' => $log['skip_reason']
                ]);
            }
            
            // Clear record data to free memory
            unset($record, $log);
        }
    }
    
    // Save final logs after processing all records
    $importLog->logs = json_encode($update_log);
    $importLog->save();

    // Final summary output
    \Models\Log::addEntry(false, false, 200, [
        'type' => 'import-complete',
        'message' => "Learning resource import completed: {$total_inserted} created, {$total_skipped} skipped"
    ]);
}






private static function insertLearningResource($record, $settings, $user_id, $import)
{
    $rejected = false;
    $inserted = false;
    $entry = $record;
    
    // Generate unique code if not provided or if it's empty
    if (empty($entry['learning_resource_code'])) {
        // Generate a unique code based on name and timestamp
        $nameSlug = substr(preg_replace('/[^A-Za-z0-9-]/', '', str_replace(' ', '-', $entry['name'] ?? 'module')), 0, 20);
        $code = strtoupper($nameSlug . '-' . uniqid());
    } else {
        $code = $entry['learning_resource_code'];
    }

    $checkExisting = LearningModule::where('name', $entry['name'])->get();

    if ($import->params['how_to_import'] === "disable_accounts_listed" && !$checkExisting->count()) {
        $result = ['updated' => 0, 'created' => 0, 'skipped' => 1, 'skip_reason' => 'Module does not exist', 'code' => $code];
        return $result;
    }

    if ($import->params['how_to_import'] === "disable_accounts_listed" && $checkExisting->count()) {
        LearningModule::where('name', trim($entry['name']))->update(['status' => 0]);
        $result = ['updated' => $checkExisting->count(), 'created' => 0, 'skipped' => 0, 'code' => $code];
        return $result;
    }

    // Ensure required fields are not null (database constraints)
    if (!isset($entry['keywords']) || $entry['keywords'] === null || $entry['keywords'] === '') {
        $entry['keywords'] = '';
    }
    
    // Ensure duration fields are not null - convert singular to plural field names
    if (!isset($entry['duration_hour']) || $entry['duration_hour'] === null || $entry['duration_hour'] === '') {
        $entry['duration_hour'] = 0;
    }
    if (!isset($entry['minutes']) || $entry['minutes'] === null || $entry['minutes'] === '') {
        $entry['minutes'] = 0;
    }
    
    // Map to the correct field names expected by the model
    $entry['duration_hours'] = $entry['duration_hour'];
    $entry['duration_minutes'] = $entry['minutes'];
    
    // Prepare entry data - skip material setup for e-learning as it will be handled separately
    if (!empty($entry["attached_file"]) && strtoupper($entry["type"]) !== 'ELEARNING') {
        $entry['material'] = [
            "sessions" => [],
            "link" => $entry["attached_file"]
        ];
    }

    // Add pass mark for non-e-learning types (e-learning handled separately above)
    if (!empty($entry["pass_mark"]) && strtoupper($entry["type"]) !== 'ELEARNING') {
        // Initialize material array if not already set
        if (!isset($entry['material'])) {
            $entry['material'] = ["sessions" => []];
        }
        $entry['material']["min_passing_percentage"] = $entry["pass_mark"];
    }

    // Get module type
    $type = \Models\LearningModuleType::where(DB::raw("LOWER(name)"), strtolower($entry["type"]))->first();

    if (!$type && strtoupper($entry["type"]) === 'ELEARNING') {
        $type = \Models\LearningModuleType::where('slug', 'e_learning')->first();
    }

    if (!$type && strtoupper($entry["type"]) === 'CLASSROOM') {
        if (in_array('classroom', explode(',', \APP\Tools::getConfig('disableResourceTypes')))) {
            $result = ['updated' => 0, 'created' => 0, 'skipped' => 1, 'skip_reason' => 'classroom is a disabled resource type', 'code' => $code];
            return $result;
        } else {
            $type = \Models\LearningModuleType::where('slug', 'classroom')->first();
        }
    }

    if (!$type) {
        $result = ['updated' => 0, 'created' => 0, 'skipped' => 1, 'skip_reason' => 'No type found', 'code' => $code];
        return $result;
    }

    // Required file for certain types
    if (
        in_array($type->slug, ['youtube', 'zoom_meeting', 'microsoft_teams']) &&
        empty($entry['attached_file'])
    ) {
        $result = ['updated' => 0, 'created' => 0, 'skipped' => 1, 'skip_reason' => 'No attached file', 'code' => $code];
        return $result;
    }

    // Handle e-learning with attached file - download and prepare for SCORM processing
    if ($type->slug === 'e_learning' && !empty($entry['attached_file'])) {
        $downloadedFile = self::downloadAttachedFile($entry['attached_file'], $settings);
        if ($downloadedFile) {
            // Set up material structure for e-learning with zip file
            $entry['material'] = [
                "sessions" => [],
                "zip_file" => $downloadedFile,
                "scorm_standard" => "2", // Default to SCORM 1.2 (2 = 1.2, 3 = 2004)
                "course_complete_status" => "1"
            ];
            
            // Add pass mark if provided
            if (!empty($entry["pass_mark"])) {
                $entry['material']["min_passing_percentage"] = $entry["pass_mark"];
            }
        } else {
            $result = ['updated' => 0, 'created' => 0, 'skipped' => 1, 'skip_reason' => 'Failed to download attached file', 'code' => $code];
            return $result;
        }
    }

    // Validate YouTube URL
    if ($type->slug === 'youtube') {
        $rx = '~
            ^(?:https?://)?(?:www\.)?(?:youtube\.com|youtu\.be)/watch\?v=([^&]+)
        ~x';
        if (!preg_match($rx, $entry['attached_file'])) {
            $result = ['updated' => 0, 'created' => 0, 'skipped' => 1, 'skip_reason' => 'Invalid youtube link', 'code' => $code];
            return $result;
        }
    }

    // Add type, creator, etc.
    $entry['type_id'] = $type->slug !== 'classroom' ? $type->id : null;
    $entry['created_by'] = $user_id;
    $entry['code'] = $code;

    if (!empty($entry['created_date'])) {
        $entry['created_at'] = $entry['created_date'];
    }

    if (!empty($entry['active'])) {
        $entry['status'] = strtolower($entry['active']) === 'yes' ? 1 : 0;
    }

    if (!empty($entry['duration_hour']) && $type->slug !== 'classroom') {
        $durations = self::parseDuration($entry['duration_hour']);
        $entry['hours'] = $durations['hours'] ?? 0;
        $entry['minutes'] = $durations['minutes'] ?? 0;
    }

    if ($type->slug === 'classroom') {
        $entry['is_course'] = 1;
        $entry['created_by_event'] = 1;
    }

    if ($settings['sharedClients']) {
        $user = User::find($user_id);
        if ($user->role->is_admin == 0 && $user->role->access_all_companies == 0 && $user->company_id) {
            $entry['company_id'] = $user->company_id;
        }
    }

    // Check if module exists
    $exists = LearningModule::where('code', $code)->exists();
    if ($exists && $type->slug === 'classroom') {
        $learning = LearningModule::where('code', $code)->first();
        self::importEventAndMapLesson($learning->id, $entry);
        $result = ['updated' => 0, 'created' => 0, 'skipped' => 1, 'skip_reason' => 'Module Already Exists', 'code' => $code];
        return $result;
    }

    if (!$exists) {
        // Handle categories
        if (!isset($entry['category']) || empty($entry['category'])) {
            $generated = self::generateCategoryUsingGPT($record);
                if (empty($generated)) {
                    $generated = 'Others';
                //$result = ['updated' => 0, 'created' => 0, 'skipped' => 1, 'skip_reason' => 'No category found/AI generated category is empty', 'code' => $code];
                //return $result;
            }
            $entry['category'] = $generated;
        }

        $category = \Models\LearningModuleCategory::firstOrCreate(['name' => $entry['category']], ['status' => 1]);
        $entry['category_id'] = $category->id;

        // Save module
        $learning = \Models\LearningModule::insertImportModule($entry, $settings, false);

        if (!empty($learning->id)) {
            if ($type->slug === 'classroom') {
                self::importEventAndMapLesson($learning->id, $entry);
            }

            $result = ['updated' => 0, 'created' => 1, 'skipped' => 0, 'code' => $code];
            return $result;
        } else {
            $result = ['updated' => 0, 'created' => 0, 'skipped' => 1, 'skip_reason' => 'Module not created', 'code' => $code];
            return $result;
        }
    } else {
        $result = ['updated' => 0, 'created' => 0, 'skipped' => 1, 'skip_reason' => 'Module Already Exists', 'code' => $code];
        return $result;
    }
}


    public static function venueImport($record, $settings)
    {
        $file = $settings["LMSTempPath"] . $record->file;
        $php_excel = \PhpOffice\PhpSpreadsheet\IOFactory::load($file);
        $sheet = $php_excel->getActiveSheet();
        $rows = $sheet->getRowIterator(1);
        $fields = $record->params['maping'];
        $data = [];
        foreach ($rows as $row) {
            $row_i = $row->getRowIndex();
            $cells = $row->getCellIterator();
            $cells->setIterateOnlyExistingCells($row_i == 1);
            $rowData = [];
            foreach ($cells as $cell_i => $cell) {
                if ($row_i == 1) {
                    //$fields[$cell_i] = $cell->getCalculatedValue();
                } else {
                    if (!empty($fields[$cell_i])) {
                        $rowData[$fields[$cell_i]] = $cell->getCalculatedValue();
                    }
                }
            }
            // Check if all values in the row are empty
            if (!array_filter($rowData)) {
                continue; // Skip this iteration if all values are empty
            }
            $data[$row_i] = $rowData;
        }
        $importLog = new ImportLog();
        $importLog->file_path = $file;
        $importLog->file_name = $record->params['original_file'];
        $importLog->params = $record->params;
        $importLog->status = true;
        $importLog->is_processed = true;
        $update_log = [
            'n_record_updated' => 0,
            'n_record_inserted' => 0,
            'n_record_rejected' => 0,
            'n_record_existing' => 0,
            'n_record_disabled' => 0,
            'n_record_deleted' => 0,
            'message' => []
        ];
        foreach ($data as $row) {
            $update_log = self::insertVenue($row, $update_log, $record);
        }
        $importLog->logs = json_encode($update_log);
        $importLog->save();
    }
    public static function insertVenue($entry, $n_records, $import)
    {
        if (Str::length(trim($entry['name'])) > 0) {
            if ($import->params['how_to_import'] == "create_new_only") {
                $venue = \Models\Venue::where('name', $entry['name'])->first();
                $n_records['n_record_rejected']++;
                array_push($n_records['message'], 'Venue already exists');
                return $n_records;
            }
            if ($import->params['how_to_import'] == "update_only") {
                $venue = \Models\Venue::where('name', $entry['name'])->first();
                $n_records['n_record_rejected']++;
                array_push($n_records['message'], 'Venue does not exist');
                return $n_records;
            }
            if ($import->params['how_to_import'] == "disable_accounts_listed") {
                $venue = \Models\Venue::where('name', $entry['name'])->first();
                if ($venue) {
                    $venue->status = 0;
                    $venue->save();
                    $n_records['n_record_updated']++;
                    return $n_records;
                } else {
                    $n_records['n_record_rejected']++;
                    array_push($n_records['message'], 'Venue does not exist');
                    return $n_records;
                }
            }
            if ($import->params['how_to_import'] == 'update_only') {
                $venue = \Models\Venue::where('name', $entry['name'])->first();
                $n_records['n_record_updated']++;
            } else if ($import->params['how_to_import'] == 'create_new_only') {
                $venue = new \Models\Venue;
                $n_records['n_record_inserted']++;
            } elseif ($import->params['how_to_import'] == 'create_and_update_existing') {
                $venue = \Models\Venue::where('name', $entry['name'])->first();
                if (!$venue) {

                    $venue = new \Models\Venue;
                    $n_records['n_record_inserted']++;
                } else {
                    $n_records['n_record_updated']++;
                }
            } else {
                $venue = new \Models\Venue;
                $n_records['n_record_inserted']++;
            }
            $venue->name = $entry['name'];
            $venue->address = isset($entry['address']) ? $entry['address'] : NULL;
            $venue->postcode = isset($entry['postcode']) ? $entry['postcode'] : NULL;
            $venue->contact_number = isset($entry['contact_number']) ? $entry['contact_number'] : NULL;
            $venue->contact_name = isset($entry['contact_name']) ? $entry['contact_name'] : NULL;
            $venue->rating = isset($entry['rating']) ? $entry['rating'] : NULL;
            $venue->capacity = isset($entry['capacity']) ? $entry['capacity'] : NULL;
            $venue->instructions = isset($entry['instructions']) ? $entry['instructions'] : NULL;
            $venue->status = 1;
            $venue->created_at = \Carbon\Carbon::now();
            $venue->updated_at = \Carbon\Carbon::now();
            $venue->save();
        } else {
            if (Str::length(trim($entry['address'])) > 0 || Str::length(trim($entry['post_code'])) > 0 || Str::length(trim($entry['contact_number'])) > 0 || Str::length(trim($entry['contact_name'])) > 0 || Str::length(trim($entry['rating'])) > 0 || Str::length(trim($entry['capacity'])) > 0) {
                $n_records['n_record_rejected']++;
                array_push($n_records['message'], 'Venue name is empty');
            }
        }

        $n_records['log'][] = [
            'record' => [
                'name' => $entry['name'],
                'address' => $entry['address'],
                'post_code' => $entry['postcode'],
                'contact_number' => $entry['contact_number'],
                'contact_name' => $entry['contact_name'],
                'rating' => $entry['rating'],
                'capacity' => $entry['capacity'],
            ]
        ];
        return $n_records;
    }

    public function learningEvidenceImport($import, $settings)
    {
        $file = $settings["LMSTempPath"] . $import->file;
        $php_excel = \PhpOffice\PhpSpreadsheet\IOFactory::load($file);
        $sheet = $php_excel->getActiveSheet();
        $rows = $sheet->getRowIterator(1);
        $fields = $import->params['maping'];
        $data = [];
        foreach ($rows as $row) {
            $row_i = $row->getRowIndex();
            $cells = $row->getCellIterator();
            $cells->setIterateOnlyExistingCells($row_i == 1);
            $rowData = [];
            foreach ($cells as $cell_i => $cell) {
                if ($row_i == 1) {
                    //$fields[$cell_i] = $cell->getCalculatedValue();
                } else {
                    if (!empty($fields[$cell_i])) {
                        $rowData[$fields[$cell_i]] = $cell->getCalculatedValue();
                    }
                }
            }
            // Check if all values in the row are empty
            if (!array_filter($rowData)) {
                continue; // Skip this iteration if all values are empty
            }
            $data[$row_i] = $rowData;
        }
        $importLog = new ImportLog();
        $importLog->file_path = $file;
        $importLog->file_name = $import->params['original_file'];
        $importLog->params = $import->params;
        $importLog->status = true;
        $importLog->is_processed = true;
        $update_log = [
            'n_record_updated' => 0,
            'n_record_inserted' => 0,
            'n_record_rejected' => 0,
            'n_record_existing' => 0,
            'n_record_disabled' => 0,
            'n_record_deleted' => 0,
            'message' => []
        ];
        foreach ($data as $key => $entry) {
            $this->insertLearningEvidence($entry, $import, $update_log, $settings);
        }

        $importLog->logs = json_encode($update_log);
        $importLog->save();
    }

    public function insertLearningEvidence($entry, $import, &$update_log, $settings)
    {
        if (isset($entry['start_date'])) {
            $entry['start_date'] = ImportUserController::fromExcelToLinux($entry['start_date']); //convert excel date to php date format
            $entry['start_date'] = gmdate("Y-m-d\TH:i:s\Z", $entry['start_date']);
        }
        if (isset($entry['end_date'])) {
            $entry['end_date'] = ImportUserController::fromExcelToLinux($entry['end_date']);
            $entry['end_date'] = gmdate("Y-m-d\TH:i:s\Z", $entry['end_date']);
        }
        DB::beginTransaction();
        if (!(isset($entry["name"]))) {
            array_push($update_log['message'], 'Schedule name is empty');
            $update_log['n_record_rejected']++;
            return;
        }
        // for case with missformated excel file, when empty table cells are "", not null
        if ($entry["name"] == "") {
            array_push($update_log['message'], 'Schedule name is empty');
            $update_log['n_record_rejected']++;
            return;
        }
        $entry["name"] = trim($entry["name"]);

        // Default type to lesson
        if (empty($entry["type"])) {
            $entry["type"] = 'lesson';
        }
        $entry["type"] = strtolower($entry["type"]);

        $entry = \Models\Schedule::setAllDayEventTime($entry);

        //Check category exist or not and create or updtae category Here
        if (!empty($entry["category"])) {
            $category = \Models\LearningModuleCategory
                ::where(DB::raw("LOWER(name)"), strtolower($entry["category"]))
                ->first();
            if ($category) {
                $category->status = true;
                $category->save();
            } else {
                $category = new \Models\LearningModuleCategory();
                $category->status = true;
                $category->name = $entry["category"];
                $category->save();
            }
            $entry["category_id"] = $category->id;
        } else {
            $entry["category_id"] = null;
        }


        // Create new lesson whenever event is created.
        if ($entry["type"] == 'lesson') {
            $entry["lesson_id"] = 'new';

            // Lesson name or id provided!
            if (
                isset($entry["lesson_name"]) &&
                $entry["lesson_name"]
            ) {
                $lesson = \Models\LearningModule
                    ::where('status', true)
                    ->where('is_course', 1);
                if (is_numeric($entry["lesson_name"])) {
                    $lesson = $lesson
                        ->where('id', intval($entry["lesson_name"]));
                } else {
                    $lesson = $lesson
                        ->where('name', $entry["lesson_name"]);
                }
                $lesson = $lesson
                    ->first();
                if ($lesson) {
                    $entry["lesson_id"] = $lesson->id;
                }
            }
        }

        if (
            isset($entry["visit_type"]) &&
            $entry["type"] == 'meeting'
        ) {
            $visit_type = \Models\ScheduleVisitType
                ::where('name', trim($entry["visit_type"]))
                ->where('status', true)
                ->first();
            if ($visit_type) {
                $entry['visit_type_id'] = $visit_type->id;
            }
        }
        //Create new event, check if there is already existing event with same name/date/duration/other parameters!
        $check_event = \Models\Schedule
            ::where('name', $entry['name']);
        if (isset($entry["start_date"])) {
            $check_event = $check_event->where('start_date', $entry["start_date"]);
        }
        if (isset($entry["end_date"])) {
            $check_event = $check_event->where('end_date', $entry["end_date"]);
        }
        $check_event = $check_event->where('type', $entry["type"]);
        // Add more checks if needed, I think these will be good enough in start.
        $check_event = $check_event->first();
        if ($import->params['how_to_import'] == 'update_only' && !$check_event) {
            array_push($update_log['message'], 'Schedule does not exist');
            $update_log['n_record_rejected']++;
            DB::rollback();
            return $update_log;
        }
        if ($import->params['how_to_import'] == 'create_new_only' && $check_event) {
            array_push($update_log['message'], 'Schedule already exist');
            $update_log['n_record_rejected']++;
            DB::rollback();
            return $update_log;
        }
        if ($import->params['how_to_import'] == 'disable_accounts_listed' && !$check_event) {
            array_push($update_log['message'], 'Schedule does not exist');
            $update_log['n_record_rejected']++;
            DB::rollback();
            return $update_log;
        }
        DB::commit();

        if ($import->params['how_to_import'] == 'disable_accounts_listed' && $check_event) {
            $update_log['n_record_updated']++;
            $check_event->status = 0;
            $check_event->save();
            return $update_log;
        }
        if ($check_event) {
            $event = $check_event;
            $update_log['n_record_updated']++;
        } else {
            $event = \Models\Schedule::createEvent($entry, false, true, true, $import->params['auth_user_id']);
            $update_log['n_record_inserted']++;
        }

        if (!empty($entry["manager_usernames"])) {
            $entry["manager_usernames"] = explode(",", $entry["manager_usernames"]);

            //Add trainers Here
            foreach ($entry["manager_usernames"] as $key => $manager_username) {
                $manager = \Models\User
                    ::where('username', trim($manager_username))
                    ->where('status', true)
                    ->first();

                if ($manager) {
                    \Models\ScheduleLink::addNewLink(
                        [
                            "schedule_id" => $event->id,
                            "type" => 'managers',
                            "link_id" => $manager->id,
                        ]
                    );
                }
            }
        }

        // If linked programme is provided, check if it exists and link
        if (!empty($entry["linked_programme"])) {
            $programme = \Models\ApprenticeshipStandard
                ::where('name', $entry["linked_programme"])
                ->where('status', true)
                ->first();
            if ($programme) {
                \Models\ScheduleLink::addNewLink(
                    [
                        "schedule_id" => $event->id,
                        "type" => 'programmes',
                        "link_id" => $programme->id,
                    ]
                );

                // linked_programme_citeria
                if (!empty($entry["linked_programme_citeria"])) {
                    // Sample Outcome 1 > Sample Criteria 1, Sample Outome 2 > Sample Criteria 2
                    $outcome_criterias = explode(",", $entry["linked_programme_citeria"]);
                    foreach ($outcome_criterias as $key => $outcome_criteria) {
                        $outcome_criteria_arr = explode(">", $outcome_criteria);

                        if (count($outcome_criteria_arr) == 2) { // Proceed only if there are outcome and criteria specified.
                            // $outcome_criteria_arr[0] is outcome - apprenticeship_issue_categories, child of $programme
                            $outcome = \Models\ApprenticeshipIssueCategories
                                ::where('name', trim($outcome_criteria_arr[0]))
                                ->where('standard_id', $programme->id)
                                ->where('status', 1)
                                ->first();
                            if ($outcome) {
                                // $outcome_criteria_arr[1] is criteria - apprenticeship_issues, child of $outcome, this will be linked against event
                                $criteria = \Models\ApprenticeshipIssues
                                    ::where('name', trim($outcome_criteria_arr[1]))
                                    ->where('issue_category_id', $outcome->id)
                                    ->where('status', 1)
                                    ->first();
                                if ($criteria) {
                                    \Models\ScheduleLink::addNewLink(
                                        [
                                            "schedule_id" => $event->id,
                                            "type" => 'issues',
                                            "link_id" => $criteria->id,
                                        ]
                                    );
                                }
                            }
                        }
                    }
                }
            }
        }

        // Link groups with event
        if (
            isset($entry["groups"]) &&
            $entry["groups"]
        ) {
            $entry["groups"] = explode(",", $entry["groups"]);

            //Loop groups, if found, link with event, if not create a new group
            foreach ($entry["groups"] as $key => $group_name) {
                $group = \Models\Group
                    ::where('name', trim($group_name))
                    ->first();

                if (!$group) {
                    $group = new \Models\Group;
                    $group->name = trim($group_name);
                    $group->status = 1;
                    $group->save();
                }

                if ($group) {
                    \Models\ScheduleLink::addNewLink(
                        [
                            "schedule_id" => $event->id,
                            "type" => 'groups',
                            "link_id" => $group->id,
                        ]
                    );
                }
            }
        }


        // Attach event with event.
        if (isset($entry["venue"])) {
            $venue = \Models\Venue::firstOrNew(
                [
                    'name' => trim($entry["venue"])
                ]
            );
            $venue->status = true;
            $venue->save();
            \Models\TableExtension::updateField('schedules', $event->id, "venue_id", $venue->id);
        }


        // If zoom/teams urk is provided, create new resource and link with event
        if (!empty($entry['zoom_teams_meeting'])) {
            // check if event already has meeting, if it does, do not add new one.

            $zoom_teams_link = \Models\ScheduleLink
                ::where('schedule_links.schedule_id', $event->id)
                ->where('schedule_links.type', 'resources')
                ->where('schedule_links.status', true)
                ->join("learning_modules", function ($join) {
                    $join
                        ->on("learning_modules.id", "schedule_links.link_id")
                        ->where("learning_modules.status", 1);
                })
                ->join("learning_module_types", function ($join) {
                    $join
                        ->on("learning_module_types.id", "learning_modules.type_id")
                        ->where("learning_module_types.slug", 'microsoft_teams')
                        ->orWhere("learning_module_types.slug", 'zoom_meeting');
                })
                ->first();

            if (!$zoom_teams_link) {
                $slug = false;
                if (str_contains($entry['zoom_teams_meeting'], 'teams')) {
                    $slug = 'microsoft_teams';
                } else if (str_contains($entry['zoom_teams_meeting'], 'zoom')) {
                    $slug = 'zoom_meeting';
                }

                $learningType = \Models\LearningModuleType
                    ::where('slug', $slug)
                    ->where('status', 1)
                    ->withoutGlobalScope('type_filter')
                    ->first();

                // Save Learning Module Here
                $params = [
                    "category_id" => $category->id,
                    "type_id" => $learningType->id,
                    "material" => [
                        "sessions" => [],
                        "link" => $entry['zoom_teams_meeting']
                    ],
                    "name" => $slug == 'microsoft_teams' ? $entry["name"] . ", " . "Microsoft Teams" : $entry["name"] . ", " . "Zoom meeting",
                ];
                $params['created_by'] = $import->params['auth_user_id'];
                $learningModule = \Models\LearningModule::insertImportModule($params, $settings);
                \Models\ScheduleLink::addNewLink(
                    [
                        "schedule_id" => $event->id,
                        "type" => 'resources',
                        "link_id" => $learningModule->id,
                    ]
                );
            }
        }

        // Link users with event
        /*
        "Q" => "trainee_username",
        "R" => "trainee_account", // primary, secondary, etc, not implemented yet!
        "S" => "trainee_completion_status",
        */
        if (isset($entry['trainee_username'])) {
            $trainees = \Models\User
                ::where('username', trim($entry['trainee_username']))
                ->where('status', true);
            if (isset($entry['trainee_account'])) {
                $account_types = array_map('trim', explode(',', $entry["trainee_account"]));
                if (count($account_types) > 0) {
                    $trainees = $trainees
                        ->whereIn(
                            'account_type_id',
                            \Models\Picklist
                                ::select('id')
                                ->whereIn('value', $account_types)
                                ->where('type', 'account_type')
                                ->get()
                        );
                }
            }
            $trainees = $trainees
                ->get();
            foreach ($trainees as $trainee_key => $trainee) {
                $user_link_data = [
                    "schedule_id" => $event->id,
                    "type" => 'users',
                    "link_id" => $trainee->id,
                ];
                if (isset($entry['trainee_completion_status'])) {
                    $user_link_data['completion_status'] = \Models\ScheduleLink::completionStatusID($entry['trainee_completion_status']);
                }
                if (isset($entry['trainee_completed_at'])) {
                    $user_link_data['completed_at'] = \APP\Tools::parseDateTime($entry['trainee_completed_at']);
                }
                \Models\ScheduleLink::addNewLink($user_link_data);
            }
        }

        // if programme and trainee is present and assignUsersToProgrammeDuringEventImport is true, check if user is not linked with programme, then assign user to programme.
        // This might be resource intensive.
        if (
            !empty($programme) &&
            !empty($trainees) &&
            \APP\Tools::getConfig('assignUsersToProgrammeDuringEventImport')
        ) {
            foreach ($trainees as $trainee_key => $trainee) {
                $assigned = \Models\ApprenticeshipStandardUser
                    ::where('user_id', $trainee->id)
                    ->where('standard_id', $programme->id)
                    ->first();
                if (!$assigned) {
                    \Models\ApprenticeshipStandardUser::assignToStandard($trainee->id, $programme->id, isset($entry["start_date"]) ? $entry["start_date"] : false);
                }
            }
        }

        // $log[] = [
        //     'record' => $entry
        // ];

        // $update_log['log'][] = [
        //     'record' => [
        //         'name' => $entry['name'],
        //         'address' => $entry['address'],
        //         'post_code' => $entry['post_code'],
        //         'contact_number' => $entry['contact_number'],
        //         'contact_name' => $entry['contact_name'],
        //         'rating' => $entry['rating'],
        //         'capacity' => $entry['capacity'],
        //     ]
        // ];
        return $update_log;
    }

    public static function validatePhoneNumber($number)
    {// Trim whitespace from the beginning and end
        $number = trim($number);

        // Remove all spaces
        $number = str_replace(' ', '', $number);

        // Remove everything except digits and +
        $number = preg_replace('/[^0-9+]/', '', $number);

        // Valid UK mobile numbers:
        // - 11-digit starting with 07
        // - International formats with +44 or 44 replacing the 0
        if (preg_match('/^07\d{9}$/', $number)) {
            return true;
        }

        if (preg_match('/^447\d{9}$/', $number)) {
            return true;
        }

        if (preg_match('/^\+447\d{9}$/', $number)) {
            return true;
        }

        return false;
    }

    private function validateUploadPermissions(Request $request, Response $response, $type)
    {
        if (!\APP\Auth::checkSession($request)) {
            return \APP\Tools::returnCode(
                $request,
                $response,
                403,
                '403 Forbidden',
                ['message' => 'Session is invalid or has expired.']
            );
        }

        $permissions = [
            'user' => ['structure' => 'system-setup-organisation-users', 'permission' => 'insert'],
            'lr' => ['structure' => 'library-learning-resources-and-lessons', 'permission' => 'insert']
        ];

        if (array_key_exists($type, $permissions)) {
            $structure = $permissions[$type]['structure'];
            $permission = $permissions[$type]['permission'];

            if (\APP\Auth::checkStructureAccess([$structure], $permission)) {
                return null;
            }
        }

        return \APP\Tools::returnCode(
            $request,
            $response,
            403,
            '403 Forbidden',
            ['message' => 'No access to this route by this users role - ' . \APP\Auth::roleId()]
        );

    }


public function importEventAndMapLesson(int $lessonId, $entry)
{
    $name = $entry['name'] . '-' . $entry['learning_resource_code'];

    if (Schedule::where('name', $name)->exists()) {
        return;
    }

    $defaultTime = "08:00:00";

    try {
        // Handle multiple possible date formats
        if (isset($entry['created_at']) && is_string($entry['created_at'])) {
            if (Carbon::hasFormat($entry['created_at'], 'd/m/Y')) {
                $entry['created_at'] = Carbon::createFromFormat('d/m/Y', $entry['created_at']);
            } elseif (Carbon::hasFormat($entry['created_at'], 'Y-m-d')) {
                $entry['created_at'] = Carbon::createFromFormat('Y-m-d', $entry['created_at']);
            } else {
                $entry['created_at'] = Carbon::parse($entry['created_at']);
            }
        }
    } catch (\Exception $e) {
        $entry['created_at'] = Carbon::now();
    }

    $startDate = isset($entry['created_at']) && $entry['created_at'] instanceof Carbon
        ? $entry['created_at']->setTimeFromTimeString($defaultTime)
        : Carbon::now()->setTimeFromTimeString($defaultTime);

    $durationInMinutes = self::convertDurationToMinutes($entry['duration'] ?? null);
    if ($durationInMinutes <= 0) {
        $durationInMinutes = 60;
    }

    $module = LearningModule::find($lessonId);

    $schedule = new Schedule();
    $schedule->name = $name;
    $schedule->type = 'lesson';
    $schedule->category_id = $module->category_id;
    $schedule->description = $module->description ?? '';
    $schedule->start_date = $startDate->toDateTimeString();
    $schedule->duration = $durationInMinutes;
    $schedule->end_date = $startDate->copy()->addMinutes($durationInMinutes)->toDateTimeString();
    $schedule->cron_task = 1;
    $schedule->status = 1;
    $schedule->save();

    if ($schedule->id) {
        $scheduleLink = new ScheduleLink();
        $scheduleLink->schedule_id = $schedule->id;
        $scheduleLink->type = 'lesson';
        $scheduleLink->link_id = $lessonId;
        $scheduleLink->status = true;
        $scheduleLink->cron_task = true;
        $scheduleLink->save();
        } else {
            return ;
    }
}


    public static function parseDuration(string $duration): ?array {
        if (preg_match('/^(\d+)h\s*(\d*)m?$/', trim($duration), $matches)) {
            return [
                'duration_hour' => (int) $matches[1],
                'duration_minute' => isset($matches[2]) && $matches[2] !== '' ? (int) $matches[2] : 0
            ];
        }
        return null;

    }



    public static function convertDurationToMinutes($duration) {
        preg_match('/(\d+)h\s*(\d+)m/', $duration, $matches);

        if ($matches) {
            $hours = (int)$matches[1];
            $minutes = (int)$matches[2];

            $totalMinutes = ($hours * 60) + $minutes;
            return $totalMinutes;
        }

        return 0;
    }

    public static function ImportUserTrainingResults($settings)
    {
        $localFilePath = $settings['LRImportFile'] ?? null;

        if (!$localFilePath || !file_exists($localFilePath)) {
            throw new \Exception("Learning Results file is not found or not provided.");
        }

        $fileExtension = strtolower(pathinfo($localFilePath, PATHINFO_EXTENSION));
        if (!in_array($fileExtension, ['csv', 'xlsx', 'xls'])) {
            throw new \Exception("The file is not a CSV or Excel file.");
        }

        // Initialize counters
        $n_users = [
            'learning_resource_count' => 0,
            'schedule_count' => 0,
            'user_not_exist_count' => 0,
            'learning_module_not_exists_count' => 0,
            'rejected_count' => 0
        ];

        // Create import log
        $import_log = new ImportLog();
        $import_log->file_name = basename($localFilePath);
        $import_log->file_path = $localFilePath;
        $import_log->params = [];
        $import_log->status = 1;
        $import_log->is_processed = true;
        $import_log->logs = [];
        $import_log->save();

        // Open the file and stream rows in batches
        $batchSize = 500;
        $batch = [];

        if (($handle = fopen($localFilePath, 'r')) !== false) {
            $headers = fgetcsv($handle);
            $headers = array_map('strtolower', $headers);

            $fields = [
                "user_code",
                "item",
                "completion_date",
                "completion_status",
                "total_hour"
            ];

            while (($row = fgetcsv($handle)) !== false) {
                $record = array_combine($fields, $row);
                $batch[] = $record;

                if (count($batch) >= $batchSize) {
                    $n_users = self::processUserTrainingResultsBatch($batch, $n_users, $import_log->id);
                    $batch = [];
                }
            }

            // Process remaining records
            if (!empty($batch)) {
                $n_users = self::processUserTrainingResultsBatch($batch, $n_users, $import_log->id);
            }

            fclose($handle);
        } else {
            throw new \Exception("Failed to open the file.");
        }

        $import_log->logs = $n_users;
        $import_log->save();

        return $n_users;
    }

    private static function processUserTrainingResultsBatch($batch, $n_users, $logId)
    {
        // Group records by user_code and item to handle multiple entries together
        $groupedRecords = [];
        foreach ($batch as $record) {
            $key = $record['user_code'] . '_' . $record['item'];
            if (!isset($groupedRecords[$key])) {
                $groupedRecords[$key] = [];
            }
            $groupedRecords[$key][] = $record;
        }

        // Process each group of records
        foreach ($groupedRecords as $records) {
            try {
                DB::beginTransaction();
                // Sort records by completion date (newest first) to ensure proper processing order
                usort($records, function($a, $b) {
                    return strtotime($b['completion_date']) - strtotime($a['completion_date']);
                });

                // Process all records for this user/course combination
                foreach ($records as $record) {
                    $n_users = self::insertUserTrainingResults($record, $n_users, $logId);
                }
                DB::commit();
            } catch (\Exception $e) {
                DB::rollback();
                $n_users['rejected_count'] += count($records);
                foreach ($records as $record) {
                    ImportLogError::create([
                        'import_log_id' => $logId,
                        'data' => $record,
                        'error_log' => $e->getMessage()
                    ]);
                }
            }
        }

        return $n_users;
    }



    public static function insertUserTrainingResults($data, $user, $importLogId)
    {
        if (empty($data) || empty($data['user_code'])) {
            $user['user_not_exist_count']++;
            ImportLogError::create([
                'import_log_id' => $importLogId,
                'data' => $data,
                'error_log' => 'User does not exist'
            ]);
            return $user;
        }
        $data['user_code'] = intval($data['user_code']); //Remove leading zeros

        // Retrieve the IDs for primary and secondary account types
        $primary_type_id = Picklist::where('type', 'account_type')->where('slug', 'Primary')->value('id');
        $secondary_type_id = Picklist::where('type', 'account_type')->where('slug', 'Secondary')->value('id');

        // Attempt to find the user ID with the provided user code and account type
        $userId = User::where('usercode', $data['user_code'])
            ->where('account_type_id', $primary_type_id)
            ->value('id');

        if (!$userId) {
            // If not found, try with the secondary account type
            $userId = User::where('usercode', $data['user_code'])
                ->where('account_type_id', $secondary_type_id)
                ->value('id');

            if (!$userId) {
                // Increment the count for non-existent users and log the error
                $user['user_not_exist_count']++;
                ImportLogError::create([
                    'import_log_id' => $importLogId,
                    'data' => $data,
                    'error_log' => 'User does not exist'
                ]);
                return $user;
            }
        }

        $learningModule = LearningModule::where('code', $data['item'])->first();
        if (!$learningModule) {
            $user['learning_module_not_exists_count']++;
            ImportLogError::create([
                'import_log_id' => $importLogId,
                'data' => $data,
                'error_log' => 'Learning module does not exist'
            ]);
            return $user;
        }

        $learningModuleId = $learningModule->id;
        $isCreatedByEvent = $learningModule->created_by_event;
        // Check if the user already has this learning module
        \Models\UserLearningModule::linkResources([$userId], [$learningModuleId], $isCreatedByEvent ? "schedule - process events, link resources to user" : "Surrey user training history import", true, true);

        // Handle multiple entries with different completion dates
        self::handleMultipleLearningResults($userId, $learningModuleId, $data);


        if ($isCreatedByEvent) {
            self::insertScheduleLink($learningModuleId, $userId, $data);
            $user['schedule_count']++;
        } else {
            $user['learning_resource_count']++;
        }
        return $user;
    }

    /**
     * Handle multiple learning results with different completion dates for the same user/module
     */
    private static function handleMultipleLearningResults($userId, $learningModuleId, $data)
    {
        $completionDate = date('Y-m-d', strtotime($data['completion_date']));

        // Check if a learning result already exists for this exact completion date
        $existingLearningResult = LearningResult::where([
            'user_id' => $userId,
            'learning_module_id' => $learningModuleId,
            'completed_at' => $completionDate
        ])->first();

        if ($existingLearningResult) {
            // Update existing result for this completion date
            self::updateLearningResult($existingLearningResult->id, $data);
        } else {
            // Get all existing learning results with completion dates for this user/module
            $allResults = LearningResult::where([
                'user_id' => $userId,
                'learning_module_id' => $learningModuleId
            ])->whereNotNull('completed_at')->orderBy('completed_at', 'desc')->get();

            if ($allResults->isEmpty()) {
                // No existing results with completion dates, find the base record created by linkResources
                $baseLearningResult = LearningResult::where([
                    'user_id' => $userId,
                    'learning_module_id' => $learningModuleId
                ])->whereNull('completed_at')->first();

                if ($baseLearningResult) {
                    self::updateLearningResult($baseLearningResult->id, $data);
                } else {
                    // Create new learning result if no base record exists
                    $newResult = new LearningResult();
                    $newResult->user_id = $userId;
                    $newResult->learning_module_id = $learningModuleId;
                    $newResult->completed_at = $completionDate;

                    $status = strtolower($data['completion_status']);
                    $newResult->completion_status = ($status == 'complete') ? 'completed' : ($status == 'not started' ? 'not attempted' : null);

                    $totalDuration = static::parseDuration($data['total_hour']);
                    if (!empty($totalDuration)) {
                        $newResult->duration_hours = $totalDuration['duration_hour'];
                        $newResult->duration_minutes = $totalDuration['duration_minute'];
                    }

                    $newResult->refreshed = 0;
                    $newResult->saveWithoutEvents();
                }
            } else {
                // Create new learning result entry
                $templateResult = $allResults->first();
                $newResult = $templateResult->replicate();
                $newResult->completed_at = $completionDate;

                $status = strtolower($data['completion_status']);
                $newResult->completion_status = ($status == 'complete') ? 'completed' : ($status == 'not started' ? 'not attempted' : null);

                $totalDuration = static::parseDuration($data['total_hour']);
                if (!empty($totalDuration)) {
                    $newResult->duration_hours = $totalDuration['duration_hour'];
                    $newResult->duration_minutes = $totalDuration['duration_minute'];
                }

                $newResult->saveWithoutEvents();

                // Now determine which entry should be the "newest" (refreshed = 0)
                self::updateRefreshedFlags($userId, $learningModuleId);
            }
        }
    }

    /**
     * Update refreshed flags to ensure only one record has refreshed=0
     * Priority: incomplete records > newest completed record
     * Also ensures only one incomplete record exists per user/module
     */
    private static function updateRefreshedFlags($userId, $learningModuleId)
    {
        // Check if there are any incomplete records (without completion dates)
        $incompleteResults = LearningResult::where([
            'user_id' => $userId,
            'learning_module_id' => $learningModuleId
        ])->whereNull('completed_at')->orderBy('created_at', 'asc')->get();

        // If there are multiple incomplete records, keep only the first one and delete the rest
        if ($incompleteResults->count() > 1) {
            $keepRecord = $incompleteResults->first();
            $deleteRecords = $incompleteResults->skip(1);

            foreach ($deleteRecords as $record) {
                $record->forceDelete(); // Force delete to bypass soft deletes
            }

            // Update the collection to only contain the kept record
            $incompleteResults = collect([$keepRecord]);
        }

        // Get all completed results ordered by completion date desc
        $completedResults = LearningResult::where([
            'user_id' => $userId,
            'learning_module_id' => $learningModuleId
        ])->whereNotNull('completed_at')->orderBy('completed_at', 'desc')->get();

        if ($incompleteResults->isNotEmpty()) {
            // If there are incomplete records, they get refreshed = 0, all completed get refreshed = 1
            foreach ($incompleteResults as $result) {
                $result->refreshed = 0;
                $result->saveWithoutEvents();
            }
            foreach ($completedResults as $result) {
                $result->refreshed = 1;
                $result->saveWithoutEvents();
            }
        } else {
            // If no incomplete records, only the newest completed gets refreshed = 0
            foreach ($completedResults as $index => $result) {
                $result->refreshed = ($index === 0) ? 0 : 1;
                $result->saveWithoutEvents();
            }
        }
    }

    /**
     * Updates the learning result details.
     */
    private static function updateLearningResult($learningResultId, $data)
    {
        $learningResult = LearningResult::find($learningResultId);
        if (!$learningResult) {
            return;
        }

        $status = strtolower($data['completion_status']);
        $learningResult->completion_status = ($status == 'complete') ? 'completed' : ($status == 'not started' ? 'not attempted' : null);
        $learningResult->completed_at = date('Y-m-d', strtotime($data['completion_date']));

        $totalDuration = static::parseDuration($data['total_hour']);
        if (!empty($totalDuration)) {
            $learningResult->duration_hours = $totalDuration['duration_hour'];
            $learningResult->duration_minutes = $totalDuration['duration_minute'];
        }

        $learningResult->saveWithoutEvents();
    }

    /**
     * Inserts a schedule links
     */
    private static function insertScheduleLink($learningModuleId, $userId, $data)
    {
        $existingScheduleLink = ScheduleLink::where(['link_id' => $learningModuleId, 'type' => 'lesson'])->first();
        if ($existingScheduleLink) {

            if (ScheduleLink::where(['schedule_id' => $existingScheduleLink->schedule_id, 'type' => 'users', 'link_id' => $userId])->exists()) {
                $scheduleLink = ScheduleLink::where(['schedule_id' => $existingScheduleLink->schedule_id, 'type' => 'users', 'link_id' => $userId])->first();
            } else {
                $scheduleLink = new ScheduleLink();
            }

            $scheduleLink->schedule_id = $existingScheduleLink->schedule_id;
            $scheduleLink->type = 'users';
            $scheduleLink->link_id = $userId;

            $status = strtolower($data['completion_status']);
            $scheduleLink->completion_status = ($status == 'complete') ? '%%event_completion_state_completed%%' : ($status == 'not started' ? '%%event_completion_state_not_attempted%%' : null);

            $scheduleLink->completed_at = date('Y-m-d', strtotime($data['completion_date']));
            $scheduleLink->duration = self::convertDurationToMinutes($data['total_hour']);
            $scheduleLink->saveWithoutEvents();
        }
    }

    /**
     * Download attached file from URL for e-learning import
     * @param string $url The URL to download from
     * @param array $settings Application settings
     * @return string|false The filename of the downloaded file or false on failure
     */
    private static function downloadAttachedFile($url, $settings)
    {
        try {
            // Validate URL
            if (!filter_var($url, FILTER_VALIDATE_URL)) {
                \Models\Log::addEntry(false, false, 400, [
                    'type' => 'import-error',
                    'message' => "Invalid URL for attached file: {$url}"
                ]);
                return false;
            }

            // Generate unique filename
            $pathInfo = pathinfo(parse_url($url, PHP_URL_PATH));
            $extension = isset($pathInfo['extension']) ? '.' . $pathInfo['extension'] : '.zip';
            $filename = 'import_' . uniqid() . '_' . time() . $extension;
            $tempFilePath = $settings["LMSTempPath"] . $filename;

            // Download file with timeout and size limits
            $context = stream_context_create([
                'http' => [
                    'timeout' => 300, // 5 minutes timeout
                    'method' => 'GET',
                    'header' => [
                        'User-Agent: Open-eLMS-Import/1.0'
                    ]
                ],
                'ssl' => [
                    'verify_peer' => false,
                    'verify_peer_name' => false,
                    'allow_self_signed' => true
                ]
            ]);

            // Use streaming download to avoid memory issues with large files
            $sourceHandle = fopen($url, 'rb', false, $context);
            if ($sourceHandle === false) {
                \Models\Log::addEntry(false, false, 500, [
                    'type' => 'import-error',
                    'message' => "Failed to open stream for URL: {$url}"
                ]);
                return false;
            }

            $destinationHandle = fopen($tempFilePath, 'wb');
            if ($destinationHandle === false) {
                fclose($sourceHandle);
                \Models\Log::addEntry(false, false, 500, [
                    'type' => 'import-error',
                    'message' => "Failed to create temp file: {$tempFilePath}"
                ]);
                return false;
            }

            // Stream download with size limit and memory management
            $totalSize = 0;
            $maxSize = 100 * 1024 * 1024; // 100MB limit
            $bufferSize = 8192; // 8KB buffer

            while (!feof($sourceHandle)) {
                $buffer = fread($sourceHandle, $bufferSize);
                if ($buffer === false) {
                    break;
                }
                
                $totalSize += strlen($buffer);
                
                // Check size limit during download
                if ($totalSize > $maxSize) {
                    fclose($sourceHandle);
                    fclose($destinationHandle);
                    unlink($tempFilePath); // Clean up partial file
                    \Models\Log::addEntry(false, false, 413, [
                        'type' => 'import-error',
                        'message' => "Downloaded file too large (>{$maxSize} bytes): {$url}"
                    ]);
                    return false;
                }
                
                if (fwrite($destinationHandle, $buffer) === false) {
                    fclose($sourceHandle);
                    fclose($destinationHandle);
                    unlink($tempFilePath); // Clean up partial file
                    \Models\Log::addEntry(false, false, 500, [
                        'type' => 'import-error',
                        'message' => "Failed to write to temp file: {$tempFilePath}"
                    ]);
                    return false;
                }
                
                // Force garbage collection for large batches
                if ($totalSize % (1024 * 1024) === 0) { // Every 1MB
                    gc_collect_cycles();
                }
            }

            fclose($sourceHandle);
            fclose($destinationHandle);

            // Verify download completed successfully
            if ($totalSize === 0) {
                unlink($tempFilePath); // Clean up empty file
                \Models\Log::addEntry(false, false, 500, [
                    'type' => 'import-error',
                    'message' => "Downloaded file is empty: {$url}"
                ]);
                return false;
            }

            // Log successful download with size info
            $fileSizeMB = round($totalSize / (1024 * 1024), 2);
            \Models\Log::addEntry(false, false, 200, [
                'type' => 'import-success',
                'message' => "Downloaded attached file: {$url} -> {$filename} ({$fileSizeMB}MB)"
            ]);

            return $filename;

        } catch (\Exception $e) {
            \Models\Log::addEntry(false, false, 500, [
                'type' => 'import-error',
                'message' => "Exception downloading attached file: {$url} - " . $e->getMessage()
            ]);
            return false;
        }
    }


    public static function generateCategoryUsingGPT($record)
    {
        $categories = LearningModuleCategory::pluck('name')->implode("\n");
        $name = !empty($record["name"]) ? $record["name"] : null;
        $description = !empty($record["description"])
            ? $record["description"]
            : null;

        $prompt =
            '
Use course name: ' .
            $name .
            ' and description: "' .
            $description .
            '", generate the following in JSON format:

1) category

The category must be selected from the following predefined list:

Categories:
' .
            $categories .
            '

The final result should be in JSON format with the following structure:
{
  "category": ""
}
';

        $API_TOKEN =
            "Bearer"." ".\APP\Tools::getConfig("openAIKey");

        $ch = curl_init();
        curl_setopt(
            $ch,
            CURLOPT_URL,
            "https://api.openai.com/v1/chat/completions"
        );
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "Authorization: " . $API_TOKEN,
            "Accept: application/json",
            "Content-Type: application/json",
        ]);

        curl_setopt(
            $ch,
            CURLOPT_POSTFIELDS,
            json_encode([
                "model" => "gpt-4o-mini",
                "max_tokens" => 300,
                "messages" => [
                    [
                        "role" => "user",
                        "content" => $prompt,
                    ],
                ],
            ])
        );

        $res = curl_exec($ch);

        if (curl_errno($ch)) {
            curl_close($ch);
            return null;
        }

        curl_close($ch);

        $data = json_decode($res);

        if (isset($data->choices[0]->message->content)) {
            $content = $data->choices[0]->message->content;

            // Clean up markdown-style formatting
            $cleaned = preg_replace('/^```json|```$/m', "", trim($content));
            $cleaned = trim($cleaned);

            $decoded = json_decode($cleaned, true);

            if (isset($decoded["category"])) {
                return $decoded["category"];
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

}
