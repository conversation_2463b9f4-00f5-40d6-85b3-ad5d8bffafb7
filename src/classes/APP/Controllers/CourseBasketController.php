<?php

declare(strict_types=1);

namespace APP\Controllers;

use APP\Auth;
use APP\Services\PaymentService;
use Models\CourseBasket;
use Models\LearningModule;
use Models\User;
use Models\UserPaymentTransaction;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Psr\Container\ContainerInterface;
use Stripe\StripeClient;
use APP\Tools;
use Models\ApprenticeshipStandard;
use Models\PurchasePaymentTransaction;
use Models\Schedule;
use Models\ScheduleLink;
use Models\StripePaymentTransaction;
use Models\UserLearningModule;
use stdClass;
use GlobalPayments\Api\Entities\Address;
use GlobalPayments\Api\Entities\Enums\AddressType;
use GlobalPayments\Api\ServiceConfigs\Gateways\GpEcomConfig;
use GlobalPayments\Api\HostedPaymentConfig;
use GlobalPayments\Api\Entities\HostedPaymentData;
use GlobalPayments\Api\Entities\Enums\HppVersion;
use GlobalPayments\Api\Entities\Exceptions\ApiException;
use GlobalPayments\Api\Services\HostedService;
use GlobalPayments\Api\Entities\Transaction;
use GlobalPayments\Api\ServiceConfigs\ServicesConfig;
use GlobalPayments\Api\ServicesContainer;
use Models\GlobalPaymentTransaction;
use SoapClient;
use SoapVar;

const SUCCESS_CODE = "succeeded";

class CourseBasketController extends Controller
{
    private $stripeSecretKey;
    private $globalPayment; // userd for common functions like get price and apply coupons
    private $stripe;
    private $hmacKey;
	private $endpoint;
	private $siteId;
	private $scpId;
	private $hmacKeyId;
	private $identifier;
	private $subjectType;
	private $uniqueReference;
	private $timeStamp;
	private $algorithm;
	private $requestId;
	private $client;
	private $scpReference;
	private $backUrl;
	private $systemCode;
	private $requestType;
	private $panEntryMethod;
	private $fundCode;
	private $vatCode;
	private $vatRate;
	private $saleSummaryDescription;
	private $saleSummaryReference;
	private $itemLineId;
	private $itemSummaryReference;
    private $reference;
    private $wsdlPath;

    public function __construct(ContainerInterface $containerInterface)
    {
        $this->container = $containerInterface;
        $this->stripeSecretKey = Tools::getConfig('stripeSecretKey');
        $this->globalPayment = new GlobalPaymentController($containerInterface);
        
        // Only initialize Stripe if we have a valid API key
        if (!empty($this->stripeSecretKey)) {
            $this->stripe = new  StripeClient($this->stripeSecretKey);
        }

        $this->siteId = \APP\Tools::getConfig('pay360_Site_Id');
        $this->scpId = \APP\Tools::getConfig('pay360_SCP_Id');
        $this->hmacKey = \APP\Tools::getConfig('pay360_hmacKey');
        $this->hmacKeyId = \APP\Tools::getConfig('pay360_hmacKey_Id');
        $this->systemCode = \APP\Tools::getConfig('pay360_systemCode');
        $this->requestType =\APP\Tools::getConfig('pay360_requestType');
        $this->panEntryMethod = \APP\Tools::getConfig('pay360_panEntryMethod');
        $this->fundCode = \APP\Tools::getConfig('pay360_fundCode');
        $this->vatCode = \APP\Tools::getConfig('pay360_vatCode');
        $this->vatRate = \APP\Tools::getConfig('pay360_vatRate');
        $this->subjectType = \APP\Tools::getConfig('pay360_subjectType');
        $this->algorithm = \APP\Tools::getConfig('pay360_algorithm');
        $this->itemSummaryReference = !empty(\APP\Tools::getConfig('pay360DefaultGeneralLedgerCode')) ? \APP\Tools::getConfig('pay360DefaultGeneralLedgerCode') : 'TXN-' . strtoupper(uniqid());
        $this->requestId = date('YmdHis') . mt_rand(1000, 9999);
        $this->reference = substr(md5((string) time()), 0, 10);

        $this->timeStamp = gmdate('YmdHis');
        $this->wsdlPath = $GLOBALS["CONFIG"]->LMSPrivatePath . 'wsdl/' . \APP\Tools::getConfig('pay360_wsdl');
        
        // Only initialize SoapClient if PHP soap extension is available and WSDL path exists
        if (class_exists('SoapClient') && !empty($this->wsdlPath) && file_exists($this->wsdlPath)) {
            $this->client = new SoapClient($this->wsdlPath, [
                'trace' => true,
                'exceptions' => true,
            ]);
        }
    }
    
    public function addToBasket(Request $request, Response $response): Response
    {
        $params = $request->getParsedBody();
        $userId = \APP\Auth::getUserId();

        $itemId = $params['item_id'];
        $frontendType = $params['item_type'];

        $typeMap = [
            'learning_module' => 'Models\LearningModule',
            'schedules'        => 'Models\Schedule',
            'programme'        => 'Models\ApprenticeshipStandard',
        ];

        if (!isset($typeMap[$frontendType])) {
            $response->getBody()->write(json_encode(['error' => 'Invalid item type']));
            return $response->withStatus(400)->withHeader('Content-Type', 'application/json');
        }

        $itemType = $typeMap[$frontendType];

        $exists = CourseBasket::where('user_id', $userId)
            ->where('item_id', $itemId)
            ->where('item_type', $itemType)
            ->exists();
        if ($exists) {
            $response->getBody()->write(json_encode(['error' => 'Item already in the basket']));
            return $response->withStatus(400)->withHeader('Content-Type', 'application/json');
        }
        CourseBasket::create([
            'user_id' => $userId,
            'item_id' => $itemId,
            'item_type' => $itemType,
        ]);
        $response->getBody()->write(json_encode(['success' => true, 'count' => PaymentService::getBasketCount()]));
        return $response->withStatus(200)->withHeader('Content-Type', 'application/json');
    }

    public function getBasketCount(Request $request, Response $response): Response
    {
        $response->getBody()->write(json_encode(['count' => PaymentService::getBasketCount()]));
        return $response->withHeader('Content-Type', 'application/json');
    }

    public function listBaskets(Request $request, Response $response): Response
    {
        $userId = \APP\Auth::getUserId();
        $baskets = CourseBasket::with(['item', 'item.category'])->where('user_id', $userId)->get();
        foreach ($baskets as $basket) {
            $item = $basket->item;
            if ($item instanceof Schedule) {
                $lesson = $item->Lesson()->first();
                if ($lesson) {
                    $item->safe_thumbnail = $lesson->safe_thumbnail;
                    $item->highlight = $lesson->highlight;
                } else {
                    $item->safe_thumbnail = null;
                    $item->highlight = null;
                }
            } elseif ($item instanceof ApprenticeshipStandard) {
                
            }
            elseif ($item) {
                $item->append(['safe_thumbnail', 'highlight']);
            }
        }
        $response->getBody()->write(json_encode($baskets));
        return $response->withHeader('Content-Type', 'application/json');
    }

    public function removeItemFromBasket(Request $request, Response $response): Response
    {
        $params = $request->getParsedBody();
        $itemId = $params['id'] ?? null;
        if (!$itemId) {
            $response->getBody()->write(json_encode(['error' => 'Missing item ID']));
            return $response->withStatus(400)->withHeader('Content-Type', 'application/json');
        }
        $cartItem = CourseBasket::find($itemId);
        if (!$cartItem) {
            $response->getBody()->write(json_encode(['error' => 'Item not found']));
            return $response->withStatus(404)->withHeader('Content-Type', 'application/json');
        }
        $cartItem->delete();
        $response->getBody()->write(json_encode(['success' => true, 'count' => PaymentService::getBasketCount()]));
        return $response->withStatus(200)->withHeader('Content-Type', 'application/json');
    }

    public function clearBasket(Request $request, Response $response): Response
    {
        $userId = \APP\Auth::getUserId();
        CourseBasket::where('user_id', $userId)->delete();
        $response->getBody()->write(json_encode(['message' => 'Cart cleared.', 'count' => PaymentService::getBasketCount()]));
        return $response->withStatus(200)->withHeader('Content-Type', 'application/json');
    }

    public function stripePayment(Request $request, Response $response): Response
    {
        // Check if Stripe is properly configured
        if (empty($this->stripe)) {
            $response->getBody()->write(json_encode(['error' => 'Stripe payment is not configured']));
            return $response->withStatus(500)->withHeader('Content-Type', 'application/json');
        }
        
        $data = $request->getParsedBody();
       
       
        // Validate input
        if (empty($data)) {
            $response->getBody()->write(json_encode(['error' => 'No items in cart']));
            return $response->withStatus(400)->withHeader('Content-Type', 'application/json');
        }
        $userId = \APP\Auth::getUserId();
        // Get user info
        if (!empty($data[0]['user_id'])) {
            $paid_for = User::with('Department')->with('Company')->where(['id' => $data[0]['user_id']])->first();
        } else {
            $paid_for = User::with('Department')->with('Company')->where(['id' => $userId])->first();
        }
        $user = User::with('Department')->with('Company')->where(['id' => $userId])->first();
        $currencyCode = 'GBP'; //TODO: get from config
        $userTransactionData = [];

        foreach ($data as $key => $value) { 
          
            $cost = $this->globalPayment->getCost($value['item_type'], $value['item_id']);
            $totalDiscount = PaymentService::getTotalDiscountPercentage($userId);
            $totalDiscountPercentage = $totalDiscount['totalDiscount'];
            
            $paymentTotal = $totalDiscountPercentage > 0 ? $cost - round(($cost * $totalDiscountPercentage) / 100, 2) : $cost;
            
            $items = [
                'user_id' => $userId,
                'type' => $value['item_type'],
                'type_reference_table' => $value['item_type'],
                'type_id' => $value['item_id'],
                'paid_for' => $paid_for->id,
                'payment_gateway' => 'stripe',
                'payment_amount' => $cost,
                'calling_application_id' => '',
                'payment_total' => $paymentTotal,
                'item_cost' => $cost
            ];
            if ($paymentTotal <= 0) {
                $items['response_description'] = $totalDiscount['zeroPaymentMessage'];
            }

            $userPaymentTransactionObj = UserPaymentTransaction::create($items);
            
            $stripeAmount = round($userPaymentTransactionObj->payment_total * 100, 2);
            $uniqueId = $userId.$value['item_type'].$value['item_id'];
            $userTransactionCost[$uniqueId] = ['cost'=> $cost];  
            $userTransactionData[] = $userPaymentTransactionObj->id;  
             // Prepare line item for Stripe
             $lineItems[] = [
                'price_data' => [
                    'product_data' => [
                        'name' => $this->globalPayment->getName($value['item_type'], $value['item_id']),
                        'metadata' => [
                            'pro_id' => $value['item_id']
                        ]
                    ],
                    'unit_amount' => $stripeAmount,
                    'currency' => $currencyCode,
                ],
                'quantity' => 1
            ];
    
        }

        try {

            $uri = $request->getUri();
            $baseUrl = $uri->getScheme() . '://' . $uri->getHost();
            if ($uri->getPort()) {
                $baseUrl .= ':' . $uri->getPort();
            }

            $callback_url = $baseUrl . '/course-basket/stripe/payment/callback?checkout_session_id={CHECKOUT_SESSION_ID}';

            // Create Stripe checkout session
            $checkout_session = $this->stripe->checkout->sessions->create([
                'line_items' => $lineItems,
                'mode' => 'payment',
                'success_url' => $callback_url,
                'cancel_url' => $callback_url,
            ]);
            foreach ($data as $key => $value) { 
                $uniqueId = $userId.$value['item_type'].$value['item_id'];
                if (array_key_exists($uniqueId, $userTransactionCost)) {
                    $cost = $userTransactionCost[$uniqueId]['cost'];
                }
                StripePaymentTransaction::create([
                    'user_id' => $user->id,
                    'type' => $value['item_type'],
                    'type_id' => $value['item_id'],
                    'cost' => $cost ?? 0,
                    'order_id' => $checkout_session->id
                ]);
            }

            foreach ($userTransactionData as $key => $value) {
                $userPaymentTransactionObj = UserPaymentTransaction::find($value);
                $userPaymentTransactionObj->calling_application_id = $checkout_session->id;
                $userPaymentTransactionObj->save();
            }
            
            $response->getBody()->write(json_encode(array(
                'status' => 1,
                'message' => 'Checkout Session created successfully!',
                'sessionId' => $checkout_session->id
            )));
            return $response->withHeader('Content-Type', 'application/json');

            
        } catch (\Exception $e) {
            $response->getBody()->write(json_encode(['error' => $e->getMessage()]));
            return $response->withStatus(500)->withHeader('Content-Type', 'application/json');
        }

    }

    public function stripeCallback(Request $request, Response $response): Response
    {
        // Check if Stripe is properly configured
        if (empty($this->stripe)) {
            return $response->withStatus(500);
        }
        
        $params = $request->getQueryParams();
        if (!isset($params['checkout_session_id']) || $params['checkout_session_id'] == '') {
            return $response->withStatus(404);
        }
        try {
            $checkout_session = $this->stripe->checkout->sessions->retrieve($params['checkout_session_id']);
            $paymentIntent = $this->stripe->paymentIntents->retrieve($checkout_session->payment_intent);
        } catch (\Exception $e) {
            $paymentIntent = new stdClass();
            $paymentIntent->status = 'failed';
            $paymentIntent->id = $params['checkout_session_id'];
            $paymentIntent->confirmation_method = 'manual';
            $paymentIntent->error = $e->getMessage();
            $paymentIntent->error_code = $e->getCode();
        }

        StripePaymentTransaction::where(['order_id' => $params['checkout_session_id']])->update([
            'transation_id' => $paymentIntent->id,
            'responses' => json_encode($paymentIntent)
        ]);
        $userPaymentTransaction = UserPaymentTransaction::where('calling_application_id', $params['checkout_session_id'])->update([
            'response_description' => $paymentIntent->confirmation_method,
            'system_generated_transaction_id' => $paymentIntent->id,
            'status' => true,
            'is_sent' => true
        ]);

        $userPaymentTransactions = UserPaymentTransaction::where('calling_application_id', $params['checkout_session_id'])->get();
        foreach ($userPaymentTransactions as $key => $value) {
            PurchasePaymentTransaction::updateData(
                $value->paid_for ? $value->paid_for : $value->user_id,
                $value->type_reference_table,
                $value->type_id,
                $value->item_cost,
                $value->system_generated_transaction_id,
                'stripe',
                $value->id
            );
        }
        $code = $paymentIntent->status == SUCCESS_CODE ? '00' : str_replace("_", " ", $paymentIntent->status);

        PaymentService::enroll($userPaymentTransactions[0]->calling_application_id, $this->container->get('settings')['LMSUrl'], $code);
        // $this->enroll($userPaymentTransactions[0]->calling_application_id, $code, $paymentIntent->id);
        $message = 'Payment ' . $paymentIntent->status;
        if ($paymentIntent->status == SUCCESS_CODE) {
            CourseBasket::where('user_id', \APP\Auth::getUserId())->delete();
        }
        $data = [
            'code' => $code,
            'message' => $message,
            'order_id' => $paymentIntent->id
        ];
        $response->getBody()->write(
            "<script>
                localStorage.setItem('payment_status', `" . json_encode($data) . "`);
                let url = '/';
                if ('" . $data['code'] . "' === '00') {
                    url = localStorage.getItem('course_success_url');
                } else {
                    url = localStorage.getItem('course_failed_url');
                }
                window.location.href = url;
            </script>"
        );

        return $response->withHeader('Content-Type', 'text/html');
    }

    public function enroll($orderId, $code = "00"): ?string
    {
        $userPaymentTransactions = UserPaymentTransaction::where('calling_application_id', $orderId)->get();
        foreach ($userPaymentTransactions as $key => $value) {
            if ($code === "00"){
                if ($value->type == "schedules") {
                    $schedule = Schedule::with('Lessons')->where('id', $value->type_id)->first();
                    $order  = ScheduleLink::where("schedule_id", $value->type_id)->where("type", 'users')->where("status", 1)->max("order");
                    $link = $this->container->settings['LMSUrl'] . "app/learner/resources/" . $schedule->lessons[0]->id . "-" . $schedule->id;
                    ScheduleLink::addNewLink([
                        'schedule_id' => $value->type_id,
                        'link_id' => $value->paid_for,
                        'type' => 'users',
                        'order' => $order
                    ]);
                    $scheduleLink = ScheduleLink::where('schedule_id', $value->type_id)->where('link_id', $value->paid_for)->whereIn('type', ['users', 'users_queue'])->first();
                    $scheduleLink->is_paid = true;
                    if (\APP\Tools::getConfig('ApproveEventUserAfterPayment')) {
                        $scheduleLink->approved = 1;
                    }
                    $scheduleLink->type = "users";
                    $scheduleLink->save();
                    \Models\Schedule::processEvents(false, $value->type_id, $value->paid_for);
                    return $link;
                } elseif ($value->type == "learning_modules") {
                    $this->enrolLearningModule($value);
                } elseif ($value->type == "programme") {
                    $standerd = ApprenticeshipStandard::where('id', $value->type_id)->first();
                    \Models\ApprenticeshipStandardUser::assignToStandard($value->user_id, $value->type_id, $standerd->start_at);
                }
            }
        }

        $this->sendEmail($userPaymentTransactions, $code, $orderId);
        
    }

    public function enrolLearningModule($userPaymentTransaction): void
    {
        UserLearningModule::linkResources($userPaymentTransaction->paid_for, [$userPaymentTransaction->type_id], 'user enrolled to this resource');
        \APP\Learning::syncUserResults($userPaymentTransaction->user_id);
        \Models\LearningResult
            ::where("learning_results.user_id", "=", $userPaymentTransaction->paid_for)
            ->whereIn("learning_results.learning_module_id", function ($query) use ($userPaymentTransaction) {
                $query
                    ->select("id")
                    ->from("learning_modules")
                    ->whereIn("id", [$userPaymentTransaction->type_id]);
            })
            ->update([
                "approved" => true,
                "is_paid" => true,
            ]); 
    }

    private function sendEmail($userPaymentTransactions, $status, $transaction_id)
    {
        $paid_for_user = false;
        $auth_user_id = \APP\Auth::getUserId();
        $auth_user = User::find($auth_user_id);

        $firstTransaction = $userPaymentTransactions[0];
        if ($firstTransaction->paid_for !== $firstTransaction->user_id) {
            $paid_for_user = User::find($firstTransaction->paid_for);
        }

        // Determine template
        $template_slug = $paid_for_user ? 'confirmation_of_purchase_manager_cart': 'confirmation_of_purchase_learner_cart';

        $template = \Models\EmailTemplate::getTemplate($template_slug);
        if (!$template) {
            return;
        }

        $custom_variables = [];
        $format = \APP\Tools::getConfig('defaultDateFormat') ?: 'd/m/Y';
        $custom_variables['TODAYSDATE'] = \Carbon\Carbon::now()->format($format);
        $custom_variables['USER_FNAME'] = $auth_user->fname;
        $custom_variables['RECEIPT_NUMBER'] = $firstTransaction->system_generated_transaction_id;

        if ($paid_for_user) {
            $custom_variables['PAID_FOR_FNAME'] = $paid_for_user->fname;
            $custom_variables['PAID_FOR_LNAME'] = $paid_for_user->lname;
            $custom_variables['LEARNER_NAME'] = $paid_for_user->fname . ' ' . $paid_for_user->lname;
        }

        // Generate PURCHASE_SUMMARY and TOTAL_FEE
        $purchase_summary = '';
        $total_fee = 0;

        foreach ($userPaymentTransactions as $tx) {
            $type_label = '';
            $item_name = '';

            switch ($tx->type) {
                case 'schedules':
                    $schedule = Schedule::find($tx->type_id);
                    if ($schedule) {
                        $type_label = 'Event';
                        $item_name = $schedule->name;
                    }
                    break;
                case 'learning_modules':
                    $module = LearningModule::find($tx->type_id);
                    if ($module) {
                        $type_label = 'Resource';
                        $item_name = $module->name;
                    }
                    break;
                case 'programme':
                    $programme = ApprenticeshipStandard::find($tx->type_id);
                    if ($programme) {
                        $type_label = 'Programme';
                        $item_name = $programme->name;
                    }
                    break;
            }

            if ($type_label && $item_name) {
                $purchase_summary .= "<li>$type_label: $item_name – Fee: " . number_format($tx->payment_amount, 2) . "</li>";
            }

            $total_fee += floatval($tx->payment_amount);
        }

        $custom_variables['PURCHASE_SUMMARY'] = $purchase_summary;
        $custom_variables['TOTAL_FEE'] = number_format($total_fee, 2);

        // Prepare email
        $email_queue = new \Models\EmailQueue;
        $email_queue->email_template_id = $template->id;
        $email_queue->recipients = [$auth_user_id];
        $email_queue->approved = 0;
        $email_queue->custom_variables = json_encode($custom_variables);
        $email_queue->save();
    }


    public function stripePaymentSuccess(Request $request, Response $response): Response
    {
    }

    public function stripePaymentFailure(Request $request, Response $response): Response
    {
        
    }

    public function globalPayment(Request $request, Response $response): Response {
        $data = $request->getParsedBody();
        $cartItems = $data['cart'] ?? [];

        if (empty($cartItems)) {
            $response->getBody()->write(json_encode(['error' => 'Cart is empty.']));
            return $response->withStatus(400)->withHeader('Content-Type', 'application/json');
        }
        $userId = \APP\Auth::getUserId();
        if(isset($data['user_id'])){
            $paid_for = User::with(['Department', 'Company'])->find($data['user_id'])->first();
        }else{
            $paid_for = User::with(['Department', 'Company'])->find($userId)->first();
        }
        $user = User::with(['Department', 'Company'])->find($userId)->first();
        $billing_address = PaymentService::updateBillingAddress($data, $user->id);
        $typeMap = [
            'Models\LearningModule' => 'learning_modules',
            'Models\Schedule' => 'schedules',
            'Models\ApprenticeshipStandard' => 'programme',
        ];
        $config = new GpEcomConfig();
        $config->merchantId = \APP\Tools::getConfig("globalPaymentsMerchantID");
        $config->accountId = \APP\Tools::getConfig("globalPaymentsAccountID");
        $config->sharedSecret = \APP\Tools::getConfig("globalPaymentsSharedSecret");
        $config->serviceUrl = \APP\Tools::getConfig("globalPaymentsEngineRequestURL");
        $config->hostedPaymentConfig = new HostedPaymentConfig();
        $config->hostedPaymentConfig->version = HppVersion::VERSION_2;
        $service = new HostedService($config);

        try {
            $totalCost = 0;
            $transactionIds = [];

            $billingAddress = new Address();
            $billingAddress->streetAddress1 = $billing_address->address_1;
            $billingAddress->streetAddress2 = $billing_address->address_2;
            $billingAddress->streetAddress3 = "";
            $billingAddress->city = $billing_address->city;
            $billingAddress->postalCode = $billing_address->postcode;
            $billingAddress->country = $billing_address->country;

            $hostedPaymentData = new HostedPaymentData();
            $hostedPaymentData->customerFirstName = $user->fname;
            $hostedPaymentData->customerLastName = $user->lname;
            $hostedPaymentData->customerEmail = $billing_address->email;
            $hostedPaymentData->customerPhoneMobile = $billing_address->phone;
            $hostedPaymentData->addressesMatch = false;
            $productDescriptions = [];
            foreach ($cartItems as $item) {
                $itemType = $typeMap[$item['item_type']];
                $cost = PaymentService::getCost($itemType, $item['item_id']);
                $type = $itemType;
                $type_id = $item['item_id'];

                $itemCost = $cost ?? 0;
                $totalCost += $itemCost;

                $totalDiscount = PaymentService::getTotalDiscountPercentage($userId);
                $totalDiscountPercentage = $totalDiscount['totalDiscount'];
            
                $paymentTotal = $totalDiscountPercentage > 0 ? $cost - round(($cost * $totalDiscountPercentage) / 100, 2) : $cost;
                $items = [
                    'user_id' => $user->id,
                    'type' => $type,
                    'type_reference_table' => $type,
                    'type_id' => $type_id,
                    'paid_for' => $paid_for->id,
                    'payment_gateway' => 'global-payment',
                    'payment_amount' => $itemCost,
                    'calling_application_id' => '',
                    'payment_total' => $paymentTotal,
                    'item_cost' => $itemCost
                ];
                if ($paymentTotal <= 0) {
                    $items['response_description'] = $totalDiscount['zeroPaymentMessage'];
                }

                $transaction = UserPaymentTransaction::create($items);

                $transactionIds[] = $transaction->id;

                $name = $item['item']['name']; 
                $costToShow =  number_format($paymentTotal, 2);
                $productDescriptions[] = "$name - €$costToShow";
            }
            // Combine all into one string
            $description = implode("\n", $productDescriptions);

            $hppJson = $service->charge((float)$totalCost)
                                ->withCurrency(Tools::getConfig('globalPaymentCurrencyCode'))
                                ->withHostedPaymentData($hostedPaymentData)
                                ->withAddress($billingAddress, AddressType::BILLING)
                                ->withAddress($billingAddress, AddressType::SHIPPING)
                                ->withCustomData(json_encode(['transactions' => $transactionIds]))
                                ->withDescription($description);
            if ($user->department) {
                $hppJson->withCustomData($user->department->name);
            }
            if ($user->company) {
                $hppJson->withCustomData($user->company->name);
            }

            $hppJson = $hppJson->serialize();
            $hppJson = json_decode($hppJson);
            foreach ($cartItems as $item) {
                $itemType = $typeMap[$item['item_type']];
                $cost = PaymentService::getCost($item['item_type'], $item['item_id']);
                GlobalPaymentTransaction::create([
                    'user_id' => $user->id,
                    'type' => $itemType,
                    'type_id' => $item['item_id'],
                    'cost' => $cost ?? 0,
                    'order_id' => $hppJson->ORDER_ID
                ]);
            }
            UserPaymentTransaction::whereIn('id', $transactionIds)->update([
                'calling_application_id' => $hppJson->ORDER_ID
            ]);
            $response->getBody()->write(json_encode($hppJson));
            return $response->withHeader('Content-Type', 'application/json');
        } catch (\Throwable $th) {
            $response->getBody()->write(json_encode(['error' => $th->getMessage()]));
            return $response->withStatus(500)->withHeader('Content-Type', 'application/json');
        }
    }

    public function globalPaymentResponse(Request $request, Response $response, array $args): Response {
        $params = $request->getParams();
        $responseJson = $params['hppResponse'];

        $config = new GpEcomConfig();
        $config->merchantId = \APP\Tools::getConfig("globalPaymentsMerchantID");
        $config->accountId = \APP\Tools::getConfig("globalPaymentsAccountID");
        $config->sharedSecret = \APP\Tools::getConfig("globalPaymentsSharedSecret");;
        $config->serviceUrl = \APP\Tools::getConfig("globalPaymentsEngineRequestURL");;

        $service = new HostedService($config);


        try {
            // create the response object from the response JSON
            $parsedResponse = $service->parseResponse($responseJson, true);
            $orderId = $parsedResponse->orderId; // GTI5Yxb0SumL_TkDMCAxQA
            $responseCode = $parsedResponse->responseCode; // 00
            $responseMessage = $parsedResponse->responseMessage; // [ test system ] Authorised
            $responseValues = $parsedResponse->responseValues; // get values accessible by key
            $userPaymentTransaction = UserPaymentTransaction::where('calling_application_id',$orderId)->update([
                'card_type'=>$responseValues['CARDTYPE'],
                'payment_authorisation_code'=>$parsedResponse->transactionReference->authCode,
                'response_description'=>$responseMessage,
                'system_generated_transaction_id'=>$parsedResponse->transactionReference->transactionId,
                'status'=>true,
                'is_sent'=>true
            ]);
            $userPaymentTransactions = UserPaymentTransaction::where('calling_application_id',$orderId)->get();
            foreach ($userPaymentTransactions as $userPaymentTransaction) {
                PurchasePaymentTransaction::updateData($userPaymentTransaction->paid_for?$userPaymentTransaction->paid_for:$userPaymentTransaction->user_id,$userPaymentTransaction->type_reference_table,$userPaymentTransaction->type_id,$userPaymentTransaction->item_cost,$userPaymentTransaction->system_generated_transaction_id,'global-payment',$userPaymentTransaction->id);
            }
            GlobalPaymentTransaction::where(['order_id'=>$orderId])->update([
                'transation_id'=>$parsedResponse->transactionReference->transactionId,
                'responses'=>json_encode($parsedResponse)
            ]);
            // $this->enroll($orderId,$responseCode,$parsedResponse->transactionReference->transactionId);
            PaymentService::enroll($orderId,$this->container->settings['LMSUrl'], $responseCode);
            $data = [
                'code'=>$responseCode,
                'message'=>$responseMessage,
                'order_id'=>$parsedResponse->transactionReference->transactionId
            ];
            if($data['code']=="00"){
                CourseBasket::where('user_id', \APP\Auth::getUserId())->delete();
            }
            return $response
                ->write("<script>localStorage.setItem('payment_status',`".json_encode($data)."`);
                        let url = '/'
                        if(".$data['code']."=='00'){
                           url = localStorage.getItem('course_success_url')
                        }else{
                           url = localStorage.getItem('course_failed_url');
                        }
                        window.location.href=url;
                        </script>");
                //->write('Transaction successful, save in db and redirect to resource');
        } catch (ApiException $e) {
            // For example if the SHA1HASH doesn't match what is expected
            $response->getBody()->write(json_encode(['error' => $e->getMessage()]));
            return $response->withStatus(500)->withHeader('Content-Type', 'application/json');
        }
    }

    public function pay360Payment(Request $request, Response $response): Response {
        // Check if Pay360 SoapClient is properly configured
        if (empty($this->client)) {
            $response->getBody()->write(json_encode(['error' => 'Pay360 payment is not configured or PHP soap extension is not available']));
            return $response->withStatus(500)->withHeader('Content-Type', 'application/json');
        }
        
        $data = $request->getParsedBody();
        $cartItems = $data['cartItems'] ?? [];
        $userId = \APP\Auth::getUserId();
        $user = User::find($userId);

        if (!$user || empty($cartItems)) {
            $response->getBody()->write(json_encode(['error' => 'Invalid request']));
            return $response->withStatus(400)->withHeader('Content-Type', 'application/json');
        }

        if (!isset($data['coupons'])) {
			$data['coupons'] = '';
		}
        // here need to check the data so print 
		if(isset($data['user_id'])){
            $paid_for = User::with(['Department', 'Company'])->find($data['user_id'])->first();
        }else{
            $paid_for = User::with(['Department', 'Company'])->find($userId)->first();
        }

        $totalCartAmount = 0;
      

        // print this is to check the correct amount
        // print_r($totalCartAmount);

        
        // Use config
        $config = $GLOBALS["CONFIG"];
        

        // Build total amount
        $amount = 0;
        foreach ($cartItems as $cartItem) {
            $amount += $cartItem['item']['cost'] ?? 0;
        }
        $typeMap = [
            'Models\LearningModule' => 'learning_modules',
            'Models\Schedule' => 'schedules',
            'Models\ApprenticeshipStandard' => 'programme',
        ];
        foreach ($cartItems as $key => $cartItem) {

            $userPaymentTransactionObj = new UserPaymentTransaction();
            $itemtype = $typeMap[$cartItem['item_type']];
            $typeDetails = PaymentService::getTypeDetails($itemtype, $cartItem['item_id']);
            $saleSummaryDescription = $typeDetails->name ?? "No description";
            $saleSummaryReference = $typeDetails->id;
            switch ($itemtype) {
                case 'schedules':
                    $itemLineId = "event-item$key-$typeDetails->id"; 
                    break;
                case 'learning_modules':
                    $itemLineId = "module-item$key-$typeDetails->id"; 
                    break;
                case 'programme':
                    $itemLineId = "programme-item$key-$typeDetails->id";  
                    break;
                
            }

            $totalDiscount = PaymentService::getTotalDiscountPercentage($userId);
            $totalDiscountPercentage = $totalDiscount['totalDiscount'];
            $paymentTotal = $totalDiscountPercentage > 0 ? $typeDetails->cost - round(($typeDetails->cost * $totalDiscountPercentage) / 100, 2) : $typeDetails->cost;
            $totalCartAmount += $paymentTotal ?? 0;
            $userPaymentTransactionObj->item_cost = $typeDetails->cost;
            $userPaymentTransactionObj->user_id = $userId;
            $userPaymentTransactionObj->payer_organisation_id = $user->company_id;
            $userPaymentTransactionObj->type = $itemtype;
            $userPaymentTransactionObj->type_id = $cartItem['item_id'];
            $userPaymentTransactionObj->calling_application_id = $this->requestId;
            $userPaymentTransactionObj->type_reference_table = $itemtype;
            $userPaymentTransactionObj->paid_for = empty($data["paid_for"]) ? $userId : $data["paid_for"];
            $userPaymentTransactionObj->payment_total = $paymentTotal;
            $userPaymentTransactionObj->payment_amount = $typeDetails->cost;
            $userPaymentTransactionObj->return_url = ""; 
            $userPaymentTransactionObj->redirect_url = ""; 
            $userPaymentTransactionObj->network_user_id = "Internet";
            $userPaymentTransactionObj->payment_gateway = "pay360";
            $userPaymentTransactionObj->status = "0";
            $userPaymentTransactionObj->item_discount = $totalDiscountPercentage;
            $userPaymentTransactionObj->save();
            $userPaymentTransactionObj->system_generated_transaction_id = substr(md5((string) time()), 0, 10) . $userPaymentTransactionObj->id;
            $this->uniqueReference = $userPaymentTransactionObj->system_generated_transaction_id;
            // $userPaymentTransactionObj->return_url = $userPaymentTransactionObj->return_url . "?sgtid=" . $userPaymentTransactionObj->system_generated_transaction_id;
            $userPaymentTransactionObj->fund_code = $this->fundCode;
			$userPaymentTransactionObj->vat_code = $this->vatCode;
            if ($paymentTotal <= 0) {
                    $userPaymentTransactionObj->response_description = $totalDiscount['zeroPaymentMessage'];
            }
            $userPaymentTransactionObj->save();
            $userPaymentTransactionObjIds[] = $userPaymentTransactionObj->id;

            $itemsForPayment[] = [
                'itemSummary' => [
                    'description' => $saleSummaryDescription,
                    'amountInMinorUnits' => $userPaymentTransactionObj->payment_total * 100,
                    'reference' => $saleSummaryReference,
                    'displayableReference' => $typeDetails->name
                ],
                'lineId' => $itemLineId,
                'lgItemDetails' => [
                    'fundCode' => $this->fundCode,
                    'narrative' => 'Item Reference: ' . $saleSummaryReference,
                ],
                'tax' => [
                    'vat' => [
                        'vatCode' => $this->vatCode,
                        'vatRate' => $this->vatRate,
                        'vatAmountInMinorUnits' => $userPaymentTransactionObj->payment_total * $this->vatRate / 100 * 100,
                    ]
                ]
            ];
            $uri = $request->getUri();
            $baseUrl = $uri->getScheme() . '://' . $uri->getHost();
            if ($uri->getPort() && !in_array($uri->getPort(), [80, 443])) {
                $baseUrl .= ':' . $uri->getPort();
            }

            $returnUrl = $baseUrl . '/course-basket/pay360/callback/' . $userPaymentTransactionObj->system_generated_transaction_id;
        }

        
        $wsdlPath = $config->LMSPrivatePath . 'wsdl/' . \APP\Tools::getConfig('pay360_wsdl');
        $narrative = 'Course Payment';
        // need to add a back url its comes from the front end request
        // $returnUrl = "https://lms.e-learningwmb.co.uk/suffolk_sandbox" . '/payment-gateway/pay360/callback/';\
			// $returnUrl = $request->getUri()->getBaseUrl() . '/payment-gateway/pay360/callback/' . $userPaymentTransactionObj->system_generated_transaction_id;

        // dd($returnUrl);

        try {
            
            
            // Step 1: Concatenate values with '!'
				$dataToSign = implode('!', [
					$this->subjectType,
					$this->scpId,
					$this->reference,
					$this->timeStamp,
					$this->algorithm,
					$this->hmacKeyId
				]);

				$dataToSignBytes = utf8_encode($dataToSign);
				$decodedKey = base64_decode($this->hmacKey);
				$hmacDigest = hash_hmac('sha256', $dataToSignBytes, $decodedKey, true);
				// Step 4: Base-64 encode the resultant hash
				$encodedDigest = base64_encode($hmacDigest);
            // Prepare SOAP request
            $data = [
                'credentials' => [
                    'subject' => [
                        'subjectType' => $this->subjectType, 
                        'identifier' => $this->scpId,
                        'systemCode' => $this->systemCode,
                    ],
                    'requestIdentification' => [
                        'uniqueReference' => $this->reference,
                        'timeStamp' => $this->timeStamp,
                    ],
                    'signature' => [
                        'algorithm' => $this->algorithm,
                        'hmacKeyID' => $this->hmacKeyId,
                        'digest' => $encodedDigest,
                    ],
                ],
                'requestType' => $this->requestType,
                'requestId' => $this->requestId,
                'routing' => [
                    'returnUrl' => $returnUrl,
                    'backUrl' => $returnUrl,
                    'siteId' => $this->siteId,
                    'scpId' => $this->scpId,
                ],
                'panEntryMethod' => $this->panEntryMethod,
                'sale' => [
                    'saleSummary' => [
                        'description' => 'Multi basket payment',
                        'amountInMinorUnits' => $totalCartAmount * 100, 
                        'reference' =>  'Multi basket payment',
                        'displayableReference' => 'Multi basket payment',
                    ],
                    'items' => [
                        'item' => $itemsForPayment // Pay360 expects items wrapped in an 'item' key
                    ]
                ]
            ];
            // dd($data);

            
            $pay_response = $this->client->__soapCall('scpSimpleInvoke', [$data]);
    
            if (
                isset($pay_response->invokeResult->redirectUrl) &&
                isset($pay_response->scpReference)
            ) {
                UserPaymentTransaction::whereIn('id', $userPaymentTransactionObjIds)->update([
                    'redirect_url' => $pay_response->invokeResult->redirectUrl,
                    'scpReference' => $pay_response->scpReference
                ]);

                $response->getBody()->write(json_encode(['redirectUrl' => $pay_response->invokeResult->redirectUrl]));
                return $response->withHeader('Content-Type', 'application/json');
            } else {
                $error_response = json_encode($pay_response);
                if (isset($pay_response->invokeResult->errorDetails->errorMessage)) {
                    $error_response = $pay_response->invokeResult->errorDetails->errorMessage;
                }
                return \APP\Tools::returnCode($request, $response, 400, $error_response);
            }
        } catch (\SoapFault $e) {
            $request = $this->client->__getLastRequest();
            $responsee = $this->client->__getLastResponse();
            echo "SOAP Fault:\n" . $e->getMessage();
            // echo "\n\nLast SOAP Request:\n" . $request;
            // echo "\n\nLast SOAP Response:\n" . $responsee;
        
            $response->getBody()->write(json_encode([
                'error' => 'SOAP Error: ' . $e->getMessage(),
                'soapRequest' => $request,
                'soapResponse' => $responsee,
            ]));
            return $response->withStatus(500)->withHeader('Content-Type', 'application/json');
        } catch (\Exception $e) {
            $response->getBody()->write(json_encode([
                'error' => 'Internal Error: ' . $e->getMessage(),
            ]));
            return $response->withStatus(500)->withHeader('Content-Type', 'application/json');
        }
    }

    public function pay360callback(Request $request, Response $response, array $args): Response
    {
        // Check if Pay360 SoapClient is properly configured
        if (empty($this->client)) {
            return \APP\Tools::returnCode($request, $response, 500, 'Pay360 payment is not configured or PHP soap extension is not available.');
        }
        
        $params = $request->getQueryParams();
		if (!isset($args['system_generated_transaction_id'])) {
			return \APP\Tools::returnCode($request, $response, 500, 'Incorrect response from payment gateway.');
		}

		$userPaymentTransactionObj = \Models\UserPaymentTransaction
			::where('system_generated_transaction_id', $args['system_generated_transaction_id'])
			->first()
		;
		if (!$userPaymentTransactionObj) {
			return \APP\Tools::returnCode($request, $response, 404, 'Payment information not found.');
		}

		$this->uniqueReference = $userPaymentTransactionObj->system_generated_transaction_id;
		$this->requestId = $userPaymentTransactionObj->calling_application_id;
		$this->scpReference = $userPaymentTransactionObj->scpReference;
		$this->backUrl = $userPaymentTransactionObj->return_url;
		$callback_response = $this->queryPaymentStatus($userPaymentTransactionObj, $params);
		$data = [
            'code'=>  $callback_response->paymentResult->status == 'SUCCESS' ? '00' : '01',
            'message'=> $callback_response->paymentResult->status == 'SUCCESS' ? 'Transaction Success' : 'Transaction Failed',
            'order_id'=>$this->uniqueReference
        ];
        if ($callback_response->paymentResult->status == 'SUCCESS'){
            // $this->enroll($userPaymentTransactionObj->calling_application_id, "00");
            PaymentService::enroll($userPaymentTransactionObj->calling_application_id, $this->container->settings['LMSUrl'], "00");
            CourseBasket::where('user_id', \APP\Auth::getUserId())->delete();
        }
			return $response
                ->write("<script>localStorage.setItem('payment_status',`" . json_encode($data) . "`);
                        let url = '/'
                        if(".$data['code']."=='00'){
                           url = localStorage.getItem('course_success_url')
                        }else{
                           url = localStorage.getItem('course_failed_url');
                        }
                        window.location.href=url;
                        </script>");

    }

    private function queryPaymentStatus($userPaymentTransactionObj, $params) 
    {
		$callback_status_response = new \stdClass();
		$callback_status_response->process_callback = true;

        if ($callback_status_response->process_callback) {
            // Step 1: Concatenate values with '!'
            $dataToSign = implode('!', [
                $this->subjectType,
                $this->scpId,
                $this->reference,
                $this->timeStamp,
                $this->algorithm,
                $this->hmacKeyId
            ]);
            

            $dataToSignBytes = utf8_encode($dataToSign);
            $decodedKey = base64_decode($this->hmacKey);
            $hmacDigest = hash_hmac('sha256', $dataToSignBytes, $decodedKey, true);
            // Step 4: Base-64 encode the resultant hash
            $encodedDigest = base64_encode($hmacDigest);
            // SOAP request parameters for querying payment status
			$params = [
				'credentials' => [
					'subject' => [
						'subjectType' => $this->subjectType,
						'identifier' => $this->scpId,
						'systemCode' => $this->systemCode,
					],
					'requestIdentification' => [
						'uniqueReference' => $this->uniqueReference,
						'timeStamp' => $this->timeStamp,
					],
					'signature' => [
						'algorithm' => $this->algorithm,
						'hmacKeyID' => $this->hmacKeyId,
						'digest' => $encodedDigest,
					],
				],
				'requestId' => $this->requestId,
				'siteId' => $this->siteId,
				'scpReference' => $this->scpReference,
				'scpId' => $this->scpId,
			];
            // Send the SOAP query request
			try {
				$callback_response = $this->client->__soapCall('scpSimpleQuery', [$params]);
				// Check the response and handle accordingly 
                // need to get all userPaymentTransactionObj ids and uodate with unique field. 
				if (isset($callback_response->transactionState)) {
                    UserPaymentTransaction::where('calling_application_id', $this->requestId)->get()->each(function ($userPaymentTransactionObj) use ($callback_response) {
                            if (isset($callback_response->paymentResult->paymentDetails->authDetails->authCode)) {
                                $userPaymentTransactionObj->payment_authorisation_code = $callback_response->paymentResult->paymentDetails->authDetails->authCode;
                            }
                            $userPaymentTransactionObj->income_management_receipt_number = $callback_response->paymentResult->paymentDetails->paymentHeader->uniqueTranId;
                            //$userPaymentTransactionObj->originators_reference = $OriginatorsReference;
                            if (isset($callback_response->paymentResult->paymentDetails->authDetails->cardDescription)) {
                                $userPaymentTransactionObj->card_scheme = $callback_response->paymentResult->paymentDetails->authDetails->cardDescription;
                            }

                            if (isset($callback_response->paymentResult->paymentDetails->authDetails->cardType)) {
                                $userPaymentTransactionObj->card_type = $callback_response->paymentResult->paymentDetails->authDetails->cardType;
                            }

                            if (isset($callback_response->paymentResult->paymentDetails->authDetails->amountInMinorUnits)) {
                                $userPaymentTransactionObj->payment_amount = $callback_response->paymentResult->paymentDetails->authDetails->amountInMinorUnits / 100;
                            }

                            $userPaymentTransactionObj->response_code = $callback_response->paymentResult->status;
                            PurchasePaymentTransaction::updateData(
                                $userPaymentTransactionObj->paid_for ? $userPaymentTransactionObj->paid_for : $userPaymentTransactionObj->user_id,
                                $userPaymentTransactionObj->type_reference_table,
                                $userPaymentTransactionObj->type_id,
                                $userPaymentTransactionObj->item_cost,
                                $userPaymentTransactionObj->system_generated_transaction_id,
                                'pay360',
                                $userPaymentTransactionObj->id
                            );
                            if ($callback_response->paymentResult->status == 'SUCCESS'){
                                $userPaymentTransactionObj->status = 1;
                            }
                            $userPaymentTransactionObj->save();
                        });
				}

			}
            catch (\SoapFault $e) {
                $request = $this->client->__getLastRequest();
                $responsee = $this->client->__getLastResponse();
                echo "SOAP Fault:\n" . $e->getMessage();
                echo "\n\nLast SOAP Request:\n" . $request;
                echo "\n\nLast SOAP Response:\n" . $responsee;
            
            } catch (\Exception $e) {
                echo $e->getMessage();
			}
        }   
        // coupon usage may need to check not writing code now
        
       return $callback_response;
    }   
}
// http://localhost/course-basket/pay360/callback/8f920cf46a268