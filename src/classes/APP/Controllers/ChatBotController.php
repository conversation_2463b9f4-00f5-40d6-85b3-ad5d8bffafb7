<?php
namespace APP\Controllers;

use APP\Tools;
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use GuzzleHttp\Client;
use Models\LearningModule;
use Illuminate\Database\Capsule\Manager as DB;


class ChatBotController extends Controller {
    public function __construct() {

    }

    public function addDataToGoogleCloud(Request $request, Response $response, $args) {
        $body = $request->getParsedBody();
        $moduleId = $body['module_id'] ?? null;

        $result = self::processModulesForAI($moduleId);

        if ($moduleId) {
            return $result;
        } else {
            $response->getBody()->write(json_encode($result));
            return $response->withHeader('Content-Type', 'application/json');
        }
    }

    public static function processModulesForAI($moduleId = null) {
        $clientId = DB::connection()->getDatabaseName();

        $query = LearningModule::query()
            ->with(['Category:id,name', 'Type:id,name,slug'])
            ->select(['id', 'name', 'description', 'category_id', 'type_id', 'material'])
            ->where('add_to_ai_chat', true);

        if ($moduleId) {
            $query->where('id', $moduleId);
        }

        $courses = $query->get()->map(function ($course) use ($clientId) {
            $data = [
                'id' => $course->id,
                'name' => $course->name,
                'description' => empty($course->description) ? "" : self::cleanText($course->description),
                'category_name' => $course?->Category?->name ?? "",
                'type_slug' => $course?->Type?->slug ?? "",
                'client_id' => $clientId,
                'youtube_url' => "",
            ];

            $cleanDescription = strip_tags($course->description ?? '');
            $combinedText = "";

            if (!empty($data['type_slug'])) {
                if ($data['type_slug'] === 'e_learning') {
                     $scormPath = $GLOBALS["CONFIG"]->LMSPublicPath . "scormdata/{$course->id}/moddata/scorm/1/xml/information.xml";
                    if (file_exists($scormPath)) {
                        $scormData = self::getScormSectionsFromXml($course->id);
                        $sectionText = implode("\n\n", $scormData);
                        $combinedText = "Title: {$course->name}\n\nDescription: {$cleanDescription}\n\nCategory: {$data['category_name']}\n\nCourse Content:\n\n{$sectionText}";
                    }
                } elseif ($data['type_slug'] == 'youtube') {
                    $youtubeUrl = $course->material->link ?? null;
                    $data['youtube_url'] = $youtubeUrl;
                    $combinedText = "Title: {$course->name}\n\nDescription: {$cleanDescription}\n\nCategory: {$data['category_name']}\n\nCourse Content:\n\n";
                }
            }

            $data['combinedText'] = $combinedText ?: "Title: {$course->name}\n\nDescription: {$cleanDescription}\n\nCategory: {$data['category_name']}";
            return $data;
        });

        $client = new \GuzzleHttp\Client();
        $results = [];
        $chatBotApiBaseUrl = Tools::getConfig('chatBotApiBaseUrl');
        
        // Ensure proper URL formatting with trailing slash
        $chatBotApiBaseUrl = rtrim($chatBotApiBaseUrl, '/') . '/';
        $pythonApi = "{$chatBotApiBaseUrl}process-module";


        // Early exit if URL is not configured
        if (empty($chatBotApiBaseUrl)) {
            $error = "chatBotApiBaseUrl configuration is empty. Please configure the Python API base URL.";
            echo "ERROR: $error\n";
            foreach ($courses as $module) {
                $results[] = [
                    'id' => $module['id'],
                    'status' => 'error',
                    'error' => $error
                ];
            }
            return [
                'status' => 'completed',
                'results' => $results
            ];
        }
        foreach ($courses as $module) {
            try {
                $res = $client->post($pythonApi, [
                    'json' => $module
                ]);
                $body = json_decode($res->getBody()->getContents(), true);
                $results[] = [
                    'id' => $module['id'],
                    'status' => $body['status'] ?? 'unknown'
                ];
            } catch (\Exception $e) {
                $results[] = [
                    'id' => $module['id'],
                    'status' => 'error',
                    'error' => $e->getMessage()
                ];
            }
        }

        return [
            'status' => 'completed',
            'results' => $results
        ];
    }


    /**
     * Get SCORM sections from information.xml for a given module
     *
     * @param int|string $moduleId
     * @return array
     */
   public static function getScormSectionsFromXml($moduleId): array {
        $xmlFilePath = $GLOBALS["CONFIG"]->LMSPublicPath . "scormdata/{$moduleId}/moddata/scorm/1/xml/information.xml";

        if (!file_exists($xmlFilePath)) {
            return [];
        }

        try {
            $xml = simplexml_load_file($xmlFilePath);
            $results = [];
            foreach ($xml->screen as $screen) {
                $section = self::cleanText((string) $screen->Section);
                $content = self::cleanText((string) $screen->Content);

                if (!empty($section)) {
                    $sections[] = $section;
                }
                if (!empty($content)) {
                    $sections[] = $content;
                }
            }

            return $sections;
        } catch (\Exception $e) {
            return [];
        }
    }


    public function getChatResponse(Request $request, Response $response, $args)
    {
        
        
        $body = $request->getParsedBody();
        $userMessage = $body['message'] ?? '';
        
        if (empty($userMessage)) {
            $response->getBody()->write(json_encode(['reply' => '']));
            return $response->withHeader('Content-Type', 'application/json');
        }
        
        $clientId = DB::connection()->getDatabaseName();
        $chatBotApiBaseUrl = Tools::getConfig('chatBotApiBaseUrl');
        
        // Ensure proper URL formatting with trailing slash
        $chatBotApiBaseUrl = rtrim($chatBotApiBaseUrl, '/') . '/';
        
        // Always use streaming to avoid empty response issues
        return $this->handleStreamingChat($request, $response, $userMessage, $clientId, $chatBotApiBaseUrl);
    }

    private function handleStreamingChat(Request $request, Response $response, $userMessage, $clientId, $chatBotApiBaseUrl)
    {
        // Set headers before any output
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Headers: Cache-Control');
        header('X-Accel-Buffering: no');
        
        // Disable output buffering
        while (ob_get_level()) {
            ob_end_clean();
        }

        $client = new Client();

        if (Tools::getConfig('sharedClients')) {
            echo "data: " . json_encode([
                'type' => 'error',
                'message' => 'This chatbot tells learners about their organisation from the learning materials within the system. This is an add-on available to enterprise users only.'
            ]) . "\n\n";
            flush();
            exit;
        }

        if (!Tools::getConfig('isLearningAI')) {
            echo "data: " . json_encode([
                'type' => 'error',
                'message' => 'This chatbot is only available if the Learning AI is enabled.'
            ]) . "\n\n";
            flush();
            exit;
        }
    
        try {
            // Send initial "thinking" event
            echo "data: " . json_encode([
                'type' => 'status',
                'message' => 'Thinking...'
            ]) . "\n\n";
            flush();
            
            $res = $client->post("{$chatBotApiBaseUrl}chat", [ 
                'json' => [
                    'message' => $userMessage,
                    'client_id' => $clientId,
                    'format' => 'markdown',
                    'stream' => true
                ],
                'stream' => true
            ]);
            
            $body = $res->getBody();
            $buffer = '';
            
            while (!$body->eof()) {
                $chunk = $body->read(1024);
                $buffer .= $chunk;
                
                // Process SSE messages from the buffer
                $messages = explode("\n\n", $buffer);
                // Keep the last incomplete message in buffer
                $buffer = array_pop($messages);
                
                foreach ($messages as $message) {
                    $lines = explode("\n", trim($message));
                    foreach ($lines as $line) {
                        if (strpos($line, 'data: ') === 0) {
                            $jsonStr = substr($line, 6); // Remove 'data: ' prefix
                            $data = json_decode($jsonStr, true);
                            
                            if ($data) {
                                // Forward the data as-is from Python service
                                echo "data: " . $jsonStr . "\n\n";
                                flush();
                                
                                // Check if streaming is complete
                                if ($data['type'] === 'complete' || ($data['done'] ?? false)) {
                                    break 3;
                                }
                            }
                        }
                    }
                }
            }
            
            // Send completion event if not already sent
            echo "data: " . json_encode([
                'type' => 'complete'
            ]) . "\n\n";
            flush();
            
        } catch (\Throwable $th) {
            echo "data: " . json_encode([
                'type' => 'error',
                'message' => $th->getMessage()
            ]) . "\n\n";
            flush();
        }

        // Important: We need to exit here to prevent Slim from trying to send response
        exit;
    }

    private function formatChatResponse($text)
    {
        if (empty($text)) {
            return $text;
        }
        
        // Clean up common formatting issues
        $text = preg_replace('/\*\*([^*]+)\*\*/', '<strong>$1</strong>', $text);
        $text = preg_replace('/\*([^*]+)\*/', '<em>$1</em>', $text);
        
        // Format numbered lists
        $text = preg_replace('/^(\d+)\.\s+(.+)$/m', '<div class="chat-list-item"><span class="chat-list-number">$1.</span> $2</div>', $text);
        
        // Format bullet points
        $text = preg_replace('/^[\-\*]\s+(.+)$/m', '<div class="chat-list-item">• $1</div>', $text);
        
        // Format course titles (assuming they start with numbers and periods)
        $text = preg_replace('/^(\d+)\.\s+\*\*([^*]+)\*\*(.*)$/m', '<div class="chat-course-title"><span class="course-number">$1.</span> <strong>$2</strong>$3</div>', $text);
        
        // Convert line breaks to proper HTML
        $text = nl2br($text);
        
        // Add some structure for better readability
        $text = '<div class="chat-formatted-response">' . $text . '</div>';
        
        return $text;
    }

    private static function cleanText(?string $text): string 
    {
        if (empty($text)) {
            return '';
        }
        
        return trim(html_entity_decode(strip_tags(strval($text))));
    }

    public function deleteModuleFromGoogleCloud(Request $request, Response $response, $args)
    {
        $body = $request->getParsedBody();
        $moduleId = $body['module_id'] ?? null;
        if (!$moduleId) {
            return ;
        }
        $module = LearningModule::with('Category')->find($moduleId);
        if (!$module) {
            return;
        }
        $chatBotApiBaseUrl = Tools::getConfig('chatBotApiBaseUrl');
        $client = new \GuzzleHttp\Client();
        $pythonApi = "{$chatBotApiBaseUrl}delete-module";
        $clientId = DB::connection()->getDatabaseName();
        $res = $client->post($pythonApi, [
            'json' => [
                'module_id' => $moduleId,
                'client_id' => $clientId
            ]
        ]);
        $body = json_decode($res->getBody()->getContents(), true);
    }

}