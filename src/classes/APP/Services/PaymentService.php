<?php

namespace APP\Services;

use Models\ApprenticeshipStandard;
use Models\CourseBasket;
use Models\LearningModule;
use Models\Schedule;
use Models\ScheduleLink;
use Models\User;
use Models\UserBillingAddress;
use Models\UserLearningModule;
use Models\UserPaymentTransaction;


class PaymentService {


    public static function updateBillingAddress($data,$user_id){
        return UserBillingAddress::updateOrCreate(['user_id'=>$user_id],[
            'user_id'=>$user_id,
            'address_1'=>$data['address1'],
            'address_2'=>isset($data['address2'])?$data['address2']:'',
            'city'=>$data['city'],
            'country'=>$data['country'],
            'postcode'=>$data['postal_code'],
            'email'=>$data['email'],
            'phone'=>$data['phone_code'].'|'.$data['phone_number']
        ]);
    }

    public static function getCost($type,$type_id)
    {
        if($type=="schedules"){
            $schedules =  Schedule::where('id',$type_id)->first();
            if($schedules){
                return $schedules->cost;
            }

        }elseif($type=="learning_modules"){
            $learning_module = LearningModule::where('id',$type_id)->first();
            if($learning_module){
                return $learning_module->cost;
            }
        }elseif($type=="programme"){
            $programme = ApprenticeshipStandard::where('id',$type_id)->first();
            if($programme){
                return $programme->cost;
            }
        }
    }

    public static function getTypeDetails($type, $typeId)
    {
        switch ($type) {
            case 'schedules':
                $entity = Schedule::select('id', 'name', 'cost', 'description')->where('id', $typeId)->first();
                break;
            case 'learning_modules':
                $entity = LearningModule::select('id', 'name', 'cost', 'description')->where('id', $typeId)->first();
                break;
            case 'programme':
                $entity = ApprenticeshipStandard::select('id', 'name', 'cost', 'description')->where('id', $typeId)->first();
                break;
            default:
                $entity = null;
        }

        return $entity;
    }

    public static function getBasketCount()
    {
        return CourseBasket::where('user_id', \APP\Auth::getUserId())->count();
    }

    public static function getTotalDiscountPercentage($userId)
    {
        $totalDiscount = 0;
        $totalManagerDiscount = 0;
        $totalUserDiscount = 0;

        $zeroPaymentMessage = "Applied ";
        if (
            \APP\Auth::isAdminInterface()
        ) {
            $totalManagerDiscount = User::calculateTotalDiscount(\APP\Auth::getUserId());
            $totalManagerDiscount = ($totalManagerDiscount > 100) ? 100 : $totalManagerDiscount;

            $zeroPaymentMessage .= "manager discount of {$totalManagerDiscount}% and ";
        } else {
            $userId = \APP\Auth::getUserId();
        }
        $totalUserDiscount = User::calculateTotalDiscount($userId);
        $totalUserDiscount = ($totalUserDiscount > 100) ? 100 : $totalUserDiscount;
        $zeroPaymentMessage .= "trainee discount {$totalUserDiscount}%";
        $totalDiscount = $totalManagerDiscount + $totalUserDiscount;
        $totalDiscount = ($totalDiscount > 100) ? 100 : $totalDiscount;
        return ['totalDiscount' => $totalDiscount, 'zeroPaymentMessage' => $zeroPaymentMessage];
    }

    public static function enroll($orderId, $lmsUrl, $code = "00")
    {
        $userPaymentTransactions = UserPaymentTransaction::where('calling_application_id', $orderId)->get();
        foreach ($userPaymentTransactions as $key => $value) {
            if ($code === "00"){
                if ($value->type == "schedules") {
                    $schedule = Schedule::with('Lessons')->where('id', $value->type_id)->first();
                    $order  = ScheduleLink::where("schedule_id", $value->type_id)->where("type", 'users')->where("status", 1)->max("order");
                    $link = $lmsUrl . "app/learner/resources/" . $schedule->lessons[0]->id . "-" . $schedule->id;
                    ScheduleLink::addNewLink([
                        'schedule_id' => $value->type_id,
                        'link_id' => $value->paid_for,
                        'type' => 'users',
                        'order' => $order
                    ]);
                    $scheduleLink = ScheduleLink::where('schedule_id', $value->type_id)->where('link_id', $value->paid_for)->whereIn('type', ['users', 'users_queue'])->first();
                    $scheduleLink->is_paid = true;
                    if (\APP\Tools::getConfig('ApproveEventUserAfterPayment')) {
                        $scheduleLink->approved = 1;
                    }
                    $scheduleLink->type = "users";
                    $scheduleLink->save();
                    \Models\Schedule::processEvents(false, $value->type_id, $value->paid_for);
                } elseif ($value->type == "learning_modules") {
                    self::enrolLearningModule($value);
                } elseif ($value->type == "programme") {
                    $standerd = ApprenticeshipStandard::where('id', $value->type_id)->first();
                    \Models\ApprenticeshipStandardUser::assignToStandard($value->user_id, $value->type_id, $standerd->start_at);
                }
            }
        }
        self::sendEmail($userPaymentTransactions, $code, $orderId);
        
    }

    public static function enrolLearningModule($userPaymentTransaction)
    {
        UserLearningModule::linkResources($userPaymentTransaction->paid_for, [$userPaymentTransaction->type_id], 'user enrolled to this resource');
        \APP\Learning::syncUserResults($userPaymentTransaction->user_id);
        \Models\LearningResult
            ::where("learning_results.user_id", "=", $userPaymentTransaction->paid_for)
            ->whereIn("learning_results.learning_module_id", function ($query) use ($userPaymentTransaction) {
                $query
                    ->select("id")
                    ->from("learning_modules")
                    ->whereIn("id", [$userPaymentTransaction->type_id]);
            })
            ->update([
                "approved" => true,
                "is_paid" => true,
            ]); 
    }

    private static function sendEmail($userPaymentTransactions, $status, $transaction_id)
    {
        $paid_for_user = false;
        $auth_user_id = \APP\Auth::getUserId();
        $auth_user = User::find($auth_user_id);

        $firstTransaction = $userPaymentTransactions[0];
        if ($firstTransaction->paid_for !== $firstTransaction->user_id) {
            $paid_for_user = User::find($firstTransaction->paid_for);
        }

        // Determine template
        $template_slug = $paid_for_user ? 'confirmation_of_purchase_manager_cart': 'confirmation_of_purchase_learner_cart';

        $template = \Models\EmailTemplate::getTemplate($template_slug);
        if (!$template) {
            return;
        }

        $custom_variables = [];
        $format = \APP\Tools::getConfig('defaultDateFormat') ?: 'd/m/Y';
        $custom_variables['TODAYSDATE'] = \Carbon\Carbon::now()->format($format);
        $custom_variables['USER_FNAME'] = $auth_user->fname;
        $custom_variables['RECEIPT_NUMBER'] = $firstTransaction->system_generated_transaction_id;

        if ($paid_for_user) {
            $custom_variables['PAID_FOR_FNAME'] = $paid_for_user->fname;
            $custom_variables['PAID_FOR_LNAME'] = $paid_for_user->lname;
            $custom_variables['LEARNER_NAME'] = $paid_for_user->fname . ' ' . $paid_for_user->lname;
        }

        // Generate PURCHASE_SUMMARY and TOTAL_FEE
        $purchase_summary = '';
        $total_fee = 0;

        foreach ($userPaymentTransactions as $tx) {
            $type_label = '';
            $item_name = '';

            switch ($tx->type) {
                case 'schedules':
                    $schedule = Schedule::find($tx->type_id);
                    if ($schedule) {
                        $type_label = 'Event';
                        $item_name = $schedule->name;
                    }
                    break;
                case 'learning_modules':
                    $module = LearningModule::find($tx->type_id);
                    if ($module) {
                        $type_label = 'Resource';
                        $item_name = $module->name;
                    }
                    break;
                case 'programme':
                    $programme = ApprenticeshipStandard::find($tx->type_id);
                    if ($programme) {
                        $type_label = 'Programme';
                        $item_name = $programme->name;
                    }
                    break;
            }

            if ($type_label && $item_name) {
                $purchase_summary .= "<li>$type_label: $item_name – Fee: " . number_format($tx->payment_total, 2) . "</li>";
            }

            $total_fee += floatval($tx->payment_amount);
        }

        $custom_variables['PURCHASE_SUMMARY'] = $purchase_summary;
        $custom_variables['TOTAL_FEE'] = number_format($total_fee, 2);

        // Prepare email
        $email_queue = new \Models\EmailQueue;
        $email_queue->email_template_id = $template->id;
        $email_queue->recipients = [$auth_user_id];
        $email_queue->approved = 0;
        $email_queue->custom_variables = json_encode($custom_variables);
        $email_queue->save();
    }
}