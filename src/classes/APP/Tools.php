<?php
namespace APP;

use Carbon\Carbon;
use DateTime;
use <PERSON><PERSON><PERSON>ie\Minify;
use Illuminate\Database\Capsule\Manager as DB;
use \GuzzleHttp\Psr7\LazyOpenStream as OpenStream;
use Models\ApprenticeshipStandardUser;
use Models\CompanyConfiguration;
use Models\ScheduleLink;
use Models\User;
use Models\Venue;
use League\Flysystem\Filesystem;
use League\Flysystem\Local\LocalFilesystemAdapter;


use OpenSpout\Common\Entity\Cell;
use OpenSpout\Common\Entity\Row;
use OpenSpout\Common\Entity\Style\Style;
use OpenSpout\Common\Entity\Style\CellAlignment;
use OpenSpout\Writer\XLSX\Entity\SheetView;
use OpenSpout\Writer\XLSX\Entity\ColumnWidth;
use OpenSpout\Writer\AutoFilter;
use OpenSpout\Common\Entity\Style\Border;
use OpenSpout\Common\Entity\Style\BorderPart;
use OpenSpout\Common\Entity\Style\Color;




use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;

class Tools {

	public static function setObjectFields($object, array $fields, array $data, $nullify_empty = false, $falsify_empty = false) {
		foreach ($fields as $field) {
			if (array_key_exists($field, $data)) {
				if (
					$data[$field] === "NULL" ||
					$data[$field] === "null"
				) {
					$object->$field = null;
				} else {
					$casts = $object->getCasts();
					$fieldType = $casts[$field] ?? null;
					if ($fieldType == 'boolean') {
						$object->$field = filter_var($data[$field], FILTER_VALIDATE_BOOLEAN);
					} else {
						$object->$field = $data[$field];
					}
				}
			} elseif ($nullify_empty) {
				$object->$field = null;
			} elseif ($falsify_empty) {
				$object->$field = false;
			}
		}
	}


	public static function getObjectIds(array $objects)
	{
		$ids = [];
		foreach($objects as $object)
		{
			if (is_array($object)) {
				$ids[] = $object["id"];
			} else {
				$ids[] = $object->id;
			}
		}

		return $ids;
	}

	public static function unsecureRandom($length = 4) {
		$bytes = random_bytes($length);
		return bin2hex($bytes);
	}

	public static function generateExcelDownload($data, $export_fields, $download_file_name, $params = [], $created_user = null, $return_log_id = false) {

		$translator = \APP\Templates::getTranslator();
		foreach ($export_fields as $key => $export_field) {
			$translated_export_field_key = $translator->replaceVersionLabels($key);
			if ($translated_export_field_key != $key) {
				$updated_export_fields[$translated_export_field_key] = $export_field;
			} else {
				$updated_export_fields[$key] = $export_field;
			}
		}

		// for preserving the order
		$export_fields = $updated_export_fields ?? [];



		$export_data = [];
		$export_data[] = array_keys($export_fields);

		// to look for field names containing array index, ex: [0]
		$array_index_pattern = '/(\w{0,})\[(\d)\]$/i';
		$array_index_replacement_obj = '${1}';
		$array_index_replacement_index = '${2}';

		foreach($data as $row) {


			$row_data = [];
			foreach($export_fields as $field_name => $field_value) {
				$objs = explode(".", $field_value);

				$v = $row;
				foreach($objs as $key_obj=>$obj) {
					# For appending multiple values into field (eg: 'fname' & 'lname' )
					$append_arr = explode(",", $obj) ?? [];
					$val = "";
					if(count($append_arr) >= 2){
						foreach ($append_arr as $arr_val) {
							if(isset($v->$arr_val)){
								$val .= $v->$arr_val. " ";
							}
						}
						$val = trim($val);
					}

					// will strip property name from [0] and give back clean property, then will give number inside brackets, and if it is number, attach it at the end of object.
					unset($replaced_obj_index);
					$replaced_obj = preg_replace($array_index_pattern, $array_index_replacement_obj, $obj);
					$replaced_obj_index = preg_replace($array_index_pattern, $array_index_replacement_index, $obj);
					if (isset($v->$replaced_obj)) {
						$v = $v->$replaced_obj;
						if (
							is_numeric($replaced_obj_index) &&
							isset($v[0])
						) {
							$v = $v[0];
						}
						// Custom logic for groups, there might be better way, but I need to list all group names, not all group object from sql table.
						if ($obj == 'groups') {
							if (is_object($v) || is_array($v)) {
								$groups = '';
								$i = 0;

								foreach ($v as $v_key => $v_value) {
									$name = null;

									// Check if $v_value is an object and has 'name' property
									if (is_object($v_value)) {
										if (property_exists($v_value, 'name')) {
											$name = $v_value->name;
										} else if (isset($v_value['name'])) {
											$name = $v_value['name'];
										}
									}
									// Check if $v_value is an array and has 'name' key
									elseif (is_array($v_value) && isset($v_value['name'])) {
										$name = $v_value['name'];
									}

									// If we found a name, append it to $groups
									if ($name !== null) {
										if ($i > 0) {
											$groups .= ', ';
										}
										$groups .= $name;
										$i++;
									}
								}

								// Set $v to the concatenated result
								$v = $groups;
							}


						}

						if (
							$obj == "targetcatalogues" &&
							is_object($v)
						) {
							$groups = '';
							$i = 0;
							foreach ($v as $v_key => $v_value) {
								if ($i > 0) {
									$groups = $groups . ', ';
								}
								$groups = $groups . $v_value->name;
								$i++;
							}
							$v = $groups;
						}

						// Same for managers
						if (
							($obj == 'managerscompact' || $obj == 'managers') &&
							is_object($v)
						) {
							$managers = '';
							$i = 0;
							foreach ($v as $v_key => $v_value) {
								if ($i > 0) {
									$managers = $managers . ', ';
								}
								$fname = $v_value->fname ?? $v_value->manager_fname ?? NULL;
								$lname = $v_value->lname ?? $v_value->manager_lname ?? NULL;
								$managers = $managers . $fname . ' ' . $lname;
								$i++;
							}
							$v = $managers;
						}

						if (
							(
								$obj == 'SubDepartmentsCompact' ||
								$obj == 'sub_departments_compact' ||
								$obj == 'subdepartments'
							) &&
							(
								is_object($v) || is_array($v)
							)
						) {
							$sub_departments_compact = '';
							$i = 0;

							foreach ($v as $v_key => $v_value) {
								$name = null;

								// Check if $v_value is an object and has 'name' property
								if (is_object($v_value)) {
									if (property_exists($v_value, 'name')) {
										$name = $v_value->name;
									} else if (isset($v_value['name'])) {
										$name = $v_value['name'];
									}
								}
								// Check if $v_value is an array and has 'name' key
								elseif (is_array($v_value) && isset($v_value['name'])) {
									$name = $v_value['name'];
								}


								// If we found a name, append it to $sub_departments_compact
								if ($name !== null) {
									if ($i > 0) {
										$sub_departments_compact .= ', ';
									}
									$sub_departments_compact .= $name;
									$i++;
								}
							}

							// Set $v to the concatenated result
							$v = $sub_departments_compact;
						}


						// Same for standard_list
						if (
							$obj == 'standard_list' &&
							is_array($v)
						) {
							$standard_list = '';
							$i = 0;
							foreach ($v as $v_key => $v_value) {
								if ($i > 0) {
									$standard_list = $standard_list . ', ';
								}
								$standard_list = $standard_list . $v_value;
								$i++;
							}
							$v = $standard_list;
						}

						// Same for LLDD and health problem category
						if (
							$obj == 'LLDDandHealthProblem'
						) {
							$v_decoded = json_decode($v, true);
							if (
								is_array($v_decoded) &&
								count($v_decoded) > 0
							) {
								$LLDDandHealthProblem = '';
								$i = 0;
								foreach ($v_decoded as $v_key => $v_value) {
									foreach ($v_value as $key => $v_sub_value) {
										if ($key == 'LLDDCat') {
											$cat_name = \Models\LlddHealthProblemsCategory
												::find($v_sub_value)
											;
											if ($i > 0) {
												$LLDDandHealthProblem = $LLDDandHealthProblem . ', ';
											}
											$LLDDandHealthProblem = $LLDDandHealthProblem . $cat_name->name;
											$i++;
										}
									}
								}
								$v = $LLDDandHealthProblem;
							} else {
								$v = '';
							}
						}
						if($obj == 'programmes' && is_array($v))
						{
							$programmes = '';
							$i = 0;
							foreach ($v as $v_key => $v_value) {
								if ($i > 0) {
									$programmes = $programmes . ', ';
								}
								$programmes = $programmes .  $v_value->name;
								$i++;
							}
							$v = $programmes;

						}
						if($obj == 'duration' && isset($objs[$key_obj-1]) && $objs[$key_obj-1]=='schedule'){
							$v= $v." Minutes";
						}
						if($obj=='createdby' && is_object($v))
						{
							$v=$v->fname." ".$v->lname;
						}
						if($obj == "total_duration")
						{
							if($row->total_duration && $row->users_count)
							{
								$v=round($row->total_duration/$row->users_count,2)." Minutes";
							}else
							{
								$v="0 Minutes";
							}
						}
					} elseif($val != ""){
						$v = $val;
					}elseif($obj== "learning_results"){
						$skill = $v->LearningResults()->whereHas('Module',function ($query) use ($field_name){
							$query->where('name',$field_name);
						})->first();
						// \Carbon\Carbon::parse($skill->due_at)->format(\APP\Tools::getConfig('defaultDateFormat'))
						if ($skill){
							$v = "$skill->completion_status (" . \Carbon\Carbon::parse($skill->due_at)->format(\APP\Tools::getConfig('defaultDateFormat')) . ")";
							$due_at = new DateTime($skill->due_at);
							$due_days = $due_at->diff(new DateTime())->days;
							if($due_days >= 0 && $skill->completion_status !== "completed"){
								$v = "due (" . \Carbon\Carbon::parse($skill->due_at)->format(\APP\Tools::getConfig('defaultDateFormat')) . ")";
							}
						}else{
							$v = "";
						}
					}else {
						$v = false;
						break;
					}
				}
				if (is_array($v)) {
					$v = json_decode(json_encode($v), true);
					if (\APP\Tools::isMultiArray($v)) {
						$row_data[] = print_r($v, true);
					} else {
						$row_data[] = implode(", ", $v);
					}
				} else if (is_object($v)) {
					if ($v instanceof \Carbon\Carbon) {
						$row_data[] = $v->toDateTimeString();
					}
					else {
						$row_data[] = print_r($v, true);
					}
				} else {
					$row_data[] = strval($v);
				}
			}
			$export_data[] = $row_data;
		}

		$writer = new \OpenSpout\Writer\XLSX\Writer();
		$writer->openToFile($download_file_name);

		$sheet = $writer->getCurrentSheet(); // get ONCE

		// Set column widths from header
		$headerRowValues = $export_data[0];
		foreach ($headerRowValues as $colIndex => $value) {
			$length = mb_strlen((string) $value);
			$width = min(max($length * 1.6, 10), 50);
			$sheet->setColumnWidth($width, $colIndex + 1);
		}


		// Define a border: light grey, thin, solid, all sides
		$border = new Border(
			new BorderPart(Border::BOTTOM, 'D3D3D3', Border::WIDTH_THIN, Border::STYLE_SOLID),
			new BorderPart(Border::TOP, 'D3D3D3', Border::WIDTH_THIN, Border::STYLE_SOLID),
			new BorderPart(Border::LEFT, 'D3D3D3', Border::WIDTH_THIN, Border::STYLE_SOLID),
			new BorderPart(Border::RIGHT, 'D3D3D3', Border::WIDTH_THIN, Border::STYLE_SOLID),
		);

		// Define header style
		$headerStyle = (new Style())
			->setFontBold()
			->setFontSize(14)
			->setBorder($border)
			->setCellAlignment(CellAlignment::CENTER)
		;


		// Write header
		$headerRow = Row::fromValues($export_data[0], $headerStyle);
		$writer->addRow($headerRow);

		// Define styles
		$evenRowStyle = (new Style())
			->setBorder($border)
			->setBackgroundColor('EEEEEE');

		// Write remaining rows
		for ($i = 1, $n = count($export_data); $i < $n; $i++) {
			$rowData = $export_data[$i];
			$row = ($i % 2 === 0)
				? Row::fromValues($rowData, $evenRowStyle)
				: Row::fromValues($rowData); // no style for odd rows

			$writer->addRow($row);
		}

		// Freeze top row
		$sheetView = new SheetView();
		$sheetView->setFreezeRow(2);
		$sheet->setSheetView($sheetView);

		// Apply auto-filter to first row
		$columnCount = count($headerRowValues);
		$autoFilter = new AutoFilter(0, 1, $columnCount - 1, 1);
		$sheet->setAutoFilter($autoFilter);

		$writer->close();


		// Log export!
		$log_id = \Models\LogExportImport::insertRecord(file_get_contents($download_file_name), '.xlsx', basename($download_file_name), $created_user, 'exports', json_encode($params), $return_log_id);
		if ($log_id) {
			return $log_id;
		}
	}

	public static function isMultiArray($a) {
		foreach($a as $v) if(is_array($v)) return TRUE;
		return FALSE;
	}

	public static function combineJsAssets($settings) {

		// list of directories in "src/tpls/js" that contains multiple files each and each directory is combined into single file.
		$combine_list = [
			'controllers',
			'services',
			'directives',
			'factories',
			'filters'
		];

		foreach ($combine_list as $key => $combine_file) {

			// Get full path of file to combine into
			$js_path = $settings["LMSPublicPath"] . "js/" . $combine_file . ".js";
			$js_path_minify = $settings["LMSPublicPath"] . "js/" . $combine_file . ".min.js";

			// variable where to put combined files
			$js = "";

			// Loop into template/js/$combine_file directory and combine all files
			foreach (glob($settings["LMSTplsPath"] . "js/" . $combine_file . "/*.js") as $filename) {
				$js .= "\n//" . basename($filename)  . "\n" . file_get_contents($filename) . "\n\n\n";
			}

			// write result in file, not finished yet, but this should work without errors
			if (is_writable($js_path)) {
				file_put_contents($js_path, $js);
			}

			// function where specific variables are extracted and then JavaScript file is included to replace php tags with given variables
			$generate_js = function () use ($js_path, $settings) {
				extract(\APP\Templates::getVariables($settings));
				include($js_path);
			};

			// output buffer is turned on, output is stored in internal buffer, nothing is sent as response
			ob_start();
			$generate_js();
			// output buffer is turned off, all buffer that was generated after "ob_start" is put into $js variable
			$js = ob_get_clean();

			// Replace site version labels
			$translator = \APP\Templates::getTranslator();
			$js = $translator->replaceVersionLabels($js);

			// Check if source map exists (generated by gulp build process)
			$source_map_path = $settings["LMSPublicPath"] . "js/" . $combine_file . ".js.map";
			$minified_source_map_path = $settings["LMSPublicPath"] . "js/" . $combine_file . ".min.js.map";
			
			// Add source map reference if map file exists
			if (file_exists($source_map_path)) {
				$js .= "\n//# sourceMappingURL=" . $combine_file . ".js.map";
			}

			// Write generated/finalized javascript file
			if (is_writable($js_path)) {
				file_put_contents($js_path, $js);
			}

			// Minify JS file too
			$minifier = new Minify\JS($js_path);
			if (is_writable($js_path_minify)) {
				$minified_content = $minifier->minify();
				
				// Add source map reference to minified file if map exists
				if (file_exists($minified_source_map_path)) {
					$minified_content .= "\n//# sourceMappingURL=" . $combine_file . ".min.js.map";
				}
				
				file_put_contents($js_path_minify, $minified_content);
			}
		}
	}

	// If username exists within different ULN, add number at the end
	public static function setUniqueUsername($name, $uln = null, $iteration = 0) {
		if (empty($name)) {
			$email = 'no-username';
		}
		$return = $name;
		if ($iteration > 0) {
			$combinedName = $name . $iteration;
		} else {
			$combinedName = $name;
		}
		$user = \Models\User
			::where('username', '=', $combinedName)
		;
		if ($uln) {
			$user = $user->where('ULN', '!=', $uln);
		}
		$user = $user->first();
		if ($user) {
			// username taken, add iteration and try again
			$iteration++;
			$return = \APP\Tools::setUniqueUsername($name, $uln, $iteration);
		} else {
			// username is not taken, return as safe
			$return = $combinedName;
		}
		return $return;
	}

	// if e-mail provided exists within another user with different ULN, change e-mail to something else.
	public static function setUniqueEmail($email = false, $uln = false, $iteration = 0) {
		//if e-mail does not exists, use default template
		if (empty($email) || !$email) {
			$email = '<EMAIL>';
		}
		$return = $email;
		if ($iteration > 0) {
			$combinedEmail = $email . $iteration;
		} else {
			$combinedEmail = $email;
		}
		$user = \Models\User
			::where('email', $combinedEmail)
		;
		if ($uln) {
			$user = $user
				->where(function ($query) use ($uln) {
					$query
						->where('ULN', '!=', $uln)
						->orWhereNull('ULN')
					;
				})
			;
		}
		$user = $user->first();

		if ($user) {
			// email taken, add iteration and try again
			$iteration++;
			$return = \APP\Tools::setUniqueEmail($email, $uln, $iteration);
		} else {
			// email is not taken, return as safe
			$return = $combinedEmail;
		}
		return $return;
	}


	// look into object/array for email/name/surname and make login from that combination
	public static function ilrUsername($ilr) {
		$return = false;

		if (isset($ilr['email']) && $ilr['email']) {
			$return = $ilr['email'];
		} elseif ((isset($ilr['fname']) && $ilr['fname']) && (isset($ilr['lname']) && $ilr['lname'])) {
			$return =
				strtoupper(preg_replace("/[^a-zA-Z0-9]+/", "", $ilr['fname'][0]))
				.
				ucfirst(strtolower(preg_replace("/[^a-zA-Z0-9]+/", "", $ilr['lname'])))
			;
		} elseif (isset($ilr->Email) && $ilr->Email) {
			$return = $ilr->Email;
		} elseif ((isset($ilr->GivenNames) && $ilr->GivenNames) && (isset($ilr->FamilyName) && $ilr->FamilyName)) {
			$return =
				strtoupper(preg_replace("/[^a-zA-Z0-9]+/", "", $ilr->GivenNames[0]))
				.
				ucfirst(strtolower(preg_replace("/[^a-zA-Z0-9]+/", "", $ilr->FamilyName)))
			;
		} elseif (isset($ilr['TRAINEE_EMAIL'])) {
			$return = $ilr['TRAINEE_EMAIL'];
		} elseif ((isset($ilr['TRAINEE_FIRSTNAME']) && $ilr['TRAINEE_FIRSTNAME']) && (isset($ilr['TRAINEE_LASTNAME']) && $ilr['TRAINEE_LASTNAME'])) {
			$return =
				strtoupper(preg_replace("/[^a-zA-Z0-9]+/", "", $ilr['TRAINEE_FIRSTNAME'][0]))
				.
				ucfirst(strtolower(preg_replace("/[^a-zA-Z0-9]+/", "", $ilr['TRAINEE_LASTNAME'])))
			;
		}
		$uln = null;
		if (isset($ilr->ULN) && $ilr->ULN) {
			$uln = $ilr->ULN;
		} elseif (isset($ilr['TRAINEE_UNIQUELEARNERNUMBER']) && $ilr['TRAINEE_UNIQUELEARNERNUMBER']) {
			$uln = $ilr['TRAINEE_UNIQUELEARNERNUMBER'];
		}
		return \APP\Tools::setUniqueUsername($return, $uln);
	}

	// ILR, iterate all accurances of Learner's propery and return json encoded string
	public static function ilrjSonEncodeField($ilr, $field, $return_raw = false) {
		$results = array();
		foreach ($ilr as $key => $value) {
			if ($key == $field) {
				$results[] = $value;
			}
		}
		if ($return_raw) {
			return $results;
		}
		$results = json_encode($results);
		// custom overrides/fixes
		if ($field == 'LearningDelivery') {
			$results = json_decode($results);
			foreach ($results as $ld_key => $LearningDelivery) {
				foreach ($LearningDelivery as $ld_item_key => $ld_item_value) {

					if ($ld_item_key == 'DelLocPostCode') {
						$results[$ld_key]->{$ld_item_key} = trim($ld_item_value);
					}

					if ($ld_item_key == 'LSDPostcode') {
						$results[$ld_key]->{$ld_item_key} = trim($ld_item_value);
					}

					if ($ld_item_key == 'LearningDeliveryFAM') {
						if (is_object($ld_item_value)) {
							$results[$ld_key]->{$ld_item_key} = [$ld_item_value];
						}
					}
				}
			}
			$results = json_encode($results);
		}
		return $results;
	}

	public static function ilrInvalid($data, $key) {
		return empty($data[$key]) || $data[$key] == "" || $data[$key] == null;
	}

	// Apply custom logic to ILR fields
	// Most of it will delete entries if one of pairs are missing.
	public static function ilrfieldClean($ilr, $field) {
		$response = false;
		$check_keys = ['LearnerEmploymentStatus', 'LearningDelivery', 'ProviderSpecLearnerMonitoring'];

		if (isset($ilr[$field]) && $ilr[$field]) {

			// Do modifications only to specific fields
			if (in_array($field, $check_keys)) {
				foreach ($ilr[$field] as $mkey => $subfield) {

					//ProviderSpecLearnerMonitoring
					if ($field == 'ProviderSpecLearnerMonitoring') {
						if (
							\APP\Tools::ilrInvalid($subfield, 'ProvSpecLearnMonOccur') ||
							\APP\Tools::ilrInvalid($subfield, 'ProvSpecLearnMon')
						) {
							unset($ilr[$field][$mkey]);
						}
					}


					//LearnerEmploymentStatus
					if (isset($subfield['EmploymentStatusMonitoring']) && $subfield['EmploymentStatusMonitoring']) {
						foreach ($subfield['EmploymentStatusMonitoring'] as $key => $subsubfield) {
							if (
								\APP\Tools::ilrInvalid($subsubfield, 'ESMType') ||
								\APP\Tools::ilrInvalid($subsubfield, 'ESMCode')
							) {
								unset($ilr[$field][$mkey]['EmploymentStatusMonitoring'][$key]);
							}
						}
						$ilr[$field][$mkey]['EmploymentStatusMonitoring'] = array_values($ilr[$field][$mkey]['EmploymentStatusMonitoring']);
					}

					//LearningDelivery
					if (isset($subfield['LearningDeliveryFAM']) && $subfield['LearningDeliveryFAM']) {
						foreach ($subfield['LearningDeliveryFAM'] as $key => $subsubfield) {
							if (
								\APP\Tools::ilrInvalid($subsubfield, 'LearnDelFAMType') ||
								\APP\Tools::ilrInvalid($subsubfield, 'LearnDelFAMCode')
							) {
								unset($ilr[$field][$mkey]['LearningDeliveryFAM'][$key]);
							}
						}
						$ilr[$field][$mkey]['LearningDeliveryFAM'] = array_values($ilr[$field][$mkey]['LearningDeliveryFAM']);
					}
				}
				$ilr[$field] = array_values($ilr[$field]);
			}
			$response = $ilr[$field];
		}

		return $response;
	}

	// encode json object to json string if found
	public static function ilrJsonEncod($ilr, $field) {
		$response = \APP\Tools::ilrfieldClean($ilr, $field);
		$skip = false;

		// Oh these custom problems...
		if (
			$field == 'LearnerHE' &&
			is_array($response) &&
			count($response) > 0 &&
			isset($response[0]) &&
			$response[0]
		) {

			foreach ($response[0] as $key => $value) {
				if (empty($value) || $value == null) {
					unset($response[0][$key]);
				}
			}

			if (count($response[0]) == 0) {
				$response = '[{}]';
			} else {
				$response = json_encode($response);
			}
			$skip = true;
		}

		if (!$skip) {
			if (
				!empty($response) &&
				(
					is_array($response) ||
					is_object($response)
				) &&
				count($response) > 0
			) {
				$response = json_encode($response);
			} else {
				$response = '[]';
			}
		}
		return $response;
	}

	public static function setFileFolderPermission($location, $default_group = 'sftp', $default_user = 'www-data', $default_permission = 'rwx') {
		if (is_dir($location) || is_file($location)) {
			exec ("setfacl -R -d -m group:" . $default_group . ":" . $default_permission . " '" . $location. "'");
			exec ("setfacl -R -m group:" . $default_group . ":" . $default_permission . " '" . $location . "'");
			exec ("chown -R " . $default_user . ":" . $default_group . " '" . $location . "'");
		}
	}

	// Generate tree structure, internet to the rescue
	// https://stackoverflow.com/questions/29384548/php-how-to-build-tree-structure-list
	public static function buildTree($elements, $parentId = 0) {
		$branch = array();
		// sort array by order
		usort($elements, function($a, $b) {
			//return strcmp($a['order'], $b['order']); // string
			return $a['order'] <=> $b['order'];
		});

		foreach ($elements as $element) {
			if ($element['parent_id'] == $parentId) {
				$children = \APP\Tools::buildTree($elements, $element['id']);
				if ($children) {
					$element['children'] = $children;
				}
				$branch[] = $element;
			}
		}
		return $branch;
	}

	// delete directory recurse
	public static function delDirTree($dir) {
		$files = array_diff(scandir($dir), array('.','..'));
		foreach ($files as $file) {
			(is_dir("$dir/$file")) ? \APP\Tools::delDirTree("$dir/$file") : unlink("$dir/$file");
		}
		return rmdir($dir);
	}

	// copy one directory over another
	public static function recurseCopy($src, $dst, $newest_only = false) {
		if (
			is_file($src) ||
			is_dir($src)
		) {
			$dir = opendir($src);
			@mkdir($dst);
			while(false !== ( $file = readdir($dir)) ) {
				if (( $file != '.' ) && ( $file != '..' )) {
					if ( is_dir($src . '/' . $file) ) {
						\APP\Tools::recurseCopy($src . '/' . $file, $dst . '/' . $file);
					} else {
						if (
							(
								is_file($src . '/' . $file) &&
								!$newest_only
							) ||
							(
								is_file($src . '/' . $file) &&
								$newest_only &&
								(
									!is_file($dst . '/' . $file) ||
									filemtime($src . '/' . $file) > filemtime($dst . '/' . $file) ||
									filesize($src . '/' . $file) != filesize($dst . '/' . $file)
								)
							)
						) {
							copy($src . '/' . $file, $dst . '/' . $file);
						}
					}
				}
			}
			closedir($dir);
		}
	}

	public static function safeName($string, $replace = "", $lowercase = false) {
		if ($lowercase) {
			$string = strtolower($string);
		}
		return preg_replace("/[^a-zA-Z0-9_]+/", $replace, $string);
	}

	public static function documentMime($types = false) {
		$all =  [
			'application/epub+zip',
			'application/excel',
			'application/msword',
			'application/pdf',
			'application/postscript',
			'application/vnd.ms-powerpoint',
			'application/vnd.oasis.opendocument.text',
			'application/vnd.openxmlformats-officedocument.presentationml.presentation',
			'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
			'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
			'application/x-mpegURL',
			'application/xml',
			'audio/3gpp',
			'audio/3gpp2',
			'audio/aac',
			'audio/aacp',
			'audio/mp3',
			'audio/mp4',
			'audio/mp4',
			'audio/mp4a-latm',
			'audio/mpeg',
			'audio/mpeg3',
			'audio/x-hx-aac-adts',
			'audio/mpeg4-generic',
			'audio/x-m4a',
			'audio/x-mpeg-3',
			'audio/x-wav',
			'image/bmp',
			'image/gif',
			'image/jpeg',
			'image/png',
			'image/vnd.adobe.photoshop',
			'text/plain',
			'text/rtf',
			'text/tab-separated-values',
			'video/3gpp',
			'video/H261',
			'video/H263',
			'video/H264',
			'video/H265',
			'video/MP2T',
			'video/mp4',
			'video/MPV',
			'video/ogg',
			'video/quicktime',
			'video/VP8',
			'video/x-flv',
			'video/x-ms-asf',
			'video/x-ms-wmv',
			'video/x-msvideo',
			'application/vnd.ms-office',
			'application/cdfv2-unknown',
			'application/vnd.oasis.opendocument.spreadsheet',
			'application/vnd.ms-excel',
			'application/zip',
			'application/octet-stream',
			'application/x-zip-compressed',
			'multipart/x-zip'
		];

		$zip = ['application/zip', 'application/octet-stream', 'application/x-zip-compressed', 'multipart/x-zip'];

		$excel = ['application/vnd.ms-excel', 'application/msexcel', 'application/x-msexcel', 'application/x-ms-excel', 'application/x-excel', 'application/x-dos_ms_excel', 'application/xls', 'application/x-xls', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/octet-stream', 'application/zip' , 'text/csv'];
		$json = ['application/json','text/plain'];
		// baffling that come excels are application/zip as mime, oh well

		if (!$types) {
			$result = array_merge($all);
		} else {
			if (is_array($types)) {
				$result = [];
				foreach ($types as $key => $type) {
					$result = array_merge($result, $$type);
				}
			} else {
				$result = $$types;
			}
		}

		return $result;
	}

	public static function allowExtensions($types = false) {
		$images = ['png', 'jpg', 'gif', 'bmp', 'jpeg',];
		$audio = ['mp3', 'ogg', 'swf', 'm4a', 'wav', 'aac', 'mp4a',];
		$video = ['mp4', 'wmv', 'asf', 'flv', 'mov',];

		$documents = ['doc', 'docx', 'xlsx', 'pps', 'pptx', 'ppt', 'txt', 'rtf', 'odt', 'pdf', 'pub', 'msg', 'ods', 'xls', 'potm', 'pptm', 'ppsx', 'xlsm',];
		$excel = ['xlsx','xls','xlsm','csv']; // Will not be combined into result, still part of documents

		$misc = ['zip',];


		if (!$types) {
			$result = array_merge($images, $audio, $video, $documents, $misc);
		} else {
			if (is_array($types)) {
				$result = [];
				foreach ($types as $key => $type) {
					$result = array_merge($result, $$type);
				}
			} else {
				$result = $$types;
			}
		}

		return $result;
	}

	public static function getConfig($key = false, $default_value = false,$user_id=false) {
		// Use ConfigCache for performance optimization while maintaining backward compatibility
		if ($key) {
			return \APP\ConfigCache::get($key, $default_value ?: false, $user_id);
		}
		
		return false;
	}

	/**
	 * Legacy getConfig method (preserved for reference)
	 * This method has been replaced with ConfigCache::get() for performance optimization
	 * 
	 * @deprecated This implementation is preserved for reference but no longer used
	 */
	private static function getConfigLegacy($key = false, $default_value = false,$user_id=false) {
		$company_id = null;

		if(!$user_id){
			if(Auth::getUser()){
				$company_id = Auth::getUserCompanyId();
			}
		}else{
			$user_exists = User::where('id',$user_id)->first();
			if ($user_exists) {
				$company_id = $user_exists->company_id;
			}
		}
		if ($key) {
			$query = \Models\Configuration
				::where('status', '=', 1)
				->where('key', '=', $key)
				->first()
				;

			// Custom overwrite for isBlackColourScheme, will have to rethink this...
			// If user is loged in branded company site, then check for theme in company entry
			if ($key == 'isBlackColourScheme') {
				// If learner and has color_scheme set, overwrite isBlackColourScheme!
				if (\APP\Auth::isLearner()) {
					$color_scheme = \Models\TableExtension::getValue('users', \APP\Auth::getUserId(), 'color_scheme');
					if ($color_scheme) {
						switch ($color_scheme) {
							case 'light':
								return false;
								break;

							case 'dark':
								return true;
								break;
						}
					}
				}
				if (!empty($_SESSION["branded_company"])) {
					$company = \Models\Company::find($_SESSION["branded_company"]);
					if ($company) {
						\Models\TableExtension::returnAllFields('companies', $company->id, $company);
						if (!empty($company->extended->learner_theme)) {
							if ($company->extended->learner_theme == 'light') {
								return false;
							} else {
								return true;
							}
						}
					}
				}
			}


			if ($query) {
				if ($company_id) {
					$company_config = \Models\CompanyConfiguration::where('company_id', $company_id)
						->where('configuration_id', $query->id)->first();
					if ($company_config) {
						$query->value = $company_config->value;
					}
				}

				$value = $query->value;
				if (
					(
						empty($value) &&
						!empty($query->default_value)
					) ||
					(
						$default_value &&
						!empty($query->default_value)
					)
					) {
						$value = $query->default_value;
				}

				if ($query->type == 'boolean') {
					return $value == '1' ? true : false;
				}
				if ($query->type == 'integer' && intval($value) == floatval($value)) {
					return intval($value);
				}
				if ($query->type == 'list') {
					return $value > "" ? explode(",", $value) : [];
				}

				if ($query->type == 'list-definition') {
					return $query->value > "" ? json_decode($query->value, true) : [];
				}
				return isset($value) ? $value : '';
			}

		}
		return false;
	}

	/**
	 * Calculate dead line date from start date and subtracting hours given in the config
	 *
	 * @param $startDate
	 * @return Carbon|null
	 */
	public static function calculateDeadline($startDate)
	{
		$bookingDeadline = self::getConfig('BookingDeadline');
		$hoursToSubtract = \APP\Tools::subArrayKeyValue($bookingDeadline, 'name', 'DeadlineHours', 'value');

		if($hoursToSubtract <= 0) {
			return null;
		}

		return Carbon::parse($startDate)->subHours((int)$hoursToSubtract);
	}

	/**
	 * Calculate drop-off deadline date for the event from start date and subtracting hours given in the config
	 *
	 * @param $startDate
	 * @return Carbon|null
	 */
	public static function calculateEventDropOffDeadline($startDate)
	{
		$bookingDeadline = self::getConfig('EventDropOffDeadline');
		$hoursToSubtract = \APP\Tools::subArrayKeyValue($bookingDeadline, 'name', 'EventDropOffDeadlineHours', 'value');

		if($hoursToSubtract <= 0) {
			return null;
		}

		return Carbon::parse($startDate)->subHours((int)$hoursToSubtract);
	}

	public static function updateConfig ($key, $value) {
		if (
			!empty($key) &&
			(
				isset($value) ||
				$value == false
			)
		) {
			$query = \Models\Configuration
				::where('status', 1)
				->where('key', $key)
				->first();
			;
			if ($query) {
				$query->value = $value;
				$query->save();
			}
		}
	}

	// Learner reference number retrieval and update
	// https://bitbucket.org/emilrw/scormdata/issues/340/learner-reference-number-automated
	public static function getLearnerRefNum($number = false) {
		if (!$number) {
			$number = \APP\Tools::getConfig('learnerReferenceNumberIteration');
		}
		return substr(\APP\Tools::getConfig('learnerReferenceNumberId'), 0, 6) . str_pad($number, 6, '0', STR_PAD_LEFT);
	}
	public static function updateLearnerRefNum($new_value = false) {
		$query = \Models\Configuration
			::where('key', '=', 'learnerReferenceNumberIteration')
			->first();
		;
		if ($new_value) {
			$query->value = $new_value;
		} else {
			$query->value = $query->value + 1;
		}
		$query->save();
	}

	public static function base64Toggle($str) {
		if (
			$str &&
			!preg_match('~[^0-9a-zA-Z+/=]~', $str)
		) {
			$check = str_split(base64_decode($str));
			$x = 0;
			foreach ($check as $char) {
				if (ord($char) > 126) {
					$x++;
				}
			};
			if ($x/count($check)*100 < 30) {
				return base64_decode($str);
			}
			//return false;
		}
		return false;
	}

	// Get timing number, depending on given type
	public static function getTimings($module) {
		switch($module->type->slug) {
			// e-learning
			case 'e_learning':
				$timing_key = "learningResource_elearning";
				break;
			// youtube
			case 'youtube':
				$timing_key = "learningResource_youtube";
				break;
			// vimeo
			case 'vimeo':
				$timing_key = "learningResource_vimeo";
				break;
			// web page
			case 'webpage':
				$timing_key = "learningResource_webpage";
				break;
			// book/cd/dvd
			case 'book_cd_dvd':
				$timing_key = "learningResource_bookCdDvd";
				break;
			// Evidence
			case 'upload':
				$timing_key = "learningResource_evidence";
				break;
		}
		if ($module->is_course) {
			$timing_key = "lessonTimings_assignedDue";
		}
		if (isset($timing_key)) {
			$due_at_offset = \Models\Timing
				::where("key", "=", $timing_key)
				->first()
			;
			if (
				isset($due_at_offset) &&
				$due_at_offset &&
				isset($due_at_offset->timing)
			) {
				return $due_at_offset->timing;
			}
		}
		return false;
	}

	public static function removeSubArray ($array, $keyField, $fieldValue, $invert = false) {
		foreach ($array as $key => $value) {
			if ($invert) { // delete elements if key does not match value
				if ($value[$keyField] != $fieldValue) {
					unset($array[$key]);
				}
			} else { // delete elements if key matches value
				if ($value[$keyField] == $fieldValue) {
					unset($array[$key]);
				}
			}
		}
		return array_values($array);
	}

	// Convert image to JPG
	public static function convertImageToJpg($originalImage, $outputImage, $quality = 95) {
		$imageTmp = false;

		switch (exif_imagetype($originalImage)) {
			case IMAGETYPE_PNG:
				$imageTmp = imagecreatefrompng($originalImage);
			break;
			case IMAGETYPE_JPEG:
				$imageTmp = imagecreatefromjpeg($originalImage);
			break;
			case IMAGETYPE_GIF:
				$imageTmp = imagecreatefromgif($originalImage);
			break;
			case IMAGETYPE_BMP:
				$imageTmp = imagecreatefrombmp($originalImage);
			break;
			// Defaults to JPG
			default:
				$imageTmp = imagecreatefromjpeg($originalImage);
			break;
		}

		if ($imageTmp) {
			// quality is a value from 0 (worst) to 100 (best)
			imagejpeg($imageTmp, $outputImage, $quality);
			imagedestroy($imageTmp);
			return true;
		}

		return false;
	}

	public static function differenceInDays (&$params) {
		$response = false;
		if (
			isset($params["difference_in_days"]) &&
			$params["difference_in_days"] &&
			$params["difference_in_days"] > 0
		) {
			$response = $params["difference_in_days"];
			unset($params["difference_in_days"]);
		}

		return $response;
	}

	public static function isSkillScan () {
		// Check if there is any resource with skill scan check - cached for performance
		$queryCallback = function() {
			return \Models\LearningModule
				::where('status', true)
				->where('is_skillscan', true)
				->exists()
			;
		};
		
		if (!function_exists('cache')) {
			// Fallback for CLI/contexts without cache
			return $queryCallback();
		}
		
		$cacheKey = \APP\Cache\CacheHelper::key('learning_modules', 'has_skillscan');
		return cache()->remember($cacheKey, 3600, $queryCallback); // 1 hour cache
	}

	public static function isPendingAssessment () {

		$query = \Models\LearningResult
			::select('learning_results.*')
			->where("refreshed", false)
			->join("user_learning_modules", function($join) {
				$join
					->on("user_learning_modules.user_id", "=", "learning_results.user_id")
					->on("user_learning_modules.learning_module_id", "=", "learning_results.learning_module_id")
					->whereNull('user_learning_modules.deleted_at')
				;
			})
			->join("users", function($join) {
				$join
					->on("learning_results.user_id", "=", "users.id")
					->where("users.status", "=", 1)
				;
				if (
					!\APP\Auth::accessAllCompanies() &&
					!\APP\Auth::accessAllLearners()
				) {
					$join->where("users.company_id", \APP\Auth::getUser()->company_id);
				}
			})
			->join("learning_modules", function($join) {
				$join
					->on("learning_results.learning_module_id", "=", "learning_modules.id")
					->where("learning_modules.status", "=", 1)
				;
				if (\APP\Auth::isAdminInterface()) {
					$join = $join
						->where('learning_modules.track_progress', true)
					;
				}
			})
			->leftjoin("learning_sessions", function($join) {
				$join
					->on("learning_sessions.learning_module_id", "=", "learning_results.learning_module_id")
					->on("learning_sessions.user_id", "=", "learning_results.user_id")
					->where("learning_sessions.completed", "=", 0)
					->where("learning_sessions.approved", "=", 0)
				;
			})
			->leftjoin("learning_module_evidence_meetings", function($join) {
				$join
					->on("learning_module_evidence_meetings.learning_modules_id", "=", "learning_results.learning_module_id")
					->on("learning_module_evidence_meetings.user_id", "=", "learning_results.user_id")
				;
			})
			->join("assessment_data", function($join) {
				$join
					->on("assessment_data.user_id", "=", "learning_results.user_id")
					->on("assessment_data.course_id", "=", "learning_results.learning_module_id")
					->where("assessment_data.status", "<>", 4)
					->whereNotNull("assessment_data.submitted_at")
				;
			})
			->where('learning_modules.is_skillscan', false)
		;
		if (!\APP\Auth::accessAllLearners()) {
			if (
				\APP\Auth::isManager() ||
				\APP\Auth::isAdmin()
			) {
				$query->where(function($query) {
					$query
						->whereIn(
							"learning_results.user_id",
							\Models\ManagerUser
								::where('manager_id', \APP\Auth::getUserId())
								->select('user_id')
								->get()
						)
						->orWhereIn(
							"learning_results.learning_module_id",
							\Models\ManagerLearningModule
								::select('manager_learning_modules.learning_module_id')
								->where('manager_learning_modules.manager_id', \APP\Auth::getUserId())
								->get()
						)
					;
				});
			}
		}

		$query = $query
			->get()
		;


		return count($query) > 0;
	}

	// check if pattern holds correct ID
	public static function checkDatePatternIn ($frequency_pattern, $id) {
		return array_key_exists('period', $frequency_pattern) &&
			is_array($frequency_pattern['period']) &&
			array_key_exists('id', $frequency_pattern['period']) &&
			$frequency_pattern['period']['id'] == $id &&
			array_key_exists('frequency', $frequency_pattern) &&
			$frequency_pattern['frequency'] > 0
		;
	}

	// Calculate next send date according to pattern and if given $last_send_date.
	public static function nextDateFromPattern ($frequency_pattern = false, $last_send_date = false) {

		$now = \Carbon\Carbon::now();
		if ($last_send_date) {
			$last_send_date = \Carbon\Carbon::parse($last_send_date);
		}
		$today_format = $now->format('Y-m-d');
		$send_today = true;
		$next_send_date = false;

		$day_of_week = $now->dayOfWeekIso; // 1 - 7
		$week_in_month = $now->weekNumberInMonth;

		if ($frequency_pattern) {

			// If day/date/month array is present, sort by value
			if (
				array_key_exists('date', $frequency_pattern) &&
				is_array($frequency_pattern['date']) &&
				count($frequency_pattern['date']) > 0
			) {
				asort($frequency_pattern['date']);
			}
			if (
				array_key_exists('day', $frequency_pattern) &&
				is_array($frequency_pattern['day']) &&
				count($frequency_pattern['day']) > 0
			) {
				asort($frequency_pattern['day']);
			}
			if (
				array_key_exists('month', $frequency_pattern) &&
				is_array($frequency_pattern['month']) &&
				count($frequency_pattern['month']) > 0
			) {
				asort($frequency_pattern['month']);
			}


			# If frequency period is every_day, set next send
			if (\APP\Tools::checkDatePatternIn($frequency_pattern, 'every_day')) {
				$next_send_date = $now;
				if ($last_send_date) {
					$next_send_date = $now->copy()->addDays($frequency_pattern['frequency']);
				}

			# if every week is specified, look for specified days in week and repeat on frequency
			} else if (\APP\Tools::checkDatePatternIn($frequency_pattern, 'every_week')) {
				$this_week = $now->copy()->startOfWeek();

				foreach ($frequency_pattern['day'] as $key => $day_to_send) {
					$day_to_send = $day_to_send - 1; // correct, 0 will be monday.
					if (
						!$next_send_date &&
						(
							(
								!$last_send_date &&
								$this_week->copy()->addDays($day_to_send)->isSameDay($now)
							) ||
							(
								$last_send_date &&
								!$this_week->copy()->addDays($day_to_send)->isSameDay($last_send_date) &&
								$this_week->copy()->addDays($day_to_send) > $last_send_date
							) ||
							$this_week->copy()->addDays($day_to_send) > $now
						)
					) {
						$next_send_date = $this_week->copy()->addDays($day_to_send);
					}
				}

				// If there is nothing left this week, check next week, if email was sent, add frequency week/s
				if (
					!$next_send_date &&
					!empty($frequency_pattern['day'][0])
				) {
					if ($last_send_date) {
						$next_send_date = $this_week->copy()->addWeeks($frequency_pattern['frequency'])->addDays(($frequency_pattern['day'][0] - 1));
					} else {
						$next_send_date = $this_week->copy()->addWeek()->addDays(($frequency_pattern['day'][0] - 1));
					}
				}

			# If month frequency is give, send out in next date/day or if last date is given, send out after frequency months
			} else if (\APP\Tools::checkDatePatternIn($frequency_pattern, 'every_month')) {

				$this_month = $now->copy();
				$next_month = $now->copy()->addMonth();

				if ($last_send_date) {
					$this_month = $this_month->addMonths($frequency_pattern['frequency']);
					$next_month = $next_month->addMonths($frequency_pattern['frequency']);
				}

				if (
					array_key_exists('day', $frequency_pattern) &&
					is_array($frequency_pattern['day']) &&
					count($frequency_pattern['day']) > 0
				) {
					if (
						$this_month->firstOfMonth($frequency_pattern['day'][0])->isSameDay($now) ||
						$this_month->firstOfMonth($frequency_pattern['day'][0]) > $now
					) {
						$next_send_date = $this_month->firstOfMonth($frequency_pattern['day'][0]);
					}

					// If next send date is not set, check next month
					if (!$next_send_date) {
						if (
							$next_month->firstOfMonth($frequency_pattern['day'][0])->isSameDay($now) ||
							$next_month->firstOfMonth($frequency_pattern['day'][0]) > $now
						) {
							$next_send_date = $next_month->firstOfMonth($frequency_pattern['day'][0]);
						}
					}

				} else if (
					array_key_exists('date', $frequency_pattern) &&
					is_array($frequency_pattern['date']) &&
					count($frequency_pattern['date']) > 0
				) {
					if (
						$this_month->day($frequency_pattern['date'][0])->isSameDay($now) ||
						$this_month->day($frequency_pattern['date'][0]) > $now
					) {
						$next_send_date = $this_month->day($frequency_pattern['date'][0]);
					}

					// If next send date is not set, check next month
					if (!$next_send_date) {
						if (
							$next_month->day($frequency_pattern['date'][0])->isSameDay($now) ||
							$next_month->day($frequency_pattern['date'][0]) > $now
						) {
							$next_send_date = $next_month->day($frequency_pattern['date'][0]);
						}
					}
				}

			# If yearly period is given, send out today and + years.
				// If date/month is given, send out on given month/date
			} else if (\APP\Tools::checkDatePatternIn($frequency_pattern, 'every_year')) {
				$this_year = $now->copy();

				if (
					array_key_exists('month', $frequency_pattern) &&
					is_array($frequency_pattern['month']) &&
					count($frequency_pattern['month']) > 0
				) {
					$this_year->month($frequency_pattern['month'][0]);
				}

				if (
					array_key_exists('date', $frequency_pattern) &&
					is_array($frequency_pattern['date']) &&
					count($frequency_pattern['date']) > 0
				) {
					$this_year->day($frequency_pattern['date'][0]);
				}

				if (
					$this_year->isSameDay($now) ||
					$now < $this_year
				) {
					$next_send_date = $this_year;
				} else {
					$next_send_date = $this_year->addYear();
				}
			}

		}

		return $next_send_date;
	}

	// Loop all arays in array and check if specific key contains specific value.
	public static function subArrayKeyValue ($array, $a_key, $a_value, $return_entry = false) {
		$response = false;
		foreach ($array as $key => $value) {
			if ($value[$a_key] == $a_value) {
				$response = true;
				if ($return_entry) {
					if ($return_entry === true) {
						$response = $value;
					} else {
						$response = $value[$return_entry];
					}
				}
				break;
			}
		}
		return $response;
	}
	/**
	 * Inserts any number of scalars or arrays at the point
	 * in the haystack immediately after the search key ($needle) was found,
	 * or at the end if the needle is not found or not supplied.
	 * Modifies $haystack in place.
	 * @param array &$haystack the associative array to search. This will be modified by the function
	 * @param string $needle the key to search for
	 * @param mixed $stuff one or more arrays or scalars to be inserted into $haystack
	 * @return int the index at which $needle was found
	 */
	public static function array_insert_after(&$haystack, $needle, $stuff){
		if (empty($needle)) {
			$needle = '';
		}
		if (! is_array($haystack) ) return $haystack;

		$new_array = array();
		for ($i = 2; $i < func_num_args(); ++$i){
			$arg = func_get_arg($i);
			if (is_array($arg)) $new_array = array_merge($new_array, $arg);
			else $new_array[] = $arg;
		}

		$i = 0;
		foreach($haystack as $key => $value){
			++$i;
			if ($key == $needle) break;
		}

		$haystack = array_merge(array_slice($haystack, 0, $i, true), $new_array, array_slice($haystack, $i, null, true));

		return $i;
	}

	//add all files and flders, recursively, to a zip archive - not sure why previous version was ignoring files on root folder, maybe my computer...
	public static function zipRecursive($path, $zip) {
		$files = new \RecursiveIteratorIterator(
			new \RecursiveDirectoryIterator($path),
			\RecursiveIteratorIterator::LEAVES_ONLY
		);

		foreach ($files as $name => $file) {
			// Skip directories (they would be added automatically)
			if (!$file->isDir()) {
				// Get real and relative path for current file
				$filePath = $file->getRealPath();
				$relativePath = substr($filePath, strlen($path) + 1);
				// Add current file to archive
				$zip->addFile($filePath, $relativePath);
			}
		}
		return $zip;
	}

	public static function getVideoId($video_url, $video_type) {
		if ($video_type == 'youtube')
		{
			if (preg_match('%(?:youtube(?:-nocookie)?\.com/(?:[\w\-?&!#=,;]+/[\w\-?&!#=/,;]+/|(?:v|e(?:mbed)?)/|[\w\-?&!#=,;]*[?&]v=)|youtu\.be/)([\w-]{11})(?:[^\w-]|\Z)%i', $video_url, $match)) {
				$video_id = $match[1];
			}
		}

		if ($video_type == 'vimeo') {

			$video_id = $video_url;

			if (preg_match('%^https?:\/\/(?:www\.|player\.)?vimeo.com\/(?:channels\/(?:\w+\/)?|groups\/([^\/]*)\/videos\/|album\/(\d+)\/video\/|video\/|)(\d+)(?:$|\/|\?)(?:[?]?.*)$%im', $video_url, $match))
			{
				$video_id = $match[3];
			}


		}

		return $video_id;
	}

	/**
		 * Enable cookie sessions across pages shown in iframes
	*/
	public static function updatePHPSessionCookie() {
		if (session_status() == PHP_SESSION_ACTIVE){

			header('Set-Cookie: PHPSESSID=' . session_id() . ';HttpOnly; SameSite=None;Secure;path=/');
		}
	}

	// Return data from CURL request
	public static function getCurlData($url, $headers = false, $post_data = false, $credas = false, $download_file = false) {
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $url);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		if ($headers) {
			curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
		}
		if ($post_data) {
			curl_setopt($ch, CURLOPT_POST, 1);
			if ($credas) {
				curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($post_data));
			} else {
				curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($post_data));
			}
		}

		if ($download_file) {
			$file_path = fopen($download_file, 'w');
			curl_setopt($ch, CURLOPT_FILE, $file_path);
			curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
		}

		$server_output = curl_exec($ch);

		curl_close($ch);

		if ($download_file) {
			fclose($file_path);
		}
		return $server_output ;
	}


	public static function uploadImage($path, $key, $original_image = null, $size = '2M') {
		$file_name = $original_image;

		// Convert size to bytes
		$size = self::convertToBytes($size);

		if (
			isset($_FILES[$key]) &&
			isset($_FILES[$key]['tmp_name']) &&
			file_exists($_FILES[$key]['tmp_name'])
		) {
			$adapter = new LocalFilesystemAdapter($path);
			$filesystem = new Filesystem($adapter);
			$uploadedFile = $_FILES[$key];

			$file_name = uniqid() . '.' . pathinfo($uploadedFile['name'], PATHINFO_EXTENSION);
			$mimeType = mime_content_type($uploadedFile['tmp_name']);

			if (!in_array($mimeType, ['image/png', 'image/gif', 'image/jpeg', 'image/jpg'])) {
					throw new \Exception("Invalid file type: $mimeType.");
			}

			if ($uploadedFile['size'] > $size) {
					throw new \Exception("File size exceeds the limit of " . ($size / (1024 * 1024)) . " MB.");
			}

			try {
					$stream = fopen($uploadedFile['tmp_name'], 'r+');
					$filesystem->writeStream($file_name, $stream);
					if (is_file($path . $original_image)) {
						$filesystem->delete($original_image);
					}
			} catch (\Exception $e) {
					$file_name = $original_image;
			} finally {
					if (isset($stream)) {
						fclose($stream);
					}
			}
		}

		return $file_name;
	}

	private static function convertToBytes($size) {
		$unit = strtoupper(substr($size, -1));
		$size = (int) $size;

		switch ($unit) {
			case 'G':
					$size *= 1024;
			case 'M':
					$size *= 1024;
			case 'K':
					$size *= 1024;
		}

		return $size;
	}



	public static function cors ($response) {
		// Enable cors only if allowApi is enabled!
		if (\APP\Tools::getConfig('allowApi')) {
			if (isset($_SERVER['HTTP_ORIGIN'])) {
				$response = $response
					->withHeader('Access-Control-Allow-Credentials', 'true')
					->withHeader('Access-Control-Allow-Origin', $_SERVER['HTTP_ORIGIN'])
					->withHeader('Access-Control-Max-Age', '86400')
				;
			}
			if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
				if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_METHOD'])) {
					$response = $response
						->withHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
					;
				}
				if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS'])) {
					$response = $response
						->withHeader('Access-Control-Allow-Headers', $_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS'])
					;
				}
			}
		}
		return $response;
	}

	public static function returnCode($request, $response, $code, $custom_text_response = false, $options = []) {

		if ($custom_text_response) {
			$options['custom_text_response'] = $custom_text_response;
		}

		// Skip logging for 404 errors (handled separately in logs.php)
		if ($code != 404) {
			\Models\Log::addEntry($request, $response, $code, $options);
		}

		// Default content type
		$content_type = 'text/html';
		$code = intval($code);

		switch ($code) {
			case 204:
				$response
					->getBody()
					->write($custom_text_response ? $custom_text_response : $code .' No Content')
				;
				break;

			case 400:
				$response
					->getBody()
					->write($custom_text_response ? $custom_text_response : $code .' Bad Request')
				;
				break;

			case 401:
				$response
					->getBody()
					->write($custom_text_response ? $custom_text_response : $code .' Unauthorized')
				;
				break;

			case 403:
				$response
					->getBody()
					->write($custom_text_response ? $custom_text_response : $code .' Forbidden')
				;
				break;

			case 404:
				$response
					->getBody()
					->write($custom_text_response ? $custom_text_response : $code .' Not Found')
				;
				break;

			case 405:
				$response
					->getBody()
					->write($custom_text_response ? $custom_text_response : $code .' Method Not Allowed')
				;
				break;

			case 406:
				$response
					->getBody()
					->write($custom_text_response ? $custom_text_response : $code .' Not Acceptable')
				;
				break;

			case 408:
				$response
					->getBody()
					->write($custom_text_response ? $custom_text_response : $code .' Request Timeout')
				;
				break;


			case 409:
				$response
					->getBody()
					->write($custom_text_response ? $custom_text_response : $code .' Conflict')
				;
				break;

			case 412:
				$response
					->getBody()
					->write($custom_text_response ? $custom_text_response : $code .' Precondition Failed')
				;
				break;

			case 415:
				$response
					->getBody()
					->write($custom_text_response ? $custom_text_response : $code .' Unsupported Media Type')
				;
				break;

			case 422:
				$message = 'Unprocessable Entity';
				$errorType = null;
				if(!empty($custom_text_response)) {
					if(is_array($custom_text_response) && isset($custom_text_response['message'])) {
						$message = $custom_text_response['message'];
					} else {
						$message = $custom_text_response;
					}

					if(is_array($custom_text_response) && isset($custom_text_response['error_type'])) {
						$errorType = $custom_text_response['error_type'];
					}
				}
				$content_type = 'application/json';
				$response
					->getBody()
					->write(
						json_encode([
							'error' => $message,
							'type' => $errorType
						])
					)
				;
				break;

			case 429:
				$response
					->getBody()
					->write($custom_text_response ? $custom_text_response : $code .' Rate limit exceeded. Try again later.')
				;
				break;

			case 500:
				$response
					->getBody()
					->write($custom_text_response ? $custom_text_response : $code .' Internal Server Error')
				;
				break;

			case 502:
				$response
					->getBody()
					->write($custom_text_response ? $custom_text_response : $code .' Bad Gateway')
				;
				break;

			case 503:
				$response
					->getBody()
					->write($custom_text_response ? $custom_text_response : $code .' Service Unavailable')
				;

				break;

		}
		return
			$response
				->withStatus($code)
				->withHeader('Content-Type', $content_type)
		;
	}


	/*
		Source website: http://www.pgregg.com/projects/php/ip_in_range/
		Version 1.2
	*/
	public static function ipInRange ($ip, $range) {
		if (strpos($range, '/') !== false) {
			// $range is in IP/NETMASK format
			list($range, $netmask) = explode('/', $range, 2);
			if (strpos($netmask, '.') !== false) {
				// $netmask is a *********** format
				$netmask = str_replace('*', '0', $netmask);
				$netmask_dec = ip2long($netmask);
				return ( (ip2long($ip) & $netmask_dec) == (ip2long($range) & $netmask_dec) );
			} else {
				// $netmask is a CIDR size block
				// fix the range argument
				$x = explode('.', $range);
				while(count($x)<4) $x[] = '0';
				list($a,$b,$c,$d) = $x;
				$range = sprintf("%u.%u.%u.%u", empty($a)?'0':$a, empty($b)?'0':$b,empty($c)?'0':$c,empty($d)?'0':$d);
				$range_dec = ip2long($range);
				$ip_dec = ip2long($ip);

				# Strategy 1 - Create the netmask with 'netmask' 1s and then fill it to 32 with 0s
				#$netmask_dec = bindec(str_pad('', $netmask, '1') . str_pad('', 32-$netmask, '0'));

				# Strategy 2 - Use math to create it
				$wildcard_dec = pow(2, (32-$netmask)) - 1;
				$netmask_dec = ~ $wildcard_dec;

				return (($ip_dec & $netmask_dec) == ($range_dec & $netmask_dec));
			}
		} else {
			// range might be 255.255.*.* or *******-*********
			if (strpos($range, '*') !==false) { // a.b.*.* format
				// Just convert to A-B format by setting * to 0 for A and 255 for B
				$lower = str_replace('*', '0', $range);
				$upper = str_replace('*', '255', $range);
				$range = "$lower-$upper";
			}

			if (strpos($range, '-')!==false) { // A-B format
				list($lower, $upper) = explode('-', $range, 2);
				$lower_dec = (float)sprintf("%u",ip2long($lower));
				$upper_dec = (float)sprintf("%u",ip2long($upper));
				$ip_dec = (float)sprintf("%u",ip2long($ip));
				return ( ($ip_dec>=$lower_dec) && ($ip_dec<=$upper_dec) );
			}

			// If ******* is added
			return ip2long($range) === ip2long($ip);
		}
		//echo 'Range argument is not in *******/24 or *******/************* format';
		return false;
	}

	public static function datePeriodQuery ($query, $date_from = false, $date_to = false) {
		$query = $query
			->where(function ($query) use ($date_from, $date_to) {
				if (
					$date_from &&
					$date_to
				) {
					$query
						->where(function ($query) use ($date_from) {
							$query->where('schedules.start_date', '<=', $date_from);
							$query->where('schedules.end_date', '>=', $date_from);
						})
						->orWhere(function ($query) use ($date_from, $date_to) {
							$query->where('schedules.start_date', '>=', $date_from);
							$query->where('schedules.start_date', '<=', $date_to);
						})
					;
				} else if ($date_from) {
					$query
						->where(function ($query) use ($date_from) {
							$query->where('schedules.start_date', '<=', $date_from);
							$query->where('schedules.end_date', '>=', $date_from);
						})
						->orWhere(function ($query) use ($date_from) {
							$query->where('schedules.start_date', '>=', $date_from);
						})
					;
				} else if ($date_to) {
					$query
						->where(function ($query) use ($date_to) {
							$query->where('schedules.end_date', '<=', $date_to);
						})
					;
				}
			})
		;
		return $query;
	}


	public static function datePeriod (&$params, $query, $search_str = false, $field = false) {
		if (!$field) {
			$field = $search_str;
		}
		if (
			$search_str &&
			isset($params["search"][$search_str]) &&
			$params["search"][$search_str]
		) {
			$params["search"][$search_str] = json_decode($params["search"][$search_str], true);
			$date_from = false;
			if (
				isset($params["search"][$search_str]['period_from']) &&
				$params["search"][$search_str]['period_from']
			) {
				$date_from = \Carbon\Carbon::parse($params["search"][$search_str]['period_from'])->startOfDay();
			}
			$date_to = false;
			if (
				isset($params["search"][$search_str]['period_to']) &&
				$params["search"][$search_str]['period_to']
			) {
				$date_to = \Carbon\Carbon::parse($params["search"][$search_str]['period_to'])->endOfDay();
			}

			$query = \APP\Tools::datePeriodQuery($query, $date_from, $date_to);
			unset($params["search"][$search_str]);
		}
		return $query;
	}

	public static function generateReviewReport($id,$settings){
		$batchReport=\Models\BatchReport::where('id',$id)->first();
		if(!$batchReport)
		{
			return false;
		}
		$query = \Models\BatchReportData::where('batch_report_id',$batchReport->id)->first();
		if(!$query)
		{
			return false;
		}
		$user=\Models\User::find($batchReport->user_id);

		$file = $GLOBALS["CONFIG"]->LMSBatchReportData . $query->file_name;
		if (is_file($file)) {
			$file_content = file_get_contents($file);
			$data_json_decoded = json_decode(gzdecode($file_content));
		} else {
			$data_json_decoded = [];
		}

		$slug =$batchReport->slug;
		$params=[];
		$params['table_data']=json_decode($batchReport->table_state,true);
		if (
			$slug === 'customreview' &&
			isset($params['table_data']['search']['review_id']) &&
			$params['table_data']['search']['review_id'] > 0
		) {
			// Custom Reviews have unique way to generate export fields
			$review = \Models\CustomReview::where('id', $params['table_data']['search']['review_id'])
				->first()
			;

			$custom_review_settings = \Models\CustomReview::exportFields($review, $data_json_decoded);
			$download_file_name = $custom_review_settings['download_file_name'];
			$export_fields = $custom_review_settings['export_fields'];
		} else {
			// Get download settings from database table

			if (
				$slug === 'taskassessmentreports' &&
				isset($params['table_data']['search']['additionalSearchParams'])
			) {
				$additionalSearchParams = json_decode($params['table_data']['search']['additionalSearchParams'], TRUE);
				if ($additionalSearchParams['report']) {
					$slug = $slug . $additionalSearchParams['report'];
				}
			}

			$review = \Models\CustomReview::where('slug', $slug)
				->first()
			;

			if (
				$review &&
				$review->download_file_name
			) {
				$download_file_name = $review->download_file_name;
				$download_file_name = uniqid($download_file_name) . ".xlsx";
			}

			$export_fields = json_decode($review->export_fields, TRUE);

		}

		if ($download_file_name) {
			\APP\Tools::generateExcelDownload(
				$data_json_decoded,
				$export_fields,
				$GLOBALS["CONFIG"]->LMSTempPath . $download_file_name,[]
				,
				$user
			);

			// Set data as read!
			$query->unread = false;
			$query->save();

			return $GLOBALS["CONFIG"]->LMSTempPath . $download_file_name;
		} else {
			return false;
			;
		}
	}

	public static function eventVenueToCustomVariables($event, $custom_variable)
	{
		/*Injecting venue details while emails contain event details*/
		if (isset($custom_variable) && isset($event)) {
			$custom_variable = json_decode($custom_variable,true);
			if ($event->venue) {
				$venue = Venue::find($event->venue->value);
				if ($venue) {
					$custom_variable['VENUE']= $venue->name;
					$custom_variable['VENUE_ADDRESS'] = $venue->address;
					$custom_variable['VENUE_POSTCODE'] = $venue->postcode;
					$custom_variable['VENUE_INSTRUCTIONS'] = $venue->instructions;
					$custom_variable['VENUE_IMAGE'] = $venue->image;
				}
			}
			$custom_variable = json_encode($custom_variable);
		}
		return $custom_variable;
	}

	// Will parse UK datetime in date object, if fails, will return false;
	public static function parseUKDateTime($datetime) {
		$response = false;
		// Replace non-breaking spaces with regular spaces
		$datetime = preg_replace('/\xc2\xa0/', ' ', $datetime);
		// Trim any leading or trailing whitespace
		$datetime = trim($datetime);
		// Detect if this is UK format, if so, parse it
		if (preg_match('/^(\d{2})\/(\d{2})\/(\d{2}|\d{4})\s+(\d{2}):(\d{2})(?::(\d{2}))?$/', $datetime, $matches)) {
			$day = $matches[1];
			$month = $matches[2];
			$year = $matches[3];
			$hour = isset($matches[4]) ? $matches[4] : '00';
			$minute = isset($matches[5]) ? $matches[5] : '00';
			$second = isset($matches[6]) ? $matches[6] : '00';

			// Create a DateTime object with the given date and time components
			$response = \Carbon\Carbon::parse("$year-$month-$day $hour:$minute:$second");
		}
		return $response;
	}


	public static function isUKDateFormat($string) {
		// Remove any leading or trailing whitespace, including spaces, tabs, and line breaks
		$string = trim($string);

		// Replace any occurrence of multiple spaces, tabs, or newlines between components with a single space
		$string = preg_replace('/\s+/', ' ', $string);

		// Regular expression for date in the format DD/MM/YYYY followed optionally by a flexible time component
		//$pattern = '/^(0[1-9]|[12][0-9]|3[01])\/(0[1-9]|1[0-2])\/\d{4}(?:\s([01][0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9])?$/';
		$pattern = '/^([1-9]|0[1-9]|[12][0-9]|3[01])\/([1-9]|0[1-9]|1[0-2])\/(\d{2}|\d{4})(?:\s([01]?[0-9]|2[0-3]):[0-5][0-9])?$/'; // More flexible support - "11/1/24 9:00"


		return preg_match($pattern, $string);
	}


	// Will try to parse datetime object/string
	public static function parseDateTime($datetime = false) {
		$response = false;
		if ($datetime) {
			if (is_string($datetime)) {
				$datetime = trim($datetime);
				try {
					$response = \Carbon\Carbon::parse($datetime);
				} catch (\Exception $e) {
					// Usually carbon fails with UK time, check if that is true
					$response = \APP\Tools::parseUKDateTime($datetime);
				}
			} else if (is_object($datetime)) {
				$response = \Carbon\Carbon::instance($datetime);
			}
		}
		return $response;
	}

	public static function fixSpreadsheetImportDateFields($cell) {
		$value = $cell->getValue();
		$formatted_value = $cell->getFormattedValue();
		// If they are the same, it is string, if they are not the same, then parse date into object.
		if (
			$value != $formatted_value
		) {
			$formatted_value = \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($value);
		}
		return $formatted_value;
	}

	public static function civicaPay($type, $type_id, $url_link, $paid_for=null, $user_id=null) {
		// try{
		 $civicaPaymentsEngineRequestURL = \APP\Tools::getConfig('civicaPaymentsEngineRequestURL');
		 $civicaPaymentsEngineAccountReference = \APP\Tools::getConfig('civicaDefaultGeneralLedgerCode');
		 $civicaPaymentsEngineVatCode = \APP\Tools::getConfig('civicaPaymentsEngineVatCode');
		 $civicaPaymentsFundCode = "SL";
		 $userPaymentTransactionObj = new \Models\UserPaymentTransaction;

		 // Do not proceed with payment when no one is present to pay for???
		 if (
			$user_id == null
		 ) {
			$user_id = \APP\Auth::getUserId();
		 }
		 if(!$user_id || $user_id==null){
			return null;
		 }

		 /*Test Transaction check*/
		 if($civicaPaymentsEngineRequestURL){
				$url=parse_url($civicaPaymentsEngineRequestURL);
				$uri= explode('/', $url['path']);
				$uri = $uri[1];
				if($uri == "SuffolkPartnershipXMLTest"){
					$userPaymentTransactionObj->is_test = 1;
				}
			}

		 if (isset($type) && isset($type_id)) {

			 /*Discount Calculation*/
			 $totaldiscount = 0;
			 $totalManagerDiscount=0;
			 $zeroPaymentMessage="Applied ";

			 //if admin ,then we need to fetch the discount of manager and user
			 if (
				 \APP\Auth::isAdminInterface()
			 ) {
				 $totalManagerDiscount = \Models\User::calculateTotalDiscount(\APP\Auth::getUserId());
				 $totalManagerDiscount=($totalManagerDiscount > 100) ? 100 : $totalManagerDiscount;

				 $zeroPaymentMessage .= "manager discount of {$totalManagerDiscount}% and ";
			 }else{
				 $user_id=\APP\Auth::getUserId();
			 }

			 $totalUserDiscount = \Models\User::calculateTotalDiscount($user_id);
			 $totalUserDiscount=($totalUserDiscount > 100) ? 100 : $totalUserDiscount;
			 $zeroPaymentMessage .= "trainee discount {$totalUserDiscount}%";
			 $totaldiscount=$totalManagerDiscount+$totalUserDiscount;

			 $totaldiscount=($totaldiscount > 100) ? 100 : $totaldiscount;


			 if ($type == "schedules") {
				 $scheduleObj = \Models\Schedule::find($type_id);
				 if ($scheduleObj) {
					 $userPaymentTransactionObj->item_cost = $scheduleObj->cost;
					 $userPaymentTransactionObj->user_id = $user_id;
					 $userPaymentTransactionObj->payer_organisation_id = \APP\Auth::getUserCompanyId();
					 $userPaymentTransactionObj->type = $scheduleObj->type;
					 $userPaymentTransactionObj->type_id = $scheduleObj->id;
					 $userPaymentTransactionObj->calling_application_id = "CPD";
					 $userPaymentTransactionObj->type_reference_table = "schedules";
					 $userPaymentTransactionObj->item_discount = $totaldiscount;
					 $userPaymentTransactionObj->item_cost = $scheduleObj->cost;
					 $userPaymentTransactionObj->paid_for = $paid_for;

					 /*Applying Discount*/
					 if ($totaldiscount > 0) {
						 $userPaymentTransactionObj->payment_total = $scheduleObj->cost - round(($scheduleObj->cost * $totaldiscount) / 100,2);
					 } else {
						 $userPaymentTransactionObj->payment_total = $scheduleObj->cost;
					 }

					 if($userPaymentTransactionObj->payment_total<=0){
						 $userPaymentTransactionObj->response_description=$zeroPaymentMessage;
					 }

					 $userPaymentTransactionObj->return_url = $url_link;
					 $userPaymentTransactionObj->network_user_id = "Internet";
					 $userPaymentTransactionObj->payment_1 = $civicaPaymentsEngineAccountReference . "|" . $civicaPaymentsFundCode . "|" . $userPaymentTransactionObj->payment_total . "|" . $civicaPaymentsEngineVatCode . "|" . $scheduleObj->type . "-" . $scheduleObj->name . "-" . $scheduleObj->id;
					 $userPaymentTransactionObj->payment_gateway = "civica";
					 $userPaymentTransactionObj->status = "0";
					 $userPaymentTransactionObj->save();
					 $UpdateObj = \Models\UserPaymentTransaction::find($userPaymentTransactionObj->id);
					 $UpdateObj->system_generated_transaction_id = substr(md5(time()), 0, 10) . $userPaymentTransactionObj->id;
					 /*$redirectURL is the URL that is sent to CIVICA payment gateway */

					 $callBackURL = $GLOBALS["CONFIG"]->LMSUrl . '/payment-gateway/civica/callback/' . $UpdateObj->system_generated_transaction_id;

					 $redirectURL = $civicaPaymentsEngineRequestURL . "?CallingApplicationID=" . $userPaymentTransactionObj->calling_application_id
					 . "&" . "PaymentTotal=" . $userPaymentTransactionObj->payment_total . "&Network_User_ID=Internet&ReturnURL=" .
					 $callBackURL . "&PaymentSourceCode=01&Payment_1=" . $userPaymentTransactionObj->payment_1;
					 $UpdateObj->redirect_url = $redirectURL;
					 $UpdateObj->return_url = $UpdateObj->return_url . "?cpr=" . $UpdateObj->system_generated_transaction_id;
					 $UpdateObj->save();
				 }
			 }
			 if ($type == "learning_modules") {
				 $learningModuleObj = \Models\LearningModule::find($type_id);
				 if ($learningModuleObj) {
					 $userPaymentTransactionObj->item_cost = $learningModuleObj->cost;
					 $userPaymentTransactionObj->user_id = $user_id;
					 $userPaymentTransactionObj->payer_organisation_id = \APP\Auth::getUserCompanyId();
					 $userPaymentTransactionObj->type = "learning module";
					 $userPaymentTransactionObj->type_id = $learningModuleObj->id;
					 $userPaymentTransactionObj->calling_application_id = "CPD";
					 $userPaymentTransactionObj->type_reference_table = "learning_modules";
					 $userPaymentTransactionObj->paid_for = $paid_for;


					 $userPaymentTransactionObj->item_discount = $totaldiscount;
					 $userPaymentTransactionObj->item_cost = $learningModuleObj->cost;
					 if(!empty($learningModuleObj->code)){
						 $civicaPaymentsEngineAccountReference = $learningModuleObj->code;
					 }
					 /*Applying Discount*/
					 if ($totaldiscount > 0) {
						 $userPaymentTransactionObj->payment_total = $learningModuleObj->cost -  round(($learningModuleObj->cost * $totaldiscount) / 100,2);
					 } else {
						 $userPaymentTransactionObj->payment_total = $learningModuleObj->cost;
					 }

					 if($userPaymentTransactionObj->payment_total<=0){
						 $userPaymentTransactionObj->response_description=$zeroPaymentMessage;
					 }

					 $userPaymentTransactionObj->return_url = $url_link;
					 $userPaymentTransactionObj->network_user_id = "Internet";
					 $userPaymentTransactionObj->payment_1 = $civicaPaymentsEngineAccountReference . "|" . $civicaPaymentsFundCode . "|" . $userPaymentTransactionObj->payment_total . "|" . $civicaPaymentsEngineVatCode . "|" . $userPaymentTransactionObj->type . "-" . $learningModuleObj->name . "-" . $learningModuleObj->id;
					 $userPaymentTransactionObj->payment_gateway = "civica";
					 $userPaymentTransactionObj->status = "0";
					 $userPaymentTransactionObj->save();
					 $UpdateObj = \Models\UserPaymentTransaction::find($userPaymentTransactionObj->id);
					 $UpdateObj->system_generated_transaction_id = substr(md5(time()), 0, 10) . $userPaymentTransactionObj->id;
					 $UpdateObj->return_url = $UpdateObj->return_url . "?cpr=" . $UpdateObj->system_generated_transaction_id;
					 /*$redirectURL is the URL that is sent to CIVICA payment gateway */
					 $callBackURL = $GLOBALS["CONFIG"]->LMSUrl . '/payment-gateway/civica/callback/' . $UpdateObj->system_generated_transaction_id;
					 $redirectURL = $civicaPaymentsEngineRequestURL . "?CallingApplicationID=" . $userPaymentTransactionObj->calling_application_id . "&" . "PaymentTotal=" . $userPaymentTransactionObj->payment_total . "&Network_User_ID=Internet&ReturnURL=" . $callBackURL . "&PaymentSourceCode=01&Payment_1=" . $userPaymentTransactionObj->payment_1;
					 $UpdateObj->redirect_url = $redirectURL;
					 $UpdateObj->save();
				 }
			 }
		 }

		 if($userPaymentTransactionObj->payment_total<=0){
			 $response_content=["status"=>0,"redirectUrl"=>'',
			 'system_generated_transaction_id'=>$UpdateObj->system_generated_transaction_id,
			 'message'=>$zeroPaymentMessage
		 ];
		 }else{
			 $response_content=["status"=>1,"redirectUrl"=>$redirectURL,'system_generated_transaction_id'=>$UpdateObj->system_generated_transaction_id];
		 }

		 return $response_content;

	}

	// Parse string "0930", do sanity checks and return hour/minute in object.
	public static function parseTimeString($timeString) {
		// Check if the input string has the correct format
		if (!preg_match('/^(\d{2})(\d{2})$/', $timeString, $matches)) {
			return false;
		}

		$hours = (int)$matches[1];
		$minutes = (int)$matches[2];

		// Check if the hours and minutes are within valid ranges
		if (
			$hours > 23 ||
			$minutes > 59
		) {
			return false;
		}

		// Create and return the result object
		$result = (object)array(
			'hours' => $hours,
			'minutes' => $minutes
		);

		return $result;
	}

	// Will take CalendarStartTime and CalendarEndTime configuration option, get minutes between them.
	public static function dayWorkMinutes() {
		$CalendarStartTime = \APP\Tools::parseTimeString(\APP\Tools::getConfig('CalendarStartTime')) ?: \APP\Tools::parseTimeString(\APP\Tools::getConfig('CalendarStartTime', true));
		if ($CalendarStartTime) {
			$day_start = \Carbon\Carbon::now()->hour($CalendarStartTime->hours)->minute($CalendarStartTime->minutes);
		}

		$CalendarEndTime = \APP\Tools::parseTimeString(\APP\Tools::getConfig('CalendarEndTime')) ?: \APP\Tools::parseTimeString(\APP\Tools::getConfig('CalendarEndTime', true));
		if ($CalendarEndTime) {
			$day_end = \Carbon\Carbon::now()->hour($CalendarEndTime->hours)->minute($CalendarEndTime->minutes);
		}

		if (
			!empty($day_start) &&
			!empty($day_end)
		) {
			$duration_minutes = $day_start->diffInMinutes($day_end);
			return $duration_minutes;
		} else {
			return false;
		}
	}

	public static function returnFixedDayStartAndEndTime($start_date = false, $end_date = false) {
		if (!$start_date) {
			$start_date = \Carbon\Carbon::now();
		}
		if (!$end_date) {
			$end_date = \Carbon\Carbon::now();
		}

		$CalendarStartTime = \APP\Tools::parseTimeString(\APP\Tools::getConfig('CalendarStartTime')) ?: \APP\Tools::parseTimeString(\APP\Tools::getConfig('CalendarStartTime', true));
		if ($CalendarStartTime) {
			$start_date
				->hour($CalendarStartTime->hours)
				->minute($CalendarStartTime->minutes)
				->second(0)
			;
		}

		$CalendarEndTime = \APP\Tools::parseTimeString(\APP\Tools::getConfig('CalendarEndTime')) ?: \APP\Tools::parseTimeString(\APP\Tools::getConfig('CalendarEndTime', true));
		if ($CalendarEndTime) {
			$end_date
				->hour($CalendarEndTime->hours)
				->minute($CalendarEndTime->minutes)
				->second(0)
			;
		}
		$return_obj = new \stdClass;
		$return_obj->start_date = $start_date;
		$return_obj->end_date = $end_date;
		$return_obj->duration = $end_date->diffInMinutes($start_date);

		return $return_obj;

	}

	public static function minMaxIdForThrottlingData ($limit = 100, $offset = 0, $collection = false) {

		$response = new \stdClass;
		$response->minId = 0;
		$response->maxId = 100;

		if ($collection) {
			$minId = clone $collection;
			$minId = $minId->orderBy('id', 'asc')->offset($offset)->limit(1)->select('id')->first();
			if ($minId) {
				$response->minId = $minId->id;
			}
			$maxId = clone $collection;
			$maxId = $maxId->orderBy('id', 'asc')->offset($offset + $limit - 1)->limit(1)->select('id')->first();
			if ($maxId) {
				$response->maxId = $maxId->id;
			} else {
				$maxId = clone $collection;
				$maxId = $maxId->orderBy('id', 'desc')->first(); // This can fail as well.
				if ($maxId) {
					$response->maxId = $maxId->id;
				}
			}
		}
		return $response;
	}

	// Parses string "HH:mm-HH:mm" and returns todays date with these times
	public static function parseTimeRange ($range = false, $date_obj = false) {
		$response = false;
		if ($range) {
			$pattern = '/^([01]\d|2[0-3]):([0-5]\d)-([01]\d|2[0-3]):([0-5]\d)$/';
			if (preg_match($pattern, $range, $matches)) {

				$startHour = intval($matches[1]);
				$startMinute = intval($matches[2]);
				$endHour = intval($matches[3]);
				$endMinute = intval($matches[4]);

				$start_time = $date_obj ? $date_obj->copy() : \Carbon\Carbon::now();
				$start_time->hour = $startHour;
				$start_time->minute = $startMinute;
				$start_time->second = 0;

				$end_time =  $date_obj ? $date_obj->copy() : \Carbon\Carbon::now();
				$end_time->hour = $endHour;
				$end_time->minute = $endMinute;
				$end_time->second = 0;

				if ($start_time < $end_time) {
					$response = new \stdClass;
					$response->start_time = $start_time;
					$response->end_time = $end_time;
				}
			}
		}
		return $response;
	}

	public static function anonymizeString($string) {
		$length = strlen($string);

		if ($length < 4) {
			$visibleChars = $length > 1 ? 1 : 0;
			$anonymizedString = str_repeat('*', $length - $visibleChars) . substr($string, -$visibleChars);
		} else {
			$visibleChars = 2;
			$anonymizedString = substr($string, 0, $visibleChars) . str_repeat('*', $length - ($visibleChars * 2)) . substr($string, -$visibleChars);
		}

		return $anonymizedString;
	}

	/**
	 * Request Maximo Service
	 * @param $payload
	 */
	public static function requestMaximo($payload)
	{
		$maximo = new Maximo($payload);
		$maximo->requestMaximo();
	}

	/**
	 * Replace vars from the given string
	 *
	 * @param $html
	 * @param $vars
	 * @return mixed|string|string[]
	 */
	public static function replaceVars($html, $vars)
	{
		foreach ($vars as $key => $value) {
			if (is_array($value)) {
					$html = self::replaceVars($html, $value);
			} else {
					$html = str_replace('<?=$' . $key . '?>', $value ?? '', $html);
			}
		}
		return $html;
	}


	public static function requestUrl() {
		$url = false;
		if (
			!empty($_SERVER['SERVER_PORT']) &&
			!empty($_SERVER['HTTP_HOST']) &&
			!empty($_SERVER['REQUEST_URI'])
		) {
			$protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' || $_SERVER['SERVER_PORT'] == 443) ? "https://" : "http://";
			$host = $_SERVER['HTTP_HOST'];
			$uri = $_SERVER['REQUEST_URI'];
			$url = $protocol . $host . $uri;
		}

		return $url;
	}

	public static function defaultDateFormatMYSQL() {
		$phpFormat = \APP\Tools::getConfig('defaultDateFormat');

		$userDateFormat = \Models\TableExtension::getValue('users', \APP\Auth::getUserId(), 'default_date_format');
		if ($userDateFormat) {
			$phpFormat = $userDateFormat;
			$mysqlFormat = Tools::convertPHPToMySQLDateFormat($phpFormat);
			return $mysqlFormat;
		}

		// Mapping PHP date format characters to MySQL DATE_FORMAT characters
		$conversionMap = [
			'd' => '%d', // Day of the month, 2 digits with leading zeros
			'm' => '%m', // Numeric representation of a month, with leading zeros
			'Y' => '%Y', // A full numeric representation of a year, 4 digits
			// Add more mappings as needed
		];

		$mysqlFormat = "";

		// Iterate through each character in the PHP format string
		for ($i = 0; $i < strlen($phpFormat); $i++) {
			$char = $phpFormat[$i];

			// Check if the current character should be mapped
			if (array_key_exists($char, $conversionMap)) {
				// Append the mapped character to the MySQL format string
				$mysqlFormat .= $conversionMap[$char];
			} else {
				// Append the character as is (for characters like '/', '-', '.', etc.)
				$mysqlFormat .= $char;
			}
		}

		return $mysqlFormat;
	}

	public static function convertPHPToMySQLDateFormat($phpFormat)
	{
		// Mapping PHP date format characters to MySQL DATE_FORMAT characters
		$conversionMap = [
			'yyyy' => '%Y',
			'yy' => '%y',
			'MM' => '%m',
			'dd' => '%d',
			'd' => '%d', // Day of the month, 2 digits with leading zeros
			'm' => '%m', // Numeric representation of a month, with leading zeros
			'Y' => '%Y', // A full numeric representation of a year, 4 digits
			'y' => '%y', // A two digit representation of a year
			'H' => '%H', // 24-hour format of an hour with leading zeros
			'i' => '%i', // Minutes with leading zeros
			's' => '%s', // Seconds, with leading zeros
			// '/' => '-', // Date separator
		];

		$mysqlFormat = strtr($phpFormat, $conversionMap);

		return $mysqlFormat;
	}

	public static function convertDateFormatToHtmlPattern($phpFormat) {
		// Mapping PHP date format characters to HTML input pattern
		$conversionMap = [
			'd' => '\\d{2}', // Day of the month, 2 digits
			'm' => '\\d{2}', // Numeric representation of a month, 2 digits
			'Y' => '\\d{4}', // A full numeric representation of a year, 4 digits
		];

		$pattern = "";

		// Iterate through each character in the PHP format string
		for ($i = 0; $i < strlen($phpFormat); $i++) {
			if (isset($conversionMap[$phpFormat[$i]])) {
				// Replace the PHP format character with the corresponding HTML pattern
				$pattern .= $conversionMap[$phpFormat[$i]];
			} else {
				// Directly append other characters (separators) to the pattern
				$pattern .= $phpFormat[$i];
			}
		}

		return $pattern;
	}
	public static function phpToMomentFormat($phpFormat) {
		$replacements = array(
			// Day
			'd' => 'DD',
			'D' => 'ddd',
			'j' => 'D',
			'l' => 'dddd',
			'N' => '',
			'S' => '',
			'w' => '',
			'z' => '',
			// Week
			'W' => '',
			// Month
			'F' => 'MMMM',
			'm' => 'MM',
			'M' => 'MMM',
			'n' => 'M',
			't' => '',
			// Year
			'L' => '',
			'o' => '',
			'Y' => 'YYYY',
			'y' => 'YY',
			// Time
			'a' => 'a',
			'A' => 'A',
			'B' => '',
			'g' => 'h',
			'G' => 'H',
			'h' => 'hh',
			'H' => 'HH',
			'i' => 'mm',
			's' => 'ss',
			'u' => '',
			// Timezone
			'e' => '',
			'I' => '',
			'O' => '',
			'P' => '',
			'T' => '',
			'Z' => '',
			// Full Date/Time
			'c' => '',
			'r' => '',
			'U' => ''
		);

		return strtr($phpFormat, $replacements);
	}

	public static function textToHTML($text) {
		// Convert special characters to HTML entities
		$safe_text = htmlspecialchars($text);

		// Convert all line breaks to <br /> tags
		$text_with_br = nl2br($safe_text);

		// Remove newline characters that still appear
		$text_clean = str_replace("\n", '', $text_with_br);

		// Replace tabs with four non-breaking spaces (customize the number as necessary)
		$final_text = str_replace("\t", '&nbsp;&nbsp;&nbsp;&nbsp;', $text_clean);

		return $final_text;
	}

	public  static function callOpenAI($url, $model, $temperature, $systemMessage, $userMessage, $authToken) {
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $url);
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($ch, CURLOPT_HTTPHEADER, [
			"Authorization: $authToken",
			"Accept: application/json",
			"Content-Type: application/json"
		]);
		curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
			"model" => $model,
			"temperature" => $temperature,
			"messages" => [
				[
					"role" => "system",
					"content" => $systemMessage
				],
				[
					"role" => "user",
					"content" => $userMessage
				]
			]
		]));
		$response = curl_exec($ch);
		curl_close($ch);
		return $response;
	}

		// Escape plain text for DB fields.
	public static function safeDBfield ($text) {
		if ($text) {
			// Remove HTML tags
			$text = strip_tags($text);

			// OR, for more robust escaping (recommended):
			// $text = htmlspecialchars($text, ENT_QUOTES, 'UTF-8');

			// Optional: Trim whitespace
			$text = trim($text);
		}

		return $text;
	}

	public static function passwordStrengthCheck(string $password, Request $request, Response $response) {
		$password_check = new \ZxcvbnPhp\Zxcvbn();
		$strength = $password_check->passwordStrength($password);
		$default_strength = intval(\APP\Tools::getConfig('PasswordStrength'));
		if (
			isset($strength['score']) &&
			$strength['score'] < $default_strength
		) {
			$error_message = "This is similar to a commonly used password. Add another word or two. Uncommon words are better.";
			if (isset($strength['feedback']['warning'])) {
				$error_message = $strength['feedback']['warning'];
				if (isset($strength['feedback']['suggestions'])) {
					foreach ($strength['feedback']['suggestions'] as $suggestion) {
						$error_message = $error_message . "  " . $suggestion;
					}
				}
			}
			return \APP\Tools::returnCode($request, $response, 406, $error_message);
		}
	}

	public static function stripEmailHtmlContent($html) {
		// Remove base64 images or any <img> tags
		$html = preg_replace('/<img[^>]+>/i', '', $html);

		// Optionally remove script/style tags
		$html = preg_replace('#<script(.*?)>(.*?)</script>#is', '', $html);
		$html = preg_replace('#<style(.*?)>(.*?)</style>#is', '', $html);

		// Strip all remaining HTML tags
		$text = strip_tags($html);

		// Decode HTML entities
		$text = html_entity_decode($text, ENT_QUOTES | ENT_HTML5, 'UTF-8');

		// Normalize whitespace
		$text = preg_replace('/\s+/', ' ', $text);
		$text = trim($text);

		return $text;
	}

	public static function isValidDate($date) {
		if (empty($date)) return false;

		try {
			$carbon = \Carbon\Carbon::parse($date);
			return $carbon->year > 1970;
		} catch (\Exception $e) {
			return false;
		}
	}
	
	public static function getDirectorySize($path) {
		$size = 0;
		if (is_dir($path)) {
			$iterator = new \RecursiveIteratorIterator(
				new \RecursiveDirectoryIterator($path, \RecursiveDirectoryIterator::SKIP_DOTS),
				\RecursiveIteratorIterator::SELF_FIRST
			);
			
			foreach ($iterator as $file) {
				if ($file->isFile()) {
					$size += $file->getSize();
				}
			}
		}
		return $size;
	}
	
	public static function getFileCount($path) {
		$count = 0;
		if (is_dir($path)) {
			$iterator = new \RecursiveIteratorIterator(
				new \RecursiveDirectoryIterator($path, \RecursiveDirectoryIterator::SKIP_DOTS),
				\RecursiveIteratorIterator::SELF_FIRST
			);
			
			foreach ($iterator as $file) {
				if ($file->isFile()) {
					$count++;
				}
			}
		}
		return $count;
	}
	
	public static function formatBytes($bytes, $precision = 2) {
		$units = array('B', 'KB', 'MB', 'GB', 'TB');
		
		$bytes = max($bytes, 0);
		$pow = floor(($bytes ? log($bytes) : 0) / log(1024));
		$pow = min($pow, count($units) - 1);
		
		$bytes /= pow(1024, $pow);
		
		return round($bytes, $precision) . ' ' . $units[$pow];
	}

}
