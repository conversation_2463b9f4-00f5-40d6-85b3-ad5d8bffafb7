<?php

namespace App;

use APP\Tools;
use Models\Company;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\OAuthTokenProvider;

class GetMSAuth implements OAuthTokenProvider
{
  private $oauthUserEmail = '';
  private $oauthRefreshToken = '';
  private $oauthClientId = '';
  private $oauthClientSecret = '';
  private $oauthTokenUrl = ''; // Replace {tenant_id} with your actual tenant ID
  private  $companyDetail;

  /**
   * @param int $company_id
   */
  public function __construct(
    $company_id
  ) {
    $company = Company::where('id', $company_id)->first();
    $this->companyDetail = $company;
    $this->oauthClientId = Tools::getConfig('MicrosoftAppClientId');
    $this->oauthClientSecret = Tools::getConfig('MicrosoftAppClientSecret');
    $this->oauthRefreshToken = $company->refresh_token;
    $this->oauthUserEmail = $company->email_from;
    if ($company->email_username) {
      $this->oauthUserEmail = $company->email_username;
    }
    $tenetID = Tools::getConfig('MicrosoftTenantID') ?? 'common';
    $this->oauthTokenUrl = "https://login.microsoftonline.com/$tenetID/oauth2/v2.0/token";
  }

  public function getToken()
  {
    $lms_redirect_url = $GLOBALS['settings']['LMSUrl'] . "company/verify";
    $isTokenValid = $this->isAccessTokenValid($this->companyDetail->access_token);
    if ($isTokenValid) {
      return $this->companyDetail->access_token;
    }
    $postData = [
      'grant_type' => 'refresh_token',
      'refresh_token' => $this->oauthRefreshToken,
      'client_id' => $this->oauthClientId,
      'client_secret' => $this->oauthClientSecret,
      'scope' => 'offline_access SMTP.Send'
    ];
    $ch = curl_init($this->oauthTokenUrl);
    curl_setopt($ch, CURLOPT_POST, 1);

    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $tokenResponse = curl_exec($ch);
    if (curl_errno($ch)) {
      echo 'Curl error: ' . curl_error($ch);
      exit();
    }
    curl_close($ch);
    $tokenData = json_decode($tokenResponse, true);
    if (isset($tokenData['access_token'])) {
      if ($this->companyDetail) {
        $this->companyDetail->access_token = $tokenData['access_token'];
        $this->companyDetail->refresh_token = $tokenData['refresh_token'];
        $this->companyDetail->save();
      }
      return $tokenData['access_token'];
    } else {
      return false;
    }
  }
  private function isAccessTokenValid($accessToken)
  {
    if (!$accessToken) {
      return false; // No token available
    }

    // Decode the token
    $tokenParts = explode('.', $accessToken);
    if (count($tokenParts) !== 3) {
      return false; // Invalid token format
    }

    $payload = json_decode(base64_decode($tokenParts[1]), true);

    if (!isset($payload['exp'])) {
      return false; // No expiration claim in token
    }

    // Check if the token is expired
    $currentTimestamp = time();
    return $payload['exp'] > $currentTimestamp;
  }

  public function getOauth64()
  {
    $token  = $this->getToken();
    if ($token && $this->oauthUserEmail) {
      return base64_encode("user=" . $this->oauthUserEmail . "\001auth=Bearer " . $token . "\001\001");
    } else {
      return null;
    }
  }
}
