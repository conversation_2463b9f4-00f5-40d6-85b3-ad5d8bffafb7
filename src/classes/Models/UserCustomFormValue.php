<?php
namespace Models;

use APP\Form;
use Illuminate\Database\Capsule\Manager as DB;

class UserCustomFormValue extends \Illuminate\Database\Eloquent\Model
{

	use \Illuminate\Database\Eloquent\SoftDeletes;


    protected $table="user_custom_form_values";
    protected $fillable=['form_id','values'];
    protected $casts=[
        "values"=>'array'
    ];


    public  function Forms(){
        return $this
            ->belongsTo('Models\Form', 'form_id', 'id' )
            ;
    }


    public  function UserForm(){
        return $this
            ->hasOne('Models\UserForm', 'user_custom_form_value_id', 'id')
            ;
    }

    protected static function boot() {
		parent::boot();
		static::saved(function ($user_custom_form_value) {
            try {
                // ------ Create record in form_logs ------ //
                $record = \Models\UserCustomFormValue::with(['UserForm.User'])->where('id', $user_custom_form_value->id)->first();
                // isset($record->UserForm) ? die(json_encode($record->UserForm)) : die('no data');
                $learner_name = isset($record->UserForm->User->fname) && isset($record->UserForm->User->lname) ? $record->UserForm->User->fname. ' ' .$record->UserForm->User->lname : '';

                if($record->UserForm && $record->UserForm->User){
                    $data_arr = [
                        'user_id' => $record->UserForm->User->id,
                        'username' => $record->UserForm->User->username,
                        'learner_name' => $learner_name,
                        'form_id' => $record->UserForm->form_id,
                        'user_form_id' => $record->UserForm->id,
                        'form_field' => json_encode($record->values) ?? '',
                        'edited_by_user' => \APP\Auth::getUserId() ?? 0,
                        'date_time' => date("Y-m-d h:i:s", time()),
                        'notes' => 'Updated form field'
                    ];
                    \Models\FormLog::addEntry($data_arr);
                }
                // ---------------------------------------- //

                // ------ Create record in user_form_values ------ //
                if(is_array($record->values) && count($record->values)){
                    foreach ($record->values as $key => $value) {
                        $form_field = \Models\FormField::with('Field.FieldCategory')
                        ->where(function($query) use($key){
                            $query->where('ilr_slug', $key)
                                ->orWhereHas('Field', function( $fieldQuery) use($key){
                                        $fieldQuery->where('slug', $key);
                                });
                        })
                        ->where('form_id', $record->UserForm->form_id ?? 0)
                        ->first();

                        if($form_field){

                            if(isset($form_field->Field->FieldCategory->slug)){
                                $type = $form_field->Field->FieldCategory->slug;

                                if($form_field->Field->FieldCategory->slug == 'event' && array_key_exists('assigned_event_fields', $record->values)){
                                    $type_id = $record->values['assigned_event_fields'];
                                }else if($form_field->Field->FieldCategory->slug == 'programme' && array_key_exists('assigned_programme_fields', $record->values)){
                                    $type_id = $record->values['assigned_programme_fields'];
                                }else{
                                    $type_id = NULL;
                                }
                            }else{
                                $type = NULL;
                            }

                            if($form_field->ilr_slug != NULL){
                                $is_ilr = '1';
                            }else{
                                $is_ilr = '0';
                            }

                            if($is_ilr === '1'){
                                $type = 'user';
                            }

                            $checkArr = [
                                'form_field_id' => $form_field->id,
                                'user_form_id' => $record->UserForm->id ?? 0
                            ];

                            $type_id = isset($type_id) ? $type_id : null;

                            $user_form_values_arr = [
                                'form_field_id' => $form_field->id,
                                'user_form_id' => $record->UserForm->id ?? 0,
                                'is_ilr' => $is_ilr,
                                'slug' => $key,
                                'value' => is_array($value) ? json_encode($value) : $value,
                                'type' => $type,
                                'type_id' => $type_id
                            ];

                            // the id from form_fields is actually stored as type_id instead of form and field id separately while binding variable
                            $dependents = CustomFieldQueryBinding::where('type', 'form')->where('type_id', $form_field->id)->get();
                            if ($dependents->count() > 0) {
                                foreach ($dependents as $dep) {
                                    self::updateDependant($dep->custom_field_query_id, $record->UserForm->User->id, $record->UserForm->id, $form_field->id, $form_field->ilr_slug != NULL);
                                }
                            }
                            UserFormValue::updateOrCreate($checkArr, $user_form_values_arr);
                        }
                    }
                }

                // ----------------------------------------------- //
            } catch (\Exception $e) {
                print_r($e->getMessage());
                die();
            }
        });
    }

    public static function updateDependant($query_id, $user_id, $user_form_id, $form_field_id, $is_ilr ,$loop = false, $current_data = [])
    {
        $modelCustomFieldQuery = CustomFieldQuery::where('id', $query_id)->first();

        if ($modelCustomFieldQuery && $modelCustomFieldQuery->type == 'query') {
            $modelCustomFieldQueryValue = $modelCustomFieldQuery->value;
            if (Form::isSelect($modelCustomFieldQueryValue)) {
                //Assigned keys like Deafult keys to handling
                $assigned_key_words = ["%%assigned_user_id%%", "%%previous_value%%"];
                if (in_array("%%assigned_user_id%%", $assigned_key_words)) {
                    $modelCustomFieldQueryValue = str_replace("%%assigned_user_id%%", $user_id, $modelCustomFieldQueryValue);
                }

                if (in_array("%%previous_value%%", $assigned_key_words)) {
                    $value = Form::fetchPreviosFilledFormValue($modelCustomFieldQuery, $user_form_id);
                    $modelCustomFieldQueryValue = str_replace("%%previous_value%%", $value, $modelCustomFieldQueryValue);
                }
                $user = User::where('id', $user_id)->first();
                $modelCustomFieldQueryValue = str_replace("%%company_id%%", $user->company_id, $modelCustomFieldQueryValue);
                $modelCustomFieldQueryValue = str_replace("%%department_id%%", $user->department_id, $modelCustomFieldQueryValue);
                $formField = FormField::with('Field')->where('id', $form_field_id)->first();
                $modelCustomFieldQueryValue = str_replace("%%slug%%", $formField->Field->slug, $modelCustomFieldQueryValue);
                //Here fetch existing form value
                $formValues = Form::fetchFormFieldCurrentValue($user_id, $modelCustomFieldQuery->id, $modelCustomFieldQuery, $user_form_id, $loop, $current_data);

                if ($formValues) {
                    foreach ($formValues as $key => $formValue) {
                      $modelCustomFieldQueryValue = preg_replace('/[\'"]*%%' . $key . '%%[\'"]*/i', '"' . addslashes($formValue) . '"', $modelCustomFieldQueryValue);
                    }
                }

                if (!empty($modelCustomFieldQueryValue)) {
                    $executeQuery = DB::select($modelCustomFieldQueryValue);
                    $value = count($executeQuery) > 0 ? reset($executeQuery[0]) : '';
                }
            }
        } elseif ($modelCustomFieldQuery && $modelCustomFieldQuery->type == 'form_field') {
            $fetchArray = Form::fetchFormFieldCurrentValue($user_id, $modelCustomFieldQuery->id, $modelCustomFieldQuery, $user_form_id, $loop);
            $value = !empty($fetchArray) ? $fetchArray['default_value'] : '';
        } elseif ($modelCustomFieldQuery && $modelCustomFieldQuery->type == 'javascript') {
            $fetchArray = Form::fetchFormFieldCurrentValue($user_id, $modelCustomFieldQuery->id, $modelCustomFieldQuery, $user_form_id, $loop);
            foreach ($fetchArray as $key => $value) {
                $value = str_replace("%%" . $key . "%%", $value, $modelCustomFieldQuery->value);
            }
        } elseif ($modelCustomFieldQuery && $modelCustomFieldQuery->type == 'previous_filled_value') {
            $value = Form::fetchPreviosFilledFormValue($modelCustomFieldQuery, $user_form_id);
        } else {
            $value = $modelCustomFieldQuery->value;
        }


        $formField = FormField::where('id', $modelCustomFieldQuery->form_field_id)->first();
        $userForm = UserForm::where("form_id", $formField->form_id)->where('user_id',$user_id)->first();
        if ($userForm == null){
            $userForm = UserForm::where('id',$user_form_id)->first();
        }
        $checkArr = [
            'form_field_id' => $formField->id,
            'user_form_id' => $userForm->id
        ];

        $user_form_values_arr = [
            'form_field_id' => $formField->id,
            'user_form_id' => $userForm->id,
            'is_ilr' => $is_ilr,
            'slug' => $formField->Field->slug,
            'value' => $value,
            'type' => 'form',
            'type_id' => $formField->id
        ];
        UserFormValue::updateOrCreate($checkArr, $user_form_values_arr);
    }

}
