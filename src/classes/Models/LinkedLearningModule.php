<?php
namespace Models;

use APP\Auth;
use APP\Learning;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

class LinkedLearningModule extends Model{
    protected $table = 'linked_learning_modules';
    protected $fillable = ['learning_module_id','linked_learning_module_id','assign','days'];
    public function Module(){
        return $this->belongsTo('Models\LearningModule');
    }
    public function LinkedModule(){
        return $this->belongsTo('Models\LearningModule','linked_learning_module_id');
    }
    public function LearningResults(){
        return $this->hasMany('Models\LearningResult','learning_module_id','learning_module_id')
            ->where('refreshed',0);
    }
    public function LearningResult(){
        return $this->hasOne('Models\LearningResult','learning_module_id','learning_module_id')
            ->where('refreshed',0)->where('user_id',Auth::getUserId());
    }
    public static function getAvailableLearning(){
        return self::whereHas('LearningResult')->pluck('linked_learning_module_id');
  }

  public static function assignLinkedModule()
  {

    // Fetch completed learning results that have not been refreshed
    LearningResult::where('completion_status', 'completed')
    ->where('refreshed', 0)
      ->join('linked_learning_modules', function ($join) {
        $join->on('linked_learning_modules.learning_module_id', '=', 'learning_results.learning_module_id')
        ->where('linked_learning_modules.assign', 'post_completion_after');
      })
      ->chunk(100, function ($results) {
        foreach ($results as $result) {
          // Calculate the post-completion assignment date
          $completedAfter = Carbon::parse($result->completed_at)->addDays($result->days);
          
          // If days are set and the current date is before the calculated completion date
          if ($result->days && $completedAfter->isPast()) {
            // Check if the user already has the linked module assigned and not refreshed
            $existingAssignment = LearningResult::where([
              ['learning_module_id', '=', $result->linked_learning_module_id],
              ['user_id', '=', $result->user_id],
              ['refreshed', '=', 0]
            ])->first();

            if (!$existingAssignment) {
              // Fetch all related module IDs
              $moduleIds = Learning::getAllModuleIds([$result->linked_learning_module_id]);

              // Assign learning resources to the user
              \Models\UserLearningModule::linkResources($result->user_id, $moduleIds, 'direct assign');

              // Assign lesson forms to the user
              \App\Controllers\FormController::assignLessonFormToUser($moduleIds, $result->user_id);
            }
          }
        }
      });
  }

}
