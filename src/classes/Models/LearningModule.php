<?php

namespace Models;

use APP\Auth;
use APP\Tools;
use Carbon\Carbon;
use Slim\App;
use Illuminate\Database\Capsule\Manager as DB;

class LearningModule extends \Illuminate\Database\Eloquent\Model {

	use \Staudenmeir\EloquentHasManyDeep\HasRelationships;
	use \Illuminate\Database\Eloquent\SoftDeletes;

	protected $casts = [
		'approval' => 'boolean',
		'self_enroll' => 'boolean',
		'order_modules' => 'boolean',
		'do_prerequisite' => 'boolean',
		'category_id' => 'integer',
		'company_id' => 'integer',
		'provider_id' => 'integer',
		'duration_change' => 'boolean',
		'refresh' => 'boolean',
		'refresh_custom_email' => 'boolean',
		'guideline' => 'boolean',
		'project' => 'boolean',
		'jackdaw' => 'boolean',
		'jackdaw_resource' => 'boolean',
		'is_skillscan' => 'boolean',
		'is_survey' => 'boolean',
		'track_progress' => 'boolean',
		'print_certificate' => 'boolean',
		'print_lesson' => 'boolean',
		'status' => 'boolean',
		'created_in_learner_interface' => 'boolean',
		'visible_learner' => 'boolean',
		'require_management_signoff' => 'boolean',
		'scorm_popup' => 'boolean',
		'scorm_full_screen' => 'boolean',
		'disable_upon_completion' => 'boolean',
		'open_in_events_only' => 'boolean',
		'complete_if_linked_events_completed' => 'boolean',
		'refresh_all_attached_learning_resources' => 'boolean',
		'copy_refresher_emails_to_line_managers' => 'boolean',
		'reset_learning' => 'boolean',
		'after_completion_do_not_reset_completion_state' => 'boolean',
		'refresh_only_if_learning_meets_query' => 'boolean',
		'further_customise_this_query' => 'boolean',
		'delivery_provider_type_id' => 'integer',
		'group_department_code_id' => 'integer',
		'is_maximo_qualification' => 'boolean',
		'remove_paid_status'=> 'boolean',
        	'access_duration' => 'integer',
		'reset_failed_quiz' => 'boolean',
        	'mandatory_certificate_upload'=> 'boolean',
        	'add_to_ai_chat'=> 'boolean',
        	'library_item_id'=> 'integer',
	];

	protected $fillable = [
		'name',
		'type_id',
		'material',
		'category_id',
		'evidence_type_id',
		'keywords',
		'cost',
        'retake_fee',
		'duration_hours',
		'duration_minutes',
		'status',
		'library_item_id',
	];

	// protected $appends = ['safe_thumbnail'];

	protected $table = 'learning_modules';

	public function Coupons() {
		return $this->belongsToMany('Models\Coupon', 'coupon_links', 'type_id', 'coupon_id')->wherePivot('type', 'learning_module')->withTimestamps();
	}

	public function Company()
	{
		return $this->belongsTo('Models\Company', 'company_id');
	}


	public function Category()
	{
		return $this->belongsTo('Models\LearningModuleCategory', 'category_id');
	}

	public function FPCategory()
	{
		return $this->belongsTo('Models\SmcrFPCategory', 'f_p_category_id');
	}

	public function Provider()
	{
		return $this->belongsTo('Models\LearningProvider', 'provider_id');
	}

	public function Type()
	{
		return $this->belongsTo('Models\LearningModuleType', 'type_id');
	}

	public function Competencies()
	{
		return $this->belongsToMany('Models\Competency', 'learning_module_competencies', 'learning_module_id', 'competency_id')
		->withPivot('points');
	}

	public function Prerequisites()
	{
		return $this->belongsToMany('Models\LearningModule', 'learning_module_prerequisites', 'learning_module_id', 'prerequisite_id');
	}

	public function LinkedSkills()
	{
		return $this->belongsToMany('Models\LearningModule', 'linked_skills','learning_module_id','link_skill_id');
	}

	public function Modules()
	{
		return $this
			->belongsToMany('Models\LearningModule', 'learning_course_modules', 'learning_course_id', 'learning_module_id')
			->withPivot('id') // Had those here: , 'schedule_state', 'schedule_id' leftovers?
			;
	}

	public function formworkflow()
	{
		return $this
			->belongsToMany('Models\FormWorkflow', 'learning_module_workflows', 'learning_module_id', 'form_workflow_id')
			->withPivot('id') // Had those here: , 'schedule_state', 'schedule_id' leftovers?
			->withTimestamps()
			;
	}

	public function userForms(){
		return $this->hasMany("Models\UserForm","type_id","id")->where("type",'lesson');
	}

	public function userProgrammeForms(){
		return $this->hasMany("Models\UserForm","reference_type_id","id")->where("type",'programme');
	}

	// Naming consistancy in some places.
	public function Resources()
	{
		return $this
			->Modules();
	}

	public function Courses()
	{
		return $this->belongsToMany('Models\LearningModule', 'learning_course_modules', 'learning_module_id', 'learning_course_id');
	}

	public function Course()
	{
		return $this->belongsTo('Models\LearningCourseModule', 'id', 'learning_module_id');
	}

	public function Users() {
		return $this
			->belongsToMany('Models\User', 'user_learning_modules', 'learning_module_id', 'user_id')
			->whereNull('user_learning_modules.deleted_at')
		;
	}

	public function userProgrammeWorkFlowRelation(){
		return $this->hasMany("Models\UserFormTemplateWorkflowRelations","reference_type_id","id");
	}

	public function EvidenceIssues()
	{
		return $this->belongsToMany('Models\ApprenticeshipIssues', 'apprenticeship_issues_evidence', 'learning_modules_id', 'apprenticeship_issues_id');
	}

	public function Meetings()
	{
		return $this->hasMany('Models\LearningModuleEvidenceMeeting', 'learning_modules_id', 'id');
	}
	public function Versions()
	{
		return $this->hasMany('Models\LearningModuleVersion', 'learning_module_id', 'id');
	}

	public function Schedules()
	{
		return $this
			->hasMany('Models\ScheduleLink', 'link_id', 'id')
			->where('type', 'resources');
	}

	public function ScheduleLink()
	{
		return $this
			->hasOne('Models\ScheduleLink', 'link_id', 'id')
			->where('type', 'resources');
	}

	// same as public function Schedules() {, just naming different
	public function ScheduleLinks()
	{
		return $this
			->Schedules();
	}

	public function ScheduleLesson()
	{
		return $this
			->hasOne('Models\ScheduleLink', 'link_id', 'id')
			->where('type', 'lesson');
	}

	public function ScheduleLessonLink()
	{
		return $this
			->hasOne('Models\ScheduleLink', 'link_id', 'id')
			->where('type', 'lesson');
	}

	public function ScheduleLessonLinks()
	{
		return $this
			->hasMany('Models\ScheduleLink', 'link_id', 'id')
			->where('type', 'lesson');
	}

	public function CreatedBy()
	{
		return $this->hasOne('Models\User', 'id', 'created_by');
	}

	public function Departments()
	{
		return $this->belongsToMany('Models\Department', 'department_learning_modules', 'learning_module_id', 'department_id');
	}

	public function Groups()
	{
		return $this->belongsToMany('Models\Group', 'group_learning_modules', 'learning_module_id', 'group_id');
	}

	public function Issues()
	{
		return $this->belongsToMany('Models\ApprenticeshipIssues', 'apprenticeship_issues_learning_modules', 'learning_modules_id', 'apprenticeship_issues_id');
	}

	public function LearningResults()
	{
		return $this->hasMany('Models\LearningResult', 'learning_module_id', 'id');
	}

	public function AssessmentCategories()
	{
		return $this->hasMany('Models\Assessment\Category', 'course_id', 'id');
	}

	public function AssessmentQuestions()
	{
		return $this->hasMany('Models\Assessment\Question', 'course_id', 'id');
	}

	public function LearningModuleEvidences()
	{
		return $this
			->hasMany('Models\LearningModuleEvidence', 'learning_modules_id', 'id');
	}

	public function LearningResultsComments()
	{
		return $this
			->hasMany('Models\LearningResultsComment', 'learning_module_id', 'id');
	}

	public function ApprenticeshipIssuesEvidences()
	{
		return $this
			->hasMany('Models\ApprenticeshipIssuesEvidence', 'learning_modules_id', 'id');
	}

	public function ApprenticeshipIssuesUserLearningModules()
	{
		return $this
			->hasMany('Models\ApprenticeshipIssuesUserLearningModules', 'learning_modules_id', 'id');
	}

	public function ApprenticeshipIssuesLearningModules()
	{
		return $this
			->hasMany('Models\ApprenticeshipIssuesLearningModules', 'learning_modules_id', 'id');
	}

	public function LearningResult()
	{
		return $this->hasOne('Models\LearningResult', 'learning_module_id', 'id');
		//return $this->belongsTo('Models\LearningResult', 'learning_module_id');
	}

	public function UserLearningModules()
	{
		return $this
			->hasMany('Models\UserLearningModule', 'learning_module_id', 'id')
			->whereNull('user_learning_modules.deleted_at')
		;
	}

	public function UserLearningModule()
	{
		return $this
			->hasOne('Models\UserLearningModule', 'learning_module_id', 'id')
			->whereNull('user_learning_modules.deleted_at')
		;
	}

	public function Feedback()
	{
		return $this->hasMany('Models\ModuleFeedback', 'module_id', 'id');
	}

	public function EvidenceType()
	{
		return $this->belongsTo('Models\EvidenceType', 'evidence_type_id');
	}

	public function EventType()
	{
		return $this->belongsTo('Models\EventType', 'event_type_id');
	}

	public function Files()
	{
		return $this
			->hasMany('Models\File', 'table_row_id', 'id')
			->where('table_name', 'learning_modules');
	}

	public function Designations() {
		return $this->belongsToMany('Models\Designation', 'designation_learning_modules', 'learning_module_id', 'designation_id');
	}


	public function TargetCatalogues() {
		return $this->belongsToMany('Models\TargetCatalogue', 'learning_module_target_catalogues', 'learning_module_id', 'target_catalogue_id');
	}
	public function LearningModuleDeliveryProviderType() {
		return $this->belongsTo('Models\LearningModuleDeliveryProviderType', 'delivery_provider_type_id');
	}
	public function LearningModuleGroupDepartmentCode() {
		return $this->belongsTo('Models\LearningModuleGroupDepartmentCode', 'group_department_code_id');
	}

	public function Queries() {
		return $this
			->hasMany('Models\ResourceQuery', 'type_id', 'id')
			->where('type', 'lessons')
		;
	}

	public static function countAndConditions($query, &$args)
	{
		if (
			isset($args["link"]) &&
			$args["link"] == '/resources/' &&
			isset($args["link_id"])
		) {
			$resource_ids = json_decode($args["link_id"], TRUE);
			$query = $query
				->withCount(['LearningModules' => function ($query) use ($resource_ids) {
					$query
						->whereIn('apprenticeship_issues_learning_modules.learning_modules_id', $resource_ids);
				}]);
		}
		return $query;
	}

	public static function moduleSlideCountTotal($module_id)
	{
		$infoXmlFilePath = $GLOBALS["CONFIG"]->LMSPublicPath . 'scormdata/' . $module_id . '/moddata/scorm/1/xml/information.xml';
		if (file_exists($infoXmlFilePath))
		{
			$xml = simplexml_load_file($infoXmlFilePath);
			$screenCount = $xml->screen->count();
			return $screenCount;
		}
		return null;
	}

	// Returns count of learning modules added by user
	public static function getCreatedCount($user_id, $type_id)
	{
		$learning = \Models\LearningModule
			::where('status', true)
			->where('type_id', $type_id)
			->where('created_by', $user_id)
			->get();
		return count($learning);
	}

	public static function deleteImage($learning = false, $field = false, $settings = false)
	{
		// "field" and "image" parameters are passed, one identifies field name that needs to be updated, other one will contain image name

		$file_config = [
			'thumbnail' => [
				'path' => 'LMSThumbPath'
			],
			'promo_image' => [
				'path' => 'LMSPromoPath'
			],
			'highlight_image' => [
				'path' => 'LMSHighlightPath'
			],
			'accreditation_main_logo' => [
				'path' => 'LMSAccreditationPath'
			],
			'accreditation_logo' => [
				'path' => 'LMSAccreditationPath'
			]
		];


		if (
			$learning &&
			$field
		) {
			$image_name = $learning->{$field};
			// logic to determine what type of image is passed, currently there is only promo or thumbnail, so if else, if more types will be added, logic will need to be extended
			$imagepath = $file_config[$field]['path'];
			if (file_exists($settings[$imagepath] . '/' . $image_name)) {
				unlink($settings[$imagepath] . '/' . $image_name);
			}

			// empty field for update, only specific field is updated.
			$learning->{$field} = '';
			$learning->save();
		}
	}

	public static function imageList()
	{
		return [
			'thumbnail' => [
				'size' => 200000,
				'path' => 'LMSThumbPath'
			],
			'promo_image' => [
				'size' => 500000,
				'path' => 'LMSPromoPath'
			],
			'highlight_image' => [
				'size' => 600000,
				'path' => 'LMSHighlightPath'
			],
			'accreditation_main_logo' => [
				'size' => 500000,
				'path' => 'LMSAccreditationPath'
			],
			'accreditation_logo' => [
				'size' => 500000,
				'path' => 'LMSAccreditationPath'
			]
		];
	}

	// Checks if attached file names to resources have correct naming.
	public static function checkImageNames($settings)
	{
		$image_lists = \Models\LearningModule::imageList();
		$resources = \Models\LearningModule
			::where('thumbnail', '>', '');

		foreach ($image_lists as $key => $image) {
			$resources = $resources
				->orWhere($key, '>', '');
		}

		$resources = $resources
			->get();

		foreach ($resources as $key => $resource) {
			$needs_saving = false;
			foreach ($image_lists as $image_key => $image) {
				if (
					isset($resource->{$image_key}) &&
					$resource->{$image_key}
				) {
					$path_parts = pathinfo($settings[$image['path']] . $resource->{$image_key});
					if ($path_parts['filename'] != $image_key . '_' . $resource->id) {
						if (is_file($settings[$image['path']] . $resource->{$image_key})) {
							$new_file_name = $image_key . '_' . $resource->id . '.' . $path_parts['extension'];
							rename($settings[$image['path']] . $resource->{$image_key}, $settings[$image['path']] . $new_file_name);
							$resource->{$image_key} = $new_file_name;
							$needs_saving = true;
						}
					}
				}
			}

			if ($needs_saving) {
				$resource->save();
			}
		}


	}

	public static function uploadImages($response, $data, $files, $settings, $learningModule) {
		$image_lists = \Models\LearningModule::imageList();
		$return_data = [];

		foreach ($image_lists as $image_key => $image_entry) {
			if (
				isset($files[$image_key]) &&
				$files[$image_key]
			) {
				// Get resource record.
				if (isset($files[$image_key])) {
					$storage = new \Upload\Storage\FileSystem($settings[$image_entry['path']], true);
					$image = new \Upload\File($image_key, $storage);
					$fileSizeValidation = new \Upload\Validation\Size($image_entry['size']);
				}

				//$imageFileName = preg_replace('/[^a-zA-Z0-9]/', '_', $learningModule->name);
				$image->setName($image_key . '_' . time() . '_' . $learningModule->id);

				$extension = strtolower($image->getExtension());
				$imageFileName = $image->getName() . '.' . $extension;
				$fileTypeValidation = new \Upload\Validation\Mimetype(['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp']);
				$fileTypeValidation->setMessage("Invalid file type. You must upload a image file.");
				$image->addValidations([
					$fileTypeValidation,
					$fileSizeValidation
				]);

				try {
					$old_image = $learningModule->{$image_key};
					$image->upload();
					$learningModule->{$image_key} = $imageFileName;
					$learningModule->save();

					$return_data[$image_key] = $imageFileName;

					// Delete old image
					if ($old_image) {
						$old_image_path = $settings[$image_entry['path']] . $old_image;
						if (file_exists($old_image_path)) {
							@unlink($old_image_path);
						}
					}

				} catch (\Upload\Exception\UploadException $e) {
					$errors = $image->getErrors();
					$response->getBody()->write("'$image_key' " . implode("\n", $errors));
					return
						$response
							->withStatus(500)
							->withHeader('Content-Type', 'text/html')
					;
				}
			}
		}
		return $return_data;
	}

	// Get list of all issues in standard that are NOT labeled as not to show untill givin date
	public static function getIncludeStandardModules($standard_user, $standard_id = false) {

		// Get all standards user is assigned to, loop
		$query = \Models\ApprenticeshipStandardUser
			::where('user_id', $standard_user->user_id)
			->with(["standard" => function ($query) use ($standard_user) {
				$query
					->where('status', true)
					->with(["issuecategories" => function ($query) use ($standard_user) {
						$query
							->where('status', true)
							->with(["issues" => function ($query) use ($standard_user) {
								$query
									->whereDoesntHave('disabled', function ($query) use ($standard_user) {
										$query->where('user_id', $standard_user->user_id);
									})
									->where('status', true)
									->with(['modules' => function ($query) use ($standard_user) {
										$query
											->where('status', 1)
											->with(['LearningResult' => function ($query) use ($standard_user) {
												$query
													->where('refreshed', 0)
													->where('user_id', $standard_user->user_id)
													->whereNotNull('completion_date_custom')
												;
											}])
										;
										if (!\APP\Tools::getConfig('showUnassignedDisabledLearning')) {
											$query
												->whereHas('userlearningmodules', function ($query) use ($standard_user) {
													$query
														->where('user_id', $standard_user->user_id)
													;
												})
											;
										}
									}])
									->with(['evidencemodule' => function ($query) use ($standard_user) {
										$query
											->wherePivot('user_id', $standard_user->user_id)
											->where('status', 1)
										;
										if (!\APP\Tools::getConfig('showUnassignedDisabledLearning')) {
											$query
												->whereHas('userlearningmodules', function ($query) use ($standard_user) {
													$query
														->where('user_id', $standard_user->user_id)
													;
												})
											;
										}
									}])
									->with(['usermodules' => function ($query) use ($standard_user) {
										$query
											->wherePivot('user_id', $standard_user->user_id)
											->where('status', 1)
										;
										if (!\APP\Tools::getConfig('showUnassignedDisabledLearning')) {
											$query
												->whereHas('userlearningmodules', function ($query) use ($standard_user) {
													$query
														->where('user_id', $standard_user->user_id)
													;
												})
											;
										}
									}])
								;
							}])
						;
					}])
				;
			}])
		;
		if ($standard_id) {
			$query = $query
				->where('standard_id', $standard_id)
			;
		}

		$query = $query
			->get()
		;

		$module_ids = [];

		// Loopity loop
		foreach ($query as $userKey => $user) {
			if (isset($user->standard) && isset($user->standard->issuecategories)) {
				foreach ($user->standard->issuecategories as $categoryKey => $category) {
					foreach ($category->issues as $issueKey => $issue) {
						// Modules
						foreach ($issue->modules as $moduleKey => $module) {
							if ($issue->visible_resource) {
								$module_ids[] = $module->id;

								if ($module->is_course == 1) {
									foreach ($module->Modules as $key => $lesson_module) {
										$module_ids[] = $lesson_module->id;
									}
								}
							} else {
								$calculated_start = \Carbon\Carbon::parse($user->start_at)->addDays($issue->start_day);
								if ($module->pivot->custom_work_window) {
									$calculated_start = \Carbon\Carbon::parse($user->start_at)->addDays($module->pivot->start_day);
								}

								if (
									$user->ilr_link &&
									$user->paused_start &&
									$user->paused_end &&
									\Carbon\Carbon::parse($calculated_start)->between(\Carbon\Carbon::parse($user->paused_start), \Carbon\Carbon::parse($user->paused_end))
								) {
									$pause_duration = \Carbon\Carbon::parse($user->paused_start)->diffInDays(\Carbon\Carbon::parse($user->paused_end));
									$calculated_start = \Carbon\Carbon::parse($calculated_start)->addDays($pause_duration);
								}

								if (
									$calculated_start <= \Carbon\Carbon::now()
								) {
									$module_ids[] = $module->id;
									if ($module->is_course == 1) {
										foreach ($module->Modules as $key => $lesson_module) {
											$module_ids[] = $lesson_module->id;
										}
									}
								} else if (
									$module->LearningResult &&
									$module->LearningResult->completion_date_custom
								) {
									$module_ids[] = $module->id;
									if ($module->is_course == 1) {
										foreach ($module->Modules as $key => $lesson_module) {
											$module_ids[] = $lesson_module->id;
										}
									}
								}
							}
						}

						// Evidence
						foreach ($issue->evidencemodule as $evidencemoduleKey => $evidencemodule) {
							$module_ids[] = $evidencemodule->id;
						}

						// User Modules
						foreach ($issue->usermodules as $usermoduleKey => $usermodule) {
							$module_ids[] = $usermodule->id;
							if ($usermodule->is_course == 1) {
								foreach ($usermodule->Modules as $key => $lesson_module) {
									$module_ids[] = $lesson_module->id;
								}
							}
						}
					}
				}
			}

		}

		$module_ids = array_unique($module_ids);
		return $module_ids;
	}

	// get all active resources that are not in standard
	public static function getNonStandardModules($standard_user, $ignore_outcome = false)
	{
		$standard_modules = \Models\LearningModule::getStandardModules($standard_user, false, $ignore_outcome);
		$response_modules = [];
		$learning_results = \Models\LearningResult
			::select('learning_module_id')
			->where('refreshed', 0)
			->whereIn('learning_module_id',
				\Models\UserLearningModule
					::select('learning_module_id')
					->where('user_id', $standard_user->user_id)
					->get()
			)
			->where('user_id', $standard_user->user_id)
			->whereNotIn('learning_module_id', $standard_modules)
			->get();

		foreach ($learning_results as $key => $learning_result) {
			$response_modules[] = $learning_result->learning_module_id;
		}
		return $response_modules;
	}

	// get all active resources id's that are assigned to user in standards, including evidence and user modules
	public static function getStandardModules($standard_user, $current_standard = false, $ignore_outcome = false) {
		$issues_ids = \Models\ApprenticeshipIssues
			::getIssuesIds($standard_user->user_id, ($current_standard ? $standard_user->standard_id : false), $ignore_outcome)
		;
		$combined_ids = [];
		$modules = \Models\ApprenticeshipIssuesLearningModules
			::whereIn('apprenticeship_issues_id',
				$issues_ids
			)
			->with(['Module' => function ($query) {
				$query
					->where('status', 1)
					->with(['Modules' => function ($query) {
						$query
							->where('status', 1)
						;
					}])
				;
			}])
			->get()
		;
		// Need to check if any of them are lessons, if so, get assigned resources to those lessons
		foreach ($modules as $module_key => $module) {
			$combined_ids[] = $module->learning_modules_id;
			if (
				$module->Module &&
				$module->Module->is_course == 1
			) {
				foreach ($module->Module->Modules as $key => $lesson_module) {
					$combined_ids[] = $lesson_module->id;
				}
			}
		}


		$user_modules = \Models\ApprenticeshipIssuesUserLearningModules
			::whereIn('apprenticeship_issues_id',
				$issues_ids
			)
			->where('user_id', $standard_user->user_id)
			->select('learning_modules_id')
			->distinct('learning_modules_id')
			->with(['Module' => function ($query) {
				$query
					->where('status', 1)
					->with(['Modules' => function ($query) {
						$query
							->where('status', 1)
						;
					}])
				;
			}])
			->get()
		;
		foreach ($user_modules as $module_key => $user_module) {
			$combined_ids[] = $user_module->learning_modules_id;
			if (
				$user_module->Module &&
				$user_module->Module->is_course == 1
			) {
				foreach ($user_module->Module->Modules as $key => $lesson_module) {
					$combined_ids[] = $lesson_module->id;
				}
			}
		}


		$evidence_modules = \Models\ApprenticeshipIssuesEvidence
			::whereIn('apprenticeship_issues_id',
				$issues_ids
			)
			->where('user_id', $standard_user->user_id)
			->select('learning_modules_id')
			->distinct('learning_modules_id')
			->with(['Module' => function ($query) {
				$query
					->where('status', 1)
					->with(['Modules' => function ($query) {
						$query
							->where('status', 1)
						;
					}])
				;
			}])
			->get()
		;
		foreach ($evidence_modules as $module_key => $evidence_module) {
			$combined_ids[] = $evidence_module->learning_modules_id;
			if (
				$evidence_module->Module &&
				$evidence_module->Module->is_course == 1
			) {
				foreach ($evidence_module->Module->Modules as $key => $lesson_module) {
					$combined_ids[] = $lesson_module->id;
				}
			}
		}

		$combined_ids = array_unique($combined_ids);
		sort($combined_ids);

		return $combined_ids;
	}

	// if you are not admin, you don't need to see evidence resource added by learners not assigned to you
	public static function filterEvidenceforRoles($query) {
		return
			$query
				->where(function ($query) {
					$query
						->whereNotIn('type_id',
							\Models\LearningModuleType
								::select('id')
								->where('slug', 'upload')
								->orWhere('slug', 'reflective_log')
								->get()
						)
						->orWhere(function ($query) {
							$query
								->whereIn('type_id',
									\Models\LearningModuleType
										::select('id')
										->where('slug', 'upload')
										->orWhere('slug', 'reflective_log')
										->get()
								)
								->where('created_in_learner_interface', false)
							;
						})
						->orWhereNull('type_id')
					;
				})
				->where(function ($query) {
					$query
						->whereNotIn('learning_modules.created_by',
							\Models\User
								::select('id')
								->whereIn('role_id',
									\Models\Role
										::select('id')
										->where('is_learner', true)
										->get()
								)
								->get()
						)
						->orWhereNull('learning_modules.created_by')
						->orWhere(function ($query) {
							$query
								->whereIn('type_id',
									\Models\LearningModuleType
										::select('id')
										->orWhere('slug', 'e_learning')
										->get()
								)
								->where('created_in_learner_interface', false)
							;
						})
					;
				})
		;
	}

	// Import resource and attach to criteria, used by standard/programme import logic
	public static function importModule($moduleValue, $issue, $settings, $extracted_directory) {
		// Check if module exists by the name, if yes, attach it to issue
		$module = \Models\LearningModule
			::where('name', $moduleValue->name)
			->first()
		;
		// If module does not exists.
		if (!$module) {

			// create new module.
			$module = new \Models\LearningModule;
			$module->id = \APP\Course::getNewCourseId(
				$moduleValue->name,
				$settings["FixedCourseIds"],
				$settings["CourseIdStart"]
			);


			$module->code = $moduleValue->code;
			$module->name = $moduleValue->name;

			if (isset($moduleValue->category->name)) {
				$category = \Models\LearningModuleCategory::firstOrCreate(
					['name' => $moduleValue->category->name],
					['status' => 1]
				);
				$module->category_id = $category->id;
			}

			if (isset($moduleValue->fpcategory->name)) {
				// create new F&P category if does not exists
				$f_p_category = \Models\SmcrFPCategory::firstOrCreate(
					['slug' => $moduleValue->fpcategory->slug],
					[
						'name' => $moduleValue->fpcategory->name,
						'status' => 1,
					]
				);
				$module->f_p_category_id = $f_p_category->id;
			}

			$module->keywords = $moduleValue->keywords;
			$module->self_enroll = $moduleValue->self_enroll;
			$module->print_lesson = $moduleValue->print_lesson;
			$module->approval = $moduleValue->approval;
			//$module->company_id = $moduleValue->company_id;
			$module->refresh = $moduleValue->refresh;
			$module->refresh_period = $moduleValue->refresh_period;
			$module->refresh_repeat = $moduleValue->refresh_repeat;
			$module->refresh_custom_email = $moduleValue->refresh_custom_email;
			$module->refresh_custom_email_subject = $moduleValue->refresh_custom_email_subject;
			$module->refresh_custom_email_body = $moduleValue->refresh_custom_email_body;
			$module->due_after_period = $moduleValue->due_after_period;
			$module->expiration_date = $moduleValue->expiration_date;
			$module->description = $moduleValue->description;

			if (isset($moduleValue->type->slug)) {
				$module_type = \Models\LearningModuleType::where('slug', $moduleValue->type->slug)->first();
				$module->type_id = $module_type->id; // check slug!
			}


			$module->material = $moduleValue->material;
			// version - not yet!
			$module->is_course = $moduleValue->is_course;
			$module->order_modules = $moduleValue->order_modules;
			$module->language = $moduleValue->language;
			$module->cost = $moduleValue->cost;
			$module->duration_hours = $moduleValue->duration_hours;
			$module->duration_minutes = $moduleValue->duration_minutes;
			$module->duration_change = $moduleValue->duration_change;
			//$module->provider_id = $moduleValue->provider_id;
			// responsible_user ??????
			$module->level = $moduleValue->level;
			$module->do_prerequisite = $moduleValue->do_prerequisite;
			$module->status = $moduleValue->status;

			$module->accreditation_description = $moduleValue->accreditation_description;
			$module->accreditation_alternative_learning_name = $moduleValue->accreditation_alternative_learning_name;
			$module->jackdaw = $moduleValue->jackdaw;
			$module->jackdaw_resource = $moduleValue->jackdaw_resource;
			//$module->jackdaw_access_token = $moduleValue->jackdaw_access_token;
			$module->project = $moduleValue->project;
			$module->created_by = \APP\Auth::getUserId();
			// created_by_group ????
			$module->api_match = $moduleValue->api_match;
			$module->rating = $moduleValue->rating;

			if (isset($moduleValue->evidencetype->name)) {
				// create new F&P category if does not exists
				$evidence_type = \Models\EvidenceType::firstOrCreate(
					['name' => $moduleValue->evidencetype->name],
					['status' => 1]
				);
				$module->evidence_type_id = $evidence_type->id;
			}

			$module->require_management_signoff = $moduleValue->require_management_signoff;

			if (isset($moduleValue->eventtype->name)) {
				// create new F&P category if does not exists
				$event_type = \Models\EventType::firstOrCreate(
					['name' => $moduleValue->eventtype->name],
					[
						'slug' => \APP\Tools::safeName($moduleValue->eventtype->name) . '_' . \APP\Tools::unsecureRandom(),
						'status' => 1,
					]
				);
				$module->event_type_id = $event_type->id;
			}

			$module->guideline = $moduleValue->guideline;
			$module->is_skillscan = $moduleValue->is_skillscan;
			$module->track_progress = $moduleValue->track_progress;
			$module->print_certificate = $moduleValue->print_certificate;
			$module->created_in_learner_interface = $moduleValue->created_in_learner_interface;
			$module->player_width = $moduleValue->player_width;
			$module->player_height = $moduleValue->player_height;
			$module->go1_id = $moduleValue->go1_id;
			$module->visible_learner = $moduleValue->visible_learner;
			$module->save();

			// importResourceImage will deal with all images!
			\Models\LearningModule::importResourceImage($module, $moduleValue, $extracted_directory);

			// If evidence files are present, add them!
			foreach ($moduleValue->learningmoduleevidences as $evidence_key => $evidence) {
				// LearningModuleEvidence
				if (is_file($extracted_directory . '/resources/' . $moduleValue->id . '/evidence/' . $evidence->evidence)) {
					$hash = bin2hex(random_bytes(16));
					copy(
						$extracted_directory. '/resources/' . $moduleValue->id . '/evidence/' . $evidence->evidence,
						$GLOBALS["CONFIG"]->LMSEvidencePath . $hash . '.' . $evidence->extension
					);
					$new_evidence = new \Models\LearningModuleEvidence;
					$new_evidence->learning_modules_id = $module->id;
					$new_evidence->added_by = \APP\Auth::getUserId();
					$new_evidence->manager = 1;
					$new_evidence->evidence = $evidence->evidence;
					$new_evidence->hash = $hash;
					$new_evidence->extension = $evidence->extension;
					$new_evidence->evidence_type = $evidence->evidence_type;
					$new_evidence->file_size = filesize($GLOBALS["CONFIG"]->LMSEvidencePath . $hash . '.' . $evidence->extension);
					$new_evidence->status = $evidence->status;
					$new_evidence->save();
				}

			}
			// if resource is e-learning and exists in reposotory, copy over
			if (
				isset($module_type->slug) &&
				$module_type->slug == 'e_learning' &&
				is_dir($settings["AvailableModulesLocation"] . "/" . $module->name)
			) {
				$course = \APP\Course::get($module);

				try {
					$source_location = $settings["AvailableModulesLocation"] . "/" . $module->name;

					$course->copyAvailableModule(
						$source_location,
						$settings["LMSScormDataPath"]
					);

				} catch (\APP\ScormException $e) {
					$course->deleteScormSetup();
					\Models\LearningModule::deleteResourceImages($module);
					$module->delete();
					$module->id = null;
				}
			}
		} else {
			$new_relationship = new \Models\ApprenticeshipIssuesLearningModules;
			$new_relationship->apprenticeship_issues_id = $issue->id;
			$new_relationship->learning_modules_id = $module->id;
			$new_relationship->sort = $moduleValue->pivot->sort;
			$new_relationship->custom_work_window = $moduleValue->pivot->custom_work_window;
			$new_relationship->start_day = $moduleValue->pivot->start_day;
			$new_relationship->end_day = $moduleValue->pivot->end_day;
			$new_relationship->save();
		}
	}

	public static function importResourceImage($resource_import, $resource_export, $extracted_directory) {
		$import_images = [
			'thumbnail' => $GLOBALS["CONFIG"]->LMSThumbPath,
			'promo_image' => $GLOBALS["CONFIG"]->LMSPromoPath,
			'highlight_image' => $GLOBALS["CONFIG"]->LMSHighlightPath,
			'accreditation_main_logo' => $GLOBALS["CONFIG"]->LMSAccreditationPath,
			'accreditation_logo' => $GLOBALS["CONFIG"]->LMSAccreditationPath,
		];
		foreach ($import_images as $import_image_key => $import_image) {
			if (is_file($extracted_directory. '/resources/' . $resource_export->id . '/' . $import_image_key . '/' . $resource_export->{$import_image_key})) {
				copy(
					$extracted_directory. '/resources/' . $resource_export->id . '/' . $import_image_key . '/' . $resource_export->{$import_image_key},
					$import_image . str_replace($resource_export->id, $resource_import->id, $resource_export->{$import_image_key})
				);
				$resource_import->{$import_image_key} = str_replace($resource_export->id, $resource_import->id, $resource_export->{$import_image_key});
			}
		}

		$resource_import->save();
	}

	public static function deleteResourceImages($resource) {
		$import_images = [
			'thumbnail' => $GLOBALS["CONFIG"]->LMSThumbPath,
			'promo_image' => $GLOBALS["CONFIG"]->LMSPromoPath,
			'highlight_image' => $GLOBALS["CONFIG"]->LMSHighlightPath,
			'accreditation_main_logo' => $GLOBALS["CONFIG"]->LMSAccreditationPath,
			'accreditation_logo' => $GLOBALS["CONFIG"]->LMSAccreditationPath,
		];
		foreach ($import_images as $import_image_key => $import_image) {
			if (is_file($GLOBALS["CONFIG"]->LMSThumbPath . $resource->{$import_image_key})) {
				unlink($GLOBALS["CONFIG"]->LMSThumbPath . $resource->{$import_image_key});
			}
		}

	}

	public function getMaterialAttribute($value)
	{
		if (empty($value)) {
			return new \stdClass();
		}

		if ($this->type_id == 4 || $this->type_id == 6) {
			//classroom training
			if ($this->type_id == 4) {
				$timing_key = "bookingWindows_classroom";
			} //on the job training
			elseif ($this->type_id == 6) {
				$timing_key = "bookingWindows_onTheJob";
			}
			$session_booking_window = \Models\Timing
				::where("key", "=", $timing_key)
				->first()
				->timing;

			$material = json_decode($value);
			$filtered_sessions = [];

			foreach ($material->sessions as $session) {
				$session_date = \Carbon\Carbon::parse($session->date);
				$session_time = \Carbon\Carbon::parse($session->time);
				$session_date->setTimezone(\Carbon\Carbon::now()->tz);
				$session_time->setTimezone(\Carbon\Carbon::now()->tz);
				$session_date->hour = $session_time->hour;
				$session_date->minute = $session_time->minute;
				$session_date->second = 0;

				$session_date = \Carbon\Carbon::parse($session->date);

				if (\Carbon\Carbon::now()->gt($session_date->subDays($session_booking_window))) {
					continue;
				}

				$learning_sessions = \Models\LearningSession
					::where("learning_module_id", "=", $this->id)
					->where("completed", "=", false)
					->where("approved", "=", true)
					->where("session_uid", "=", $session->session_uid);

				$session->enrolled = $learning_sessions->count();
				$filtered_sessions[] = $session;
			}

			$material->sessions = $filtered_sessions;

			return $material;
		}
		return json_decode($value);
	}

	public function setRefreshPeriodAttribute($value)
	{
		if (empty($value)) {
			$this->attributes['refresh_period'] = 0;
		} else {
			$this->attributes['refresh_period'] = $value;
		}
	}

	public function setMaterialAttribute($value)
	{
		if (!empty($value)) {
			$this->attributes['material'] = json_encode($value);
		} else {
			$this->attributes['material'] = "";
		}
	}


	public function getSafeThumbnailAttribute() {
		$LMSPublicPath = $GLOBALS["CONFIG"]->LMSPublicPath;
		$LMSDefaultTypeCustomPath = $GLOBALS["CONFIG"]->LMSDefaultTypeCustomPath;
		$WWWPublicPath = $GLOBALS["CONFIG"]->LMSUrl;
		if ($this->thumbnail){
			$thumbnail = explode("?r=", $this->thumbnail)[0];
		}

		$types = [
			'e_learning' => 'e-learning_thumb.jpg',
			'youtube' => 'youtube_thumb.jpg',
			'webpage' => 'webpage_thumb.jpg',
			'zoom_meeting' => 'zoom_meeting_thumb.jpg',
			'microsoft_teams' => 'microsoft_teams_thumb.jpg',
			'classroom' => 'classroom_thumb.jpg',
			'book_cd_dvd' => 'bookcddvd_thumb.jpg',
			'on_the_job' => 'on-the-job.jpg',
			'upload' => 'upload_thumb.jpg',
			'blog_entry' => 'blog-entry-thumb.jpg',
			'reflective_log' => 'reflective-log_thumb.jpg',
			'vimeo' => 'vimeo_thumb.jpg',
			'google_classroom' => 'google_classroom_thumb.jpg',
			'moodle_course' => 'moodle_course_thumb.jpg',
			'h5p' => 'h5p_thumb.jpg',
			'jamboard' => 'jamboard.jpg',
			'form' => 'form.jpeg',
			'skill' => 'skill_thumb.jpg',
		];


		// In case some broken resource without type, show default e_learning
		$type_slug = 'e_learning';
		if (
			$this->type &&
			$this->type->slug
		) {
			$type_slug = $this->type->slug;
		} else if ($this->is_skill) {
			$type_slug = 'skill';
		}


		$response = false;
		if (
			$this->thumbnail &&
			is_file($LMSPublicPath . 'images/thumbnails/' . $thumbnail)
		) {
			$response = $WWWPublicPath . 'images/thumbnails/' . $this->thumbnail;
		} else if (
			is_file($LMSPublicPath . 'scormdata/' . $this->id . '/moddata/scorm/1/images/thumb.jpg')
		) {
			$response = $WWWPublicPath . 'scorm/file.php/' . $this->id . '/moddata/scorm/1/images/thumb.jpg';
		} else if ($this->is_course) {
			$response = $WWWPublicPath . 'images/default_type/lesson_thumb.jpg';
			if (is_file($LMSDefaultTypeCustomPath . 'lesson_thumb.jpg')) {
				$response = $WWWPublicPath . 'images/default_type_custom/lesson_thumb.jpg';
			}
		} else if (isset($types[$type_slug])) {
			$response = $WWWPublicPath . 'images/default_type/' . $types[$type_slug];
			if (is_file($LMSDefaultTypeCustomPath . $types[$type_slug])) {
				$response = $WWWPublicPath . 'images/default_type_custom/' . $types[$type_slug];
			}

			// Skill scan is e-learning, but unique thumbnail
			if (
				$type_slug == 'e_learning' &&
				$this->is_skillscan
			) {
				$response = $WWWPublicPath . 'images/default_type/skill_scan_thumb.jpg';
			}

		} else { // In case type is missing, serve at least some image.
			$response = $WWWPublicPath . 'images/default_type/e-learning_thumb.jpg';
			if (is_file($LMSDefaultTypeCustomPath . 'e-learning_thumb.jpg')) {
				$response = $WWWPublicPath . 'images/default_type_custom/e-learning_thumb.jpg';
			}
		}
		return $response;
	}


	public function getSafePromoAttribute() {
		$LMSPublicPath = $GLOBALS["CONFIG"]->LMSPublicPath;
		$LMSDefaultTypeCustomPath = $GLOBALS["CONFIG"]->LMSDefaultTypeCustomPath;
		$WWWPublicPath = $GLOBALS["CONFIG"]->LMSUrl;
		if ($this->promo_image) {
			$promo = explode("?r=", $this->promo_image)[0];
		}

		$types = [
			'e_learning' => 'e-learning.jpg',
			'youtube' => 'youtube.jpg',
			'webpage' => 'webpage.jpg',
			'zoom_meeting' => 'zoom_meeting.jpg',
			'microsoft_teams' => 'microsoft_teams.jpg',
			'classroom' => 'classroom.jpg',
			'book_cd_dvd' => 'bookcddvd.jpg',
			'on_the_job' => 'on-the-job.jpg',
			'upload' => 'upload.jpg',
			'blog_entry' => 'blog-entry.jpg',
			'reflective_log' => 'reflective-log.jpg',
			'vimeo' => 'vimeo.jpg',
			'google_classroom' => 'google_classroom.jpg',
			'moodle_course' => 'moodle_course.jpg',
			'h5p' => 'h5p.jpg',
			'form' => 'form.jpeg',
			'skill' => 'skill.jpg',
		];

		// In case some broken resource without type, show default e_learning
		$type_slug = 'e_learning';
		if (
			$this->type &&
			$this->type->slug
		) {
			$type_slug = $this->type->slug;
		} else if ($this->is_skill) {
			$type_slug = 'skill';
		}

		$response = false;
		if (
			$this->promo_image &&
			is_file($LMSPublicPath . 'images/promo/' . $promo)
		) {
			$response = $WWWPublicPath . 'images/promo/' . $this->promo_image;
		} else if (
			is_file($LMSPublicPath . 'scormdata/' . $this->id . '/moddata/scorm/1/images/promo.jpg')
		) {
			$response = $WWWPublicPath . 'scorm/file.php/' . $this->id . '/moddata/scorm/1/images/promo.jpg';
		} else if ($this->is_course) {
			$response = $WWWPublicPath . 'images/default_type/lesson.jpg';
			if (is_file($LMSDefaultTypeCustomPath . 'lesson.jpg')) {
				$response = $WWWPublicPath . 'images/default_type_custom/lesson.jpg';
			}
		} else if (isset($types[$type_slug])) {
			$response = $WWWPublicPath . 'images/default_type/' . $types[$type_slug];
			if (is_file($LMSDefaultTypeCustomPath . $types[$type_slug])) {
				$response = $WWWPublicPath . 'images/default_type_custom/' . $types[$type_slug];
			}
		} else {
			$response = $WWWPublicPath . 'images/default_type/e-learning.jpg';
			if (is_file($LMSDefaultTypeCustomPath . 'e-learning.jpg')) {
				$response = $WWWPublicPath . 'images/default_type_custom/e-learning.jpg';
			}
		}
		return $response;
	}

	public function getHighlightAttribute() {
		$LMSPublicPath = $GLOBALS["CONFIG"]->LMSPublicPath;
		$WWWPublicPath = $GLOBALS["CONFIG"]->LMSUrl;

		$response = false;

		if (
			$this->highlight_image &&
			is_file($LMSPublicPath . 'images/highlight/' . $this->highlight_image)
		) {
			$response = $WWWPublicPath . 'images/highlight/' . $this->highlight_image;
		} else if (is_file($LMSPublicPath . 'scormdata/' . $this->id . '/moddata/scorm/1/images/highlight.jpg')) {
			$response = $WWWPublicPath . 'scorm/file.php/' . $this->id . '/moddata/scorm/1/images/highlight.jpg';
		}

		return $response;
	}

	// Get all standdards module is attached to!
	public static function Standards($module_id, $user_id = false, $standard_id = false, $only_name = true) {
		$standards = \Models\ApprenticeshipStandard
			::whereHas('Issuecategories', function ($query) use ($module_id, $user_id) {
				$query
					->where('apprenticeship_issue_categories.status', true)
					->whereHas('Issues', function ($query) use ($module_id, $user_id) {
						$query
							->where('apprenticeship_issues.status', true)
							->where(function ($query) use ($module_id, $user_id) {
								$query
									->whereIn('apprenticeship_issues.id',
										\Models\ApprenticeshipIssuesLearningModules
											::select('apprenticeship_issues_id')
											->where('learning_modules_id', $module_id)
											->orWhereIn('learning_modules_id',
												\Models\LearningCourseModule
													::select('learning_course_id')
													->where('learning_module_id', $module_id)
													->get()
											)
											->get()
									)
									->orWhereIn('apprenticeship_issues.id',
										\Models\ApprenticeshipIssuesUserLearningModules
											::select('apprenticeship_issues_id')
											->where(function ($query) use ($module_id) {
												$query = $query
													->where('learning_modules_id', $module_id)
													->orWhereIn('learning_modules_id',
														\Models\LearningCourseModule
															::select('learning_course_id')
															->where('learning_module_id', $module_id)
															->get()
													)
												;
											})
											->where('user_id', $user_id)
											->get()
									)
									->orWhereIn('apprenticeship_issues.id',
										\Models\ApprenticeshipIssuesEvidence
											::select('apprenticeship_issues_id')
											->where(function ($query) use ($module_id) {
												$query = $query
													->where('learning_modules_id', $module_id)
													->orWhereIn('learning_modules_id',
														\Models\LearningCourseModule
															::select('learning_course_id')
															->where('learning_module_id', $module_id)
															->get()
													)
												;
											})
											->where('user_id', $user_id)
											->get()
									)
								;
							})
						;
					})
				;
			})
			->where('status', true);

		if ($user_id) {
			$standards = $standards
				->whereIn('id',
					\Models\ApprenticeshipStandardUser
						::select('standard_id')
						->where('user_id', $user_id)
						->get()
				);
		}

		if ($standard_id) {
			$standards = $standards
				->where('id', $standard_id);
		}

		$standards = $standards
			->get()
		;

		if ($only_name) {
			$standards = $standards
				->pluck('name')
				->toArray()
			;
		}

		return $standards;
	}

	// Get all outcomes module is attached to!
	public static function Outcomes($module_id, $user_id = false, $standard_id = false) {
		$outcomes = \Models\ApprenticeshipIssueCategories
			::where('apprenticeship_issue_categories.status', 1)
			->whereHas('Issues', function ($query) use ($module_id, $user_id) {
				$query
					->where(function ($query) use ($module_id, $user_id) {
						$query
							->whereIn('apprenticeship_issues.id',
								\Models\ApprenticeshipIssuesLearningModules
									::select('apprenticeship_issues_id')
									->where('learning_modules_id', $module_id)
									->orWhereIn('learning_modules_id',
										\Models\LearningCourseModule
											::select('learning_course_id')
											->where('learning_module_id', $module_id)
											->get()
									)
									->get()
							)
							->orWhereIn('apprenticeship_issues.id',
								\Models\ApprenticeshipIssuesUserLearningModules
									::select('apprenticeship_issues_id')
									->where('learning_modules_id', $module_id)
									->orWhereIn('learning_modules_id',
										\Models\LearningCourseModule
											::select('learning_course_id')
											->where('learning_module_id', $module_id)
											->get()
									)
									->where('user_id', $user_id)
									->get()
							)
							->orWhereIn('apprenticeship_issues.id',
								\Models\ApprenticeshipIssuesEvidence
									::select('apprenticeship_issues_id')
									->where('learning_modules_id', $module_id)
									->orWhereIn('learning_modules_id',
										\Models\LearningCourseModule
											::select('learning_course_id')
											->where('learning_module_id', $module_id)
											->get()
									)
									->where('user_id', $user_id)
									->get()
							)
						;
					})
				;
			})
		;

		if ($user_id) {
			$outcomes = $outcomes
				->whereIn('apprenticeship_issue_categories.standard_id',
					\Models\ApprenticeshipStandardUser
						::select('standard_id')
						->where('user_id', $user_id)
						->get()
				)
			;
		}

		if ($standard_id) {
			$outcomes = $outcomes
				->where('apprenticeship_issue_categories.standard_id', $standard_id)
			;
		}

		$outcomes = $outcomes
			->get();

		return $outcomes;

	}

	// Get all criteria ttached to evidence
	public static function Criteria($module_id, $user_id = false, $standard_id = false) {
		$criteria = \Models\ApprenticeshipIssues
			::where(function ($query) use ($module_id, $user_id) {
				$query
					->whereIn('apprenticeship_issues.id',
						\Models\ApprenticeshipIssuesLearningModules
							::select('apprenticeship_issues_id')
							->where('learning_modules_id', $module_id)
							->orWhereIn('learning_modules_id',
								\Models\LearningCourseModule
									::select('learning_course_id')
									->where('learning_module_id', $module_id)
									->get()
							)
							->get()
					)
					->orWhereIn('apprenticeship_issues.id',
						\Models\ApprenticeshipIssuesUserLearningModules
							::select('apprenticeship_issues_id')
							->where('learning_modules_id', $module_id)
							->orWhereIn('learning_modules_id',
								\Models\LearningCourseModule
									::select('learning_course_id')
									->where('learning_module_id', $module_id)
									->get()
							)
							->where('user_id', $user_id)
							->get()
					)
					->orWhereIn('apprenticeship_issues.id',
						\Models\ApprenticeshipIssuesEvidence
							::select('apprenticeship_issues_id')
							->where('learning_modules_id', $module_id)
							->orWhereIn('learning_modules_id',
								\Models\LearningCourseModule
									::select('learning_course_id')
									->where('learning_module_id', $module_id)
									->get()
							)
							->where('user_id', $user_id)
							->get()
					)
				;
			})
			->whereIn('apprenticeship_issues.issue_category_id',
				\Models\ApprenticeshipIssueCategories
					::select('apprenticeship_issue_categories.id')
					->where('apprenticeship_issue_categories.status', 1)
					->get()
			)
		;

		if ($user_id) {
			$criteria = $criteria
				->whereIn('apprenticeship_issues.issue_category_id',
					\Models\ApprenticeshipIssueCategories
						::select('apprenticeship_issue_categories.id')
						->whereIn('apprenticeship_issue_categories.standard_id',
							\Models\ApprenticeshipStandardUser
								::select('apprenticeship_standards_users.standard_id')
								->where('apprenticeship_standards_users.user_id', $user_id)
								->get()
						)
						->get()
				)
			;
		}

		if ($standard_id) {
			$criteria = $criteria
				->whereIn('apprenticeship_issues.issue_category_id',
					\Models\ApprenticeshipIssueCategories
						::select('apprenticeship_issue_categories.id')
						->where('apprenticeship_issue_categories.standard_id', $standard_id)
						->get()
				)
			;
		}

		$criteria = $criteria
			->get();

		return $criteria;
	}

	public static function saveDataAsPDF ($result = false, $file_location = false) {
		// instantiate and use the dompdf class
		$dompdf = new \Dompdf\Dompdf();

		$comments = '';
		// Format comments as string
		foreach ($result->comments as $key => $comment) {
			$comments = $comments . '<p>By: <strong>' . $comment->createdby->fname . ' ' . $comment->createdby->lname . '</strong><br>';
			$comments = $comments . '<small>' . $comment->created_at_uk . '</small><br> ' . nl2br($comment->comment) . '</p>';
		}

		switch ($result->module->type->slug) {
			case 'reflective_log':
				$dompdf->loadHtml(
					'<h1>' . $result->module->name . '</h1>' .

					'<h2>Description</h2>' .
					'<p>' . $result->module->description . '</p>' .

					'<h2>What have I learnt?</h2>' .
					'<p>' . $result->log_learned . '</p>' .

					'<h2>What more do I need to learn?</h2>' .
					'<p>' . $result->log_to_learn . '</p>' .

					'<h2>How have I used this is my job role?</h2>' .
					'<p>' . $result->log_used . '</p>' .

					($comments > '' ? '<h2>Comments</h2>' : '') .
					($comments > '' ? $comments : '') .

					($result->duration_hours || $result->duration_minutes ? '<h2>Duration</h2>' : '') .
					($result->duration_hours || $result->duration_minutes ? '<p>' . $result->duration_hours . ' hours, ' . $result->duration_minutes . ' minutes</p>' : '')
				);
				break;

			default:
				// Can tackle custom ones here, later
				$dompdf->loadHtml(
					'<h1>' . $result->module->name . '</h1>' .

					'<h2>Description</h2>' .
					'<p>' . $result->module->description . '</p>' .

					($result->duration_hours || $result->duration_minutes ? '<h2>Duration</h2>' : '') .
					($result->duration_hours || $result->duration_minutes ? '<p>' . $result->duration_hours . ' hours, ' . $result->duration_minutes . ' minutes</p>' : '')
				);
				break;
		}


		// (Optional) Setup the paper size and orientation
		$dompdf->setPaper('A4', 'portrait');

		// Render the HTML as PDF
		$dompdf->render();

		if ($file_location) {
			// save file in specified location
			$output = $dompdf->output();
			if ($output) {
				file_put_contents($file_location, $output);
				if (is_file($file_location)) {
					return true;
				} else {
					return false;
				}
			} else {
				return false;
			}
			return true;
		} else {
			// Output the generated PDF to Browser
			$dompdf->stream(\APP\Tools::safeName($result->module->name, '_'));
		}
	}

	public function saveWithoutEvents(array $options = []) {
		return static::withoutEvents(function() use ($options) {
			return $this->save($options);
		});
	}

	public static function upgradeElearningRoomFiles($settings, $resource_ids = false) {
		$installedResources = \Models\LearningModule
			::where(function ($query) {
				$query
					->where('jackdaw', 1)
					->orWhere('jackdaw_resource', true)
				;
			})
			->whereIn( 'type_id',
				\Models\LearningModuleType
					::select('id')
					->where('slug', 'e_learning')
					->get()
			)
		;

		if ($resource_ids) {
			$installedResources = $installedResources
				->whereIn('id', $resource_ids)
			;
		}

		$installedResources = $installedResources
			->get()
		;

		$updated_resources = [];
		$failed_resources = [];
		foreach ($installedResources as $key => $installedResource) {
			// ignoring errors for update all
			try {
				// Update room files, force overwrite, if update succeed, add name to updated list.
				if (\APP\Jackdaw::updateRoomFiles($settings["LMSPublicPath"], $installedResource->id, true, true)) {
					$updated_resources[] = $installedResource->name;
				}
			} catch (Exception $e) {
				$failed_resources[] = $installedResource->name;
			}
		}
		return [
			'updated_resources' => $updated_resources,
			'failed_resources' => $failed_resources
		];
	}


	public static function insertImportModule($data, $settings, $reponse = false) {
		$availableModuleId = isset($data["availableModuleId"]) ? $data["availableModuleId"] : 0;
		$data["refresh_period"] = isset($data["refresh_period"]) ? $data["refresh_period"] : 0;
		$data["refresh_repeat"] = isset($data["refresh_repeat"]) ? $data["refresh_repeat"] : null;

		$learning = new \Models\LearningModule;
		if (!isset($data['level'])) {
			$data['level'] = 'NULL';
		}

		$fields = [
			"code", "name", "category_id", "keywords", "self_enroll", "approval", "require_management_signoff",
			"company_id", "refresh", "refresh_period", "refresh_repeat", "refresh_custom_email",
			"refresh_custom_email_subject", "refresh_custom_email_body", "description", "type_id", "is_course", "language",
			"cost", "duration_hours", "duration_minutes", "duration_change", "provider_id",
			"level", "do_prerequisite", "material", "accreditation_description", "evidence_type_id",
			"is_skillscan", "track_progress", "print_certificate", "f_p_category_id", "responsible_user",
			"expiration_date", "player_width", "player_height", "event_type_id", "visible_learner", "scorm_popup", "badge",
			"accreditation_alternative_learning_name", "disable_upon_completion", "copy_refresher_emails_to_line_managers",
			"is_skill", 'repetition_period', 'reset_learning', "delivery_provider_type_id", "group_department_code_id",
			"refresh_only_if_learning_meets_query", "further_customise_this_query", "is_maximo_qualification", "refresh_date",
			"remove_paid_status", "self_enroll_access", "editing_access","payment_buys", "created_by_event", "scorm_full_screen",
			"mandatory_certificate_upload",'add_to_ai_chat', 'library_item_id',"retake_fee"
		];

		\APP\Tools::setObjectFields($learning, $fields, $data);

		$learning->id = \APP\Course::getNewCourseId(
			$learning->name,
			$settings["FixedCourseIds"],
			$settings["CourseIdStart"]
		);

		$isEnterprise = !\APP\Tools::getConfig('sharedClients');
		$learning->status = 1;
		$learning->created_by = isset($data['created_by']) ? $data['created_by'] : (empty(\APP\Auth::getUserId()) ? null : \APP\Auth::getUserId());
		$learning->self_enroll_access = (isset($data['self_enroll']) && isset($data['self_enroll_access'])) ?  ($data['self_enroll_access'] == 0 && !$isEnterprise ? null : $data['self_enroll_access'])  : null;
		$learning->editing_access = isset($data['editing_access']) ?  ($data['editing_access'] == 0 && !$isEnterprise ? null : $data['editing_access'])  : null;
        $learning->save();
		if (isset($data['self_enroll'])) {
			if (isset($data['self_enroll_access'])) {
				if ($data['self_enroll_access'] == 1 || $data['self_enroll_access'] == 2) {
					if (\Models\CompanyModuleEnrollment::where('learning_module_id', $learning->id)->where('type', 1)->exists()) {
						\Models\CompanyModuleEnrollment::where('learning_module_id', $learning->id)->where('type', 1)->delete();
					}
					if(!isset($data['companies']) || count($data['companies'])==0 && !$isEnterprise)
					{
						$data['companies'] = [Auth::getUser()->company];
					}
					foreach ($data['companies'] as $company) {
						if ($company) {
							$CompanyModuleEnrollment = new CompanyModuleEnrollment();
							$CompanyModuleEnrollment->learning_module_id = $learning->id;
							$CompanyModuleEnrollment->company_id = $company['id'];
							$CompanyModuleEnrollment->type = 1;
							$CompanyModuleEnrollment->save();
						}
					}

				}
			}
		}
		if (isset($data['editing_access'])) {
			if ($data['editing_access'] == 1 || $data['editing_access'] == 2) {
				if (\Models\CompanyModuleEnrollment::where('learning_module_id', $learning->id)->where('type', 2)->exists()) {
					\Models\CompanyModuleEnrollment::where('learning_module_id', $learning->id)->where('type', 2)->delete();
				}
				if(!isset($data['edit_access_companies']) || count($data['edit_access_companies'])==0 && !$isEnterprise)
				{
						$data['edit_access_companies'] = [Auth::getUser()->company];
				}
				foreach ($data['edit_access_companies'] as $company) {
					if ($company) {
						$CompanyModuleEnrollment = new CompanyModuleEnrollment();
						$CompanyModuleEnrollment->learning_module_id = $learning->id;
						$CompanyModuleEnrollment->company_id = $company['id'];
						$CompanyModuleEnrollment->type = 2;
						$CompanyModuleEnrollment->save();
					}
				}
			}
		}

		if (isset($data["target_catalogues"])) {
			$target_catalogue_ids = [];
			foreach ($data["target_catalogues"] as $key => $target_catalogue) {
				$target_catalogue_ids[] = $target_catalogue['id'];
			}
			\Models\LearningModuleTargetCatalogue::syncEntries($learning->id, $target_catalogue_ids);
		}

		if(isset($data['credly_badge'])){
			$inserted_data = [] ;
			foreach($data['credly_badge'] as $credly_badge){
				LearningModuleCredlyBadge::updateOrCreate(['learning_module_id'=>$learning->id,'badge_template_id'=>$credly_badge['id']],['learning_module_id'=>$learning->id,'badge_template_id'=>$credly_badge['id']]);
				$inserted_data[]=$credly_badge['id'];
			}
			LearningModuleCredlyBadge::where(['learning_module_id'=>$learning->id])->whereNotIn('badge_template_id',$inserted_data)->delete();
		}
		$competencies = [];
		if (isset($data["competencies"])) {
			foreach ($data["competencies"] as $c) {
				$competencies[$c["id"]] = [
					"points" => $c["points"],
				];
			}
		}
		$learning->competencies()->sync($competencies);

		$linked_skills = [];
		if (isset($data["linked_skills"])) {
			foreach ($data["linked_skills"] as $c) {
				$linked_skills[$c["id"]] = [
					"link_skill_id" => $c["id"],
				];
			}
		   $learning->LinkedSkills()->sync($linked_skills);

		}

		$prerequisites = isset($data["prerequisites"]) ? $data["prerequisites"] : [];
		$learning->prerequisites()->sync(\APP\Tools::getObjectIds($prerequisites));
	        if (isset($data['linked'])) {
	            $linked = isset($data["linked"]) ? $data["linked"] : [];
	            foreach ($linked as $link) {
	                LinkedLearningModule::create([
	                    'learning_module_id' => $learning->id,
	                    'linked_learning_module_id' => $link['id'],
	                    'assign' => $link['assign'],
	                    'days' => isset($link['days'])?$link['days']:NULL,
	                ]);
	            }
	        }

		if (
			$learning->type_id == 1 &&
			isset($data["material"]["zip_file"])
		) {
			$course = \APP\Course::get($learning);
			$course->deleteScormSetup();

			try {
				if (is_array($data["material"]["zip_file"])) {
					$zipFileName = $data["material"]["zip_file"]['fileName'];
				} else {
					$zipFileName = $data["material"]["zip_file"];
				}
				$zipFilePath = $settings["LMSTempPath"] . $zipFileName;
				$course->setupScorm(
					$zipFilePath,
					$settings["LMSScormDataPath"]
				);

				// Check if this module can be edited by Jackdaw, if yes, set flag in DB and that will be used in listings pages to show/hide jackdaw button
				if (file_exists($settings["LMSScormDataPath"] . $learning->id . "/moddata/scorm/1/xml/information.xml")) {
					$learning->jackdaw = 1;
					$learning->save();
				}

			} catch (\APP\ScormException $e) {
				$learning->delete();
				return  ["status"=>500 ,"error"=>implode("\n", ["Scorm error." . $e->getMessage()])];
			}
		}

		if (
			$learning->type_id == 1 &&
			$availableModuleId > 0
		) {
			$course = \APP\Course::get($learning);
            $available_module = \Models\AvailableModule::getItem($availableModuleId);

			if ($available_module) {
                try {
                    $source_location = \Models\AvailableModule::download($available_module->id,$settings['LMSTempPath'].$available_module->original_filename,$settings['LMSTempPath']);
					$course->copyAvailableModule(
						$source_location,
						$settings["LMSScormDataPath"]
					);

				} catch (\APP\ScormException $e) {
					$course->deleteScormSetup();
					$learning->delete();
					return  ["status"=>500 ,"error"=>implode("\n", ["Scorm error." . $e->getMessage()])];
				}
			}
		}

		// check if installed resource is jackdaw compatible, might be better solutioin, someday
		if (
			$learning->type_id == 1 &&
			file_exists($settings["LMSScormDataPath"] . $learning->id . "/moddata/scorm/1/xml/information.xml")
		) {
			$learning->jackdaw = 1;
			$learning->save();

			// Overwrite room filews with newest version
			\APP\Jackdaw::updateRoomFiles($settings["LMSPublicPath"], $learning->id);
		}


		if (
			isset($data['material']) &&
			$data['material'] &&
			isset($data['material']['min_passing_percentage'])
		) {
			if (file_exists($settings["LMSScormDataPath"] . $learning->id . "/moddata/scorm/1/xml/information.xml")) {
				$xml = simplexml_load_file($settings["LMSScormDataPath"] . $learning->id . "/moddata/scorm/1/xml/information.xml");
				foreach ($xml as $key => $screen) {
					if (
						isset($screen->Quizes)
						&& isset($screen->Quizes->settings)
						&& isset($screen->Quizes->settings->PassMark)
					) {
						$screen->Quizes->settings->PassMark = ($data['material']['min_passing_percentage'] - 1);
						$xml->asXml($settings["LMSScormDataPath"] . $learning->id . "/moddata/scorm/1/xml/information.xml");
					}
				}
			}
		}

		// Add comments/urls if present
		if (
			isset($data['learning_module_evidences']) &&
			is_array($data['learning_module_evidences'])
		) {
			foreach ($data['learning_module_evidences'] as $key => $learning_module_evidence) {
				if (isset($learning_module_evidence['evidence'])) {
					$evidence = new \Models\LearningModuleEvidence;
					$evidence->learning_modules_id = $learning->id;
					$evidence->user_id = \APP\Auth::getUserId();
					$evidence->added_by = \APP\Auth::getUserId();
					$evidence->manager = \APP\Auth::isAdminInterface();
					$evidence->hash = bin2hex(random_bytes(16));
					$evidence->evidence = $learning_module_evidence['evidence'];
					$evidence->evidence_type = 'comment';
					$evidence->status = 1;
					$evidence->save();
				}
			}
		}
		return $learning;
	}

	public static function returnValidResourcesIDs($resource_ids) {
		if (count($resource_ids) == 0) {
			return [];
		}
		
		$cacheKey = 'all_valid_learning_modules';
		
		// Try to get all valid IDs from cache
		try {
			if (function_exists('cache')) {
				$allValidIds = cache()->get($cacheKey);
				if ($allValidIds !== null) {
					// Fast array intersection - only return requested IDs that are valid
					return array_values(array_intersect($resource_ids, $allValidIds));
				}
			}
		} catch (\Exception $e) {
			// Cache might not be available - continue without cache
		}
		
		// Cache miss - rebuild entire valid list
		$now = \Carbon\Carbon::now();
		$allValidIds = \Models\LearningModule
			::where('status', 1)
			->where(function($query) use ($now) {
				$query
					->whereNull('expiration_date')
					->orWhere('expiration_date', '>', $now)
				;
			})
			->pluck('id')
			->toArray()
		;
		
		// Cache all valid IDs for 24 hours (query builder runs once per hour)
		try {
			if (function_exists('cache')) {
				cache()->put($cacheKey, $allValidIds, 86400);
			}
		} catch (\Exception $e) {
			// Cache might not be available - continue without cache
		}
		
		// Return intersection of requested IDs with valid IDs
		return array_values(array_intersect($resource_ids, $allValidIds));
	}

	// Attach to any model build, if needed. show only enabled and not expired resources
	public function scopeValidResource($query) {
		return $query
			->where('learning_modules.status', 1)
			->where(function($query) {
				$query
					->whereNull("learning_modules.expiration_date")
					->orWhere("learning_modules.expiration_date", ">", \Carbon\Carbon::now())
				;
			})
		;
	}

	public function scopeCronTask($query) {
		$query->where('cron_task', true);
		return $query;
	}

	public static function updateCronStatus($changes, $entry) {
		$checkAlreadyExist = LearningModule::where('id', $entry->id)->first();
	        if(!$checkAlreadyExist  &&isset($changes['is_maximo_qualification']) && $changes['is_maximo_qualification']!=1){
	            return;
	        }

	        if($checkAlreadyExist && !$checkAlreadyExist->maximo_created  && isset($changes['is_maximo_qualification']) && $changes['is_maximo_qualification']!=1){
	            return;
	        }

		if (
			isset($changes['name']) ||
			isset($changes['description']) ||
			isset($changes['refresh_period']) ||
			isset($changes['status']) ||
			isset($changes['print_certificate']) ||
			isset($changes['is_maximo_qualification'])
		) {
	            $entry->maximo_api_attempts = 0;
	            $entry->maximo_retry_time = NULL;
		    $entry->cron_task = true;
		}
	}

	protected static function boot() {
		parent::boot();

		// Filter out learning modules with disabled resource types
		static::addGlobalScope('disabled_resource_types', function (\Illuminate\Database\Eloquent\Builder $builder) {
			// If no type_id, show regardless. Otherwise check if type is enabled
			$builder->where(function ($query) {
				$query->whereNull('learning_modules.type_id')
					  ->orWhereHas('Type', function ($subQuery) {
						  $subQuery->where('status', 1);
					  });
			});
		});

		static::creating(function($entry) {
			if(Auth::getUser()){
				if(\APP\Tools::getConfig('sharedClients') && Auth::isManagerInterface()){
					$user = Auth::getUser();
					if ($user->role->is_admin == 0 && $user->role->access_all_companies == 0 && $user->company_id) {
						$entry->company_id = $user->company_id;
					}
				}

			}
			self::updateCronStatus($entry->getDirty(), $entry);
		});

		static::updating(function($entry) {
			$old_entry = \Models\LearningModule::find($entry->id);
			$changes = $entry->getDirty();
			$date_fields = [
				'expiration_date',
				'module_date',
				'updated_at',
			];
			\Models\TableHistory::trackChanges($old_entry, $changes, $date_fields, 'learning_modules', $entry->id);
			static::updateCronStatus($changes, $entry);
		});

		static::saved(function ($learning_module) {
			if ($learning_module->is_course) {
				cache()->forget('course_all');
				cache()->forget('event_course_all');
			} else {
				cache()->forget('learning_module_all');
			}
			
			// Clear skill scan cache if is_skillscan or status changed
			if ($learning_module->isDirty(['is_skillscan', 'status'])) {
				$cacheKey = \APP\Cache\CacheHelper::key('learning_modules', 'has_skillscan');
				cache()->forget($cacheKey);
			}
			
			// Clear valid resources cache if status or expiration_date changed
			if ($learning_module->isDirty(['status', 'expiration_date'])) {
				self::invalidateValidResourcesCache();
			}
		});

		static::saving(function($learning_module) {
			// Allways set this to true for lesson types!
			$learning_module->updated_by = \APP\Auth::getUserId();
			$changes = $learning_module->getDirty();
			self::updateCronStatus($changes,$learning_module);
		});
		
		static::deleted(function ($learning_module) {
			// Clear skill scan cache if a skill scan module was deleted
			if ($learning_module->is_skillscan) {
				$cacheKey = \APP\Cache\CacheHelper::key('learning_modules', 'has_skillscan');
				cache()->forget($cacheKey);
			}
			
			// Always clear valid resources cache when a module is deleted
			self::invalidateValidResourcesCache();
		});
	}

	public function CredlyBadges(){
		return $this->hasMany(LearningModuleCredlyBadge::class);
	}

	public function cmiPrefix() {
		$scormStandard = isset($this->material->scorm_standard) ? $this->material->scorm_standard : 2;
		return $scormStandard < 3 ? 'cmi.core.' : 'cmi.';
	}

	public function cmiCompletionStatus() {
		$scormStandard = isset($this->material->scorm_standard) ? $this->material->scorm_standard : 2;
		return $this->cmiPrefix() . ($scormStandard < 3 ? 'lesson_status' : 'completion_status');
	}
	public function linked(){
	return $this->hasMany(LinkedLearningModule::class,'learning_module_id','id')
	    ->join('learning_modules as lr',function($join){
	        $join->on('lr.id','=','linked_learning_modules.linked_learning_module_id');
	    })->select('lr.name','lr.id','linked_learning_modules.assign','linked_learning_modules.days','learning_module_id');
	}

    public function isLinked(){
        return $this->hasOne('Models\LinkedLearningModule','linked_learning_module_id','id')->whereHas('LearningResult');
    }

    public function linkedMainModules()
    {
        return $this->hasMany('Models\LinkedLearningModule','linked_learning_module_id','id')->whereHas('LearningResult');
    }

    public function getIsLockedAttribute()
    {
        $isLocked = false;
        $today = \Carbon\Carbon::now();

        foreach ($this->linked as $linkedModule){
            if ($linkedModule->assign == "mandatory precursor"){
                $isLocked = true;
                $learningResult = LearningResult::where('learning_module_id', $linkedModule->id)->first();
                if ($learningResult && $learningResult->completed_at) {
                    $isLocked = false;
                }
            }
        }
        // if already has prerequisite, then dont check other conditions
        if ($isLocked) {
            return true;
        }

        foreach ($this->linkedMainModules as $linkedModule) {
            $isLocked = true;
            if ($linkedModule->assign == "immediately" || $linkedModule->assign == "mandatory precursor"){
                $isLocked = false;
                continue;
            }
            if ($linkedModule->LearningResult->completed_at) {
                switch ($linkedModule->assign) {
                    case 'post_completion':
                        $isLocked = false;
                        break;
                    case 'post_completion_after':
                        $completedAt = Carbon::parse(optional($linkedModule->LearningResult)->completed_at);
                        if ($completedAt) {
                            $daysDifference = $completedAt->diffInDays($today, false);
                            if ($daysDifference >= $linkedModule->days) {
                                $isLocked = false;
                            }
                        }
                        break;
                }
            }
            if ($isLocked) {
                break;
            }
        }

        return $isLocked;
    }


	public static function getCourseCreditCost($learning_module)
	{
		$catalogue_course = ["1 Introduction to StepChange Debt Charity", "2 StepChange About Debt", "3 Stepchange Referring a Tenant", "4 Case Studies Step Change", "Absence Management for Managers_ Strategies for Effectively Managing Employee Attendance", "The Psychology Behind Anti-Social Behaviour", "Adult and child obesity", "Advanced Data Protection Mastering Legislation and Best Practices", "Alcohol and Drug Awareness", "Alcohol Awareness for Managers", "Allergy Management and Anaphylaxis Identifying and Responding to Allergic Reactions", "AML and Anti-Terrorism for UK Gambling", "AML and CTF Compliance Mastery", "Amphetamine Awareness for Managers", "An Introduction to Section 20", "Anti Money Laundering Introduction", "Anti Money Laundering Regulated Activities", "Anti Social Behaviour for Community Safety Officers", "Anti Social Behaviour for General Employees", "Anti-Bribery and Corruption", "Anti-Money Laundering in Practice", "Applying for Universal Credit", "Armed Forces Covenant", "Asbestos Awareness", "Asbestos Awareness for Social Housing", "Asking Better Questions", "Assistive Technology", "Avoiding Slips Trips and Falls at Home", "Banking and Financial Risks", "Basics of Managing Rental Property", "Bespoke Business Documents", "Bid Writing", "Black Lives Matter", "Black Lives Matter for Business", "Bribery Act", "Bribery Act for Social Housing", "Business Continuity and BCP Essentials", "Business Continuity and BIA Essentials", "Business Continuity and Governance", "Business Continuity and Human Factor", "Business Continuity and IT Disaster Recovery", "Business Continuity and Risk Management Essentials", "Business Continuity Maintenance, Monitoring and Assurance", "Business Continuity Testing Essentials", "Business Innovation and Growth", "Cannabis Awareness for Managers", "Care Act", "Care Act for Frontline Staff A Down-to-Earth Understanding", "Care Certificate", "CDM Contractor Management", "CDM for Client and Managing Agents", "Chair Lead and Manage Meetings", "Clear and Compelling Presentations", "Climate Change", "Cocaine Awareness for Managers", "Coding in the Digital Marketplace", "Combating Hate Crime_ Promoting Understanding and Inclusivity", "Complaints Handling", "Comprehensive Understanding of Child Safeguarding_ Principles and Practices", "Confined Space Awareness", "Conflict of Interest Definition Examples and How to Deal With It", "Coronavirus and Working from Home", "Coronavirus and Working in the Hotel Sector", "Coronavirus and Working in the Office Environment", "Coronavirus and Working in the Restaurant Sector", "Coronavirus and Working in the Retail Sector", "Coronavirus Awareness", "COSHH", "Creating a Great Home-Working Environment", "Creating a Respectful Workplace Fostering Dignity and Inclusion", "Credit Brokerage Accreditation", "CRM an Introduction", "Customer Service for Housing Associations", "Customer Service General", "Customer Service Techniques", "Cyber Awareness and Staying Safe Online", "Data Protection", "Dealing with Grievance Issues", "Dealing with Performance Issues", "Decoding Balance Sheets", "Delivering a Media Pitch", "Dementia Awareness", "Dementia Awareness for Carers", "Dementia Awareness for Senior Staff", "Demystifying Double Entry Bookkeeping", "Deprivation of Liberty Safeguards", "Difficult Conversations", "Digital and Social Media Strategies", "Digital Etiquette and Citizenship", "Dignity at Work", "Disability Awareness in the Workplace Promoting Accessibility and Inclusion", "Driver Safety", "Drugs and Young People", "Early Recognition and Management of Sepsis Identifying and Responding to Critical Infections", "Ecstasy Awareness for Managers", "Effective Absence Management_ Strategies for Ensuring Workforce Attendance", "Effective Communication", "Effective Medicines Management Ensuring Safety and Accuracy", "Emotional Intelligence", "Employee Monitoring in the Workplace_ Balancing Privacy and Security", "e-Office Safety Mobile Pro", "e-Office Safety Pro", "e-Office Safety Pro No Assessment", "Epilepsy", "Equality and Diversity", "Equality and Diversity Foundation", "Essentials of Medical Devices Introduction and Safety Considerations", "e-Start", "Estate Management", "Ethical Decision-Making Navigating Business Ethics with Wisdom", "Ethical Values and Code of Conduct", "Evidence Gathering and Crime Scene Management", "Exploring Equality, Diversity, and Inclusion (EDI)_ Unpacking the Nine Protected Characteristics", "FGM and honour based violence", "File Naming Conventions", "Financial Management and Budgeting", "Fire and Electrical Safety in the Home", "Fire Marshal Training", "Fire Safety", "First Aid", "Food Safety - An Introduction", "Food Safety - Chilling", "Food Safety - Cleaning", "Food Safety - Cooking", "Food Safety - Cross Contamination", "Food Safety - Final Quiz", "Fraud Awareness Understanding, Reporting and Prevention", "Frontline Support for Individuals with Dementia_ Enhancing Care and Understanding", "Functional Skills Maths 2D Shapes", "Functional Skills Maths 3D Shapes", "Functional Skills Maths Charts", "Functional Skills Maths Data", "Functional Skills Maths Decimals", "Functional Skills Maths Fractions", "Functional Skills Maths Probability", "Functional Skills Maths Ratios", "Functional Skills Maths Scale", "GDPR Awareness", "GDPR Customer Service", "General Security", "Guest Complaints", "Guest Name Usage", "HACCP", "Hand-Arm Vibration at Work", "Handling a Difficult Conversation in a Care Setting", "Harnessing Intellectual Property (IP) Maximizing the Benefits for Your Business", "Health and Safety for Managers", "Health and Safety Induction", "Heroin Awareness for Managers", "Home and Mobile Working (no assessment)", "Home Ownership", "Home Working Risk Assessment A Comprehensive Guide", "Housing Associations", "How to manage an Office Facility", "How to Write an Effective CV", "Human Rights", "Implementing the GDPR", "Importance of Equality, Diversity, and Inclusion_ Fostering a Culture of Belonging", "Importance of Science and Technology in Business", "Increasing Awareness Learning Disability and Autism in Healthcare Settings", "Induction for Social Housing", "Infection Prevention and Control for Everyone", "Infection Prevention and Control in Business", "Infection Prevention and Control in Healthcare", "Information Systems", "Intellectual Property (IP) Basics_ Safeguarding and Utilizing Intellectual Assets", "Internal Training Coach Trainer", "Internal Training Curriculum Developer", "Internal Training Distributor", "Internal Training Financial Auditor", "Internal Training Quality Assurer", "Internal Training Super Admin", "Introduction to CDM", "Introduction to Fundraising", "Introduction to Governance", "Introduction to Governance for SHPs", "Introduction to Local Government", "Introduction to Marketing", "Introduction to Medication Awareness", "Introduction to Professional Boundaries for Care", "Introduction to Project Management", "Introduction to Regulatory Framework", "Introduction to the Care Act 2014", "Introduction to the Financial Conduct Rules", "Introduction to the Mental Capacity Act", "Introduction to Whistleblowing", "ISO 14001 Environmental Management Systems", "ISO 27001 Essentials for Local Government", "ISO 27001 Information Security Management Systems", "ISO 50001 Energy Management Systems", "ISO 9001 Essentials for Local Government", "ISO 9001 Quality Management Systems", "ISO Advanced", "ISO Beginners", "IT Security", "Laboratory Safety and Ergonomics", "Law and Journalism", "Learning Techniques", "Legal High Awareness for Managers", "Legal Issues Concerning Media Assets", "Legionella Awareness", "LGBT Awareness", "Life story work", "Life Transitions", "Lone Working", "Lone Working Safety for Home and Remote Workers", "Love Food Hate Waste", "Managing Attendance", "Managing Conflict and Resilience In Action", "Managing Conflict and Resilience Introduction", "Managing Disciplinary Issues", "Managing Information Securely", "Managing Medicines", "Managing Performance", "Managing Stress, Isolation and Self-Motion for Home-Workers", "Managing your Digital Footprint", "Manual Handling", "Manual Handling of People in Care Settings", "Mastering Credit Management", "Mastering Time Management", "Mastering Time Management with Andrew Huberman", "Medication Awareness in Healthcare Best Practices and Safety Guidelines", "Mental Capacity Act", "Mental Health Awareness at Workplace", "Mental Health Refresher", "Mental Wellbeing and Mindfulness", "Microaggressions in the Workplace", "Microsoft Excel in 30 Minutes", "Microsoft Power Point in 15 Minutes", "Microsoft Word in 15 Minutes", "Modern Slavery", "Money Laundering", "Money Management and Responsibilities", "Navigating Diverse Working Cultures Fostering Inclusiveness and Thriving Teams", "Negotiating Skills Quiz", "Note Taking at Interviews", "Nutrition Awareness", "Open eLMS AI Editor Session 1 Understanding what makes great e-learning", "Open eLMS AI Editor Session 2 Scripting and the information design of an e-learning project", "Open eLMS AI Editor Session 3 Search and edit images to the correct size", "Open eLMS AI Editor Session 4 Start to Create an e-Learning Session", "Open eLMS AI Editor Session 5 Create buttons and interactivity in e-learning", "Open eLMS AI Editor Session 6 Process videos using Adobe Media Encoder", "Open eLMS AI Editor Session 7 Exporting the e-Learning", "Opportunity Cost Demystified", "Organisational Structures", "Organising Events", "Patient Chaperone Policy Ensuring Privacy and Comfort in Medical Settings", "People Management", "Power BI Introduction", "Power BI Searching Sorting Filtering", "Power BI Visualisations", "Preparing a Media Proposal", "Preparing a Media Proposal Workbook", "Preparing for a Job Interview", "Presenting Skills Final Quiz", "Prevent Strategy", "Privacy in Practice - An in-depth education of Privacy & Confidentiality", "Problem Solving in Business", "Promoting British Values_ Embedding Principles of Democracy, Rule of Law, and Respect", "Protecting Children_ Building Awareness on Child Sexual Exploitation", "Psychologically Informed Environments", "Public Relations Tools and Activities", "Recognising and Reporting Abuse (Care)", "Reformation in the Criminal Justice System", "Risk Assessment", "Safe Handling of Controlled Drugs Protocols and Procedures", "Safe Handling of Medical Gases Procedures and Precautions", "Safe Working from Home and Office", "Safeguarding Adults", "Safeguarding Adults at Risk_ Compliance and Best Practices in Scotland", "Safeguarding Adults at Risk_ Ensuring Protection in Wales", "Safeguarding Children", "Safeguarding Children and Young People in Scotland_ Promoting Safety and Well-being", "Safeguarding Children and Young People in Wales_ Ensuring Protection and Support", "Risk Assessment and Management", "Safety for Lone Workers (for Care)", "Scriptwriting for e-Learning", "Search Engine Optimisation", "Section 20 Intro for Leaseholder Staff", "Selling Alcohol Legally and Responsibly", "Senior Managers Guide to Equality and Diversity_ Driving Inclusion at the Organizational Level", "Sharps Awareness", "Sharps Awareness and Safety in Medical Environments Handling and Disposal", "Social Media and Journalism", "Social Media Usage", "Start Using Power BI", "Storyboarding for Media Creation", "Stress", "Substance Abuse Awareness Understanding Drug and Alcohol Impact in the Workplace", "Supporting New and Expectant Mothers_ Ensuring Workplace Well-being and Compliance", "Taking the mystery out of Section 20", "Team Supervision", "Telephone Courtesy", "Tenancy Management", "The 7 Cs of Effective Communication", "The Art of Active Listening", "Thinking on Your Feet Techniques for Quick and Impactful Responses", "Time Management", "Treating Customers Fairly - Finance", "Unconscious Bias", "Understanding Business Markets", "Understanding Domestic Abuse_ Supporting Victims and Creating a Safe Environment", "Understanding Duty of Care", "Understanding Financial Controls", "Understanding Gifts and Hospitality Regulations", "Understanding Informed Consent in Medical Practice Empowering Patients with Information", "Understanding ISO 22301 BCMS Essentials", "Understanding Person-Centred Care", "Understanding RIDDOR Your Guide to Injury Reports", "Understanding the Adult Support and Protection (Scotland) Act", "Understanding the Adults with Incapacity (Scotland) Act 2000", "Understanding the Connections between Organisational Structure, Strategy, and the Business Operating Environment", "Understanding the Payment Card Industry Data Security Standard", "Understanding the PREVENT Strategy_ Identifying and Preventing Radicalization", "Universal Credit - Children and Childcare", "Universal Credit Health Conditions and Disabilities", "Universal Credit Housing", "Universal Credit How Do I Claim?", "Universal Credit Self Employment", "Universal Credit Working", "Unveiling the Profit and Loss Account Understanding Financial Performance", "Using Social Media Responsibly", "Value for Money in Social Housing", "Volatile Substances Awareness for Managers", "Welfare Reform Landlords", "Welfare Reform Tenants", "What is Universal Credit", "WIIFM Getting People to Listen with Impact and Relevance", "Working at Heights", "Working Safely with Steps and Ladders", "Writing a Good Story", "Writing a Proposal", "Writing an Effective Press Release", "Written Communication", "Zeiteinteilung", "Unbewusste Voreingenommenheit", "Strategien für digitale und soziale Medien", "Manuelle Handhabung", "Einführung in die Bekämpfung der Geldwäsche für Wohnungsbaugesellschaften", "Gesundheit und Sicherheit für Manager", "Coronavirus und Arbeiten von zu Hause aus", "ISO Anfänger", "ISO 27001 Informationssicherheits-Managementsysteme", "ISO 9001 Qualitätsmanagementsysteme", "Gestión del tiempo", "Sesgo inconsciente", "Estrategias de redes sociales y digitales", "Manejo manual", "Introducción contra el blanqueo de capitales para asociaciones de vivienda", "Santé et sécurité pour les gestionnaires", "Coronavirus y trabajar desde casa", "Principiantes ISO", "Sistemas de gestión de seguridad de la información ISO 27001", "Sistemas de gestión de calidad ISO 9001", "Gestion du temps", "Biais inconscient", "Stratégies de médias numériques et sociaux", "Manipulation manuelle", "Introduction à la lutte contre le blanchiment d'argent pour les associations de logement", "Santé et sécurité pour les gestionnaires", "Coronavirus et travail à domicile", "Débutants ISO", "Systèmes de gestion de la sécurité de l'information ISO 27001", "Systèmes de gestion de la qualité ISO 9001", "Gestione del tempo", "Bias inconscio", "Strategie digitali e social media", "Movimentazione manuale", "Introduzione contro il riciclaggio di denaro per le associazioni abitative", "Salute e sicurezza per i dirigenti", "Coronavirus e lavoro da casa", "Principianti ISO", "Sistemi di gestione della sicurezza delle informazioni ISO 27001", "Sistemi di gestione della qualità ISO 9001", "أدوات وأنشطة العلاقات العامة", "استراتيجيات الوسائط الرقمية والاجتماعية", "تنظيم الأحداث", "كتابة بيان صحفي فعال", "وسائل التواصل الاجتماعي والصحافة"];
		//search $learning_module->name is included above array
		if (in_array($learning_module->name, $catalogue_course)) {
			return [
				'is_credit' => true,
				'credit_cost' => 1
			];
		} else {
			return [
				'is_credit' => false,
				'credit_cost' => 0
			];
		}
	}

    public static $catelog = [
        "1 Introduction to StepChange Debt Charity",
        "2 StepChange About Debt",
        "3 Stepchange Referring a Tenant",
        "4 Case Studies Step Change",
        "Absence Management for Managers_ Strategies for Effectively Managing Employee Attendance",
        "The Psychology Behind Anti-Social Behaviour",
        "Addressing Diverse Needs in Housing: Promoting Inclusivity and Accessibility",
        "Adult and child obesity",
        "Advanced Data Protection Mastering Legislation and Best Practices",
        "Agile Project Management Essentials",
        "Alcohol and Drug Awareness",
        "Alcohol Awareness for Managers",
        "Allergy Management and Anaphylaxis Identifying and Responding to Allergic Reactions",
        "AML and Anti-Terrorism for UK Gambling",
        "AML and CTF Compliance Mastery",
        "Amphetamine Awareness for Managers",
        "Amplifying Your Influence",
        "An Introduction to Section 20",
        "Anti Money Laundering Introduction",
        "Anti Money Laundering Regulated Activities",
        "Anti Social Behaviour for Community Safety Officers",
        "Anti Social Behaviour for General Employees",
        "Anti-Bribery and Corruption",
        "Anti-Money Laundering in Practice",
        "Applying for Universal Credit",
        "Armed Forces Covenant",
        "Asbestos Awareness",
        "Asbestos Awareness for Social Housing",
        "Asking Better Questions",
        "Assistive Technology",
        "Avoiding Slips Trips and Falls at Home",
        "Banking and Financial Risks",
        "Basics of Managing Rental Property",
        "Bespoke Business Documents",
        "Bid Writing",
        "Bikeability Road Positions",
        "Black Lives Matter",
        "Black Lives Matter for Business",
        "Breast and Prostate Cancer Awareness",
        "Bribery Act",
        "Bribery Act for Social Housing",
        "Building Authentic Rapport",
        "Business Continuity and BCP Essentials",
        "Business Continuity and BIA Essentials",
        "Business Continuity and Governance",
        "Business Continuity and Human Factor",
        "Business Continuity and IT Disaster Recovery",
        "Business Continuity and Risk Management Essentials",
        "Business Continuity Maintenance, Monitoring and Assurance",
        "Business Continuity Testing Essentials",
        "Business Innovation and Growth",
        "Cannabis Awareness for Managers",
        "Care Act",
        "Care Act for Frontline Staff A Down-to-Earth Understanding",
        "Care Certificate",
        "CDM Contractor Management",
        "CDM for Client and Managing Agents",
        "Chair Lead and Manage Meetings",
        "Clear and Compelling Presentations",
        "Climate Change",
        "Cocaine Awareness for Managers",
        "Coding in the Digital Marketplace",
        "Combating Hate Crime_ Promoting Understanding and Inclusivity",
        "Complaints Handling",
        "Comprehensive Understanding of Child Safeguarding_ Principles and Practices",
        "Confined Space Awareness",
        "Conflict of Interest Definition Examples and How to Deal With It",
        "Conflict Resolution Anger and Difficult People",
        "Coronavirus and Working from Home",
        "Coronavirus and Working in the Hotel Sector",
        "Coronavirus and Working in the Office Environment",
        "Coronavirus and Working in the Restaurant Sector",
        "Coronavirus and Working in the Retail Sector",
        "Coronavirus Awareness",
        "COSHH",
        "Crafting a Comprehensive Balance Sheet Navigating Assets, Liabilities, and Equity",
        "Creating a Great Home-Working Environment",
        "Creating a Respectful Workplace Fostering Dignity and Inclusion",
        "Credit Brokerage Accreditation",
        "CRM an Introduction",
        "Cultivating a Positive Customer Service Attitude",
        "Customer Service for Housing Associations",
        "Customer Service General",
        "Customer Service Language Mastery",
        "Customer Retention Mastery",
        "Customer Service Techniques",
        "Cyber Awareness and Staying Safe Online",
        "Cyber Security Fundamentals",
        "Data Analysis Essentials",
        "Database Management Essentials",
        "Data Protection",
        "Dealing with Grievance Issues",
        "Dealing with Performance Issues",
        "Decoding Balance Sheets",
        "Delivering Exceptional Customer Service: Going Above and Beyond",
        "Delivering a Media Pitch",
        "Dementia Awareness",
        "Dementia Awareness for Carers",
        "Dementia Awareness for Senior Staff",
        "Demystifying Double Entry Bookkeeping",
        "Deprivation of Liberty Safeguards",
        "Difficult Conversations",
        "Digital and Social Media Strategies",
        "Digital Etiquette and Citizenship",
        "Dignity at Work",
        "Disability Awareness in the Workplace Promoting Accessibility and Inclusion",
        "Driver Safety",
        "Drugs and Young People",
        "Early Recognition and Management of Sepsis Identifying and Responding to Critical Infections",
        "Ecstasy Awareness for Managers",
        "Effective Absence Management_ Strategies for Ensuring Workforce Attendance",
        "Effective Communication",
        "Effective Management in Housing Associations Strategies for Success",
        "Effective Medicines Management Ensuring Safety and Accuracy",
        "Emotional Intelligence",
        "Emotional Intelligence Essentials",
        "Employee Monitoring in the Workplace_ Balancing Privacy and Security",
        "e-Office Safety Mobile Pro",
        "e-Office Safety Pro",
        "e-Office Safety Pro No Assessment",
        "Epilepsy",
        "Equality and Diversity",
        "Equality and Diversity Foundation",
        "Essentials of Medical Devices Introduction and Safety Considerations",
        "e-Start",
        "Estate Management",
        "Ethical Decision-Making Navigating Business Ethics with Wisdom",
        "Ethical Values and Code of Conduct",
        "Evidence Gathering and Crime Scene Management",
        "Excelling in Customer Service",
        "Exploring Equality, Diversity, and Inclusion (EDI)_ Unpacking the Nine Protected Characteristics",
        "FGM and honour based violence",
        "File Naming Conventions",
        "Financial Management and Budgeting",
        "Fire and Electrical Safety in the Home",
        "Fire Marshal Training",
        "Fire Safety",
        "First Aid",
        "Food Safety - An Introduction",
        "Food Safety - Chilling",
        "Food Safety - Cleaning",
        "Food Safety - Cooking",
        "Food Safety - Cross Contamination",
        "Food Safety - Final Quiz",
        "Fraud Awareness Understanding, Reporting and Prevention",
        "Frontline Support for Individuals with Dementia_ Enhancing Care and Understanding",
        "Functional Skills Maths 2D Shapes",
        "Functional Skills Maths 3D Shapes",
        "Functional Skills Maths Charts",
        "Functional Skills Maths Data",
        "Functional Skills Maths Decimals",
        "Functional Skills Maths Fractions",
        "Functional Skills Maths Probability",
        "Functional Skills Maths Ratios",
        "Functional Skills Maths Scale",
        "GDPR Awareness",
        "GDPR Customer Service",
        "General Security",
        "Google Docs Interactive Tutorial",
        "Guest Complaints",
        "Guest Name Usage",
        "HACCP",
        "Hand-Arm Vibration at Work",
        "Handling Angry Callers Effectively",
        "Handling a Difficult Conversation in a Care Setting",
        "Harnessing Intellectual Property (IP) Maximizing the Benefits for Your Business",
        "Harnessing IP: Business Benefits",
        "Health and Safety for Managers",
        "Health and Safety Induction",
        "Heroin Awareness for Managers",
        "Home and Mobile Working (no assessment)",
        "Home Ownership",
        "Home Working Risk Assessment A Comprehensive Guide",
        "Housing Associations",
        "How to manage an Office Facility",
        "How to Write an Effective CV",
        "Human Rights",
        "Implementing the GDPR",
        "Importance of Equality, Diversity, and Inclusion_ Fostering a Culture of Belonging",
        "Importance of Science and Technology in Business",
        "Increasing Awareness Learning Disability and Autism in Healthcare Settings",
        "Induction for Social Housing",
        "Infection Prevention and Control for Everyone",
        "Infection Prevention and Control in Business",
        "Infection Prevention and Control in Healthcare",
        "Information Systems",
        "Inspiring Action",
        "Intellectual Property (IP) Basics_ Safeguarding and Utilizing Intellectual Assets",
        "Internal Training Coach Trainer",
        "Internal Training Curriculum Developer",
        "Internal Training Distributor",
        "Internal Training Financial Auditor",
        "Internal Training Quality Assurer",
        "Internal Training Super Admin",
        "Introduction to CDM",
        "Introduction to Data Science",
        "Introduction to Fundraising",
        "Introduction to Governance",
        "Introduction to Governance for SHPs",
        "Introduction to Local Government",
        "Introduction to Marketing",
        "Introduction to Medication Awareness",
        "Introduction to Professional Boundaries for Care",
        "Introduction to Project Management",
        "Introduction to Regulatory Framework",
        "Introduction to the Care Act 2014",
        "Introduction to the Financial Conduct Rules",
        "Introduction to the Mental Capacity Act",
        "Introduction to Whistleblowing",
        "ISO 14001 Environmental Management Systems",
        "ISO 27001 Essentials for Local Government",
        "ISO 27001 Information Security Management Systems",
        "ISO 45001 Awareness",
        "ISO 50001 Energy Management Systems",
        "ISO 9001 Essentials for Local Government",
        "ISO 9001 Quality Management Systems",
        "ISO Advanced",
        "ISO Beginners",
        "IT Help Desk Mastery for Technical Specialists ",
        "IT Security",
        "Laboratory Safety and Ergonomics",
        "Law and Journalism",
        "Learning Techniques",
        "Legal High Awareness for Managers",
        "Legal Issues Concerning Media Assets",
        "Legionella Awareness",
        "LGBT Awareness",
        "Life story work",
        "Life Transitions",
        "Lone Working",
        "Lone Working Safety for Home and Remote Workers",
        "Love Food Hate Waste",
        "Managing Attendance",
        "Managing Conflict and Resilience In Action",
        "Managing Conflict and Resilience Introduction",
        "Managing Disciplinary Issues",
        "Managing Information Securely",
        "Managing Medicines",
        "Managing Performance",
        "Managing Stress, Isolation and Self-Motion for Home-Workers",
        "Managing your Digital Footprint",
        "Manual Handling",
        "Manual Handling of People in Care Settings",
        "Mastering Credit Management",
        "Mastering Google Sheets ",
        "Mastering the LAER Method Effective Communication for Influence and Impact",
        "Mastering Telephone Customer Service",
        "Mastering Time Management",
        "Mastering Time Management with Andrew Huberman",
        "Medication Awareness in Healthcare Best Practices and Safety Guidelines",
        "Mental Capacity Act",
        "Mental Health Awareness at Workplace",
        "Mental Health Refresher",
        "Mental Wellbeing and Mindfulness",
        "Microaggressions in the Workplace",
        "Microsoft Excel in 30 Minutes",
        "Microsoft Power Point in 15 Minutes",
        "Microsoft Word in 15 Minutes",
        "Modern Slavery",
        "Money Laundering",
        "Money Management and Responsibilities",
        "Navigating Diverse Working Cultures Fostering Inclusiveness and Thriving Teams",
        "Negotiating Skills Quiz",
        "Net Present Value Simplified",
        "Network Security ISO 27001 and 27033 Essentials",
        "Network and System Administration Essentials",
        "Negotiating Skills Quiz",
        "Nutrition Awareness",
        "Open eLMS AI Editor Session 1 Understanding what makes great e-learning",
        "Open eLMS AI Editor Session 2 Scripting and the information design of an e-learning project",
        "Open eLMS AI Editor Session 3 Search and edit images to the correct size",
        "Open eLMS AI Editor Session 4 Start to Create an e-Learning Session",
        "Open eLMS AI Editor Session 5 Create buttons and interactivity in e-learning",
        "Open eLMS AI Editor Session 6 Process videos using Adobe Media Encoder",
        "Open eLMS AI Editor Session 7 Exporting the e-Learning",
        "Opportunity Cost Demystified",
        "Organisational Structures",
        "Organising Events",
        "Patient Chaperone Policy Ensuring Privacy and Comfort in Medical Settings",
        "People Management",
        "Power BI Introduction",
        "Power BI Searching Sorting Filtering",
        "Power BI Visualisations",
        "Power vs. Influence",
        "Preparing a Media Proposal",
        "Preparing a Media Proposal Workbook",
        "Preparing for a Job Interview",
        "Presenting Skills Final Quiz",
        "Prevent Strategy",
        "Privacy in Practice - An in-depth education of Privacy & Confidentiality",
        "Problem Solving in Business",
        "Promoting British Values_ Embedding Principles of Democracy, Rule of Law, and Respect",
        "Protecting Children_ Building Awareness on Child Sexual Exploitation",
        "Psychologically Informed Environments",
        "Public Relations Tools and Activities",
        "Recognising and Reporting Abuse (Care)",
        "Reformation in the Criminal Justice System",
        "Risk Assessment",
        "Safe Handling of Controlled Drugs Protocols and Procedures",
        "Safe Handling of Medical Gases Procedures and Precautions",
        "Safe Working from Home and Office",
        "Safeguarding Adults",
        "Safeguarding Adults at Risk_ Compliance and Best Practices in Scotland",
        "Safeguarding Adults at Risk_ Ensuring Protection in Wales",
        "Safeguarding Children",
        "Safeguarding Children and Young People in Scotland_ Promoting Safety and Well-being",
        "Safeguarding Children and Young People in Wales_ Ensuring Protection and Support",
        "Risk Assessment and Management",
        "Safety for Lone Workers (for Care)",
        "Scriptwriting for e-Learning",
        "Search Engine Optimisation",
        "Secrets of Persuasion",
        "Section 20 Intro for Leaseholder Staff",
        "Selling Alcohol Legally and Responsibly",
        "Senior Managers Guide to Equality and Diversity_ Driving Inclusion at the Organizational Level",
        "Sharps Awareness",
        "Sharps Awareness and Safety in Medical Environments Handling and Disposal",
        "Social Media and Journalism",
        "Social Media Usage",
        "Start Using Power BI",
        "Storyboarding for Media Creation",
        "Stress",
        "Substance Abuse Awareness Understanding Drug and Alcohol Impact in the Workplace",
        "Supporting New and Expectant Mothers_ Ensuring Workplace Well-being and Compliance",
        "Taking the mystery out of Section 20",
        "Taxi Safety and Regulations",
        "Team Supervision",
        "Telephone Courtesy",
        "Tenancy Management",
        "The 7 Cs of Effective Communication",
        "The Art of Active Listening",
        "The Influencing Star Mastering the Art of Persuasion and Influence",
        "The Power of Empathy",
        "The Price-Earnings Ratio Unveiled",
        "Thinking on Your Feet Techniques for Quick and Impactful Responses",
        "Time Management",
        "Transforming Disgruntled Customers: Effective Complaint Handling for Loyalty",
        "Treating Customers Fairly - Finance",
        "Unconscious Bias",
        "Understanding Business Markets",
        "Understanding DevOps",
        "Understanding Domestic Abuse_ Supporting Victims and Creating a Safe Environment",
        "Understanding Duty of Care",
        "Understanding Financial Controls",
        "Understanding Gifts and Hospitality Regulations",
        "Understanding Informed Consent in Medical Practice Empowering Patients with Information",
        "Understanding ISO 22301 BCMS Essentials",
        "Understanding Person-Centred Care",
        "Understanding RIDDOR Your Guide to Injury Reports",
        "Understanding Software Development",
        "Understanding the Adult Support and Protection (Scotland) Act",
        "Understanding the Adults with Incapacity (Scotland) Act 2000",
        "Understanding the Connections between Organisational Structure, Strategy, and the Business Operating Environment",
        "Understanding the Payment Card Industry Data Security Standard",
        "Understanding the PREVENT Strategy_ Identifying and Preventing Radicalization",
        "Universal Credit - Children and Childcare",
        "Universal Credit Health Conditions and Disabilities",
        "Universal Credit Housing",
        "Universal Credit How Do I Claim?",
        "Universal Credit Self Employment",
        "Universal Credit Working",
        "Unleashing Your Personal Power",
        "Unlocking the Power of Influence",
        "Unveiling the Profit and Loss Account Understanding Financial Performance",
        "Using Social Media Responsibly",
        "Value for Money in Social Housing",
        "Voice of Success",
        "Volatile Substances Awareness for Managers",
        "Welfare Reform Landlords",
        "Welfare Reform Tenants",
        "What is Universal Credit",
        "WIIFM Getting People to Listen with Impact and Relevance",
        "Working at Heights",
        "Working Safely with Steps and Ladders",
        "Writing a Good Story",
        "Writing a Proposal",
        "Writing an Effective Press Release",
        "Written Communication",
        // "GERMAN",
        "Zeiteinteilung",
        "Unbewusste Voreingenommenheit",
        "Strategien für digitale und soziale Medien",
        "Manuelle Handhabung",
        "Einführung in die Bekämpfung der Geldwäsche für Wohnungsbaugesellschaften",
        "Gesundheit und Sicherheit für Manager",
        "Coronavirus und Arbeiten von zu Hause aus",
        "ISO Anfänger",
        "ISO 27001 Informationssicherheits-Managementsysteme",
        "ISO 9001 Qualitätsmanagementsysteme",
        // "SPANISH",
        "Gestión del tiempo",
        "Sesgo inconsciente",
        "Estrategias de redes sociales y digitales",
        "Manejo manual",
        "Introducción contra el blanqueo de capitales para asociaciones de vivienda",
        "Santé et sécurité pour les gestionnaires",
        "Coronavirus y trabajar desde casa",
        "Principiantes ISO",
        "Sistemas de gestión de seguridad de la información ISO 27001",
        "Sistemas de gestión de calidad ISO 9001",
        // "FRENCH",
        "Gestion du temps",
        "Biais inconscient",
        "Stratégies de médias numériques et sociaux",
        "Manipulation manuelle",
        "Introduction à la lutte contre le blanchiment d'argent pour les associations de logement",
        "Santé et sécurité pour les gestionnaires",
        "Coronavirus et travail à domicile",
        "Débutants ISO",
        "Systèmes de gestion de la sécurité de l'information ISO 27001",
        "Systèmes de gestion de la qualité ISO 9001",
        // "ITALIAN",
        "Gestione del tempo",
        "Bias inconscio",
        "Strategie digitali e social media",
        "Movimentazione manuale",
        "Introduzione contro il riciclaggio di denaro per le associazioni abitative",
        "Salute e sicurezza per i dirigenti",
        "Coronavirus e lavoro da casa",
        "Principianti ISO",
        "Sistemi di gestione della sicurezza delle informazioni ISO 27001",
        "Sistemi di gestione della qualità ISO 9001",
        // "ARABIC",
        "أدوات وأنشطة العلاقات العامة",
        "استراتيجيات الوسائط الرقمية والاجتماعية",
        "تنظيم الأحداث",
        "كتابة بيان صحفي فعال",
        "وسائل التواصل الاجتماعي والصحافة",
        "Absence Management for Managers: Strategies for Effectively Managing Employee Attendance",
        "Addressing Anti-Social Behavior: Understanding and Responding to Incidents",
        "Allergy Management and Anaphylaxis: Identifying and Responding to Allergic Reactions",
        "Combatting Hate Crime: Promoting Understanding and Inclusivity",
        "Comprehensive Understanding of Child Safeguarding: Principles and Practices",
        "Comprehensive Understanding of Safeguarding Vulnerable Adults: Promoting Safety and Well-being",
        "Creating a Respectful Workplace: Fostering Dignity and Inclusion",
        "Designated Officers and Leads: Safeguarding Children and Young People Effectively",
        "Disability Awareness in the Workplace: Promoting Accessibility and Inclusion",
        "Early Recognition and Management of Sepsis: Identifying and Responding to Critical Infections",
        "Effective Absence Management: Strategies for Ensuring Workforce Attendance",
        "Effective Communication in Conference Calls: Strategies for Clear and Engaging Conversations",
        "Effective Medicines Management: Ensuring Safety and Accuracy",
        "Employee Monitoring in the Workplace: Balancing Privacy and Security",
        "Engaging and Productive Team Meetings: Fostering Collaboration and Results",
        "Essentials of Medical Devices: Introduction and Safety Considerations",
        "Establishing Professional Boundaries: Maintaining Ethical Conduct in the Workplace",
        "Ethical Decision-Making: Navigating Business Ethics with Wisdom",
        "Exploring Equality, Diversity, and Inclusion (EDI): Unpacking the Nine Protected Characteristics",
        "Facilitating Successful Conference Calls: Techniques for Smooth and Productive Discussions",
        "Frontline Support for Individuals with Dementia: Enhancing Care and Understanding",
        "Harnessing Intellectual Property (IP): Maximizing the Benefits for Your Business",
        "Importance of Equality, Diversity, and Inclusion: Fostering a Culture of Belonging",
        "Increasing Awareness: Learning Disability and Autism in Healthcare Settings",
        "Initiating Conversations: Techniques for Starting Meaningful Interactions",
        "Intellectual Property (IP) Basics: Safeguarding and Utilizing Intellectual Assets",
        "Introduction to Human Rights Act: Promoting Rights and Freedoms in the Workplace",
        "Keeping Meetings on Track: Strategies for Maintaining Focus and Achieving Objectives",
        "Mastering Meeting Minutes: Techniques for Accurate and Comprehensive Note-Taking",
        "Mastering the Art of Presentation: Engaging Strategies to Captivate Your Audience",
        "Medication Awareness in Healthcare: Best Practices and Safety Guidelines",
        "Navigating the Mental Capacity Act: Ensuring Ethical Decision-Making",
        "Patient Chaperone Policy: Ensuring Privacy and Comfort in Medical Settings",
        "Personal Safety at Work: Essential Guidelines for Ensuring Security",
        "Preparing for Effective Meetings: Tips for Planning and Organizing Ahead",
        "Promoting British Values: Embedding Principles of Democracy, Rule of Law, and Respect",
        "Protecting Children: Building Awareness on Child Sexual Exploitation",
        "Remembering Names with Ease: Techniques for Building Rapport and Connection",
        "Safe Handling of Controlled Drugs: Protocols and Procedures",
        "Safe Handling of Medical Gases: Procedures and Precautions",
        "Safeguarding Adults at Risk: Compliance and Best Practices in Scotland",
        "Safeguarding Adults at Risk: Ensuring Protection in Wales",
        "Safeguarding Children and Young People in Scotland: Promoting Safety and Well-being",
        "Safeguarding Children and Young People in Wales: Ensuring Protection and Support",
        "Safeguarding Officers or Leads: Meeting the Needs of Vulnerable Adults",
        "Senior Manager's Guide to Equality and Diversity: Driving Inclusion at the Organizational Level",
        "Sharps Awareness and Safety in Medical Environments: Handling and Disposal",
        "Substance Abuse Awareness: Understanding Drug and Alcohol Impact in the Workplace",
        "Supporting New and Expectant Mothers: Ensuring Workplace Well-being and Compliance",
        "The Power of Active Listening: Enhancing Communication and Understanding",
        "Understanding Domestic Abuse: Supporting Victims and Creating a Safe Environment",
        "Understanding Duty of Care",
        "Understanding Informed Consent in Medical Practice: Empowering Patients with Information",
        "Understanding Person-Centred Care",
        "Understanding the Care Act 2014: Roles and Responsibilities for Organizations",
        "Understanding the PREVENT Strategy: Identifying and Preventing Radicalization",
        "Whistleblowing: Encouraging a Culture of Transparency and Accountability",
        "Unleash Your Confidence: Embracing Your Inner Power",
        "Resilience in Action: Building Strength in the Face of Challenges",
        "Amplify Your Personal Impact: Strategies for Making a Lasting Impression",
        "Bouncing Back from Setbacks: Embracing Failure as a Catalyst for Growth",
        "Cultivating Self-Confidence: Building a Strong Foundation of Belief",
        "Charisma Unleashed: Unveiling the Secrets of Personal Magnetism",
        "Managing Nerves with Ease: Techniques for Confident Performance",
        "Cultivating High Self-Esteem: Building a Healthy Self-Image",
        "Embracing Feedback with Confidence: Navigating Criticism with Grace",
        "The Art of Assertiveness: Strategies for Expressing Yourself with Confidence",
        "Road Safety and Traffic Management: Highways and Transport Safety",
        "Risk Assessment for Transportation",
        "Environmental Impact Assessment in Transportation",
        "Road Infrastructure Maintenance",
        "Building Rapport: Mastering the Art of Connection and Relationship-Building",
        "Making a Lasting Impression: Strategies for Leaving a Positive Impact",
        "Empowered Boundaries: Saying No with Confidence and Respect",
        "Creating Career Opportunities: Steps to Make Yourself Promotable",
        "Standing Firm: Asserting Yourself and Maintaining Boundaries",
        "Taking Charge of Your Life: Empowering Strategies for Personal Control",
        "Mastering Inner Dialogue: Unlocking Success through Positive Self-Talk",
        "Conflict Resolution: Dealing with Anger and Difficult People",
        "Embracing Feedback with Confidence: Navigating Criticism with Grace",
        "Managing Difficult Situations and Challenging Customers: Assertiveness Techniques for Success",
        "Assertiveness Unleashed: Techniques for Effective Communication and Self-Expression",
        "Navigating Difficult Conversations with Confidence: Strategies for Positive Outcomes",
        "Persuasion Skills for Success: Influencing Your Boss with Impact",
        "Embracing Your Worth: Overcoming Inferiority and Building Self-Confidence",
        "The Power of Positive Language: Harnessing the Impact of Words for Success",
        "Exploring Emotional Intelligence: Understanding and Nurturing Your Emotional Skills",
        "Thriving Amidst Difficult Colleagues: Strategies for Collaboration and Productivity",
        "Overcoming Communication Barriers: Enhancing Understanding and Connection",
        "Mastering the LAER Method: Effective Communication for Influence and Impact",
        "Unleashing Your Personal Power: Strategies for Greater Influence",
        "Conflict Resolution: Dealing with Anger and Difficult People",
        "The Influencing Star: Mastering the Art of Persuasion and Influence",

        "Building Authentic Rapport: Strategies for Connecting and Influencing Others",
        "Amplifying Your Influence: Techniques for Increasing Your Impact",
        "Inspiring Action: Motivating Others to Take Charge and Make a Difference",
        "Secrets of Persuasion: Unveiling Techniques for Effective Influence",
        "Unlocking the Power of Influence: Exploring the Six Influencing Styles",
        "Voice of Success: Enhancing Vocal Quality for Effective Communication",
        "The Power of Empathy: Fostering Connection and Understanding",
        "Emotional Intelligence Essentials: Nurturing Your Emotional Skills for Influence",
        "Power vs. Influence: Understanding and Harnessing Their Distinctions",
        "WIIFM: Getting People to Listen with Impact and Relevance",
        "Clear and Compelling Presentations: Delivering Information with Impact",
        "The 7 Cs of Effective Communication: Keys to Clear and Engaging Interactions",
        "Thinking on Your Feet: Techniques for Quick and Impactful Responses",
        "Transforming Disgruntled Customers: Effective Complaint Handling for Loyalty",
        "Excelling in Customer Service: Strategies for Competitive Advantage",
        "Customer Retention Mastery: Techniques for Building Customer Loyalty",
        "Customer Service Language Mastery",
        "Delivering Exceptional Customer Service: Going Above and Beyond",
        "Cultivating a Positive Customer Service Attitude: Keys to Service Excellence",
        "Mastering Telephone Customer Service: Techniques for Outstanding Interactions",
        "Confidently Handling Angry Callers: Strategies for Effective Resolution",
        "The Art of Active Listening: Enhancing Understanding and Connection",
        "Asking Better Questions: Techniques for Effective Communication and Engagement",
        "Opportunity Cost Demystified: Exploring the Value of Alternative Choices",
        "Mastering Credit Management: Strategies for Timely Payment Collection",
        "Decoding Balance Sheets: Unlocking Insights into Financial Health",
        "Demystifying Double Entry Bookkeeping: Essential Principles and Practices",
        "Unveiling the Profit and Loss Account: Understanding Financial Performance",
    ];

    //create scope that check maximo_api_attempts is less than MaximoRetryCount and maximo_retry_time greater than MaximoRetryDelay
    public function scopeMaximoApiAttempts($query)
    {
        $intervel = Carbon::now()->subMinutes(Tools::getConfig('MaximoRetryDelay'));
        $retry = Tools::getConfig('MaximoRetryCount');
        return $query->where(function ($query) use ($intervel, $retry) {
            $query->where('maximo_api_attempts', '<', $retry)->where('maximo_retry_time', '<', $intervel);
        })->orWhere(function ($query) {
            $query->whereNull('maximo_retry_time');
        });
    }


    public function companies()
    {
        return $this->belongsToMany('Models\Company', 'company_module_enrollment', 'learning_module_id', 'company_id')->where('type', 1);
    }

	public function companyModuleEnrollments()
	{
		return $this->hasMany('Models\CompanyModuleEnrollment', 'learning_module_id', 'id');
	}

	public function editAccessCompanies()
    {
        return $this->belongsToMany('Models\Company', 'company_module_enrollment', 'learning_module_id', 'company_id')->where('type', 2);
    }

    public function scopeInternalCourse($query) {
        $query->whereIn('name', LearningModule::$catelog);
        return $query;
    }

    public function scopeNotInternalCourse($query) {
        $query->whereNotIn('name', LearningModule::$catelog);
        return $query;
    }

    public function vector(){
        return $this->belongsTo(LearningModuleVectorizedData::class, 'id','learning_module_id');
    }

	public function forms()
    {
        return $this->belongsToMany(Form::class, 'form_learning_module_mapping', 'module_id', 'form_id')->withTimestamps();
    }

	public function getRefreshDateAttribute($value) {
		if ($value === '0000-00-00 00:00:00' || $value === null) {
			return null;
		}
		return $value;
	}

	public function scopeAIChatbotCourses($query) {
        return $query->where('add_to_ai_chat', true);
    }

	public function courseBasketItems()
    {
        return $this->morphMany(CourseBasket::class, 'item');
    }

	/**
	 * Invalidate the all valid resources cache
	 */
	protected static function invalidateValidResourcesCache() {
		try {
			if (function_exists('cache')) {
				cache()->forget('all_valid_learning_modules');
			}
		} catch (\Exception $e) {
			// Cache might not be available - continue silently
		}
	}

}
