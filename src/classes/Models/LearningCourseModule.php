<?php
namespace Models;


class LearningCourseModule extends \Illuminate\Database\Eloquent\Model {
	protected $table = 'learning_course_modules';

	protected $fillable = [
		'learning_course_id',
		'learning_module_id'
	];


	/**
	 * @param $entries
	 * @return int
	 * @def update lesson tracking status based on the resources
	 */
	public static function updateTracking($lesson){
		if(!empty($lesson)) {
			$lesson = \Models\LearningModule::find($lesson->id);
			if ($lesson) {
				if (!empty($lesson->Modules)) {
					$track_progress = 1;
					foreach ($lesson->Modules as $module) {
						if ($module->track_progress) {
							$track_progress = 1;
							break;
						} else {
							$track_progress = 0;
						}
					}
					$lesson->track_progress = $track_progress;
					$lesson->save();
				}
			}
		}
	}

	protected static function boot() {
		parent::boot();

		static::created(function($entry) {
			// \Models\LearningCourseModule::updateTracking($entry);
			self::invalidateCourseModulesCache($entry->learning_course_id);
		});
		
		static::updated(function($entry) {
			// Invalidate cache for both old and new course IDs
			if ($entry->isDirty('learning_course_id')) {
				self::invalidateCourseModulesCache($entry->getOriginal('learning_course_id'));
			}
			self::invalidateCourseModulesCache($entry->learning_course_id);
		});
		
		static::deleted(function($entry) {
			self::invalidateCourseModulesCache($entry->learning_course_id);
		});
	}
	
	/**
	 * Invalidate cache for a specific course's modules
	 */
	protected static function invalidateCourseModulesCache($course_id) {
		if (empty($course_id)) {
			return;
		}
		
		try {
			if (function_exists('cache')) {
				$cacheKey = 'course_modules_' . $course_id;
				cache()->forget($cacheKey);
			}
		} catch (\Exception $e) {
			// Cache might not be available - continue silently
		}
	}
}
