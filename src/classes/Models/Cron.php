<?php

namespace Models;

use APP\Controllers\AssignmentController;
use APP\Controllers\ChatBotController;
use APP\Controllers\HRLearningResultsImportController;
use APP\Controllers\ImportController;
use APP\Controllers\ImportUserController;
use APP\Controllers\PowerBIController;
use APP\Email;
use App\GetMSAuth;
use APP\Tools;
use Carbon\Carbon;
use GlobalPayments\Api\Entities\GpApi\DTO\Card;
use Illuminate\Database\Capsule\Manager as DB;
use \Models\UserScheduleWaitingList;

class Cron extends \Illuminate\Database\Eloquent\Model {
	protected $casts = [
		'status' => 'boolean',
		'locked' => 'boolean',
	];

	protected $fillable = ['function', 'name', 'description', 'frequency', 'next_run_time', 'preferred_time', 'conditions'];

	/**
	 * Calculate and set the next run time for this cron task
	 */
	public function calculateNextRunTime() {
		$this->next_run_time = \Carbon\Carbon::now()->addMinutes($this->frequency);
		return $this;
	}

	/**
	 * Check if this cron task is due to run
	 */
	public function isDue() {
		$now = \Carbon\Carbon::now();

		// If never run before or next_run_time is null, it's due
		if ($this->times_run == 0 || !$this->next_run_time) {
			return true;
		}

		// Check if next_run_time is in the past
		return \Carbon\Carbon::parse($this->next_run_time)->lessThan($now);
	}

	public static function runAll($settings) {
		$crons = \Models\Cron
			::where('status', true)
			->get()
		;

		// time limit minimum and maximum, inbetween is set in configuration option
		$maximum_kill = 300;
		$default_kill = 300;
		$minimum_kill = 20;
		$kill_time_minutes = \APP\Tools::getConfig('CrontTaskKillTime');
		if (!$kill_time_minutes) {
			$kill_time_minutes = $default_kill;
		}
		if ($kill_time_minutes > $maximum_kill) {
			$kill_time_minutes = $maximum_kill;
		}

		if ($kill_time_minutes < $minimum_kill) {
			$kill_time_minutes = $minimum_kill;
		}
		// Kill this task after set amount of time(seconds).
		$kill_time_seconds = $kill_time_minutes * 60;
		set_time_limit($kill_time_seconds);

		$now = \Carbon\Carbon::now();

		foreach ($crons as $key => $cron) {
			// Calculate lock timeout for stuck tasks
			$updated_at = \Carbon\Carbon::parse($cron->updated_at);
			$cron_unlock_time = $kill_time_minutes + 10;
			$locked_timeout = $updated_at->copy()->addMinutes($cron_unlock_time);

			// Add additional timeout based on last run time (twice the time)
			$locked_timeout_last_run_time = $updated_at->copy()->addSeconds($cron->last_run_time * 2);
			if ($locked_timeout_last_run_time > $locked_timeout) {
				$locked_timeout = $locked_timeout_last_run_time;
			}

			// Check if task is stuck and unlock it
			if ($cron->locked && $now > $locked_timeout) {
				$cron->locked = false;
				$cron->save();
			}

			// Conditions refer to licensing options in lms_config file.
			$condition_met = true;
			if ($cron->conditions) {
				$conditions = json_decode($cron->conditions, TRUE);

				foreach ($conditions as $key => $condition) {
					if (isset($conditions['true'])) {
						foreach ($condition as $key => $condition_value) {
							if (!$settings["licensing"][$condition_value]) {
								$condition_met = false;
							}
						}
					}

					if (isset($conditions['false'])) {
						foreach ($condition as $key => $condition_value) {
							if ($settings["licensing"][$condition_value]) {
								$condition_met = false;
							}
						}
					}
				}
			}

			// Check if cron should run: not locked, due to run (or force run), and conditions met
			if (
				!$cron->locked &&
				($cron->isDue() || $cron->force_run) &&
				$condition_met
			) {
				// Check preferred time (hour of day) if set
				$preferred_time_ok = (
					$cron->preferred_time == $now->format("G") ||
					(empty($cron->preferred_time) && $cron->preferred_time !== 0) ||
					$cron->force_run
				);

				if ($preferred_time_ok) {
					//if method exists
					if (method_exists('\Models\Cron', $cron->function)) {
						$time_start = microtime(true);
						// RUN IT!

						// Add to cron history
						$history_entry = new \Models\CronHistory;
						$history_entry->name = $cron->name;
						$history_entry->cron_id = $cron->id;
						$history_entry->locked = $cron->locked;
						$history_entry->status = $cron->status;
						$history_entry->last_run_time = $cron->last_run_time;
						$history_entry->average_run_time = $cron->average_run_time;
						$history_entry->force_run = $cron->force_run;
						//$history_entry->updated_at_and_last_run_time = $updated_at_and_last_run_time;
						$history_entry->locked_timeout = $locked_timeout;
						$history_entry->frequency = $cron->frequency;
						$history_entry->preferred_time = $cron->preferred_time;
						$history_entry->save();

						$query = \Models\Cron::lockForUpdate()->find($cron->id); //- causing locked database
						//$query = \Models\Cron::find($cron->id);
						// Lock method!
						$query->locked = true;
						$force_run = false;
						if ($query->force_run) {
							$force_run = true;
							$query->timestamps = false;
						}
						$query->force_run = false;
						$query->save();
						if (!$force_run) {
							$query->touch(); // make sure updated_at is updated, so that next time cron task is ran, old locked task will not be repeated
						}
						try {
							$obj = new \Models\Cron();
							$method_response = $obj->{$cron->function}($settings);
							$time_end = microtime(true);
							$time = $time_end - $time_start;
							$query->average_run_time = $query->average_run_time > 0 ? (($query->average_run_time + $time) / 2) : $time;
							if (
								is_string($method_response) ||
								is_numeric($method_response)
							) {
								$query->output = $method_response . "\n ---------------- \n" . $query->output;
								$query->last_output = $method_response;
							} else {
								$query->output = json_encode($method_response) . "\n ---------------- \n" . $query->output;
								$query->last_output = json_encode($method_response);
							}
						} catch(\Exception $e) {
							$time_end = microtime(true);
							$time = $time_end - $time_start;
							$query->last_output = $e->getMessage();
						}

						$query->times_run = $query->times_run + 1;
						$query->locked = false;
						$query->last_run_time = $time;

						// Calculate and set next run time
						$query->calculateNextRunTime();
						$query->save();

					} else {
						if (\APP\Tools::getConfig('cronTaskDebug')) {
							if (isset($settings['settings']['database']['database'])) {
								echo "Method does not exists: " . $cron->function . ', on database/site: ' . $settings['settings']['database']['database'];
							} else {
								echo "Method does not exists: " . $cron->function . ', on site: ' . $settings['LMSUri'];
							}
						}
					}
				}
			}
		}
	}


	public function learnerAimReferenceServiceUpdate ($settings) {
		return "\n running: learnerAimReferenceServiceUpdate";
	}

	public function processBatchReports ($settings) {
		return \Models\BatchReport::processAll($settings);
	}

	public function dailyStatistic ($settings) {
		$time_start = microtime(true);
		return \Models\DailyStatistic::updateStats($time_start);
	}

	public function updateDashboards ($settings) {
		//Update dashboard statistics
		$stats = new \APP\Statistics(
			$settings["licensing"]["isApprentix"],
			$settings["licensing"]["isSMCR"]
		);
		$stats->populate();
		$stats->save_history();
		$stats->update();

		//Update dashboard data views
		$dts = new \APP\Dataviews(
			$settings["licensing"]["isApprentix"],
			$settings["licensing"]["isSMCR"]
		);
		$dts->populate();
		$dts->update();
	}

	public function userProgress ($settings) {
		return \Models\User::calculateGlobalProgress(false, true, $settings);
	}

	public function refreshResults ($settings) {
		$send_email = \APP\Tools::getConfig('sendRefreshEmail');
		return \APP\Refresh::refreshResults(false, false, false, $send_email, $settings, 'Nightly Cron task.');
	}

	public function queryBuilderItrator($settings) {
		$query = null;
		return \DB\ResourceQuery::queryItrator($query, $settings, true);
	}

	public function sendCivicaData($settings) {
		return UserPaymentTransaction::sendCivicaDataMail();
	}

	public static function apprentixNightly ($settings) {
		$output = [];
		if ($settings["licensing"]["isApprentix"]) {

			// Refresh all resources attached to standard if periodic_repeat is true and user has been on programme for periodic_repeat_months.
			$output[] = \Models\ApprenticeshipStandard::RefreshEntries();

			// Update all completion percentages for user/manager programmes
			$output[] = \Models\ApprenticeshipStandardUser::updateTimeSpent();
			$output[] = \Models\ApprenticeshipStandardUser::updateAllUserPercentage();
			$output[] = \Models\ApprenticeshipStandardUser::updateFallBehindUserPercentage();
			$output[] = \Models\ApprenticeshipStandardUser::updateFallBehindAssesorPercentage();
			$output[] = \Models\ApprenticeshipStandardUser::calculateEndDate();
			$output[] = \Models\ApprenticeshipStandardUser::calculateExpectedTime();


			// Check links between programmes and ILR records, then update progreamme/resource dates accrodingly!
			$linkIlrToUserProgramme = \APP\Tools::getConfig('linkIlrToUserProgramme');
			if ($linkIlrToUserProgramme) {
				$output[] = \Models\ApprenticeshipStandardUser::linkIlrProgramme();
			}
		}
		$output[] = \Models\User::calculateDaysSinceLastReview();

		return $output;

	}

	public static function apprentixNightlyReminders ($settings) {
		if ($settings["licensing"]["isApprentix"]) {
			// Send out reminder emails for resources that are not completed but expected completion date from today is less than X days.
			// Where X is in configuration option apprentixEmailReminderFrequency
			$send_frequency = \APP\Tools::getConfig('apprentixEmailReminderFrequency');
			\Models\ApprenticeshipStandardUser::sendReminderEmail(false, $send_frequency);
		}
	}

	public function processEmailQueue ($settings) {
		$send_email = \APP\Tools::getConfig('enableEmailFunctionality');
		if ($send_email) {
			return \APP\Email::processEmailQueue($settings['email'], false, $settings['LMSUrl']);
		}
	}

	public function smcrNotifications ($settings) {
		if ($settings["licensing"]["isSMCR"]) {
			return \APP\Smcr::sendNotifications($settings);
		}
	}

	public function nightlyMaintenance ($settings) {
		\APP\Maintenance::nightly($settings);
		\APP\Maintenance::archiveUserLearningModules($settings);
	}

	public function deleteEvidances ($settings) {
		\APP\Maintenance::evidenceTypeDeletion($settings);
	}

	public  function processEvents ($settings) {
		\Models\Schedule::processEvents($settings);
	}

	public function userFieldAlert ($settings) {
		\Models\User::userFieldAlert($settings);
	}

	public function processQueryUsers($settings){
		\Models\ResourceQuery::proccessUser($settings);
	}

	public function computeRecommendations ($settings) {
		return \APP\Recommendations::compute($settings, false);
	}

	public function nightlyUserDataExport ($settings) {
		\Models\User::exportDataToFile($settings, $settings['LMSPrivateUploadPath'] . 'export/');
		}
		public  function pdfRemove($settings)
		{
					FormWorkflow::deletePdf($settings);
		}
	public function assignApprenticeshipStandardToUsers($settings){
		\Models\ApprenticeshipStandardUser::processAssigmentToDepartmentUsers($settings);
		\Models\ApprenticeshipStandardUser::processAssigmentToDesignationUsers($settings);
		\Models\ApprenticeshipStandardUser::processAssigmentToGroupUsers($settings);
	}

	public function removeApprenticeshipStandardToUsers($settings){
		//Remove Programs from users
		\Models\ApprenticeshipStandardUser::processRemoveAssigmentToDepartmentUsers($settings);
		\Models\ApprenticeshipStandardUser::processRemoveAssigmentToDesignationUsers($settings);
		\Models\ApprenticeshipStandardUser::processRemoveAssigmentToGroupUsers($settings);
	}

	public function assignLearningModuleToUsers($settings){
			\Models\UserLearningModule::assignUsers($settings);
			\Models\UserLearningModule::removeUsers($settings);
	}

	public function refreshLessons ($settings) {
		return \APP\Refresh::refreshLessons();
	}

	public function refreshEvents ($settings) {
		return \APP\Refresh::refreshEvents(false, [], [], true, 'Nightly refresh Event cron task.');
	}

	public function eventReminderAdvanceNotification($settings) {
		\Models\Schedule::sendReminderAdvanceNotification($settings);
	}
	public function updateLessonStatusOnResourceCompletion($settings) {
		return \Models\LearningResult::updateLessonStatusOnLearningResult($settings);
	}

    public function refreshPowerbiDashboardData($settings) {
        $pbc = new PowerBIController();
        $powerbiAllReport = PowerBiReport::where('type','all')->get();
        $powerbiCompanyReports = PowerBiReport::where('type','company')->get();
        // $pbc->processDashboard();
    }

	public function refreshPowerbiDashboardCompanyAndDirectData($settings) {
		$pbc = new PowerBIController();
		return $pbc->refreshPowerbiDashboardCompanyAndDirectData();
	}

	public function refreshPowerbiDashboardAllData($settings) {
		$pbc = new PowerBIController();
		return $pbc->refreshPowerbiDashboardAllData();
	}

	public function processPowerbiDashboardData($settings) {
		$pbc = new PowerBIController();
		return $pbc->processQueuedReports();
	}

	public function processInitialPushPowerbiDashboardData($settings) {
		$pbc = new PowerBIController();
		return $pbc->processQueuedInitialPushReports();
	}


	public function updateMissingLastUpdatedLearnerProgrammeField($settings) {
		return \Models\ApprenticeshipStandardUser::lastUpdateFix();
    }
    public function updateExistingLinkedManager($settings)
    {
        return ManagerUser::updateExisitngLinkedManager($settings);
    }
    public function notifyUndersubscribedEvents($settings)
    {
        \Models\Schedule::notifyUndersubscribedEvents($settings);
    }

    public function refreshSkillMonitoring($settings) {
		return \Models\ApprenticeshipStandard::refreshSkill($settings);
	}

	public function sendAutomaticStarUpInstructionEmail($settings) {
		$enableAutomaticStarUpInstructionEmails = Tools::getConfig('enableAutomaticStarUpInstructionEmails'); //Need to send email when enableAutomaticStarUpInstructionEmails is true
		$sent_cnt = 0;
		if ($enableAutomaticStarUpInstructionEmails) {
			User
				::where(['startup_instructions_sent' => false, 'status' => true, 'exclude_from_emails' => false])
				->chunkById(100, function ($users) use (&$sent_cnt) { //Proccessing 100 users batch to save memory
					foreach ($users as $user) {
						if (Email::sendInstructionEmail($user->id, [])) {
							$sent_cnt++;
						}
					}
				})
			;
			return $sent_cnt . " users processed.";
		}
	}

	public function syncLessonDueDatesToResourceDueDates($settings) {
		if (\APP\Tools::getConfig('forceLessonDueDateToResource')) {
			return \Models\LearningResult::syncLessonDueDatesToResourceDueDates();
		}
	}

	public function deleteTempView($settings){
		CustomReport::deleteTempView($settings);
	}
	public function updateDemoData($settings) {
		\APP\DemoData::update();
	}
    public function refreshAssignedLearningResource($settings) {
        return	\Models\LearningResult::refreshUserAssignedLearningResourceOnLearningResult($settings);
    }
    public function emailNotifyRefreshedResources($settings)
    {
        $output[] = LearningResult::emailNotifyRefreshedResources();
        $output[] = ApprenticeshipStandardUser::emailNotifyRefreshedResources();
		return json_encode($output);
	}
    public function notifyWaitingUsersEventDeadlineAtPassed($settings)
    {
        return \Models\Schedule::notifyWaitingUsersEventDeadlineAtPassed();
    }

    public function changeWaitingListUserStatus($settings){
        ScheduleLink::changeWaitingListUserStatus($settings);
    }
	public function cronImportUserMyhrGtaa($settings) {
		$SftpHrClientName = \APP\Tools::getConfig('SFTPHRClientName');
		if (strtoupper($SftpHrClientName) === 'GTAA' || empty($SftpHrClientName)) {
			$data = ImportUserController::importCron($settings);
			$date =  date('Y-m-d H:i:s');
			if (is_array($data)) {
				$data =  ['processed_date'=>$date]+$data;
			} else {
				$import_log = new ImportLog();
				$import_log->file_name = '';
				$import_log->file_path = '';
				$import_log->params = '';
				$import_log->status = 1;
				$import_log->is_processed =true;
				$import_log->logs = [];
				$import_log->save();
				ImportLogError::create(['import_log_id'=>$import_log->id,'data'=>$data,'error_log'=>$data]);
				$data = ['processed_date'=>$date,'message'=>$data];
			}
			return $data;
		}

	}

	public function processDistributions($settings) {
		AssignmentController::cronAssignment($settings);
	}

	public function awaitingSignOffFormsReminder($settings) {
		return UserForm::sendAwaitingSignOffFormEmailReminder();
	}

	public function refreshOutlookVenueToken($settings){
		if(Tools::getConfig('useOutlookVenues') && Tools::getConfig('OutlookRoomAccessToken') && Tools::getConfig('OutLookRoomRefreshToken')){
			Venue::getToken(true);
			return Carbon::now();
		}
	}

	protected static function boot() {
		parent::boot();

		static::updating(function($entry) {
			$old_entry = \Models\Cron::find($entry->id);
			$changes = $entry->getDirty();
			$date_fields = [
			];
			\Models\TableHistory::trackChanges($old_entry, $changes, $date_fields, 'crons', $entry->id);
		});
	}
    public function removeOutlookEvent($settings){
    	return \Models\Schedule::removeOutlookEvent($settings);
    }

	public function proccessMaximoData($settings){
		$logger = \APP\LoggerHelper::getLogger();
		if(Tools::getConfig('enableMaximo')){
			\APP\Maximo::createQualification($settings,$logger);
			\App\Maximo::linkOrUpdateUser($settings,$logger);
			return Carbon::now();
		}else{
			return "Maximo not enabled!";
		}
    }

    /*
     * Write the description of the method here
     * @param $settings
     * @return
     */
    public function processProgrammeImport($settings)
    {
        return \Models\ApprenticeshipStandard::processProgrammeImport($settings);
    }
    public function processImportWizard($settings){
        return ImportController::cron($settings);
    }
    public function removeCreditLearningResource($settings){
        return CreditUsage::unassignLearningFromUser();
	}
    public function processLinkedAccountCompleteStatus($settings){
        return LearningResult::processLinkedAccountCompleteStatus();
    }
    public function vectorizeLearningModuleData($settings){
        return LearningModuleVectorizedData::vectorizeData($settings);
    }
	public static function renewRefreshToken($settings) {
		return Email::renewRefreshToken();
	}
	public function refreshPowerBIDataFlowAndDataset($settings)
	{
		$resp = [];
		$resp[] = PowerBIController::refreshDataFlow($settings);
		$resp[] = PowerBIController::refreshDataSets($settings);
		return json_encode($resp);
	}
  	public function multiAttemptsAtQuizNotification($settings){
		return \APP\Learning::multiAttemptsAtQuizNotification($settings);
    }

    public static function refreshDateSpecificResources($settings) {
		return \APP\Refresh::refreshDateSpecificResources();
	}

	public function cronImportHrDataBradford($settings) {
		$SftpHrClientName = \APP\Tools::getConfig('SFTPHRClientName');
		if (strtoupper($SftpHrClientName) === 'BRADFORD') {
			$data = ImportUserController::ImportBradFordHrUsers($settings);
			$date =  date('Y-m-d H:i:s');
			if (is_array($data)) {
				$data =  ['processed_date'=>$date]+$data;
			} else {
				$import_log = new ImportLog();
				$import_log->file_name = '';
				$import_log->file_path = '';
				$import_log->params = '';
				$import_log->status = 1;
				$import_log->is_processed =true;
				$import_log->logs = [];
				$import_log->save();
				ImportLogError::create(['import_log_id'=>$import_log->id,'data'=>$data,'error_log'=>$data]);
				$data = ['processed_date'=>$date,'message'=>$data];
			}
			return $data;
		}
	}
    public function waitingListRecycle($settings){
        return Schedule::waitingListRecycle();
    }

	public static function processUserDownloads($settings) {
		return \Models\UserDownload::processAll();
  }
  public static function linkedModuleAssign($settings){
    return LinkedLearningModule::assignLinkedModule();
  }

  	public function cronImportHrDataSurrey($settings) {
		$data = ImportUserController::ImportSurreyHrUsers($settings);
		$date =  date('Y-m-d H:i:s');
		if (is_array($data)) {
			$data =  ['processed_date'=>$date]+$data;
		} else {
			$import_log = new ImportLog();
			$import_log->file_name = '';
			$import_log->file_path = '';
			$import_log->params = '';
			$import_log->status = 1;
			$import_log->is_processed =true;
			$import_log->logs = [];
			$import_log->save();
			ImportLogError::create(['import_log_id'=>$import_log->id,'data'=>$data,'error_log'=>$data]);
			$data = ['processed_date'=>$date,'message'=>$data];
		}
		return $data;
	}


	public function cronImportHRandLearningResults_S($settings) {
		return HRLearningResultsImportController::cronImportHRandLearningResults_S($settings);
	}

    public static function processScheduletWaitingList($settings) {
        return UserScheduleWaitingList::checkCompletedSchedule();
    }

    public function outlookSubscription($settings){
        return OutlookSubscription::updateSubscription();
    }
    public static function LearningModuleVectorizedDataTableTruncate($settings) {
        return LearningModuleVectorizedData::LearningModuleVectorizedDataTableTruncate();
    }


	public static function regenerateProgrammeCompletionTimes($settings) {
		$apprenticeshipStandard = \Models\ApprenticeshipStandard::where('duration_updated', 1)->get();
		$apprenticeshipStandard->each(function ($standard) {
			$users = $standard->Users;
			foreach ($users as $user) {
				\Models\ApprenticeshipStandardUser::assignToStandard($user->id, $standard->id, null);
			}
		});
	}

	/**
	 * Manager Self Assignment Cron Job
	 * Automatically assigns managers to themselves as learners when configuration is enabled
	 */
	public function managerSelfAssignment($settings) {
		return \Models\ManagerUser::processManagerSelfAssignment($settings);
	}
	public static function addApprovedCoursesToGoogleCloud($settings) {
		if (\APP\Tools::getConfig('isLearningAI')) {
        	return ChatBotController::processModulesForAI();
		}
    }

}
