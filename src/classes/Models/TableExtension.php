<?php

namespace Models;

class TableExtension extends \Illuminate\Database\Eloquent\Model {
	
	// Cache duration in seconds (1 hour)
	private static $cacheTimeout = 3600;
	protected $casts = [
		'status' => 'boolean',
	];

	protected $fillable = [
		'table',
		'table_id',
		'name'
	];

    protected static function boot()
    {
        parent::boot();
        static::saving(function ($tableExtension) {
            if ($tableExtension->getAttribute('table') === "schedules" && $tableExtension->name === "venue_id") {
                $originalValue = $tableExtension->getOriginal('value');
                $newValue = $tableExtension->getAttribute('value');
                if ($originalValue != $newValue) {
                    $schedule = Schedule::find($tableExtension->table_id);
                    Schedule::sendRescheduledMail($schedule);
                    Schedule::updateRescheduledEvent($schedule);
                }
            }
        });
        
        // Clear cache when table extension records are modified
        static::saved(function ($tableExtension) {
            self::clearCache($tableExtension->table, $tableExtension->table_id, $tableExtension->name);
        });
        
        static::deleted(function ($tableExtension) {
            self::clearCache($tableExtension->table, $tableExtension->table_id, $tableExtension->name);
        });
    }

	public static function updateField ($table = false, $table_id = false, $field_name = false, $value = false, $type = false) {
		$field_type = \Models\TableExtension::defaultTypes($table, $field_name);
		if (
			$field_type &&
			$table &&
			$table_id &&
			$field_name
		) {
			$entry = \Models\TableExtension::firstOrNew(
				[
					'table' => $table,
					'table_id' => $table_id,
					'name' => $field_name
				]
			);
			$entry->type = $field_type;
			$entry->value = \Models\TableExtension::valueTypeConversion($value, $entry->type, 'to');
			$entry->save();
			
			// Cache invalidation is handled automatically by the saved() event
		}
	}

	public static function returnAllFields ($table, $table_id, &$object) {
		if (
			$table &&
			$table_id
		) {
			// Try to get from cache first
			$extended_fields = null;
			try {
				if (function_exists('cache') && php_sapi_name() !== 'cli') {
					$cacheKey = self::getCacheKey($table, $table_id);
					$extended_fields = cache()->get($cacheKey);
				}
			} catch (\Exception $e) {
				// Cache might not be available - continue without cache
			}
			
			// Cache miss - query database
			if ($extended_fields === null) {
				$extended_fields = \Models\TableExtension
					::where('table_id', $table_id)
					->where('table', $table)
					->where('status', true)
					->get()
					->toArray()
				;
				
				// Cache the results
				self::cacheAllFields($table, $table_id, $extended_fields);
			}
			
			// Add extended object anyway! So it does not trigger "PHP Notice:  Trying to get property of non-object".
			$object->extended = new \stdClass;
			if (count($extended_fields) > 0) {
				foreach ($extended_fields as $key => $extended_field) {
					$object->extended->{$extended_field['name']} = \Models\TableExtension::valueTypeConversion($extended_field['value'], $extended_field['type'], 'from');
				}
			}
		}
	}

	// Holds tables with extended fields, would need to automate/integrate this better in future.
	// If you think that you can do better, don't do the lesser way! - and that is why poor men kept dreaming big till he died
	public static function defaultTypes ($table = false, $field_key = false) {
		$response = false;

		if (
			$table &&
			$field_key
		) {
			$entry = \Models\TableExtensionField
				::where('table', $table)
				->where('field_key', $field_key)
				->where('status', true)
				->first()
			;
			if ($entry) {
				$response = $entry->field_type;
			}
		}


		return $response;
	}

	// Will convert value to appropiate data type when saving/retrieving data.
	public static function valueTypeConversion ($value, $type, $direction = 'from') {
		switch ($type) {
			case 'integer':
				$value = (int) $value;
				break;

			case 'float':
				$value = (float) $value;
				break;

			case 'boolean':
				$value = ($value == 'true' || $value == 1 ? true : false);
				break;

			case 'json':
				if ($direction == 'from') {
					$value = json_decode($value);
				} else {
					$value = json_encode($value);
				}
				break;

			default:
				$value = $value;
				break;
		}
		return $value;
	}

	public static function getValue($table, $table_id, $name) {
		// Generate cache key
		$cacheKey = self::getCacheKey($table, $table_id, $name);
		
		// Try to get from cache first
		try {
			if (function_exists('cache') && php_sapi_name() !== 'cli') {
				$cachedValue = cache()->get($cacheKey);
				if ($cachedValue !== null) {
					return $cachedValue === 'CACHE_FALSE' ? false : $cachedValue;
				}
			}
		} catch (\Exception $e) {
			// Cache might not be available - continue without cache
		}
		
		// Cache miss - query database
		$entry = \Models\TableExtension
			::where('table', $table)
			->where('table_id', $table_id)
			->where('name', $name)
			->first()
		;
		
		$result = false;
		if (
			$entry &&
			$entry->value != 'undefined'
		) {
			$result = \Models\TableExtension::valueTypeConversion($entry->value, $entry->type, 'from');
		}
		
		// Store in cache
		try {
			if (function_exists('cache') && php_sapi_name() !== 'cli') {
				// Use special value to cache 'false' results
				$cacheValue = $result === false ? 'CACHE_FALSE' : $result;
				cache()->put($cacheKey, $cacheValue, self::$cacheTimeout);
			}
		} catch (\Exception $e) {
			// Cache might not be available - that's ok
		}
		
		return $result;
	}


	public function tableVenue(){
		$this->hasOne('Models\Venue','id','value');
	}
	
	/**
	 * Generate cache key for table extension values
	 */
	private static function getCacheKey($table, $table_id, $name = null) {
		try {
			if ($name) {
				return \APP\Cache\CacheHelper::key('table_ext', $table, $table_id, $name);
			} else {
				return \APP\Cache\CacheHelper::key('table_ext_all', $table, $table_id);
			}
		} catch (\RuntimeException $e) {
			// Fallback for CLI/cron contexts where container isn't set
			// Get database name from Eloquent connection config
			try {
				$dbConfig = \Illuminate\Database\Capsule\Manager::connection()->getConfig();
				$dbName = $dbConfig['database'] ?? 'lms';
			} catch (\Exception $dbException) {
				// Final fallback if database connection fails
				$dbName = 'lms';
			}
			
			$cleanDb = preg_replace('/[^a-zA-Z0-9_]/', '_', $dbName);
			if ($name) {
				return $cleanDb . ':table_ext:' . $table . ':' . $table_id . ':' . $name;
			} else {
				return $cleanDb . ':table_ext_all:' . $table . ':' . $table_id;
			}
		}
	}
	
	/**
	 * Clear cache for specific field or entire table record
	 */
	private static function clearCache($table, $table_id, $name = null) {
		try {
			if (function_exists('cache') && php_sapi_name() !== 'cli') {
				if ($name) {
					// Clear specific field cache
					cache()->forget(self::getCacheKey($table, $table_id, $name));
				}
				// Also clear bulk cache for this table/record
				cache()->forget(self::getCacheKey($table, $table_id));
			}
		} catch (\Exception $e) {
			// Cache might not be available - that's ok
		}
	}
	
	/**
	 * Bulk cache all fields for a table record (used by returnAllFields)
	 */
	private static function cacheAllFields($table, $table_id, $fields) {
		try {
			if (function_exists('cache') && php_sapi_name() !== 'cli') {
				$cacheKey = self::getCacheKey($table, $table_id);
				cache()->put($cacheKey, $fields, self::$cacheTimeout);
				
				// Also cache individual fields for getValue() calls
				foreach ($fields as $field) {
					$fieldCacheKey = self::getCacheKey($table, $table_id, $field['name']);
					$result = \Models\TableExtension::valueTypeConversion($field['value'], $field['type'], 'from');
					$cacheValue = $result === false ? 'CACHE_FALSE' : $result;
					cache()->put($fieldCacheKey, $cacheValue, self::$cacheTimeout);
				}
			}
		} catch (\Exception $e) {
			// Cache might not be available - that's ok
		}
	}
	
	/**
	 * Debug method to check cache status (can be removed in production)
	 */
	public static function getCacheStats($table, $table_id, $name = null) {
		$stats = [];
		try {
			if (function_exists('cache') && php_sapi_name() !== 'cli') {
				if ($name) {
					$cacheKey = self::getCacheKey($table, $table_id, $name);
					$stats['single_field'] = [
						'key' => $cacheKey,
						'cached' => cache()->has($cacheKey),
						'value' => cache()->get($cacheKey)
					];
				}
				
				$bulkCacheKey = self::getCacheKey($table, $table_id);
				$stats['bulk_fields'] = [
					'key' => $bulkCacheKey,
					'cached' => cache()->has($bulkCacheKey),
					'count' => cache()->has($bulkCacheKey) ? count(cache()->get($bulkCacheKey)) : 0
				];
			}
		} catch (\Exception $e) {
			$stats['error'] = $e->getMessage();
		}
		return $stats;
	}
}