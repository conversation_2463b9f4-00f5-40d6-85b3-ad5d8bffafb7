<?php

namespace Models;

use Illuminate\Database\Capsule\Manager as Capsule;

class Structure extends \Illuminate\Database\Eloquent\Model {
	protected $table = 'structure';
	//protected $fillable = ['id', 'name', 'name_template', 'key', 'description', 'description_template', 'icon', 'color', 'parent_id', 'order', 'hidden', 'status', 'show_help'];

	public function roles() {
		return $this->belongsToMany('Models\Role', 'role_structure');
	}
	public function role() {
		return $this->hasOne('Models\RoleStructure');
	}

	public function getNameAttribute($value) {
		$translator = \APP\Templates::getTranslator();
		$value = $translator->replaceLabels($value);
		return $value;
	}

	public static function firstLink($settings = false, $short = false, $key = false, $first_structure = 0, $ignore_main_role = false) {
		$response = false;
		$response_key = false;


		if (
			$settings &&
			isset($settings['licensing'])
		) {
			$structure = \Models\Structure::allItems($settings['licensing']['hiddenMenuItems']);
			$tree = \APP\Tools::buildTree($structure->toArray());

			// Helper function to validate if a dashboard path exists in user's available structure
			$validateDashboardPath = function($groupKey, $tabKey = null, $pageKey = null) use ($tree) {
				foreach ($tree as $group) {
					if ($group['key'] === $groupKey) {
						if ($tabKey === null) {
							return true; // Group exists
						}
						if (isset($group['children'])) {
							foreach ($group['children'] as $tab) {
								if ($tab['key'] === $tabKey) {
									if ($pageKey === null) {
										return true; // Tab exists
									}
									if (isset($tab['children'])) {
										foreach ($tab['children'] as $page) {
											if ($page['key'] === $pageKey) {
												return true; // Page exists
											}
										}
									}
								}
							}
						}
					}
				}
				return false;
			};

			// If user has dashboard, set default url as next one, usually manage learning.
			if (
				\Models\Dashboard::hasDashboard()
			) {
				$first_structure++;
			}

			// If alt-role is not main role, do not load "Home" as first page.
			if (
				!\APP\Auth::isUserMainRole() &&
				!$ignore_main_role
			) {
				$first_structure++;
			}

			if (
				\APP\Auth::isUserMainRole() &&
				\APP\Tools::getConfig('hideHomeButton') &&
				!empty($settings["shadow"])
			) {
				$first_structure++;
            }
            //Add logic to redirect to specified URL if logged-in user is a manager
            if (\APP\Auth::isManagerInterface()) {
                // Validate that manage-learning path exists in user's structure
                if ($validateDashboardPath('manage-learning', 'manage-learning-resources', 'lessons-and-learning-resources')) {
                    $response = ($short ? '' : $settings["LMSAppUri"]) . 'dashboard/manage-learning/manage-learning-resources/lessons-and-learning-resources';
                    $response_key = 'manage-learning';
                }
                // If path doesn't exist, fall through to default structure logic
            }
			if (
                (!\APP\Auth::isLearner() || \APP\Auth::roleId() == \APP\Tools::getConfig('powerbiDashboardViewerRoleID')) &&
				isset($tree[$first_structure]['key']) &&
				isset($tree[$first_structure]['children'][0]['key']) &&
                isset($tree[$first_structure]['children'][0]['children'][0]['key']) &&
                !$response
            ) {

				$response = ($short ? '' : $settings["LMSAppUri"]) . 'dashboard/'. $tree[$first_structure]['key'] . '/' . $tree[$first_structure]['children'][0]['key'] . '/' . $tree[$first_structure]['children'][0]['children'][0]['key'];
				$response_key = $tree[$first_structure]['key'];
			} else {
				if (!$short) {
					$response = $settings["LMSUri"];
				}
			}


			// If user has more than one role, send them to "dashboard/home/<USER>/home-roles-list"
			// And only if user is in original role!
			if (
				\APP\Auth::isUserMainRole() &&
				!\APP\Tools::getConfig('hideHomeButton') &&
				!$ignore_main_role
			) {
				// Validate that home path exists in user's structure
				if ($validateDashboardPath('home', 'home-roles', 'home-roles-list')) {
					$response = ($short ? '' : $settings["LMSAppUri"]) . 'dashboard/home/<USER>/home-roles-list';
					$response_key = 'home';
				}
				// If path doesn't exist, fall through to default structure logic
			}
			// If user has dashboard assigned and it is maximised, send them to "/dashboard/charts/charts-custom/charts-custom-list"
			if (
				\Models\Dashboard::hasMaximisedDashboard() &&
				!$ignore_main_role
			) {
				// Validate that charts path exists in user's structure
				if ($validateDashboardPath('charts', 'charts-custom', 'charts-custom-list')) {
					$response = ($short ? '' : $settings["LMSAppUri"]) . 'dashboard/charts/charts-custom/charts-custom-list';
					$response_key = 'charts';
				}
				// If path doesn't exist, fall through to default structure logic
			}



		}

		return $key ? $response_key : $response;
	}

	public static function allItems($hiddenMenuItems) {

		$structure = \Models\Structure
			::where('hidden', '=', 0)
			->whereNotIn('key', $hiddenMenuItems)
		;


		// If logged in user is not Admin, return only allowed structure pages
		if (!\APP\Auth::isAdmin()) {
			$structure = $structure
				->whereIn('id',
					\Models\RoleStructure
						::where('role_id', '=', \APP\Auth::roleId())
						->where('view', '=', 1) // If page is allowed to be listed
						->select('structure_id')
						->get()
				)
			;
		}

		/*
			REFACTOR THIS PLZ!
			Make it more pretty and readable.
		*/

		// If user is administrator and have access to all learners, show different interface when logged in
		if (
			\APP\Auth::isAdmin() ||
			(
				\APP\Auth::isManager() &&
				\App\Auth::showAdminGUI()
			)
		) {
			$structure->where('id', '!=', 89);
			if (\APP\Auth::isAdmin()) {
				$structure->where('id', '!=', 1); // // Hides "Manage Learning" page.
			}
		} elseif (
			\APP\Auth::isQa()
		) {
			$structure
				->where('id', '!=', 1)
				->where('id', '!=', 77)
			;
		} else {
			$structure
				->where('id', '!=', 77) // Hides "Administer learning" page.
				->where('id', '!=', 89)
			;
		}
		/*
			EOF REFACTOR THIS PLZ!
		*/

		// If user has no additional roles hide "Home" button
		if (
			!\Models\Role::hasRoleAccess() &&
			!\APP\Auth::isAdmin(true)
		) {
			$structure
				->where('id', '!=', 103)
			;
		}

		// If user has no active dashboard, hide dashboard button
		if (!\Models\Dashboard::hasDashboard()) {
			$structure
				->where('id', '!=', 106)
			;
		}

		$assignments = \Models\Assignment::getUserEntries();
		if (
			!$assignments ||
			count($assignments) == 0
		) {
			$structure
				->where('id', '!=', 191)
			;
		}

		$structure = $structure->get();

		return $structure;
	}

	public function resetTable() {
		Capsule::connection()->statement("SET foreign_key_checks = 0");
		Capsule::connection()->table('structure')->truncate();
		Capsule::connection()->statement("SET foreign_key_checks = 0");
		// Build structure here!
	}
}
