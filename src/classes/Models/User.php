<?php

namespace Models;

use Illuminate\Database\Capsule\Manager as DB;
use Illuminate\Database\Capsule\Manager as Capsule;

class User extends \Illuminate\Database\Eloquent\Model
{

	protected $table = 'users';
	protected $casts = [
		'designation_id' => 'integer',
		'country_id' => 'integer',
		'company_id' => 'integer',
		'department_id' => 'integer',
		'location_id' => 'integer',
		'city_id' => 'integer',
		'role_id' => 'integer',
		'shadow_role_id' => 'integer',
		'exclude_from_reports' => 'boolean',
		'exclude_from_ilr_export' => 'boolean',
		'self_attested' => 'boolean',
		'self_attested_reminder_sent' => 'boolean',
		'certified' => 'boolean',
		'exclude_from_emails' => 'boolean',
		'PriorAttain' => 'array',
		'standards.pivot.paused' => 'boolean',
    	'dynamic_fields'=>'array',
    	'ContactPreference'=>'array',
        'startup_instructions_sent' => 'boolean',
        'has_visited_learner_interface'=>'boolean'
	];
	protected $fillable = [
		'ULN',
		'role_id',
		'fname',
		'lname',
		'username',
		'DateOfBirth',
		'email',
		'skype_id',
		'status',
		'created_at',
		'updated_at',
        'company_id'
	];

  protected $hidden = array('password');
	public function FullName()
	{
		return $this->fname . " " . $this->lname;
	}

    public function StripeSubscriptions()
    {
        return $this->hasMany('Models\StripeSubscription', 'user_id', 'id');
    }

    public function licenseSubscriptions()
    {
        return $this->hasMany('Models\StripeSubscription', 'user_id', 'id')->where('stripe_subscriptions.type', 'license');
    }

    public function LicensePlans()
    {
        return $this->hasManyThrough(
            LicensePlan::class,
            StripeSubscription::class,
            'user_id', // Foreign key on StripeSubscription table...
            'id', // Foreign key on LicensePlan table...
            'id', // Local key on User table...
            'type_id' // Local key on StripeSubscription table...
        )->where('stripe_subscriptions.type', 'license')
            ->where('stripe_subscriptions.period_end', '>', date('Y-m-d H:i:s'))
            ->where('stripe_subscriptions.status', 'active');
    }

	public function getFullNameAttribute($value) {
		return $this->fname . " " . $this->lname;
	}

	public function Role() {
		return $this->belongsTo('Models\Role', 'role_id', 'id');
	}

	public function ShadowRole() {
		return $this->belongsTo('Models\Role', 'shadow_role_id', 'id');
	}

	public function Company()
	{
		return $this->belongsTo('Models\Company');
	}

	public function Department()
	{
		return $this->belongsTo('Models\Department');
	}

	public function Location()
	{
		return $this->belongsTo('Models\Location');
	}

	public function Watch() {
		return $this->belongsTo('Models\Watch', 'watch_id', 'id');
	}

	public function Designation() {
		return $this->belongsTo('Models\Designation');
	}

	public function EthnicityObj()
	{
		return $this->belongsTo('Models\Ethnicity', "Ethnicity", "id");
	}

	public function Ethnicity()
	{
		return $this->belongsTo('Models\Ethnicity');
	}

	public function StaffType() {
		return $this->belongsTo('Models\SmcrStaffType', 'staff_type_id', 'id');
	}
	public function StaffFunctionResponsibility() {
		return $this->hasMany('Models\SmcrStaffFunctionResponsibility', 'user_id', 'id');
	}

	public function SmcrCommitteeRolePerson() {
		return $this->hasMany('Models\SmcrCommitteeRolePerson', 'user_id', 'id');
	}


	 public function UserFormValues()
    {
        return $this->hasmanythrough('models\userformvalue', 'models\userform');
    }

    public function dateOfHire()
    {
        return $this->hasOne(CustomFieldValue::class, 'type_id', 'id')
            ->select('custom_field_values.*')
            ->where('custom_field_values.type', 'user')
            ->join('fields', 'fields.id', '=', 'custom_field_values.field_id')
            ->where('fields.slug', 'last_hire_date');
    }

    public function employeeType()
    {
        return $this->hasOne(CustomFieldValue::class, 'type_id', 'id')
            ->select('custom_field_values.*')
            ->where('custom_field_values.type', 'user')
            ->join('fields', 'fields.id', '=', 'custom_field_values.field_id')
            ->where('fields.slug', 'employee_type');
    }

    public function employeeStatus()
    {
        return $this->hasOne(CustomFieldValue::class, 'type_id', 'id')
            ->select('custom_field_values.*') // fields also has value & it was overrding this value
            ->where('custom_field_values.type', 'user')
            ->join('fields', 'fields.id', '=', 'custom_field_values.field_id')
            ->where('fields.slug', 'employee_status');
    }

	public function scopeWithAndWhereHas($query, $relation, $constraint) {
		return $query->whereHas($relation, $constraint)
			->with([$relation => $constraint])
		;
	}

	public function ReportsTo() {
		return $this->belongsTo('Models\User', 'report_to', 'id');
	}
	public function ResponsibleFor() {
		return $this->hasMany('Models\User', 'report_to', 'id');
	}

	public function Country()
	{
		return $this->belongsTo('Models\Country');
	}

	public function City()
	{
		return $this->belongsTo('Models\City');
	}

	public function StandardUserPivot() {
		return $this->belongsTo('Models\ApprenticeshipStandardUser', 'id', 'user_id');
	}
	public function UserActiveProgrammeStatus() {
		return $this->belongsTo('Models\UserCustomProgrammeStatus', 'id', 'user_id')->where('is_active',1);
	}

	public function StandardUserPivotMany() {
		return $this->hasMany('Models\ApprenticeshipStandardUser');
	}

	public function Groups()
  {
		return $this->belongsToMany('Models\Group', 'group_users');
	}

	public function Qualifications() {
		return $this
			->belongsToMany('Models\Qualification', 'qualification_users')
			->whereNull('qualification_users.deleted_at')
		;
	}

	public function Managers()
	{
		return
			$this
				->belongsToMany('Models\User', 'manager_users', 'user_id', 'manager_id')
				->withPivot('id', 'created_by', 'comment_link')
				->whereNull('manager_users.deleted_at')
		;
	}

	public function ManagersTiny(){
		return
			$this
				->belongsToMany('Models\User', 'manager_users', 'user_id', 'manager_id')
				->select('users.id', 'users.fname', 'users.lname', 'users.role_id')
				->whereNull('manager_users.deleted_at')
			;
	}

	public function SubDepartments() {
		return $this->hasMany('Models\UserSubDepartment', 'user_id', 'id');
	}

	public function SubDepartmentsCompact() {
		return
			$this
				->belongsToMany(
					'Models\Department',
					'user_sub_departments',
					'user_id',
					'department_id'
				)
				->where('user_sub_departments.deleted_at', null)
		;
	}


	public function ManagersCompact(){
		return
			$this
				->belongsToMany('Models\User', 'manager_users', 'user_id', 'manager_id')
				->select('users.id', 'users.fname', 'users.lname', 'users.email', 'users.designation_id')
				->with('designation')
				->whereNull('manager_users.deleted_at')
		;
	}

    public function ManagersWithCompanyFormatted(){
        return $this->belongsToMany('Models\User', 'manager_users', 'user_id', 'manager_id')
            ->selectRaw('users.id, CONCAT(users.fname, " ", users.lname, " - ", companies.name) AS name, companies.id')
            ->join('companies', 'users.company_id', '=', 'companies.id')
			->whereNull('manager_users.deleted_at');
    }

	public function Employees()
	{
		return $this->belongsToMany('Models\User', 'manager_users', 'manager_id', 'user_id')->whereNull('manager_users.deleted_at');
	}

	public function Modules() {
		return $this
			->belongsToMany('Models\LearningModule', 'user_learning_modules', 'user_id', 'learning_module_id')
			->whereNull('user_learning_modules.deleted_at')
			->withTimestamps()
		;
	}
	public function CustomReviews() {
		return $this
			->belongsToMany('Models\CustomReview', 'user_custom_reviews', 'user_id', 'custom_review_id')
			->withTimestamps()
		;
	}

	public function Events() {
		return $this
			->belongsToMany('Models\LearningModule', 'user_learning_modules', 'user_id', 'learning_module_id')
			->whereNull('user_learning_modules.deleted_at')
			->with('Type')
			->with('EventType')
			->withTimestamps()
		;
	}


	public function LearningResults() {
		return $this->hasMany('Models\LearningResult');
	}

	public function UserLearningModules() {
		return $this->hasMany('Models\UserLearningModule');
	}


	public function LearningResultArchives() {
		return $this->hasMany('Models\LearningResultArchive', 'user_id', 'id');
	}

	public function UserCustomProgrammeStatuses() {
		return $this->hasMany('Models\UserCustomProgrammeStatus');
	}

	public function ManagerReviews() {
		return $this
			->hasMany('Models\ManagerReview', 'user_id', 'id')
		;
	}

	public function BatchReportManagers() {
		return $this
			->hasMany('Models\BatchReportManager','user_id', 'id')
		;
	}

	public function Competencies()
	{
		return $this
			->belongsToMany('Models\Competency', 'user_competencies', 'user_id', 'competency_id')
			->withPivot('points', 'id', 'badge', 'acquired_at')
		;
	}

	public function Standards() {
		return $this
			->belongsToMany('Models\ApprenticeshipStandard', 'apprenticeship_standards_users', 'user_id', 'standard_id')
			->withPivot(
				'id', 'status', 'completed_at', 'completion_status', 'percentage', 'percentage_time', 'start_at',
				'due_at', 'working_hours', 'working_hours_custom', 'deleted_at', 'paused', 'paused_start', 'paused_end',
				'ilr_link','criteria_completion', 'sign_off_trainee', 'sign_off_trainee_at', 'sign_off_manager',
				'sign_off_manager_at', 'sign_off_manager_by', 'manager_refused_time', 'manager_refused_by',
				'time_spent_last_log', 'time_spent'
			)
			->where('apprenticeship_standards_users.deleted_at', null)
		;
	}



	public function Evidence()
	{
		return $this
			->hasMany('Models\LearningModule', 'created_by', 'id')
			->where("type_id", "=", "7")
		;
	}

	public function AcquiredCompetencies()	// not working!
	{
		return $this
			->competencies()
			->where("user_competencies.points", ">=", "competencies.required_points")
		;
	}

	public function SkillScans() {
		return $this->hasMany('Models\SkillScan');
	}

	public function LearningDeliveries() {
		return $this->hasMany('Models\IlrLearningDelivery');
	}

	public function ApprenticeshipIssuesUserLearningModules() {
		return $this->hasMany('Models\ApprenticeshipIssuesUserLearningModules');
	}

	public function SmcrReports() {
		return $this->hasMany('Models\SmcrReport');
	}

	public function Schedules() {
		return $this
			->hasMany('Models\ScheduleLink', 'link_id', 'id')
			->where('type', 'users')
		;
	}

	public function ManagerSchedules() {
		return $this
			->hasMany('Models\ScheduleLink', 'link_id', 'id')
			->where('type', 'managers')
		;
	}


	public function ScheduleLink() {
		return $this
			->hasOne('Models\ScheduleLink', 'link_id', 'id')
			->where('type', 'users')
		;
	}

	/**
	 * Not checking status of schedule link
	 */
	public function ScheduleLinkOfUser() {
		return $this
			->hasOne('Models\ScheduleLink', 'link_id', 'id')
		;
	}


	public function ManagerScheduleLink() {
		return $this
			->hasOne('Models\ScheduleLink', 'link_id', 'id')
			->where('type', 'managers')
		;
	}

	public function WaitingScheduleLink() {
		return $this
			->hasOne('Models\ScheduleLink', 'link_id', 'id')
			->where('type', 'users_queue')
		;
	}

	public function ScheduleLinks() {
		return $this
			->hasMany('Models\ScheduleLink', 'link_id', 'id')
			->where('type', 'users')
		;
	}

	public function ScheduleLinksAndWaiting() {
		return $this
			->hasMany('Models\ScheduleLink', 'link_id', 'id')
			->whereIn('type', ['users', 'users_queue'])
		;
	}

	public function ExtraFields() {
		return $this->belongsTo('Models\UserExtraField', 'id', 'user_id');
	}

	public function ExtensionFields() {
		return $this
			->hasMany('Models\TableExtension', 'table_id', 'id')
			->where("table", "users")
		;
	}

	public function Employer() {
		return $this
			->hasOne('Models\TableExtension', 'table_id', 'id')
			->where("table", "users")
			->where('name', 'Employer')
		;
	}

	public function Provider() {
		return $this
			->hasOne('Models\TableExtension', 'table_id', 'id')
			->where("table", "users")
			->where('name', 'Provider')
		;
	}

	public function DevelopmentCoach() {
		return $this
			->hasOne('Models\TableExtension', 'table_id', 'id')
			->where("table", "users")
			->where('name', 'DevelopmentCoach')
		;
	}

	public function UserCustomReview() {
		return $this
			->hasOne('Models\UserCustomReview', 'user_id', 'id')
		;
	}

	public function CreatedBy() {
		return $this->belongsTo('Models\User', 'created_by', 'id');
	}

	public function AssignedDocumentTemplate() {
		return $this
			->belongsToMany('Models\DocumentTemplate', 'assignments', 'source_table_id', 'link_table_id')
			->where('source_table', 'users')
			->where('link_table', 'document_templates')
			->whereNull('assignments.deleted_at')
			->withTimestamps()
		;
	}

	public function AssignedFormWorkflow() {
		return $this
			->belongsToMany('Models\FormWorkflow', 'assignments', 'source_table_id', 'link_table_id')
			->where('source_table', 'users')
			->where('link_table', 'form_workflow')
			->whereNull('assignments.deleted_at')
			->withTimestamps()
		;
	}

	public function AccountType() {
		return $this
			->hasOne('Models\Picklist', 'id', 'account_type_id')
			->where('type', 'account_type')
		;
	}
    public function AccountTypeVal() {
        return $this->belongsTo('Models\Picklist', 'account_type_id', 'id');
    }

	// Delete users and everything related to them!
	public static function deleteUser($id, $settings) {

		\Models\IlrLearningDeliveryMonitoring
			::where('user_id', $id)
			->forceDelete()
		;
		\Models\IlrLearningDeliveryFinancialRecord
			::where('user_id', $id)
			->forceDelete()
		;

		\Models\IlrLearningDelivery
			::where('user_id', $id)
			->forceDelete()
		;

		\Models\ManagerGroup
			::where('manager_id', $id)
			->forceDelete()
		;

		\Models\IlrLearningDeliveryMonitoring
			::where('user_id', $id)
			->forceDelete()
		;

		\Models\IlrLearnerEmploymentStatus
			::where('user_id', $id)
			->forceDelete()
		;

		\Models\IlrLearnerDestinationAndProgression
			::where('user_id', $id)
			->forceDelete()
		;


		\Models\UserLearningModule
			::where("user_id", $id)
			->forceDelete()
		;

		\Models\UserLearningModuleArchive
			::where("user_id", $id)
			->forceDelete()
		;

		\Models\TableExtension
			::where("table_id", $id)
			->where('table', 'users')
			->forceDelete()
		;

		\Models\Survey
			::where("user_id", $id)
			->forceDelete()
		;

		$learning_results = \Models\LearningResult
			::where("user_id", $id)
			->withTrashed()
			->pluck('id')
			->toArray()
		;

		\Models\LearningResultsComment
			::whereIn("learning_results_id", $learning_results)
			->forceDelete()
		;

		\Models\LearningResultsComment
			::where("comment_by_user_id", $id)
			->forceDelete()
		;

		\Models\LearningResultsComment
			::where("created_for_user_id", $id)
			->forceDelete()
		;

		\Models\SkillScanAnswer
			::whereIn('skill_scan_id',
				\Models\SkillScan
					::select('id')
					->where('user_id', $id)
					->get()
			)
			->forceDelete()
		;

		\Models\SkillScanHistory
			::where("user_id", $id)
			->forceDelete()
		;

		\Models\SkillScanHistory
			::whereIn("skill_scan_id",
				\Models\SkillScan
					::select('id')
					->where("user_id", $id)
					->get()
			)
			->forceDelete()
		;

		\Models\SkillScan
			::where("user_id", $id)
			->forceDelete()
		;

		\Models\QaLearningResult
			::whereIn('learning_result_id', $learning_results)
			->forceDelete()
		;

		\Models\LearningResult
			::where("user_id", $id)
			->withTrashed()
			->forceDelete()
		;

		\Models\Scorm\Track::where("userid", $id)
			->forceDelete()
		;

		\Models\Scorm\Log::where("userid", $id)
			->forceDelete()
		;

		\Models\Scorm\RoleAssignment::where("userid", $id)
			->forceDelete()
		;

		$assessment_tasks = \Models\Assessment\Task
			::where("user_id", $id)
			->pluck('id')
			->toArray()
		;

		\Models\Assessment\TaskComment
			::whereIn("assessment_task_id", $assessment_tasks)
			->forceDelete()
		;

		\Models\Assessment\TaskComment
			::where("user_id", $id)
			->forceDelete()
		;

		\Models\Assessment\TaskComment
			::whereIn('assessment_task_id',
				\Models\Assessment\Task
					::select('id')
					->where('reporter_id', $id)
					->get()
			)
			->forceDelete()
		;

		\Models\Assessment\Task
			::where("user_id", $id)
			->forceDelete()
		;

		\Models\Assessment\Task
			::where("reporter_id", $id)
			->forceDelete()
		;

		\Models\EmailQueue
			::where("from", $id)
			->forceDelete()
		;

		\Models\Assessment\Data
			::where("user_id", $id)
			->forceDelete()
		;

		\Models\ApprenticeshipStandardUser
			::where("user_id", $id)
			->forceDelete()
		;

		$manager_reviews = \Models\ManagerReview
			::where("user_id", $id)
			->pluck('id')
			->toArray()
		;
		\Models\ManagerReviewsComment
			::whereIn("review_id", $manager_reviews)
			->forceDelete()
		;

		\Models\ManagerReview
			::where("user_id", $id)
			->forceDelete()
		;

		\Models\ManagerUser
			::where("user_id", $id)
			->orWhere('manager_id', $id)
			->forceDelete()
		;

		\Models\GroupUser
			::where("user_id", $id)
			->forceDelete()
		;

		\Models\UserCompetency
			::where("user_id", $id)
			->forceDelete()
		;

		\Models\LearningSession
			::where("user_id", $id)
			->forceDelete()
		;

		$evidences = \Models\LearningModuleEvidence
			::where("user_id", $id)
			->get()
		;
		foreach ($evidences as $key => $evidence) {
			if (is_file($settings["LMSEvidencePath"] . $evidence->evidence)) {
				unlink($settings["LMSEvidencePath"] . $evidence->evidence);
			}
			if (is_file($settings["LMSEvidencePath"] . $evidence->hash . '.' . $evidence->extension)) {
				unlink($settings["LMSEvidencePath"] . $evidence->hash . '.' . $evidence->extension);
			}
		}
		\Models\LearningModuleEvidence
			::where("user_id", $id)
			->forceDelete()
		;


		\Models\LearningModuleEvidenceMeeting
			::where("user_id", $id)
			->forceDelete()
		;

		\Models\ApprenticeshipIssuesEvidence
			::where('user_id', $id)
			->forceDelete()
		;

		\Models\ApprenticeshipIssuesUserDisabled
			::where('user_id', $id)
			->forceDelete()
		;

		\Models\ApprenticeshipIssuesUserLearningModules
			::where("user_id", $id)
			->forceDelete()
		;

		\Models\SchedulePermission
			::where("user_id", $id)
			->forceDelete()
		;


		\Models\LearningModulePrerequisite
			::whereIn('learning_module_id',
				\Models\LearningModule
					::where("created_by", $id)
					->select('id')
					->where(function ($query) use ($id) {
						$query = $query
							->whereIn('type_id',
								\Models\LearningModuleType
									::select('id')
									->where('slug', 'upload')
									->orWhere('slug', 'blog_entry')
									->get()
							)
							->whereDoesntHave('ApprenticeshipIssuesUserLearningModules')
							->whereDoesntHave('ApprenticeshipIssuesLearningModules')
							->whereDoesntHave('ApprenticeshipIssuesEvidences')
							->whereDoesntHave('LearningModuleEvidences')
							->whereDoesntHave('LearningResult')
							->whereDoesntHave('Course')
						;
					})
					->get()
			)
			->orWhereIn('prerequisite_id',
				\Models\LearningModule
					::where("created_by", $id)
					->select('id')
					->where(function ($query) {
						$query = $query
							->where(function ($query) {
								$query = $query
									->whereIn('type_id',
										\Models\LearningModuleType
											::select('id')
											->where('slug', 'upload')
											->orWhere('slug', 'blog_entry')
											->get()
									)
									->whereDoesntHave('ApprenticeshipIssuesUserLearningModules')
									->whereDoesntHave('ApprenticeshipIssuesLearningModules')
									->whereDoesntHave('ApprenticeshipIssuesEvidences')
									->whereDoesntHave('LearningModuleEvidences')
									->whereDoesntHave('LearningResult')
									->whereDoesntHave('Course')
								;
							})
						;
					})
					->get()
			)
			->forceDelete()
		;


		\Models\CompanyResourceInterestedIn
			::whereIn('learning_module_id',
				\Models\LearningModule
					::where("created_by", $id)
					->select('id')
					->where(function ($query) use ($id) {
						$query = $query
							->whereIn('type_id',
								\Models\LearningModuleType
									::select('id')
									->where('slug', 'upload')
									->orWhere('slug', 'blog_entry')
									->get()
							)
							->whereDoesntHave('ApprenticeshipIssuesUserLearningModules')
							->whereDoesntHave('ApprenticeshipIssuesLearningModules')
							->whereDoesntHave('ApprenticeshipIssuesEvidences')
							->whereDoesntHave('LearningModuleEvidences')
							->whereDoesntHave('LearningResult')
							->whereDoesntHave('Course')
						;
					})
					->get()
			)
			->forceDelete()
		;

		$learning_modules_delete = \Models\LearningModule
			::where("created_by", $id)
			->select('id')
			->where(function ($query) {
				$query = $query
					->where(function ($query) {
						$query = $query
							->whereIn('type_id',
								\Models\LearningModuleType
									::select('id')
									->where('slug', 'upload')
									->orWhere('slug', 'blog_entry')
									->get()
							)
							->whereDoesntHave('ApprenticeshipIssuesUserLearningModules')
							->whereDoesntHave('ApprenticeshipIssuesLearningModules')
							->whereDoesntHave('ApprenticeshipIssuesEvidences')
							->whereDoesntHave('LearningModuleEvidences')
							->whereDoesntHave('LearningResult')
							->whereDoesntHave('Course')
						;
					})
				;
			})
			->withTrashed()
			->get()
		;

		foreach ($learning_modules_delete as $key => $learning_module_delete) {
			\Models\DepartmentLearningModule
				::where('learning_module_id', $learning_module_delete->id)
				->forceDelete()
			;

			// remove LearningModuleVersion
			$module_versions = \Models\LearningModuleVersion
				::where('learning_module_id', $learning_module_delete->id)
				->get()
			;
			foreach ($module_versions as $key => $module_version) {
				$module_version->forceDelete();
			}

			// remove LearningModuleEvidences, if missed by miracle
			$module_evidences = \Models\LearningModuleEvidence
				::where('learning_modules_id', $learning_module_delete->id)
				->get()
			;
			foreach ($module_evidences as $key => $module_evidence) {
				// If file exists, delete it, then db entry
				if ($module_evidence->evidence_type == 'file') {
					$filename = $settings["LMSEvidencePath"] . $module_evidence->hash . '.' . $module_evidence->extension;
					if (is_file($filename)) {
						unlink($filename);
					}
				}
				$module_evidence->forceDelete();
			}

			// remove ApprenticeshipIssuesUserLearningModules
			\Models\ApprenticeshipIssuesUserLearningModules
				::where('learning_modules_id', $learning_module_delete->id)
				->forceDelete()
			;

			// remove Feedback
			\Models\ModuleFeedback
				::where('module_id', $learning_module_delete->id)
				->forceDelete()
			;

			// remove Files
			$files = \Models\File
				::where('table_name', 'learning_modules')
				->where('table_row_id', $learning_module_delete->id)
				->get()
			;
			foreach ($files as $key => $file) {
				$filename = $settings["LMSFilePath"] . $file->hash . '.' . $file->extension;
				if (is_file($filename)) {
					unlink($filename);
				}
				$file->forceDelete();
			}

			// remove thumbnails, promo, highlight
			$images = [
				$settings['LMSThumbPath'] .'/'. $learning_module_delete->thumbnail,
				$settings['LMSPromoPath'] .'/'. $learning_module_delete->promo_image,
				$settings['LMSHighlightPath'] .'/'. $learning_module_delete->highlight_image,
			];
			foreach ($images as $key => $image) {
				if (is_file($image)) {
					unlink($image);
				}
			}

			// check ApprenticeshipIssuesLearningModules
			\Models\ApprenticeshipIssuesLearningModules
				::where('learning_modules_id', $learning_module_delete->id)
				->forceDelete()
			;

			// check ApprenticeshipIssuesEvidence
			\Models\ApprenticeshipIssuesEvidence
				::where('learning_modules_id', $learning_module_delete->id)
				->forceDelete()
			;

			$learning_module_delete->forceDelete();
		}

		\Models\Session
			::where("user_id", $id)
			->forceDelete()
		;

		\Models\ForgottenPasswordToken
			::where("user_id", $id)
			->forceDelete()
		;

		// Update created modules created_by field with null

		\Models\LearningModule
			::where("created_by", $id)
			->withTrashed()
			->update(["created_by" => null])
		;

		\Models\ManagerLearningModule
			::where("manager_id", $id)
			->forceDelete()
		;

		\Models\ManagerDepartment
			::where("manager_id", $id)
			->forceDelete()
		;

		\Models\ModuleFeedback
			::where("user_id", $id)
			->forceDelete()
		;

		// Containers, remove created_by
		\Models\LearningModuleContainer
			::where("created_by", $id)
			->update(["created_by" => null])
		;

		\Models\ErrorLog
			::where("user_id", $id)
			->forceDelete()
		;

		\Models\Scorm\UserLastAccess
			::where("userid", $id)
			->forceDelete()
		;

		\Models\Scorm\UserLastAccess
			::where("userid", $id)
			->forceDelete()
		;

		\Models\SmcrCommitteeRolePerson
			::where("user_id", $id)
			->forceDelete()
		;

		\Models\SmcrReport
			::where("user_id", $id)
			->forceDelete()
		;

		\Models\Comment
			::where("added_by", $id)
			->forceDelete()
		;

		\Models\CompanyComment
			::where("user_id", $id)
			->forceDelete()
		;

		\Models\IlrTemplates
			::where("created_by", $id)
			->forceDelete()
		;

		\Models\LearningModuleEvidenceMeeting
			::where("approved_by", $id)
			->update(['approved_by' => null])
		;

		\Models\Favorite
			::where("user_id", $id)
			->forceDelete()
		;

		\Models\UserGatewayReadiness
			::where("user_id", $id)
			->forceDelete()
		;

		$files = \Models\File
			::where("added_by", $id)
			->get()
		;
		foreach ($files as $key => $file) {
			$filename = $settings["LMSFilePath"] . $file->hash . '.' . $file->extension;
			if (is_file($filename)) {
				unlink($filename);
			}
			$file->forceDelete();
		}

		\Models\QaLearningResult
			::where("qa_id", $id)
			->forceDelete()
		;

		\Models\QualityControl
			::where("user_id", $id)
			->orWhere("qa_user_id", $id)
			->forceDelete()
		;

		\Models\LearningResult
			::where("user_id", $id)
			->withTrashed()
			->forceDelete()
		;

		\Models\UserCustomReview
			::where("user_id", $id)
			->forceDelete()
		;

		\Models\Topic
			::where("added_by", $id)
			->update(['added_by' => null])
		;

		\Models\Topic
			::where("added_by", $id)
			->onlyTrashed()
			->forceDelete()
		;

		\Models\UserSubDepartment
			::where("user_id", $id)
			->withTrashed()
			->forceDelete()
		;

		\Models\ManagerCompany
			::where("manager_id", $id)
			->forceDelete()
		;

		\Models\EmailHistory
			::where("user_id", $id)
			->forceDelete()
		;

		\Models\UserLearningRecommendations
			::where('user_id', $id)
			->forceDelete()
		;

		\Models\SmcrStaffFunctionResponsibility
			::where('user_id', $id)
			->forceDelete()
		;

		\Models\UserForm
			::where('user_id', $id)
			->withTrashed()
			->forceDelete()
		;
		\Models\UserWorkflowForm
			::where('user_id', $id)
			->withTrashed()
			->forceDelete()
		;

		\Models\LearningResult
			::where("sign_off_manager_by", $id)
			->update(['sign_off_manager_by' => null])
		;

		\Models\UserFormAssignedType
			::where('user_id', $id)
			->withTrashed()
			->forceDelete()
		;

		\Models\UserBadge
			::where('user_id', $id)
			->withTrashed()
			->forceDelete()
		;

		if (Capsule::schema()->hasTable('visible_learning_modules')) {
			DB::table('visible_learning_modules')->where('user_id', $id)->delete();
		}

		if (Capsule::schema()->hasTable('manager_programmes')) {
			DB::table('manager_programmes')->where('manager_id', $id)->delete();
		}

		// AND DELETE USERS!
		\Models\User
			::where("id", $id)
			->forceDelete()
		;
		\Models\Scorm\User
			::where("id", $id)
			->forceDelete()
		;
	}

	// Calculate user's overall progress of assigned resources, time spent, percentages, etc
	// https://bitbucket.org/emilrw/scormdata/issues/574/2-view-training-data-by-employee
	public static function calculateGlobalProgress($user_ids = false, $time_calc = false, $settings = false) {
		if ($user_ids && !is_array($user_ids)) {
			$user_ids = [$user_ids];
		}

		$learning_modules_stats = \collect([]);
		$query = \Models\User
			::select('users.*')
			->with('stafftype')
			->with('managerreviews')
			->validuser()
		;

		if (
			$user_ids &&
			is_array($user_ids)
		) {
			$query->whereIn('id', $user_ids);
		}


		$query->chunkById(100, function ($users) use (&$learning_modules_stats, $time_calc)  {
			foreach ($users as $user) {

				$user_learningresults = \Models\LearningResult
					::select('learning_results.*')
					->where('learning_results.user_id', $user->id)
					->where('learning_results.refreshed', 0)
					->with(['module' => function ($query) {
						$query
							->with('fpcategory')
							->validresource()
						;
					}])
					->whereNotExists(function ($query) {
						$query
							->select(DB::raw(1))
							->from('schedule_links')
							->where('schedule_links.type', 'lesson')
							->whereNull('schedule_links.deleted_at')
							->where('schedule_links.status', 1)
							->whereRaw('schedule_links.link_id = learning_results.learning_module_id')
							->whereExists(function ($query) {
								$query
									->select(DB::raw(1))
									->from('schedule_links as user_schedule_link')
									->whereIn('user_schedule_link.type', ['users', 'users_queue'])
									->where('user_schedule_link.status', 1)
									->whereNull('user_schedule_link.deleted_at')
									->whereRaw('user_schedule_link.link_id = learning_results.user_id')
								;
							})
							->join("schedules", function ($join) {
								$join
									->on("schedules.id", "=", "schedule_links.schedule_id")
									->where('schedules.status', 1)
									->whereNull('schedules.deleted_at')
								;
							})
						;
					})
					->join("learning_modules", function ($join) {
						$join
							->on("learning_modules.id", "=", "learning_results.learning_module_id")
							->whereRaw('learning_modules.deleted_at is null')
							->whereRaw('learning_modules.status = 1')
							->whereRaw('learning_modules.track_progress = 1')
							->where('learning_modules.visible_learner', true)
							->where(function ($query) {
								$query = $query
									->where(function ($query) {
										$query = $query
											->where('learning_modules.is_course', 1)
											->where('open_in_events_only', false)
											->where('created_by_event', 0)
										;
									})
									->orWhere('learning_modules.is_course', 0)
								;
							})
						;
					})
					->join("user_learning_modules", function ($join) {
						$join
							->on("user_learning_modules.user_id", "=", "learning_results.user_id")
							->on("user_learning_modules.learning_module_id", "=", "learning_results.learning_module_id")
							->whereNull('user_learning_modules.deleted_at')
						;
					})
					->get()
				;

				$not_started_resources = 0;
				$in_progress_resources = 0;
				$completed_resources = 0;
				$total_resources = 0;
				$completed = 0;

				$not_started_resources_smcr = 0;
				$in_progress_resources_smcr = 0;
				$completed_resources_smcr = 0;
				$total_resources_smcr = 0;
				$completed_smcr = 0;

				foreach ($user_learningresults as $learningresult) {

					$total_resources++;
					if ($learningresult->completion_status == 'completed') {
						$completed_resources++;
					}
					if ($learningresult->completion_status == 'in progress') {
						$in_progress_resources++;
					}
					if ($learningresult->completion_status == 'not attempted') {
						$not_started_resources++;
					}

					// If SMCR, calculate only resources assigned to F&P category.
					if ($GLOBALS["CONFIG"]->licensing['isSMCR']) {
						if (
							$learningresult->module &&
							$learningresult->module->fpcategory &&
							$learningresult->module->fpcategory->status == 1
						) {
							$total_resources_smcr++;
							if ($learningresult->completion_status == 'completed') {
								$completed_resources_smcr++;
							}
							if ($learningresult->completion_status == 'in progress') {
								$in_progress_resources_smcr++;
							}
							if ($learningresult->completion_status == 'not attempted') {
								$not_started_resources_smcr++;
							}
						}
					}
				}

				// Calculate totals!
				if ($completed_resources > 0) {
					$completed = (100 / $total_resources) * $completed_resources;
				}
				if ($completed_resources_smcr > 0) {
					$completed_smcr = (100 / $total_resources_smcr) * $completed_resources_smcr;
				}

				$user->total_resources = $total_resources;
				$user->not_started_resources = $not_started_resources;
				$user->in_progress_resources = $in_progress_resources;
				$user->completed_resources = $completed_resources;
				$user->completed = number_format($completed, 2);

				if ($GLOBALS["CONFIG"]->licensing['isSMCR']) {
					$user->total_resources_smcr = $total_resources_smcr;
					$user->not_started_resources_smcr = $not_started_resources_smcr;
					$user->in_progress_resources_smcr = $in_progress_resources_smcr;
					$user->completed_resources_smcr = $completed_resources_smcr;
					$user->completed_smcr = number_format($completed_smcr, 2);
				}

				if ($time_calc) {
					// Try to calculate time
					$time_spent = 0;
					$time_spent_off_the_job_training = 0;
					foreach ($user_learningresults as $result) {
						if (
							$result->completion_status == 'completed' &&
							$result->module
						) {
							if(!$learning_modules_stats->has($result->learning_module_id)) //check learning module exist in array
							{
								$fields_obj = new \stdClass();
								$fields_obj->total_duration = 0;
								$fields_obj->total_users = 0;
								$learning_modules_stats->put($result->learning_module_id, $fields_obj); //Intialize the array
							}
							$learning_modules_stats[$result->learning_module_id]->total_users++;
							//store total hours and get nunber of completed
							// pick e-learning only, get time from scorm track

							// Check if learning reesult or resource has duration, use that or use scom track.
							if (
								$result->duration_hours > 0 ||
								$result->duration_minutes > 0
							) {
								if ($result->off_the_job_training) {
									$time_spent_off_the_job_training = $time_spent_off_the_job_training + ($result->duration_hours * 60);
									$time_spent_off_the_job_training = $time_spent_off_the_job_training + $result->duration_minutes;
								} else {
									$time_spent = $time_spent + ($result->duration_hours * 60);
									$time_spent = $time_spent + $result->duration_minutes;
								}
								$learning_modules_stats[$result->learning_module_id]->total_duration+=($result->duration_hours * 60)+$result->duration_minutes;
							} else if (
								$result->module->duration_hours > 0 ||
								$result->module->duration_minutes > 0
							) {
								if ($result->off_the_job_training) {
									$time_spent_off_the_job_training = $time_spent_off_the_job_training + ($result->module->duration_hours * 60);
									$time_spent_off_the_job_training = $time_spent_off_the_job_training + $result->module->duration_minutes;
								} else {
									$time_spent = $time_spent + ($result->module->duration_hours * 60);
									$time_spent = $time_spent + $result->module->duration_minutes;
								}
								$learning_modules_stats[$result->learning_module_id]->total_duration+=($result->module->duration_hours * 60)+ $result->module->duration_minutes;
							}
							if ($result->module->type_id == 1) {
								$track = \Models\Scorm\Track::where("scormid", $result->learning_module_id)
									->where("userid", $user->id)
									->where("element", "cmi.core.total_time")
									->first()
								;
								if ($track && $track->value) {
									$track_duration = explode(":", $track->value);
									if (isset($track_duration[0])) {
										if ($result->off_the_job_training) {
											$time_spent_off_the_job_training = $time_spent_off_the_job_training + ($track_duration[0] * 60);
										} else {
											$time_spent = $time_spent + ($track_duration[0] * 60);
										}
										$result->duration_scorm = $track_duration[0] * 60;
										$learning_modules_stats[$result->learning_module_id]->total_duration+=($track_duration[0] * 60);
										if (isset($track_duration[1])) {
											if ($result->off_the_job_training) {
												$time_spent_off_the_job_training = $time_spent_off_the_job_training + $track_duration[1];
											} else {
												$time_spent = $time_spent + $track_duration[1];
											}
											$result->duration_scorm = ($track_duration[0] * 60) + $track_duration[1];
											$learning_modules_stats[$result->learning_module_id]->total_duration+=($track_duration[0] * 60) + $track_duration[1];
										}
										$result->saveWithoutEvents();
									}
								}
							}
						}
					}
					foreach ($user->managerreviews as $managerreview) {
						if ($managerreview->off_the_job_training) {
							$time_start = \Carbon\Carbon::parse($managerreview->time_start);
							$time_end = \Carbon\Carbon::parse($managerreview->time_end);
							$difference = $time_start->diffInMinutes($time_end);
							if ($difference > 0) {
								$time_spent_off_the_job_training = $time_spent_off_the_job_training + $difference;
							}
						} else {
							$time_start = \Carbon\Carbon::parse($managerreview->time_start);
							$time_end = \Carbon\Carbon::parse($managerreview->time_end);
							$difference = $time_start->diffInMinutes($time_end);
							if ($difference > 0) {
								$time_spent = $time_spent + $difference;
							}
						}
					}

					$user->time_spent = number_format(($time_spent / 60), 2);
					$user->time_spent_off_the_job_training = number_format(($time_spent_off_the_job_training / 60), 2);
				}

				// If SMCR, check if valid certificate, if it has been a year, if so, move into archive.
				// also set user status to not-started.
				// Do not refresh resources!


				if (
					$GLOBALS["CONFIG"]->licensing['isSMCR'] &&
					$user->staff_type_id > 0
				) {
					// Get specific user roles timings.
					$timing_key = false;
					switch ($user->staff_type_id) {
						case 1: // Standard (Conduct Rules)
							$timing_key = 'smcr_standard_conduct_rules_reassessed';
							break;
						case 2: // Certification Staff
							$timing_key = 'smcr_certification_staff_reassessed';
							break;
						case 3: // Senior Manager
							$timing_key = 'smcr_senior_manager_reassessed';
							break;
					}
					if ($timing_key) {
						$timing = \Models\Timing::where('key', $timing_key)
							->first()
						;
					}

					// Get list of exipred completed report/s for user
					$expired_completed_reports = \Models\SmcrReport
						::where('user_id', $user->id)
						->where('status', true)
						->where('completion_status', 'Completed')
						->where('expire_at', '<=', \Carbon\Carbon::now())
						->where('type_id', 1)
						->get()
					;

					// Set them all to archived!
					// Only if Timing is specified
					if ($timing->timing > 0) {
						foreach ($expired_completed_reports as $report) {
							$update_report = \Models\SmcrReport::find($report->id);
							$update_report->completion_status = 'Archived';
							$update_report->save();
						}

						// Reset conduct rules learning, if there was completed learning resource!
						if (
							count($expired_completed_reports) > 0 &&
							$user->staff_type_id == 1
						) {
							\APP\Smcr::resetUserLearning($user, 'Calculate global progress, reset Conduct rules learning, if expired_completed_reports > 0.');
							$user->not_started_resources_smcr = $user->completed_resources_smcr;
							$user->in_progress_resources_smcr = 0;
							$user->completed_resources_smcr = 0;
							$user->completed_smcr = 0;
							$user->completed_resources = 0;
							$user->completed = 0;

							$template = \Models\EmailTemplate
								::where('name', 'Conduct Rules Staff member needs to be (re)trained')
								->where('status', true)
								->first()
							;
							// Send out only if user needs to be re-certificated! Has something in archive.
							if ($template) {
								$email_queue = new \Models\EmailQueue;
								$email_queue->email_template_id = $template->id;
								$email_queue->recipients = [$user->id];
								$email_queue->custom_variables = json_encode([
									'SMCR_TYPE' => $user->stafftype->name,
								]);
								$email_queue->save();
							}
						}
					}

					// Get all completed reports
					$completed_reports = \Models\SmcrReport
						::where('user_id', $user->id)
						->where('status', true)
						->where('completion_status', 'Completed')
						->where('type_id', 1)
						->count()
					;

					// Get all archived reports
					$archived_reports = \Models\SmcrReport
						::where('user_id', $user->id)
						->where('status', true)
						->where('completion_status', 'Archived')
						->where('type_id', 1)
						->count()
					;

					// Get all incomplete reports.
					$incomplete_certificate = \Models\SmcrReport
						::where('user_id', $user->id)
						->where('status', true)
						->where('completion_status', '!=', 'Completed')
						->where('completion_status', '!=', 'Archived')
						->where('type_id', 1)
						->first()
					;

					// Reset certificate if time is past expire time, archive old one and create new one!
					if (
						!$incomplete_certificate &&
						$completed_reports == 0 &&
						isset($timing->timing) &&
						$timing->timing > 0
					) {
						$incomplete_certificate = new \Models\SmcrReport;
						$incomplete_certificate->type_id = 1;
						$incomplete_certificate->user_id = $user->id;
						$incomplete_certificate->start_at = \Carbon\Carbon::now();
						$incomplete_certificate->expire_at = \Carbon\Carbon::now()->addDays($timing->timing);
						$incomplete_certificate->completion_status = 'Not Started';
						$incomplete_certificate->save();


						// Force refresh all user's assigned resources.
						// Will do this to completed ones, so far.


						// Send e-mail to user "needs to be (re)assessed".
						switch ($user->staff_type_id) {
							/*
							case 1: // Standard (Conduct Rules)
									$template_name = 'Conduct Rules Staff member needs to be (re)trained';
								break;
								*/

							case 2: // Certification Staff
								$template_name = 'Certification Staff member needs to be (re)assessed';
								break;

							case 3: //Senior Manager
								$template_name = 'Senior Manager role needs to be (re)assessed';
								break;
						}
						if (isset($template_name)) {
							$template = \Models\EmailTemplate
								::where('name', $template_name)
								->where('status', true)
								->first()
							;
							// Send out only if user needs to be re-certificated! Has something in archive.
							if (
								$template &&
								$archived_reports > 0
							) {
								$email_queue = new \Models\EmailQueue;
								$email_queue->email_template_id = $template->id;
								$email_queue->recipients = [$user->id];
								$email_queue->custom_variables = json_encode([
									'SMCR_TYPE' => $user->stafftype->name,
								]);
								$email_queue->save();
							}
						}

						// Those are reset upon generating new report/cerificate.
						$user->last_completion_date = $incomplete_certificate->start_at;
						$user->next_completion_date = $incomplete_certificate->expire_at;
						$user->self_attested = false;
						$user->certified = false;
					}


					// If user has 0 completed and 0 in progress resources, set to Not Started
					if (
						$user->completed_resources_smcr == 0 &&
						$user->in_progress_resources_smcr == 0 &&
						$completed_reports == 0
					) {
						$user->learning_status = 'Not Started';
					}

					// If user has started resources set to in progress
					if (
						(
							$user->in_progress_resources_smcr > 0 ||
							(
								$user->not_started_resources_smcr > 0 &&
								$user->completed_resources_smcr > 0
							)
						) &&
						$completed_reports == 0
					) {
						$user->learning_status = 'In Progress';
					}

					// If user has no progress and no started resources, but has completed resources or 0 resources!, set status to Completed.
					if (
						(
							$user->completed_resources_smcr > 0 &&
							$user->in_progress_resources_smcr == 0 &&
							$user->not_started_resources_smcr == 0
						) ||
						$user->total_resources_smcr == 0
					) {
						$user->learning_status = 'Completed';
					}

					// Update incomplete certificate status with progress
					if ($incomplete_certificate) {
						if ($user->learning_status == 'Not Started') {
							$incomplete_certificate->completion_status = 'Not Started';
							$incomplete_certificate->save();
						}

						if ($user->learning_status == 'In Progress') {
							$incomplete_certificate->completion_status = 'In Progress';
							$incomplete_certificate->save();
						}

						// Completed status to report/certificate will be set manualy by manager after user has self attested!
						if ($user->learning_status == 'Completed') {
							// Send out "Self-Attestation Alert" to user, if "self_attested_reminder_sent" is false and set "self_attested_reminder_sent" to true, when user self attests, self_attested_reminder_sent is set to false.
							if (
								!$user->self_attested_reminder_sent &&
								!$user->self_attested
							) {
								$template_slug = false;
								switch ($user->staff_type_id) {
									case 2: // Certification Staff
										$template_slug = 'certification_staff_member_self_attestation_alert';
										break;
									case 3: // Senior Manager
										$template_slug = 'senior_manager_self_attestation_alert';
										break;
								}
								if ($template_slug) {
									$template = \Models\EmailTemplate::getTemplate($template_slug);
									if ($template) {
										$email_queue = new \Models\EmailQueue;
										$email_queue->email_template_id = $template->id;
										$email_queue->recipients = [$user->id];
										$email_queue->save();

										$user->self_attested_reminder_sent = true;
									}
								}
							}

							// If conduct rules, refresh all learning as certificate was reneved and all resources are completed.
							if ($user->staff_type_id == 1) {
								// If incomplete certificate expire_at is in the past, set it to completed and generate new one!
								$incomplete_certificate->completion_status = 'Completed';
								$incomplete_certificate->save();
							}

							// Refresh ONLY if timing is set more than 0
							if (
								$user->staff_type_id == 1 &&
								isset($timing->timing) &&
								$timing->timing > 0
							) {
								// Due to timing value being 0, start_at and expire_at are identical, fix it!
								if (
									$incomplete_certificate->start_at == $incomplete_certificate->expire_at &&
									isset($timing->timing) &&
									$timing->timing > 0
								) {
									$incomplete_certificate->expire_at = \Carbon\Carbon::parse($incomplete_certificate->start_at)->addDays($timing->timing);
								}

								$incomplete_certificate->save();
								if (\Carbon\Carbon::parse($incomplete_certificate->expire_at) <= \Carbon\Carbon::now()) {
									$incomplete_certificate->completion_status = 'Archived';
									$incomplete_certificate->save();

									// New incomplete certificate!
									$new_incomplete_certificate = new \Models\SmcrReport;
									$new_incomplete_certificate->type_id = 1;
									$new_incomplete_certificate->user_id = $user->id;

									$new_incomplete_certificate->start_at = \Carbon\Carbon::parse($incomplete_certificate->expire_at);
									$new_incomplete_certificate->expire_at = \Carbon\Carbon::parse($incomplete_certificate->expire_at)->addDays($timing->timing);
									$new_incomplete_certificate->completion_status = 'Not Started';
									$new_incomplete_certificate->save();

									// Reset all user data
									$user->last_completion_date = $new_incomplete_certificate->start_at;
									$user->next_completion_date = $new_incomplete_certificate->expire_at;
									$user->certified = false;
									$user->self_attested = false;
									$user->completed_resources_smcr = 0;
									$user->in_progress_resources_smcr = 0;
									$user->not_started_resources_smcr = 0;
									$user->completed_smcr = 0;
									$user->learning_status = 'Not Started';


									$template = \Models\EmailTemplate
										::where('name', 'Conduct Rules Staff member needs to be (re)trained')
										->where('status', true)
										->first()
									;
									// Send out only if user needs to be re-certificated! Has something in archive.
									if ($template) {
										$email_queue = new \Models\EmailQueue;
										$email_queue->email_template_id = $template->id;
										$email_queue->recipients = [$user->id];
										$email_queue->custom_variables = json_encode([
											'SMCR_TYPE' => $user->stafftype->name,
										]);
										$email_queue->save();
									}
									\APP\Smcr::resetUserLearning($user, 'Refresh conduct rules learning if incomplete_certificate expired.');
								}
							}
						}
					}
				}

				// If not Apprentix, set users learning_status, for openelms mainly.
				if (
					!$GLOBALS["CONFIG"]->licensing['isApprentix'] &&
					!$GLOBALS["CONFIG"]->licensing['isSMCR']
				) {
					// If user has 0 completed and 0 in progress resources, set to Not Started
					if (
						$user->completed_resources == 0 &&
						$user->in_progress_resources == 0
					) {
						$user->learning_status = 'Not Started';
					}

					// If user has started resources set to in progress
					if (
						$user->in_progress_resources > 0
					) {
						$user->learning_status = 'In Progress';
					}

					// If user has no progress and no started resources, but has completed resources, set status to Completed.
					if (
						$user->completed_resources > 0 &&
						$user->in_progress_resources == 0 &&
						$user->not_started_resources == 0
					) {
						$user->learning_status = 'Completed';
					}
				}


				if ($user->isDirty()) {
					$user->saveWithoutEvents();
				}
			}
		});

		foreach($learning_modules_stats as $learning_module_id => $data) {
			$average_duration = 0;
			if (
				$data->total_duration > 0 &&
				$data->total_users > 0
			) {
				$average_duration = $data->total_duration / $data->total_users;
			}
			$update_stats_res = \Models\LearningModule::find($learning_module_id);

			if ($update_stats_res) {
				$update_stats_res->total_duration = $data->total_duration;
				$update_stats_res->users_count = $data->total_users;
				$update_stats_res->average_duration = $average_duration;
				$update_stats_res->saveWithoutEvents();
			}
		}
	}

	// Similar to ApprenticeshipIssues.php "getIssueModules"", Might merge them at some point
	// Attached all linked resources to issue
	public static function getIssueModules (&$query, $user_id, $sub_issue = false) {
		if (\APP\Auth::isLearner()) {
			$query = $query
				->whereDoesntHave('disabled', function ($query) use ($user_id) {
					$query->where('user_id', $user_id);
				})
			;
		}
		if ($sub_issue) {
			if (\APP\Auth::isAdminInterface()) {
				$query = $query
					->with(['QualityFeedback' => function ($query) use ($user_id) {
						$query= $query
						->where('user_id', $user_id)
						->select('*', DB::raw("DATE_FORMAT(quality_controls.created_at,'%d/%m/%Y') AS qa_created_at"))
						->with(['qaUser' => function ($query) {
							$query
								->select('id', 'fname', 'lname')
							;
						}]);
					}])
					->with(['QualityControlSub' => function ($query) use ($user_id) {
						$query
							->where('user_id', $user_id)
							->with(['qaUser' => function ($query) {
								$query
									->select('id', 'fname', 'lname')
								;
							}])
						;
					}])
				;
			}
		} else {
			if (\APP\Auth::isAdminInterface()) {
				$query = $query
					->with(['QualityFeedback' => function ($query) use ($user_id) {
						$query= $query
						->where('user_id', $user_id)
						->select('*', DB::raw("DATE_FORMAT(quality_controls.created_at,'%d/%m/%Y') AS qa_created_at"))
						->with(['qaUser' => function ($query) {
							$query
								->select('id', 'fname', 'lname')
							;
						}]);
					}])
					->with(['QualityControl' => function ($query) use ($user_id) {
						$query
							->where('user_id', $user_id)
							->orderBy('id', 'DESC')
							->with(['qaUser' => function ($query) {
								$query
									->select('id', 'fname', 'lname')
								;
							}])
						;
					}])
				;
			}
		}
		$query = $query
			->where('status', '=', 1)
			->with(['disabled' => function($query) use ($user_id) {
				$query
					->where('user_id', '=', $user_id)
				;
			}])
			->with(['modules' => function($query) use ($user_id) {
				$query
					->where('status', '=', 1)
					->where('track_progress', true)
					->with('type')
					->with('category')
					->with(['learningresult' => function($query) use ($user_id) {
						$query
							->where('user_id',  $user_id)
							->select('id', 'user_id', 'learning_module_id', 'completion_status', 'due_at', 'completed_at','sign_off_manager_at', 'grace_at',
								'completion_date_custom', 'qa', 'created_at', 'credits','sign_off_manager',DB::raw('(SELECT COUNT(*) FROM learning_results AS lr WHERE lr.learning_module_id = learning_results.learning_module_id AND lr.user_id = learning_results.user_id AND lr.refreshed = 1) AS refreshed_count'),
								DB::raw('(select lr.created_at from  learning_results AS lr WHERE lr.learning_module_id = learning_results.learning_module_id AND lr.user_id = learning_results.user_id AND lr.refreshed = 1 ORDER BY lr.id DESC LIMIT 1) as previous_created_at'),
								DB::raw('(select lr.sign_off_manager_at  from  learning_results AS lr WHERE lr.learning_module_id = learning_results.learning_module_id AND lr.user_id = learning_results.user_id AND lr.refreshed = 1 ORDER BY lr.id DESC LIMIT 1) as previous_completed_at'))
							->where('refreshed', 0)
						;
						if (\APP\Auth::isAdminInterface())
						{
							$query
								->with(['QualityFeedback' => function ($query) use ($user_id) {
									$query= $query
									->where('user_id', $user_id)
									->with(['qaUser' => function ($query) {
										$query
											->select('id', 'fname', 'lname')
										;
									}]);
								}])
								->with(['QualityControl' => function ($query) use ($user_id) {
									$query
										->where('user_id', $user_id)
										->orderBy('id', 'DESC')
										->with(['qaUser' => function ($query) {
											$query
												->select('id', 'fname', 'lname')
											;
										}])
									;
								}])
							;
						}
					}])
					->with(['userlearningmodules' => function($query) use ($user_id) {
						$query->where('user_id', '=', $user_id);
					}])
					->whereHas('userlearningmodules', function ($query) use ($user_id) {
						$query
							->where('user_id', $user_id)
						;
					})
					->select(
						'learning_modules.id',
						'learning_modules.name',
						'learning_modules.type_id',
						'learning_modules.thumbnail',
						'learning_modules.is_course',
						'learning_modules.category_id',
						'learning_modules.track_progress',
						'learning_modules.evidence_type_id',
						'learning_modules.is_skill',
						'learning_modules.repetition_period'

					)
					->withCount(['Modules' => function ($query) use ($user_id) {
						$query
							->where('status', true)
							->where(function ($query) use ($user_id) {
								$query
									->whereIn('learning_modules.id',
										\Models\UserLearningModule
											::select('learning_module_id')
											->where('user_id', $user_id)
											->get()
									)
									->orWhere('learning_modules.self_enroll', true)
								;
							})
						;
					}])
				;
				\Models\User::getIssueModulesComments($query, $user_id);
			}])
			// modules attached for issues, but for individual user
			->with(['usermodules' => function($query) use ($user_id) {
				$query
					->where('status', '=', 1)
					->where('track_progress', true)
					->with('type')
					->with('category')
					->wherePivot('user_id', '=', $user_id)
					->with(['learningresult' => function($query) use ($user_id) {
						$query
							->where('user_id', '=', $user_id)
							->select('id', 'user_id', 'learning_module_id', 'completion_status', 'due_at', 'completed_at', 'grace_at', 'completion_date_custom', 'qa', 'created_at', 'credits')
							->where('refreshed', 0)
						;
						if (\APP\Auth::isAdminInterface())
						{
							$query
								->with(['QualityFeedback' => function ($query) use ($user_id) {
									$query= $query
									->where('user_id', $user_id)
									->with(['qaUser' => function ($query) {
										$query
											->select('id', 'fname', 'lname')
										;
									}]);
								}])
								->with(['QualityControl' => function ($query) use ($user_id) {
									$query
										->where('user_id', $user_id)
										->orderBy('id', 'DESC')
										->with(['qaUser' => function ($query) {
											$query
												->select('id', 'fname', 'lname')
											;
										}])
									;
								}])
							;
						}
					}])
					->with(['userlearningmodules' => function($query) use ($user_id) {
						$query->where('user_id', '=', $user_id);
					}])
					->whereHas('userlearningmodules', function ($query) use ($user_id) {
						$query
							->where('user_id', $user_id)
						;
					})
					->select(
						'learning_modules.id',
						'learning_modules.name',
						'learning_modules.type_id',
						'learning_modules.is_course',
						'learning_modules.category_id',
						'learning_modules.track_progress'
					)
					->withCount(['Modules' => function ($query) use ($user_id) {
						$query
							->where('status', true)
							->where(function ($query) use ($user_id) {
								$query
									->whereIn('learning_modules.id',
										\Models\UserLearningModule
											::select('learning_module_id')
											->where('user_id', $user_id)
											->get()
									)
									->orWhere('learning_modules.self_enroll', true)
								;
							})
						;
					}])
				;
				\Models\User::getIssueModulesComments($query, $user_id);
			}])
			->with(['evidencemodule' => function($query) use ($user_id) {
				$query
					->where('status', '=', 1)
					->where('track_progress', true)
					->wherePivot('user_id', '=', $user_id)
					->with("type")
					->with('category')
					->with(['learningresult' => function($query) use ($user_id) {
						$query
							->where('user_id', '=', $user_id)
							->select('*')
							->where('refreshed', 0)
						;
						if (\APP\Auth::isAdminInterface())
						{
							$query
								->with(['QualityFeedback' => function ($query) use ($user_id) {
									$query= $query
									->where('user_id', $user_id)
									->with(['qaUser' => function ($query) {
										$query
											->select('id', 'fname', 'lname')
										;
									}]);
								}])
								->with(['QualityControl' => function ($query) use ($user_id) {
									$query
										->where('user_id', $user_id)
										->orderBy('id', 'DESC')
										->with(['qaUser' => function ($query) {
											$query
												->select('id', 'fname', 'lname')
											;
										}])
									;
								}])
							;
						}
					}])
					->with(['userlearningmodules' => function($query) use ($user_id) {
						$query->where('user_id', '=', $user_id);
					}])
					->whereHas('userlearningmodules', function ($query) use ($user_id) {
						$query
							->where('user_id', $user_id)
						;
					})
					->select(
						'learning_modules.id',
						'learning_modules.name',
						'learning_modules.type_id',
						'learning_modules.is_course',
						'learning_modules.category_id',
						'learning_modules.track_progress',
						'learning_modules.evidence_type_id'
					)
					->withCount(['Modules' => function ($query) use ($user_id) {
						$query
							->where('status', true)
							->where(function ($query) use ($user_id) {
								$query
									->whereIn('learning_modules.id',
										\Models\UserLearningModule
											::select('learning_module_id')
											->where('user_id', $user_id)
											->get()
									)
									->orWhere('learning_modules.self_enroll', true)
								;
							})
						;
					}])
				;
				\Models\User::getIssueModulesComments($query, $user_id);
			}])
			->distinct()
		;
	}

	public static function getIssueModulesComments (&$query, $user_id) {
		$query
			->with(['learningresultscomments' => function ($query) use ($user_id) {
				$query
					->where(function ($query) use ($user_id) {
						$query = $query
							->where('comment_by_user_id', $user_id)
							->orWhere('created_for_user_id', $user_id)
						;
					})
					->where('status', true)
					->with(['CreatedBy' => function ($query) {
					$query
						->select(
							'id',
							'fname',
							'lname',
							'role_id'
						);
					}])
				;
				if (\APP\Auth::isLearner()) {
					$query = $query
						->where('visible_learner', true)
					;
				}
			}])
		;
	}


	public static function calculateStandardProgressModules (&$issue, &$standard = false) {
		$types = [
			'modules',
			'evidencemodule',
			'usermodules'
		];

		foreach ($types as $key => $type) {
			if ($issue->relationLoaded($type)) {
				foreach ($issue->{$type} as $key => $module) {
					if (
						$module &&
						$module->learningresult &&
						$module->track_progress
					) {
						// Use issues completion date as default
						$module->completion_date = \Carbon\Carbon::parse($module->learningresult->due_at)->format('d/m/Y');

						if ($module->learningresult->grace_at) {
							$module->completion_date = \Carbon\Carbon::parse($module->learningresult->grace_at)->format('d/m/Y');
						}

						// If issue/module pivot has custom dates, use them!
						// if (
						// 	$module->pivot &&
						// 	$module->pivot->custom_work_window &&
						// 	$module->pivot->end_day &&
						// 	$standard &&
						// 	isset($standard->start_date)
						// ) {

						// 	if ($standard->paused) {
						// 		if ($module->pivot->start_day > $standard->days_till_pause_start) {
						// 			$module->pivot->start_day = $module->pivot->start_day + $standard->length_of_pause;
						// 		}
						// 		if ($module->pivot->end_day > $standard->days_till_pause_start) {
						// 			$module->pivot->end_day = $module->pivot->end_day + $standard->length_of_pause;
						// 		}
						// 	}

						// 	$module->completion_date = \Carbon\Carbon::parse($standard->start_date)->addDays($module->pivot->end_day)->format('d/m/Y'); //->format(\APP\Tools::getConfig('defaultDateFormat'));
						// }

						// If learning result has completion_date_custom date, use that as completion dates, individual setting for learner
						if ($module->learningresult->completion_date_custom) {
							$module->completion_date = \Carbon\Carbon::parse($module->learningresult->completion_date_custom)->format('d/m/Y'); //->format(\APP\Tools::getConfig('defaultDateFormat'));
						}

						$issue->resources++;
						$issue->resources_single++;
						if (
							$module->type &&
							$module->type->fit_for_evidence
						) {
							$issue->resources_qualification++;
							$issue->resources_single_qualification++;
						}

						if ($module->learningresult->completion_status == 'completed') {
							$issue->completed_resources++;
							$issue->completed_resources_single++;

							if (
								$module->type &&
								$module->type->fit_for_evidence
							) {
								$issue->completed_resources_qualification++;
								$issue->completed_resources_single_qualification++;
							}

							if (
								$module->learningresult->credits &&
								$module->learningresult->credits > 0
							) {
								$issue->received_credits = $issue->received_credits + $module->learningresult->credits;
							}

							if ($standard) {
								if (isset($standard->actual_end_date)) {
									if ($standard->actual_end_date < $module->learningresult->completed_at) {
										$standard->actual_end_date = $module->learningresult->completed_at;
									}
								} else {
									$standard->actual_end_date = $module->learningresult->completed_at;
								}
							}
						}
						if (
							$module->relationLoaded('userlearningmodules') &&
							count($module->userlearningmodules) == 0
						) {
							$module->disabled = true;
						}
						$module->setAppends(['safe_thumbnail', 'highlight']);
					}
				}
			}
		}
	}

	public static function calculateCriteriaDates(&$criteria, &$standard) {
		if ($standard->paused) {
			if ($criteria->start_day > $standard->days_till_pause_start) {
				$criteria->start_day = $criteria->start_day + $standard->length_of_pause;
			}
			if ($criteria->end_day > $standard->days_till_pause_start) {
				$criteria->end_day = $criteria->end_day + $standard->length_of_pause;
			}
		}
		$criteria->completion_date = \Carbon\Carbon::parse($standard->start_date)->addDays($criteria->end_day)->format('d/m/Y'); //->format(\APP\Tools::getConfig('defaultDateFormat'));
	}

	public static function calculateCriteriaCommonData(&$criteria, &$parent, &$standard, &$outcome) {

		if ($criteria->resources > 0) {
			$criteria->progress = round((100 / $criteria->resources) * $criteria->completed_resources, 2);
			$outcome->issue_progress = $outcome->issue_progress + $criteria->progress;
		}
		$parent->completed_resources = $parent->completed_resources + $criteria->completed_resources;
		$parent->resources = $parent->resources + $criteria->resources;
		$outcome->received_credits = $outcome->received_credits + $criteria->received_credits;

		// Qualification calculations
		if (
			$standard->type == 'Qualifications' &&
			$standard->number_of_evidence_expected &&
			$standard->number_of_evidence_expected > 0
		) {

			$parent->child_criteria_cnt++;

			// If current criteria has children criteria, ignore this criteria, calculate progress from children sum!
			if (
				$criteria->child_criteria_cnt &&
				$criteria->child_criteria_progress
			) {
				$criteria->criteria_progress = round($criteria->child_criteria_progress / $criteria->child_criteria_cnt, 2);
			} else {
				// One resource percentage
				$one_resource_percentage = 100 / $standard->number_of_evidence_expected;
				// Get completed percentage, this can go over 100%
				$criteria->criteria_progress = round($one_resource_percentage * $criteria->completed_resources_single_qualification, 2);
				if ($criteria->criteria_progress >= 100) {
					$criteria->criteria_progress = 100;
				}

				// If outcome is marked for exclusion in calculation, do not add these
				if (!$outcome->exclude_outcome) {
					$standard->lowest_criteria_cnt++;
					$standard->lowest_criteria_progress = $standard->lowest_criteria_progress + $criteria->criteria_progress;
				}
			}

			// Correct over 100% problem and update parent with in/completed criteria count
			if ($criteria->criteria_progress >= 100) {
				$criteria->criteria_progress = 100;
				$parent->child_completed_criteria_cnt++;
			} else {
				$parent->child_incomplete_criteria_cnt++;
			}

			$parent->child_criteria_progress = $parent->child_criteria_progress + $criteria->criteria_progress;

		}
	}

	public static function setCriteriaDefaults(&$criteria, &$outcome) {
		$outcome->issue_cnt++;
		$criteria->completed_resources = 0;
		$criteria->completed_resources_qualification = 0;
		$criteria->completed_resources_single = 0;
		$criteria->completed_resources_single_qualification = 0;
		$criteria->resources = 0;
		$criteria->resources_qualification = 0;
		$criteria->resources_single = 0;
		$criteria->resources_single_qualification = 0;
		$criteria->progress = 0;
		$criteria->received_credits = 0;
		$criteria->criteria_progress = 0;
		$criteria->child_criteria_progress = 0;
		$criteria->child_criteria_cnt = 0;
		$criteria->child_completed_criteria_cnt = 0;
		$criteria->child_incomplete_criteria_cnt = 0;
	}



	public static function calculateStandardProgress (&$standards, $save = false, $debug = false) {
		if ($debug) echo "\n[DEBUG] calculateStandardProgress called with save=" . ($save ? 'true' : 'false') . "\n";
		foreach ($standards as $key => $standard) {
			if ($debug) echo "\n[DEBUG] Processing standard ID: {$standard->id}, Name: {$standard->name}\n";
			$standard->completed_resources = 0;
			$standard->resources = 0;
			$standard->completion_date = \Carbon\Carbon::parse($standard->pivot->due_at)->format('d/m/Y'); //->format(\APP\Tools::getConfig('defaultDateFormat'));
			$standard->start_date = \Carbon\Carbon::parse($standard->pivot->start_at);
			$standard->actual_end_date = false;
			$standard->total_critera = 0;
			$standard->critera_count = 0;
			$standard->completed_resources_exclusion_filter = 0;
			$standard->resources_exclusion_filter = 0;
			$standard->criteria_progress = 0;
			$standard->child_criteria_cnt = 0;
			$standard->lowest_criteria_cnt = 0;
			$standard->lowest_criteria_progress = 0;
			$standard->child_criteria_progress = 0;

			$standard->paused = false;
			if (
				$standard->pivot->paused == 1 &&
				$standard->pivot->paused_start &&
				$standard->pivot->paused_end
			) {
				$standard->paused = true;

				$standard->paused_start = $standard->pivot->paused_start;
				$standard->paused_end = $standard->pivot->paused_end;

				//Calculate difference between start and start of pause
				$standard->days_till_pause_start = \Carbon\Carbon::parse($standard->start_date)->diffInDays(\Carbon\Carbon::parse($standard->paused_start));

				// Calculate length of pause
				$standard->length_of_pause = \Carbon\Carbon::parse($standard->paused_start)->diffInDays(\Carbon\Carbon::parse($standard->paused_end));
			}


			foreach ($standard->issuecategories as $key => $issuecategory) {
				if (
					$issuecategory->issues_count > 0 &&
					$issuecategory->issues_disabled_count == $issuecategory->issues_count
				) {
					continue;
				}
				$issuecategory->completed_resources = 0;
				$issuecategory->resources = 0;
				$issuecategory->issue_cnt = 0;
				$issuecategory->percentage = 0;
				$issuecategory->issue_progress = 0;
				$issuecategory->progress = 0;
				$issuecategory->received_credits = 0;
				$issuecategory->criteria_progress = 0;
				$issuecategory->child_criteria_cnt = 0;

				foreach ($issuecategory->issues as $key => $issue) {
					if ($issue->disabled) {
						continue;
					}
					\Models\User::setCriteriaDefaults($issue, $issuecategory);
					\Models\User::calculateCriteriaDates($issue, $standard);

					if (
						!$issuecategory->completion_date ||
						\Carbon\Carbon::parse($standard->start_date)->addDays($issue->end_day) > $issuecategory->completion_date
					) {
						$issuecategory->completion_date = \Carbon\Carbon::parse($standard->start_date)->addDays($issue->end_day)->format('d/m/Y'); //->format(\APP\Tools::getConfig('defaultDateFormat'));
					}

					// loop all modules/evidence modules and specific user modules to calculate progress
					\Models\User::calculateStandardProgressModules($issue, $standard);

					foreach ($issue->children as $key => $subIssue) {
						if ($subIssue->disabled) {continue;}
						\Models\User::setCriteriaDefaults($subIssue, $issuecategory);
						\Models\User::calculateCriteriaDates($subIssue, $standard);
						\Models\User::calculateStandardProgressModules($subIssue, $standard);

						foreach ($subIssue->children as $key => $subSubIssue) {
							if ($subSubIssue->disabled) {continue;}
							\Models\User::setCriteriaDefaults($subSubIssue, $issuecategory);
							\Models\User::calculateCriteriaDates($subSubIssue, $standard);
							\Models\User::calculateStandardProgressModules($subSubIssue, $standard);
							\Models\User::calculateCriteriaCommonData($subSubIssue, $subIssue, $standard, $issuecategory);

							// Save SUB SUB criteria-user progress
							if ($save) {
								$apprenticeship_issue_user = \Models\ApprenticeshipIssueUser
									::where('user_id', $standard->pivot->user_id)
									->where('issue_id', $subSubIssue->id)
									->first()
								;
								if (!$apprenticeship_issue_user) {
									$apprenticeship_issue_user = new \Models\ApprenticeshipIssueUser;
									$apprenticeship_issue_user->issue_id = $subSubIssue->id;
									$apprenticeship_issue_user->user_id = $standard->pivot->user_id;
								}
								$apprenticeship_issue_user->percentage = $subSubIssue->progress;
								$apprenticeship_issue_user->save();
							}
						}

						\Models\User::calculateCriteriaCommonData($subIssue, $issue, $standard, $issuecategory);

						// Save SUB criteria-user progress
						if ($save) {
							$apprenticeship_issue_user = \Models\ApprenticeshipIssueUser
								::where('user_id', $standard->pivot->user_id)
								->where('issue_id', $subIssue->id)
								->first()
							;
							if (!$apprenticeship_issue_user) {
								$apprenticeship_issue_user = new \Models\ApprenticeshipIssueUser;
								$apprenticeship_issue_user->issue_id = $subIssue->id;
								$apprenticeship_issue_user->user_id = $standard->pivot->user_id;
							}
							$apprenticeship_issue_user->percentage = $subIssue->progress;
							$apprenticeship_issue_user->save();
						}

					}

					\Models\User::calculateCriteriaCommonData($issue, $issuecategory, $standard, $issuecategory);

					if (!$issuecategory->exclude_outcome) {
						$standard->total_critera += $issue->progress;
						$standard->critera_count++;
					}

					// Save criteria-user progress
					if ($save) {
						$apprenticeship_issue_user = \Models\ApprenticeshipIssueUser
							::where('user_id', $standard->pivot->user_id)
							->where('issue_id', $issue->id)
							->first()
						;
						if (!$apprenticeship_issue_user) {
							$apprenticeship_issue_user = new \Models\ApprenticeshipIssueUser;
							$apprenticeship_issue_user->issue_id = $issue->id;
							$apprenticeship_issue_user->user_id = $standard->pivot->user_id;
						}
						$apprenticeship_issue_user->percentage = $issue->progress;
						$apprenticeship_issue_user->save();
					}

				}

				// If Qualifications, set criteria_progress for outcome

				if (
					$standard->type == 'Qualifications' &&
					$standard->number_of_evidence_expected &&
					$standard->number_of_evidence_expected > 0
				) {
					if (!$issuecategory->exclude_outcome) {
						$standard->child_criteria_cnt++;
					}
					if (
						$issuecategory->child_criteria_progress > 0 &&
						$issuecategory->child_criteria_cnt > 0
					) {
						$issuecategory->criteria_progress = round($issuecategory->child_criteria_progress / $issuecategory->child_criteria_cnt, 2);
						if (!$issuecategory->exclude_outcome) {
							$standard->child_criteria_progress = $standard->child_criteria_progress + $issuecategory->criteria_progress;
						}
					}
				}

				$issuecategory->progress = 0;
				if ($issuecategory->resources > 0) {
					$issuecategory->progress = round((100 / $issuecategory->resources) * $issuecategory->completed_resources, 2);
				}

				$standard->completed_resources = $standard->completed_resources + $issuecategory->completed_resources;
				$standard->resources = $standard->resources + $issuecategory->resources;

				/*Exclude Issue category and calculate resource percentage*/
				if (!$issuecategory->exclude_outcome) {
					$standard->completed_resources_exclusion_filter = $standard->completed_resources_exclusion_filter + $issuecategory->completed_resources;
					$standard->resources_exclusion_filter = $standard->resources_exclusion_filter + $issuecategory->resources;
				}

				if ($issuecategory->issue_progress > 0) {
					$issuecategory->issue_progress = round($issuecategory->issue_progress / $issuecategory->issue_cnt, 2);
				}

				// Update Category/Outcome progress
				if ($save) {
					$apprenticeship_issue_categories_user = \Models\ApprenticeshipIssueCategoriesUser
						::where('user_id', $standard->pivot->user_id)
						->where('issue_category_id', $issuecategory->id)
						->first()
					;
					if (!$apprenticeship_issue_categories_user) {
						$apprenticeship_issue_categories_user = new \Models\ApprenticeshipIssueCategoriesUser;
						$apprenticeship_issue_categories_user->issue_category_id = $issuecategory->id;
						$apprenticeship_issue_categories_user->user_id = $standard->pivot->user_id;
					}
					$apprenticeship_issue_categories_user->percentage = $issuecategory->progress;
					$apprenticeship_issue_categories_user->percentage_issues = $issuecategory->issue_progress;
					$apprenticeship_issue_categories_user->save();
				}

			}

			if (
				$standard->critera_count > 0 &&
				$standard->total_critera > 0
			) {
				$standard->criteria_progress =  round($standard->total_critera / $standard->critera_count, 2);
			}

			// If Qualifications, set criteria_progress for standard
			if (
				$standard->type == 'Qualifications' &&
				$standard->number_of_evidence_expected &&
				$standard->number_of_evidence_expected > 0
			) {
				$standard->criteria_progress = 0;
				if (
					$standard->lowest_criteria_progress > 0 &&
					$standard->lowest_criteria_cnt > 0
				) {
					$standard->criteria_progress = round($standard->lowest_criteria_progress / $standard->lowest_criteria_cnt, 2);
				}
			}

			if ($standard->resources > 0) {
				if ($standard->resources_exclusion_filter > 0) {
					$standard->progress = round((100 / $standard->resources_exclusion_filter) * $standard->completed_resources_exclusion_filter, 2);
				}
				if ($debug) echo "[DEBUG] Standard progress calculated: {$standard->progress}%\n";
				if ($debug) echo "[DEBUG] Resources: {$standard->resources}, Completed: {$standard->completed_resources}\n";
				if ($debug) echo "[DEBUG] Resources (exclusion filter): {$standard->resources_exclusion_filter}, Completed: {$standard->completed_resources_exclusion_filter}\n";

				// Update Standard/Programme progress
				if ($save) {
					if ($debug) echo "\n[DEBUG] Save mode - updating apprenticeship_standards_users for user_id: {$standard->pivot->user_id}, standard_id: {$standard->id}\n";
					$apprenticeship_standards_users = \Models\ApprenticeshipStandardUser
						::where('standard_id', $standard->id)
						->where('user_id', $standard->pivot->user_id)
						->first()
					;

					if ($debug) echo "[DEBUG] Found apprenticeship_standards_users record: " . ($apprenticeship_standards_users ? 'Yes' : 'No') . "\n";
					if ($debug && $apprenticeship_standards_users) {
						echo "[DEBUG] Current completion_status: {$apprenticeship_standards_users->completion_status}\n";
					}

					$apprenticeship_standards_users->percentage = $standard->progress;
					$apprenticeship_standards_users->criteria_completion = $standard->criteria_progress;

					if ($debug) echo "[DEBUG] Setting percentage to: {$standard->progress}\n";
					if ($debug) echo "[DEBUG] Setting criteria_completion to: {$standard->criteria_progress}\n";

					// check if all required completion states are met
					$criteriaMet = false;
					$resourcesMet = false;
					$timeMet = false;

					if ($debug) echo "\n[DEBUG] Checking completion conditions:\n";
					if ($debug) echo "[DEBUG] standard->completion_criteria: " . ($standard->completion_criteria ? 'true' : 'false') . "\n";
					if ($debug) echo "[DEBUG] standard->completion_resources: " . ($standard->completion_resources ? 'true' : 'false') . "\n";
					if ($debug) echo "[DEBUG] standard->completion_time: " . ($standard->completion_time ? 'true' : 'false') . "\n";

					if ($standard->completion_criteria) {
						$criteriaMet = ($apprenticeship_standards_users->criteria_completion >= 100);
						if ($debug) echo "[DEBUG] Criteria check: criteria_completion={$apprenticeship_standards_users->criteria_completion} >= 100 = " . ($criteriaMet ? 'true' : 'false') . "\n";
					}

					if ($standard->completion_resources) {
						$resourcesMet = (
							($standard->resources == $standard->completed_resources && !empty($standard->actual_end_date)) ||
							($standard->resources_exclusion_filter > 0 && $standard->resources_exclusion_filter == $standard->completed_resources_exclusion_filter)
						);
						if ($debug) {
							echo "[DEBUG] Resources check:\n";
							echo "[DEBUG]   standard->resources: {$standard->resources}\n";
							echo "[DEBUG]   standard->completed_resources: {$standard->completed_resources}\n";
							echo "[DEBUG]   standard->actual_end_date: " . ($standard->actual_end_date ? $standard->actual_end_date : 'empty') . "\n";
							echo "[DEBUG]   standard->resources_exclusion_filter: {$standard->resources_exclusion_filter}\n";
							echo "[DEBUG]   standard->completed_resources_exclusion_filter: {$standard->completed_resources_exclusion_filter}\n";
							echo "[DEBUG]   Resources met: " . ($resourcesMet ? 'true' : 'false') . "\n";
						}
					}

					if ($standard->completion_time) {
						$timeMet = ($apprenticeship_standards_users->percentage_time >= 100);
						if ($debug) echo "[DEBUG] Time check: percentage_time={$apprenticeship_standards_users->percentage_time} >= 100 = " . ($timeMet ? 'true' : 'false') . "\n";
					}

					// Determine how many conditions need to be met
					$requiredConditions = ($standard->completion_criteria ? 1 : 0) +
										  ($standard->completion_resources ? 1 : 0) +
										  ($standard->completion_time ? 1 : 0);

					// Count how many conditions are actually met
					$conditionsMet = ($criteriaMet ? 1 : 0) +
									 ($resourcesMet ? 1 : 0) +
									 ($timeMet ? 1 : 0);

					if ($debug) {
						echo "\n[DEBUG] Condition summary:\n";
						echo "[DEBUG] Required conditions: {$requiredConditions}\n";
						echo "[DEBUG] Conditions met: {$conditionsMet}\n";
						echo "[DEBUG] criteriaMet: " . ($criteriaMet ? 'true' : 'false') . "\n";
						echo "[DEBUG] resourcesMet: " . ($resourcesMet ? 'true' : 'false') . "\n";
						echo "[DEBUG] timeMet: " . ($timeMet ? 'true' : 'false') . "\n";
					}

					$oldStatus = $apprenticeship_standards_users->completion_status;

					// Update the completion status based on the conditions
					if ($conditionsMet >= $requiredConditions) {
						$apprenticeship_standards_users->completion_status = 'completed';
						$apprenticeship_standards_users->completed_at = $standard->actual_end_date;
						if ($debug) echo "[DEBUG] Setting status to 'completed' (conditions met: {$conditionsMet} >= required: {$requiredConditions})\n";
					} else {
						$apprenticeship_standards_users->completed_at = null;
						if ($standard->completed_resources > 0) {
							$apprenticeship_standards_users->completion_status = 'in progress';
							if ($debug) echo "[DEBUG] Setting status to 'in progress' (completed_resources: {$standard->completed_resources} > 0)\n";
						} else {
							$apprenticeship_standards_users->completion_status = 'not attempted';
							if ($debug) echo "[DEBUG] Setting status to 'not attempted' (completed_resources: {$standard->completed_resources} = 0)\n";
						}
					}

					if ($debug) echo "[DEBUG] Status change: '{$oldStatus}' -> '{$apprenticeship_standards_users->completion_status}'\n";

					try {
						$apprenticeship_standards_users->save();
						if ($debug) echo "[DEBUG] Successfully saved apprenticeship_standards_users record\n";
					} catch (\Exception $e) {
						if ($debug) echo "[ERROR] Failed to save apprenticeship_standards_users: " . $e->getMessage() . "\n";
					}
				}
			}



		}
	}

	// synchronize all data from user's table to relevant ILR tables, temporary functionality(OR IS IT?).
	public static function syncIlrData ($ilr_fields, $user_id = false) {

		// If user_id is given, get only him! GET HIM
		$users = \Models\User::where('id', '>', 0);
		if ($user_id) {
			$users = $users->where('id', $user_id);
		}
        $users = $users->get();

		// Model/table and respectively ILR field in user table where to take data from.
		$sync_objects = [
			[
				'model' => 'IlrLearningDelivery',
				'field' => 'LearningDelivery'
			],
			[
				'model' => 'IlrLearnerEmploymentStatus',
				'field' => 'LearnerEmploymentStatus'
			],
			[
				'model' => 'IlrLearnerDestinationAndProgression',
				'field' => 'DPOutcome', // Field must be identical in ILR main tree
				'userField' => 'LearnerDestinationandProgression', // user field indicated different field in user table that contains relevant information with ILR field
				'subUserField' => 'DPOutcome' // In case user field directly does not contain relevant information, but in sub-field.
            ],
            [
                'model' => 'IlrUserPriorAttainment',
                'field' => 'PriorAttain',
            ],
            [
                'model' => 'IlrUserLearnerFam',
                'field' => 'LearnerFAM',
            ]
			// LearnerDestinationandProgression
		];

		$sync_sub_objects = [
			[
				'model' => 'IlrLearningDeliveryFinancialRecord',
				'field' => 'AppFinRecord'
			],
			[
				'model' => 'IlrLearningDeliveryMonitoring',
				'field' => 'ProviderSpecDeliveryMonitoring'
			],
		];

		// Delete all enties from all sub-objects
		foreach ($sync_sub_objects as $key => $sync_sub_object) {
			$model = '\Models\\' . $sync_sub_object['model'];
			$delete_users = $model::where('id', '>', 0);
			if ($user_id) {
				$delete_users = $delete_users->where('user_id', $user_id);
			}
			$delete_users = $delete_users->delete();
		}

		// Delete all entries from listed models for given user/s.
		foreach ($sync_objects as $key => $sync_object) {
			$model = '\Models\\' . $sync_object['model'];
			$delete_users = $model::where('id', '>', 0);
			if ($user_id) {
				$delete_users = $delete_users->where('user_id', $user_id);
			}
			$delete_users = $delete_users->delete();
		}

		$user_ids = [];
		foreach ($users as $key => $user) { // Loop all users
			$user_ids[] = $user->id;

			// Loop sync_objects and take data from user table and put in respective ilr tables.
			foreach ($sync_objects as $key => $sync_object) {
				$userFields = false; // user field must be false in beggining
				$user_field = $sync_object['field'];

				// If sepecific individual user field is specified, look for it in user table
				if (
					isset($sync_object['userField']) &&
					$sync_object['userField']
				) {
					$user_field = $sync_object['userField'];
				}

				// If field exists, decode it in array/s!
                if ($user->{$user_field} && is_string($user->{$user_field}) ) {

					$userFields = json_decode($user->{$user_field}, true);
                }elseif($user->{$user_field} && is_array($user->{$user_field})){
                    $userFields = $user->{$user_field};
                }

				// IF subUserField is specified return array with key as subUserField
				if (
					isset($sync_object['subUserField']) &&
					isset($userFields[$sync_object['subUserField']])
				) {
					$userFields = $userFields[$sync_object['subUserField']];
				}

				// If user has specific field, decode it
				if (
					$userFields &&
					count($userFields) > 0 // If decoded field contains entries
				) {
					// Initialize new model for each entry, previous entries are deleted, multiple learning deliveries, etc.
					foreach ($userFields as $key => $userField) {
						$model = '\Models\\' . $sync_object['model'];
						$query = new $model;
                        $query->user_id = $user->id;

						// Clean subobjects variables
						foreach ($sync_sub_objects as $key => $sync_sub_object) {
							if (isset(${$sync_sub_object['field']})) {
								unset(${$sync_sub_object['field']});
							}
						}

						// Loop ILR field children, each children will be recorded as seperate field.
						// Each field will be restricted to type/length, coming from official documentation
						// Except fields that contain children, they will be as encoded JSON.
						foreach ($ilr_fields[$sync_object['field']]['children'] as $ilr_field_key => $ilr_field) {
							if (
								isset($userField[$ilr_field_key])
							) {
								if (isset($ilr_field['type']) && $ilr_field['type'] == 'json') {
									if (
										!is_array($userField[$ilr_field_key]) &&
										is_object($userField[$ilr_field_key])
									) {
										$userField->{$ilr_field_key} = [$userField[$ilr_field_key]];
									}
									$query->{$ilr_field_key} = json_encode($userField[$ilr_field_key]);

									// Loop sub objects and put them in variables for use later!
									foreach ($sync_sub_objects as $key => $sync_sub_object) {
										if ($ilr_field_key == $sync_sub_object['field']) {
											${$sync_sub_object['field']} = $userField[$ilr_field_key];
										}
									}

								} else if (isset($ilr_field['type']) && $ilr_field['type'] == 'date') {
									$query->{$ilr_field_key} = null;
									if (
										isset($userField[$ilr_field_key]) &&
										$userField[$ilr_field_key] &&
										!is_array($userField[$ilr_field_key])
									) {
										try {
											$query->{$ilr_field_key} = \Carbon\Carbon::parse($userField[$ilr_field_key]);
										} catch (\Exception $e) {
											$query->{$ilr_field_key} = null;
										}
									}
								} else {
									if (
										!is_array($userField[$ilr_field_key])
									) {
										$query->{$ilr_field_key} = $userField[$ilr_field_key];
									}
								}
							}
						}
						$query->save();

						// If key is AppFinRecord, put them in ilr_learning_delivery_financial_records
						if (isset($AppFinRecord)) {
							foreach ($AppFinRecord as $key => $fin_record) {
								$AppFinRecord_query = new \Models\IlrLearningDeliveryFinancialRecord;
								$AppFinRecord_query->ilr_learning_delivery_id = $query->id;
								$AppFinRecord_query->user_id = $user->id;
								if (
									isset($fin_record['AFinType']) &&
									!is_array($fin_record['AFinType'])
								) {
									$AppFinRecord_query->AFinType = $fin_record['AFinType'];
								}
								if (
									isset($fin_record['AFinCode']) &&
									!is_array($fin_record['AFinCode'])
								) {
									$AppFinRecord_query->AFinCode = $fin_record['AFinCode'];
								}
								if (
									isset($fin_record['AFinDate']) &&
									!is_array($fin_record['AFinDate'])
								) {
									try {
										$AppFinRecord_query->AFinDate = \Carbon\Carbon::parse($fin_record['AFinDate']);
									} catch (\Exception $e) {
										//
									}
								}
								if (
									isset($fin_record['AFinAmount']) &&
									is_numeric($fin_record['AFinAmount'])
								) {
									$AppFinRecord_query->AFinAmount = $fin_record['AFinAmount'];
								}
								$AppFinRecord_query->save();
							}
						}


						if (isset($ProviderSpecDeliveryMonitoring)) {
							foreach ($ProviderSpecDeliveryMonitoring as $key => $monitoring) {
								$monitoring_query = new \Models\IlrLearningDeliveryMonitoring;
								$monitoring_query->ilr_learning_delivery_id = $query->id;
								$monitoring_query->user_id = $user->id;
								if (
									isset($monitoring['ProvSpecDelMonOccur']) &&
									!is_array($monitoring['ProvSpecDelMonOccur'])
								) {
									$monitoring_query->ProvSpecDelMonOccur = $monitoring['ProvSpecDelMonOccur'];
								}
								if (
									isset($monitoring['ProvSpecDelMon']) &&
									!is_array($monitoring['ProvSpecDelMon'])
								) {
									$monitoring_query->ProvSpecDelMon = $monitoring['ProvSpecDelMon'];
								}
								if (
									$monitoring_query->ProvSpecDelMonOccur &&
									$monitoring_query->ProvSpecDelMon
								) {
									$monitoring_query->save();
								}
							}
                        }
					}
				}
            }
        }
        foreach($users as $user){
            $learningDelivery  = IlrLearningDelivery::where('user_id',$user->id)->where('LearningDeliveryFAM','!=','[]')->whereNotNull('LearningDeliveryFAM')->get();
            IlrLearningDeliveryFam::where('user_id',$user->id)->delete();
            foreach($learningDelivery as $ld){
                $LearningDeliveryFAM = json_decode($ld->LearningDeliveryFAM,true);
                foreach($LearningDeliveryFAM as $ldfam){
                    IlrLearningDeliveryFam::create([
                        'user_id' => $user->id,
                        'learning_delivery_id' => $ld->id,
                        'LearnDelFAMType' => $ldfam['LearnDelFAMType'],
                        'LearnDelFAMCode' => $ldfam['LearnDelFAMCode'],
                        'LearnDelFAMDateFrom' => $ldfam['LearnDelFAMDateFrom'],
                        'LearnDelFAMDateTo' => $ldfam['LearnDelFAMDateTo']
                    ]);
                }
            }

        }

		// Update actual costs for standard_user link if any user's learning delivery has actual standard referenced.
		\Models\ApprenticeshipStandardUser::sumActualCosts($user_ids);

		// Re-calculate dates for cost reports, in case dates have changed.
		\Models\ApprenticeshipStandardUser::calculateEndDate([$user_ids]);
	}

	// If non admin is editing admin, fail!
	public static function adminRoleCheck ($response = false, $role_id = false, $check = false) {
		if (
			!\APP\Auth::isAdmin() &&
			$role_id
		) {
			$user_role = \Models\Role::find($role_id);
			if ($user_role->is_admin) {
				if ($check || !$response) {
					return false;
				} else {
					return \APP\Tools::returnCode($response, $response, 403);
				}
			}
		}
		if ($check || !$response) {
			return true;
		}
	}

	// Will check if manager is allowed to add manager roles
	public static function managerRoleCheck ($response = false, $role_id = false, $check = false) {
		if (
			!\APP\Auth::isAdmin() &&
			$role_id
		) {
			$user_role = \Models\Role::find($role_id);
			if (
				$user_role->is_admin ||
				(
					!$user_role->is_admin &&
					(
						$user_role->is_manager ||
						$user_role->is_cd ||
						$user_role->is_fa ||
						$user_role->is_qa
					) &&
					!\APP\Auth::allowAssigningManagerRoles()
				)
			) {
				if ($check || !$response) {
					return false;
				} else {
					return \APP\Tools::returnCode($response, $response, 403);
				}
			}
		}
		if ($check || !$response) {
			return true;
		}
	}

	public static function combinedRoleCheck ($data, $user) {
		$response = true;
		if (
			!empty($data['role_id']) &&
			(
				!\Models\User::adminRoleCheck(false, $data['role_id'], true) ||
				!\Models\User::managerRoleCheck(false, $data['role_id'], true)
			)
		) {
			$response = false;
		}
		if (
			$user->role_id &&
			(
				!\Models\User::adminRoleCheck(false, $user->role_id, true) ||
				!\Models\User::managerRoleCheck(false, $user->role_id, true)
			)
		) {
			$response = false;
		}
		return $response;
	}

	// Finds all users that has duplicated LearnRefNumber
	public static function findAllLearnRefNumberDuplicates () {
		$results = \Models\User::whereIn('LearnRefNumber', function ( $query ) {
			$query
				->select('LearnRefNumber')
				->from('users')
				->groupBy('LearnRefNumber')
				->havingRaw('count(*) > 1')
				->where('LearnRefNumber', '!=', '')
			;
		})->get();

		// Loop them and run save on each to increment duplicated LearnRefNumber
		foreach ($results as $key => $result) {
			$result->save();
		}
	}

	public static function generateNewLearnRefNumber ($number = false) {
		// Assign unique number as ID
		if (!$number) {
			//select biggest number in LearnRefNumber
			$top_users = \Models\User
				::whereNotNull('LearnRefNumber')
				//->where('status', true)
				->get()
			;
			$top_number = 0;
			foreach ($top_users as $key => $top_user) {
				$user_number = (int) preg_replace('~^.*?([0-9]+(?:\.[0-9]+)?)$~', '$1', $top_user->LearnRefNumber);
				if ($user_number > $top_number) {
					$top_number = $user_number;
				}
			}
			//$top_number = (int) preg_replace('~^.*?([0-9]+(?:\.[0-9]+)?)$~', '$1', $top_user->LearnRefNumber);
			// Update counter
			\APP\Tools::updateLearnerRefNum($top_number + 1);
			$new_number = \APP\Tools::getLearnerRefNum($top_number + 1);
		} else {
			$new_number = \APP\Tools::getLearnerRefNum($number);
		}
		return $new_number;
	}

	public static function checkLearnRefNumberDuplicate ($user) {
		$duplicated_lrn = \Models\User
			::where('LearnRefNumber', $user->LearnRefNumber)
			->where('id', '!=', $user->id)
			//->where('status', true)
			->get()
		;
		if (count($duplicated_lrn) > 0) {
			$user->LearnRefNumber = \Models\User::generateNewLearnRefNumber($user->id);
		}
		return $user;
	}

	// Update users last contact date
	public static function updateLastContactDate($user_ids, $date = false) {
		if (!is_array($user_ids)) {
			$user_ids = [$user_ids];
		}

		$users = \Models\User
			::whereIn('id', $user_ids)
			->get()
		;

		if (!$date) {
			$date = \Carbon\Carbon::now();
		} else {
			$date = \Carbon\Carbon::parse($date);
		}

		foreach ($users as $key => $user) {
			$user->last_contact_date = $date;
			$user->save();
		}
	}

	// Ran each night, will check if field alert system is enabled, if so, check if interval is up, send out reminder to users if any of fields are empty
	public static function userFieldAlert () {

		$enableUserFieldAlertSystem = \APP\Tools::getConfig('enableUserFieldAlertSystem');
		$send_email = \APP\Tools::getConfig('enableEmailFunctionality');

		if (
			$enableUserFieldAlertSystem &&
			$send_email
		) {
			$userFieldAlertSystemInterval = \APP\Tools::getConfig('userFieldAlertSystemInterval');
			$userFieldAlertSystemMonitoredFields = \APP\Tools::getConfig('userFieldAlertSystemMonitoredFields');
			$userFieldAlertSystemIntervalRunTime = \APP\Tools::getConfig('userFieldAlertSystemIntervalRunTime');
			$first_run_time = false;

			if ($userFieldAlertSystemIntervalRunTime) {
				$userFieldAlertSystemIntervalRunTime = \Carbon\Carbon::parse($userFieldAlertSystemIntervalRunTime);
			} else {
				$first_run_time = true;
				$userFieldAlertSystemIntervalRunTime = \Carbon\Carbon::yesterday();
			}

			if ($userFieldAlertSystemInterval < 0) {
				$userFieldAlertSystemInterval = 30;
			}
			$next_run_time = $userFieldAlertSystemIntervalRunTime->addDays($userFieldAlertSystemInterval);
			//$next_run_time = $userFieldAlertSystemIntervalRunTime->addDays(0);
			//$first_run_time = true;
			if (
				$next_run_time->lessThanOrEqualTo(\Carbon\Carbon::now()) ||
				$first_run_time
			) {
				// time to send!
				// Check if template is availabe
				$template = \Models\EmailTemplate::getTemplate('user_field_alert_reminder');
				if (
					$template &&
					$userFieldAlertSystemMonitoredFields > ''
				) {
					// is there actually any field that needs to be checked ??
					$userFieldAlertSystemMonitoredFields = explode(',', $userFieldAlertSystemMonitoredFields);
					if (count($userFieldAlertSystemMonitoredFields) > 0) {
						// Get all users that have those fields missing
						$users = \Models\User
							::where('status', true)
						;

						$users = $users
							->where(function ($query) use ($userFieldAlertSystemMonitoredFields) {
								foreach($userFieldAlertSystemMonitoredFields as $field) {
									if (
										$field &&
										strpos($field, 'extended_') === false
									) {
										$query = $query
											->orWhere($field, '')
											->orWhereNull($field)
										;
									}
								}
								$query = $query
									->orWhereHas('ExtensionFields', function ($query) use ($userFieldAlertSystemMonitoredFields) {
										// Not sure how to do it here properly, but there must be a way to iterate userFieldAlertSystemMonitoredFields, select extended ones and return if field is not found!
									})
								;
							})
						;

						$users = $users
							->get()
						;

						// get fields!
						$user_field_alert_list = \Models\Picklist::where('type', 'user_field_alert_list')->get();
						$field_names = [];
						foreach ($user_field_alert_list as $key => $user_field_alert_list_item) {
							$field_names[$user_field_alert_list_item->slug] = $user_field_alert_list_item->value;
						}

						// Get extended fields!
						$user_field_alert_list_extended = \Models\TableExtensionField
							::where("status", true)
							->where('versions', 'like', '%"' . $GLOBALS["CONFIG"]->licensing['version'] . '"%')
							->where('show_learner', true)
							->get()
						;
						$field_names_extended = [];
						foreach ($user_field_alert_list_extended as $key => $user_field_alert_list_item_extended) {
							$field_names_extended[$user_field_alert_list_item_extended->field_key] = $user_field_alert_list_item_extended->field_name;
						}

						// Will have to generate queue for each user...
						// Or make a sets of variations! sinking
						$field_user_sets = [];
						foreach ($users as $key => $user) {
							\Models\TableExtension::returnAllFields('users', $user->id, $user);
							$user_missing_fields = '';
							$user_missing_fields_names = '';
							// Get list of all missing fields.

							foreach ($userFieldAlertSystemMonitoredFields as $key => $field) {
								if (
									strpos($field, 'extended_') === false &&
									(
										$user->{$field} == '' ||
										$user->{$field} == null
									) &&
									isset($field_names[$field])
								) {
									// Build a string of missing fields for user, will use this as a set to attach users to, so that don't need to create millions of emails!
									$user_missing_fields = $user_missing_fields . $field;
									$user_missing_fields_names = $user_missing_fields_names . '<li>' . $field_names[$field] . '</li>';
								}

								$extended_field = str_replace("extended_", "", $field);
								if (
									strpos($field, 'extended_') !== false &&
									$user->extended &&
									property_exists($user->extended, $extended_field) &&
									(
										$user->extended->{$extended_field} == '' ||
										$user->extended->{$extended_field} == null
									) &&
									isset($field_names_extended[$extended_field])
								) {
									$user_missing_fields = $user_missing_fields . $field;
									$user_missing_fields_names = $user_missing_fields_names . '<li>' . $field_names_extended[$extended_field] . '</li>';
								}

							}


							if ($user_missing_fields > '') {
								if (
									isset($field_user_sets[$user_missing_fields]) &&
									is_array($field_user_sets[$user_missing_fields])
								) {
									$field_user_sets[$user_missing_fields]['users'][] = $user->id;
								} else {
									$field_user_sets[$user_missing_fields] = [
										'users' => [$user->id],
										'field_list' => $user_missing_fields_names
									];
								}

							}
						}

						foreach ($field_user_sets as $key => $field_user_set) {
							$email_queue = new \Models\EmailQueue;
							$email_queue->email_template_id = $template->id;
							$email_queue->recipients = $field_user_set['users'];
							$email_queue->custom_variables = json_encode([
								'INCOMPLETE_FIELDS' => $field_user_set['field_list'],
							]);
							$email_queue->save();
						}

						// Update userFieldAlertSystemIntervalRunTime
						$update_userFieldAlertSystemIntervalRunTime = \Models\Configuration::where('key', 'userFieldAlertSystemIntervalRunTime')->first();
						if ($update_userFieldAlertSystemIntervalRunTime) {
							$update_userFieldAlertSystemIntervalRunTime->value = \Carbon\Carbon::now()->toDateTimeString();
							$update_userFieldAlertSystemIntervalRunTime->save();
						}

					}
				}

			}
		}
	}

	public static function importNewUsers($settings, $import_file, $params, $response = false, $notify_roles = [], $nightly_import = false) {

		//open excel file
		try {

			$n_users = [
				'n_users_updated' => 0,
				'n_users_inserted' => 0,
				'n_users_disabled' => 0,
				'n_users_deleted' => 0,
				'n_users_rejected' => 0,
			];
/*
			$php_excel = \PhpOffice\PhpSpreadsheet\IOFactory::load(
				$import_file
			);
			*/
			$php_excel = \PhpOffice\PhpSpreadsheet\IOFactory::load($import_file);
			$sheet = $php_excel->getActiveSheet();
			$rows = $sheet->getRowIterator(1);

			$isImportFromCron = $params['import_from_cron'] ?? false;

			if($isImportFromCron) {
			    $emailAlert = Configuration::query()->where('key', '=', 'cronImportFailureEmail')->first();
                if (!empty($emailAlert) && $emailAlert->value) {
                    $emailAlertMail = $emailAlert->value;
                } else {
                    $adminRole = Role::query()->select('id')->where('access_all_learners', '=', 1)->where('is_admin', '=', 1)->first();
                    $admin = User::query()->where('role_id', '=', $adminRole->id)->first();
                    $emailAlertMail = $admin->email;
                }
            }

			switch($params["type"]) {
				case "import_new":
					$fields = [
							"A" => "usercode",
							"B" => "fname",
							"C" => "lname",
							"D" => "company",
							"E" => "department",
							"F" => "designation",
							"G" => "location",
							"H" => "role",
							"I" => "country",
							"J" => "city",
							"K" => "email",
							"L" => "phone",
							"M" => "username",
							"N" => "description",
							"O" => "group",
							"P" => "password",
							"Q" => "ULN",
							"R" => "managers", // managers user needs to be assigned to, seperated by coma.
							"S" => "staff_type",
							"T" => "department_sub_2",
							"U" => "department_sub_3",
							"V" => "department_sub_4",
							"W" => "department_sub_5",
							"X" => "department_sub_6",
							"Y" => "department_sub_7",
							"Z" => "account_type",
					];
					$data = [];
					foreach($rows as $row) {
						$row_i = $row->getRowIndex();
						$cells = $row->getCellIterator();
						$cells->setIterateOnlyExistingCells($row_i == 1);
						if ($row_i > 1) {
							$data[$row_i] = [];
						}
						foreach($cells as $cell_i => $cell) {
							if ($row_i == 1) {
								//$fields[$cell_i] = $cell->getCalculatedValue();
							} else {
								if (
									empty($fields[$cell_i])
								) {
									continue;
								}
								$data[$row_i][$fields[$cell_i]] = $cell->getCalculatedValue();
							}
						}
					}
					foreach($data as $record) {

						$foreign_fields = [
							"designation" => "\Models\Designation",
							"country" => "\Models\Country",
							"company" => "\Models\Company",
							"department" => "\Models\Department",
							"location" => "\Models\Location",
							"city" => "\Models\City",
							"role" => "\Models\Role",
							"group" => "\Models\Group",
							"staff_type" => "\Models\SmcrStaffType",
							"account_type" => "\Models\Picklist",
						];

						foreach($foreign_fields as $foreign_field => $foreign_field_model) {
							if (isset($record[$foreign_field])) {

								if ($foreign_field == 'role') {
									$foreign_field_obj = $foreign_field_model
										::whereRaw('TRIM(name) = ?', [trim($record[$foreign_field])])
										->where('status', true)
									;
								} else if ($foreign_field == 'account_type') {
									$foreign_field_obj = $foreign_field_model
										::where("value", $record[$foreign_field])
										->where('type', 'account_type')
									;
								} else {
									$foreign_field_obj = $foreign_field_model
										::where("name", $record[$foreign_field])
									;
								}

								$foreign_field_obj = $foreign_field_obj
									->first()
								;



								if ($foreign_field_obj) {
									$record[$foreign_field . "_id"] = $foreign_field_obj->id;
								} else {
									// Does not exists, create new, but not for role, managers and staff_type, and account_type!
									if (
										$foreign_field != 'role' &&
										$foreign_field != 'managers' &&
										$foreign_field != 'staff_type' &&
										$foreign_field != 'account_type'
									) {
										$foreign_field_obj = new $foreign_field_model;
										$foreign_field_obj->name = $record[$foreign_field];
										$foreign_field_obj->status = true;
										if ($foreign_field == 'department' && isset($record["company_id"])) {
											$foreign_field_obj->company_id = $record["company_id"];
										}
										if ($foreign_field == 'city' && isset($record["country_id"])) {
											$foreign_field_obj->country_id = $record["country_id"];
										}
										$foreign_field_obj->save();
										$record[$foreign_field . "_id"] = $foreign_field_obj->id;
									}
								}
							}
						}

						if (
							\APP\Tools::getConfig('fixUserNameOnImport') &&
							isset($record["username"]) &&
							substr($record["username"], 0, 1) === "1"
						) {
							$record["username"] = "0" . $record["username"];
						}

						if (
							\APP\Tools::getConfig('fixUserCodeOnImport') &&
							isset($record["usercode"]) &&
							substr($record["usercode"], 0, 1) === "1"
						) {
							$record["usercode"] = "0" . $record["usercode"];
						}

						if (empty($record["username"])) {
							if (isset($record["email"])) {
								$record["username"] = $record["email"];
							}
							if (
								isset($record["usercode"]) &&
								\APP\Tools::getConfig('importUserCodeForMissingUsername')
							) {
								$record["username"] = $record["usercode"];
							}
						}


						/**
						 * uniqueUsernamePerUser and uniqueEmailUsers now can be switched on and off
						 */
						$existing_user = false;
						if (
							\APP\Tools::getConfig('uniqueEmailPerUser') &&
							\APP\Tools::getConfig('uniqueUsernamePerUser')
						) {
							$existing_user = \Models\User
								::where("username", $record["username"])
							;
							if (isset($record["email"])) {
								$existing_user = $existing_user
									->where("email", $record["email"])
								;
							}
						} else if (\APP\Tools::getConfig('uniqueUsernamePerUser')) {
							$existing_user = \Models\User
								::where("username", $record["username"])
							;
						} else if (\APP\Tools::getConfig('uniqueEmailPerUser')) {
							$existing_user = \Models\User
								::where("email", $record["email"])
							;
						} else {
							// uniqueEmailPerUser and uniqueUsernamePerUser is set to false, user can be added regardless
						}

						if (
							\APP\Tools::getConfig('userImportReplaceUsernameWithEmplyeeId') &&
							!empty($record["usercode"]) &&
							$existing_user
						) {
							$existing_user = $existing_user
								->orWhere("username", $record["usercode"])
							;
							$record["username"] = $record["usercode"];
						}

						if ($existing_user) {
							$existing_user = $existing_user
								->get()
							;
						}

						$user = new \Models\User;
						$user->creation_notes = 'user created using import file function, either by nightly import or manual';
						$is_existing_user = false;

						if ($existing_user) {
							foreach($existing_user as $e_user) {
								$user = $e_user;
								$is_existing_user = true;
								break;
							}
						}

						if (
							isset($record["username"]) ||
							isset($record["email"])
						) {

							// if no e-mail is given and configuration option "allowEmptyEmailImport" is enabled, generate e-mail.

							if (
								empty($record["email"]) &&
								\APP\Tools::getConfig('allowEmptyEmailImport') &&
								$user->email == ""
							) {
								$record["email"] = \APP\Tools::setUniqueEmail();
							}

							$fields = [
								"username", "usercode", "fname", "lname", "email", "phone",
								"designation_id", "country_id", "company_id", "department_id",
								"location_id", "city_id", "role_id", "description",
								"expiration_dt", "registration_dt", "ULN", "staff_type_id",
								"status", "account_type_id"
							];

							// Default role to learner, if user has no role
							if (!$user->role_id) {
								$user->role_id = \APP\Tools::getConfig('defaultRegisterRole');
							}

							// If role is specified during import and role is admin, and if user importing is not admin, then deny role change.
							if (
								isset($record['role_id']) &&
								!$nightly_import && // This check is done only if import is not done manually
								!\Models\User::adminRoleCheck($response, $record['role_id'], true)
							) {
								$record['role_id'] = NULL;
							}


							foreach($fields as $field) {
								if (isset($record[$field])) {
									$user->$field = $record[$field];
								}
							}

							if ($user->staff_type_id) {
								$user->staff_type_assigned = \Carbon\Carbon::now();
							}

							if (isset($record["password"]) && $record["password"]) {
								$user->password = password_hash($record["password"], PASSWORD_BCRYPT, ['cost' => 12]);
							} else {
								if (!$is_existing_user) {
									$user->password = "";
								}
							}

							if (
								$is_existing_user &&
								$user->status == 0
							) {
								$user->status = \APP\Tools::getConfig('reEnableUsersOnImport');
                                if($user->status) {
                                    self::notifyUserIsEnabled($user);
                                }
							} else {
								$user->status = 1;
							}

							// perform custom actions if department name matches ones specified in configuration options.
							if (
								$user->department_id
							) {
								$user_department = \Models\Department::find($user->department_id);
								// Set user status to 0 if department name for imported user is the same as DisableUserDepartmentOnImport in configuration options!
								if ($user_department) {
									if (
										\APP\Tools::getConfig('DisableUserDepartmentOnImport') &&
										\APP\Tools::getConfig('DisableUserDepartmentOnImport') == $user_department->name
									) {
										$user->status = 0;
										Schedule::userStatusUpdate($user->id,NULL,false);
									}

									// Set user status to 1 if department name for imported user is the same as EnableUserDepartmentOnImport in configuration options!
									if (
										\APP\Tools::getConfig('EnableUserDepartmentOnImport') &&
										\APP\Tools::getConfig('EnableUserDepartmentOnImport') == $user_department->name
									) {

									    if($user->status && $user->status == 0) {
                                            $user->status = 1;
                                            self::notifyUserIsEnabled($user);
                                        }

									}
								}
							}


							try {
								// Add default UKPRN
								if (!$user->UKPRN) {
									$user->UKPRN = \APP\Tools::getConfig('defaultUKPRN');
								}
								if (!$user->PrevUKPRN) {
									$user->PrevUKPRN = \APP\Tools::getConfig('defaultPrevUKPRN');
								}
								if (!$user->LearnRefNumber) {
									$user->LearnRefNumber = \APP\Tools::getLearnerRefNum();
								}
								$user->saveWithoutEvents();

								// If sub departments are given in spreadsheet, import them!
								$sub_dep_id = $user->department_id;
								foreach($record as $k => $v) {
									if (
										strpos($k, 'department_sub') !== false &&
										$user &&
										$user->id &&
										$user->company_id &&
										$user->company_id > 0 &&
										$sub_dep_id &&
										$sub_dep_id > 0
									) {
										$sub_dep_id = \Models\Department::addSubItem($v, $sub_dep_id, $user->company_id);
										// add UserSubDepartment entry, if does not exist!
										if (
											$sub_dep_id &&
											$sub_dep_id > 0
										) {
											\Models\UserSubDepartment::firstOrCreate(
												[
													'user_id' => $user->id,
													'department_id' => $sub_dep_id
												]
											);
										}
									}
								}

								// Copy Learning Delivery to seperate table, temporary solution, eventually I will have to remove JSON functionality and use proper SQL relationships.

								// there is no ilr data update on this import!
								// Not sure this realy needs to be here!
								//\Models\User::syncIlrData($settings['ilr_fields'], $user->id);

								if (!$is_existing_user) {
									$n_users['n_users_inserted']++;
									\APP\Tools::updateLearnerRefNum();
								} else {
									$n_users['n_users_updated']++;
								}

								if (count($notify_roles) > 0) {
									\APP\Email::sendInstructionEmail($user->id, $notify_roles);
								}
							} catch(\Exception $e) {

							    if($isImportFromCron) {
                                    // Send Email Alert to the given configuration email
                                    $emailClass = \APP\Email::createMailer("Import Users CRON Fail Alert");

                                    if($emailClass) {
                                        $vars = [
                                            "TO_USER_NAME" => $emailAlertMail,
                                            "USER_EMPLOYEE_CODE" => $user->usercode,
                                            "USER_FIRST_NAME" => $user->fname,
                                            "USER_LAST_NAME" => $user->lname,
                                            "USER_ROLE" => $user->role->name,
                                            "USER_EMAIL" => $user->email,
                                            "IMPORT_EXCEPTION_MESSAGE" => 'ERROR CODE - ' .$e->getCode() .' ERROR MESSAGE - '. $e->getMessage(),
                                        ];

                                        $emailClass->sendAlertEmail($emailAlertMail, $vars);
                                    }
                                }


                                continue;
							}


							if (isset($record["group_id"])) 	{
								\Models\GroupUser::firstOrCreate(
									[
										'user_id' => $user->id,
										'group_id' => $record["group_id"]
									],
									[
										'status' => true
									]
								);
								// Assign user all the modules in group
								\Models\GroupLearningModule::AssingToUser($record["group_id"], $user);
							}

							if (!empty($record["managers"])) {
								$record["managers"] = preg_replace('/\s*,\s*/', ',', trim($record["managers"]));
								$managers_usernames = explode(',', $record["managers"]);

								$valid_manager_role_ids = \Models\Role
									::where('status', true)
									->where('is_manager', true)
									->pluck('id')
								;

								$managers_list = \Models\User
									::whereIn('username', $managers_usernames)
									->validuser()
									->whereIn('role_id', $valid_manager_role_ids)
									->get()
								;

								if (\APP\Tools::getConfig('deleteAndLinkManagersOnUserImport')) {
									$manager_ids = \Models\ManagerUser::where("user_id", $user->id)
										->pluck('manager_id')
										->toArray()
									;

									if (!empty($manager_ids)) {
										\Models\ManagerUser::unLink([$user->id], $manager_ids, 'User import, deleteAndLinkManagersOnUserImport is enabled');
									}
								}

								foreach ($managers_list as $assign_manager) {
									\Models\ManagerUser::link([$user->id], [$assign_manager->id], 'User import from spreadsheet file');
								}
							}

						}
					}
					break;
				case "import_delete":
					$user_ids = [];
					$user_names = [];
					$user_codes = [];
					foreach($rows as $row) 	{
						$row_i = $row->getRowIndex();
						if ($row_i == 1) 	{
							continue;
						}
						$cells = $row->getCellIterator();
						foreach($cells as $cell_i => $cell) {
							if ($cell_i == 'A' && $cell->getCalculatedValue()) {
								$user_names[] = $cell->getCalculatedValue();
							}
						}
					}

					$delete_users = \Models\User
						::whereIn("username", $user_names)
						->with('role')
						->get()
					;
					foreach ($delete_users as $key => $delete_user) {
						if (
							$delete_user->role &&
							!$delete_user->role->is_admin
						) {
							\Models\User::deleteUser($delete_user->id, $settings);
							$n_users['n_users_deleted']++;
						}
					}
					break;

				case "import_disable":
					$user_ids = [];
					$user_names = [];
					$user_codes = [];
					foreach($rows as $row) 	{
						$row_i = $row->getRowIndex();
						if ($row_i == 1) 	{
							continue;
						}
						$cells = $row->getCellIterator();
						foreach($cells as $cell_i => $cell) {
							if ($cell_i == 'A' && intval($cell->getCalculatedValue()) > 0) {
								$user_ids[] = intval($cell->getCalculatedValue());
							}
							if ($cell_i == 'B' && $cell->getCalculatedValue()) {
								$user_names[] = $cell->getCalculatedValue();
							}
							if ($cell_i == 'C' && $cell->getCalculatedValue()) {
								$user_codes[] = $cell->getCalculatedValue();
							}
						}
					}

					$disable_users = \Models\User
						::whereIn("id", $user_ids)
						->orWhereIn("username", $user_names)
						->orWhereIn("usercode", $user_codes)
						->where('status', true)
						->get()
					;
					foreach ($disable_users as $key => $disable_user) {
						$disable_user->status = false;
						$disable_user->save();
						$n_users['n_users_disabled']++;
					}
					break;

			}
			return $n_users;
		} catch(\Exception $e) {
			if ($response) {
				return
					$response->withStatus(500)
						->withHeader('Content-Type', 'text/html')
						->write($e->getMessage())
				;
			} else {
				return $e->getMessage();
			}
		}
	}

	public static function discountList($user_id) {
		return \Models\User
			::where('id', $user_id)
			->select('id','discount_percentage', 'fname', 'lname', 'email', 'phone', 'company_id', 'role_id', 'skype_id','department_id')
			->with('company')
			->with('department')
			->first();
	}

	public static function calculateTotalDiscount($user_id) {
        $discount= \Models\User::discountList($user_id);
		$totaldiscount = 0;
		if (!empty($discount->discount_percentage)) {
			$totaldiscount = $discount->discount_percentage;
		}
		if (!empty($discount->company->discount_percentage)) {
			$totaldiscount = $totaldiscount + $discount->company->discount_percentage;
		}
		if (!empty($discount->department->discount_percentage)) {
			$totaldiscount = $totaldiscount + $discount->department->discount_percentage;
		}
		return $totaldiscount;
	}

	public static function hasFullDiscount(int $user_id): bool
	{
		$discount = self::discountList($user_id);
		return $discount?->discount_percentage == 100 || $discount?->company?->discount_percentage == 100 || $discount?->department?->discount_percentage == 100;
	}


	// Initial query to get manager list for user.
	public static function managerList($args) {
		$user = \Models\User
			::where('id', $args['user_id'])
			->select('id')
			->first()
			;

		$managerIds = [];
		foreach($user->managerstiny as $u) $managerIds[] = $u->id;

		$managers = \Models\User
			::WhereIn('id', $managerIds)
			->where('status', true)
			->select('id', 'fname', 'lname', 'email', 'phone', 'company_id', 'role_id')
			->with(['company' => function($query)
			{
				$query
					->select('id', 'name')
				;
			}])
			->with(['role' => function($query)
			{
				$query
					->select('id', 'name')
				;
			}])
			;

		return $managers;
	}

	// Will find out if user needs to be approved for booking
	public static function bookingApproved($user_id, $resource_id) {
		$response = false;
		$key = 'DoesNotRequireBookingApproval';

		$resource = \Models\LearningModule::find($resource_id);
		if (!$resource->approval) {
			$response = true;
		}

		$user = \Models\User
			::where('id', $user_id)
			->first()
		;
		if (
			$user &&
			$resource
		) {
			if (\Models\TableExtension::getValue('users', $user->id, $key)) {
				$response = true;
			}
			if (
				$user->company_id &&
				\Models\TableExtension::getValue('companies', $user->company_id, $key)
			) {
				$response = true;
			}
			if (
				$user->department_id &&
				\Models\TableExtension::getValue('departments', $user->department_id, $key)
			) {
				$response = true;
			}
		}

		return $response;
	}

	public function saveWithoutEvents(array $options=[]) {
		return static::withoutEvents(function() use ($options) {
			return $this->save($options);
		});
	}

	public static function exportDataToFile($settings, $path) {
		if (
			\APP\Tools::getConfig('enableMightlyUserDataExport') &&
			is_dir($path) &&
			is_writable($path)
		) {
			$users = \Models\User
				::where('status', 1)
				->with('company')
				->with('department')
				->get()
			;

			$list = [['ID', 'Username', 'Email', 'First name', 'Last name', 'Company', 'Department']];
			foreach ($users as $key => $user) {
				$list[] = [
					$user->id,
					$user->username,
					$user->email,
					$user->fname,
					$user->lname,
					$user->company ? $user->company->name : '',
					$user->department ? $user->department->name : ''
				];
			}
			$fp = fopen($path . 'output.csv', 'w');
			fputs($fp, $bom =( chr(0xEF) . chr(0xBB) . chr(0xBF) ));

			foreach ($list as $fields) {
				fputcsv($fp, $fields);
			}

			fclose($fp);

			return count($users) . " entries exported";
		}
	}

	protected static function boot() {
		parent::boot();

		static::saving(function($user) {
			// Check if LearnRefNumber is duplicated, if so increment it and run
			if (
				isset($GLOBALS["CONFIG"]) &&
				$GLOBALS["CONFIG"]->licensing['isApprentix']
			) {
				$user = \Models\User::checkLearnRefNumberDuplicate($user);
			}

			$escape_fields = ['fname', 'lname', 'username', 'email', 'phone', 'usercode', 'description', 'emergency_name', 'emergency_relationship', 'emergency_contact_numbers', 'visa_number', 'skype_id', 'zoom_id', 'teams_id'];
			foreach($escape_fields as $field) {
				if (!empty($user[$field])){
					$user[$field] = \APP\Tools::safeDBfield($user[$field]);
				}
			}

		});

		static::created(function($user) {
			\Models\Designation::AssignResources($user->designation_id, false, $user);

			if ($user->designation_id) {
				$user->designation_assigned = \Carbon\Carbon::now();
			}

			if ($user->staff_type_id) {
				$user->staff_type_assigned = \Carbon\Carbon::now();
			}

		});

		static::updating(function($user) {
			$old_entry = \Models\User::find($user->id);

			\Models\Designation::AssignResources($user->designation_id, $old_entry->designation_id, $user);

			if ($user->designation_id != $old_entry->designation_id) {
				$user->designation_assigned = \Carbon\Carbon::now();
			}

			if ($user->staff_type_id != $old_entry->staff_type_id) {
				$user->staff_type_assigned = \Carbon\Carbon::now();
			}

			if (
				(
					$old_entry ||
					$user
				) &&
				\APP\Auth::getUserId() > 0
			) {
				/*
					Possibly worth zipping this up, for future or older entries.
					$history->before = gzdeflate(json_encode($old_entry));
					$history->after = gzdeflate(json_encode($user));
				*/


				// Manual check of passed date fields, new ones will be carbon object, old ones will be string, convert old ones to carbon and check if same, exclude from changes, if no changes, ignore update!
				$date_fields = [
					'visa_date',
					'visa_date',
					'DateOfBirth',
					'last_login_dt',
					'expiration_dt',
					'registration_dt',
					'last_contact_date',
					'staff_type_assigned',
					'password_changed_at',
					'staff_type_assigned',
					'designation_assigned',
					'next_completion_date',
					'last_completion_date',
					'previous_last_login_dt',
					'staff_type_sign_off_approval',
				];

				$ignore_fields = [
					'shadow_role_id'
				];
				$changes = $user->getDirty();

				\Models\TableHistory::trackChanges($old_entry, $changes, $date_fields, 'users', $user->id, $ignore_fields);

			}

		});

		static::saved(function($user) {
			cache()->forget('manager_role_all');


			$original = $user->getOriginal();
			$updated = $user->getChanges();


			// Check if department change was done and department has standard assigned to it
			if (array_key_exists('department_id', $updated)) {
				$original_department_id = isset($original['department_id']) ? $original['department_id'] : false;
				$new_department_id = isset($updated['department_id']) ? $updated['department_id'] : false;


				// Remove standards assigned to this department to user
				if ($original_department_id) {
					\Models\Department::removeUserFromStandard($original_department_id, $user->id);

					\Models\ScheduleLink::processAction(
						'delete',
						$original_department_id,
						'departments',
						[
							'link' => 'users',
							'entries' => [$user->id]
						]
					);

					\Models\UserCustomReview::updateUser($user->id, $original_department_id, 'departments', 'remove');
				}

				// add standards assigned to this department to user
				if ($new_department_id) {
					\Models\Department::assignUserToStandard($new_department_id, $user->id);

					\Models\ScheduleLink::processAction(
						'create',
						$new_department_id,
						'departments',
						[
							'link' => 'users',
							'entries' => [$user->id]
						]
					);

					\Models\UserCustomReview::updateUser($user->id, $new_department_id, 'departments', 'add');
				}
			}

			// Do actions if designation/job has changed
			if (array_key_exists('designation_id', $updated)) {
				$original_designation_id = isset($original['designation_id']) ? $original['designation_id'] : false;
				$new_designation_id = isset($updated['designation_id']) ? $updated['designation_id'] : false;

				if ($original_designation_id) {
					\Models\UserCustomReview::updateUser($user->id, $original_designation_id, 'designations', 'remove');
				}

				if ($new_designation_id) {
					\Models\UserCustomReview::updateUser($user->id, $new_designation_id, 'designations', 'add');
				}
			}

			$scorm_user = \Models\Scorm\User::firstOrNew(["id" => $user->id]);
			$scorm_user->id = $user->id;
			$scorm_user->username = isset($user->username) ? $user->username : "";
			$scorm_user->firstname = isset($user->fname) ? $user->fname : "";
			$scorm_user->lastname = isset($user->lname) ? $user->lname : "";
			$scorm_user->email = isset($user->email) ? $user->email : "";
			$scorm_user->timemodified = time();
			$scorm_user->save();

			\Models\Scorm\RoleAssignment::firstOrCreate(["userid" => $user->id]);

			$department_module_ids = \Models\DepartmentLearningModule
				::where("department_id", "=", $user->department_id)
				->get()
				->pluck("learning_module_id")
				->toArray()
			;

			if (!empty($department_module_ids)) {
				\Models\UserLearningModule::linkResources($user->id, $department_module_ids, 'user - saved - add any deparment resource to user');
			}

			$mandatory_learning_module_ids = \Models\LearningModule
				::select('id')
				->where('status', '=', 1)
				->where('created_in_learner_interface', '!=', true)
				->whereIn('category_id', function($query){
					$query
						->select('id')
						->from('learning_module_categories')
						->where('is_mandatory', '=', 1)
						->where('status', '=', 1)
					;
				})
				->get()
				->pluck('id')
				->toArray()
			;

			if (!empty($mandatory_learning_module_ids)) {
				\Models\UserLearningModule::linkResources($user->id, $mandatory_learning_module_ids, 'user - saved - assign any mandatory resource to user.');
			}

			// Assign user to manager, or remove user from manager, if comapny_id changes that is related to manager
			if (array_key_exists('company_id', $updated)) {
				$original_company_id = isset($original['company_id']) ? $original['company_id'] : false;
				$new_company_id = isset($updated['company_id']) ? $updated['company_id'] : false;

				// Remove user from managers who were part of old company
				if ($original_company_id) {
					\Models\ManagerUser
						::whereIn('manager_id',
							\Models\ManagerCompany
								::select('manager_id')
								->where('company_id', $original_company_id)
								->get()
						)
						->where('user_id', $user->id)
						->delete()
					;
				}

				// Find all managers with this company and assign user to these managers
				if ($new_company_id) {
					$new_company_managers = \Models\ManagerCompany
						::where('company_id', $new_company_id)
						->get()
					;

					foreach ($new_company_managers as $key => $new_company_manager) {
						$entry = \Models\ManagerUser
							::firstOrCreate(
								[
									'manager_id' => $new_company_manager->manager_id,
									'user_id' => $user->id,
								]
							)
						;
						$entry->save();
					}
				}

			}

			\APP\Learning::syncUserResults($user->id);
		});

		static::creating(function($user) {

			// Check if uniqueUsernamePerUser is true, do not allow creating new user with same username.
			if (
				\APP\Tools::getConfig('uniqueUsernamePerUser') &&
				$user->username
			) {
				$existing_user = \Models\User
					::where('username', $user->username)
					->first()
				;
				if ($existing_user) {
					return false;
				}

			}

			if (\APP\Auth::getUserId()) {
				$user->created_by = \APP\Auth::getUserId();
			}
		});

		// If HideOtherRegisterdUsers is set to true, hide other users with role set in defaultRegisterRole
		static::addGlobalScope('type_filter', function (\Illuminate\Database\Eloquent\Builder $builder) {
			if (isset($GLOBALS["CONFIG"]->HideOtherRegisterdUsers)) {
				$defaultRegisterRole = \APP\Tools::getConfig('defaultRegisterRole');
				if (
					$defaultRegisterRole &&
					\APP\Auth::getUserId()
				) {
					$builder = $builder
						->where(function ($query) use ($defaultRegisterRole) {
							$query
								->where('users.role_id', '!=', $defaultRegisterRole)
								->orWhere('users.id', \APP\Auth::getUserId())
							;
						})

					;
				}
			}
        });

	}



	public function kaliidusImportByCron($settings){
		try{
			$process_to_run=\Models\ImportLogs::where("is_processed","0")->first();
			if($process_to_run){
				$param=json_decode($process_to_run->params);
				$filePath=$process_to_run->file_path;
				if($param->import_user && $filePath){
					\APP\Import::kallidusImportUser($filePath);
				}
				if($param->import_learning && $filePath){
					$response=new Response();
					\APP\Import::kallidusImportLearning($filePath,$settings,$response);
				}
				if($param->import_event_data && $filePath){
					\APP\Import::kallidusImportEventData($filePath);
					\APP\Import::kallidusImportEventUser($filePath);
				}
				if($param->import_learning_records && $filePath){
					\APP\Import::kallidusImportLearningRecordData($filePath);
				}
			}
	    } catch(\Exception $e) {
			throw new Error("Error found When try to insert record!!");
		}
	}


	public static function calculateDaysSinceLastReview() {
		$time_start = microtime(true);

		$links = \Models\ScheduleLink
			::whereHas('Schedule',function($query) {
				$query
					->where("type","meeting")
					->whereHas("VisitType",function($query) {
						$query
							->where("is_enable_days_since_last_review", 1)
						;
					})
				;
			})
			->rightJoin('schedules','schedules.id','=','schedule_links.schedule_id')
			->select([
				'schedule_links.*',
				DB::raw('max(schedules.start_date) as max_start_date')
			])
			->groupBy('schedule_links.link_id')
			->where("schedule_links.type", "users")
			->where("schedule_links.completion_status", "%%event_completion_state_completed%%")
			->get()
		;

		$total_entries = count($links);

		$today = \Carbon\Carbon::now();

		$updated_entries = 0;
		foreach ($links AS $link) {
			$startDate = \Carbon\Carbon::parse($link->max_start_date);
			$days_since_last_review = $startDate->diffInDays($today);

			$user = \Models\User
			::where('id', $link->link_id)
			->first()
			;
			if ($user) {
				$user->days_since_last_review = $days_since_last_review;
				$user->timestamps = false;
				$user->saveWithoutEvents();

				if ($user->wasChanged()) {
					$updated_entries++;
				}
			}
		}
		$time_end = microtime(true);
		$time = number_format(($time_end - $time_start), 2);
		return "\n\n Finished calculateDaysSinceLastReview in: {$time} seconds, {$total_entries} total entries, {$updated_entries} updated entries.\n";
	}

	public function UserForms(){
		return $this
			->hasMany('Models\UserForm','user_id','id')
		//->where('status', '1')
		;
	}

	public function UserWorkFlow(){
		return $this
			->hasOne('Models\UserWorkflowForm','user_id','id')
		;
  }
  public function custom() {
       return $this->hasOne(FormValueView::class,'user_id','id');
  }
    public function customUser() {
        return $this->hasOne(FormValueView::class,'user_id','id');
    }
  /**
	 * Get the user IDs assigned to a specific manager and Department.
	 *
	 * @param int $managerId The ID of the manager.
	 * @param int $departmentId   The ID of the group.
	 * @param bool $isAssignerAdmin Indicates whether the assigner is an admin or not. Defaults to false.
	 *
	 * @return \Illuminate\Support\Collection The collection of user IDs.
	 */
	public static function getUserIdsByManagerAndDepartment($managerId, $departmentId, $isAssignerAdmin = false)
	{

		$query = self::where([['department_id', $departmentId],['department_id','!=', '']]);

		if (!$isAssignerAdmin) {
			$query->join('manager_users', 'manager_users.user_id', '=', 'users.id')
				->where('manager_users.manager_id', $managerId)
				->whereNull('manager_users.deleted_at');
		}

		return $query->pluck('users.id')->toArray();
	}

  /**
	 * Get the user IDs assigned to a specific manager and Designation.
	 *
	 * @param int $managerId The ID of the manager.
	 * @param int $designationId   The ID of the group.
	 * @param bool $isAssignerAdmin Indicates whether the assigner is an admin or not. Defaults to false.
	 *
	 * @return \Illuminate\Support\Collection The collection of user IDs.
	 */
	public static function getUserIdsByManagerAndDesignation($managerId, $designationId, $isAssignerAdmin = false)
	{
		$query = self::where([['designation_id', $designationId],['designation_id','!=', '']]);

		if (!$isAssignerAdmin) {
			$query->join('manager_users', 'manager_users.user_id', '=', 'users.id')
				->where('manager_users.manager_id', $managerId)
				->whereNull('manager_users.deleted_at');
		}

		return $query->pluck('users.id')->toArray();
	}

	// return enabled user who is not expired.
	public static function returnValidUser($user_id = false, $relationships = []) {
		$response =  false;
		if ($user_id) {
			$userObj = \Models\User
				::where('users.status', 1)
				->where(function($query) {
					$query
						->whereNull("users.expiration_dt")
						->orWhere("users.expiration_dt", ">", \Carbon\Carbon::now())
					;
				})
			;
			// hardcoding this
			foreach ($relationships as $key => $relationship) {
				switch ($relationship) {
					case 'role':
						$userObj = $userObj
							->with('role')
						;
						break;
				}
			}
			$userObj = $userObj
				->find($user_id)
			;
			if ($userObj) {
				$response = $userObj;
			}
		}
		return $response;
	}

	// Attach to any model build, if needed. show only enabled and not expired users
	public function scopeValidUser($query) {
		return $query
			->where('users.status', 1)
			->where(function($query) {
				$query
					->whereNull("users.expiration_dt")
					->orWhere("users.expiration_dt", ">", \Carbon\Carbon::now())
				;
			})
		;
	}

	// Return users that has access to current role
	public function scopeAcessFilter($query) {
		if (
			!\APP\Auth::accessAllLearners() &&
			!\APP\Auth::isAdmin()
		) {
			$query = $query
				->whereIn("users.id", function($query) {
					$query
						->select("user_id")
						->from("manager_users")
						->where("manager_id", "=", \APP\Auth::getUserId())
						->whereNull('manager_users.deleted_at')
					;
				})
			;
		}

		if (
			!\APP\Auth::isAdmin() &&
			!\APP\Auth::accessAllCompanies() &&
			\APP\Auth::getUserCompanyId()
		) {
			$query = $query
				->where('users.company_id', \APP\Auth::getUserCompanyId())
			;
		}
		return $query;
	}

    public static function notifyUserIsEnabled($user)
    {
        $template = \Models\EmailTemplate::query()
            ->where('name', '=', 'Enabled User Notification')
            ->where('status', true)
            ->first();

        if($template) {
            // When user is enabled fire email
            $email_queue = new \Models\EmailQueue;
            $email_queue->email_template_id = $template->id;
            $email_queue->recipients = [intval($user->id)];
            $email_queue->custom_variables = json_encode([
                'USER_FNAME' => $user->fname,
                'USER_LNAME' => $user->lname,
                'CONFIG_LMSUrl' => $GLOBALS["CONFIG"]->LMSUrl,
                'REGARDS' => $GLOBALS["CONFIG"]->Regards,
            ]);
            $email_queue->save();
        }
	}

	public static function getAccessAllLearnersUsers($form, $role_id = null)
	{
		$formSignOffRoles = $form->FormSignOffRoles->pluck('role_id')->toArray();
		$users = self::whereHas('Role', function ($subQuery) {
					$subQuery->where('access_all_learners', 1);
		})->get();
		foreach ($users as $key => $user) {
			if (empty($role_id)) {
				if (!in_array($user->role_id, $formSignOffRoles)) {
					$users->forget($key);
				}
			} else {
				if (!in_array($user->role_id, $formSignOffRoles) || $user->role_id != $role_id) {
					$users->forget($key);
				}
			}
		}
		return $users;
	}

	public function centralAuthTokens()
	{
		return $this->hasOne(CentralAuthToken::class, 'user_id', 'id');
	}
    public function progressionOutcomeTypes()
    {
        return $this->hasManyThrough(
            IlrProgressionOutcomeType::class,
            IlrLearnerDestinationAndProgression::class,
            'user_id',            // Foreign key on IlrLearnerDestinationAndProgression
            'value',              // Local key on IlrProgressionOutcomeType (assumes 'value' is primary or unique)
            'id',                 // Local key on User
            'OutType'             // Foreign key on IlrLearnerDestinationAndProgression that links to IlrProgressionOutcomeType.value
        );
    }
    public function employmentStatuses()
    {
        return $this->hasManyThrough(
            IlrEmploymentStatus::class,
            IlrLearnerEmploymentStatus::class,
            'user_id',        // Foreign key on IlrLearnerEmploymentStatus
            'id',             // Local key on IlrEmploymentStatus
            'id',             // Local key on User
            'EmpStat'         // Foreign key on IlrLearnerEmploymentStatus referencing IlrEmploymentStatus.id
        );
    }
    public function employmentStatus()
    {
        return $this->hasOne(
            IlrLearnerEmploymentStatus::class,
            'id',
            'user_id'
        );
    }
    public function aimTypes()
    {
        return $this->hasManyThrough(
            IlrLearningDeliveryAimType::class,  // Final model
            IlrLearningDelivery::class,         // Intermediate model
            'user_id',                           // Foreign key on IlrLearningDelivery table
            'id',                              // Foreign key on IlrLearningDeliveryAimType table
            'id',                              // Local key on User table
            'AimType'                    // Local key on IlrLearningDelivery table
        );
    }
    public function ilrLearningDeliveries()
    {
        return $this->belongsTo(IlrLearningDelivery::class, 'id', 'user_id');
    }
    public function ilrFundingModels()
    {
        return $this->hasManyThrough(
            IlrLearningDeliveryFundingModel::class,
            IlrLearningDelivery::class,
            'user_id',
            'id',
            'id',
            'FundModel'
        );
    }

    public function ilrProgrammeTypes()
    {
        return $this->hasManyThrough(
            'Models\IlrLearningDeliveryProgrammeType',
            'Models\IlrLearningDelivery',
            'user_id',
            'id',
            'id',
            'ProgType'
        );
    }
    public function ilrOutcomes()
    {
        return $this->hasManyThrough(
            'Models\IlrLearningDeliveryOutcome',
            'Models\IlrLearningDelivery',
            'user_id',                    // Foreign key on IlrLearningDelivery
            'ilr_learning_delivery_id',   // Foreign key on IlrLearningDeliveryOutcome
            'id',                         // Local key on User
            'id'                          // Local key on IlrLearningDelivery
        );
    }
    public function ilrCompletionStatuses()
    {
        return $this->hasManyThrough(
            'Models\IlrLearningDeliveryCompletionStatus',
            'Models\IlrLearningDelivery',
            'user_id',                          // FK on IlrLearningDelivery
            'ilr_learning_delivery_id',         // FK on IlrLearningDeliveryCompletionStatus
            'id',                               // Local key on User
            'id'                                // Local key on IlrLearningDelivery
        );
    }
    public function ilrLdmMonitorings()
    {
        return $this->hasManyThrough(
            'Models\IlrLearningDeliveryMonitoring',
            'Models\IlrLearningDelivery',
            'user_id',                      // FK on IlrLearningDelivery
            'ilr_learning_delivery_id',     // FK on IlrLearningDeliveryMonitoring
            'id',                           // Local key on User
            'id'                            // Local key on IlrLearningDelivery
        );
    }
    public function ilrFinancialRecords()
    {
        return $this->hasMany(\Models\IlrLearningDeliveryFinancialRecord::class, 'user_id', 'id');
    }
    public function ilrFinancialRecordCodes()
    {
        return $this->hasManyThrough(
            IlrLearningDeliveryFinancialRecordCode::class,
            IlrLearningDeliveryFinancialRecord::class,
            'user_id',
            'id',
            'id',
            'AFinCode'
        );
    }
    public function ilrFinancialRecordTypes()
    {
        return $this->hasManyThrough(
            IlrLearningDeliveryFinancialRecordType::class,
            IlrLearningDeliveryFinancialRecord::class,
            'user_id',
            'id',
            'id',
            'AFinType'
        );
    }
    public function priorAttainment()
    {
        return $this->belongsTo(PriorAttainment::class, 'PriorAttain', 'id');
    }

}
