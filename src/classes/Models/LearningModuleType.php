<?php
namespace Models;

class LearningModuleType extends \Illuminate\Database\Eloquent\Model {
	protected $table = 'learning_module_types';
	protected $fillable = ['id', 'name', 'status', 'slug', 'field', 'fit_for_evidence'];
	protected $casts = [
		'custom' => 'boolean',
		'fit_for_evidence' => 'boolean'
	];

	// Overwrite name of some learning module types.
	public function getNameAttribute($value) {

		if (
			isset($GLOBALS["CONFIG"]->licensing) &&
			count($GLOBALS["CONFIG"]->licensing['overwriteModuleType']) > 0 &&
			isset($GLOBALS["CONFIG"]->licensing['overwriteModuleType'][$value]) &&
			$GLOBALS["CONFIG"]->licensing['overwriteModuleType'][$value]
		) {
			$value = $GLOBALS["CONFIG"]->licensing['overwriteModuleType'][$value];
		}

		if ($this->slug == 'upload') {
			//Look into default lables for resource_type__upload overwrite.
			$label = \Models\DefaultLabel
				::where('slug', 'resource_type__upload')
				->where('status', true)
				->first()
			;
			if (
				$label &&
				$label->overwrite
			) {
				$value = $label->overwrite;
			}
		}

		if ($this->slug == 'webpage') {
			//Look into default lables for resource_type__webpage overwrite.
			$label = \Models\DefaultLabel
				::where('slug', 'resource_type__webpage')
				->where('status', true)
				->first()
			;
			if (
				$label &&
				$label->overwrite
			) {
				$value = $label->overwrite;
			}
		}

		return $value;
	}

	// Returns ID from slug
	public static function getId($slug = false) {
		$response = false;
		if ($slug) {
			$query = \Models\LearningModuleType::where('slug', $slug)->first();
			if ($query) {
				$response = $query->id;
			}
		}
		return $response;
	}

	public function LearningModuleTypeParameter() {
		return $this->hasMany('Models\LearningModuleTypeParameter', 'learning_module_types_id', 'id');
	}

	public function getFieldAttribute($value) {
		$filed = json_encode($value);
		return $filed;
	}

	protected static function boot() {
		parent::boot();

		// Exclude disabled resources!
		static::addGlobalScope('type_filter', function (\Illuminate\Database\Eloquent\Builder $builder) {
			$builder->where('status', 1);
        });

		static::saved(function($entry) {
			cache()->forget('course_all');
			cache()->forget('event_course_all');
			cache()->forget('learning_module_all');
		});
    }

}
