<?php

namespace Models;

use APP\Controllers\CustomReportController;
use Psr\Container\ContainerInterface;
use Illuminate\Database\Capsule\Manager as DB;

class UserDownload extends \Illuminate\Database\Eloquent\Model
{
    use \Illuminate\Database\Eloquent\SoftDeletes;

    public static function processAll()
    {
        global $app;
        $container = $app->getContainer();

        if (!$container) {
            return "error while processing";
        }

        $processed = 0;

		$logger = \APP\LoggerHelper::getLogger();

        self::where('status', 'scheduled for processing')
            ->orderBy('id')
            ->chunkById(100, function ($entries) use (&$processed, $container, $logger) {
                foreach ($entries as $entry) {
                    $time_start = microtime(true);
                    try {
                        $added_by_user = \Models\User::where('id', $entry->user_id)
                            ->validuser()
                            ->first();

                        if (!$added_by_user) {
                            continue;
                        }

                        \APP\Auth::loginCronUser($entry->user_id);
                        $file_generated = false;
                        $download_file_name = '';

                        $entry->status = 'processing';
                        $entry->save();

                        if ($entry->type === 'Legacy Custom Reports') {
                            $params = json_decode($entry->params, true);
                            $params_original = $params['table_state'];
                            $params_original['search'] = $params_original['search']['predicateObject'] ?? [];
                            $review_id = $params["id"];
                            $query_id = 'customreviewList';
                            $QueryBuilder = "\\APP\\QueryBuilder\\" . $query_id;
                            $query = $QueryBuilder::generate($params_original, false);
                            $data = \APP\SmartTable::searchPaginate($params_original, $query, false, false, false, false, true);

                            $stream_response = \Models\CustomReview::streamDownloadFile(
                                $review_id,
                                $data,
                                $GLOBALS["CONFIG"]->LMSTempPath,
                                false,
                                $params_original,
                                $entry
                            );

                            if ($stream_response['status'] === 'error') {
                                $entry->status = 'error';
                                $entry->save();
                                continue;
                            }

                            $file_generated = true;
                            $download_file_name = $stream_response['file_name'];

                            $entry->export_id = $stream_response['export_id'];
                            $entry->rows_processed = $stream_response['rows_processed'];
                        } elseif ($entry->type === 'Data Tables') {
                            $reportController = $container->get('APP\\Controllers\\CustomReportController');
                            $params = json_decode($entry->args, true);
                            $customReport = $params;
                            $reports = $reportController->generateReport($customReport, $params);

                            $export_fields = [];
                            $export_fields_type = [];

                            if (!empty($params['selected_fields'])) {
                                foreach ($params['selected_fields'] as $value) {
                                    $export_fields[$value["name"]] = $value['slug'];
                                    $export_fields_type[$value["name"]] = $value['type'];
                                }
                            } else {
                                $fields = $customReport['fields'];
                                usort($fields, fn($a, $b) => ($a['order'] ?? 0) <=> ($b['order'] ?? 0));
                                foreach ($fields as $value) {
                                    $export_fields[$value["name"]] = $value['column_name'];
                                    $export_fields_type[$value["name"]] = $value['type'];
                                }
                            }

                            $download_file_name = \APP\Tools::safeName(uniqid($entry->name . '__' . $entry->type . '_'), "_") . ".xlsx";
                            $export_data = ['export_fields' => $export_fields, 'file_name' => $download_file_name];

                            $reports = \APP\SmartTable::searchPaginate($params, $reports, false, false);
                            $entry->rows_processed = count($reports);
                            $entry->status = 'genereting file';
                            $entry->save();

                            $data = \Models\CustomReview::transformReports($reports, $export_fields, $export_fields_type, $reportController);

                            foreach ($export_data['export_fields'] as $key => $field) {
                                $export_data['export_fields'][$key] = str_replace(['.', '+'], '_', $field);
                            }

                            $entry->export_id = \APP\Tools::generateExcelDownload(
                                $data,
                                $export_data['export_fields'],
                                $GLOBALS["CONFIG"]->LMSTempPath . $download_file_name,
                                [],
                                null,
                                true
                            );

                            $file_generated = true;
                        }

                        $time_end = microtime(true);
                        $entry->generation_seconds = $time_end - $time_start;
                        $entry->status = 'ready';
                        $entry->save();
                        $processed++;

                        if ($file_generated) {
                            $template = \Models\EmailTemplate::getTemplate('file_download_ready_notification');
                            if ($template) {
                                $DOWNLOAD_LINK = $GLOBALS["CONFIG"]->LMSUrl . 'download/excel/' . $download_file_name . '/auth';
                                if (\APP\Tools::getConfig('redirectAllLinksThroughSSO')) {
                                    $DOWNLOAD_LINK = $GLOBALS["CONFIG"]->LMSUrl . 'saml/?ReturnTo=' . urlencode($DOWNLOAD_LINK);
                                }
                                \Models\EmailQueue::create([
                                    'email_template_id' => $template->id,
                                    'recipients' => [$added_by_user->id],
                                    'from' => null,
                                    'custom_variables' => json_encode([
                                        'USER' => $added_by_user->fname . ' ' . $added_by_user->lname,
                                        'FILE_NAME' => $download_file_name,
                                        'REPORT_NAME' => $entry->name,
                                        'DOWNLOAD_LINK' => $DOWNLOAD_LINK,
                                    ]),
                                ]);
                            }
                        }
                        \APP\Auth::logout();
                    } catch (\Throwable $e) {
                        $entry->status = 'error';
                        $entry->save();
						$logger->error("Failed to process user Download", [
							'error' => $e,
							'entry_id' => $entry->id,
							'user_id' => $entry->user_id,
							'entry_type' => $entry->type,
							'entry_name' => $entry->name,
						]);
                        continue;
                    }
                }
            });

        return "$processed entries processed";
    }
}
