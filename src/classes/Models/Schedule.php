<?php

namespace Models;

use APP\Teams;
use APP\Tools;
use Carbon\Carbon;
use Illuminate\Database\Capsule\Manager as DB;

class Schedule extends \Illuminate\Database\Eloquent\Model {

	use \Staudenmeir\EloquentHasManyDeep\HasRelationships;
	use \Illuminate\Database\Eloquent\SoftDeletes;

	protected $casts = [
		'cron_task' => 'boolean',
		'status' => 'boolean',
		'reminder_sent' => 'boolean',
		'visible_learner' => 'boolean',
		'visible_learner_task' => 'boolean',
		'visible_schedule' => 'boolean',
		'outlook_event_response' => 'array',
		'refreshed' => 'boolean',
		'do_not_send_advance_notifications' => 'boolean',
		'all_day_event' => 'boolean',
	];
	 protected $fillable = [
		'name',
		'duration',
		'start_date',
	 ];

    public function Coupons()
    {
        return $this->belongsToMany('Models\Coupon', 'coupon_links', 'type_id', 'coupon_id')->wherePivot('type','schedule')->withTimestamps();
    }

	public function DeletedBy() {
		return $this->belongsTo('Models\User', 'deleted_by', 'id');
	}

	public function CreatedBy() {
		return $this->belongsTo('Models\User', 'created_by', 'id');
	}

	public function Permissions() {
		return $this->hasMany('Models\SchedulePermission', 'schedule_id', 'id');
	}
	public function Children() {
		return $this->hasMany('Models\Schedule', 'parent_id', 'id');
	}

	public function VisitType() {
		return $this->belongsTo('Models\ScheduleVisitType');
	}

	public function Category() {
		return $this->belongsTo('Models\LearningModuleCategory', 'category_id', 'id');
	}

	public function Owner() {
		return $this
			->belongsTo('Models\SchedulePermission', 'id', 'schedule_id')
			->where('type', 'owner')
		;
	}

	public function Files() {
		return $this
			->hasMany('Models\File', 'table_row_id', 'id')
			->where('table_name', 'schedules')
		;
	}

	public function Comments() {
		return $this
			->hasMany('Models\Comment', 'table_row_id', 'id')
			->where('table_name', 'schedules')
		;
	}

	public function ScheduleLink() {
		return $this
			->hasOne('Models\ScheduleLink', 'link_id', 'id')
			->where('type', 'schedules')
		;
	}

	/*
	public function Resources() {
		return $this
			->hasManyThrough(
				'Models\LearningModule',
				'Models\ScheduleLink',
				'schedule_id',
				'id',
				'id',
				'link_id'
			)
			->where('schedule_links.type', 'resources')
			->where('schedule_links.status', true)
		;
	}
	*/
	// Test bed for https://github.com/staudenmeir/eloquent-has-many-deep
	public function Resources() {
		return $this
			->hasManyDeep(
				'Models\LearningModule',
				['Models\ScheduleLink'],
				[
					'schedule_id',
					'id',
				],
				[
					'id',
					'link_id',
				]
			)
			->where('schedule_links.type', 'resources')
			->where('schedule_links.status', true)
			->withIntermediate(
				'Models\ScheduleLink'
			)
		;
	}

	// Linked events!
	public function Schedules() {
		return $this
			->hasManyDeep(
				'Models\Schedule',
				['Models\ScheduleLink'],
				[
					'schedule_id', // check if schedule_id equals current schedule id
					'id',
				],
				[
					'id', // find all schedules entries using link_id from schedule_links as schedules id
					'link_id',
				]
			)
			->where('schedule_links.type', 'schedules')
			->where('schedule_links.status', true)
			->withIntermediate(
				'Models\ScheduleLink'
			)
		;
	}

	// Linked parent events!
	public function ScheduleParents() {
		return $this
			->hasManyDeep(
				'Models\Schedule',
				['Models\ScheduleLink'],
				[
					'link_id', // check if link_id equals current schedule id
					'id',
				],
				[
					'id', // find all schedules entries using schedule_id from schedule_links as schedules id
					'schedule_id',
				]
			)
			->where('schedule_links.type', 'schedules')
			->where('schedule_links.status', true)
			->withIntermediate(
				'Models\ScheduleLink'
			)
		;
	}

	public function sheduleChild() {
		return $this
			->hasManyDeep(
				'Models\Schedule',
				['Models\ScheduleLink'],
				[
					'link_id',
					'id',
				],
				[
					'id',
					'schedule_id',
				]
			)
			->where('schedule_links.type', 'schedules')
			->where('schedule_links.status', true)
			->withIntermediate(
				'Models\ScheduleLink'
			)
		;
	}

	public static function getChild($schedule_id, $schedules = []) {
		$schedule = \Models\Schedule
			::where('id', $schedule_id)
			->with(['Schedules' => function ($query) {
				$query = $query
					->with(['Users' => function ($query) {
						$query = $query
							->select(
								'users.id'
							)
							->where('users.status', true)
						;
					}])
				;
			}])
			->first()
		;


		if (
			$schedule &&
			!empty($schedule->schedules->toArray())
		) {
			foreach($schedule->schedules as $child_schedule) {
				if (!in_array($child_schedule->id, $schedules)) {
					$schedules[] = $child_schedule->id;
					$schedules = self::getChild($child_schedule->id, $schedules);
				}
			}
		}
		return $schedules;
	}


	// BROKEN, NEED LARAVEL 5.7! For that need PHP 7.1+
	public function Lesson() {
		return $this
			->hasOneThrough(
				'Models\LearningModule',
				'Models\ScheduleLink',
				'schedule_id',
				'id',
				'id',
				'link_id'
			)
			->where('schedule_links.type', 'lesson')
			->where('schedule_links.status', true)
		;
	}

	public function Lessons() {
		return $this
			->hasManyDeep(
				'Models\LearningModule',
				['Models\ScheduleLink'],
				[
					'schedule_id',
					'id',
				],
				[
					'id',
					'link_id',
				]
			)
			->where('schedule_links.type', 'lesson')
			->where('schedule_links.status', true)
		;
	}

	public function Standards() {
		return $this
			->hasManyDeep(
				'Models\ApprenticeshipStandard',
				['Models\ScheduleLink'],
				[
					'schedule_id',
					'id',
				],
				[
					'id',
					'link_id',
				]
			)
			->where('schedule_links.type', 'programmes')
			->where('schedule_links.status', true)
		;
	}




	// Identical to Standards, just need that consistent naming in events!
	public function Programmes() {
		return $this
			->Standards()
		;
	}

	public function Users() {
		return $this
			->hasManyDeep(
				'Models\User',
				['Models\ScheduleLink'],
				[
					'schedule_id',
					'id',
				],
				[
					'id',
					'link_id',
				]
			)
			->where('schedule_links.type', 'users')
			->where('schedule_links.status', true)
			->withIntermediate('Models\ScheduleLink', ['id', 'completion_status', 'order', 'learner_requirement'])
		;
	}

    public function CancelledUsers() {
        return $this
            ->hasManyDeep(
                'Models\User',
                ['Models\ScheduleLink'],
                [
                    'schedule_id',
                    'id',
                ],
                [
                    'id',
                    'link_id',
                ]
            )
            ->whereIn('schedule_links.type', ['users','users_queue'])
            ->where('schedule_links.status', false)
            ->whereColumn('schedule_links.link_id', 'schedule_links.deleted_by')
            ->select('users.fname', 'users.lname', 'users.email', 'users.id', 'schedule_links.cancellation_reason','schedule_links.type')
            ->withTrashed('schedule_links.deleted_at')
            ;
    }


	public function Forms() {
		return $this
			->hasManyDeep(
				'Models\Form',
				['Models\ScheduleLink'],
				[
					'schedule_id',
					'id',
				],
				[
					'id',
					'link_id',
				]
			)
			->where('schedule_links.type', 'forms')
			->where('schedule_links.status', true)
		;
	}

	public function Waiting() {
		return $this
			->hasManyDeep(
				'Models\User',
				['Models\ScheduleLink'],
				[
					'schedule_id',
					'id',
				],
				[
					'id',
					'link_id',
				]
			)
			->where(function ($query) {
				$query = $query
					->where('schedule_links.type', 'users_queue')
				;
			})
			->where('schedule_links.status', true)
			->orderBy('schedule_links.order', 'ASC')
		;
	}

	public function WaitingForApproval() {
		return $this
			->hasManyDeep(
				'Models\User',
				['Models\ScheduleLink'],
				[
					'schedule_id',
					'id',
				],
				[
					'id',
					'link_id',
				]
			)
			->where('schedule_links.approved', 0)
			->where(function ($query) {
				$query = $query
					->where('schedule_links.type', 'users_queue')
					->orWhere('schedule_links.type', 'users')
				;
			})
			->where('schedule_links.status', true)
			->orderBy('schedule_links.updated_at', 'ASC')
		;
	}


	public function LinkedUsers() {
		return $this
			->hasManyDeep(
				'Models\User',
				['Models\ScheduleLink'],
				[
					'schedule_id',
					'id',
				],
				[
					'id',
					'link_id',
				]
			)
			// ->where('schedule_links.approved', 0)
			->where(function ($query) {
				$query = $query
					->where('schedule_links.type', 'users_queue')
					->orWhere('schedule_links.type', 'users')
				;
			})
			// ->where('schedule_links.status', true)
			->orderBy('schedule_links.updated_at', 'ASC')
		;
	}


    public function RejectedUsers()
    {
        return $this->hasManyThrough(
            'Models\RejectRequestItems',
            'Models\ScheduleLink',
            'schedule_id',
            'item_id',
            'id',
            'id'
        );
    }


	public function Managers() {
		return $this
			->hasManyDeep(
				'Models\User',
				['Models\ScheduleLink'],
				[
					'schedule_id',
					'id',
				],
				[
					'id',
					'link_id',
				]
			)
			->where('schedule_links.type', 'managers')
			->where('schedule_links.status', true)
		;
	}

	public function Visitors() {
		return $this
			->hasManyThrough(
				'Models\User',
				'Models\ScheduleLink',
				'schedule_id',
				'id',
				'id',
				'link_id'
			)
			->where('schedule_links.type', 'managers')
			->where('schedule_links.status', true)
			//->where('schedule_links.manager_visitor', true)
		;
	}

	public function Departments() {
		return $this
			->hasManyThrough(
				'Models\Department',
				'Models\ScheduleLink',
				'schedule_id',
				'id',
				'id',
				'link_id'
			)
			->where('schedule_links.type', 'departments')
			->where('schedule_links.status', true)
		;
	}

	public function Groups() {
		return $this
			->hasManyThrough(
				'Models\Group',
				'Models\ScheduleLink',
				'schedule_id',
				'id',
				'id',
				'link_id'
			)
			->where('schedule_links.type', 'groups')
			->where('schedule_links.status', true)
		;
	}

	public function Links() {
		return $this->hasMany('Models\ScheduleLink', 'schedule_id', 'id');
	}
	public function ResourceLinks() {
		return
			$this
				->Links()
				->where('status', true)
				->where('type', 'resources')
		;
	}

	public function UserLink() {
		return
			$this
				->hasOne('Models\ScheduleLink', 'schedule_id', 'id')
				->where('status', true)
				->where('type', 'users')
		;
	}

    public function Attended() {
        return $this
            ->hasManyDeep(
                'Models\User',
                ['Models\ScheduleLink'],
                [
                    'schedule_id',
                    'id',
                ],
                [
                    'id',
                    'link_id',
                ]
            )
            ->where('schedule_links.type', 'users')
            ->where('schedule_links.status', true)
            ->whereIn('schedule_links.completion_status', ['%%event_completion_state_completed%%', 'Completed'])
            ->withIntermediate('Models\ScheduleLink', ['id', 'completion_status', 'order'])
            ;
    }

	public function Venue() {
		return
			$this
				->hasOne('Models\TableExtension','table_id','id')
				->where('table', 'schedules')
		;
  }
  /**
	* Get Table Venue details from TableExtension and Venu table
	*
	* @return
	*/
  public function VenueDeatils(){
		return $this->hasOneThrough(Venue::class,TableExtension::class,'table_id','id','id','value')->where('table','schedules');
  }

	// Checks if event and assigned lesson to it was created by same person, if that is the case allow updating lesson resources or vice verse.
	public static function sameCreator($learning_course_module = false) {
		// In this case will find out event from learning_course_modules using learning_course_id
		$lesson = \Models\LearningModule::find($learning_course_module->learning_course_id);
		if (!$lesson->created_by) {
			return false;
		}
		$event = \Models\Schedule
			::whereHas("Links", function($query) use ($lesson) {
				$query = $query
					->where('schedule_links.type', 'lesson')
					->where('schedule_links.link_id', $lesson->id)
					->where('schedule_links.status', true)
				;
			})
			->where('created_by', $lesson->created_by)
			->first()
		;
		if (!$event) {
			return false;
		}

		return true;
	}

	public static function createEvent ($data, $manager = false, $return_full = false, $duplicate_check = false , $user_id = false, $import = false) {
		// IF CD and managers are in data, create unique event for each manager

		$data = \Models\Schedule::setAllDayEventTime($data);

        if(empty($data["deadline_at"])) {
            $data['deadline_at'] = \APP\Tools::calculateDeadline($data['start_date']);
        }

        if(empty($data["drop_off_deadline_at"])) {
            $data['drop_off_deadline_at'] = \APP\Tools::calculateEventDropOffDeadline($data['start_date']);
        }

		if ($duplicate_check) {
			$event = \Models\Schedule::firstOrNew(
				[
					"name" => isset($data["name"]) ? $data["name"] : null,
					"start_date" => isset($data["start_date"]) ? \Carbon\Carbon::parse($data["start_date"]) : null,
					"end_date" => isset($data["end_date"]) ? \Carbon\Carbon::parse($data["end_date"]) : null,
					"duration" => isset($data["duration"]) ? $data["duration"] : null,
                    'deadline_at' => $data["deadline_at"],
                    'drop_off_deadline_at' => $data["drop_off_deadline_at"]
				]
			);
		} else {
			$event = new \Models\Schedule;
			$event->name = isset($data["name"]) ? $data["name"] : null;
			$event->start_date = isset($data["start_date"]) ? $data["start_date"] : null;
			$event->duration = isset($data["duration"]) ? $data["duration"] : null;
			$event->deadline_at = $data["deadline_at"];
            $event->drop_off_deadline_at = $data["drop_off_deadline_at"];
		}

		$event->cost = isset($data["cost"]) ? $data["cost"] : null;
		$event->description = isset($data["description"]) ? $data["description"] : '';
		$event->location = isset($data["location"]) ? $data["location"] : '';
		$event->type = isset($data["type"]) ? $data["type"] : null;
		$event->category_id = isset($data["category_id"]) ? $data["category_id"] : null;
		$event->visit_type_id = isset($data["visit_type_id"]) ? $data["visit_type_id"] : null;
		$event->end_date = isset($data["end_date"]) ? \Carbon\Carbon::parse($data["end_date"]) : null;
		$event->all_day_event = isset($data["all_day_event"]) ? $data["all_day_event"] : false;
		$event->parent_id = isset($data["parent_id"]) ? $data["parent_id"] : null;
		$event->created_by = $manager && isset($manager['id']) ? $manager['id'] : ($user_id ?: \APP\Auth::getUserId());
		$event->created_for = isset($data["created_for"]) ? $data["created_for"] : null;
		$event->visible_learner = isset($data["visible_learner"]) ? $data["visible_learner"] : false;
		$event->visible_learner_task = isset($data["visible_learner_task"]) ? $data["visible_learner_task"] : false;
		$event->visible_schedule = isset($data["visible_schedule"]) ? $data["visible_schedule"] : true;
		$event->enrole_any_learner = isset($data["enrole_any_learner"]) && $data["enrole_any_learner"] != 'false' && $data["enrole_any_learner"] != 'no' ? true : false;
		$event->maxclass = isset($data["maxclass"]) ? $data["maxclass"] : 0;
		$event->minclass = isset($data["minclass"]) ? $data["minclass"] : 0;
		$event->previous_start_date = $event->start_date;
		if (
			\APP\Tools::getConfig("enableGlobalOutlookIntegration",false,$event->created_by) &&
			\APP\Tools::getConfig("addAllEventstoOutlook",false,$event->created_by)
		) {
			$event->outlook_integration = 1;
			$event->outlook_refresh_token = "use_global";
		}

		$event->save();
		if (isset($data['room_email']) && $data['room_email'] && Tools::getConfig('useOutlookVenues',false,$even->created_by)) {
			$teams = new Teams();
			if (!isset($data['end_date'])) {
				$end_date = Carbon::create($data['start_date'])->addMinute($data['duration']);
			} else {
				$end_date = $data['end_date'];
			}
			$defaultTimeZone =  Tools::getConfig('defaultTimeZone',false,$event->created_by);
			$teams->token = Venue::getToken();
			$locations = Venue::getLocation($data['room_email']);
			if($locations)
			{
				$locations = $locations->value;
				$venue = Venue::where('room_email',$data['room_email'])->first();
				if(!$venue){
					$address = "";
					$postcode = "";
					if($locations[0]->address)
					{
						$address = $locations[0]->address->street."\n".$locations[0]->address->city."\n".$locations[0]->address->state."\n".$locations[0]->address->countryOrRegion;
						$postcode = $locations[0]->address->postalCode;
					}
					$venue = Venue::create(['room_email'=>$data['room_email'],'name'=>$locations[0]->displayName,'address'=>$address,'postcode'=>$postcode,'capacity'=>$locations[0]->capacity,'status'=>1]);
				}
				if(!isset($data['extended'])){
					$data['extended']=[];
				}
				$data['extended']['venue_id']=$venue->id;

			}
			$event->save();
		}
		// IF extension fields are present loop them and update data accordingly.
		if (isset($data["extended"])) {
			foreach ($data["extended"] as $field_name => $value) {
				\Models\TableExtension::updateField('schedules', $event->id, $field_name, $value);
			}
		}

		// If "created_for", link event with that user!
		if ($event->created_for) {
			$link = \Models\ScheduleLink::firstOrNew(
				[
					"schedule_id" => $event->id,
					"type" => 'users',
					"link_id" => $event->created_for,
				]
			);
			$link->status = true;
			$link->cron_task = true;
			$link->save();

			$relationArray=\Models\Schedule::getEventTypeRelation($event->id);

			if($relationArray){
				// $forms=array_unique($forms);
				foreach ($relationArray as $form) {
					$form_data=['type'=>'forms',
					'schedule_type'=>'forms',
					'link_id'=>$form['form_id'],
					'schedule_id'=>$event->id,
					'schedule_linked_type'=>'form_workflow',
					"schedule_linked_type_id"=>$form['workflow_id']];
					$form_data=array_merge($form_data,$form);
					\Models\Schedule::assignFormsToUser($form_data);
				}
			}
			$data['schedule_id']=$event->id;
			$data['event_date']=$event->start_date;
			\Models\Schedule::assignUsersToForm($data);



		}

		// If person who creates event is manager, link manager with that event!
		if (
			(
				\APP\Auth::isManager() ||
				\APP\Auth::isCD()
			) &&
			!$event->parent_id &&
			!$import
		) {
			$link = \Models\ScheduleLink::firstOrNew(
				[
					"schedule_id" => $event->id,
					"type" => 'managers',
					"link_id" => ($manager && isset($manager['id']) ? $manager['id'] : ($user_id ?: \APP\Auth::getUserId())),
				]
			);
			$link->status = true;
			$link->cron_task = true;
			$link->save();
		}


	//   }
		// If parent id is given, this is additional time for schedule, no need to add for permissions or cron task
		if (!$event->parent_id) {

			$event->cron_task = true;
			$event->save();

			// Create entry in schedule_permissions for user that he is owner
			$permission = \Models\SchedulePermission::firstOrNew(
				[
					"schedule_id" => $event->id,
					"type" => 'owner',
				]
			);
			$permission->user_id = $user_id ?: \APP\Auth::getUserId();
			$permission->save();


			// Currently event works with lessons, when creating event new lesson can be created or exisitng one can be chosen.
			if ($event->type) {
				$lesson_id = false;

				$link_lesson = \Models\ScheduleLink
					::where('schedule_id', $event->id)
					->where('type', 'lesson')
					->first()
				;
				if ($link_lesson) {
					 $data['lesson_id'] = $link_lesson->link_id;
				}

				if (
					$data["type"] == 'lesson' &&
					isset($data["lesson_id"]) &&
					$data["lesson_id"] == 'new'
				) {
					$new_lesson = new \Models\LearningModule();
					$new_lesson->name = $event->name;
					$new_lesson->is_course = 1;
					$new_lesson->created_by_event = 1;
					$new_lesson->status = 1;
					$new_lesson->created_by = $user_id ?: \APP\Auth::getUserId();
					if ($event->category_id) {
						$new_lesson->category_id = $event->category_id;
					}
					if ($event->description) {
						$new_lesson->description = $event->description;
					}
					$new_lesson->save();

					$lesson_id = $new_lesson->id;

					// Save type as lesson after setting up a new one.
					$event->type = $data["type"];
					$event->save();
				} else if (
					$data["type"] == 'lesson' &&
					isset($data["lesson_id"]) &&
					$data["lesson_id"] > 0
				) {
					$lesson_id = $data["lesson_id"];

					// Get all resources assigned to lesson and link them with event
					$resources = \Models\LearningModule
						::where('status', true)
						->whereIn('id',
							\Models\LearningCourseModule
								::select('learning_module_id')
								->where('learning_course_id', $lesson_id)
								->get()
						)
						->get()
					;
					foreach ($resources as $key => $resource) {
						$link = \Models\ScheduleLink::firstOrNew(
							[
								"schedule_id" => $event->id,
								"type" => 'resources',
								"link_id" => $resource->id,
							]
						);
						$link->status = true;
						$link->cron_task = true;
						$link->save();
					}
				}
				if ($lesson_id) {
					$link = \Models\ScheduleLink::firstOrNew(
						[
							"schedule_id" => $event->id,
							"type" => 'lesson',
							"link_id" => $lesson_id,
						]
					);
					$link->status = true;
					$link->cron_task = true;
					$link->save();

					/** Here we fetch form Ids from Lesson */
					// $learning_module_id[]=$lesson_id;
					// $formIds=\Models\FormWorkflow::getFormFromLearningModule($learning_module_id);
					// $forms=array_merge($forms,$formIds);

				}
			}
		}
		$relationArray=\Models\Schedule::getEventTypeRelation($event->id);

		if($relationArray){
			// $forms=array_unique($forms);
			foreach ($relationArray as $form) {
				$form_data=['type'=>'forms',
				'schedule_type'=>'forms',
				'link_id'=>$form['form_id'],
				'schedule_id'=>$event->id,
				'schedule_linked_type'=>'form_workflow',
				"schedule_linked_type_id"=>$form['workflow_id']];

				\Models\ScheduleLink::addNewLink($form_data);
				$form_data=array_merge($form_data,$form);
				\Models\Schedule::assignFormsToUser($form_data);
			}
		}

		if ($return_full) {
			return $event;
		} else {
			return $event->id;
		}

	}

	public static function countAndConditions($query, &$params) {

		$schedule_id = false;

		if (isset($params["search"]["schedule_id"])) {
			$schedule_id = $params["search"]["schedule_id"];
			unset($params["search"]["schedule_id"]);
		}

		if (
			isset($params["link"]) &&
			$params["link"] == '/schedule/' &&
			isset($params["link_id"])
		) {
			$schedule_id = $params["link_id"];
		}

		$relationship = 'Schedules';
		if (isset($params["search"]["relationship"])) {
			$relationship = $params["search"]["relationship"];
			unset($params["search"]["relationship"]);
		}


		if ($schedule_id) {
			$query = $query
				->withCount([$relationship => function($query) use ($schedule_id) {
					$query
						->where('schedule_links.schedule_id', $schedule_id)
						->where('schedule_links.status', true)
					;
				}])
			;

			if (isset($params["search"]["added"])) {
				$added = $params["search"]["added"];
				unset($params["search"]["added"]);
				if ($added == 1) {
					$query = $query
						->whereHas($relationship, function ($query) use ($added, $schedule_id) {
							$query
								->where('schedule_links.schedule_id', $schedule_id)
								->where('schedule_links.status', true)
							;
						})
					;
				} else {
					$query = $query
						->whereDoesntHave($relationship, function ($query) use ($added, $schedule_id) {
							$query
								->where('schedule_links.schedule_id', $schedule_id)
							;
						})
					;
				}
			}
		}
		return $query;
	}

	public static function setForCron($id) {
		$entry = \Models\Schedule::find($id);
		if ($entry) {
			$entry->cron_task = true;
			$entry->save();
		}
	}


	// List all changed schedule events and update relevant assignment tables.
	public static function processEvents($settings = null, $event_ids = false, $user_ids = false, $update_lesson_completion_state = false) {
		if ($event_ids) {
			if (!is_array($event_ids)) {
				$event_ids = [$event_ids];
			}
		}


		if ($user_ids) {
			if (!is_array($user_ids)) {
				$user_ids = [$user_ids];
			}
		}

		$schedules = \Models\Schedule
			::where('id', '>', 0)
		;

		// Process only events that are given!
		if ($event_ids) {
			$schedules = $schedules
				->whereIn('id', $event_ids)
			;
		}

		$schedules = $schedules
			->get()
		;

		// Set up variables used in reminder
		$now = \Carbon\Carbon::now();

        foreach ($schedules as $key => $schedule) {

			$lead_minutes = \APP\Tools::getConfig('scheduleReminderLead',false,$schedule->created_by);
			$scheduleIsInThePast = \Carbon\Carbon::now()->greaterThan(\Carbon\Carbon::parse($schedule->start_date));
			// Process deleted events
			if (
				!$schedule->status &&
				$schedule->parent_id == NULL &&
				(
					$schedule->cron_task == true ||
					// Or if event id and user ids are provided, delete users off this event
					(
						$event_ids &&
						$user_ids
					)
				)
			) {
				$users = \Models\User
					::whereIn('users.id',
						\Models\ScheduleLink
							::select('link_id')
							->where('schedule_id', $schedule->id)
							->where('type', 'users')
							->get()
					)
				;

				if ($user_ids) {
					$users = $users
						->whereIn('id', $user_ids)
					;
				}

				$users = $users
					->get()
				;

				$resources = \Models\LearningModule
					::whereIn('learning_modules.id',
						\Models\ScheduleLink
							::select('link_id')
							->where('schedule_id', $schedule->id)
							->where('type', 'resources')
							->get()
					)
					->pluck('learning_modules.id')
					->toArray()
				;

				$lessons = \Models\LearningModule
					::whereIn('learning_modules.id',
						\Models\ScheduleLink
							::select('link_id')
							->where('schedule_id', $schedule->id)
							->where('type', 'lesson')
							->get()
					)
					->pluck('learning_modules.id')
					->toArray()
				;

				$issues = \Models\ApprenticeshipIssues
					::whereIn('apprenticeship_issues.id',
						\Models\ScheduleLink
							::select('link_id')
							->where('schedule_id', $schedule->id)
							->where('type', 'issues')
							->get()
					)
					->pluck('apprenticeship_issues.id')
					->toArray()
				;

				// Linked event, excluding current event
				$linked_event_ids = \Models\Schedule::getChild($schedule->id);
				$linked_event_ids = array_diff($linked_event_ids, [$schedule->id]); // remove current event ID from list
				$linked_event_ids = array_values($linked_event_ids); // reset index

				// Process assigned users
				$merged_resources = array_merge($resources, $lessons);
				$assigned_user_ids = [];
				foreach ($users as $key => $user) {
					$assigned_user_ids[] = $user->id;

					// Detach resources/lessons from user
					\Models\UserLearningModule::unlinkResources($user->id, $merged_resources, 'schedule - process events, remove resources from user, from deleted event');

					// Remove issue<->resource map from user in "apprenticeship_issues_user_learning_modules"
					\Models\ApprenticeshipIssuesUserLearningModules
						::whereIn('apprenticeship_issues_id', $issues)
						->whereIn('learning_modules_id', $merged_resources)
						->where('user_id', $user->id)
						->delete()
					;
				}

				// If $user_ids is provided delete links only related to that user and do not delete event
				if ($user_ids) {
					//Delete specific links!
					\Models\ScheduleLink
						::where('schedule_id', $schedule->id)
						->where('type', 'users')
						->whereIn('link_id', $user_ids)
						->update(['deleted_by' => $schedule->deleted_by])
					;
					\Models\ScheduleLink
						::where('schedule_id', $schedule->id)
						->where('type', 'users')
						->whereIn('link_id', $user_ids)
						->delete()
					;
				} else {
					//Delete all links!
					\Models\ScheduleLink
						::where('schedule_id', $schedule->id)
						->update(['deleted_by' => $schedule->deleted_by])
					;
					\Models\ScheduleLink
						::where('schedule_id', $schedule->id)
						->delete()
					;

					// Delete children schedule_links
					\Models\ScheduleLink
						::whereIn('schedule_id',
							\Models\Schedule
								::select('id')
								->where('parent_id', $schedule->id)
								->get()
						)
						->update(['deleted_by' => $schedule->deleted_by])
					;
					\Models\ScheduleLink
						::whereIn('schedule_id',
							\Models\Schedule
								::select('id')
								->where('parent_id', $schedule->id)
								->get()
						)
						->delete()
					;


					// Delete children
					\Models\Schedule
						::where('parent_id', $schedule->id)
						->update(['deleted_by' => $schedule->deleted_by, 'deleted_reason' => 'delete children in Schedule.php when processing deleted event'])
					;
					\Models\Schedule
						::where('parent_id', $schedule->id)
						->delete()
					;

					// Delete permissions
					$schedule->permissions()->delete();

					// Delete Forums attached to Schedule!
					\Models\Post
						::whereIn('topic_id',
							\Models\Topic
								::select('id')
								->whereIn('forum_id',
									\Models\Forum
										::select('id')
										->where('schedule_id', $schedule->id)
										->get()
								)
								->get()
						)
						->delete()
					;
					\Models\Topic
						::whereIn('forum_id',
							\Models\Forum
								::select('id')
								->where('schedule_id', $schedule->id)
								->get()
						)
						->delete()
					;
					\Models\Forum::where('schedule_id', $schedule->id)->delete();

					//User Forms
					$user_form_list=\Models\UserFormTemplateWorkflowRelations::where("type","schedule")
					->withTrashed()->where("type_id", $schedule->id)->get();
						  if($user_form_list){
						foreach($user_form_list AS $user_form_list_val){
							\Models\UserForm::deleteUserForms($user_form_list_val->user_form_id);
						}
					}



					// Send out emails to all attached users that this event is canelled.
					$template = \Models\EmailTemplate::getTemplate('event_cancellation');
					if (
						$template &&
						count($assigned_user_ids) > 0
						&& !$scheduleIsInThePast
					) {
						$event_date = '';
						if ($schedule->start_date) {
							$date_format = (\APP\Tools::getConfig('defaultDateFormat') ?? 'd/m/Y') . ' H:i';
							$event_date = \Carbon\Carbon::parse($schedule->start_date)->format($date_format);
						}

						$email_queue = new \Models\EmailQueue;
						$email_queue->email_template_id = $template->id;
						$email_queue->recipients = $assigned_user_ids;
						$email_queue->from = $schedule->deleted_by;
						$email_queue->custom_variables = json_encode([
							'EVENT_NAME' => $schedule->name,
							'EVENT_ID' => $schedule->id,
							'EVENT_DATE' => $event_date,
						]);
						$email_queue->custom_variables = \APP\Tools::eventVenueToCustomVariables($schedule,$email_queue->custom_variables);
						$email_queue->save();
					}
					// Delete schedule
					$schedule->delete();
					if (
						$template &&
						$template->slug
					) {
						\Models\EmailQueue::deleteUnsentEventNotifications($schedule, $assigned_user_ids, $template->slug);
					}

					// If there are at least one linked event, check if its lesson depends of completed linked events and, if all linked events are completed, complete lesson
					if (
						count($linked_event_ids) > 0 &&
						!empty($linked_event_ids[0])
					) {
						\Models\Schedule::completionCheck($linked_event_ids[0]);
					}


				}
			}

			// Action alive events
			if (
				$schedule->status &&
				$schedule->parent_id == NULL &&
				(
					$schedule->cron_task == true ||
					// Or if event id and user ids are provided, assign users to this event
					(
						$event_ids &&
						$user_ids
					)
				)
			) {
				/*
					UPDATE ALL USERS/RESOURCES/ISSUES relationships using active schedule links!
				*/
				$users = \Models\User
					::whereIn('users.id',
						\Models\ScheduleLink
							::select('link_id')
							->where('schedule_id', $schedule->id)
							->where('type', 'users')
							->get()
					)
					->with(['ScheduleLink' => function($query) use($schedule) {
						$query
							->where('schedule_links.schedule_id', $schedule->id)
						;
					}])
				;

				if ($user_ids) {
					$users = $users
						->whereIn('id', $user_ids)
					;
				}

				$users = $users
					->get()
				;

				$resources = \Models\LearningModule
					::whereIn('learning_modules.id',
						\Models\ScheduleLink
							::select('link_id')
							->where('schedule_id', $schedule->id)
							->where('type', 'resources')
							->where('status', true)
							->get()
					)
					->pluck('learning_modules.id')
					->toArray()
				;

				$lessons = \Models\LearningModule
					::whereIn('learning_modules.id',
						\Models\ScheduleLink
							::select('link_id')
							->where('schedule_id', $schedule->id)
							->where('type', 'lesson')
							->where('status', true)
							->get()
					)
					->pluck('learning_modules.id')
					->toArray()
				;

				$issues = \Models\ApprenticeshipIssues
					::whereIn('apprenticeship_issues.id',
						\Models\ScheduleLink
							::select('link_id')
							->where('schedule_id', $schedule->id)
							->where('type', 'issues')
							->where('status', true)
							->get()
					)
					->pluck('apprenticeship_issues.id')
					->toArray()
				;


				/*
					Take all DELETED schedule links and unasign users from resources, etc.
				*/
				$deleted_users = \Models\User
					::whereIn('users.id',
						\Models\ScheduleLink
							::select('link_id')
							->where('schedule_id', $schedule->id)
							->where('type', 'users')
							->where('status', false)
							->get()
					)
				;
				if ($user_ids) {
					$deleted_users = $deleted_users
						->whereIn('users.id', $user_ids)
					;
				}
				$deleted_users = $deleted_users
					->get()
				;

				$deleted_resources = \Models\LearningModule
					::whereIn('learning_modules.id',
						\Models\ScheduleLink
							::select('link_id')
							->where('schedule_id', $schedule->id)
							->where('type', 'resources')
							->where('status', false)
							->get()
					)
					->pluck('learning_modules.id')
					->toArray()
				;

				$deleted_lessons = \Models\LearningModule
					::whereIn('learning_modules.id',
						\Models\ScheduleLink
							::select('link_id')
							->where('schedule_id', $schedule->id)
							->where('type', 'lesson')
							->where('status', false)
							->get()
					)
					->pluck('learning_modules.id')
					->toArray()
				;

				$deleted_issues = \Models\ApprenticeshipIssues
					::whereIn('apprenticeship_issues.id',
						\Models\ScheduleLink
							::select('link_id')
							->where('schedule_id', $schedule->id)
							->where('type', 'issues')
							->where('status', false)
							->get()
					)
					->pluck('apprenticeship_issues.id')
					->toArray()
				;

				$merged_deleted_resources = array_merge($deleted_resources, $deleted_lessons);
				$merged_resources = array_merge($resources, $lessons);

				$send_email_to_users_ids = [];

				// Process ALL users
				foreach ($users as $key => $user) {

					/* what is happening here?
						// Detach resources/lessons from user
						$user->modules()->detach($merged_resources);
						// Attach them back!
						$user->modules()->attach($merged_resources);
					*/
					\Models\UserLearningModule::linkResources($user->id, $merged_resources, 'schedule - process events, link resources to user');
					\Models\UserLearningModule::unlinkResources($user->id, $merged_deleted_resources, 'schedule - process events, remove resources from user, from active event');

					\APP\Learning::syncUserResults($user->id);

					// Link issues with modules/users
					foreach ($issues as $key => $issue) {
						foreach ($merged_resources as $key => $merged_resource) {

							\Models\ApprenticeshipIssuesUserLearningModules::linkEntry(
								[$issue],
								[$merged_resource],
								$user->id
							);

						}
					}

					// Unlink user - standard issues - event lesson/resources
					\Models\ApprenticeshipIssuesUserLearningModules
						::whereIn('apprenticeship_issues_id', $deleted_issues)
						->whereIn('learning_modules_id', $merged_resources)
						->where('user_id', $user->id)
						->delete()
					;

					// If user link is marked for cron task and status is 1, then he is added and create new email for him to be sent out!
					if (
						$user->ScheduleLink &&
						$user->ScheduleLink->status == 1 &&
						$user->ScheduleLink->cron_task == 1 &&
						$schedule->visible_learner &&
						!$user->ScheduleLink->ignore_email
					) {
						$send_email_to_users_ids[] = $user->id;
					}

					// If Attendeed has attended event, check if current linked lesson is completed, if not, set it to completed!
					if ($update_lesson_completion_state) {
						print_r(json_encode($user->ScheduleLink));
						if (
							$user->ScheduleLink &&
							$user->ScheduleLink->completion_status == \APP\Templates::translate('%%event_completion_state_completed%%')
						) {
							foreach ($lessons as $key => $lesson) {
								$learning_result = \Models\LearningResult
									::where('user_id', $user->id)
									->where('learning_module_id', $lesson)
									->where('refreshed', 0)
									->first()
								;
								if ($learning_result) {
									$learning_result->completion_status = 'completed';
									if ($user->ScheduleLink->completed_at) {
										$learning_result->completed_at = $user->ScheduleLink->completed_at;
									} else {
										$learning_result->completed_at = $schedule->end_date;
									}
									$learning_result->saveWithoutEvents();
								}
							}
						}
					}
				}

				// Get all schedule links to resources, get field completion_date_custom, update all learning results with that!
				$completion_date_links = \Models\ScheduleLink
					::where('status', true)
					->where('cron_task', true)
					->whereIn('type', ['resources', 'lesson'])
					//->whereNotNull('completion_date_custom')
					->where('schedule_id', $schedule->id)
					->get()
				;

				$duration = !empty($schedule->duration) ? $schedule->duration : 0;
				foreach ($completion_date_links as $key => $completion_date_link) {
					// update all learning results 'completion_date_custom' field with users in this schedule
					$learning_results = \Models\LearningResult
						::where('learning_module_id', $completion_date_link->link_id)
						->where('refreshed', 0)
						->where('completion_status', '!=', 'completed')
						->whereIn('user_id',
							\Models\ScheduleLink
								::select('link_id')
								->where('schedule_id', $schedule->id)
								->where('type', 'users')
								->where('status', true)
								->get()
						)
					;

					if ($user_ids) {
						$learning_results = $learning_results
							->whereIn('user_id', $user_ids)
						;
					}

					$learning_results = $learning_results
						->get()
					;
					foreach ($learning_results as $key => $learning_result) {
						$learning_result->completion_date_custom = $completion_date_link->completion_date_custom;
						if (\APP\Tools::getConfig('changeResourceDatewhenEventDateChange')) {
							$learning_result->completion_date_custom = \Carbon\Carbon::parse($schedule->end_date);
						}
						$learning_result->due_at = \Carbon\Carbon::parse($schedule->end_date);
                        if (
                            \APP\Tools::getConfig('updateLearningResultGraceAtForSchedule') &&
                            $learning_result->grace_at
                        ) {
                            $learning_result->grace_at = \Carbon\Carbon::parse($schedule->end_date);
                        }
						$learning_result->save();
					}
					//update resource link to make sure its completion status is synced with the corresponding user completion status
					$completion_date_link->save();
				}


				// Set up email queueue
				if (count($send_email_to_users_ids) > 0) {
					switch ($schedule->type) {
						case 'lesson':
							$template_slug = 'schedule_created';
						break;
						case 'meeting':
							$template_slug = 'schedule_meeting_created';
						break;

						default:
							$template_slug = 'schedule_created';
						break;
					}

					$template = \Models\EmailTemplate::getTemplate($template_slug);
					if ($template) {

						// Retrieve manager
                        $managers = $schedule->Managers();
                        if ($managers) {
                            $managerNames = $managers->select('fname', 'lname')
                                ->get()
                                ->map(function ($user) {
                                    return $user->fname . ' ' . $user->lname;
                                })->implode(', ');
                        } else {
                            $managerNames = '';
                        }

						$attachments = $schedule->Files()->get();
						$ATTACHMENT_LIST = false;
						if ($attachments->count() > 0){
							$ATTACHMENT_LIST = "Attached files:<br>";
							foreach ($attachments as $attachment) {
								$attachmentUrl = $GLOBALS["CONFIG"]->RootPath.'file/'.$attachment->hash.'/'.$attachment->file;
								$ATTACHMENT_LIST.= "<a href='$attachmentUrl'>$attachment->file</a></br>";
							}
						}
                        if ($managerNames && !$scheduleIsInThePast) {

							$start_date_uk = \Carbon\Carbon::parse($schedule->start_date);
							$email_queue = new \Models\EmailQueue;
							$email_queue->email_template_id = $template->id;
							$email_queue->recipients = $send_email_to_users_ids;
							$email_queue->from = NULL; // for using defaultEmailFromName
							$email_queue->custom_variables = json_encode([
								'EVENT_NAME' => $schedule->name,
								'EVENT_ID' => $schedule->id,
								'EVENT_LOCATION' => $schedule->location,
								'EVENT_DATE' => $schedule->start_date ? \Carbon\Carbon::parse($schedule->start_date)->format(\APP\Tools::getConfig('defaultDateFormat')) : "--",
								'EVENT_DESCRIPTION' => $schedule->description,
								'LESSON_ID' => ($lessons && $lessons[0] ? $lessons[0] : ''),
								'EVENT_TIME' => $start_date_uk->format(\APP\Tools::getConfig('defaultDateFormat',false,$schedule->created_by) . " H:i"),
                                'MANAGER_NAMES' => $managerNames,
								// 'MANAGER_FNAME' => $manager->fname,
								// 'MANAGER_LNAME' => $manager->lname,
								'ATTACHMENT_LIST' => $ATTACHMENT_LIST ?? '',
                                'BOOKING_DEADLINE_DATE' => !empty($schedule->deadline_at) ? Carbon::parse($schedule->deadline_at)->format(\APP\Tools::getConfig('defaultDateFormat',false,$schedule->created_by) . " H:i") : '-',
                                'CANCELLATION_DEADLINE_DATE' => !empty($schedule->drop_off_deadline_at) ? Carbon::parse($schedule->drop_off_deadline_at)->format(\APP\Tools::getConfig('defaultDateFormat',false,$schedule->created_by) . " H:i") : '-',
							]);
							$email_queue->custom_variables = \APP\Tools::eventVenueToCustomVariables($schedule,$email_queue->custom_variables);
							$email_queue->save();

							\Models\EmailQueue::deleteUnsentEventNotifications($schedule, $send_email_to_users_ids, $template->slug, $email_queue);
						}
					}
				}


				// process deleted users!
				$deleted_user_ids = [];
				foreach ($deleted_users as $key => $deleted_user) {
					$deleted_user_ids[] = $deleted_user->id;
					// Detach all resources
					$deleted_user->modules()->detach($merged_resources);
					$deleted_user->modules()->detach($merged_deleted_resources);

					// Unlink deleted issues/resources link
					\Models\ApprenticeshipIssuesUserLearningModules
						::whereIn('apprenticeship_issues_id', $deleted_issues)
						->whereIn('learning_modules_id', $merged_deleted_resources)
						->where('user_id', $deleted_user->id)
						->delete()
					;

					// Unlink remaining issues/resources link
					\Models\ApprenticeshipIssuesUserLearningModules
						::whereIn('apprenticeship_issues_id', $issues)
						->whereIn('learning_modules_id', $merged_resources)
						->where('user_id', $deleted_user->id)
						->delete()
					;
				}

				if (count($deleted_user_ids) > 0) {
					\Models\EmailQueue::deleteUnsentEventNotifications($schedule, $deleted_user_ids, 'removed_users');
				}


				// If $user_ids is provided delete links only related to that user and do not update event
				if ($user_ids) {
					//Delete specific links!
					\Models\ScheduleLink
						::where('schedule_id', $schedule->id)
						->where('type', 'users')
						->whereIn('link_id', $user_ids)
						->where('status', false)
						->update(['deleted_by' => $schedule->deleted_by])
					;
					\Models\ScheduleLink
						::where('schedule_id', $schedule->id)
						->where('type', 'users')
						->where('status', false)
						->whereIn('link_id', $user_ids)
						->delete()
					;
				} else {
					// Delete obselete links
					\Models\ScheduleLink
						::where('schedule_id', $schedule->id)
						->where('status', false)
						->delete()
					;

					// Update links as processed

					\Models\ScheduleLink
						::where('schedule_id', $schedule->id)
						->where('cron_task', true)
						->update(
							[
								'cron_task' => false
							]
						)
					;

					// Set schedule as processed
					$schedule->cron_task = false;
					$schedule->save();
				}
				if (
					$schedule->previous_start_date &&
					$schedule->start_date &&
					$schedule->previous_start_date != $schedule->start_date
				) {
					self::sendRescheduledMail($schedule);
					$schedule->previous_start_date = $schedule->start_date;
					$schedule->save();
				}

				$venue = null;
				if ($schedule->venue) {
					$venue = Venue::where('id',$schedule->venue->value)->first();
				}
				//Add event to outlook
				if (
					(
						$schedule->outlook_integration &&
						$schedule->outlook_refresh_token
					) ||
					(
						Tools::getConfig('useOutlookVenues',false,$schedule->created_by) &&
						Tools::getConfig('OutlookRoomAccessToken',false,$schedule->created_by) &&
						Tools::getConfig('OutLookRoomRefreshToken',false,$schedule->created_by)
					)
				) {
					if (\Carbon\Carbon::parse($schedule->start_date)->gte(\Carbon\Carbon::now())) {

						try {

							$teams = new \APP\Teams(false);

							if (
								\APP\Tools::getConfig("enableGlobalOutlookIntegration",false,$schedule->created_by) &&
								$schedule->outlook_refresh_token == "use_global"
							) {
								$teams->refresh_global_calendar_oauth_token(\APP\Tools::getConfig("GlobalOutlookIntegrationSecurityToken",false,$schedule->created_by));
							} else if (
								Tools::getConfig('useOutlookVenues',false,$schedule->created_by) &&
								Tools::getConfig('OutlookRoomAccessToken',false,$schedule->created_by) &&
								Tools::getConfig('OutLookRoomRefreshToken',false,$schedule->created_by)
							) {
								$teams->token = Venue::getToken();
							} else {
								$teams->refresh_calendar_oauth_token($schedule->outlook_refresh_token);
							}

							$meeting_url = false;

							if (isset($merged_resources)) {

								$meeting_type_ids = \Models\LearningModuleType
									::whereIn("slug", ["microsoft_teams", "zoom_meeting"])
									->withoutGlobalScope('type_filter')
									->pluck("id")
								;

								//get teams meeting again as they could have been modified
								if ($meeting = \Models\LearningModule::whereIn("type_id", $meeting_type_ids)->whereIn("id", $merged_resources)->first()){
									if ($meeting->material && $meeting->material->link){
										$meeting_url = $meeting->material->link;
									}
								}
							}

							$user_emails = [];

							//Get all users and managers ant not just the selected ones
							$all_users = \Models\User
								::whereIn('users.id',
									\Models\ScheduleLink
										::select('link_id')
										->where('schedule_id', $schedule->id)
										->whereIn('type', ['users', 'managers'])
										->get()
								)
								->get()
							;

							foreach($all_users as $user){
								if (!in_array($user->email, $user_emails)) {
									$user_emails[] = $user->email;
								}
							};
							if (
								$venue &&
								$venue->room_email
							) {
								$user_emails[] = $venue->room_email;
							}
							$teams->createEventFromSchedule($schedule, $user_emails, $meeting_url);
						} catch(\Exception $e) {
							\Models\Log::addEntry(false, false, 500, ['type' => 'outlook - event add/update', 'message' => "Event: $schedule->id, " . $e->getMessage()]);
							// Sometimes Access token denied is returned here with 500, should that kill all event process?
						}
					}
				}
			}


			// Loop all schedules, check if reminder needs to be sent out, only to those that are visible to learner
			if (
				$schedule->status &&
				!$schedule->reminder_sent &&
				$schedule->visible_learner &&
				!$user_ids // Do not check reminders if this eventprocessing is triggered by user.
			) {
				// check start time, even for children, send out reminders!
				$start_date = \Carbon\Carbon::parse($schedule->start_date);
				$difference =  $start_date->diffInMinutes($now);
				if (
					$start_date > $now &&
					$difference < $lead_minutes
				) {
					$schedule_name = $schedule->name;
					$schedule_id = $schedule->id;


					// If schedule is child, get parent!
					if (
						$schedule->parent_id
					) {
						// Get parent of schedule!
						$schedule_parent = \Models\Schedule::find($schedule->parent_id);
						if (!$schedule->name) {
							$schedule_name = $schedule_parent->name;
						}
						$schedule_id = $schedule_parent->id;
					}
					$reminder_users_ids = \Models\User
						::whereIn('users.id',
							\Models\ScheduleLink
								::select('link_id')
								->where('schedule_id', $schedule_id)
								->where('ignore_email', "0")
								->where('type', 'users')
								->get()
						)
					->pluck('users.id')
					->toArray()
					;

					switch ($schedule->type) {
						case 'lesson':
							$template_slug = 'schedule_reminder';
						break;
						case 'meeting':
							$template_slug = 'schedule_meeting_reminder';
						break;

						default:
							$template_slug = 'schedule_reminder';
						break;
					}

					$template = \Models\EmailTemplate::getTemplate($template_slug);
					if (
						$template &&
						count($reminder_users_ids) > 0
						&& !$scheduleIsInThePast
					) {
						$reminder_lesson = \Models\LearningModule
							::whereIn('learning_modules.id',
								\Models\ScheduleLink
									::select('link_id')
									->where('schedule_id', $schedule_id)
									->where('type', 'lesson')
									->get()
							)
							->first()
						;

                        $managers = $schedule->Managers();
                        if ($managers) {
                            $managerNames = $managers->select('fname', 'lname')
                                ->get()
                                ->map(function ($user) {
                                    return $user->fname . ' ' . $user->lname;
                                })->implode(', ');
                        } else {
                            $managerNames = '';
                        }

						$start_date_uk = \Carbon\Carbon::parse($schedule->start_date);
						$email_queue = new \Models\EmailQueue;
						$email_queue->email_template_id = $template->id;
						$email_queue->recipients = $reminder_users_ids;
						$email_queue->from = null;
						$email_queue->custom_variables = json_encode([
							'EVENT_NAME' => $schedule_name,
							'EVENT_LOCATION' => $schedule->location,
							'EVENT_DESCRIPTION' => $schedule->description,
							'LESSON_ID' => ($reminder_lesson ? $reminder_lesson->id . '-' . $schedule->id : ''),
							'EVENT_TIME' => $start_date_uk->format(\APP\Tools::getConfig('defaultDateFormat',false,$schedule->created_by) . " H:i"),
                            'MANAGER_NAMES' => $managerNames,
							// 'MANAGER_FNAME' => ($manager ? $manager->fname : ""),
							// 'MANAGER_LNAME' => ($manager ? $manager->lname : ""),
						]);
								$email_queue->custom_variables = \APP\Tools::eventVenueToCustomVariables($schedule,$email_queue->custom_variables);
						$email_queue->save();

						$schedule->reminder_sent = true;
						$schedule->save();
					}
				}
			}
		}
	}


	/*Forum Relation a schedule event will be only having one forum currently*/
	public function forum() {
		return $this->hasOne('Models\Forum','schedule_id');
	}

	// Delete event!
	public static function deleteEvent ($schedule_id = false) {
		if ($schedule_id) {
			$event = \Models\Schedule::find($schedule_id);
			// If this is child, just delete it, else mark for cron action!
			/*Delete all related References*/
			if ($event->parent_id) {
				if ($event->comments()->exists()) {
					$event->comments()->delete();
				}

				$forum = $event->forum();
				if ($forum->exists()) {
					if ($forum->posts()->exists()) {
						$forum->posts()->delete();
					}

					if ($forum->topics()->exists()) {
						$forum->topics()->delete();
					}
					$forum->delete();
				}
				$event->deleted_by = \App\Auth::getUserId();
				$event->deleted_reason = 'delete event children in public static function deleteEvent';
				$event->deleted_at_user = \Carbon\Carbon::now();
				$event->save();

				$event->delete();
			} else {
				$event->status = false;
				$event->deleted_by = \App\Auth::getUserId();
				$event->deleted_reason = 'mark event as to be deleted in public static function deleteEvent';
				$event->deleted_at_user = \Carbon\Carbon::now();
				$event->cron_task = true;
				$event->save();
			}
		}
	}

	public static function filterVisibleDays($query) {
		$EventsVisibleDays = \APP\Tools::getConfig('EventsVisibleDays');
		if (
			\APP\Auth::isLearner() &&
			$EventsVisibleDays &&
			$EventsVisibleDays > 0
		) {
			$query = $query
				->whereRaw('schedules.start_date > DATE_SUB(NOW(), INTERVAL ? DAY)', [$EventsVisibleDays])
			;

		}
		return $query;
	}

	public function Queries() {
		return $this
			->hasMany('Models\ResourceQuery', 'type_id', 'id')
			->where('type', 'events')
		;
	}


	public static function sendReminderAdvanceNotification($settings) {
		$eventReminderAdvanceNotificationInterval = \APP\Tools::getConfig('eventReminderAdvanceNotificationInterval');
		if (
			$eventReminderAdvanceNotificationInterval &&
			count($eventReminderAdvanceNotificationInterval) > 0
		) {
			$event_reminder_advance_notification = \Models\EmailTemplate::getTemplate('event_reminder_advance_notification');

			$schedules = \Models\Schedule
				::where('status', 1)
				->whereRaw('start_date > NOW()')
				->whereNull('parent_id')
				->where('visible_learner', 1)
				->where('do_not_send_advance_notifications', 0)
				->get()
			;

			foreach ($schedules as $key => $schedule) {
				// Check start date against eventReminderAdvanceNotificationInterval, send out event_reminder_advance_notification reminder if on the day.
				if ($event_reminder_advance_notification) {
					$start_date = \Carbon\Carbon::parse($schedule->start_date);
					if ($start_date > \Carbon\Carbon::now()) {
						foreach ($eventReminderAdvanceNotificationInterval as $key => $advance_day) {
							if (empty($advance_day)) {
								continue;
							}
							//check if today is the day, start_date - advance_day == today, then send out reminder
							$advance_date = $start_date->copy()->subDays($advance_day);
							if ($advance_date->isToday()) {

								// find out all users that this reminder needs to be sent to.
								$recipients_ids = \Models\User
									::whereHas('ScheduleLink',function($query) use ($schedule) {
										$query
											->where('schedule_links.schedule_id', $schedule->id)
											->where('schedule_links.status', 1)
											->where('schedule_links.type', 'users')
											->where('schedule_links.ignore_email', 0)
											->where('schedule_links.approved', 1)
											->whereNull('schedule_links.deleted_at')
										;
									})
									->where('status', 1)
									->pluck('users.id')
									->toArray()
								;

								// Only if more than 1 user is assigned, proceed
								if (count($recipients_ids) > 0) {
									// Find first manager assigned to event
									$first_manager = \Models\User
										::whereHas('ManagerSchedules',function($query) use ($schedule) {
											$query
												->where('schedule_links.schedule_id', $schedule->id)
												->where('schedule_links.status', 1)
												->where('schedule_links.type', 'managers')
												->whereNull('schedule_links.deleted_at')
											;
										})
										->where('status', 1)
										->first()
									;

									// Create some default variables for template
									$WITH_MANAGER_FULL_NAME = '';
									$EVENT_URL = '';
									$MANAGER_REGARDS = '';
									$first_lesson = false;

									// If not lesson and manager is found, add manager full name and regards
									if (
										$schedule->type != 'lesson' &&
										$first_manager
									) {
										$WITH_MANAGER_FULL_NAME = 'with ' . $first_manager->fname . '' . $first_manager->lname;
										$MANAGER_REGARDS = '<p>Regards<br>' . $first_manager->fname . '' . $first_manager->lname . '</p>';

									// If lesson, retrieve lesson and generate URL
									} else if ($schedule->type == 'lesson') {
										$first_lesson = \Models\LearningModule
											::whereIn('learning_modules.id',
												\Models\ScheduleLink
													::select('link_id')
													->where('schedule_id', $schedule->id)
													->where('type', 'lesson')
													->get()
											)
											->where('status', 1)
											->first()
										;
										if ($first_lesson) {
											$event_link = $GLOBALS["CONFIG"]->LMSUrl . 'app/learner/resources/' . $first_lesson->id . '-' . $schedule->id;
											if (\APP\Tools::getConfig('redirectAllLinksThroughSSO',false,$schedule->created_by)) {
												$event_link = $GLOBALS["CONFIG"]->LMSUrl . 'saml/?ReturnTo=' . urlencode($event_link);
											}
											$EVENT_URL = '<p><a href="' . $event_link . '">' . $schedule->name . '</a></p>';
										}
									}
									 // need to get the zoom meeting and the team meeting link if there and then add that to custom variables.

									$scheduleLinks = ScheduleLink::join('learning_modules as lm', 'schedule_links.link_id', '=', 'lm.id')
																->join('learning_module_types as lmt', 'lm.type_id', '=', 'lmt.id')
																->where('schedule_links.schedule_id', $schedule->id)
																->where('schedule_links.type', 'resources')
																->whereIn('lmt.slug', ['microsoft_teams', 'zoom_meeting'])
																->select('schedule_links.id', 'lm.id as lm_id', 'lmt.slug', 'lm.material')
																->first();

									$meetingLink = "";
									if (!empty($scheduleLinks) && !empty($scheduleLinks->material)) {
										$material = json_decode($scheduleLinks->material, true);
										$meetingLink = $material['link'] ? '<a href="' . $material['link'] . '">' . $material['link'] . '</a>' : "";
									} else {
										$meetingLink = "";
									}

									$email_queue = new \Models\EmailQueue;
									$email_queue->email_template_id = $event_reminder_advance_notification->id;
									$email_queue->recipients = $recipients_ids;
									$email_queue->from = $first_manager ? $first_manager->id : null;
									$email_queue->custom_variables = json_encode([
										'EVENT_NAME' => $schedule->name,
										'EVENT_ID' => $schedule->id,
										'EVENT_LOCATION' => ($schedule->location ? ', at ' . $schedule->location : ''),
										'EVENT_DESCRIPTION' => $schedule->description,
										'LESSON_ID' => ($first_lesson ? $first_lesson->id : ''),
										'EVENT_TIME' => $start_date->copy()->format(\APP\Tools::getConfig('defaultDateFormat',false,$schedule->created_by) . " H:i"),
										'WITH_MANAGER_FULL_NAME' => $WITH_MANAGER_FULL_NAME,
										'MANAGER_FNAME' => ($first_manager ? $first_manager->fname : ''),
										'MANAGER_LNAME' => ($first_manager ? $first_manager->lname : ''),
										'EVENT_ID' => $schedule->id,
										'EVENT_URL' => $EVENT_URL,
										'MEETING_LINK' => $meetingLink,
										'MANAGER_REGARDS' => $MANAGER_REGARDS,
									]);
									$email_queue->custom_variables = \APP\Tools::eventVenueToCustomVariables($schedule,$email_queue->custom_variables);
									$email_queue->save();
								}
							}
						}
					}
				}
			}
		}
	}

	// Check if event needs to be completed against all linked event completion states
	public static function completionCheck($event_id) {
		$event = \Models\Schedule
			::find($event_id)
			->with(['Users' => function ($query) {
				$query
					->where('users.status', true)
				;
			}])
			->whereHas("Users", function($query) {
				$query = $query
					->where('users.status', 1)
				;
			})
			->with('Lesson')
			->whereHas("Lesson", function($query) {
				$query = $query
					->where('learning_modules.status', 1)
					->where('learning_modules.open_in_events_only', 1)
					->where('learning_modules.complete_if_linked_events_completed', 1)
				;
			})
			->first()
		;

		if ($event) {
			foreach ($event->Users as $key => $user) {
				$lesson_result = \Models\LearningResult
					::where('user_id', $user->id)
					->where('learning_module_id', $event->Lesson->id)
					->where('refreshed', 0)
					->where('completion_status', '!=', 'completed')
					->first()
				;
				// Lesson is not completed, lets check if all linked events are completed.
				if ($lesson_result) {
					$complete_lesson = \Models\ScheduleLink::checkLinkedEventsCompletion($lesson_result->learning_module_id, $event->id, $lesson_result->user_id);
					if ($complete_lesson) {
						$lesson_result->completion_status = 'completed';
						$lesson_result->completed_at = \Carbon\Carbon::now();
						// checkLinkedEventsCompletion will return date of latest completed event as response, if all lessons are completed
						if ($complete_lesson !== true) {
							$lesson_result->completed_at = $complete_lesson;
						}
						$lesson_result->saveWithoutEvents();
					}
				}
			}
		}
	}

	protected static function boot() {
		parent::boot();
		static::saving(function($schedule) {
			// Allways set this to true for lesson types!
			if ($schedule->type == 'lesson') {
				$schedule->visible_learner = true;
			}
		});

		static::updating(function($entry) {
			$old_entry = \Models\Schedule::find($entry->id);
			$changes = $entry->getDirty();
			$date_fields = [
				'start_date',
				'end_date',
				'outlook_start_date',
				'drop_off_deadline_at',
				'deadline_at',
			];
			$ignore_fields = [
				'outlook_refresh_token',
				'cron_task',
			];
			\Models\TableHistory::trackChanges($old_entry, $changes, $date_fields, 'schedules', $entry->id, $ignore_fields, false, true);
        });

        // after saving event, check enrole_any_learner
        static::saved(function($schedule) {
            if ($schedule->enrole_any_learner) {
                UserScheduleWaitingList::sendEmail($schedule->id);
            }
        });
	}

    public static function updateRescheduledEvent($schedule)
    {
        if ($schedule->outlook_integration && $schedule->outlook_refresh_token) {

            if (\Carbon\Carbon::parse($schedule->start_date)->gte(\Carbon\Carbon::now())) {

                try {
                    $teams = new \APP\Teams(false);
                    if (\APP\Tools::getConfig("enableGlobalOutlookIntegration",false,$schedule->created_by) && ($schedule->outlook_refresh_token == "use_global")) {
                        $teams->refresh_global_calendar_oauth_token(\APP\Tools::getConfig("GlobalOutlookIntegrationSecurityToken",false,$schedule->created_by));
                    } else {
                        $teams->refresh_calendar_oauth_token($schedule->outlook_refresh_token);
                    }

                    $meeting_url = false;

                    if (isset($merged_resources)) {

                        $meeting_type_ids = \Models\LearningModuleType
                            ::whereIn("slug", ["microsoft_teams", "zoom_meeting"])
                            ->withoutGlobalScope('type_filter')
                            ->pluck("id");

                        //get teams meeting again as they could have been modified
                        if ($meeting = \Models\LearningModule::whereIn("type_id", $meeting_type_ids)->whereIn("id", $merged_resources)->first()) {
                            if ($meeting->material && $meeting->material->link) {
                                $meeting_url = $meeting->material->link;
                            }
                        }
                    }

                    $user_emails = [];

                    //Get all users and managers ant not just the selected ones
                    $all_users = \Models\User
                        ::whereIn('users.id',
                            \Models\ScheduleLink
                                ::select('link_id')
                                ->where('schedule_id', $schedule->id)
                                ->whereIn('type', ['users', 'managers'])
                                ->get()
                        )
                        ->get();

                    foreach ($all_users as $user) {
                        if (!in_array($user->email, $user_emails)) {
                            $user_emails[] = $user->email;
                        }
                    };

                    $teams->createEventFromSchedule($schedule, $user_emails, $meeting_url);
                } catch (\Exception $e) {
                    // Sometimes Access token denied is returned here with 500, should that kill all event process?
                }
            }
        }
    }

    public static function sendRescheduledMail($schedule)
    {
        // Only send rescheduled email if start_date is in the future
        if ($schedule->start_date && \Carbon\Carbon::parse($schedule->start_date)->isPast()) {
            return;
        }

        $users = \Models\User::whereIn('users.id', \Models\ScheduleLink::select('link_id')->where('schedule_id', $schedule->id)->where('type', 'users')->get());
        $send_email_to_users_ids = $users->pluck('id')->toArray();

        if (count($send_email_to_users_ids) > 0) {
            switch ($schedule->type) {
                case 'lesson':
                    $template_slug = 'schedule_created_rescheduled';
                    break;
                case 'meeting':
                    $template_slug = 'schedule_meeting_created_rescheduled';
                    break;

                default:
                    $template_slug = 'schedule_created_rescheduled';
                    break;
            }

            $template = \Models\EmailTemplate::getTemplate($template_slug);
            if ($template) {
                // Retrieve manager
                $manager = \Models\User::whereIn('users.id',
                    \Models\SchedulePermission::select('user_id')
                        ->where('schedule_permissions.schedule_id', $schedule->id)
                        ->where('schedule_permissions.type', 'owner')
                        ->get()
                )->first();
                if (!$manager) {
                    $manager = \APP\Auth::getUser();
                }
                $attachments = $schedule->Files()->get();
                $ATTACHMENT_LIST = false;
                if ($attachments->count() > 0) {
                    $ATTACHMENT_LIST = "Attached files:<br>";
                    foreach ($attachments as $attachment) {
                        $attachmentUrl = $GLOBALS["CONFIG"]->RootPath . 'file/' . $attachment->hash . '/' . $attachment->file;
                        $ATTACHMENT_LIST .= "<a href='$attachmentUrl'>$attachment->file</a></br>";
                    }
                }

                $lessons = \Models\LearningModule
                    ::whereIn('learning_modules.id',
                        \Models\ScheduleLink
                            ::select('link_id')
                            ->where('schedule_id', $schedule->id)
                            ->where('type', 'lesson')
                            ->get()
                    )
                    ->pluck('learning_modules.id')
                    ->toArray();


                if ($manager) {
                    $start_date_uk = \Carbon\Carbon::parse($schedule->start_date);
                    $email_queue = new \Models\EmailQueue;
                    $email_queue->email_template_id = $template->id;
                    $email_queue->recipients = $send_email_to_users_ids;
                    $email_queue->from = $manager->id;
                    $email_queue->custom_variables = json_encode([
                        'EVENT_NAME' => $schedule->name . " Rescheduled",
                        'EVENT_ID' => $schedule->id,
                        'EVENT_LOCATION' => $schedule->location,
                        'EVENT_DESCRIPTION' => $schedule->description,
                        'LESSON_ID' => ($lessons && $lessons[0] ? $lessons[0] : ''),
                        'EVENT_TIME' => $start_date_uk->format(\APP\Tools::getConfig('defaultDateFormat',false,$schedule->created_by) . " H:i",'',$schedule->created_by),
                        'MANAGER_FNAME' => $manager->fname,
                        'MANAGER_LNAME' => $manager->lname,
                        'ATTACHMENT_LIST' => $ATTACHMENT_LIST ?? ''
                    ]);
                    $email_queue->custom_variables = \APP\Tools::eventVenueToCustomVariables($schedule, $email_queue->custom_variables);
                    $email_queue->save();

                    \Models\EmailQueue::deleteUnsentEventNotifications($schedule, $send_email_to_users_ids, $template->slug, $email_queue);
                }
            }
        }
    }

public static function userStatusUpdate($user_id,$deleted_by_user_id,$status)
  {
	\Models\ScheduleLink::where('type','users')
		  ->where('link_id',$user_id)
		  ->whereHas('Schedule',function($query){
		  $query->where('end_date','>',\Carbon\Carbon::today()->toDateString());
		  $query->where(function($query){
				$query->where('cost',0);
				$query->orWhereNull('cost');
		  });
		})
		->update(
						[
							'status' => $status,
							'deleted_by' => $deleted_by_user_id,
							'cron_task' => true
						]
			 );
	 $schedule_links= \Models\ScheduleLink::where('type','users')
		  ->where('link_id',$user_id)
		  ->whereHas('Schedule',function($query){
		  $query->where('end_date','>',\Carbon\Carbon::today()->toDateString());
		  $query->where(function($query){
				$query->where('cost',0);
				$query->orWhereNull('cost');
		  });
		})
		->get();
	 foreach ($schedule_links as $schedule_link) {
			\Models\ScheduleLink::resetUsers($schedule_link->schedule_id);
				\Models\Schedule::setForCron($schedule_link->schedule_id);
	 }
  }

	public static function assignUsersToForm($data){
		  $relations_ids=[];
		$relationArray=\Models\Schedule::getEventTypeRelation($data['schedule_id']);
		foreach($relationArray AS $form){

			$schedule_data=["form_id"=>$form['form_id'],
							"refreshed_date"=>$data['event_date'],
							"user_id"=>isset($data['created_for'])?$data['created_for']:$data['link_id'],
							"schedule_id"=>$data["schedule_id"],
							//it is for insert data type schedule_linked_events
							"schedule_linked_type"=>isset($data['schedule_linked_type'])?$data['schedule_linked_type']:'',
							'type'=>'schedule'
						];
			$schedule_data=array_merge($schedule_data,$form);
			$schedule_data['type']='schedule';

				$user_form=\Models\UserForm::addUsersForm($schedule_data);

				/**
					  * Add log for userform to workflow relation
					  * tag - workflow_lesson_assign
					  */
					 $schedule_data['user_form_id']=$user_form['id'];


					 unset($schedule_data['form_id']);
					 unset($schedule_data['user_id']);
				unset($schedule_data['schedule_id']);
				unset($schedule_data['refreshed_date']);
				unset($schedule_data['schedule_linked_type_id']);
					 unset($schedule_data['schedule_linked_type']);

					 $relationModel=\Models\UserFormTemplateWorkflowRelations::addUserFormTemplateWorkFlow($schedule_data);
				if(isset($form['workflow_id']) && $form['workflow_id']!=NULL){
					$relations_ids[]=['workflow_id'=>$form['workflow_id'],
					'id'=>$relationModel->id,"schedule_id"=>$data["schedule_id"]];
				}
		}

		/**Workflow Assigned Forms */
		foreach($relations_ids AS $relations_id){
			$user_workflow_form=\Models\UserWorkflowForm::firstOrCreate(["user_id"=>isset($data['created_for'])?$data['created_for']:$data['link_id'],
				"type"=>'schedule',
				"type_id"=>$relations_id['schedule_id'],
				"form_workflow_id"=>$relations_id['workflow_id'],

				]);
			\Models\UserFormTemplateWorkflowRelations::where("id",$relations_id['id'])
				->update(["user_workflow_id"=>$user_workflow_form->id]);
		}
	}


	public static function assignFormsToUser($data){
		$relations_ids=[];
		$existUsers=\Models\ScheduleLink::where("type","users")
			->where('schedule_id',$data['schedule_id'])->get();
		$schedule_type=isset($data['schedule_type'])?$data['schedule_type']:$data['type'];
		$schedule_type_id=isset($data['schedule_type_id'])?$data['schedule_type_id']:$data['link_id'];

		$schedule_link_form= \Models\ScheduleLink::where('type',$schedule_type)
			->where(['schedule_id'=>$data['schedule_id'],'link_id'=>$schedule_type_id])->first();
		if($schedule_link_form){
		$eventData=[
				'schedule_link_id'=>$schedule_link_form->id,
				'schedule_linked_type'=>$data['schedule_linked_type'],
				'schedule_linked_type_id'=>isset($data['schedule_linked_type_id'])?$data['schedule_linked_type_id']:''
			];
		  \Models\ScheduleLinkedFormDetails::insertScheduleLinkedDetails($eventData);
		}

		foreach($existUsers AS $user){
				$schedule_data=["form_id"=>$data['type']=='programmes'?$data['form_id']:$data['link_id'],
				'schedule_link_id'=>$user['id'],
				"refreshed_date"=>isset($data['event_date'])?$data['event_date']:'',
				"type_id"=>$data['schedule_id'],
				"user_id"=>$user['link_id'],
				"schedule_id"=>$data["schedule_id"],
				//it is for insert data type schedule_linked_events
				"schedule_linked_type"=>$data['schedule_linked_type'],
				"schedule_linked_type_id"=>isset($data['schedule_linked_type_id'])?$data['schedule_linked_type_id']:'',

				'type'=>'schedule'];

			$schedule_data=array_merge($schedule_data,$data);
			$schedule_data['type'] = 'schedule';
			$user_form = \Models\UserForm::addUsersForm($schedule_data);



				/**
					  * Add log for userform to workflow relation
					  * tag - workflow_lesson_assign
					  */
					 $schedule_data['user_form_id']=$user_form['id'];
				$schedule_data["type"]='schedule';
				$schedule_data['assigned_method']=isset($data['assigned_method'])?$data['assigned_method']:'form_schedule_assign';
					 unset($schedule_data['form_id']);
					 unset($schedule_data['order']);
					 unset($schedule_data['user_id']);
					 unset($schedule_data['link_id']);
				unset($schedule_data['schedule_id']);
				unset($schedule_data['schedule_linked_type_id']);
					 unset($schedule_data['schedule_link_id']);
					 unset($schedule_data['schedule_linked_type']);
				unset($schedule_data['refreshed_date']);
				unset($schedule_data['approval']);
				unset($schedule_data['schedule_type']);
				unset($schedule_data['schedule_type_id']);
				unset($schedule_data['event_date']);
			unset($schedule_data['enrole_any_learner']);
			unset($schedule_data['deadline_at']);
			unset($schedule_data['drop_off_deadline_at']);


					 $relationModel=\Models\UserFormTemplateWorkflowRelations::addUserFormTemplateWorkFlow($schedule_data);

				if(isset($schedule_data['workflow_id']) && $schedule_data['workflow_id']!=NULL){
					$relations_ids[]=['workflow_id'=>$schedule_data['workflow_id'],
					'id'=>$relationModel->id,
					"schedule_id"=>$data["schedule_id"],
					"user_id"=>$user['link_id']];
				}
			}


			/**Workflow Assigned Forms */
			foreach($relations_ids AS $relation_id){
				$user_workflow_form=\Models\UserWorkflowForm::firstOrCreate([
					"type"=>'schedule',
					"type_id"=>$relation_id['schedule_id'],
					"form_workflow_id"=>$relation_id['workflow_id'],
					"user_id"=>$relation_id['user_id']
					]);
				\Models\UserFormTemplateWorkflowRelations::where("id",$relation_id['id'])
					->update(["user_workflow_id"=>$user_workflow_form->id]);
			}



	}


	public static function getEventTypeRelation($schedule_id){
		$relationArray=[];
		$schedule= \Models\Schedule::where("id", $schedule_id)->first();
/** Fro Meeting */
		if($schedule['type']=='meeting'){
			$schedule_visit_type_workflows =\Models\ScheduleVisitType::
			whereHas("FormWorkFlow.workflow", function ($query)  {
				$query = $query
					->where('form_workflow.status', "1")
				;
			})->
			with('FormWorkFlow.workflow.form_workflow_templates.templateForms')
			->where('id',$schedule["visit_type_id"])->first();
			if($schedule_visit_type_workflows && $schedule_visit_type_workflows->FormWorkFlow){
				foreach ($schedule_visit_type_workflows->FormWorkFlow as $visit_type_workflow) {
					if($visit_type_workflow->workflow)
					{
						foreach ($visit_type_workflow->workflow->form_workflow_templates as $key => $workflow_templates) {
							if($workflow_templates->templateForms)
							{
								foreach ($workflow_templates->templateForms as  $template_binding) {
									if($template_binding->form_id)
									{
										$relationArray[]=["workflow_id"=>$workflow_templates->form_workflow_id,
										'template_id'=>$template_binding->document_template_id,
										'assigned_method'=>'workflow_schedule_visit_assign',
										'type' => 'schedule',
										'type_id' => $schedule_id,
										'form_id'=>$template_binding->form_id];
									}
								}
							}
						}
					}
				}
			}
		}else{
		/**For Event type */
			$event_type = \Models\EventType::with('FormWorkFlow.workflow.form_workflow_templates.templateForms')
			->where('slug',$schedule['type'])->first();
			if (
				$event_type &&
				$event_type->FormWorkFlow
			) {
				foreach ($event_type->FormWorkFlow as $visit_type_workflow) {
					if($visit_type_workflow->workflow)
					{
						foreach ($visit_type_workflow->workflow->form_workflow_templates as $key => $workflow_templates) {
							if($workflow_templates->templateForms)
							{
								foreach ($workflow_templates->templateForms as  $template_binding) {
									if($template_binding->form_id)
									{
										$relationArray[]=["workflow_id"=>$workflow_templates->form_workflow_id,
										'template_id'=>$template_binding->document_template_id,
										'assigned_method'=>'workflow_schedule_event_assign',
										'type' => 'schedule',
										'type_id' => $schedule_id,
										'form_id'=>$template_binding->form_id];
									}
								}
							}
						}
					}
				}
			}
		}

		/**Lesson */
		$lessonFetch=\Models\ScheduleLink::where('schedule_id', $schedule_id)
							->where('type', 'lesson')
							->first();
		if($lessonFetch){
		$learningModuleArr=\Models\FormWorkflow::getFormFromLearningModuleRelation([$lessonFetch->link_id]);
			foreach ($learningModuleArr as  $learning_module) {
					$relationArray[]=["workflow_id"=>$learning_module['workflow_id'],
					'template_id'=>$learning_module["template_id"],
					'assigned_method'=>'workflow_schedule_lesson_assign',
					'type' => 'schedule',
					'type_id' => $schedule_id,
					'reference_type_id'=>$lessonFetch->link_id,
					'reference_type'=>"lesson",
					'form_id'=>$learning_module['form_id']];
			}
		}

		/** Direct */
		$existForms=\Models\ScheduleLink::where("schedule_links.type","forms")
				  ->select("schedule_links.*")
				->where('schedule_id',$schedule_id)
				->join('schedule_linked_form_details',
				'schedule_linked_form_details.schedule_link_id','=','schedule_links.id')
					 ->where("schedule_linked_form_details.type","direct")
				->get();
			// print_r($existForms);die;
			foreach ($existForms as  $exist_form) {
					$relationArray[]=[
					'assigned_method'=>'form_schedule_assign',
					'type' => 'schedule',
					'type_id' => $schedule_id,
					'form_id'=>$exist_form['link_id']];
			}

		/** Programme */
		$existForms=\Models\ScheduleLink::where("schedule_links.type","programmes")->select("schedule_links.*")
		->where('schedule_id',$schedule_id)->get();
		foreach ($existForms as  $exist_form) {
			$learning_modules=\Models\ApprenticeshipStandard::fetchFormIdsUsingProgrammes($exist_form->link_id);
			$learningModuleArr=\Models\FormWorkflow::getFormFromLearningModuleSchedule($learning_modules);
			foreach ($learningModuleArr as  $learning_module) {
				$relationArray[]=["workflow_id"=>$learning_module['workflow_id'],
				'template_id'=>$learning_module["template_id"],
				'assigned_method'=>'workflow_schedule_programme_assign',
				'type' => 'schedule',
				'type_id' => $schedule_id,
				'reference_type_id'=>$exist_form->link_id,
				'reference_type'=>"programme",
				'form_id'=>$learning_module['form_id']];
		}
		}


		return $relationArray;
	}



	public function checkSameFormAssignedOtherSchedule($form_id,$schedule_id){
		$assignedScheduleLink=\Models\ScheduleLink::where('schedule_id',"!=",$schedule_id)
			->where("link_id",$form_id)
			->where("type", "forms")
			->where("status", "1")
			->first();
		return !empty($assignedScheduleLink)?true:false;
	}


	// Detach Assigned workflow
	public static function detachWorkFlowAssignedForms($workflow_id){
		$scheduleLinkedFormDetails=\Models\ScheduleLinkedFormDetails::where("type","form_workflow")
		->where("type_id",$workflow_id)->get();
		if(!empty($scheduleLinkedFormDetails)){
			foreach($scheduleLinkedFormDetails AS $scheduleLinkedFormDetailVal){
			$scheduleLink=\Models\ScheduleLink::where('id',$scheduleLinkedFormDetailVal['schedule_link_id'])->first();
			$scheduleLink->status="0";
			$scheduleLink->save();

			//Here detch event through forms
				/*
				* Here we checked form assigned to other schedules
				* if assigned, then we do not delete the current Form form user assigned
				*/
				// $checkIsAssigned=\Models\UserForm::checkSameFormAssignedOtherSchedule($scheduleLink->link_id,$scheduleLink->schedule_id);
				// if($checkIsAssigned==false){
					$userFormDetails=\Models\UserForm::where("form_id",$scheduleLink->link_id)->where("type","schedule")
					->where("type_id",$scheduleLink->schedule_id)->get();
					foreach( $userFormDetails AS $userFormVal){
						\Models\UserCustomFormValue::where("id",$userFormVal->user_custom_form_value_id)->delete();
						\Models\UserForm::where("id",$userFormVal->id)->delete();
					}
				//}

			}
		}

		//Here detch direct assigned workflow  forms
		$forms=[];
		$userWorkFlowForm=\Models\UserWorkflowForm::with('form_workflow.form_workflow_templates.templateForms')
		->where('form_workflow_id',$workflow_id)->get();
		if(!empty($userWorkFlowForm)){
			foreach($userWorkFlowForm AS $userWorkFlowFormVal){

					if($userWorkFlowFormVal->form_workflow->form_workflow_templates){

						foreach ($userWorkFlowFormVal->form_workflow->form_workflow_templates as $key => $workflow_templates) {

							if($workflow_templates->templateForms)
							{
								foreach ($workflow_templates->templateForms as  $template_binding) {
									if($template_binding->form_id)
									{
										$forms [] = $template_binding->form_id;
									}
								}

							}
					}
				}
			}
			if($forms){
				$userFormDetails=\Models\UserForm::whereIn("form_id",$forms)->where("type","work_flow")->get();
				foreach( $userFormDetails AS $userFormVal){
					\Models\UserCustomFormValue::where("id",$userFormVal->user_custom_form_value_id)->delete();
					\Models\UserForm::where("id",$userFormVal->id)->delete();
				}
			}
		}
	}

	public static function setAllDayEventTime($data) {
		// If all day event, set CalendarStartTime to start_date and CalendarEndTime to end_date, if hours/minutes failed to be parsed, retrieve default values

		if (isset($data["start_date"])) {
			$data["start_date"] = \APP\Tools::parseDateTime($data["start_date"]);
		}

		if (isset($data["end_date"])) {
			$data["end_date"] = \APP\Tools::parseDateTime($data["end_date"]);
		}

		// Set correct duration
		if (
			!empty($data["start_date"]) &&
			!empty($data["end_date"])
		) {
			if (empty($data["duration"])) {
				$data["duration"] = $data['start_date']->diffInMinutes($data["end_date"]);
			}
			$data['all_day_event'] = $data['start_date']->isSameDay($data["end_date"]) ? false : true;
			$maximum_duration = \APP\Tools::dayWorkMinutes();
			if (
				$maximum_duration &&
				$maximum_duration > 0 &&
				$data["duration"] > $maximum_duration
			) {
				$data["duration"] = $maximum_duration;
			}

			// Moving work done in https://emil-reisser-weston.atlassian.net/browse/SCOR-3846 to here.
			if ($data["start_date"] > $data["end_date"]) {
				$data["end_date"] = $data['start_date']->copy()->addMinutes($data["duration"]);
			}

			// If not all day event, end date should be generated by adding duration to start date.
			if (!$data['all_day_event']) {
				$data["end_date"] = $data['start_date']->copy()->addMinutes($data["duration"]);
			}
		}

		if (empty($data["duration"])) {
			$data["duration"] = 30; // default duration
		}

		if (
			!empty($data["start_date"]) &&
			empty($data["end_date"]) &&
			!empty($data["duration"])
		) {
			$data["end_date"] = \Carbon\Carbon::parse($data["start_date"])->addMinutes($data["duration"]);
		}

		return $data;
	}

    public static function notifyUndersubscribedEvents($settings)
    {
        // get all the events which have start date today or less than that and end date not past current date time
        $currentDateTime = Carbon::now();
        $format = \APP\Tools::getConfig('defaultDateFormat')?\APP\Tools::getConfig('defaultDateFormat'):'d/m/Y';
        $format .= ' H:i:s';
        Schedule::query()
//            ->where('start_date', '<=', $currentDateTime)
            ->where('end_date', '>=', $currentDateTime)
            ->where('is_deadline_alert_mailed', '=', false)
            ->chunkById(100, function ($schedules) use($format) {

                foreach ($schedules as $schedule) {

                    $deadlineAt = empty($schedule->deadline_at) ? Tools::calculateDeadline($schedule->start_date) :  $schedule->deadline_at;

                    if(empty($deadlineAt)) {
                        return;
                    }

                    // if deadline date has passed
                    if (Carbon::parse($deadlineAt)->isPast()) {
                        if ($schedule->users->count() <= 0) {

                            // notify to the managers
                            $template = \Models\EmailTemplate
                                ::where('name', 'Booking Undersubscribed')
                                ->where('status', true)
                                ->first();

                            if (empty($template)) {
                                return;
                            }

                            $managers = $schedule->Managers;

                            if($managers->isEmpty()) {
                                return;
                            }
                            $event_date = Carbon::parse($schedule->start_date)->format($format);

                            foreach ($managers as $manager) {
                                $email_queue = new \Models\EmailQueue;
                                $email_queue->email_template_id = $template->id;
                                $email_queue->recipients = [intval($manager->id)];
                                $email_queue->custom_variables = json_encode([
                                    'USER_FNAME' => $manager->fname,
                                    'USER_LNAME' => $manager->lname,
                                    'BOOKING_SESSION' => $schedule->name,
                                    'REGARDS' => $GLOBALS["CONFIG"]->Regards,
                                    'EVENT_DATE' => $event_date
                                ]);
                                $email_queue->save();
                            }

                            $schedule->is_deadline_alert_mailed = true;
                            $schedule->save();
                        }
                    }

                }

            });


    }

    /**
     * When a scheduled event passes the deadline after which no new learners can be assigned, all learners on the waiting list must be notified.
     * The notification can direct users to register for another course with the same content, etc.
     */
    public static function notifyWaitingUsersEventDeadlineAtPassed()
    {
        // get all the events which are not completed
        $currentDateTime = Carbon::now();

        $mailedCount = 0;

        Schedule::query()
//            ->where('start_date', '<=', $currentDateTime)
            ->where('end_date', '>=', $currentDateTime)
            ->whereHas('Waiting')
            ->where('is_deadline_passed_alert_waiting_users', '=', false)
            ->chunkById(100, function ($schedules) use (&$mailedCount) {

                foreach ($schedules as $schedule) {

                    $deadlineAt = empty($schedule->deadline_at) ? Tools::calculateDeadline($schedule->start_date) :  $schedule->deadline_at;

                    if($schedule->Waiting->isEmpty()) {
                        return;
                    }

                    // if deadline date has passed
                    if (Carbon::parse($deadlineAt)->isPast()) {

                        $template = \Models\EmailTemplate::getTemplate('Enrollment Closed Alert');

                        if ($template) {
	                        // notifying waiting users
	                        foreach ($schedule->Waiting as $user) {
	                            $email_queue = new \Models\EmailQueue;
	                            $email_queue->email_template_id = $template->id;
	                            $email_queue->recipients = [intval($user->id)];
	                            $email_queue->custom_variables = json_encode([
	                                'USER_FNAME' => $user->fname,
	                                'USER_LNAME' => $user->lname,
	                                'EVENT_NAME' => $schedule->name,
	                                'REGARDS' => $GLOBALS["CONFIG"]->Regards,
	                            ]);
	                            $email_queue->save();
	                        }

	                        $schedule->is_deadline_passed_alert_waiting_users = true;
	                        $schedule->save();

	                        $mailedCount++;
                        }


                    }

                }

            });

        return 'Mails Queued - ' . $mailedCount;
  }
  public static function removeOutlookEvent($settings)
  {
    $deleted_result = [];
    $teams = new \APP\Teams(false);
    // Get all the schedules deleted and have outlook integration
    Schedule::onlyTrashed()->whereNotNull('outlook_event_id')->chunk(500, function ($schedules) use ($teams,$deleted_result) {
      foreach ($schedules as $schedule) {
        $company_email = isset($schedule->CreatedBy->company) ? $schedule->CreatedBy->company->email_from : null;
        $room_email = $schedule->room_email;
        $sender_email = $company_email; //default company email
        if ($room_email || !empty($company_email)) {
          $sender_email =  Tools::getConfig("outlookOutGoingEmail") == '1' ? $company_email : $room_email;
          if (empty($sender_email)) {
            $sender_email = $company_email ?? $room_email;
          }
          $sender_email = trim($sender_email);
        }
        $result = $teams->deleteEvent($schedule->outlook_event_id, $sender_email);
        $deleted_result[$schedule->id]=['outlook_event_id'=>$schedule->outlook_event_id,'result'=>$result];
        $schedule->outlook_event_id = null;
        $schedule->save();
      }
    });
    return json_encode($deleted_result);
  }
    public static function waitingListRecycle()
    {

        $lastUpdated = Tools::getConfig('lastWaitingListRecycle');
        if (!$lastUpdated) {
            $lastUpdated = Carbon::now()->startOfDay();
        } else {
            $lastUpdated = Carbon::parse($lastUpdated);
        }
        $lastUpdated->addHour(Tools::getConfig('WaitingListRecycleTime'));
        if ($lastUpdated->isPast()) {
            Schedule::whereHas('Waiting')->chunk(100, function ($schedules) {
                foreach ($schedules as $schedule) {
                    $links = ScheduleLink::where('schedule_id', $schedule->id)->where('type', 'users_queue')->orderBy('order')->get();
                    $rotatedLinks = $links->push($links->shift());
                    // Update the order in the database
                    foreach ($rotatedLinks as $index => $link) {
                        $link->order = $index + 1;
                        $link->save();
                    }
                }
            });
            Configuration::where('key','lastWaitingListRecycle')->update(['value'=>Carbon::now()->toString()]);
        }
    }

	public static function updateFutureEvents () {
		$updated_links = 0;

		// Find all future events, find all linked users, if status is %%event_completion_state_not_attempted%%, set completion_status as ""
		$future_events = \Models\Schedule
			::where('schedules.status', true)
			->where('start_date', '>', \Carbon\Carbon::now())
			->orderBy('name')
			->get()
		;
		foreach ($future_events as $key => $future_event) {
			$future_event_user_links = \Models\ScheduleLink
				::where('schedule_links.status', true)
				->where('schedule_links.schedule_id', $future_event->id)
				->whereIn('schedule_links.type', ['users', 'users_queue'])
				//->whereRaw("schedule_links.completion_status = '%%event_completion_state_not_attempted%%'")
				->get()
			;
			foreach ($future_event_user_links as $key => $future_event_user_link) {
				$future_event_user_link->completion_status = '';
				$future_event_user_link->saveWithoutEvents();
				$updated_links++;
			}
		}
		return $updated_links;
	}
}
