<?php
include __DIR__ . '/../crons/setup.php';

use Models\ResourceQuery;
use Illuminate\Database\Capsule\Manager as DB;

// Suppress PHP warnings/notices during testing
error_reporting(E_ERROR);

echo "=== ResourceQuery Optimization Test ===\n";
echo "Date: " . date('Y-m-d H:i:s') . "\n\n";

// Get all resource queries with valid linked items
$resourceQueries = ResourceQuery::whereNotNull('raw_query')
    ->where(function($query) {
        // Resources and lessons: must have valid learning_modules
        $query->where(function($subQuery) {
            $subQuery->whereIn('type', ['resources', 'lessons'])
                ->whereExists(function($existsQuery) {
                    $existsQuery->select(DB::raw(1))
                        ->from('learning_modules')
                        ->whereRaw('learning_modules.id = resource_queries.type_id')
                        ->where('learning_modules.status', 1);
                });
        })
        // Events: must have valid schedules with future or today's start_date
        ->orWhere(function($subQuery) {
            $subQuery->where('type', 'events')
                ->whereExists(function($existsQuery) {
                    $existsQuery->select(DB::raw(1))
                        ->from('schedules')
                        ->whereRaw('schedules.id = resource_queries.type_id')
                        ->where('schedules.status', 1)
                        ->whereNull('schedules.deleted_at')
                        ->where('schedules.start_date', '>=', DB::raw('CURDATE()'));
                });
        })
        // Include other types that don't use type_id linking
        ->orWhereNotIn('type', ['resources', 'lessons', 'events']);
    })
    ->get();

if ($resourceQueries->isEmpty()) {
    echo "No ResourceQuery entries found with raw_query field.\n";
    exit(1);
}

// Get total count for comparison
$totalQueries = ResourceQuery::whereNotNull('raw_query')->count();
$validQueries = count($resourceQueries);
$filteredOut = $totalQueries - $validQueries;

echo "Total ResourceQuery entries with raw_query: $totalQueries\n";
echo "Filtered out (invalid/inactive items): $filteredOut\n";
echo "Valid entries to test: $validQueries\n\n";

$totalOldTime = 0;
$totalNewTime = 0;
$testResults = [];
$errorCount = 0;
$optimizationUsedCount = 0;
$processedCount = 0;

foreach ($resourceQueries as $query) {
    $processedCount++;

    // Show progress every 100 queries (without newline)
    if ($processedCount % 100 == 0) {
        echo "\rProcessed: $processedCount/" . count($resourceQueries);
        if ($processedCount == count($resourceQueries)) {
            echo "\n"; // Final newline
        }
    }
    $hasError = false;
    $hasResultMismatch = false;
    $errorMessages = [];
    $missingStatusFilter = false;

    // Initialize defaults for results storage
    $oldExecutionTime = 0;
    $newExecutionTime = 0;
    $oldUserIds = [];
    $newUserIds = [];
    $optimizationUsed = false;
    $userIdsMatch = false;
    $performanceImprovement = 0;

    try {
        // Parse the query variables
        $queryVariables = json_decode($query->query_variable, true);
        if ($queryVariables === null) {
            $hasError = true;
            $errorMessages[] = "Could not parse query_variable JSON: " . json_last_error_msg();
        } else {
            // Check if original query is missing status filter
            $originalRawQuery = $query->raw_query;
            $hasStatusFilter = strpos($originalRawQuery, 'users.status') !== false;
            $usesUsersTable = strpos($originalRawQuery, 'FROM users') !== false;
            $missingStatusFilter = false;
            
            // For fair comparison, do NOT modify the original query
            // Instead, we'll remove status filter from new query if original doesn't have it
            $adjustedOriginalQuery = $originalRawQuery;
            if ($usesUsersTable && !$hasStatusFilter) {
                $missingStatusFilter = true;
                // Note: We don't modify the original query to preserve its intended business logic
                // Instead, we'll remove status filter from the new query for fair comparison
            }
            
            // Test original raw_query (adjusted if needed)
            $oldStartTime = microtime(true);

            try {
                $oldResults = DB::select($adjustedOriginalQuery);
                $oldEndTime = microtime(true);
                $oldExecutionTime = ($oldEndTime - $oldStartTime);

                // Extract user IDs from old results
                $oldUserIds = array_map(function($user) {
                    return (int)$user->id;
                }, $oldResults);
                sort($oldUserIds);

            } catch (Exception $e) {
                $hasError = true;
                $errorMessages[] = "Original query failed: " . $e->getMessage();
                $errorCount++;
            }

            // Test new generateRawQuery method only if original worked and has query variables
            if (!$hasError) {
                // Skip queries with no query variables (manually crafted SQL)
                if (empty($queryVariables)) {
                    // For manually crafted queries, mark as matching (no optimization possible)
                    $newUserIds = $oldUserIds;
                    $newExecutionTime = $oldExecutionTime;
                    $userIdsMatch = true;
                    $optimizationUsed = false;
                    $performanceImprovement = 0;
                } else {
                    $newStartTime = microtime(true);

                    try {
                        $newRawQuery = \DB\ResourceQuery::generateRawQuery($queryVariables);
                    
                    // If original query was missing status filter, remove it from new query for fair comparison
                    $adjustedNewQuery = $newRawQuery;
                    if ($missingStatusFilter) {
                        // Handle various patterns of users.status = 1 removal
                        
                        // Pattern 1: "where users.status = 1 and ((...conditions...))" 
                        $adjustedNewQuery = preg_replace('/where users\.status = 1 and \(\(/i', 'where ((', $adjustedNewQuery);
                        
                        // Pattern 2: "WHERE users.status = 1 AND ..." (general case)
                        $adjustedNewQuery = preg_replace('/WHERE users\.status = 1 AND /i', 'WHERE ', $adjustedNewQuery);
                        
                        // Pattern 3: UNION queries - remove users.status = 1 from each part
                        $adjustedNewQuery = preg_replace('/users\.status = 1 AND \(\(/i', '((', $adjustedNewQuery);
                        
                        // Pattern 4: Remove standalone "users.status = 1 AND " patterns
                        $adjustedNewQuery = str_replace('users.status = 1 AND ', '', $adjustedNewQuery);
                        
                        // Pattern 5: Handle "WHERE users.status = 1" as the only condition (remove WHERE entirely)
                        $adjustedNewQuery = preg_replace('/WHERE users\.status = 1(?!\s+AND)/i', '', $adjustedNewQuery);
                        
                        // Clean up any orphaned WHERE clauses
                        $adjustedNewQuery = preg_replace('/WHERE\s*(?=UNION|GROUP BY|ORDER BY|LIMIT|$)/i', '', $adjustedNewQuery);
                        
                        // Clean up multiple spaces and trim
                        $adjustedNewQuery = preg_replace('/\s+/', ' ', $adjustedNewQuery);
                        $adjustedNewQuery = trim($adjustedNewQuery);
                    }
                    
                    $newResults = DB::select($adjustedNewQuery);
                    $newEndTime = microtime(true);
                    $newExecutionTime = ($newEndTime - $newStartTime);

                    // Extract user IDs from new results
                    $newUserIds = array_map(function($user) {
                        return (int)$user->id;
                    }, $newResults);
                    sort($newUserIds);

                    // Check if optimization was used (by looking for UNION in query)
                    $optimizationUsed = strpos($newRawQuery, 'UNION') !== false;
                    if ($optimizationUsed) {
                        $optimizationUsedCount++;
                    }

                    // Compare results
                    $userIdsMatch = ($oldUserIds === $newUserIds);
                    $performanceImprovement = $oldExecutionTime > 0 ? (($oldExecutionTime - $newExecutionTime) / $oldExecutionTime) * 100 : 0;

                    } catch (Exception $e) {
                        $hasError = true;
                        $errorMessages[] = "New query failed: " . $e->getMessage();
                        $errorCount++;
                    }
                }
            }
        }

        if (!$userIdsMatch) {
            $hasResultMismatch = true;
        }

        // Only show output if there are errors or mismatches
        if ($hasError || $hasResultMismatch) {
            echo "Testing ResourceQuery ID: {$query->id}\n";
            echo "Type: {$query->type}, Action: {$query->action}\n";

            if ($hasError) {
                foreach ($errorMessages as $msg) {
                    echo "  ❌ $msg\n";
                }
            }

            if ($hasResultMismatch && !$hasError) {
                echo "  🔍 Testing original raw_query...\n";
                if (isset($missingStatusFilter) && $missingStatusFilter) {
                    echo "  ⚠️  Original query missing users.status = 1 filter\n";
                    echo "  📝 For fair comparison: Removed status filter from new query to match original business logic\n";
                }
                echo "  ✅ Original query completed in " . number_format($oldExecutionTime, 4) . "s, found " . count($oldUserIds) . " users\n";
                echo "  🚀 Testing new generateRawQuery method...\n";
                echo "  ✅ New query completed in " . number_format($newExecutionTime, 4) . "s, found " . count($newUserIds) . " users\n";

                if ($optimizationUsed) {
                    echo "  🎯 UNION optimization was used!\n";
                }

                echo "  ❌ User results DO NOT match!\n";
                echo "    - Original: " . count($oldUserIds) . " users\n";
                echo "    - New: " . count($newUserIds) . " users\n";

                // Show differences
                $onlyInOld = array_diff($oldUserIds, $newUserIds);
                $onlyInNew = array_diff($newUserIds, $oldUserIds);

                if (!empty($onlyInOld)) {
                    echo "    - Only in original: " . implode(', ', array_slice($onlyInOld, 0, 10)) . (count($onlyInOld) > 10 ? '...' : '') . "\n";
                }
                if (!empty($onlyInNew)) {
                    echo "    - Only in new: " . implode(', ', array_slice($onlyInNew, 0, 10)) . (count($onlyInNew) > 10 ? '...' : '') . "\n";
                }

                if ($performanceImprovement > 0) {
                    echo "  📈 Performance improvement: " . number_format($performanceImprovement, 1) . "% (" .
                         number_format($oldExecutionTime * 1000, 2) . "ms → " .
                         number_format($newExecutionTime * 1000, 2) . "ms)\n";
                } else if ($performanceImprovement < 0) {
                    echo "  📉 Performance regression: " . number_format(abs($performanceImprovement), 1) . "% (" .
                         number_format($oldExecutionTime * 1000, 2) . "ms → " .
                         number_format($newExecutionTime * 1000, 2) . "ms)\n";
                }

                // Debug: Show both queries for debugging
                if (isset($newRawQuery) && isset($queryVariables)) {
                    echo "  🔍 DEBUGGING - Query mismatch detected:\n";
                    echo "  📄 Original query: " . $query->raw_query . "\n";
                    if ($missingStatusFilter && isset($adjustedNewQuery)) {
                        echo "  📄 New query (original): " . $newRawQuery . "\n";
                        echo "  📄 New query (adjusted - status filter removed): " . $adjustedNewQuery . "\n";
                    } else {
                        echo "  📄 New query: " . $newRawQuery . "\n";
                    }
                    echo "  📊 Query variables count: " . count($queryVariables) . "\n";
                    // Show just the key parts of query variables for readability
                    foreach ($queryVariables as $i => $qv) {
                        echo "  📋 Query $i: {$qv['query_variable_where']} with logic='" . ($qv['logic'] ?? 'null') . "'\n";
                    }
                }
            }

            echo "\n";  // Only print newline when there was output
        }

        // Store results
        $testResults[] = [
            'query_id' => $query->id,
            'type' => $query->type,
            'action' => $query->action,
            'old_time' => $oldExecutionTime,
            'new_time' => $newExecutionTime,
            'old_count' => count($oldUserIds),
            'new_count' => count($newUserIds),
            'results_match' => $userIdsMatch,
            'optimization_used' => $optimizationUsed,
            'performance_improvement' => $performanceImprovement
        ];

        $totalOldTime += $oldExecutionTime;
        $totalNewTime += $newExecutionTime;

    } catch (Exception $e) {
        echo "  ❌ Test failed with exception: " . $e->getMessage() . "\n\n";
        $errorCount++;
        continue;
    }
}

// Summary
echo "=== SUMMARY ===\n";
echo "Total queries tested: " . count($testResults) . "\n";
echo "Queries with errors: " . $errorCount . "\n";
echo "Queries using UNION optimization: " . $optimizationUsedCount . "\n";
echo "Total old execution time: " . number_format($totalOldTime, 4) . "s\n";
echo "Total new execution time: " . number_format($totalNewTime, 4) . "s\n";

if ($totalOldTime > 0) {
    $overallImprovement = (($totalOldTime - $totalNewTime) / $totalOldTime) * 100;
    echo "Overall performance improvement: " . number_format($overallImprovement, 1) . "%\n";
}

// Results accuracy
$accurateResults = array_filter($testResults, function($result) {
    return $result['results_match'];
});

echo "Results accuracy: " . count($accurateResults) . "/" . count($testResults) . " (" . number_format((count($accurateResults) / count($testResults)) * 100, 1) . "%)\n";

// Show top performance improvements
echo "\n=== TOP PERFORMANCE IMPROVEMENTS ===\n";
usort($testResults, function($a, $b) {
    return $b['performance_improvement'] <=> $a['performance_improvement'];
});

$topImprovements = array_slice($testResults, 0, 5);
foreach ($topImprovements as $result) {
    if ($result['performance_improvement'] > 0) {
        echo "Query ID {$result['query_id']}: " . number_format($result['performance_improvement'], 1) . "% improvement (" .
             number_format($result['old_time'] * 1000, 2) . "ms → " .
             number_format($result['new_time'] * 1000, 2) . "ms)";
        if ($result['optimization_used']) {
            echo " [UNION used]";
        }
        echo "\n";
    }
}

// Show any problematic queries
echo "\n=== PROBLEMATIC QUERIES ===\n";
$problemQueries = array_filter($testResults, function($result) {
    return !$result['results_match'] || $result['performance_improvement'] < -10;
});

if (empty($problemQueries)) {
    echo "No problematic queries found! ✅\n";
} else {
    foreach ($problemQueries as $result) {
        echo "Query ID {$result['query_id']}: ";
        if (!$result['results_match']) {
            echo "Results mismatch ({$result['old_count']} vs {$result['new_count']} users) ";
        }
        if ($result['performance_improvement'] < -10) {
            echo "Performance regression " . number_format(abs($result['performance_improvement']), 1) . "% (" .
                 number_format($result['old_time'] * 1000, 2) . "ms → " .
                 number_format($result['new_time'] * 1000, 2) . "ms)";
        }
        echo "\n";
    }
}

echo "\n=== Test Complete ===\n";