// Checks agains each resource, used in multiple places
@mixin resource-check() {
	.fa {
		& {
			position: absolute;
			left: 0.3125rem;
			bottom: 0.3125rem;
			font-size: 1.5rem;
			color: #fff;
			background: #000;
			width: 1.875rem;
			height: 1.875rem;
			border-radius: 0.9375rem;
			text-align: center;
			padding-top: 0.1875rem;
			box-shadow: 0 0 3px 1px rgba(0,0,0,0.2);
			z-index: 1;
		}
		&.red {
			background: #d9534f;
		}
		&.orange {
			background: #f0ad4e;
		}
		&.green {
			background: #26B99A;
		}
		&.blue {
			background: #337ab7;
		}
		&.gold {
			color: #ebef00;
			background: transparent;
		}
		&.qa {
			left: 2.8125rem;
		}
	}
}

.ico {
	&-container {
		position: absolute;
		left: 0.3125rem;
		bottom: 0.3125rem;
		height: 1.875rem;
		width: 100%;
		z-index: 1;
	}
	&-item {
		display: inline-block;
		color: #fff;
		border-radius: 0.75rem;
		width: 1.5rem;
		height: 1.5rem;
		padding-top: 0.1875rem;
		background-size: cover !important;
		@media screen and (max-width: $screen-xs) {
			width: 0.875rem;
			height: 0.875rem;
		}
	}

}

.learner {
	&-categories-landing {
		@media screen and (max-width: $screen-md-min) {
			font-size: 2.5rem;
		}
	}
	&-info-icon {
		& {
			font-size: 2rem;
			line-height: 3.5625rem;
			color: #fff;
			cursor: pointer;
		}
		@at-root {
			a#{&} {
				&:hover,
				&:focus {
					color: #fff;
					text-decoration: none;
				}
			}
		}
	}
	&-front {
		& {
			text-align: center;
		}
		> div {
			a {
				& {
					cursor: pointer;
					transition: all 300ms ease-in-out;
					display: block;
					padding: 1.875rem 0;
					text-decoration: none;
					font-size: 2.5rem;
					height: 100%;
					color: #fff;
				}
				&:hover {
					background: #3a3939;
				}
			}
		}
		.fa,
		.glyphicon {
			display: block;
			font-size: 5.625rem;
		}
	}

	&__due-tasks {
		.fa {
			& {
				position: relative;
			}
			.due-count {
				position: absolute;
				top: 81%;
				left: 56%;
				font-size: 1.625rem;
				color: #000;
				margin: -0.8125rem 0 0 -1.1875rem;
				text-align: center;
				box-shadow: 1px 1px 1px 1px;
				background: #fff;
				border-radius: 1.25rem;
				padding: 0.3125rem;
				min-width: 0.9375rem;
			}
		}
	}
	&__top {
		&-icons {
			height: 5.3125rem;
			list-style: none;
			margin: 0;
			padding: 0;
			display: inline-flex;
			align-items: center;
			&--dark {
				& {
					float: none;
					text-align: center;
				}
				.learner__top-icon {
					& {
						float: none;
						display: inline-block;
					}
					a {
						& {
							color: #444;
							transition: all 500ms ease-in-out;
							line-height: 0.625rem;
							padding: 0.75rem 1.25rem 1.125rem 1.25rem; //padding: 20px;
							text-decoration: none;
							position: relative;
						}
						&:hover {
							div {
								color: #ffffff88;
							}
						}
						img {
							vertical-align: unset;
						}
						div {
							font-size: 0.625rem;
							color: #fff; //#ffffff88;
							text-transform: uppercase;
							letter-spacing: 0.5px;
						}
						span.glyphicon {
							color: #fff;
						}
						.cart {
							color: var(--btn-warning-color);
							background-color: var(--btn-warning-bg);
							padding: 0.4rem;
							font-size: 0.8rem;
							border-radius: 100%;
							position: absolute;
							font-weight: 600;
							top: 16%;
							right: 15%;
						}
					}
				}
				img {
					height: 2.8125rem;
					image-rendering: -o-crisp-edges;
					image-rendering: -webkit-optimize-contrast;
				}
			}
			@media screen and (max-width: 768px) {
				float: none;
				display: flex;
				&--dark {
					img {
						height: 4.375rem;
					}
				}
			}
		}
		&-icon {
			& {
				display: inline-block;
				align-self: center;
				@media screen and (max-width: 768px) {
					flex-grow: 1;
				}
				@media screen and (max-width: $screen-xs) {
					flex-grow: 0;
				}

			}
			&--hide-narrow {
				@media screen and (max-width: $screen-md-min) {
					display: none;
				}
			}

			a {
				& {
					display: block;
					line-height: 3.5625rem;
					padding: 0 0.75rem;
					font-size: 1.25rem;
					text-align: center;
					cursor: pointer;
					color: #fff;
					@media screen and (max-width: $screen-md-min) {
						padding: 0 1.5rem;
					}
					@media screen and (max-width: $screen-xs) {
						padding: 0 0.375rem;
					}
				}
				&.upload {
					font-size: 1.875rem;
					line-height: 3.375rem;
					color: #f0ad4e;
					@media screen and (max-width: $screen-md-min) { //$screen-xs-max) {
						font-size: 2.5rem;
					}
					&.smcr {
						line-height: initial;
					}
				}
				.fa {
					position: relative;
					@media screen and (max-width: $screen-sm) {
						font-size: 1.25rem;
					}
				}
				.due-count {
					position: absolute;
					top: 1rem;
					left: 0.8125rem;
					text-align: center;
					color: #000;
					font-size: 0.75rem;
					font-weight: 700;
					font-family: Yantramanav, Ubuntu, system-ui, -apple-system, segoe ui, Roboto, Helvetica, Arial, sans-serif, apple color emoji, segoe ui emoji;
					background: white;
					padding: 0.1875rem;
					border-radius: 0.625rem;
					box-shadow: 1px 1px 1px 1px;
					min-width: 0.9375rem;
				}
				span.glyphicon {
					font-size: 1.75rem;
					padding-bottom: 0.5rem;
					color: #000;
					padding-top: 0.5rem;
				}
			}
		}
		&-text-menu {
			a {
			    display: flex;
    			flex-wrap: wrap;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				text-transform: uppercase;
				font-weight: normal;
				font-size: 0.6875rem;
				letter-spacing: 1px;
				//line-height: 54px;
				padding: 0 0.4375rem 0 0.4375rem;
				color: #fff;
				&.active_item {
					text-decoration: none; //underline;
					font-weight: bold;
				}
				white-space: nowrap;
				@media screen and (max-width: $screen-md-min) {
					font-size: 1.125rem;
				}
			}
		}
	}
	&-resource {
		&__category,
		&__status,
		&__type,
		&__description {
			border-radius: 0.1875rem;
			padding: 0.0625rem 0.3125rem;
			display: inline-block;
			@media screen and (max-width: $screen-md-min) {
				font-size: 1.4rem;
			}
			@media screen and (max-width: $screen-sm) {
				font-size: 1rem;
			}
		}
		&__category {
			& {
				background: rgba(223,184,28, 0.1);
			}
			&--health-and-safety {
				background: rgba(223,184,28, 0.2);
			}
			&--security {
				background: rgba(88, 144, 193, 0.2)
			}
			&--hr {
				background: rgba(63, 218, 98, 0.2);
			}
			&--housing {
				background: rgba(158, 92, 92, 0.2);
			}
		}
		&__favorite {
			font-size: 0.8em;
			cursor: pointer;
			&.fa-star {
				color: #ebef00;
			}
		}
		&__loading {
			& {
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				pointer-events: none;
				z-index: 2;
			}
			.loading-splash {
				&.sub-loading {
					position: absolute;
					opacity: 1;
				}
				&.in {
					opacity: 0;
					pointer-events: none;
				}
			}
		}
		&__missing {
			& {
				color: #fff;
				position: absolute;
				z-index: 56;
				top: 0;
				left: 0;
				right: 0;
				text-align: center;
				font-size: 1.875rem;
				padding: 1.25rem;
				opacity: 0;
				transition: all 150ms ease-in-out;
				.light-theme & {
					color: #212121;
				}
			}
			&--visible {
				opacity: 1;
				pointer-events: all;
				background-color: darkorange;
			}
		}
		&__status {
			&-parent {
				@include resource-check();
				.fa {
					position: relative;
					bottom: 0;
				}
			}
			& {
				//background: rgba(217, 83, 79, 0.2);
				font-size: 1.125rem;
				@media screen and (max-width: $screen-md-min) {
					font-size: 2.375rem;
				}
				@media screen and (max-width: $screen-xs) {
					font-size: 1.3125rem;
				}
			}
			&--not-attempted {
				//background: rgba(217, 83, 79, 0.2);
			}
			&--in-progress {
				//background: rgba(240, 173, 78, 0.2);
			}
			&--completed {
				//background: rgba(38, 185, 154, 0.2);
			}
		}
		&__type {
			background: rgba(144, 185, 0, 0.2);
		}
		&__description {
			color: rgba(255, 255, 255, 0.85);
			padding: 0;
			width: 16.5625rem;
			@media (max-width: $screen-md-min) {
				font-size: 2.375rem; //40px;
				&__read-more {
					font-size: 1.125rem; //30px;
				}
				width: 100%; //900px;
			}
			@media screen and (max-width: $screen-xs) {
				font-size: 1.125rem;
			}
			.light-theme & {
				color: #212121;
			}

		}
		&__expired {
			background:
			rgba(255,255,255, 0.2);
			padding: 0.625rem;
			margin-bottom: 1.25rem;
			font-size: 1.25rem;
		}


		&__finder {
			& {
				position: absolute;
				bottom: 15.625rem;
				left: 0;
				right: 0;
				background: rgba(33, 33, 33, 0.93);
				box-shadow: 0 0.375rem 0.625rem 1px rgba(0,0,0,0.4);
				z-index: 2;
				transition: all 150ms ease-in-out;
				color: #fff;
				padding-top: 0.9375rem;
				overflow-x: hidden;
			}
			&--pkf {
				background: #333;
			}
			&--close {
				position: absolute;
				top: 0.625rem;
				right: 0.625rem;
				font-size: 0.75rem;
				cursor: pointer;
				.fa {
					font-weight: 800;
					font-size: 1.25rem;
					vertical-align: bottom;
					margin-left: 0.3125rem;
				}
			}
		}
		&__content {
			background: $learner-dark;
			.light-theme & {
				background: #f6f6f6;
				color: #212121;
			}
			width: 100%;
			color: #fff;
			.x_panel {
				background: rgba(255, 255, 255, 0.08);
				border: none;
			}
			.x_title {
				border-bottom: 0.125rem solid rgba(230, 233, 237, 0.32);
			}
			.table-striped>tbody>tr:nth-of-type(odd) {
				background-color: rgba(249, 249, 249, 0.08);
			}
			.table>tbody>tr>td, .table>tbody>tr>th, .table>tfoot>tr>td, .table>tfoot>tr>th, .table>thead>tr>td, .table>thead>tr>th {
				border-top: 0.0625rem solid rgba(221, 221, 221, 0.24);
			}
			.table>thead>tr>th {
				border-bottom: 0.125rem solid rgba(221, 221, 221, 0.22);
			}
			.ln_solid {
				border-top: 0.0625rem solid rgba(229, 229, 229, 0.52);
				background-color: rgb(0, 0, 0);
			}
		}
		&__wrap {
			& {
				overflow: hidden;
			}
		}
		&__screen-wrap {
			transition: transform 400ms ease-in-out;
			transform: none;
		}
		&__screen  {
			width: 100%;
			overflow: hidden;
		}
		&__overview {
			& {
				background: #fff;
				@media screen and (max-width: $screen-md-min) {
					h1 {
						font-size: 1.125rem;
					}
				}
			}
			&.first {
				transform: translateX(-100%);
			}
			&.second {
				transform: translateX(-200%);
			}
			&.third {
				transform: translateX(-300%);
			}
			&.fourth {
				transform: translateX(-400%);
			}
		}
		&__pre-requisite-resources,
		&__lesson-info,
		&__required-modules {
			& {
				background: $learner-dark;
				//color: #222;
				.light-theme & {
					background: #f6f6f6;
					color: #212121;
				}
			}
			> div > div { // horrible, I know, no really, horrible, who does this in 2017!
				height: 25rem;
				position: relative;
			}
			ul.resource-list {
				& {
					transition: all 450ms ease-in-out;
					list-style: none;
					margin: 0;
					padding: 0;
					//text-align: center;
					//white-space: nowrap;
					position: absolute;
					top: 0;
					left: 0;
					right: 0;
					display: flex;
				}
				li.resource-list--item {
					& {
						display: inline-block;
						max-width: 18rem;
						min-width: 15.9375rem;
					}
					> a {
						& {
							cursor: pointer;
							display: block;
							transition: all 150ms ease-in-out;
							margin: 1.25rem 1.25rem 0px 1.25rem;
							position: relative;
							color: #fff;
    						max-width: 15.4375rem;
    						white-space: initial;
						}
						&:hover,
						&:focus {
							text-decoration: none;
						}
						@include resource-check();
						span {
							position: relative;
							display: inline-block;
						}
						img {
							max-height: 9.375rem;
							max-width: 100%;
							box-shadow: 1px 1px 1px 1px rgba(0,0,0,0.3);
						}
					}
				}
			}
		}
		&__lesson-info {
			background: $learner-dark;
		}
		&__book-learning-module {
			padding: 1.25rem;
		}
		&__details,
		&__meetings {
			& {
				background: $learner-dark;
				.light-theme & {
					background: #f6f6f6;
					color: #212121;
				}
			}
			&--contained {
				position: absolute;
				top: 1rem;
				left: 0;
				right: 0;
				bottom: 0;
				overflow: hidden;
				overflow-y: auto;
				@media (max-width: $screen-md-min) {
					font-size: 1.125rem;
				}
			}
		}
		&__sign-off {
			& {
				background: $learner-dark;
				.light-theme & {
					background: #f6f6f6;
					color: #212121;
				}
			}
			> div {
				padding-top: 1rem;
			}
		}
		&__overview,
		&__tabs {
			width: 100%;
		}
		&__tabs {
			& {
				min-height: 3.125rem;
				background: $learner-dark;
			}
			.light-theme & {
				background: #fff;
				color: #212121;
				ul {
					li {
						a {
							color: #212121 !important;
						}
						&:hover {
							a {
								background: #cacaca !important;
							}
						}
					}
				}
			}
			ul {
				& {
					list-style: none;
					margin: 0 0 0 0.625rem;
					padding: 0;
				}
				li {
					& {

					}
					a {
						@media screen and (max-width: $screen-md-min) {
							& {
								font-size: 1.875rem;
							}
						}
						@media screen and (max-width: $screen-xs) {
							font-size: 1.3125rem;
						}
						display: block;
						transition: all 150ms ease-in-out;
						line-height: 3.125rem;
						padding: 0 1.25rem;
						color: #fff;
						font-weight: 700;
						white-space: nowrap;
						&:hover,
						&:focus {
							text-decoration: none;
						}
					}
					&:hover {
						a {
							background: #404040;

						}
					}
					&.active {
						a {
							border-bottom: solid 0.3125rem red;
							line-height: 2.8125rem;
						}
					}
				}
			}
		}
		&__includes {
			.include {
				&-completed,
				&-progress,
				&-favorite {
					@include resource-check();
					.fa {
						position: initial;
					}
				}
			}
		}
		&__sidebar {
			& {
				width: 18.75rem;
				@media screen and (max-width: $screen-md-min) {
					& {
						width: 100%; //auto;
					}
					font-size: 1.875rem;
				}
				background: $learner-dark;
				padding: 0 1.25rem;
				color: #fff;
				position: relative;
			}
			img {
				@media screen and (max-width: $screen-xs) {
					display: none;
				}
			}
			h1 {
				font-size: 1.875rem;
				@media screen and (max-width: $screen-md-min) {
					font-size: 3rem;
				}
				@media screen and (max-width: $screen-sm) {
					font-size: 1.5rem;
					padding-top: 0.625rem;
				}
				@media screen and (max-width: $screen-xs) {
					font-size: 1rem;
					padding-top: 0.625rem;
				}

				border-bottom: solid 1px rgba(255, 255, 255, 0.85);
				padding-bottom: 0.625rem;
				margin-bottom: 1.5625rem;
			}
			.light-theme & {
				background: #fff;
				color: #212121;
				&:after {
				/* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#ffffff+0,ffffff+100&1+0,0+100 */
				background: -moz-linear-gradient(left,  rgba(255,255,255,1) 0%, rgba(255,255,255,0) 100%); /* FF3.6-15 */
				background: -webkit-linear-gradient(left,  rgba(255,255,255,1) 0%,rgba(255,255,255,0) 100%); /* Chrome10-25,Safari5.1-6 */
				background: linear-gradient(to right,  rgba(255,255,255,1) 0%,rgba(255,255,255,0) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
				filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#00ffffff',GradientType=1 ); /* IE6-9 */

				}
			}
			&__shadow {
				&:after {
					content: "";
					position: absolute;
					top: 0;
					left: 100%;
					bottom: 0;
					width: 7.5rem;
					z-index: 1;
					/* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/$learner-dark+0,000000+100&1+0,0+100 */
					background: -moz-linear-gradient(left, rgba(33,33,33,1) 0%, rgba(0,0,0,0) 100%); /* FF3.6-15 */
					background: -webkit-linear-gradient(left, rgba(33,33,33,1) 0%,rgba(0,0,0,0) 100%); /* Chrome10-25,Safari5.1-6 */
					background: linear-gradient(to right, rgba(33,33,33,1) 0%,rgba(0,0,0,0) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
					filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='$learner-dark', endColorstr='#00000000',GradientType=1 ); /* IE6-9 */
				}
			}
		}
		&__promo {
			& {
				//background: url('../images/resource-bg.jpg') #888 center scroll no-repeat;
				background-color: $learner-dark;
				background-position: center;
				background-repeat: no-repeat;
				background-size: cover;
				color: #fff;
				font-size: 3.125rem;
				//padding: 50px;
				position: relative;
			}
			&-close {
				.lms-content--learner & { // reference parent for owerwrite.
					& {
						position: absolute;
						top: 0.625rem;
						right: 0.625rem;
						color: #fff;
						font-weight: 100;
						padding: 0 0.625rem;
						font-size: 3.125rem;
						line-height: initial;
						text-shadow: 0 0 0.375rem $learner-dark;
						transition: all 150ms ease-in-out;
						.light-theme & {
							color: #212121;
						}
					}
					&:hover,
					&:focus {
						text-decoration: none;
						text-shadow: 0 0 6px #fff;
						color: $learner-dark;
					}
				}
			}
		}
		&__footer {
			height: 12.5rem;
			//background: $learner-dark;
			width: 100%;
			position: relative;
		}
		&__include-completed,
		&__include-progress,
		&__include-favorite {
			& {
				margin: 0.625rem;
				color: #fff;
				font-size: 1rem;
				cursor: pointer;
			}
			.fa {
				& {
					width: 1.25rem;
					font-size: 1.25rem;
				}
				&.fa-check-square-o {
					//color: #26B99A;
				}
			}
		}
		&__browse {
			& {
				position: relative;
				margin: 0.625rem 0.625rem 3.125rem;
				padding: 0.625rem;
				background: #fff;
				box-shadow: 0px 3px 3px 0 rgba(0, 0, 0, 0.4);
				cursor: pointer;
				text-align: center;
				font-weight: 600;
				font-size: 1rem;
				text-transform: uppercase;
				color: #374e67;
				@media screen and (max-width: $screen-md-min) {
					& {
						font-size: 1.375rem;
						margin-bottom: 2.8125rem;
					}
				}
			}
			&:after{
				content: "";
				position: absolute;
				width: 0;
				height: 0;
				margin-left: -0.5em;
				bottom: -1.875rem;
				left: 50%;
				border: 1em solid black;
				border-color: transparent transparent #fff #fff;
				transform-origin: 0 0;
				transform: rotate(-45deg);
				box-shadow: -3px 3px 3px 0 rgba(0, 0, 0, 0.4);
				@media screen and (max-width: $screen-md-min) {
					& {
						bottom: -43px;
					}
				}
			}
		}
		&__resources {
			& {
				//background: $learner-dark;
			}
			&--search-results {
				& {
					background: transparent;
				}
			}
		}
		&__resource {
			& {
				position: relative;
			}
			h3 {
				margin: 1rem 0 0.8125rem 0.625rem;
				font-size: 1.25rem;
				@media screen and (max-width: $screen-md-min) {
					font-size: 1.75rem;
				}
				@media screen and (max-width: $screen-xs) {
					font-size: 1.25rem;
				}

				span {
					background: #223;
				    color: white;
				    padding: 0.3125rem;
				}

				i {
					color: #ddd;
					font-size: 1rem;
				}

				.light-theme & {
					color: #212121;
				}
			}
			&--search-result {
				h3 {
					color: inherit;
				}
			}
			> div {
				position: relative;
				height: 10.625rem;
				overflow: hidden;
				@media screen and (max-width: $screen-md-min) {
					height: 15.3125rem;
				}
				@media screen and (max-width: $screen-xs) {
					height: 7.8125rem;
				}
				&.expanded {
					height: auto;
					overflow: visible;
					ul {
						position: relative;
						white-space: normal;
						overflow: visible;
						left: 0 !important;
						transition: all 0ms ease-in-out;
						li {
							margin-bottom: 1.25rem;
						}
					}
				}
			}
			ul {
				& {
					padding: 0;
					margin: 0;
					list-style: none;
					white-space: nowrap;
					position: absolute;
					top: 0;
					left: 0;
					right: 0;
					bottom: -1.25rem;
					overflow: auto;
					transition: all 450ms ease-in-out;
				}
				li {
					display: inline-block;
					vertical-align: bottom;
					margin: 0 0.625rem;
					position: relative;
					&.smcr {
						margin: 0.625rem 0.625rem 0;
					}
					a {
						& {
							cursor: pointer;
							display: block;
							height: 9.375rem;
							transition: all 150ms ease-in-out;
							position: relative;
							@media screen and (max-width: $screen-md-min) {
								height: 14rem;
							}
							@media screen and (max-width: $screen-xs) {
								height: 7rem;
							}
						}
						&:focus {
							outline: 0.25rem solid #ff0065;
							outline-offset: -2px;
						}
						&.thumbnail-redesign {
							width: 16.5625rem;
							background: #fff;
							border: solid 1px #b7b7b7;
							position: relative;
							img {
								position: absolute;
								top: 0;
								right: 0.625rem;
								height: auto;
								max-width: 55%;
								min-width: 0;
								min-height: 0;
								bottom: 0;
								margin: auto;
								max-height: 8.625rem;
							}
							.resource-title {
								div {
									background: 0 0;
									color: #212121;
									box-shadow: none;
									font-weight: 400;
									text-transform: uppercase;
									font-size: 0.75rem;
									letter-spacing: initial;
									max-width: 41%;
									padding: 0 0.25rem 0 0.375rem;
									-webkit-line-clamp: 4;
									display: -webkit-box;
									overflow: hidden;
									text-overflow: ellipsis;
									-webkit-box-orient: vertical;
									white-space: normal;
									@media screen and (max-width: $screen-xs) {
										-webkit-line-clamp: 3;
									}
								}
							}
						}
						@include resource-check();
						img {
							height: 100%;
							min-width: 15.375rem;
							min-height: 9.375rem;

							@media screen and (max-width: $screen-md-min) {
								min-width: 23rem;
								min-height: 14rem;
							}

							@media screen and (max-width: $screen-xs) {
								width: 11.5625rem;
								height: 7rem;
								min-width: auto;
								min-height: auto;
							}

							&.img-blur {
								filter: blur(0.625rem);
							}
						}
					}
					&.active {
						a {
							border-radius: 3px;
							outline: 0.25rem solid red;
							outline-offset: -3px;
						}
						a:focus {
							outline: 0.3125rem solid yellow;
							outline-offset: -0.3125rem;
						}
					}
				}
			}
			&.latest_releases {
				> div {
					height: 29.375rem;
				}
				ul {
					li {
						a {
							& {
								height: 28.125rem;
							}
							img {
								min-height: 28.125rem;
							}
						}
					}
				}
			}
		}
		&__active-action {
			padding: 0 0 1.25rem;
		}
		&-upload {
			padding: 1.25rem;
		}
	}
	&__gauge {
		& {
			min-width: 3.75rem;
		}
		> div {
			color: #fff;
		}
		&:hover,
		&.visible {
			> div {
				background: #313131;
			}
			.dropdown-menu {
				display: block;
				top: 3.25rem;
				left: -4.25rem;
				background: rgba(0,0,0,.77);
				border: none;
				right: auto;
				max-width: 18.125rem;
				overflow: hidden;
				border-radius: 0 0 0.3125rem 0.3125rem;
				z-index: 1;
				margin-top: 0;
				a {
					color: #fff;
					transition: all 150ms ease-in-out;
					padding: 0.625rem 1rem;
					text-overflow: ellipsis;
					overflow: hidden;
					font-size: 0.75rem;
					text-align: left;
					&:hover,
					&:focus {
						background-color: #212121;
					}
					&.selected {
						text-decoration: underline;
					}
				}
			}
		}
	}
}

.resource {
	// resource sign-off for non-automated types
	&-sign-off {
		& {
			font-size: 2.5rem;
			cursor: pointer;
		}
		&--no-click {
			cursor: default;
		}
		&.fa-check-square-o {
			color: #26b99a;
		}
	}
	// title over resource thumbnails
	&-title {
		& {
			position: absolute;
			left: 0;
			right: 0;
			bottom: 0;
			top: 0;
		}
		div {
			background-color: rgba(0,0,0,.9);
			color: #fff;
			padding: 0.625rem;
			white-space: normal;
			box-shadow: 0 0 1px 1px rgba(0,0,0,0.3);
			text-align: center;
			font-family: 'Roboto', sans-serif;
			font-weight: 800;
			font-size: 1.125rem;
			letter-spacing: 1px;
			max-height: 9.375rem;
			overflow: hidden;

			@media screen and (max-width: $screen-md-min) {
				max-height: 14.0625rem;
				font-size: 1.875rem;
				line-height: 1;
			}
			@media screen and (max-width: $screen-xs) {
				font-size: 1.3125rem;
				max-height: 7.8125rem;
			}
		}
	}
}
.tooltip {
	&.white {
		&-right {
			&--static {
				right: -7.6875rem;
				top: 1rem !important;
				visibility: visible !important;
				white-space: nowrap;
				left: auto !important;
				&.smcr {
					top: 0.375rem !important;
				}
			}
		}
		&-right,
		&-bottom {
			.tooltip-inner {
				color: #444;
				background-color: #fff;
			}
			.tooltip-arrow {
				border-top-color: #fff;
			}
		}
		&-right {
			.tooltip-arrow {
				border-top-color: transparent;
				border-right-color: #fff;
			}
		}
	}
}
.modal-open {
	padding-right: 0px !important;
}

.lms {
	&-header--learner {
		& {
			//background: $learner-dark;
			border-bottom: none;
		}
		.lms-header__profile, .lms-header__learnerprofile {
			.user-profile {
				color: #fff;
			}
			@media screen and (max-width: $screen-md-min) {
				font-size: 1.2em; //40px;
			}
			&:hover {
				background: #313131;
			}
			.dropdown-menu.dropdown-usermenu {
				& {
					background: rgba(0, 0, 0, 1); //, 0.77);
					border: none;
				}
				a {
					& {
						color: #fff;
						transition: all 150ms ease-in-out;
					}
					&:hover {
						background-color: $learner-dark;
					}
					&:focus {
						background-color: $brand-primary;
						color: #fff;
					}
					@media screen and (max-width: $screen-md-min) {
						font-size: 1em;
					}
				}
			}
			a .fa-bars {
				margin: 0rem 1.875rem 0rem 1.5625rem;
				font-size: 2.625rem;
			}
		}
	}
	&-content {
		&--learner {
			/*
				Mostly generic overwrites
			*/
			& {
				padding: 0;
				//	background-color: $learner-dark;
				color: #fff;
				.light-theme & {
					color: #212121;
				}
			}
			.has-success .control-label {
    			color: #74dc76;
			}
			.x_panel {
				background: rgba(255, 255, 255, 0.08);
				border: none;
			}
			.x_title {
				border-bottom: 0.125rem solid rgba(230, 233, 237, 0.32);
			}
			.table-striped>tbody>tr:nth-of-type(odd) {
				background-color: rgba(249, 249, 249, 0.08);
			}
			.table>tbody>tr>td, .table>tbody>tr>th, .table>tfoot>tr>td, .table>tfoot>tr>th, .table>thead>tr>td, .table>thead>tr>th {
				border-top: 0.0625rem solid rgba(221, 221, 221, 0.24);
			}
			.table>thead>tr>th {
				border-bottom: 0.125rem solid rgba(221, 221, 221, 0.22);
			}
			.ln_solid {
				border-top: 0.0625rem solid rgba(229, 229, 229, 0.52);
				background-color: rgb(0, 0, 0);
			}

			a {
				// color: var(--calendar-event-link-color);
				// .light-theme & {
				// 	color: #212121;
				// }
			}
			h4 small {
				color: var(--calendar-event-link-color);
				.light-theme & {
					color: var(--calendar-event-link-color);
				}
			}

			/* button overwrites for learner inteface, darkened */
			.btn {
				&-default {
					& {
						color: #ffffff;
						background-color: #464545;
						border-color: #464545;
					}

					&.light {
						color: #333;
						background-color: #fff;
						border-color: #ccc;
					}

					&:focus,
					&.focus,
					&:hover,
					&:active,
					&.active {
						color: #ffffff;
						background-color: #2c2c2c;
						border-color: #272727;
						&.light {
							color: #333;
							background-color: #e6e6e6;
							border-color: #adadad;
						}

					}
					&:active:focus {
						color: #ffffff;
						background-color: #1a1a1a;
						border-color: #060606;
						&.light {
							color: #333;
							background-color: #d4d4d4;
							border-color: #8c8c8c;
						}
					}

					&.bottom {
						&--green {
							border-bottom: solid 0.25rem #26b99a;
							span.fa {
								color: #26b99a;
							}
						}
						&--white {
							border-bottom: solid 0.25rem #fff;
							span.fa {
								color: #fff;
							}
						}
						&--orange {
							border-bottom: solid 0.25rem #f0ad4e;
							span.fa {
								color: #f0ad4e;
							}
						}
						&--red {
							border-bottom: solid 0.25rem #c9302c;
							span.fa {
								color: #c9302c;
							}
						}
						&--info {
							border-bottom: solid 0.25rem #3498db;
							span.fa {
								color: #3498db;
							}
						}
						&--yellow {
							border-bottom: solid 0.25rem #b6b901;
							span.fa {
								color: #b6b901;
							}
						}
					}
				}

				&-primary {
					& {
						color: #ffffff;
						background-color: #375a7f;
						border-color: #375a7f;
					}
					&:focus,
					&.focus,
					&:hover,
					&:active,
					&.active {
						color: #ffffff;
						background-color: #28415b;
						border-color: #253c54;
					}
					&:active:focus {
						color: #ffffff;
						background-color: #1d2f43;
						border-color: #101b26;
					}
				}

				&-info {
					& {
						color: #ffffff;
						background-color: #217cb9; //#3498db;
						border-color: #3498db;
					}
					&:focus,
					&.focus,
					&:hover,
					&:active,
					&.active {
						color: #ffffff;
						background-color: #1c6a9f; //#217dbb;
						border-color: #2077b2;
					}
					&:active:focus {
						color: #ffffff;
						background-color: #103e5c; //#1c699d;
						border-color: #16527a;
					}
				}

				&-launch {
					& {
						background: #217cb9; //#2578CF;
						border: solid 1px #226aa7;
						color: #fff;
					}
					@media screen and (max-width: $screen-md-min) {
						& {
							margin-top: 1.25rem;
							font-size: 1.75rem;
						}
					}
					@media screen and (max-width: $screen-xs) {
						font-size: medium;
					}
					&:hover,
					&:focus {
						color: #fff;
						background: #226aa7;
					}
				}

				&-smcr {
					& {
						background: #ff7800;
						border: solid 1px #db6700;
						color: #fff;
					}
					&:hover,
					&:focus {
						color: #fff;
						background: #db6700;
					}
				}

				&-green {
					& {
						background: #26b99a;
						border: solid 1px #1c8c75;
						color: #fff;
					}
					&:hover,
					&:focus {
						color: #fff;
						background: #1c8c75;
					}
				}
				&-orange {
					& {
						background: #f0ad4e;
						border: solid 1px #cc913d;
						color: #fff;
					}
					&:hover,
					&:focus {
						color: #fff;
						background: #cc913d;
					}
				}
				&-yellow {
					& {
						background: #b6b901;
						border: solid 1px #9b9d00;
						color: #fff;
					}
					&:hover,
					&:focus {
						color: #fff;
						background: #9b9d00;
					}
				}
				&-red {
					& {
						background: #c9302c;
						border: solid 1px #ac2925;
						color: #fff;
					}
					&:hover,
					&:focus {
						color: #fff;
						background: #ac2925;
					}
				}
			}

			/* Light calendar overwrite with darker one*/
			mwl-calendar .cal-row-fluid:hover, mwl-calendar .cal-year-box .row:hover {
				background-color: #0002; //rgba(250, 250, 250, 0.12);
			}
			mwl-calendar .cal-cell.drop-active, mwl-calendar .cal-day-hour-part.drop-active, mwl-calendar .cal-week-box .cal-cell1.drop-active, mwl-calendar .cell-focus, mwl-calendar [class*=cal-cell] .drop-active, mwl-calendar [class*=cal-cell]:hover {
				background-color: #0002; //rgba(237, 237, 237, 0.22);
			}
			mwl-calendar .cal-day-tick {
				border: 1px solid var(--calendar-today-bg);
				background-color: var(--calendar-background-color);
			}
			mwl-calendar span[data-cal-date] {
				opacity: 0.85;
			}
			mwl-calendar .cal-day-outmonth span[data-cal-date] {
				opacity: .75;
			}
			mwl-calendar .cal-day-weekend span[data-cal-date] {
				color: var(--calendar-accent-color);
				font-weight: bold;
			}
			mwl-calendar .cal-day-today {
				background: var(--calendar-today-bg);
			}
			mwl-calendar .cal-day-today span[data-cal-date] {
				color: var(--calendar-today-text);
				opacity: 1;
			}
			.cal-day-box .cal-day-hour {
				background-color: rgba(255, 255, 255, 0.09);
			}
			.cal-day-box .cal-day-hour:nth-child(odd) {
				background-color: rgba(250, 250, 250, 0);
			}
			.cal-day-box .cal-day-hour:hover {
				background-color: #0002; //rgba(237, 237, 237, 0.3);
			}
			mwl-calendar .cal-slide-content {
				background-color: rgba(85, 85, 85, 0.28);
			}

			.has-error .help-block {
				background: rgb(242, 222, 222);
				padding: 3px 0.625rem;
				border-radius: 0.25rem;
				border: solid 1px #a94442;
				opacity: 0.9;
			}
			.list-group-item {
				background: #4e4e4e;
				.light-theme & {
					background: #dedede;
				}
			}
		}
	}
}
/* directional arrows for horizontal module list in learner interface*/
.direction {
	&-arrow {
		& {
			position: absolute;
			top: 0;
			z-index: 1;
			font-size: 3.75rem;
			bottom: 1.25rem;
			line-height: 9.5rem;
			cursor: pointer;
			padding: 0 0.625rem;
			transition: all 150ms ease-in-out;
			opacity: 0;
			pointer-events: none;
			background: rgba(0, 0, 0, 0.2);
			//max-height: 200px;
			@media screen and (max-width: $screen-md-min) {
				line-height: 14.1875rem;
			}
			@media screen and (max-width: $screen-xs) {
				line-height: 7rem;
				height: 7rem;
			}
		}
		&.visible {
			pointer-events: auto;
			opacity: 1;
		}
		&:hover {
			background: rgba(0, 0, 0, 0.5);
		}
		&--left {
			left: 0px;

		}
		&--right {
			right: 0px;
		}
	}
}
.latest_releases {
	.direction {
		&-arrow {
			line-height: 28.25rem;
		}
	}
}

/* Resource upload specific styles */
.resource {
	&-upload {
		&-form {
			> div {
				padding: 3px 1.25rem;
			}
		}
		&-box {
			& {
				//background: rgba(255, 255, 255, 0.07);
				//border: solid 1px rgba(255, 255, 255, 0.2);
				background: rgba(0, 0, 0, 0.07);
				border: solid 1px rgba(0, 0, 0, 0.2);
				border-radius: 1.25rem;
				margin: 1.25rem 0;
				padding: 1.25rem;
				min-height: 6.25rem;
				text-align: center;
				font-size: 1.25rem;
				position: relative;
			}
			&--admin {
				& {
					background: rgba(0, 0, 0, 0.07);
					border: solid 1px rgba(0, 0, 0, 0.2);
					.lms-learner & {
						background: rgba(255,255,255,0.3);
					}
				}
			}
			&.dragover {
				border: 5px dashed blue;
			}
		}
		&__file-list {
			& {
				text-align: left;
				margin-top: 0.625rem;
			}
			li {
				& {
					font-size: 1rem;
					position: relative;
					margin: 1px 0;
				}
				.fa-times {
					font-size: 1.25rem;
					color: #c9302c;
					cursor: pointer;
					position: relative;
					z-index: 1;
				}
				.name {
					& {
						position: relative;
						z-index: 2;
						padding: 2px 0.3125rem;
					}
					&--admin {
						color: #6e6e6e;
					}
				}
				.progress {
					& {
						position: absolute;
						top: 0;
						left: 0;
						bottom:0;
						width: 0%;
						background: #449d44;
						opacity: 0.3;
						transition: all 150ms ease-in-out;
						z-index: 1;
						height: initial;
						margin: 0;

					}
					&.complete {
						& {
							background: #449d44;
							opacity: 1;
						}
						&--admin {
							background: rgba(68, 157, 68, 0.4);
						}
					}
				}
			}
		}
	}
}
/* EOF Resource upload specific styles */


/* Trainee progress */
.trainee {
	&-progress {
		&__sidebar {
			& {
				color: #fff;
				width: 18.75rem;
				padding: 0.625rem 0.3125rem;
				overflow-y: auto;
				overflow-x: hidden;
				&.closed {
					width: 0px;
					overflow: hidden;
				}
			}
			.light-theme & {
				color: #212121 !important;
			}
			.sidebar {
				&__standard {
					ul {
						& {
							list-style-type: none;
							padding-left: 0.75rem;
						}
						li {
							input {
								align-self: baseline;
								margin-top: 3px;
							}
							label {
								& {
									cursor: pointer;
									padding-right: 0.625rem;
									//text-shadow: 1px 1px 1px #000;
									font-weight: 100;
									overflow: hidden;
									text-overflow: ellipsis;
									white-space: nowrap;
									&:hover{
										white-space: normal;
									}
								}
								&.green {
									color: #5cb85c;
								}
								&.orange {
									color: #ec971f;
								}
								&.red {
									color: #c9302c;
								}
							}
						}
					}
				}
			}
		}
		&__show-hide {
			position: absolute;
			top: -3.3125rem;
			left: 14.375rem;
			z-index: 1;
			padding: 2px 6px 0px 0;
			margin: 0;
			box-shadow: 0px 0px 1px #fff;
			&.left-side {
				left: 1.25rem;
			}
		}
	}
}
/* EOF Trainee progress */


/* Upload evidence, edit evidence, eveidence related. */
.attached-evidence {
	& {

	}
	&__entry {
		& {
			padding: 3px;
		}
		&:nth-child(even) {
			& {
				background: rgba(0, 0, 0, 0.05);
			}
			&.admin-entry {
				background: rgba(0, 0, 0, 0.1);
			}

		}
	}
	&__list {
		& {
			background: rgba(255, 255, 255, 0.3);
			padding: 0.625rem;
		}
		h2 {
			margin-top: 0;
		}

	}
	&__name {
		padding-right: 0.625rem;
	}
}
.visit-learning-resource {
	width: 100%;
}
.evidence {
	&-name {
	}
	&-thumbnail,
	&-promo_image {
		max-height: 9.375rem;
	}
	&-close {
		position: absolute;
		top: 0.9375rem;
		right: 1.25rem;
	}
	&--edit {
		& {
			position: fixed;
			z-index: 2;
			background: rgba(0, 0, 0, 0.6);
			top:0;
			left:0;
			right: 0;
			bottom: 0;
			padding-top: 6.25rem;
		}
		form {
			background: rgba(0, 0, 0, 0.6);
			padding: 3.75rem 1.25rem 1.25rem;
			max-width: 62.5rem;
			margin: auto;
			position: relative;
		}
	}
}
.match-to {
	&-issue {
		& {
			padding: 0.3125rem 0.625rem;
			cursor: pointer;
			transition: all 250ms ease-in-out;
		}
		&:hover {
			background: #2f2f2f;
		}
		&:nth-child(odd) {
			& {
				background: #404040;
			}
			&:hover {
				background: #525252
			}

		}
		.u-squared-checkbox {
			& {
				float: right;
				min-width: 1.5625rem;
			}
			label {
				left: 0;
			}
		}

	}
	&-category {
		& {
			transition: all 250ms ease-in-out;
		}
		&:hover {
			background: #000;
		}
	}
}

/*
Change to use inline html for launching scorm resource
*/
.popup-resources {
	& {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		min-height: 0px;
		//min-width: 1024px;
		background: rgba(0, 0, 0, 0.9);
		z-index: 10000;
		transform: scale(1) translateY(-100vh);
		transition: transform 400ms ease-in-out;
		//pointer-events: none;
		overflow: hidden;
	}
	&.show {
		transform: scale(1) translateX(0vh);
		//pointer-events: initial;
		overflow: initial;
		//min-height: 630px;
	}
	iframe {
		& {
			height: 40.625rem;
			width: 66.875rem;
			display: block;
		}
		&.full-screen {
			position: fixed !important; /* Or absolute, depending on context */
			top: 0 !important;
			left: 0 !important;
			width: 100vw !important;
			height: 100vh !important;
			border: none !important;
			z-index: 9999 !important; /* Ensure it's on top */
		}
		&.youtube {
			width: 100%;
			height: 100%;
			max-width: 165vh;
			max-height: 100vh;
		//	pointer-events: none;
		}
		&.vimeo,
		&.moodle {
			width: 100%;
			height: 100%;
			max-width: 165vh;
			max-height: 100vh;
			//	pointer-events: none;
		}
		&.h5p {
			width: 100%;
			height: 100%;
			max-width: 165vh;
			max-height: 100vh;
			//	pointer-events: none;
		}
		&.scorm {
			//max-width: 164vh; // oh what is this????
			//position: absolute;
			//top: 50%;
			//left: 50%;
			//margin: -315px 0 0 -512px;
		}
		&.turnitin {
			width: 100%;
			height: 100%;
			max-width: 165vh;
			max-height: 100vh;
			background-color: white
		}
	}

	&__close {
		cursor: pointer;
		position: absolute;
		top: 0.625rem;
		right: 0.625rem;
		font-size: 1.25rem;
		background: #5f5f5f;
		padding: 0.3125rem 0.625rem;
		z-index: 1;
		width:6.25rem;
	}
	&__start {
		cursor: pointer;
		position: absolute;
		top: 3.125rem;
		right: 0.625rem;
		font-size: 1.25rem;
		background: #5f5f5f;
		padding: 0.3125rem 0.625rem;
		z-index: 1;
		width:6.25rem;
	}
	&__pause {
		cursor: pointer;
		position: absolute;
		top: 3.125rem;
		right: 0.625rem;
		font-size: 1.25rem;
		background: #5f5f5f;
		padding: 0.3125rem 0.625rem;
		z-index: 1;
		width:6.25rem;
	}
	&__wrap {
		width: 100%;
		height: 100%;
		position: relative;
		//align-items: center;
		padding: 0.625rem;
	}
	&__print {
		cursor: pointer;
		position: absolute;
		top: 0.625rem;
		left: 0.625rem;
		font-size: 1.25rem;
		background: #5f5f5f;
		padding: 0.3125rem 0.625rem;
		z-index: 1;
		width:6.25rem;
	}
}

/* Trainee interface - evidence - meetings */

// need to revert calendar overwrites.
.learner-resource__meetings {
	.uib-datepicker-popup {
		& {
			color: #73879c;
		}
		.btn-default {
			color: #333;
			background-color: #fff;
			border-color: #ccc;
		}
		a {
			color: #337ab7;
		}
	}
	&-list {
		.meeting {
			& {
				background: #222;
				margin: 0.3125rem;
				padding: 0.3125rem 0.625rem;
				position: relative;
				.light-theme & {
					background: #e8e8e8;
				}
			}
			h3 {
				margin:  0;
			}
			hr {
				margin-top: 0.625rem;
				margin-bottom: 0.625rem;
			}
			&-time {
				font-size: 1rem;
			}
			&-status {

			}
			&-delete,
			&-approve {
				display: none;
			}
			&:hover {
				.meeting-delete,
				.meeting-approve {
					display: block;
					position: absolute;
					top: 0.3125rem;
					right: 0.3125rem;
				}
			}

			&:nth-child(odd) {
				background: #464646;
			}
		}
	}
}

/* EOF Trainee interface - evidence - meetings*/

/*
	Ratings, hearts
*/
.rating {
	&-heart {
		max-width: 1.5625rem;
		cursor: pointer;
		color: #EE3129;
		margin: 0 3px;
		&--small {
			max-width: 1.25rem;
			cursor: initial;
		}
	}
}

.jackdaw-licence {
	position: absolute;
	top: 1.25rem;
	z-index: 1;
	left: 105%;
	color: #fff;
	background: #5d5d5d;
	border-radius: 0.3125rem;
	padding: 0.3125rem 0.625rem;
	white-space: nowrap;
	line-height: 0.8125rem;
	font-size: 0.75rem;
	a {
		color: #fff;
		text-decoration: underline;
	}
}

/*
	Upload evidence/log/blog
*/
.upload {
	.actions {
		& {
			font-size: 0;
			color: #434343;
			border-top: solid 1px #CED6E0;
			padding: 1px 0 0;
			margin: 0 0 0.625rem;
		}
		&-item {
			& {
				display: inline-block;
				font-size: 0.875rem;
				position: relative;
				background: #c6c6c6;
				cursor: pointer;
				height: 6.875rem;
				flex-grow: 1;
			}
			@include m-state-arrow; // mixins.scss
			&__info {
				& {
					position: absolute;
					top: 14%;
					left: 10.3125rem;
					pointer-events: none;
					right: 0.625rem;
					z-index: 2;
					color: #434343;
					transition: 0.3s ease-in-out all;
					@media (max-width: 1570px) {
						//left: 135px;
					}
					@media (max-width: 1345px) {
						//left: 110px;
					}
					@media (max-width: 1000px) {
						left: 1.875rem;
					}
				}
				strong {
					display: block;
					font-size: 1rem;
					padding-bottom: 0.3125rem;
					line-height: initial;
				}
				small {
					display: block;
					font-size: 0.6875rem;
					line-height: initial;
					@media (max-width: 100px) {
						display: none;
					}
				}
			}
			img {
				position: absolute;
				bottom: 0;
				max-height: 100%;
				left: 0;
				image-rendering: -o-crisp-edges;
				image-rendering: -webkit-optimize-contrast;
				transition: 0.3s ease-in-out all;
				@media (max-width: 1570px) {
					//max-height: 75%;
				}
				@media (max-width: 1345px) {
					//max-height: 60%;
				}
				@media (max-width: 1000px) {
					opacity: 0.2;
				}
			}

			&--active {
				& {
					background: #f8ac93;
				}
			}

			&--first {
				img {
					margin-left: -1.25rem;
				}
			}
			&--middle {
				img {
					margin-left: 1.25rem;
				}
			}
			&--last {
				img {
					margin-left: 1.875rem;
				}
			}


			&__hover-effect {

			}
		}
	}
}

.resources-filter {
	width: 18.75rem;
}

.x_panel {
	.row {
		@media screen and (max-width: $screen-md) {
			flex-wrap: wrap;
			max-width: 100vw;
			//&:hover {
				//background: #eee;
			//}
			//width: 100vw !important;
			//&::after {
				//content: "";
			    //width: 100vw;
			    //margin-top: 6px;
			    //border-bottom: 1px solid #ccc;
			//}
		}
	}
}

.rank-table {
	@media (max-width: $screen-sm) {
		font-size: 0.5rem;
	}
}

html.lms-learner {
	background: #212121 no-repeat center center;
	background-image: var(--bg-image);
	background-size: cover;
	background-attachment: fixed;
	@media screen and (max-width: $screen-md-min) {
		padding-top: 0px;
	}
	padding-top: $header-height; //57px;
	.lms {
		&-header {
			position: fixed;
			top: 0;
			left: 0;
			right: 0;
			z-index: 3;
			background: #212121; // #333333
			align-items: center; //flex-start;

			@media screen and (max-width: $screen-md-min) {
				flex-wrap: nowrap;
				position: sticky;
				//align-items: center;
			}
			@media screen and (max-width: $screen-sm) {
				position: relative;
			}

			&__logo {
				align-self: center;
			}

			&__search {
				//margin-top: 10px;
				align-self: center;
				@media screen and (max-width: $screen-md-min) {
					margin-top: 0px;
					max-width: 22.5rem;
					.dropdown-menu,
					button,
					input {
						font-size: 1rem;
						@media (max-width: $screen-md-min) {
							font-size: 2.4rem;
						}
					}
				}
			}
			ol {
			    list-style-type: none;
			    margin-block-start: 0px;
			    margin-block-end: 0px;
			    margin-inline-start: 0px;
			    margin-inline-end: 0px;
			    padding-inline-start: 0px;
			    line-height: 1;

			    @media screen and (max-width: $screen-md-min) {
					justify-content: flex-end;
				}

			    @media screen and (max-width: $screen-xs) {
					justify-content: flex-start;
				}

			}
		}
	}

	.uib-datepicker-popup.dropdown-menu {
		background: #666;
	}

	.mobile-menu-label {
		color: #fff;
		padding: 0.9375rem 0.9375rem 0.625rem 0.9375rem;
		font-weight: 700;
		border-bottom: solid 1px rgba(255,255,255,.85);
		background-color: #337ab7;
	}

	.mobile-search-line {
		align-items: center;
		justify-content: center;
		padding: 1.25rem 1.875rem 1.25rem 1.875rem;
		form {
			max-width: 90vw;
			flex-grow: 1;
		}
	}
	.learning-duelabel-container {
		width: 100%;
		padding: 0 0.375rem;
		position: absolute;
		top: 0;
		left: 0;
		font-weight: 600;
		display: flex;
		align-items: center;
		justify-content: space-between;
		@media screen and (max-width: $screen-sm)
		{
			img {
				height: 0.8rem;
			}
		}
	}

	.event-item-header {
		&--enrolled {
			background: #292929;
		}
		&--enrollable {
			background: #525252;
		}
		&--waiting {
			background: #592929;
		}
	}

	// Calendar event overrides for waiting list text color
	// Using CSS variables with !important to override inline ng-style
	.day-highlight {
		// Target waiting list events by background color
		&[style*="background-color: rgb(89, 41, 41)"], 
		&[style*="background-color:#592929"] {
			// Override text color for all text elements
			a, a:hover, a:focus, strong {
				color: var(--calendar-waiting-list-text, #fff) !important;
			}
		}
	}

	.learning-duelabel-container img{
		min-height: auto;
		min-width: auto;
		width: 2.5rem;
	}

	.modules-scroll-mobile {
		@media (max-width: $screen-sm) {
			overflow-y: auto;
    		height: 100%;
		}
	}
}
//lms-learner
html.light-theme.lms-learner {
	background: #fff no-repeat center center;
	background-image: var(--bg-image);
	background-size: cover;
	background-attachment: fixed;
	color: #212121;

	.lms-header {
		background: #fff;
		color: #212121;
		.learner__gauge > div,
		.user-profile,
		.learner__top-text-menu a,
		.learner__top-icon a {
			color: #212121;
		}
		.learner__gauge {
			&:hover,
			&.visible {
				& > div,
				.dropdown-menu {
					background: #fff;
				a {
					color: #212121;

					&:hover,
					&:focus {
						background-color: #cacaca;
					}
					&.selected {
						text-decoration: underline;
					}
				}
				}
			}
		}
		.lms-header__profile {
			&:hover {
				background: #fff;
			}
		}
		.dropdown-menu.dropdown-usermenu {
			background: #fff;
			color: #212121aa;
			a {
				& {
					color: #212121;
				}
				&:hover {
					background-color: #cacaca;
				}
			}
		}
	}
}

.resp-table-container
{
	@media screen and (max-width: $screen-sm)
	{
		padding: 0;
	}
}

.name-search-header {
	display: none;
	@media screen and (max-width: $screen-sm) {
		.name-search-header {
			display: block;
			padding: 0;
			tr {
				padding: 0 0.5rem;
			}
			th {
				padding: 0;
				border: none;
				display: block;
			}
		}
	}
}

.resp-table
{
	.name-search-header {
		display: none;
	}
	@media screen and (max-width: $screen-sm)
	{
		font-size: .6rem;

		thead {
			display: none;
		}
		th {
			display: none;
		}
		.name-search-header {
			display: block;
			padding: 0;
			tr {
				padding: 0 0.5rem;
			}
			th {
				padding: 0;
				border: none;
				display: block;
			}
		}
		tr {
			display: block;
			padding: 1rem;
		}
		tr:nth-of-type(2n) {
			background: #ebeef0;
		}
		td {
			text-align: left;
			padding: 0.2rem;
			border-top: unset;
			display:block;
		}
		// td:first-child {
		// 	padding-top: 1rem;
		// }
		// td:last-child {
		// 	padding-bottom: 1rem;
		// }
		td::before {
			content: attr(data-cell) ": ";
			text-transform: capitalize;
			font-weight: 800;
		}
		td:empty {
			display: none;
		}
		tfoot td::before
		{
			content: none;
		}
	}
}

.assignments-block {
	.tab-btn-x {
		padding: 0;
		width: 100%;
		border-radius: 0;
		padding: 1rem;
		&:focus, &:focus-visible, &:focus-within {
			outline: none;
			box-shadow: none;
		}
	}
	table {
		&.events-listing-table, &.learning-list-table {
			tbody, thead {
				th {
					text-align: left;
				}

				td:not(.nothing-to-show) {
					text-align: left;
				}
			}

		}
	}
}

.light-theme.lms-learner {
	.learner__top-icon {
		a {
			div {
				color: #222 !important;
			}
			img {
				filter: brightness(0.2);
			}
		}
		.active {
			div {
				color: #fff !important;
			}
			img {
				filter: brightness(1);
			}
		}
	}
}