
.chat-launcher {
  position: fixed;
  bottom: 25px;
  right: 25px;
  background-color: #43a047;
  color: white;
  width: 60px;
  height: 60px;
  font-size: 26px;
  border-radius: 50%;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  z-index: 9999;
}

.chat-panel {
  width: 380px;
  height: 100vh;
  position: fixed;
  right: 0;
  top: 0;
  background: #ffffff;
  box-shadow: -3px 0 10px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  font-family: "Segoe UI", sans-serif;
  border-left: 2px solid #c8e6c9;
  z-index: 9998; /* right under launcher */
}

.chat-close {
  position: absolute;
  top: 10px;
  right: 15px;
  font-size: 18px;
  color: #888;
  cursor: pointer;
}

.chat-close:hover {
  color: #000;
}



.chat-header {
  background: #4caf50;
  padding: 15px;
  border-bottom: 1px solid #ccc;
}

.chat-header h4 {
  margin: 0 0 5px;
  color: #2e7d32;
}

.chat-body {
  flex-grow: 1;
  padding: 10px;
  overflow-y: auto;
  background: #f9f9f9;
}

.chat-user .chat-message {
  background: #d4edda;
  color: #155724;
  // text-align: right;
  float: right;
  border-radius: 15px 0 15px 15px;
  margin: 5px 0;
  padding: 8px 12px;
  max-width: 80%;
  clear: both;
}

.chat-bot .chat-message {
  background: #f1f1f1;
  color: #333;
  float: left;
  border-radius: 0 15px 15px 15px;
  margin: 5px 0;
  padding: 8px 12px;
  max-width: 80%;
  clear: both;
}

.chat-bot-error .chat-message {
  background: #e50707;
  color: #333;
  float: left;
  border-radius: 0 15px 15px 15px;
  margin: 5px 0;
  padding: 8px 12px;
  max-width: 80%;
  clear: both;
}


.chat-footer {
  display: flex;
  padding: 10px;
  border-top: 1px solid #ccc;
  background: #fff;
}

.chat-footer input {
  flex: 1;
  padding: 10px;
  border-radius: 20px;
  border: 1px solid #ccc;
}

.chat-footer button {
  margin-left: 10px;
  background: none;
  border: none;
  color: #4caf50;
  font-size: 20px;
  cursor: pointer;
}

.suggested-questions {
  padding: 10px;
  background: #fff;
}

.suggested-questions {
  padding: 15px 15px 5px;
  background: #fff;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.suggested-questions button {
  background: #ffffff;
  border: 2px solid #4caf50;
  color: #333;
  font-weight: 500;
  padding: 5px 10px;
  border-radius: 8px;
  text-align: left;
  width: 100%;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
  cursor: pointer;
  transition: background 0.2s;
}

.suggested-questions button:hover {
  background: #f1f8e9;
}

.typing {
  font-style: italic;
  font-size: 13px;
  color: #999;
  margin-top: 10px;
}
.text-black {
    color: black;
}

.intro-bubbles {
  padding: 10px 15px 0;
  background: #fff;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.intro-bubbles .bubble {
  background: #f1f1f1;
  color: #333;
  padding: 8px 12px;
  border-radius: 15px;
  display: inline-block;
  width: fit-content;
  max-width: 90%;
  font-size: 14px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

.typing {
  display: flex;
  justify-content: flex-start;
  gap: 5px;
  padding: 10px 0 0 5px;
  clear: both;
}

.typing .dot {
  height: 8px;
  width: 8px;
  background-color: #bbb;
  border-radius: 50%;
  animation: blink 1.4s infinite both;
}

.typing .dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing .dot:nth-child(3) {
  animation-delay: 0.4s;
}

.chat-footer input:disabled {
  background-color: #f0f0f0;
  color: #999;
  cursor: not-allowed;
  opacity: 0.6;
}

.chat-footer button:disabled {
  color: #666;
  cursor: not-allowed;
  opacity: 0.6;
}

// .chat-footer button:disabled i {
//   color: #999;
// }

@keyframes blink {
  0%, 80%, 100% { opacity: 0; }
  40% { opacity: 1; }
}

// Chat controls styling
.chat-controls {
  margin-top: 10px;
  text-align: center;
}

.chat-control-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background: rgba(255, 255, 255, 0.3);
  }
  
  i {
    margin-right: 4px;
  }
}

// Formatted chat message styling
.chat-bot-formatted .chat-message {
  max-width: 90%;
  line-height: 1.5;
  
  .chat-formatted-response {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
  }
  
  .chat-course-title {
    margin: 12px 0 8px 0;
    font-weight: bold;
    color: #2e7d32;
    
    .course-number {
      color: #4caf50;
      font-weight: 600;
    }
  }
  
  .chat-list-item {
    margin: 6px 0;
    padding-left: 8px;
    
    .chat-list-number {
      color: #4caf50;
      font-weight: 600;
      margin-right: 6px;
    }
  }
  
  .chat-thinking {
    color: #666;
    font-style: italic;
    opacity: 0.8;
  }
  
  strong {
    color: #2e7d32;
    font-weight: 600;
  }
  
  em {
    color: #555;
    font-style: italic;
  }
  
  // Better spacing for formatted content
  p {
    margin: 8px 0;
  }
  
  // Style for course categories
  div:contains("Category:") {
    color: #666;
    font-size: 0.9em;
  }
}

@media (max-width: 480px) {
  .chat-panel {
    width: 100%;
    height: 100%;
    border-radius: 0;
  }

  .chat-launcher {
    width: 50px;
    height: 50px;
    font-size: 22px;
  }
}

// below styles for ai search 

html.light-theme {
  --bg-color: #f9f9f9;
  --border-color: #ddd;
  --card-bg: #fff;
  --card-border: #ccc;
  --text-color: #222;
  --accent-color: #0066cc;
}

html.dark-theme {
  --bg-color: #1e1e1e;
  --border-color: #333;
  --card-bg: #2a2a2a;
  --card-border: #444;
  --text-color: #e0e0e0;
  --accent-color: #4aa8ff;
}


.ai-results {
  border-radius: 6px;
  padding: 1rem;
  margin-top: 1rem;
  border: 1px solid var(--border-color);
  background-color: var(--bg-color);
}

.ai-results__title {
  font-weight: 600;
  margin-bottom: 1rem;
  font-size: 1.2rem;
  color: var(--text-color);
}

.ai-result-card {
  background-color: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: 5px;
  margin-bottom: 1rem;
  padding: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.ai-result-card__header {
  font-weight: bold;
  font-size: 1rem;
  margin-bottom: 0.5rem;
  color: var(--accent-color);
}

.ai-result-card__content {
  position: relative;
  font-size: 0.95rem;
  color: var(--text-color);
  line-height: 1.5;
  max-height: 200px;
  overflow: hidden;
  transition: max-height 0.4s ease, padding 0.3s ease;
}

.ai-result-card__content.expanded {
  max-height: none !important;
  overflow: visible !important;
}

.ai-result-card__content::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2rem;
  pointer-events: none;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.ai-result-card__content.expanded::after {
  display: none;
}

.chat-thinking {
  font-style: italic;
  color: gray;
  font-size: 1rem;
}

.chat-dots span {
  animation: blink 1.5s infinite;
  opacity: 0;
  font-weight: bold;
}

.chat-dots span:nth-child(1) { animation-delay: 0s; }
.chat-dots span:nth-child(2) { animation-delay: 0.3s; }
.chat-dots span:nth-child(3) { animation-delay: 0.6s; }

@keyframes blink {
  0%, 100% { opacity: 0; }
  50% { opacity: 1; }
}

/*-------------------------------------
  Show More / Less Button
-------------------------------------*/
.ai-toggle-btn-container {
  text-align: center;
  margin-top: 1rem;
}

.ai-toggle-btn {
  border: 1px solid var(--accent-color);
  background: transparent;
  color: var(--accent-color);
  padding: 0.4rem 1.5rem;
  border-radius: 30px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: background-color 0.3s, color 0.3s;
}

.ai-toggle-btn:hover,
.ai-toggle-btn:focus {
  background-color: var(--accent-color);
  color: #fff;
  outline: none;
}

/*-------------------------------------
  Feedback / Footer Section (optional)
-------------------------------------*/
.ai-result-card__footer {
  border-top: 1px solid var(--card-border);
  margin-top: 1rem;
  padding-top: 0.75rem;
  text-align: center;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

/*-------------------------------------
  Utility: Button Styles
-------------------------------------*/
.ai-results .btn-outline {
  border: 1px solid var(--accent-color);
  background: transparent;
  color: var(--accent-color);
  padding: 0.3rem 1rem;
  border-radius: 4px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: background-color 0.3s, color 0.3s;
}

.ai-results .btn-outline:hover,
.ai-results .btn-outline:focus {
  background-color: var(--accent-color);
  color: #fff;
}

.ai-results .btn-xs {
  padding: 0.25rem 0.6rem;
  font-size: 0.75rem;
}

.ai-results .u-text--center {
  text-align: center;
  margin-top: 1rem;
}