.u {
	&-flexbox {
		& {
			display: flex;
			_:-ms-fullscreen, :root & {
				display: block;  // This will remove flexbox for administration, hassle for IE
			}
			.force-flex {
				display: flex!important;
			}
			.lms-learner & {
				display: flex; // This will add flex back for learner, fine for IE (for now)
				@media (max-width: $screen-md) {
					font-size: 2rem;
				}
				@media (max-width: $screen-sm) {
					ul {
						flex-wrap: wrap;
					}
					font-size: 1.6rem;
					max-width: 100vw;
					img {
						max-width: 80vw;
					}
					.col-xs-12 {
						max-width: 100vw;
						font-size: 2rem;
						.learners-standard-category {
							padding-left: 0px;
						}
					}
					.ilr-standard-options {
						font-size: 1.6rem;
					}
				}
				@media (max-width: $screen-xs) {
					font-size: 1.6rem;
				}
			}
			&.u-flexbox--no-ie {
				_:-ms-fullscreen, :root & {
					display: block;  // This will remove flexbox for administration, hassle for IE
				}
			}
		}
		&--wrap {
			flex-wrap: wrap;
		}
		&-flow {
			&--column {
				flex-flow: column;
			}
		}
		&--direction {
			&-row {
				flex-direction: row;
			}
		}
		&--justify {
			&-space {
				justify-content: space-between;
			}
			&-stretch {
				justify-content: stretch;
			}
			&-end {
				justify-content: flex-end;
			}
			&-center {
				justify-content: center;
			}
			&-space {
				justify-content: space-between;
			}
		}
		&--align {
			&-center {
				align-items: center;
			}
			&-baseline {
				align-items: baseline;
			}
		}
		&--direction {
			&-column {
				flex-direction: column;

				// column breaks in IE 11 and probobly below too, this will make IE 11 look not so good, but will function! Technical debt!
				_:-ms-fullscreen, :root & {
					flex-direction: inherit;
					flex-wrap: wrap;
					align-content: flex-start;
				}
			}
		}
	}

	&-flex {
		&-align {
			&--center {
				align-self: center;
			}
		}
		&--grow {
			flex-grow: 1;
		}
		&--no-grow {
			flex-grow: 0;
		}
		&--shrink {
			flex-shrink: 1;
		}
		&--no-shrink {
			flex-shrink: 0;
		}
		&--safari {
			flex: 1 1 150px;
		}
		&--basis {
			&-full {
				flex-basis: 100%;
			}
		}
		&--middle {
			display: flex;
			flex-direction: column;
			justify-content: center;
		}
	}

	&-text-decoration {
		&--none {
			text-decoration: none !important;
		}
		&--underline {
			text-decoration: underline;
		}
	}
	&-text-shadow {
		&--for-white {
			text-shadow: 0px 0px 7px rgba(150, 150, 150, 1);
		}
	}

	&-padding {
		&--bottom {
			&-ten {
				padding-bottom: 10px;
			}
			&-thirty {
				padding-bottom: 30px;
			}
		}
		&--top-bottom {
			&-five {
				padding-top: 5px;
				padding-bottom: 5px;
			}
			&-ten {
				padding-top: 10px;
				padding-bottom: 10px;
			}
		}
		&--top {
			&-three {
				padding-top: 3px;
			}
			&-ten {
				padding-top: 10px;
			}
			&-twenty {
				padding-top: 20px;
			}
			&-thirty {
				padding-top: 30px;
			}
			&-thirty-four {
				padding-top: 34px;
			}
			&-fourty {
				padding-top: 40px;
			}
		}
		&--left {
			&-five {
				padding-left: 5px;
			}
			&-ten {
				padding-left: 10px;
			}
			&-twenty {
				padding-left: 20px;
			}
			&-thirty {
				padding-left: 30px;
			}
			&-zero {
				padding-left: 0;
			}
		}
		&--right {
			&-five {
				padding-right: 5px;
			}
			&-twenty {
				padding-right: 20px;
			}
			&-zero {
				padding-right: 0;
			}
		}
		&--five {
			padding: 5px;
		}
		&--ten {
			padding: 10px;
		}
		&--fifteen {
			padding: 15px;
		}
		&--twenty {
			padding: 20px;
		}
		&--lg-button {
			padding: 2px 3px 0px 5px;
		}
	}

	&-margin {
		&--top-botom {
			&-ten {
				margin-bottom: 10px;
				margin-top: 10px;
			}
		}
		&--top {
			&-five {
				margin-top: 5px;
			}
			&-ten {
				margin-top: 10px;
			}
			&-twenty {
				margin-top: 20px;
			}
			&-fifty {
				margin-top: 50px;
			}
			&-hundred {
				margin-top: 100px;
			}
			&-minus {
				&34 {
					margin-top: -34px;
				}
				&37 {
					margin-top: -37px;
				}
				&42 {
					margin-top: -42px;
				}
			}
		}
		&--bottom {
			&-zero {
				margin-bottom: 0px;
			}
			&-three {
				margin-bottom: 3px;
			}
			&-five {
				margin-bottom: 5px;
			}
			&-ten {
				margin-bottom: 10px;
			}
			&-fifteen {
				margin-bottom: 15px;
			}
			&-twenty {
				margin-bottom: 20px;
			}
		}
		&--right {
			&-two {
				margin-right: 2px;
			}
			&-five {
				margin-right: 5px;
			}
			&-ten {
				margin-right: 10px;
			}
			&-twenty {
				margin-right: 20px;
			}
			&-thirty {
				margin-right: 30px;
			}
		}
		&--left {
			&-minus-seven {
				margin-left: -7px;
			}
			&-five {
				margin-left: 5px;
			}
			&-ten {
				margin-left: 10px;
			}
			&-fifteen {
				margin-left: 15px;
			}
			&-twenty {
				margin-left: 20px;
			}
			&-twenty-five {
				margin-left: 25px;
			}
			&-thirty {
				margin-left: 30px;
			}
		}
		&--right-bottom {
			&-five {
				margin-right: 5px;
				margin-bottom: 5px;
			}
		}
		&--minus-five {
			margin: -5px;
		}
		&--five {
			margin: 5px;
		}
		&--ten {
			margin: 10px;
		}
		&-twenty {
			&.form-group {
				margin: 20px;
			}
		}
		&--none {
			margin: 0;
		}
	}

	&-pointer {
		cursor: pointer;
	}

	&-no-pointer {
		cursor: default;
	}
	&-not-allowed {
		cursor: not-allowed;
	}
	&-clickable-link {
		cursor: pointer;
		text-decoration: none;
		&:hover {
			text-decoration: underline;
		}
	}


	&-position {
		&--relative {
			position: relative;
		}
		&--static {
			position: static;
		}
		&--absolute {
			& {
				position: absolute
			}
			&-middle {
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
			}
		}
		&--top-right {
			& {
				top: 0px;
				right: 0px;
			}
			&-five {
				top: 5px;
				right: 5px;
			}
			&-minus-five {
				top: -5px;
				right: -5px;
			}
			&-ten {
				top: 10px;
				right: 10px;
			}
			&-minus-ten {
				top: -10px;
				right: -10px;
			}
		}
		&--top-left {
			& {
				top: 0px;
				left: 0px;
			}
			&-three {
				top: 3px;
				left: 3px;
			}
		}
		&--top-left-right {
			& {
				top: 0px;
				left: 0px;
				right: 0px;
				&-fifteen {
					top: 15px;
					left: 15px;
					right: 15px;
				}
			}
		}
		&--bottom {
			&-left-right {
				bottom: 0;
				left: 0;
				right: 0;
				&-fifteen {
					bottom: 15px;
					left: 15px;
					right: 15px;
				}
			}
		}
	}

	&-text {
		&--left {
			text-align: left !important;
		}
		&--right {
			text-align: right;
		}
		&--center {
			text-align: center;
		}
		&--initial {
			text-align: initial;
		}
		&-overflow {
			&--ellipsis {
				text-overflow: ellipsis;
			}
		}
		&-transform {
			&--cap {
				text-transform: capitalize;
			}
		}
	}
	&-white {
		&--pre {
			&-wrap {
				white-space: pre-wrap;
			}
			&-line {
				white-space: pre-line;
			}
		}
		&--no {
			&-wrap {
				white-space: nowrap;
			}
		}
		&--normal {
			white-space: normal;
		}
	}
	&-bold {
		font-weight: 800;
	}

	&-overflow {
		&--hidden {
			overflow: hidden;
		}
		&--auto {
			overflow: auto;
		}
		&--clip {
			overflow: clip;
		}
		&--x {
			&-scroll {
				overflow-x: scroll;
			}
			&-hidden {
				overflow-x: hidden;
			}
			&-auto {
				overflow-x: auto;
			}
		}
	}
	&-height-limit {
		&--overflow {
			&-y {
				overflow-y: auto;
				max-height: 400px;
			}
			&-y-two {
				overflow-y: auto;
				max-height: 200px;
			}
			&-y-one-five {
				overflow-y: auto;
				max-height: 150px;
			}
			&-y-60vh {
				overflow-y: auto;
				max-height: 60vh;
			}
		}
	}
	&-width {
		// That one case with tables.
		&--one-percent {
			width: 1%;
			white-space: nowrap;
		}
		&--thirty-percent {
			width: 30%;
		}
		&--hundred-percent {
			width: 100%;
		}
		&--hundred-px {
			width: 100px !important;
		}
		&--110-px {
			width: 110px;
		}
		&--130-px {
			width: 130px;
		}
		&--150-px {
			width: 150px;
		}
		&--two-hundred-px {
			width: 200px;
		}
		&--265-px {
			width: 265px;
		}
		&--four-hundred-px {
			width: 400px;
		}
		&--five-hundred-px {
			width: 500px;
		}
		&--min {
			&-hundred {
				min-width: 100%;
			}
			&-1p {
				min-width: 1%;
			}
			&-100px {
				min-width: 100px;
			}
			&-200px {
				min-width: 200px;
			}
			&-250px {
				min-width: 250px;
			}
			&-700px {
				min-width: 700px;
			}
			&-110-px {
				min-width: 110px;
			}
		}
		&--auto {
			width: auto !important;
		}
	}
	&-height {
		&--30 {
			height: 30px;
		}
		&--33 {
			height: 33px;
		}
		&--40 {
			height: 40px;
		}
	}
	&-max-width {
		max-width: 100%;
		&--video {
			max-width: 100%;
			min-width: 100%;
			max-height: 100%;
		}
		&--three-hundred {
			max-width: 300px;
		}
		&--200px {
			max-width: 200px;
		}
		&--120px {
			max-width: 120px;
		}
		&--160px {
			max-width: 160px;
		}
		&--1px {
			max-width: 10px;
		}
		&--30px {
			max-width: 30px;
		}
		&--40prct {
			max-width: 40%;
		}
	}
	&-max-height {
		&--five-hundred {
			max-height: 500px;
		}
		&--four-hundred {
			max-height: 400px;
		}
		&--three-hundred {
			max-height: 300px;
		}
		&--two-hundred {
			max-height: 200px;
		}
		&--one-hundred {
			max-height: 100px;
		}
		&--nine-ty {
			max-height: 90px;
		}
		&--fifty {
			max-height: 50px;
		}
		&--twenty-six {
			max-height: 26px;
		}
	}
	&-min-height {
		&--33 {
			min-height: 33px;
		}
		&--fifty-one {
			min-height: 51px;
		}
	}
	&-line-height {
		&--thirty {
			line-height: 30px;
		}
		&--initial {
			line-height: initial;
		}
	}
	&-break-line {
		word-wrap: break-word;         /* All browsers since IE 5.5+ */
		overflow-wrap: break-word;     /* Renamed property in CSS3 draft spec */
		max-width: 200px;
	}
	&-break-cell-line {
		word-wrap: break-word;
	}

	&-modal {
		&--full-width {
			.modal-lg {
				min-width: 95%;
			}
		}
		&--some-width {
			.modal-lg {
				min-width: 800px;
			}
		}
		// attempt to use full screen modal window, width and height, using flex
		&--full-window {
			& {
				display: flex !important; // life
			}
			.modal-dialog {
				& {
					flex-grow: 1;
					margin: 30px;
					display: flex;
				}
				.modal-content {
					& {
						flex-grow: 1;
						display: flex;
						flex-direction: column;
					}
					.modal-body {
						flex-grow: 1;
						display: flex;
						iframe {
							flex-grow: 1;
						}
						&.block {
							display: block;
						}
					}
					.modal-header {
						display: none;
						&.visible {
							display: block;
						}
					}
				}
			}
		}
	}

	// Hide some elements if page is loaded in iframe, should be removed when Everything will transition into proper AJAX calls.
	&-iframe {
		.lms-header,
		.save-and-exit,
		a[back-button] {
			display: none;
		}
		.loading-splash {
			background-color: #fff;
		}
	}

	&-hover-action {
		.show-element {
			& {
				display: none;
			}
			&--visible {
				visibility: hidden;

			}
			&--opacity {
				opacity: 0;
			}
			&--filled-white {
				background: white;
				border: solid 1px #ddd;
				margin-top: -1px;
				margin-left: -1px;
				transition: opacity 0.1s ease-in-out;
				z-index: 1;
			}
		}
		&:hover {
			.show-element {
				display: initial;
				&--visible {
					visibility: visible;
				}
				&--opacity {
					opacity: 1;
				}
			}
			.dark-bg {
				background: rgba(60, 60, 60, 0.1);
			}
			.white-contrast {
				background-color: #fff;
				li .name {
					color: #000;
				}
			}
		}
	}

	&-single-line-ellipsis {
		overflow: hidden;
		text-overflow: ellipsis;
	}

	&-multi-line-ellipsis {
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		&.one_lines {
			-webkit-line-clamp: 1;
		}
		&.two_lines {
			-webkit-line-clamp: 2;
		}
		&.three_lines {
			-webkit-line-clamp: 3;
		}
		&.four_lines {
			-webkit-line-clamp: 4;
		}
		&.five_lines {
			-webkit-line-clamp: 5;
		}
		&.six_lines {
			-webkit-line-clamp: 6;
		}
		&.seven_lines {
			-webkit-line-clamp: 7;
		}
		&.eight_lines {
			-webkit-line-clamp: 8;
		}
		&.ten_lines {
			-webkit-line-clamp: 10;
		}
		&.fifteen_lines {
			-webkit-line-clamp: 15;
		}
		&.seventeen_lines {
			-webkit-line-clamp: 17;
		}
		&.twenty_lines {
			-webkit-line-clamp: 20;
		}
	}

	&-select-group {
		&-button {
			height: 34px;
		}
		&-button2 {
			height: 36px;
		}
		&-label {
			.x_title & {
				color: #222;
			}
			border: none;
			background: transparent;
		}
	}

	/*
	<div class="u-squared-checkbox">
		<input type="checkbox" value="" name="check" ng-model="" id="checkbox" />
		<label for="checkbox"></label>
	</div>
	*/
	&-squared-checkbox {
		position: relative;
		&.inline-block {
			display: inline-block;
		}
		label {
			&:not(.label-text) {
				& {
					width: 20px;
					height: 20px;
					min-width: 20px;
					cursor: pointer;
					position: absolute;
					top: 0;
					left: 20px;
					background: #f7f7f7;
					border-radius: 2px;
					box-shadow: 0px 0px 2px rgba(0,0,0,1);
					transition: all 150ms ease-in-out;
				}
				&:after {
					content: '';
					width: 10px;
					height: 7px;
					position: absolute;
					top: 5px;
					left: 5px;
					border: 3px solid #333;
					border-top: none;
					border-right: none;
					background: transparent;
					opacity: 0;
					transform: rotate(-45deg);
				}
			}
			&.zero-position {
				left:0;
			}
			&.label-text {
				margin-left: 15px;
				cursor: pointer;
				&--bigger {
					margin-left: 30px;
				}
			}
		}
		input[type=checkbox] {
			visibility: hidden;
			&:checked + label:after {
				opacity: 1;
			}
		}
	}

	&-disabled {
		& {
			color: gray;
			pointer-events: none;
			opacity: 0.6;
		}
		&--active {
			pointer-events: initial;
		}
		&--row {
			min-height: 35px;
		}
		&--events {
			pointer-events: none;
		}
		&--gray {
			color: rgba(0,0,0, 0.3);
		}
	}

	&-alternatig-row {
		&{
			padding-top: 15px;
		}
		&:nth-child(even){
			background-color: #f3f3f3;
		}
		&:nth-child(odd){
			background-color: #e1e3ec;
		}
	}
	&-help-block {
		color: #a94442;
		margin-top: 5px;
		margin-bottom: 10px;
	}
	&-input {
		&-pound {
			position: relative;
			input {
				padding-left:18px;
			}
			&:before {
				position: absolute;
				top: 6px;
				content: "£";
				left: 20px;
				font-size: 16px;
			}
		}
	}
	&-hide-sort {
		cursor: default;
		&::after {
			display: none;
		}
	}
	&-type-bg {
		background-color: #ebf3ee;
	}
	&-radio {
		width: 20px;
		height: 20px;
		border-radius: 10px;
		border: solid 2px rgba(0,0,0,.5);
		margin: 0 10px 0 0;
		cursor: pointer;
		position: relative;
		&.selected {
			&:after {
				content: "";
				position: absolute;
				top: 4px;
				left: 4px;
				width: 8px;
				height: 8px;
				background: rgba(0,0,0,.5);
				border-radius: 4px;
			}
		}
		&.white {
			border: solid 2px rgba(255,255,255,.9);
			&.selected {
				&:after {
					background: rgba(255,255,255,.9);
				}
			}
		}
	}
	&-invisible {
		visibility: hidden;
		width: 1px !important;
		height: 1px !important;
		overflow: hidden;
		margin: 0 !important;
		padding: 0 !important;
		display: inline;
	}
	&-table {
		&--fixed {
			table-layout: fixed;
		}
		&--break-words {
			table-layout: fixed;
			word-wrap: break-word;
		}
		&-cell {
			&--break-all {
				word-break: break-all;
				overflow-wrap: break-word;
				max-width: 200px;
			}
			&--break-long {
				word-break: break-all;
				overflow-wrap: break-word;
				max-width: 250px;
			}
			&--break-output {
				word-break: break-all;
				overflow-wrap: break-word;
				max-width: 200px;
				white-space: pre-wrap;
			}
		}
	}
	&-display {
		&--inline-block {
			display: inline-block;
		}
		&--inline {
			display: inline;
		}
		&--table-row {
			display: table-row;
		}
		&--block {
			display: block;
		}
		&--initial {
			display: initial!important;
		}
	}
	&-z-index {
		&--one {
			z-index: 1;
		}
	}
	&-vertical-align {
		&--middle {
			vertical-align: middle !important;
		}
		&--top {
			vertical-align: top;
		}
	}
	&-pulse {
		animation: pulse 5s infinite cubic-bezier(0.66, 0, 0, 1);
		box-shadow: 0 0 0 0 rgba(#5a99d4, .5);
		&:hover {
			animation: none;
		}
	}
	&-background {
		&--transparent {
			background-color: rgba(#000, .5)
		}
		&--red {
			background: red;
		}
		&--white {
			background: white;
		}
		&--dark {
			background: #666;
		}
		&--darker {
			background: #444;
		}
	}
	&-hide-hover {
		&:hover {
			text-decoration:none !important;
		}
	}
	&-badge {
		& {
			display: inline-block;
			min-width: 10px;
			padding: 3px 7px;
			font-size: 12px;
			font-weight: 700;
			line-height: 1;
			vertical-align: middle;
			white-space: nowrap;
			text-align: center;
			border-radius: 10px;
			color: #fff;
		}
		&--red {
			background: #CD4A47;
		}

	}
	&-font {
		&-size {
			&--14 {
				font-size: 14px;
			}
			&--16 {
				font-size: 16px;
			}
			&--22 {
				font-size: 22px;
			}
			&--30 {
				font-size: 30px;
			}
			&--1000pct {
				font-size: 1000%;
			}
		}
		&-family {
			&--courier {
				font-family: 'courier new',monospace;
			}
		}
	}
	&-no-transition {
		transition: none;
	}
	&-text {
		&--red {
			color: red;
		}
		&--dark {
			color: #333;
		}
		&--greenish {
			color: #00ca00;
		}
		&--gold {
			color: gold;
		}
		&--silver {
			color: silver;
		}
		&--bronze {
			color: #cd7f32;
		}
		&--white {
			color: #fff !important; //eh
		}
		&--generic {
			color: rgb(95, 114, 134);
		}
	}
	&-responsive-iframe {
		position: relative;
		padding-bottom: 56.25%; /* 16:9 */
		padding-top: 25px;
		height: 0;

		iframe {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
		}
	}
	&-pop-out-text {
		.single_line {
			white-space: nowrap;
			overflow-x: hidden;
			text-overflow: ellipsis;
			::hover {

			}
		}
	}
	&-border {
		border: solid 1px #ddd;
		&--radius {
			&10 {
				border-radius: 10px;
			}
			&0 {
				border-radius: 0px;
			}
			&-top-left-right {
				&0 {
					border-top-left-radius: 0;
					border-top-right-radius: 0;
				}
			}
			&-bottom-left-right {
				&0 {
					border-bottom-left-radius: 0;
					border-bottom-right-radius: 0;
				}
			}
			&-left {
				&0 {
					border-bottom-left-radius: 0;
					border-top-left-radius: 0;
				}
			}
		}
		&--none {
			border: 0 !important;
		}
		&--bottom {
			border-bottom: solid 1px #ddd;
			&-half:after {
				content: '';
				position: absolute;
				left: 0;
				bottom: -3px;
				height: 1px;
				width: 60%;
				background: #fff;
				.light-theme & {
					background: #212121;
				}
			}
		}
	}
	&-loading {
		&--absolute-full {
			position: absolute;
			left: 0;
			top: 0;
			right: 0;
			bottom: 0;
			z-index: 10;
			background: rgba(255, 255, 255, 0.7);
			padding-top: 29px;
			&.dark {
				background: rgba(255, 255, 255, 0.8);
			}
		}
		&--overlay {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background-color: rgba(255, 255, 255, 0.9);
			z-index: 9999;
			display: flex;
			align-items: center;
			justify-content: center;
		}
		&--overlay-content {
			text-align: center;
			.loading-icon {
				color: #337ab7;
				font-size: 3rem;
			}
			.loading-text {
				margin-top: 15px;
				font-size: 16px;
				color: #337ab7;
			}
		}
	}
	&-number-input-arrows {
		&--hidden {
			@at-root input#{&} {
				&::-webkit-outer-spin-button,
				&::-webkit-inner-spin-button {
					-webkit-appearance: none;
					margin: 0;
				}
				&[type=number] {
					-moz-appearance: textfield;
				}
			}
		}
	}
	&-spinner {
		-webkit-animation: u-spin 4s linear infinite;
		-moz-animation: u-spin 4s linear infinite;
		animation: u-spin 4s linear infinite;
	}
	&-password-cover {
		position: relative;
		&:after {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background-color: #fff;
			content: "************************************************************************************************";
			overflow: hidden;
		}
	}
	&-larger-font-up-to-md {
		@media screen and (max-width: $screen-md-min) {
			font-size: 19px;
		}
	}
	&-seperator {
		&--top {
			border-top: 1px #c6c6c6 solid;
			margin-top: 5px;
		}
	}
	&-flow {
		&--none {
			float: none !important;
		}
	}
	&-http-status {
		&--200 {
			color: #28A745;
		}
		&--201 {
			color: #73D13D;
		}
		&--204 {
			color: #A0D911;
		}
		&--400 {
			color: #FFC107;
		}
		&--401 {
			color: #FD7E14;
		}
		&--403 {
			color: #DC3545;
		}
		&--404 {
			color: #007BFF;
		}
		&--405 {
			color: #6F42C1;
		}
		&--408 {
			color: #B197FC;
		}
		&--500 {
			color: #C82333;
		}
		&--502 {
			color: #E64980;
		}
	}
}

@-moz-keyframes u-spin { 100% { -moz-transform: rotate(360deg); } }
@-webkit-keyframes u-spin { 100% { -webkit-transform: rotate(360deg); } }
@keyframes u-spin { 100% { -webkit-transform: rotate(360deg); transform:rotate(360deg); } }

@keyframes pulse {
	to {
		//box-shadow: 0 0 0 45px rgba(232, 76, 61, 0);
		box-shadow: 0 0 0 15px rgba(#5a99d4, 0);
	}
}

.ng-confirm-content-pane {
	user-select: text !important;
	-webkit-user-select: text !important; /* For Chrome, Safari, and Opera */
	-moz-user-select: text !important; /* For Firefox */
	-ms-user-select: text !important; /* For Internet Explorer and Edge */
}

// Log expand button styles
.log-expand-btn {
	opacity: 0.7;
	transition: opacity 0.2s;
	
	&:hover {
		opacity: 1;
	}
}