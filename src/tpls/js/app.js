/*global doToStr:true, checkContentForDates:true, google:true*/

(function(angular) {
	'use strict';

	angular.module('lmsApp', ['ngRoute', 'ui.bootstrap', 'smart-table', 'angular-confirm', 'ui.bootstrap.showErrors', 'autocomplete', 'chart.js', 'mwl.calendar', 'ngCookies', 'smoothScroll', 'ngAnimate', 'angular.filter', 'ui.sortable', 'rzModule', 'ngFileUpload', 'isteven-multi-select', 'ui.tinymce', 'ngSanitize', 'angularjs-gauge', 'ngclipboard', 'googlechart', 'cp.ngConfirm','hierarchical-selector', 'toggle-switch','mgo-angular-wizard', 'mc.resizer'])

	// To change URL in browser without triggering routes. Ex: $location.path('learner/resources/' + value.id, false);
	.run(['$route', '$rootScope', '$location', function ($route, $rootScope, $location) {
		
		let appProp = $location.$$search && $location.$$search.appMode;
		$rootScope.appMode = appProp ? ($location.$$search.appMode === 'true' ? true : false) : false;

		// Determine if the request is from a mobile device by checking the 'is_mobile' flag in the URL parameters.
		let mobileProp = $location.search() && $location.search().is_mobile;
		$rootScope.isMobileRequest = mobileProp === 'true';
	}])

	.config(['$compileProvider', function ($compileProvider) {
		$compileProvider.aHrefSanitizationWhitelist(/^\s*(https?|ftp|mailto|file|sms|tel|skype):/);
	}]).factory('authInterceptor', ['$q', '$location','$rootScope', function($q, $location,$rootScope) {
		return {
			responseError: function(rejection) {
				if (rejection.status === 403) {
					// This shows meaningless message if any request to back end fails with 403
					//$rootScope.$broadcast('permission_denied');
				}
				return $q.reject(rejection);
			}
		};
	}])
	.config(['$httpProvider', function($httpProvider) {
		$httpProvider.interceptors.push('authInterceptor');
	}])

	.factory('$exceptionHandler', function () {
		return function (exception, cause) {
			var message = (exception && exception.message) || String(exception);
			var stack = (exception && exception.stack) || '';

			window.logClientError({
				type: 'angular_exception',
				message: message,
				stack: stack,
				url: window.location.href
			});

			console.log('Angular Exception: ', exception, cause);
		};
	})

	.decorator('$templateRequest', function($delegate, $log) {
		return function(tpl, ignoreRequestError) {
			return $delegate(tpl, ignoreRequestError).catch(function(err) {
				if (err && err.hasOwnProperty('isGloballyCancelled')) {
					// Suppress known cancellation errors
					return '';
				}
				$log.warn('Template request failed:', err);
				throw err;
			});
		};
	})

	.controller('lmsController', function($timeout, $rootScope, $http, $route, $uibModal, $log, $location, $scope, $roleService, $timingService, $learningActionService, $document, $fileService, $window, $ngConfirm, $sce, $billingService, $lmsVisuals, dynamicLoadingService) {

		$scope.$watchGroup(['groupTitle', 'tabTitle', 'pageTitle'], function(newValues) {
			var [group, tab, page] = newValues;
			var title = "<?=$LMSTitle?>";
			if (group) title += ': ' + group;
			if (tab)   title += ' - ' + tab;
			if (page)  title += ' - ' + page;
			document.title = title;
		});

		$rootScope.timestamp = new Date().getTime();
		$rootScope.bgImage = "url('../learners-interface/background?v=" + $rootScope.timestamp + "')";

		$rootScope.version = "<?=\APP\Tools::getConfig('version')?>";

		dynamicLoadingService.get('shadow_roles', '<?=$LMSUri?>role/shadow/all').then(function(data) {
			$rootScope.shadow_roles = data;
		});

		dynamicLoadingService.get('cities', '<?=$LMSUri?>city/all').then(function(data) {
			$rootScope.cities = data;
		});

		// Role service for checking roles and rendering view accordingly.
		$rootScope.rs = $roleService;
		$rootScope.bs = $billingService;
		$rootScope.visuals = $lmsVisuals;
		$rootScope.ts = $timingService;
		$rootScope.las = $learningActionService;
		$rootScope.fs = $fileService;
		$rootScope.tinymce_options= {
			selector: 'textarea',  // change this value according to your HTML
			plugins: 'image lists advlist link',
			toolbar: 'undo redo | bold italic underline strikethrough | link | fontfamily fontsize blocks | alignleft aligncenter alignright alignjustify | outdent indent |  numlist bullist checklist | forecolor backcolor casechange permanentpen formatpainter removeformat | pagebreak | charmap emoticons | fullscreen  preview save | image | a11ycheck ltr rtl | footnotes',
			promotion: false,
			setup: function(editor) {
				editor.on('change', function() {
					editor.save();
				});
				
				// Override setContent to prevent parser errors
				var originalSetContent = editor.setContent;
				editor.setContent = function(content, args) {
					if (editor.parser && editor.initialized) {
						return originalSetContent.call(editor, content, args);
					}
					// If parser isn't ready, store content for later
					editor.pendingContent = content;
					return editor;
				};
				
				// Ensure proper cleanup on remove
				editor.on('remove', function() {
					if (editor.parser) {
						editor.parser = null;
					}
				});
			},
			init_instance_callback: function(editor) {
				// Ensure editor is fully initialized before any content operations
				editor.initialized = true;
				
				// Set any pending content now that parser is ready
				if (editor.pendingContent) {
					editor.setContent(editor.pendingContent);
					editor.pendingContent = null;
				}
				
				// Additional safety check - ensure parser is available
				if (!editor.parser) {
					setTimeout(function() {
						if (editor.parser) {
							editor.initialized = true;
							if (editor.pendingContent) {
								editor.setContent(editor.pendingContent);
								editor.pendingContent = null;
							}
						}
					}, 100);
				}
			}
		};

		// Global CKEditor configuration for customizations (directive has comprehensive defaults)
		// This can be used to override specific settings across all CKEditor instances
		$rootScope.ckeditor_options = {
			// Example: Custom placeholder for general use
			placeholder: "Enter your content here...",
			// The directive provides comprehensive defaults, so only specify overrides here
		};

		$rootScope.disableLazyLoading = false;
		$rootScope.ieVersion = $document[0].documentMode;
		if ($rootScope.ieVersion) {
			$rootScope.disableLazyLoading = true;
		}
		$rootScope.msEdge = /Edge/.test(navigator.userAgent);

		$rootScope.globalCloseModalDelay = 1500; // Miliseconds to close modal window afeter pressing processing save, after "save & close"

		$rootScope.stripHTMLTags = (htmlContent) => {
			// Create a temporary DOM element
			const tempDiv = document.createElement("div");
			// Assign the HTML content to the div
			tempDiv.innerHTML = $sce.trustAsHtml(htmlContent);

			let textContent = tempDiv.textContent || tempDiv.innerText || "";

			// Remove newline characters
			return textContent.replace(/(\n\s*)+/g, "\n");
		}

		$rootScope.hideTopIcon = function (group) {
			// console.log(group)
			var features = $rootScope.plan.features;
			var config = $rootScope.config
			if (group.key === 'powerbi-charts-main') {
				return !config.isEmbeddedPowerBI;
				// || !features.management_information_system;
			} else if (group.key === 'review') { // report builder
				if($rootScope.config.sharedClients)
				{
					return !features.management_information_system;
				}
			} else {
				return false;
			}
		}

		$rootScope.dashboard = {
			selected: {},
			index: {},
			learner: false,
			administration: false,
			pkf: false // personal knowledge file!
		};

		$rootScope.greaterThan = function(prop, val) {
			return function(item) {
				return item[prop] > val;
			};
		};

		$rootScope.triggerSubmit = function($formName) {
			$rootScope.$broadcast('makeSubmit', {formName:$formName });
		};

		$rootScope.trustHtml = function (comment){
			return $sce.trustAsHtml(comment);
		};

		// Tell learner tasks menu to reset to default state.
		$rootScope.resetTasks = function () {
			if (document.baseTitle) {
				document.title = document.baseTitle;
			}
			$timeout(function() {
				$rootScope.$broadcast('learner-tasks-reset');
			}, 20);
		};

		$rootScope.skip2main = () =>
		{
			var main = document.getElementsByClassName('lms-content')[0];
			if (main)
			{
				main.setAttribute('tabindex', '-1');
				main.focus();
				main.scrollIntoView({
					behavior: 'smooth',
					block: 'start',
					inline: 'nearest'
				});
			}
		};

		// Vague attempt to determine are you viewing this from mobile!
		$rootScope.isMobile = function () {
			return (/mobile/i.test(navigator.userAgent) && !/ipad|tablet/i.test(navigator.userAgent));
		};

		// Whenever learner signs-off, adds comment/file/anything, action count must be refreshed.
		$rootScope.$on('update-actions', function() {
			$rootScope.las.get();
		});

		// where I will store some essentials globally.
		$rootScope.resetDashboard = function () {
			if (
				$rootScope.shadow_roles.length > 1
			) {
				var timer = 200;
				$rootScope.dashboard.group = {};
				$rootScope.dashboard.selected = {};
				$rootScope.dashboard.index = {};
				if ($rootScope.hideGroups) {
					$timeout(function() {
						angular.element('.dashboard-icons').hide();
						$timeout(function() {
							angular.element('.dashboard-icons').slideDown(timer);
							angular.element('#custom_dashboard').slideDown(timer);
							$rootScope.hideGroups = false;
						}, 20);
					}, 0);
				}
			}
		};

		$rootScope.getMenu = function () {
			$http({
				method: 'get',
				url: '<?=$LMSUri?>menu'
			}).then(function successCallback(response) {
				$rootScope.dashboard.structure = response.data.structure;
				$rootScope.dashboard.dashId = null;
				var i = 0;
				$rootScope.dashboard.structure.forEach(val => {
					if (val.id === 106) {
						$rootScope.dashboard.dashId = i;
					}
					i++;
				});
				$rootScope.assignments = response.data.assignments;
				$rootScope.currentUser = response.data.user;
				$rootScope.plan = response.data.plan;
				$rootScope.config = response.data.config;
				$rootScope.isOpenElms = response.data.licensing.isOpenElms;
				$rootScope.isOpenElmsTMS = response.data.licensing.isOpenElmsTMS;
				$rootScope.isApprentix = response.data.licensing.isApprentix;
				$rootScope.isJackdawCloud = response.data.licensing.isJackdawCloud;
				$rootScope.isSMCR = response.data.licensing.isSMCR;
				$rootScope.permissions = response.data.permissions;
				$rootScope.customDashboard = response.data.customDashboard;
				$rootScope.dashboardUrl = response.data.dashboardUrl;
				$rootScope.dashboardUrlKey = response.data.dashboardUrlKey;
				$rootScope.workUrl = response.data.workUrl;
				$rootScope.workUrlKey = response.data.workUrlKey;
				$rootScope.hasFullDiscount = response.data.hasFullDiscount;
				//$rootScope.initDashboard(); // Run dashboard initializations
				if ($rootScope.config.disableLazyLoading) {
					$rootScope.disableLazyLoading = true;
				}
				$rootScope.multiCartItemCount = response.data.multiCartItemCount;

				$rootScope.startCalendarTime = $rootScope.convertConfigurationDate($rootScope.config.CalendarStartTime);

				// fade page in after angular is up and active
				$timeout(function() {
					$rootScope.ngLoaded = true;
					//remove loading element, performance issue
					$timeout(function() {
						if (angular.element('.loading-splash').length > 0) {
							angular.element('.loading-splash').remove();
						}
					}, 3500);

					if (
						$rootScope.config.googleTranslate &&
						!$scope.googleTranslateAdded
					) {
						$scope.addGoogleTranslate();
					}
				}, 500);

				if (!$rootScope.rs.interface('admin')) {
					$rootScope.$broadcast('update-actions');
				}

				if ($rootScope.config.badgesEnabled) {
					dynamicLoadingService.get('badgesListGlobal', '<?=$LMSUri?>api-badgr/list/').then(function(data) {
						$rootScope.badgesListGlobal = data.list;
					});
				}

			}, function errorCallback(response) {
				$rootScope.getMenuError = response.data;
			});
		};


		$rootScope.getMenu();
		// Reload menu when jackdaw modal is closed from learner/jackdaw interface
		$rootScope.$on("modal-jackdaw-closed",function () {
			$rootScope.getMenu();
		});

		// Also, reload menu when configuration has changed.
		$rootScope.$on("configuration-refresh",function () {
			$rootScope.getMenu();
		});

		// Generic menu refresh.
		$rootScope.$on("menu-refresh",function () {
			$rootScope.getMenu();
		});


		$rootScope.reloadApp = function(complete) {
			$http.get("<?=$LMSUri?>reload").then(
				function () {
					if (complete) {
						window.location.reload();
					} else {
						$route.reload();
					}
				},
				function () {
					window.location.reload();
				}
			);
		};

		$rootScope.logout = function () {
			if (
				$rootScope.config.tryTraineeReminder &&
				$rootScope.rs.interface("admin") &&
				!$rootScope.currentUser.has_visited_learner_interface
			) {
				$ngConfirm({
					title: "Confirm",
					content: $rootScope.config.tryTraineeReminderText,
					scope: $scope,
					buttons: {
						yes: {
							text: "Yes",
							btnClass: "btn-primary",
							action: function (scope, button) {
								let learner = $rootScope.shadow_roles.find(function (role) {
									return role.is_learner;
								});
								if (learner) {
								$http({
									method: "POST",
									url: "<?=$LMSUri?>role/shadow",
									data: {
									id: learner.id,
									},
								}).then(function successCallback() {
									window.location.replace("<?=$LMSUri?>manage");
								});
								} else {
								$scope.logoutCall();
								}
							},
						},
						no: {
							text: "No",
							btnClass: "btn-default",
							action: function (scope, button) {
								$scope.logoutCall();
							},
						},
					},
				});
			} else {
				$scope.logoutCall();
			}
		};

		$rootScope.logoutCall = function () {
			$http.get("<?=$LMSUri?>logout").then(function () {
				// send to other tabs to log out, set flag not to run extra log out here
				$rootScope.thisTabLogOut = true;
				localStorage.setItem("logout-event", "logout" + Math.random());
				window.location = "<?=$LMSUri?>";
			});
		};


		/*
			Opens links into modal window. by applying "open-in-modal" attribute to A tag
			That is the dream.
		*/
		$rootScope.openInModal =  function(url, title) {
			if ($rootScope.rs.interface('admin')) {
				var modalInstance = $uibModal.open({
						animation: true,
						ariaLabelledBy: 'modal-title',
						ariaDescribedBy: 'modal-body',
						templateUrl: '<?=$LMSTplsUriHTML?>modal-iframe.html?v=<?=$version?>',
						controller: 'ModalInstanceCtrl', // modal window controller
						controllerAs: '$ctrl',
						size: 'lg',
						windowClass: 'u-modal--full-window',
						backdrop: 'static',
						resolve: {
							data: function () {
								return {
									title: title,
									url: url
								};
							}
						}
					});
				modalInstance.result.then(function () {
					$log.info('Learning modal closed at: ' + new Date());
				}, function () {
					$log.info('Learning modal dismissed at: ' + new Date());
				});
			} else {
				$location.path(url);
				$scope.$apply(); // is this really da way?
			}
		};

		// Return module type name, if ID is provided.
		$rootScope.typeName = function (id) {
			var response = false,
				i;
			if (id && $rootScope.types) {
				for (i = $rootScope.types.length - 1; i >= 0; i--) {
					if ($rootScope.types[i].id === id) {
						response = $rootScope.types[i].name;
					}
				}
			}
			return response;
		};

		// Global datepicker options
		$rootScope.datePickerOptions = {
			startingDay: 1
		};

		// This function is called when google translate gets initialized
		$window.googleTranslateElementInit = function () {
			$window.googleTranslate = new google.translate.TranslateElement({pageLanguage: 'en', autoDisplay: false}, 'google_translate_element');
		};

		// Initializes/inserts google translate script in header
		$scope.addGoogleTranslate = function () {
			$scope.googleTranslateScriptTag = document.createElement('script');
			$scope.googleTranslateScriptTag.src = '//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit';
			$scope.googleTranslateScriptTag.type = 'text/javascript';
			$scope.googleTranslateScriptTag.async = false;
			document.getElementsByTagName('head')[0].appendChild($scope.googleTranslateScriptTag);
			$scope.googleTranslateAdded = true;
		};

		$rootScope.convertConfigurationDate = function (stringDate) {
			if (
				stringDate &&
				stringDate.split
			) {
				stringDate = stringDate.split("");
				var hour = stringDate[0] + stringDate[1];
				var minute = stringDate[2] + stringDate[3];
				return { "hour": hour, "minute": minute };
			}
		};

		$rootScope.myProfile = function (type) {
			var old_title = document.title;
			document.title = document.baseTitle + ': <?=\APP\Templates::translate('%%title__my_profile%%')?>';
			var modalInstance = $uibModal.open({
					animation: true,
					ariaLabelledBy: 'modal-title',
					ariaDescribedBy: 'modal-body',
					templateUrl: '<?=$LMSTplsUriHTML?>modal-my-profile.html?v=<?=$version?>',
					controller: 'ModalMyProfile',
					size: 'lg',
					windowClass: 'u-modal--full-width',
					backdrop: 'static',
					resolve: {
						data: function () {
							return {
								type: type
							};
						}
					}
				})
			;

			// Reload user data here!
			modalInstance.result.then(function () {
				document.title = old_title;
				$log.info('myProfile modal closed with save at: ' + new Date());
				$rootScope.getMenu();
			}, function () {
				document.title = old_title;
				$log.info('myProfile modal dismissed at: ' + new Date());
			});
		};

		$rootScope.changePassword = function (type) {
			var modalInstance = $uibModal.open({
					animation: true,
					ariaLabelledBy: 'modal-title',
					ariaDescribedBy: 'modal-body',
					templateUrl: '<?=$LMSTplsUriHTML?>modal-change-password.html',
					controller: 'ModalChangePassword',
					size: 'lg',
					// windowClass: 'u-modal--full-width',
					backdrop: 'static',
					resolve: {
						data: function () {
							return {
								type: type
							};
						}
					}
				})
			;

			modalInstance.result.then(function () {
				$log.info('changePassword modal closed with save at: ' + new Date());
			}, function () {
				$log.info('changePassword modal dismissed at: ' + new Date());
			});
		};


		let payment_status = localStorage.getItem('payment_status');
		if(payment_status){
			payment_status = JSON.parse(payment_status);

			localStorage.removeItem("payment_status");
			if(payment_status.code=="00"){
				$ngConfirm({
						title: "Transaction Completed Successfully",
						content:"Response Message: "+payment_status.message+ "<br> Order ID: "+payment_status.order_id,//Message added
						columnClass: 'col-md-6 col-md-offset-3',
						type: 'green',
						typeAnimated: true,
						buttons: {
							tryAgain: {
								text: 'Okay',
								btnClass: 'btn-green',
								action: function(){
								}
							},
						}
					});
			}else{
				$ngConfirm({
						title: "Transaction Not Completed",
						content: payment_status.message,
						type: 'red',
						typeAnimated: true,
						columnClass: 'col-md-6 col-md-offset-3',
						buttons: {
							tryAgain: {
								text: 'Okay',
								btnClass: 'btn-red',
								action: function(){
								}
							},
						}
					});
			}
		}

        $scope.isDocsBotVisible = false;
		$scope.docsBotInit = false;
		$scope.toggleDocsBot = function() {
			$scope.isDocsBotVisible = !$scope.isDocsBotVisible;
			if (!$scope.docsBotInit) {
				DocsBotAI.init(
					{
						id: $rootScope.config.AIPoweredHelpAssistant,
						identify: {
							//name: $rootScope.currentUser.fname + ' ' + $rootScope.currentUser.lname,
							//email: $rootScope.currentUser.email,
							//customVariable: 'customValue', // This will be recorded in the question metadata and accessible via the API.
						},
						options: {
							//questions: [
								//"How do I create new eLearning as " + ($rootScope.currentUser.shadow_role ? $rootScope.currentUser.shadow_role.name : $rootScope.currentUser.role.name) + "?",
							//]
						},

					}
				);
				$scope.docsBotInit = true;
			}
		};
	})

	.run(function($interval, $http, $rootScope, $ajaxActions, $ngConfirm) {
		$rootScope.sessionWarningShown = false;
		$rootScope.sessionExtendModal = null;
		
		$interval(function() {
			$http({
				method: 'GET',
				url: '<?=$LMSUri?>checksession'
			}).then(function successCallback(response) {
				$rootScope.loggedIn = true;
				
				// Check if response contains remaining time data
				if (response.data && response.data.remainingMinutes !== undefined) {
					var remainingMinutes = response.data.remainingMinutes;
					
					// Show warning if less than 10 minutes remaining and not already shown
					if (remainingMinutes <= 10 && remainingMinutes > 0 && !$rootScope.sessionWarningShown) {
						$rootScope.sessionWarningShown = true;
						$rootScope.sessionExtendModal = $ngConfirm({
							title: 'Session Timeout Warning',
							content: 'Your session will expire in ' + remainingMinutes + ' minute(s). Do you want to extend your session?',
							type: 'orange',
							typeAnimated: true,
							buttons: {
								extend: {
									text: 'Extend Session',
									btnClass: 'btn-primary',
									action: function() {
										// Make a request to extend the session
										$http.get('<?=$LMSUri?>extend-session').then(function() {
											$rootScope.sessionWarningShown = false;
											$rootScope.sessionExtendModal = null;
										});
									}
								},
								logout: {
									text: 'Logout',
									btnClass: 'btn-default',
									action: function() {
										$rootScope.logout();
									}
								}
							},
							onOpenBefore: function() {
								// Reset warning flag when modal is manually closed
								$rootScope.sessionWarningShown = false;
								$rootScope.sessionExtendModal = null;
							}
						});
					}
					
					// Reset warning flag if session was extended
					if (remainingMinutes > 10) {
						$rootScope.sessionWarningShown = false;
						if ($rootScope.sessionExtendModal) {
							$rootScope.sessionExtendModal.close();
							$rootScope.sessionExtendModal = null;
						}
					}
				}
			}, function errorCallback() {
				$rootScope.loggedIn = false;
				$rootScope.logout(true);
			});
		}, 5 * 60 * 1000);

		/*
			Preload some data in global that are reused in multiple places.
		*/
		$ajaxActions.initialRequests($rootScope);


	})
	.config(['showErrorsConfigProvider', function(showErrorsConfigProvider) {
		showErrorsConfigProvider.showSuccess(true);
	}])
	.config(['calendarConfig', function(calendarConfig) {
		calendarConfig.dateFormatter = 'moment'; // use moment to format dates
		calendarConfig.allDateFormats.moment.date.hour = 'HH:mm';
		calendarConfig.showTimesOnWeekView = true;
		moment.locale('en_gb', {
			week : {
				dow : 1 // Monday is the first day of the week
			}
		});
	}])
	.config(function($httpProvider) {

		// Interrcept All requests that send date object, convert to string
		$httpProvider.defaults.transformRequest = function(data) {
			var data_copy
			;


			// check if object or date, if so, process, then stringify and send back
			if (
				(
					typeof data === 'object' &&
					data !== null
				) ||
				data instanceof Date
			) {
				if (data instanceof Date) {
					data_copy = new Date(data.getTime());
					data_copy = doToStr(data_copy);
				} else {
					data_copy = angular.copy(data);
					checkContentForDates(data_copy);
				}
				return JSON.stringify(data_copy);
			}
			return data;
		};
	})
;



}(window.angular));


