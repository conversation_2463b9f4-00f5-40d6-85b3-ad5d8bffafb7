angular.module('lmsApp').controller('ModalEnrolProgramme', function ($http, $scope, $rootScope, $uibModalInstance, $paymentGatewayService, data, $location,$ngConfirm, $confirm, $learnerOperations, $globalPaymentService, $stripeService, $pay360Service, $govUKPayService, CourseBasketService) {

	$scope.passData = data;
	$scope.paymentGatewayService = $paymentGatewayService;
	$scope.los = $learnerOperations;
	$scope.dataLoading = true;
	$scope.isMultiSelectShoppingBasket = $rootScope.config.isMultiSelectShoppingBasket;

	$scope.couponOpt = {
		enabled: false,
		code: ""
	};

	$scope.applyCoupon = function () {
		$scope.dataLoading = true;
		$globalPaymentService.applyCoupon({resource_id: $scope.passData.standard.id, coupon_code: $scope.couponOpt.code, type: "standard"})
			.then(function successCallback(response) {
			$scope.dataLoading = false;
			if (response.data.status == '404') {
				$confirm({
					text: response.data.msg ?? 'Invalid coupon',
					title: 'Error',
					ok: 'Ok',
					// cancel: 'No'
				})
			} else {
				$scope.discounts = response.data
			}
		});

	}

	$scope.save = function (standard) {
		/* Civica Payments Engine Enable check. Enabling this option will allow learning to be purchased from the system */
		if (
			(
				$rootScope.config.isCivicaPaymentsEngine ||
				$rootScope.config.isGlobalPaymentsEngine ||
				$rootScope.config.enableStripePayment ||
				$rootScope.config.enablePay360 ||
				$rootScope.config.enableGovUKPay
			) &&
			standard.discounted_cost > 0 &&
			!standard.approval &&
			(
				!$scope.discounts ||
				(
					$scope.discounts &&
					$scope.discounts.total != 0
				)
			)
		) {
			if ($rootScope.config.isCivicaPaymentsEngine) {
				$scope.paymentGatewayService.civicaPayment(
					standard.id,
					'programme',
					null,
					null,
					$scope.couponOpt.enabled && $scope.couponOpt.code !== '' ? $scope.couponOpt.code : undefined
				);
			} else if ($rootScope.config.enableStripePayment) {
				standard.link = "learner/resources";
				if (
					$scope.couponOpt.enabled &&
					$scope.couponOpt.code !== ""
				) {
					$stripeService.pay(
						'programme',
						standard.id,
						standard,
						$scope.couponOpt.code,
					);
				} else {
					$stripeService.pay(
						'programme',
						standard.id,
						standard,
					);
				}
			} else if ($rootScope.config.enablePay360) {
				standard.link = "learner/resources";
				$pay360Service.pay(
					standard.id, 'programme', null, null,
					$scope.couponOpt.enabled && $scope.couponOpt.code !== "" ? $scope.couponOpt.code : undefined
				);
			}else if($rootScope.config.enableGovUKPay){
				standard.link = "learner/resources";
				$govUKPayService.pay(
				standard.id, 'programme', null, null,
					$scope.couponOpt.enabled && $scope.couponOpt.code !== "" ? $scope.couponOpt.code : undefined

				);
			}
			else {
				if (
					$scope.couponOpt.enabled &&
					$scope.couponOpt.code !== ''
				) {
					standard.link = "learner/resources";
					$globalPaymentService.makePayment(
						'programme',
						standard.id,
						standard,
						$scope.couponOpt.code
					);
				} else {
					$globalPaymentService.makePayment(
						'programme',
						standard.id,
						standard
					);
				}
			}
		} else {
			$scope.saveAction(standard);
		}
	};


	$scope.getCouponAssignedForResource = function () {
		$globalPaymentService.getCouponAssignedForResource($scope.passData.standard.id, "programme")
			.then(function successCallback(response) {
				$scope.passData.standard = response.data
				$scope.dataLoading = false;
			});
	}

	$scope.getCouponAssignedForResource();

	$scope.saveAction = function (standard) {
		$http({
			method: 'PUT',
			url: "<?=$LMSUri?>apprenticeshipstandards/" + standard.id + "/user/" + $rootScope.currentUser.id,
			data: {
				start_at: standard.start_at
			}
		}).then(function successCallback() {
			// console.log("Enrolled successfully")
			window.location.reload()
			$scope.closeModal()
		});
	};

	$scope.closeAlert = function () {
		$scope.alerts = [];
	};
	$scope.closeModal = function () {
		$uibModalInstance.close();
	};
	$scope.cancelModal = function () {
		$uibModalInstance.dismiss('cancel');
	};

	$scope.removeEntry = function (index, list) {
		list.splice(index, 1);
	};
	$scope.isArray = angular.isArray;

	$scope.addToBasket = function (standard) {
		CourseBasketService.addToBasket(standard.id, 'programme');
		$scope.closeModal();
	}

	////////////////////////////////////////////////////////////////////////////

});
