angular.module('lmsApp').controller('LearnerTextMenu', function ($scope, $learnerProgrammes, $learnerOperations, $rootScope, $location, $log, $http, $timeout, $ngConfirm) {

	$scope.lp = $learnerProgrammes;
	$scope.lo = $learnerOperations;
	$scope.menuItems = [];
	$scope.reloadLearnerResources = false;

	$scope.items = [];
	$scope.bgImageUpdating = false;
	$scope.bgImageDeleting = false;

	$scope.status = {
		isopen: false
	};

	$scope.toggled = function(open) {
		//$log.log('Dropdown is now: ', open);
	};

	$scope.toggleDropdown = function($event) {
		$event.preventDefault();
		$event.stopPropagation();
		$scope.status.isopen = !$scope.status.isopen;
	};

	$scope.appendToEl = angular.element(document.querySelector('#dropdown-long-content'));

	$rootScope.getDefaultLearnerMenu = function() {
		if ($rootScope.currentUser.default_screen){
			if ($rootScope.currentUser.default_screen === 'list') {
				return 'learner/tasks/list';
			}else if ($rootScope.currentUser.default_screen === 'eporfolio') {
				return 'learner/resources';
			}else {
				return 'learner/resources';
			}
		}
		return $rootScope.config.makeResourcesLinktoHome && $rootScope.config.isLearnerLandingPage ? 'learner/categories' : 'learner/resources';
	}



	$scope.menuItems = [
		{
			name: '%%learner_menu__cart%%',
			url: 'course-baskets/list',
			enabled: $rootScope.config.isMultiSelectShoppingBasket && !$rootScope.hasFullDiscount,
			slug: 'learner_basket',
			icon:'svg/top_menu_cart_white.svg',
			title: '%%learner_menu__cart%%',
			order: 1
		},
		{
			name: '%%learner_menu__dashboard%%',
			url: 'dashboard/charts/charts-custom/charts-custom-list',
			enabled: ($rootScope.customDashboard && $rootScope.customDashboard > 0),
			slug: 'learner_menu__dashboard',
			icon:'svg/top_menu_admin_dashboard.svg',
			title: '%%learner_menu__dashboard%%',
			order: 2
		},
		{
			name: '%%learner_menu__resources%%',
			url: $rootScope.getDefaultLearnerMenu(),
			enabled: !$rootScope.config.hideLearningHeaderButton,
			slug: 'learner_menu__resources',
			icon:'svg/top_menu_learning.svg',
			title: '%%learner_menu__resources%%',
			order: 3
			//click: $scope.reloadRoute
		},
		{
			name: '%%learner_menu__calendar%%',
			url: 'learner/tasks/calendar',
			hiddenCalendar: false,
			enabled: !$rootScope.config.hideCalendarHeaderButton,
			slug: 'learner_menu__calendar',
			icon:'svg/top_menu_calendar.svg',
			title: '%%learner_menu__calendar%%',
			order: 4
		},
		{
			name: '%%learner_menu__homework%%',
			url: 'learner/tasks/list',
			hiddenCalendar: true,
			enabled: false,
			slug: 'learner_menu__homework',
			icon:'svg/top_menu_assignments.svg',
			title: '%%learner_menu__homework%%',
			order: 5
		},
		{
			name: '%%learner_menu__progress%%',
			url: '#',
			click: $scope.lp.viewLearner,
			enabled: $rootScope.config.competenciesGamification,
			slug: 'learner_menu__progress',
			icon:'svg/top_menu_progress.svg',
			title: '%%learner_menu__progress%%',
			order: 6
		},
		{
			name: '%%learner_menu__reports%%',
			url: 'learner/reports',
			enabled: (
				$rootScope.assignments &&
				$rootScope.assignments.length > 0 &&
				!$rootScope.config.hideReportsHeaderButton &&
				(
					$rootScope.config.showReportMenuLearnerInterface ||
					(
						$rootScope.currentUser &&
						$rootScope.shadow_roles &&
						$rootScope.shadow_roles.length > 1
					)
				)
			),
			icon:'svg/top_menu_admin_reports.svg',
			glyphicon: ($rootScope.assignments && $rootScope.assignments.length === 1 ? $rootScope.assignments[0].icon : false),
			title: '%%learner_menu__reports%%',
			order: 7
		},
	];

	console.log($rootScope.hasFullDiscount,$scope.menuItems);
	

	$scope.setMenuItem = function (item) {
		if (
			item &&
			item.click
		) {
			item.click();
		} else {
			// Set menu item only if not set, ignore if on current
			if (item !== $scope.activeMenuItem) {
				$scope.activeMenuItem = item;
				document.title = document.baseTitle + ': ' + $scope.activeMenuItem.title;
				if (
					item &&
					(
						item.hiddenCalendar ||
						item.hiddenCalendar === false
					)
				) {
					$rootScope.hiddenCalendar = item.hiddenCalendar;
					$rootScope.$broadcast('learner-tasks-hidden-calendar', item.hiddenCalendar);
				}
			}
		}
	};

	$scope.hideMenu = function () {
		if (document.activeElement.title === "hideMenu") {
			$scope.status.isopen = false;
		}
	};

	for (var i = $scope.menuItems.length - 1; i >= 0; i--) {
		if ($location.$$path.indexOf($scope.menuItems[i].url) > -1) {
			$scope.setMenuItem($scope.menuItems[i]);
		}
	}

	menuBlur = (event) => $timeout(function () {
		if (document.activeElement?.parentElement?.parentElement?.id !== "dropdown-menu")
		{
			$scope.status.isopen = false;
			console.log('mmm?');
		}
	}, 100);

	$scope.setDefaultScreen = function () {
		$http({
			method: 'PUT',
			url: '<?=$LMSUri?>user/set-default-screen',
			data: {
				default_screen: $rootScope.currentUser.default_screen
			}
		}).then(function successCallback() {
			location.reload();
		});
	};

	$scope.deleteBackgroundImage = function () {
		$scope.bgImageDeleting = true;
		$http({
			method: 'DELETE',
			url: '<?=$LMSUri?>user/delete/background_image/' + $scope.currentUser.id,
		}).then(function successCallback() {
			$scope.bgImageDeleting = false;
			location.reload();
		}, function errorCallback(response) {
			$scope.bgImageDeleting = false;

			$ngConfirm({
				title: 'Failed',
				// content: response.data,
				content: 'Image file can not be removed.',
				type: 'red',
				typeAnimated: true,
				buttons: {
					tryAgain: {
						text: 'Ok',
						btnClass: '',
						action: function(){}
					},
				}
			});
		});
	}

	$scope.uploadBackgroundImage = function () {
		$scope.bgImageUpdating = true;
		if ($scope.files.background_image) {
			var formData = new FormData();
			var file = "background_image"
			formData.append(file, $scope.files.background_image);
			formData.append("field", file);
			formData.append("id", $scope.currentUser.id);

			$http({
				method: 'POST',
				url: '<?=$LMSUri?>user/upload/' + file,
				data: formData,
				headers: {
					'Content-Type': undefined
				},
				transformRequest: angular.identity
			}).then(function successCallback(response) {
				location.reload();
			}, function errorCallback(response) {
				$scope.bgImageUpdating = false;
				$ngConfirm({
					title: 'Failed',
					content: response.data,
					type: 'red',
					typeAnimated: true,
					buttons: {
						tryAgain: {
							text: 'Ok',
							btnClass: '',
							action: function(){}
						},
					}
				});
			});
		}
	}

	$scope.setDateFormat = function () {
		$http({
			method: 'PUT',
			url: '<?=$LMSUri?>user/set-date-format',
			data: {
				default_date_format: $rootScope.currentUser.default_date_format
			}
		}).then(function successCallback() {
			location.reload();
		});
	};


	$scope.setAccessibleUI = function () {
		$http({
			method: 'PUT',
			url: '<?=$LMSUri?>user/set-accessible-ui',
			data: {
				accessible_ui: $rootScope.currentUser.accessible_ui
			}
		}).then(function successCallback() {
			// location.reload();
		});
	};

	$scope.setColorScheme = function () {
		$http({
			method: 'PUT',
			url: '<?=$LMSUri?>user/set-color-scheme',
			data: {
				color_scheme: $rootScope.currentUser.color_scheme
			}
		}).then(function successCallback() {
			// Update HTML element class dynamically instead of page reload
			var htmlElement = angular.element(document.documentElement);

			// Remove existing theme classes
			htmlElement.removeClass('light-theme dark-theme');

			// Add new theme class based on user preference
			if ($rootScope.currentUser.color_scheme === 'dark') {
				htmlElement.addClass('dark-theme');
			} else {
				htmlElement.addClass('light-theme');
			}

			// Trigger calendar refresh to update event colors with new theme
			$rootScope.$broadcast('themeChanged');
		});
	};

	$scope.initialLoad = true;

	$rootScope.$on('learner-tasks-reset', function() {
		if ($location.$$path.indexOf('learner/tasks') > -1) {
			$scope.setMenuItem($scope.menuItems[3]);
		} else if ($location.$$path.indexOf('learner/resources') > -1) {
			$scope.setMenuItem($scope.menuItems[1]);
		} else  {
			delete $scope.activeMenuItem;
		}
	});

	$rootScope.$on('learner-tasks-learnerresources', function() {
		$scope.reloadLearnerResources = false;
		$scope.setMenuItem($scope.menuItems[1]);
	});
});
