angular.module('lmsApp')
    .controller('ChatBotController', function($scope, $rootScope, $timeout, $sce, chatService) {
        $scope.chatOpen = false;
        $scope.streamingType = 'word';
        $scope.messages = [];
        $scope.userMessage = '';
        $scope.isTyping = false;
        $scope.suggestionsVisible = true;
        $scope.cs = chatService;
        $scope.suggestions = [
            '%%chat_bot__suggestion_1%%',
            '%%chat_bot__suggestion_2%%',
            '%%chat_bot__suggestion_3%%'
        ];

        // $scope.toggleChat = function() {
        //     if ($rootScope.config.sharedClients) {
        //         alert('This chatbot is available only for enterprise clients.');
        //         return;
        //     }
        //     $scope.chatOpen = !$scope.chatOpen;
        //     if ($scope.chatOpen) {
        //         $timeout(() => $scope.scrollToBottom(), 100);
        //     }
        // };

        $scope.sendMessage = function() {
            $scope.suggestionsVisible = true;
            if (!$scope.userMessage.trim()) return;

            const userMessage = $scope.userMessage;
            $scope.messages.push({ sender: 'user', text: userMessage });
            $scope.userMessage = '';
            $scope.isTyping = true;

            $scope.sendStreamingMessage(userMessage);
        };

        $scope.sendStreamingMessage = function(message) {
            const botMessage = {
                sender: 'bot',
                text: '',
                streaming: true,
                trustedHtml: $sce.trustAsHtml('<div class="chat-thinking">Thinking...</div>')
            };
            $scope.messages.push(botMessage);
            $scope.scrollToBottom();

            $scope.cs.streamResponse({
                message,
                streamType: $scope.streamingType,
                onChunk: function(html) {
                    botMessage.trustedHtml = html;
                    $scope.scrollToBottom();
                    $scope.$applyAsync();
                },
                onComplete: function() {
                    botMessage.streaming = false;
                    $scope.isTyping = false;
                    $scope.$applyAsync();
                },
                onError: function(err) {
                    botMessage.streaming = false;
                    $scope.isTyping = false;
                    botMessage.error = true;
                    botMessage.trustedHtml = $sce.trustAsHtml(err);
                    $scope.$applyAsync();
                }
            });
        };

        $scope.scrollToBottom = function() {
            $timeout(() => {
                const el = document.getElementById("chatScroll");
                if (el) el.scrollTop = el.scrollHeight;
            }, 100);
        };

        $scope.sendSuggestion = function(text) {
            $scope.userMessage = text;
            $scope.sendMessage();
        };

        $scope.checkEnter = function(event) {
            if (event.which === 13) $scope.sendMessage();
        };

        $scope.getClassForMessage = function(msg) {
            if (msg.sender === 'user') return 'chat-user';
            if (msg.sender === 'bot' && msg.error) return 'chat-bot-error';
            if (msg.sender === 'bot' && msg.formatted) return 'chat-bot chat-bot-formatted';
            return 'chat-bot';
        };

        $scope.shouldShowHtml = function(msg) {
            return msg.formatted !== false && msg.trustedHtml;
        };
    });
