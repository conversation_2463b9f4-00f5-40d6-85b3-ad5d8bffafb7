angular.module('lmsApp')
.controller('DiskSpaceAuditController', function($scope, $http, $rootScope, $learnerProgrammes, $ngConfirm) {
	$scope.alerts = [];
	$scope.closeAlert = function() {
		$scope.alerts = [];
	};
		$scope.isLoading = false;
		$scope.isLoadingStats = true;
		$scope.isLoadingOrphanedFiles = false;
		$scope.stats = null;
		$scope.data = [];
		$scope.total = 0;
		$scope.refreshTable = 0;
		$scope.orphanedFiles = [];
		$scope.orphanedFilesStats = null;
		$scope.showEvidenceDetails = false;
		$scope.evidenceDataLoaded = false;

		$scope.lp = $learnerProgrammes;

		$scope.init = function() {
			// Ensure loading states are properly initialized
			$scope.isLoading = false;
			// Load disk space statistics
			$scope.loadDiskSpaceStats();
			// Mark page as loaded since we're no longer auto-loading the evidence table
			$rootScope.dashboard.pageLoaded = true;
		};

		$scope.isUpdatingFileSizes = false;
		$scope.updateFileSizesResult = null;

		$scope.loadDiskSpaceStats = function() {
			$scope.isLoadingStats = true;
			$http.get('<?=$LMSUri?>disk-space/stats')
				.then(function(response) {
					$scope.stats = response.data;
					$scope.isLoadingStats = false;
				})
				.catch(function(error) {
					$scope.isLoadingStats = false;
					$scope.alerts.push({
						type: 'danger',
						msg: 'Failed to load disk space statistics: ' + (error.data || 'Unknown error')
					});
				});
		};

		$scope.callServer = function(tableState) {
			// Only load if evidence details are shown
			if (!$scope.showEvidenceDetails) {
				// Return early with empty data if evidence section not visible
				$scope.data = [];
				$scope.total = 0;
				return;
			}

			$scope.tableState = tableState;
			updateReport(tableState, "<?=$LMSUri?>disk-space/list", $scope, $http, function() {
				// Table loaded
			});
		};

		$scope.formatFileSize = function(bytes) {
			if (!bytes || bytes === 0) return '0 B';

			var units = ['B', 'KB', 'MB', 'GB', 'TB'];
			var i = Math.floor(Math.log(bytes) / Math.log(1024));

			return (bytes / Math.pow(1024, i)).toFixed(2) + ' ' + units[i];
		};

		$scope.refreshStats = function() {
			$scope.loadDiskSpaceStats();
		};

		$scope.loadOrphanedFiles = function() {
			$scope.isLoadingOrphanedFiles = true;

			// Clear the current array first to force table refresh
			$scope.orphanedFiles.length = 0;

			$http.get('<?=$LMSUri?>disk-space/orphaned-files')
				.then(function(response) {
					$scope.orphanedFilesStats = response.data;

					// Replace the entire array contents
					$scope.orphanedFiles.length = 0;
					if (response.data.orphaned_files && response.data.orphaned_files.length > 0) {
						$scope.orphanedFiles.push.apply($scope.orphanedFiles, response.data.orphaned_files);
					}

					$scope.isLoadingOrphanedFiles = false;

					// Force scope digest to update the table
					if (!$scope.$$phase) {
						$scope.$apply();
					}
				})
				.catch(function(error) {
					$scope.isLoadingOrphanedFiles = false;
					$scope.alerts.push({
						type: 'danger',
						msg: 'Failed to load orphaned files: ' + (error.data || 'Unknown error')
					});
				});
		};

		$scope.refreshOrphanedFiles = function() {
			$scope.loadOrphanedFiles();
		};

		$scope.viewOrphanedFile = function(file) {
			var viewUrl = '<?=$LMSUri?>disk-space/orphaned-files/view/' + encodeURIComponent(file.file_name);
			window.open(viewUrl, '_blank');
		};

		$scope.confirmDeleteOrphanedFile = function(file) {
			$ngConfirm({
				title: 'Confirm File Deletion',
				columnClass: 'col-md-8 col-md-offset-2',
				content: '<div class="alert alert-danger"><strong>Warning:</strong> This action is <strong>irreversible</strong>!</div>' +
					'<p>You are about to permanently delete the following orphaned file from the system:</p>' +
					'<div class="well">' +
					'<strong>File:</strong> <code style="word-break: break-all; white-space: normal;">' + file.file_name + '</code><br>' +
					'<strong>Size:</strong> ' + file.file_size_formatted + '<br>' +
					'<strong>Type:</strong> ' + file.file_type + '<br>' +
					'<strong>Modified:</strong> ' + file.modified_date +
					'</div>' +
					'<p>This file will be <strong>permanently removed</strong> from the server and cannot be recovered.</p>' +
					'<p>Are you sure you want to continue?</p>',
				buttons: {
					cancel: {
						text: 'Cancel',
						btnClass: 'btn-default'
					},
					deleteFile: {
						text: 'Yes, Delete Permanently',
						btnClass: 'btn-danger',
						action: function() {
							$scope.deleteOrphanedFile(file);
						}
					}
				}
			});
		};

		$scope.deleteOrphanedFile = function(file) {
			file.deleting = true;

			$http.delete('<?=$LMSUri?>disk-space/orphaned-files/' + encodeURIComponent(file.file_name))
				.then(function(response) {
					$scope.alerts.push({
						type: 'success',
						msg: 'File "' + file.file_name + '" has been deleted successfully.'
					});

					// Refresh the entire orphaned files list
					$scope.loadOrphanedFiles();
				})
				.catch(function(error) {
					file.deleting = false;

					var errorMsg = 'Failed to delete file';
					if (error.data && error.data.error) {
						errorMsg += ': ' + error.data.error;
					}

					$scope.alerts.push({
						type: 'danger',
						msg: errorMsg
					});
				});
		};

		$scope.updateFileSizes = function() {
			$scope.isUpdatingFileSizes = true;
			$scope.updateFileSizesResult = null;

			$http.post('<?=$LMSUri?>disk-space/update-file-sizes')
				.then(function(response) {
					$scope.isUpdatingFileSizes = false;
					$scope.updateFileSizesResult = response.data;

					// Show success alert
					$scope.alerts.push({
						type: 'success',
						msg: response.data.message
					});

					// Refresh the statistics to show updated file sizes
					$scope.loadDiskSpaceStats();

					// If we're on the detailed table view, refresh it
					if ($scope.evidenceDataLoaded) {
						$scope.refreshTable = $scope.refreshTable + 1;
					}
				})
				.catch(function(error) {
					$scope.isUpdatingFileSizes = false;

					var errorMsg = 'Failed to update file sizes';
					if (error.data && error.data.error) {
						errorMsg = error.data.error;
					}

					$scope.alerts.push({
						type: 'danger',
						msg: errorMsg
					});
				});
		};

		$scope.toggleEvidenceDetails = function(directoryName) {
			if (directoryName.toLowerCase() === 'evidence') {
				$scope.showEvidenceDetails = !$scope.showEvidenceDetails;

				// Load evidence data only when first expanded
				if ($scope.showEvidenceDetails && !$scope.evidenceDataLoaded) {
					$scope.evidenceDataLoaded = true;

					// Create a basic table state and load data
					var initialTableState = {
						pagination: {
							start: 0,
							number: 25
						},
						sort: {
							predicate: 'created_at',
							reverse: true
						},
						search: {}
					};

					$scope.callServer(initialTableState);
				}
			}
		};

		$scope.isEvidenceDirectory = function(directoryName) {
			return directoryName.toLowerCase() === 'evidence';
		};

		$scope.refreshLearningModuleFiles = function() {
			if (!$scope.evidenceDataLoaded) {
				return; // Only refresh if evidence section is loaded
			}

			// Simple refresh using counter increment
			$scope.refreshTable = $scope.refreshTable + 1;
		};

		$scope.viewLearningModuleFile = function(file) {
			var viewUrl = '<?=$LMSUri?>disk-space/learning-module-files/view/' + file.id;
			window.open(viewUrl, '_blank');
		};

		$scope.confirmDeleteLearningModuleFile = function(file) {
			$ngConfirm({
				title: 'Confirm File Deletion',
				columnClass: 'col-md-8 col-md-offset-2',
				content: '<div class="alert alert-danger"><strong>Warning:</strong> This action is <strong>irreversible</strong>!</div>' +
					'<p>You are about to permanently delete the following learning module evidence file:</p>' +
					'<div class="well">' +
					'<strong>File:</strong> <code style="word-break: break-all; white-space: normal;">' + file.file_name + '</code><br>' +
					'<strong>Size:</strong> ' + $scope.formatFileSize(file.file_size) + '<br>' +
					'<strong>Type:</strong> ' + file.file_type + '<br>' +
					'<strong>Module:</strong> <span style="word-break: break-word;">' + file.module_name + '</span><br>' +
					'<strong>Uploaded by:</strong> ' + file.uploaded_by + '<br>' +
					'<strong>Upload Date:</strong> ' + file.created_at +
					'</div>' +
					'<p>This file will be <strong>permanently removed</strong> from the server and cannot be recovered.</p>' +
					'<p>Are you sure you want to continue?</p>',
				buttons: {
					cancel: {
						text: 'Cancel',
						btnClass: 'btn-default'
					},
					deleteFile: {
						text: 'Yes, Delete Permanently',
						btnClass: 'btn-danger',
						action: function() {
							$scope.deleteLearningModuleFile(file);
						}
					}
				}
			});
		};

		$scope.deleteLearningModuleFile = function(file) {
			file.deleting = true;

			$http.delete('<?=$LMSUri?>disk-space/learning-module-files/' + file.id)
				.then(function(response) {
					$scope.alerts.push({
						type: 'success',
						msg: 'File "' + file.file_name + '" has been deleted successfully.'
					});

					// Refresh only the Learning Module Evidence Files table
					if ($scope.evidenceDataLoaded) {
						$scope.refreshTable = $scope.refreshTable + 1;
					}
				})
				.catch(function(error) {
					file.deleting = false;

					var errorMsg = 'Failed to delete file';
					if (error.data && error.data.error) {
						errorMsg += ': ' + error.data.error;
					}

					$scope.alerts.push({
						type: 'danger',
						msg: errorMsg
					});
				});
		};
	});