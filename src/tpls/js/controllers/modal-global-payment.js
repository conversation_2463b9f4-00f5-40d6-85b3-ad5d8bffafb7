angular.module('lmsApp').controller('modalGlobalPayment', function($http, $scope, $rootScope, $globalPaymentService, $uibModalInstance,data, CourseBasketService) {
  
  $scope.passData = data;
  
  
  $scope.countries = [];
  if($scope.passData.type=="schedules"){
    $scope.passData.calendarEvent = data.resData;
  }else if($scope.passData.type=="learning_modules"){
    $scope.passData.calendarEvent = data.resData;
  }
  else if($scope.passData.type=="programme"){
    $scope.passData.calendarEvent = data.resData;
    $scope.passData.calendarEvent.link = "learner/resources";
  }
  $http({
    method:'GET',
    url:'https://gist.githubusercontent.com/kpanuragh/aa7f36d9625dbd1e8247b93dbcea514c/raw/181d1c1f72c59200d62aebbd36df24a6b2ce6a27/CountryCodes.json'
  }).then(res=> {
    $scope.countries = res.data;

    $scope.changeCountry = () => {
      let selected = $scope.countries.find(e => {
        return e.code == $scope.billingData.country;
      })
      $scope.billingData.phone_code = selected.dial_code.replace('+', '');
    }
    $scope.coupons = $scope.passData.coupons;
    $scope.alerts = [];
    let country = $rootScope.currentUser?.country?.code || 'GB';
    if (country) {
      let checkCountry = $scope.countries.find(e => e.code == country);
      if (!checkCountry) {
        country = "GB";
      }
    }
    let city = $rootScope.currentUser?.city?.name || '';
    let address1 = $rootScope.currentUser.AddLine1 || '';
    let address2 = $rootScope.currentUser.AddLine2 || '';
    let postcode = $rootScope.currentUser.Postcode || '';
    $scope.billingData = {email: $rootScope.currentUser.email, country: country,city:city,address1:address1,address2:address2,postal_code:postcode, ...$scope.passData};
    $scope.changeCountry();
    $scope.country = [];
    $scope.billing_address = true;
    $scope.enableLoading = false;
    $rootScope.$on('enablePaymentLoading', function (event, status) {
      
      setTimeout(() => {
        $scope.$apply(function () {
          $scope.enableLoading = status;
        });
      });
    });

    $globalPaymentService.getCountry((country) => {
      $scope.country = Object.keys(country).map((key) => {
        return {
          name: key,
          code: country[key]
        }
      })
      $globalPaymentService.getBillingAddress($scope.passData.user_id,(data) => {
        if (data) {
          $scope.billingData = {
            email: data.email,
            address1: data.address_1,
            address2: data.address_2,
            city: data.city,
            country: data.country,
            postal_code: data.postcode,
            phone_code: data.phone.split('|')[0],
            phone_number: data.phone.split('|')[1],
            type: $scope.passData.type,
            type_id: $scope.passData.type_id,
            user_id: $scope.passData.user_id
          };
        }
      });
    });

    $scope.closeAlert = function () {
      $scope.alerts = [];
    };
    $scope.closeModal = function () {
      $uibModalInstance.close();
    };
    $scope.payment = function () {
      $scope.$broadcast("show-errors-check-validity");
      if ($scope.paymentDetails.$valid) {
        $scope.billing_address = false;
        $scope.billingData.coupons = $scope.coupons; //passing coupons data
        $scope.billingData.user_id = $scope.passData.user_id;
        if ($scope.passData.multiple) {
          $scope.billingData.cart = $scope.passData.cart;
          CourseBasketService.globalMakePayment($scope.billingData, false, $scope)
        } else {
          $globalPaymentService.globalPaymentEngine($scope.billingData, false, $scope)
        }
      }
    }
  });
})
