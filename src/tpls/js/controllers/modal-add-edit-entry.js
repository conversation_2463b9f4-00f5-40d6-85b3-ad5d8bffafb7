angular.module('lmsApp')
	.controller('ModalAddEditEntry', function ($scope, data, $uibModalInstance, $http, $rootScope, $timeout, $fileService, $filter, $ajaxActions, $learningService, dynamicLoadingService) {
		$scope.passData = data;
        $scope.alerts = [];
        $scope.accordions = {settings:false,integration:false}
        $scope.companyConfiguration = {
            settings:1,
            integration:1
        };
        $scope.toggleSection = ($event, element_id) => {
          $event.stopPropagation();
          if ($scope.transitionInProgress) return;

          $scope.transitionInProgress = true;
          var element = document.getElementById(element_id);

          let tmp = element.style["max-height"];
          element.style["padding-top"] = "2rem";
          element.style["padding-bottom"] = "2rem";
          element.style["max-height"] = "fit-content";

          var thisHeight = element.offsetHeight;
          element.style["padding-top"] = "0rem";
          element.style["padding-bottom"] = "0rem";
          element.style["max-height"] = tmp;

          $scope.accordions[element_id.split("-")[0]] = !(
            $scope.accordions[element_id.split("-")[0]] || false
          );

		  dynamicLoadingService.get('companies', '<?=$LMSUri?>company/all').then(function(data) {
			$rootScope.companies = data;
		  });

          $timeout(() => {
            if ($scope.accordions[element_id.split("-")[0]]) {
              element.style["max-height"] = thisHeight + 100 + "px";
              element.style["padding-top"] = "2rem";
              element.style["padding-bottom"] = "2rem";
              element.style["transform"] = "scaleY(1)";
              element.style["opacity"] = "1";
              $timeout(() => {
                element.style["max-height"] = "fit-content";
                $scope.transitionInProgress = false;
              }, 500);
            } else {
              element.style["max-height"] = thisHeight + 100 + "px";
              $timeout(() => {
                element.style["max-height"] = "0px";
                element.style["padding-top"] = "0rem";
                element.style["padding-bottom"] = "0rem";
                element.style["transform"] = "scaleY(0)";
                element.style["opacity"] = "0";
                $scope.transitionInProgress = false;
              }, 20);
            }
          }, 20);
        };
        $rootScope.$on("company_configuration",function(event,data){
            $scope.companyConfiguration[data.type]=data.data;
        })
        $scope.disableField = function (field={}) {
          if (field.disable) {
            return field.disable;
          }
          if (field.disableFun) {
            return field.disableFun($scope.entryData,field);
          }
          if ($scope.passData.disabledFun) {
            return $scope.passData.disabledFun($scope.entryData,null);
          }
          return false;
        };

		$scope.closeAlert = function() {
			$scope.alerts = [];
		};
		$scope.closeModal = function () {
			$uibModalInstance.close();
		};
		$scope.cancelModal = function () {
			$uibModalInstance.dismiss('cancel');
		};

		$scope.removeEntry =  function (index, list) {
			list.splice(index, 1);
		};
		$scope.isArray = angular.isArray;

		$scope.imgPreviewStyles = {
			'display': 'flex',
			'justifyContent': 'flexStart',
			'alignItems': 'start',
			'flexDirection': 'column',
			'gap': '1rem',
			'marginTop': '1rem'
		};

		$scope.marginTopNone = {
			marginTop: 0
		};

		////////////////////////////////////////////////////////////////////////////

		$scope.files = [];
		$scope.showTextItem = null;
        $scope.entryData = {"custom-field":{}};
		$scope.fs = $fileService;
		$scope.filteredFields = [];
		$scope.dataLoading = false;
		$scope.entryData.self_enroll = $rootScope.config.sharedClients
		if ($rootScope.config.sharedClients === false) {
			$scope.entryData.self_enroll_access = 0;
		}
		if ($rootScope.config.sharedClients === false) {
			$scope.entryData.editing_access = 0;
		}
		$scope.ls = $learningService;

		$scope.runFilters = function () {
			$scope.filteredFields = $filter('filter')($scope.passData.fields, function(field) {
				return field.enabled &&
					(
						(
							field.edit &&
								(
									$scope.passData.id ||
									$scope.entryData.id
								)
						) ||
						!field.edit
					)
				;
			});
		};

		// Reset!
		if ($scope.passData.file_service) {
			$scope.fs.showForm = false;
			$scope.fs.upload_files = [];
			$scope.fs.setConfig(
				{
					table_name: false,
					table_row_id: false,
					list: false
				}
			);
		}

		$scope.getEntry = function () {
			$http({
				method: 'GET',
				url: "<?=$LMSUri?>" + $scope.passData.slug + "/" + $scope.passData.id,
			}).then(function successCallback(response) {
				$scope.entryData = response.data;
				$scope.prevSelfEnroll  =  $scope.entryData.self_enroll;

				// Set up file service
				if ($scope.passData.file_service) {
					$scope.fs.setConfig(
						{
							table_name: $scope.passData.file_service_table_name || $scope.passData.slug,
							table_row_id: $scope.passData.id,
							list: $scope.entryData.files
						}
					);
				}
				// Format conversion, if needed!
				angular.forEach($scope.passData.fields, function(field) {
					var i, j;
					if (
						field.type === 'date' ||
						field.type === 'datetime'
					) {
						if (response.data[field.key]) {
							$scope.entryData[field.key] = getJsDate(response.data[field.key]);
						} else {
							$scope.entryData[field.key] = null;
						}
					}
					if (field.type === 'list') {
						if (!response.data[field.key]) {
							response.data[field.key] = [];
						}
					}
					if (field.type ===  'multi_select'){
						$scope.entryData[field.key]=[];
						// for (x in field.list) {
						// 	field.list[x].ticked=false
						// }
						// $scope.entryData[field.key]=[];
							for (x in field.list) {
								field.list[x].ticked=false
							}
						for (x in field.list) {
								$scope.entryData.form_work_flow.map(function(e){
									if (field.list[x].id == e.form_workflow_id) {

										field.list[x].ticked=true
									}
								 });
						}
					}
					// Will reuse multi_select, just something that others can use
					if (field.type === 'multi-select') {
						for (i = 0; i < field.list.length; i++) {
							field.list[i].ticked = false;
							if (
								$scope.entryData[field.entryList] &&
								angular.isArray($scope.entryData[field.entryList]) &&
								$scope.entryData[field.entryList].length > 0
							) {
								for (j = 0; j < $scope.entryData[field.entryList].length; j++) {
									// ID field if needed could be changed as definition as well
									if ($scope.entryData[field.entryList][j].id === field.list[i].id) {
										field.list[i].ticked = true;
									}
								}
							}
						}
					}
					if (
						field.encode_on_load &&
						response.data[field.key]
					) {
						response.data[field.key] = JSON.stringify(response.data[field.key]);
					}
					if (field.extended) {
						$scope.entryData[field.key] = $scope.entryData.extended[field.key];
					}
					if (
						(
							field.default ||
							field.default === 0
						) &&
						(
							field.default_on_empty ||
							field.default_on_empty === 0
						) &&
						(
							$scope.entryData[field.key] === '' ||
							$scope.entryData[field.key] === null
						)
					) {
						$scope.entryData[field.key] = field.default;
					}
				});
				$scope.$broadcast("entry-retrieved");
			});
			$scope.runFilters();
		};


		// If entry exists, retrieve it
		if ($scope.passData.id) {
			$scope.editEntry = true;
			$scope.getEntry();
		} else {
			angular.forEach($scope.passData.fields, function(field) {
				if (
					field.cast &&
					field.cast === 'array'
				) {
					$scope.entryData[field.key] = [];
				}
				if (
					field.default ||
					field.default === 0
				) {
					$scope.entryData[field.key] = field.default;
				}
				if (field.type ===  'multi_select'){
					$scope.entryData[field.key]=[];
					for (x in field.list) {
						field.list[x].ticked=false
				    }
				}
			});
			$scope.runFilters();
		}

		$scope.update_method = $scope.passData.update_method || 'PUT';

		$scope.addDataToForm = function(field, data) {
			var field_key = field.key;
			if ($scope.update_method === 'PUT') {
				if ($scope.formData.extended) {
 					$scope.formData.extended = {};
				}
				if (field.extended) {
					$scope.formData.extended[field_key] = data;
				} else {
					$scope.formData[field_key] = data;
				}
			} else {
				if (field.extended) {
					field_key = 'extended[' + field_key + ']';
				}
				$scope.formData.append(field_key, data);
			}
		};

		// check if field needs to be shown
		$scope.includeLogic = function (field) {
			var response = true,
				i
			;

			//required_boolean will indicate that to show a field, another field must be boolean and set to true.
			if (
				field &&
				field.required_boolean
			) {
				for (i = $scope.filteredFields.length - 1; i >= 0; i--) {
					if ($scope.filteredFields[i].key === field.required_boolean) {
						if (
							$scope.entryData[$scope.filteredFields[i].key] !== true ||
							!$scope.filteredFields[i].visible
						) {
							response = false;
						}
					}
				}
			}

			field.visible = response;

			return response;
		};

		const add_object_to_formdata=function(key,item,arr){
			if(typeof(item)==='object' && item && item.constructor===Array){
				for(var i=0;i<item.length;i++){
					var item2=item[i];
					var key2=key+'[' + i +']';
					if(arr){
						key2=key+'[' + key2 +']';
					}
					add_object_to_formdata(key2,item2,true);
				}
			}else if(typeof(item)==='object' && item){
				for ( var key2 in item ) {
					var item2=item[key2];
					if(arr){
						key2=key+'[' + key2 +']';
					}
					add_object_to_formdata(key2,item2);
				}
			}else{
				$scope.formData.append(key,item);
			}
		};

		$scope.objectToFormData = (obj, formData = new FormData(), parentKey = '')=>{
			for (const key in obj) {
				if (obj.hasOwnProperty(key)) {
					const value = obj[key];
					const formKey = parentKey ? `${parentKey}[${key}]` : key;

					if (value instanceof File) {
						// If the value is a File (e.g., for file uploads), append it directly
						formData.append(formKey, value);
					} else if (typeof value === 'object' && value !== null) {
						// If the value is an object, recursively call objectToFormData
						$scope.objectToFormData(value, formData, formKey);
					} else {
						// Otherwise, append the key-value pair
						formData.append(formKey, value);
					}
				}
			}

			return formData;
		};

		$scope.save = function (close) {

			$scope.dataLoading = true;
			var data_copy;
			var multi_select_ids=[];

			close = close || false;
			$scope.closeModalAfterInsert = close;
			$scope.alerts = [];
			if ($scope.entryForm.$valid) {
				$scope.showErrors = false;

				// Insert/add method is allways POST
				if (!$scope.entryData.id) {
					$scope.update_method = 'POST';
				}

				if ($scope.update_method === 'PUT') {
					$scope.contentType = 'application/json';
					$scope.formData = {};
				} else {
					$scope.formData = new FormData();
					$scope.contentType = undefined;
				}

				if ($scope.entryData.id) {
					$scope.addDataToForm({key:'id'}, $scope.entryData.id);
				}


				// Append fields to form if filled and convert to specific forms if needed.
				angular.forEach($scope.passData.fields, function(field) {
					if (
						(
							$scope.entryData[field.key] ||
							$scope.entryData[field.key] === 0
						) ||
						$scope.files[field.key] ||
						field.extended
					) {
						switch(field.type) {
							case 'file_image':
								if (
									$scope.files[field.key] !== null &&
									$scope.files[field.key] !== undefined
								) {
									$scope.addDataToForm(field, $scope.files[field.key]);
								}
							break;
							case 'file':
								if (
									$scope.files[field.key] !== null &&
									$scope.files[field.key] !== undefined
								) {
									$scope.addDataToForm(field, $scope.files[field.key]);
								}
							break;
							case 'date': // Convert data object to string.
							case 'datetime':
								if ($scope.entryData[field.key] instanceof Date) {
									data_copy = new Date($scope.entryData[field.key].getTime());
									data_copy = doToStr(data_copy);
									$scope.addDataToForm(field, data_copy);
								} else {
									$scope.addDataToForm(field.key, $scope.entryData[field.key]);
								}
							break;
                            case 'custom-field':
                                $scope.objectToFormData($scope.entryData['custom-field'],$scope.formData,'custom-field');
                                break;
							case 'multi-select':
								multi_select_ids = [];
								for (let i = 0; i < $scope.entryData[field.key].length; i++) {
									multi_select_ids.push($scope.entryData[field.key][i].id);
								}
								$scope.addDataToForm(field, multi_select_ids);
								break;
							case 'multi_select':
								multi_select_ids = [];
								for (let i = 0; i < $scope.entryData[field.key].length; i++) {
									multi_select_ids.push($scope.entryData[field.key][i].id);
								}
								// $scope.entryData[field.key]=[];
								$scope.addDataToForm(field,multi_select_ids);
								//break;
							case '':
								let data ={[field.key]:$scope.entryData[field.key]};
								add_object_to_formdata(field.key,data);
								break;
							default:
								if (field.encode) {
									$scope.addDataToForm(field, JSON.stringify($scope.entryData[field.key]));
								} else {
									$scope.addDataToForm(field, $scope.entryData[field.key]);
								}

						}
					}
				});
				if ($scope.entryData.custom_field){
					$scope.objectToFormData($scope.entryData.custom_field,$scope.formData,'custom_field');
				}

				if ($scope.entryData.custom_field) {
					$scope.objectToFormData($scope.entryData.custom_field, $scope.formData, 'custom_field');
				}

				if ($scope.entryData.companies) {
					add_object_to_formdata('companies', $scope.entryData.companies);

				}
				if ($scope.entryData.edit_access_companies) {
					add_object_to_formdata('edit_access_companies', $scope.entryData.edit_access_companies);
				}



				$scope.httpParameters = {
					method: $scope.update_method,
					url: "<?=$LMSUri?>" + $scope.passData.slug + "/" + ($scope.entryData.id || 'new'),
					data: $scope.formData,
					headers: {'Content-Type': $scope.contentType}
				};

				if ($scope.update_method === 'POST') {
					$scope.httpParameters.transformRequest = angular.identity;
				}

				$http($scope.httpParameters).then(function successCallback(response) {
					$scope.alerts = [{ type: 'success', msg: $scope.passData.name + ' details have been saved.' }];
					$rootScope.$broadcast("entry-refresh", response.data);
					if ($scope.entryData.id) {
						$rootScope.$broadcast("entry-refresh-update", response.data);
					} else {
						$rootScope.$broadcast("entry-refresh-new", response.data);
					}

					// Will reload some global calls in memory
					// Define a mapping between slugs and their corresponding AJAX requests
					const ajaxRequestsMapping = {
						'company': [['<?=$LMSUri?>company/all', 'companies']],
						'department': [['<?=$LMSUri?>department/all', 'departments']],
						// Add more cases here as needed
					};

					// Retrieve the slug
					const slug = $scope.passData.slug;

					// Check if the slug exists in the mapping
					if (ajaxRequestsMapping.hasOwnProperty(slug)) {
						// Perform the AJAX request
						$ajaxActions.multiGetRequests(ajaxRequestsMapping[slug], $rootScope);
					}

					if (
						$scope.passData.file_service &&
						$scope.fs.upload_files.length > 0
					) {
						$scope.fs.setConfig(
							{
								table_name: $scope.passData.file_service_table_name || $scope.passData.slug,
								table_row_id: response.data,
								list: $scope.entryData.files
							}
						);

						// Upload files!
						$scope.fs.upload();
						$scope.postInsertResponseData = response.data;
					} else {
						if (
							!$scope.entryData.id &&
							!response.data
						) {
							$scope.closeModalAfterInsert = true;
						}
						if (
							!$scope.entryData.id &&
							$scope.passData.keepOpenModalAfterInsert
						) {
							$scope.closeModalAfterInsert = false;
						}
						$scope.postInsert($scope.entryData.id || response.data);
					}

				}, function errorCallback(response) {
					$scope.alerts = [{ type: 'danger', msg: $scope.passData.name + ' details have not been saved: ' + response.data }];
					$scope.dataLoading = false;
				});
			} else {
				$scope.showErrors = true;
				$scope.alerts = [{ type: 'danger', msg: 'Please correct the errors.' }];
				$timeout(function () {
					$('[uib-modal-window]').animate({
						scrollTop: $('[uib-alert]').offset().top
					}, 300);
				}, 0);
				$scope.dataLoading = false;
			}
		};

		$scope.postInsert = function (id) {
			// Load new resource!
			if (id) {
				id = parseInt(id, 10);
			}

			if (
				!$scope.closeModalAfterInsert
			) {
				$scope.passData.id = id;
				$scope.editEntry = true;
				$scope.getEntry();
			} else {
				$timeout(function () {
					$uibModalInstance.close();
				}, 300);
			}
			$scope.dataLoading = false;
		};

		$scope.deleteFile = function (file) {
			$http({
				method: 'DELETE',
				url: "<?=$LMSUri?>" + $scope.passData.slug + "/" + file.key + "/" + $scope.entryData.id
			}).then(function successCallback() {
				$scope.alerts = [{ type: 'success', msg: file.name + ' is deleted!' }];
				delete $scope.entryData[file.key];
			}, function errorCallback() {
				$scope.alerts = [{ type: 'danger', msg: file.name + ' has not been deleted!' }];
			});
		};

		$scope.$on('files-refresh', function() {
			// If files are added with existing company, refresh, if company was added with files, close instance!
			if ($scope.passData.id) {
				$scope.getEntry();
			} else {
				$scope.postInsert($scope.postInsertResponseData);
			}
		});

		$scope.addItemToList = function (item, list) {
			var i,
				exists = false
			;
			list = list || [];
			if (item) {
				for (i = list.length - 1; i >= 0; i--) {
					if (list[i].id === item.id) {
						exists = true;
					}
				}
				if (!exists) {
					list.push(item);
				}
			}
		};

		$scope.removeItemFromList = function (item, list) {
			var i
			;
			for (i = list.length - 1; i >= 0; i--) {
				if (list[i].id === item.id) {
					list.splice(i, 1);
				}
			}
		};

		$scope.moveItemIndexInList = function (index, list, position) {
			var move_to = list[index - position],
				move_from = list[index]
			;

			list[index - position] = move_from;
			list[index] = move_to;
		};

		$scope.isItemInList = function (item, list, id) {
			var i,
				response = false
			;
			if (
				item &&
				list
			) {
				for (i = list.length - 1; i >= 0; i--) {
					if (list[i][id] === item[id]) {
						response = true;
					}
				}
			}

			return response;
		};

		/*
			Some custom functions
		*/

		// Check if list2 exists in list1 by comparing id property, then set isSelected for list1
		$scope.compareAndSelectLists = function(list1, list2, id) {
			var
				i,
				j
			;
			for (i = list1.length - 1; i >= 0; i--) {
				list1[i].isSelected = false;
				for (j = list2.length - 1; j >= 0; j--) {
					if (list1[i][id] === list2[j][id]) {
						list1[i].isSelected = true;
					}
				}
			}
		};

		$scope.$watch('entryData.learner_access', function (newValue) {
			if (newValue == 1) {
				$scope.entryData.open_in_events_only = 1;
				$scope.entryData.self_enroll = 0;
			} else if (newValue == 2) {
				$scope.entryData.self_enroll = 1;
				$scope.entryData.open_in_events_only = 0;
				if ($scope.entryData.hasOwnProperty('id') && $scope.prevSelfEnroll == 0) {
					$scope.entryData.self_enroll_access = $rootScope.config.sharedClients ==  false ? 0 : 1;
				}
			} else if (newValue == 3) {
				$scope.entryData.self_enroll = 0;
				$scope.entryData.open_in_events_only = 0;
			}
		});

		$scope.changeEvent= function (val,list){
			if (!val) {
				$scope.showTextItem = null;
			}
			angular.forEach(list, function (p) {
				if (p.id === val) {
					$scope.showTextItem = p.description;
				}

			});
		};

		$scope.searchChildrenForName = function(id, list) {
			var i,
				response = false
			;
			for (i = list.length - 1; i >= 0; i--) {
				if (response) {
					break;
				}
				if (list[i].id === id) {
					response = list[i].name;
				} else if (
					list[i].children &&
					angular.isArray(list[i].children) &&
					list[i].children.length > 0
				) {
					response = $scope.searchChildrenForName(id, list[i].children);
				}
			}
			return response;
		};

		// If clean up needs to be done!
		/*
		$scope.$on("$destroy", function() {
			console.log('destroy!');
		});
		*/
        $scope.getAccessToken = function (){
            if($scope.entryData.access_token){
                $http({
                    url:'<?=$LMSUri?>company/verify/'+$scope.passData.id,
                    method:'delete'
                }).then(res=>{
                    $scope.entryData.access_token = "";
                    $scope.entryData.refresh_token = "";
                    $scope.alerts.push({msg:'Unauthorized successfully'});
                }).catch(error=>{
                    $scope.alerts.push({msg:"Something went wrong please try again"});
                })
            }else{
				$http({
					url: '<?=$LMSUri?>company/verify?company_id='+$scope.passData.id,
					method:'GET'
				}).then(res=>{
					let win = window.open(res.data, 'Popup', 'width=600,height=400');
                    let timer = setInterval(function() {
                        if(win.closed) {
                            $scope.entryData.access_token=true;
						    $scope.alerts = [{msg:"Authorized successfully!",type:"success"}];
                            $scope.$digest();
                            clearInterval(timer);
                        }
                    }, 1000);
					}).catch(error=>{
					$scope.alerts.push({msg:'Failed to retrive data',type:'danger'});
				})
            }
        }

		$scope.accordions = {
			mandatory: true
		};

		$scope.toggleSection = ($event, element_id) =>
		{
			$event.preventDefault()
			$event.stopPropagation();
			if ($scope.transitionInProgress) return;

			$scope.transitionInProgress = true;
			var element = document.getElementById(element_id);

			let tmp = element.style['max-height'];
			element.style['padding-top'] = '1rem';
			element.style['padding-bottom'] = '2rem';
			element.style['max-height'] = 'fit-content';

			var thisHeight = element.offsetHeight;
			element.style['padding-top'] = '0rem';
			element.style['padding-bottom'] = '0rem';
			element.style['max-height'] = tmp;

			$scope.accordions[element_id.split('-')[0]] = !($scope.accordions[element_id.split('-')[0]] || false);

			$timeout(()=>{
				if ($scope.accordions[element_id.split('-')[0]])
				{
					element.style['max-height'] = thisHeight+1000+'px';
					element.style['padding-top'] = '1rem';
					element.style['padding-bottom'] = '2rem';
					element.style['transform'] = 'scaleY(1)';
					element.style['opacity'] = '1';
					$timeout(()=>{
						element.style['max-height'] = 'fit-content';
						$scope.transitionInProgress = false;
					}, 500);
				}
				else
				{
					element.style['max-height'] = thisHeight+1000+'px';
					$timeout(()=>{
						element.style['max-height'] = '0px';
						element.style['padding-top'] = '0rem';
						element.style['padding-bottom'] = '0rem';
						element.style['transform'] = 'scaleY(0)';
						element.style['opacity'] = '0';
						$scope.transitionInProgress = false;
					}, 20);
				}
			}, 20);
		};


	})
;
