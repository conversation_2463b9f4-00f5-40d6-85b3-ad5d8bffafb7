angular.module('lmsApp').controller('learnerSearchBar', function ($scope, $rootScope, $routeParams, $location, smoothScroll, $timeout, dynamicLoadingService, chatService, $sce) {
	$scope.updateType = function (type) {
		$scope.selectedType = type;
		$rootScope.$broadcast('resources-search-type', type);
	};

	$scope.updateResourceSearch = function () {
		$rootScope.$broadcast('resources-search-string', $scope.resourceSearch);
	};

	dynamicLoadingService.get('types', '<?=$LMSUri?>learning/types/all').then(function(data) {
		$rootScope.types = data;
	});


	/*
		search query can be passed as parameter "sq" in URL.
		https://openelms/app/learner/resources?sq=13587
	*/
	$scope.$on('resource-list-retrieved', function (event, args) {
		$timeout(function () {
			if ($location.search().sq) {
				$scope.resourceSearch = $location.search().sq;
				$scope.updateResourceSearch();
			}
		}, 500);
	});

	$rootScope.showingAllAIResults = false;
	$rootScope.isAISearchInProgress = false;

	$scope.submitSearch = function () {
		if (
			$scope.resourceSearch &&
			!$routeParams.resource &&
			$location.$$path !== '/personal-knowledge-file'
		) {
			$scope.updateResourceSearch();
			$rootScope.temporaryResourceSearch = $scope.resourceSearch;
			$location.path('learner/resources');
		}
		// if (document.querySelector('.learner-resource__browse')) {
		// 	smoothScroll(document.querySelector('.learner-resource__browse'));
		// }
		 window.scrollTo({
			top: 0,
			behavior: 'smooth'
		});

		if (!$scope.isAISearchInProgress) {
     		$scope.isAISearchInProgress = true;
		}

		// Only call AI search on Enter key and clicking search icon, not typing
        $scope.callAISearch($scope.resourceSearch)
	};

	

	$scope.callAISearch = function (query) {

		if (!$rootScope.config.isLearningAI) {
			return;
		}

		if ($rootScope.config.isLearningAI && $rootScope.config.sharedClients) {
			$rootScope.showAIResults = true
			$rootScope.aiResults = [{
				title: 'AI Error',
				trustedHtml: "This chatbot tells learners about their organisation from the learning materials within the system. This is an add-on available to enterprise users only."
			}];
			return;
		}

		if ($rootScope.config.isLearningAI && !$rootScope.config.sharedClients) {
			$rootScope.showAIResults = true
			if (!query || query.trim() === '') {
				return;
			}
			let started = false;
			$rootScope.aiResults = [{
				title: 'AI Suggestion',
				trustedHtml: $sce.trustAsHtml(`
					<div class="chat-thinking">
						Thinking<span class="chat-dots">
							<span>.</span><span>.</span><span>.</span>
						</span>
					</div>
				`)
			}];
			$rootScope.aiLoading = true;
			chatService.streamResponse({
				message: query,
				streamType: 'word',

				onChunk: function (html) {
					// Replace "Thinking..." with real AI content once stream starts
					if (!started && html && html.length > 0) {
						started = true;
					}
					$rootScope.aiResults[0].trustedHtml = html;
					$scope.$applyAsync();
				},

				onComplete: function () {
					$rootScope.aiLoading = false;
					$scope.$applyAsync();
				},

				onError: function (err) {
					$rootScope.aiResults = [{
						title: 'AI Error',
						trustedHtml: $sce.trustAsHtml(err)
					}];
					$rootScope.aiLoading = false;
					$scope.$applyAsync();
				}
			});
		}
};
	$rootScope.toggleShowAllAIResults = function () {
		$rootScope.showingAllAIResults = !$rootScope.showingAllAIResults;
	};
	$rootScope.uploadResourceToolTip = true;

	$scope.handleSearchKeyPress = function (event) {
		if (event.which === 13) {
			event.preventDefault();
			$scope.submitSearch();
		}
	}
});