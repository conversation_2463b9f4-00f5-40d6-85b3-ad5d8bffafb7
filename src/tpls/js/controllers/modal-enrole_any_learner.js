angular.module('lmsApp').controller('ModalEnrolAnyLearner', function ($http, $scope, $rootScope, $uibModalInstance, $paymentGatewayService, data, $location,$ngConfirm, $confirm, $learnerOperations, $globalPaymentService, $stripeService, $pay360Service, $govUKPayService, CourseBasketService) {
	$scope.isMultiSelectShoppingBasket = $rootScope.config.isMultiSelectShoppingBasket;
	$scope.passData = data;
    if($scope.passData.calendarEvent && !$scope.passData.calendarEvent.start_date_time && $scope.passData.calendarEvent.startsAt){
        $scope.passData.calendarEvent.start_date_time = $scope.passData.calendarEvent.startsAt;
	}
    if($scope.passData.calendarEvent && $scope.passData.calendarEvent.completion_status=='waiting list'){
        $learnerOperations.showCancellPopup($scope.passData.calendarEvent);
        $uibModalInstance.close();
    }
    if(data?.calendarEvent?.venue_deatils)
    {
        $scope.passData.calendarEvent.venueDetails = data.calendarEvent.venue_deatils;
        if($scope.passData.calendarEvent?.venueDetails?.image)
        {
            $scope.passData.calendarEvent.venueDetails.image_url="<?=$LMSUri?>venue/"+$scope.passData.calendarEvent.venueDetails.image
        }
    }
	$scope.paymentGatewayService = $paymentGatewayService;
	$scope.los = $learnerOperations;

	$scope.dataLoading = true;

	$scope.couponOpt = {
		enabled: false,
		code: ""
	};

	$scope.applyCoupon = function () {
		$scope.dataLoading = true;
		$globalPaymentService.applyCoupon({resource_id: $scope.passData.calendarEvent.id, coupon_code: $scope.couponOpt.code, type: 'schedule'})
		.then(function successCallback(response) {
			$scope.dataLoading = false;
			if (response.data.status == '404') {
				$confirm({
					text: response.data.msg ?? 'Invalid coupon',
					title: 'Error',
					ok: 'Ok',
					// cancel: 'No'
				})
			} else {
				$scope.discounts = response.data
			}
		});

	}

	$scope.save = function (event, type) {
		/* Civica Payments Engine Enable check. Enabling this option will allow learning to be purchased from the system */
		if (
			(
				$rootScope.config.isCivicaPaymentsEngine ||
				$rootScope.config.isGlobalPaymentsEngine ||
				$rootScope.config.enableStripePayment ||
				$rootScope.config.enablePay360 ||
				$rootScope.config.enableGovUKPay
			) &&
			event.discounted_cost > 0 &&
			!event.approval &&
			(
				!$scope.discounts ||
				(
					$scope.discounts &&
					$scope.discounts.total != 0
				)
			)
		) {
			if ($rootScope.config.isCivicaPaymentsEngine) {
				if (
					$scope.couponOpt.enabled &&
					$scope.couponOpt.code !== ''
				) {
					$scope.paymentGatewayService.civicaPayment(event.id, type, null, null, $scope.couponOpt.code, $scope.passData?.calendarEvent?.learner_requirement);
				} else {
					$scope.paymentGatewayService.civicaPayment(event.id, type, null, null, null, $scope.passData?.calendarEvent?.learner_requirement);
				}
			} else if ($rootScope.config.enableStripePayment) {
				if (
					$scope.couponOpt.enabled &&
					$scope.couponOpt.code !== ""
				) {
					$stripeService.pay(type, event.id, $scope.passData.calendarEvent, $scope.couponOpt.code);
				} else {
					$stripeService.pay(type, event.id, $scope.passData.calendarEvent);
				}
			} else if ($rootScope.config.enablePay360) {
				$pay360Service.pay(
					event.id, type, null, null,
					$scope.couponOpt.enabled && $scope.couponOpt.code !== "" ? $scope.couponOpt.code : undefined,
					$scope.passData?.calendarEvent?.learner_requirement,
                    $scope.passData.calendarEvent
				);
			}
			else if($rootScope.config.enableGovUKPay){
				$govUKPayService.pay(
					event.id, type, null, null,
					$scope.couponOpt.enabled && $scope.couponOpt.code !== "" ? $scope.couponOpt.code : undefined,
					$scope.passData?.calendarEvent?.learner_requirement,
                    $scope.passData.calendarEvent
				);
			}
			else {
				if (
					$scope.couponOpt.enabled &&
					$scope.couponOpt.code !== ''
				) {
					$globalPaymentService.makePayment(type, event.id, $scope.passData.calendarEvent, $scope.couponOpt.code);
				} else {
					$globalPaymentService.makePayment(type, event.id, $scope.passData.calendarEvent);
				}
			}
		} else {
			$scope.saveAction(event);
		}
	};


	$scope.getCouponAssignedForResource = function () {
		$globalPaymentService.getCouponAssignedForResource($scope.passData.calendarEvent.id, 'schedule')
		.then(function successCallback(response) {
			$scope.passData.calendarEvent.coupon_exists = response.data.coupon_exists
			$scope.dataLoading = false;
		});
	}

	$scope.getCouponAssignedForResource();

	$scope.saveAction = function (event) {
		$http({
			method: 'POST',
			url: '<?=$LMSUri?>schedule/link/new',
			data: {
				link_id: $rootScope.currentUser.id,
				schedule_id: event.id,
				approval: event.approval,
				learner_requirement: event.learner_requirement,
				type: "users"
			}
		}).then(function successCallback (response) {
			let message='';

			if ((response.data.type !== 'users_queue') && (response.data.type !== 'not_approved')) {
				message+= 'You have enrolled successfully!.<br><br>';
			}

			if (response.data.type === 'not_approved') {
				message+= 'Your request has been received and is awaiting management approval.<br>You will be notified once your place is confirmed.<br>';
			}

			if (response.data.linked_events) {
				let paid_course = [],
					free_course = [],
					waiting_list = [],
					enrolled_event = []
				;

				response.data.linked_events.forEach(function(schedule) {
					if (
						schedule.cost &&
						schedule.cost > 0
					) {
						paid_course.push(schedule.name + " (" + moment(schedule.start_date).format("DD/MM/YYYY HH:mm") + ")");
					} else {
						if (
							schedule.enrole_any_learner &&
							schedule.maxclass > 0 &&
							schedule.users.length >= schedule.maxclass
						) {
							waiting_list.push(schedule.name+" ("+moment(schedule.start_date).format("DD/MM/YYYY HH:mm")+")");
						} else {
							enrolled_event.push(schedule.name+" ("+moment(schedule.start_date).format("DD/MM/YYYY HH:mm")+")");
						}
						free_course.push(schedule.name+" ("+moment(schedule.start_date).format("DD/MM/YYYY HH:mm")+")");
					}
				});

				if (free_course.length) {
					message+=` %%learner_event_enroll_linked_events%% <br> ${free_course.join(", <br>")}.<br><br>`;
				}
				if (enrolled_event.length) {
					message+=` %%learner_event_enroll_linked_events_alternative%%<br><br>`;
				}
				if (waiting_list.length) {
					message +=`  ${waiting_list.join(", ")} has no room currently available, you are on the waiting list.<br>`;
				}
				if (paid_course.length) {
					message+=`  It also contains paid events which you will need to pay for separately:<br> ${paid_course.join(", <br>")}.<br>`;
				}
			}
			if (!message) {
				message='You have enrolled successfully!';
			}
			if (response.data.type === 'users_queue') {
				$scope.closeModal();
				if ('/' + $scope.passData.calendarEvent.link !== $location.path()) {
					// Quick fix for surrey of if user is waiting list show a modal showing the event details.
					$http({
						method: 'POST',
						url: '<?=$LMSUri?>schedule/get/position',
						data: {
							userId: $rootScope.currentUser.id,
							scheduleId: event.id,
						}
					}).then(function successCallback (response) {
							angular.extend($scope.passData.calendarEvent, {
								title: event.title.replace(/\[.*?\]/, '[Waiting List]'),
								waiting_list: true,
								position: response.data.position
							});
							 $learnerOperations.showCancellPopup($scope.passData.calendarEvent);
							setTimeout(function () {
								window.location.reload();
							}, 3000);
					});

					// $location.path($scope.passData.calendarEvent.link);
				} else {
					$rootScope.$broadcast('refresh-details');
				}
			}
			if (
				response.data.type === 'users' ||
				response.data.type === 'not_approved'
			) {
				$ngConfirm({
					title: "Alert",
					content: message,
					typeAnimated: true,
					columnClass: 'col-md-6 col-md-offset-3',
					buttons: {
						tryAgain: {
							text: 'ok',
							btnClass: '',
							action: function() {
								$scope.closeModal();
								$scope.los.getResourceList(true);
								if ('/' + $scope.passData.calendarEvent.link !== $location.path()) {
									$location.path($scope.passData.calendarEvent.link);
								}
							}
						},
					}
				});
			}
		})
		.catch((err) => {

			if(err.data.error) {
				$ngConfirm({
					title: "Alert",
					type: "red",
					content: err.data.error,
					typeAnimated: true,
					columnClass: 'col-md-6 col-md-offset-3',
					buttons: {
						tryAgain: {
							text: 'ok',
							btnClass: 'btn-danger',
							action: function() {
								$scope.closeModal();
							}
						},
					}
				});
			}

		});
	};

	$scope.closeAlert = function () {
		$scope.alerts = [];
	};
	$scope.closeModal = function () {
		$uibModalInstance.close();
	};
	$scope.cancelModal = function () {
		$uibModalInstance.dismiss('cancel');
	};

	$scope.checkIfDateSame = function (startAt, endAt) {
		return moment(startAt).isSame(endAt, 'day');
	}

	$scope.removeEntry = function (index, list) {
		list.splice(index, 1);
	};
	$scope.isArray = angular.isArray;

	$scope.addToBasket = function (event) {
		if (event.enrolledUsers >= event.maxclass && event.maxclass != 0) {
			if (!confirm("You are trying to add an event which is fully booked - would you like to continue?")) {
				return;
			}
		}
		CourseBasketService.addToBasket(event.id, 'schedules');
		$scope.closeModal();
	}

	////////////////////////////////////////////////////////////////////////////

});
