angular.module('lmsApp')
	.controller('LearnerTasksController', function ($scope, $paymentGatewayService, $learnerOperations, $dateOperation, $location, $http, $rootScope, $uibModal, $timeout, $routeParams, $ngConfirm, dynamicLoadingService) {
        $scope.alerts = [];
		$rootScope.showTask = true;
		$rootScope.showSchedule = true;
		$rootScope.dashboard.pkf = false;
		$scope.paymentGatewayService = $paymentGatewayService;

		$scope.tabselector = $rootScope.config.defaultLearnerTaskView;

		dynamicLoadingService.get('event_types', '<?=$LMSUri?>event-type/all').then(function(data) {
			$rootScope.event_types = data;
		});

		dynamicLoadingService.get('standards', '<?=$LMSUri?>apprenticeshipstandards/all').then(function(data) {
			$rootScope.standards = data;
		});

    $scope.event_due_date_uk = "";
    $scope.expected_completion_date = "";
    $rootScope.$on('date-range-change', function(event, args) {
      if(args.event_from && args.event_to){
      $scope.event_due_date_uk = JSON.stringify({period_from:args.event_from,period_to:args.event_to});
      }else{
        $scope.event_due_date_uk="";
      }
      if(args.learning_to && args.learning_from){
        $scope.expected_completion_date = JSON.stringify({period_from:moment(args.learning_from).format('YYYY-MM-DD'),period_to:moment(args.learning_to).format('YYYY-MM-DD')});
      }else{
        $scope.expected_completion_date="";
      }
    })


		/*Check Payment response based on param*/
		$scope.paymentGatewayService.responseDisplay();

		// Option to show hide calendar and list all tasks in list
		// $scope.showManuallyAssignedWorkOnly = ($rootScope.isSMCR ? 'hide' :'show');
		$scope.showManuallyAssignedWorkOnly = ($rootScope.config.TasksShowByDefaultCalendarEntriesOnly ? 'hide' :'show');
		$scope.taskListType = $rootScope.config.TasksDefaultSelectedEventType;

		$scope.showTasksOnCalendarOnly = function() {
			$scope.showManuallyAssignedWorkOnly = ($scope.showManuallyAssignedWorkOnly === 'show' ? 'hide' : 'show');
		};

		$scope.showTasksOnCalendarOnly();

		$scope.calendarChoice = false;
		$rootScope.calendarOperations = {
			addToExternal: false
		};
		$scope.hiddenCalendar = $rootScope.hiddenCalendar || $rootScope.hiddenCalendar === false ? $rootScope.hiddenCalendar : true;
		$scope.calendarView = 'week'; // default view
		$scope.viewDate = new Date(); // highlighted day of the month
		$scope.taskEvents = []; // create empty array event
		$scope.calendarEvents = [];
		$scope.eventsList = [];

		// Helper function to format event status for display
		$scope.formatEventStatus = function(status, position, waitingList, approved) {
			if (status === 'completed' || status === ('%%event_completion_state_completed%%').toLowerCase()) {
				return '[Completed]';
			}
			if (waitingList === true || status === 'waiting list') {
				if (approved === false) {
					return '[Waiting for Approval]';
				}
				return '[Waiting List]';
			}
			if (position === 'Not Paid') {
				return '[Payment Required]';
			}
			if (position === 'not enrolled') {
				return '[Not Enrolled]';
			}
			if (position === 'enrolled') {
				return '[Enrolled]';
			}
			if (status === 'in_progress' || status === 'in progress') {
				return '[In Progress]';
			}
			if (status === 'not_attempted' || status === 'not attempted' || !status) {
				return '[Not Started]';
			}
			// Fallback for any other status
			return status ? '[' + status.charAt(0).toUpperCase() + status.slice(1).replace(/_/g, ' ') + ']' : '';
		};

		//Time fetch from configuration
		var startCalendarTime=$rootScope.convertConfigurationDate($scope.config.CalendarStartTime);
		$scope.startTime = startCalendarTime.hour+":"+startCalendarTime.minute;
		var endCalendarTime=$rootScope.convertConfigurationDate($scope.config.CalendarEndTime);
		$scope.endTime = endCalendarTime.hour+":"+endCalendarTime.minute;

		$scope.isLoading = true;
		$scope.eventActions = [{
			label: '<i class=\'fa fa-calendar-plus-o add-to-calendar\' title="Add to Calendar"><span class="hidden">Add to Calendar</span></i>',
			onClick: function(args) {
				var googleDate = $dateOperation.dateToGoogleCalendar(args.calendarEvent.startsAt),
					link = document.getElementsByTagName('base')[0].href + args.calendarEvent.link,
					googleEndDate
				;

				if (args.calendarEvent.endsAt){
					googleEndDate = $dateOperation.dateToGoogleCalendar(args.calendarEvent.endsAt);
				} else {
					googleEndDate = googleDate;
				}


				$scope.addToCalendarTitle = args.calendarEvent.title;
				$scope.addToCalendarLocation = 'At Your desk';
				$scope.addToCalendarDescription = 'For+details,+link+here:+ ' + link;

				$scope.googleCalendarLink = 'https://www.google.com/calendar/render?action=TEMPLATE&text=' + $scope.addToCalendarTitle + '&dates=' + googleDate + '/' + googleEndDate + '&details=' + $scope.addToCalendarDescription + '&location=' + $scope.addToCalendarLocation + '&sf=true&output=xml';

				$scope.icalCalendarLink ='<?=$LMSUri?>calendar/ical?time=' + googleDate + '&timeEnd=' + googleEndDate + '&location=' + $scope.addToCalendarLocation + '&description=' + $scope.addToCalendarDescription + '&url=' + link + '&title=' + $scope.addToCalendarTitle;

				$scope.outlookCalendarLink = 'https://bay02.calendar.live.com/calendar/calendar.aspx?rru=addevent&dtstart=' + googleDate + '&dtend=' + googleEndDate + '&summary=' + $scope.addToCalendarTitle + '&location=' + $scope.addToCalendarLocation + '&description=' + $scope.addToCalendarDescription + '&allday=false&uid=';

				$scope.yahooCalendarLink = 'https://calendar.yahoo.com/?v=60&view=d&type=20&title=' + $scope.addToCalendarTitle + '&st=' + googleDate + '&et=' + googleEndDate + '&desc=' + $scope.addToCalendarDescription + '&in_loc=' + $scope.addToCalendarLocation + '&uid=';
				$scope.calendarChoice = true;
				$rootScope.calendarOperations.addToExternal = true;
				document.getElementById("calendarChoice").focus();
			}
		}];

		// proxy to date service
		$scope.dateToUk = $dateOperation.dateToUk;

		$scope.removeEvent = function (calendarEvent) {
			$http({
				method: 'POST',
				url: '<?=$LMSUri?>schedule/link/delete',
				data: {
					link_id: $rootScope.currentUser.id,
					schedule_id: calendarEvent.id,
					type: 'users_queue'
				}
			}).then(function successCallback () {
				location.reload();
			});
		};

		$scope.enableGlobalOutlookIntegration = $rootScope.config.enableGlobalOutlookIntegration;

		//manually add event to Outlook calendar
		$scope.copyTaskToOutlook = function(calendarEventData, formatData = false, type = 'events') {
			let calendarEvent = calendarEventData;
			if(formatData) {
				calendarEvent = $scope.taskEvents.find((data) => {
					if(type == 'event') {
						return calendarEventData.schedule_id == data.id;
					} else {
						if(calendarEventData?.module) {
							return calendarEventData.module.id == data.id;
						}
					}
				});
			}

			$http({
				method: 'POST',
				url: '<?=$LMSUri?>schedule/' + $rootScope.currentUser.id + '/outlook',
				data: {
					event_id: calendarEvent.calendarEventId,
					event_type: calendarEvent.type.name,
					title: calendarEvent.title,
					description: calendarEvent.description,
					start_date: calendarEvent.startsAt,
					duration: calendarEvent.duration || 60,
					link: calendarEvent.link,
				}
			}).then(function successCallback () {
				$ngConfirm({
					title: "Success",
					content: "Event copied to Outlook calendar.",
					typeAnimated: true,
					columnClass: 'col-md-6 col-md-offset-3',
					buttons: {
						tryAgain: {
							text: 'ok',
							btnClass: '',
							action: function(){
								location.reload();
							}
						},
					}
				});
			});
		};

		// When event is clicked, this function is executed
		$scope.calendarEventView = function (calendarEventData, formatData = false, type = 'event') {
			let calendarEvent = calendarEventData;
			if(formatData) {
				calendarEvent = $scope.taskEvents.find((data) => {
					if(type == 'event') {
						return calendarEventData.schedule_id == data.id;
					} else {
						return calendarEventData.module.id == data.id;
					}
				});
				if (!calendarEvent) {
					calendarEvent = calendarEventData;
				}
			}

			// if whole object is present, open it in modal window!
			if (calendarEvent.whole_object) {
				$uibModal.open({
					animation: true,
					ariaLabelledBy: 'modal-title',
					ariaDescribedBy: 'modal-body',
					templateUrl: '<?=$LMSTplsUriHTML?>modal-calendar-event.html?v=<?=$version?>',
					controller: 'ModalCalendarEvent',
					size: 'lg',
					backdrop: 'static',
					resolve: {
						data: function () {
							return {
								calendarEvent: calendarEvent
							};
						}
					}
				});
			} else if (
				calendarEvent.type &&
				calendarEvent.type.slug &&
				calendarEvent.type.slug === 'schedule_lesson' &&
				(
					calendarEvent.enrole_any_learner
				)
        &&
        (calendarEvent.position!='enrolled' || calendarEvent.waiting_list!=false)
			) {
				if (calendarEvent.position === "Not Paid") {
					$location.path(calendarEvent.link);
				} else {
					$http.get("<?=$LMSUri?>schedule/"+calendarEvent.id).then(function(res) {
						calendarEvent.schedules=res?.data?.schedules || [];
						calendarEvent.venueDetails = res?.data?.venue_deatils || null;
						calendarEvent.deadline_at = res?.data?.deadline_at || null;
						calendarEvent.drop_off_deadline_at = res?.data?.drop_off_deadline_at || null;
						if(calendarEvent?.venueDetails?.image) {
							calendarEvent.venueDetails.image_url = '<?=$LMSUri?>venue/'+calendarEvent?.venueDetails?.image || null;
						}
						if(res.data.cost !== null){
							calendarEvent.cost = res.data.cost
						}
						if(res.data.discounted_cost !== null) {
							calendarEvent.discounted_cost = res.data.discounted_cost
						}
                        if(calendarEvent.completion_status == 'waiting list') {
                            $learnerOperations.showCancellPopup(calendarEvent);
                        }else{
						$uibModal.open({
							animation: true,
							ariaLabelledBy: 'modal-title',
							ariaDescribedBy: 'modal-body',
							templateUrl: '<?=$LMSTplsUriHTML?>modal-enrole_any_learner.html?v=<?=$version?>',
							controller: 'ModalEnrolAnyLearner',
							size: 'lg',
							backdrop: 'static',
							resolve: {
								data: function () {
									return {
										calendarEvent: calendarEvent
									};
								}
							}
						});
                        }
					});
				}
			} else {
				if (calendarEvent.link) {
					$location.path(calendarEvent.link);
				} else if (calendarEventData?.module?.id) {
					$location.path('learner/resources/' + calendarEventData?.module?.id);
				}
			}
		};

		//switch calendar/task view
		$scope.showAllTasks = function (force) {
			if (
				force ||
				force === false
			) {
				$scope.hiddenCalendar = force;
			} else {
				$scope.hiddenCalendar = !$scope.hiddenCalendar;
			}
			$scope.resizeEvent();
		};


		$scope.resizeEvent = function () {
			$timeout(function() {
				var resizeEvent = window.document.createEvent('UIEvents');
				resizeEvent.initUIEvent('resize', true, false, window, 0);
				window.dispatchEvent(resizeEvent);
			}, 20);
		};

		$scope.$on('learner-tasks-hidden-calendar', function(event, args) {
			$scope.learnerTasksHiddenCalendarEvent = event;
			$scope.showAllTasks(args);
		});

		$scope.$on('resource-list-retrieved', function(event, args) {
			var i = 0,
				start_at_date,
				completion_date_custom = false,
				j,
				k,
				custom_hours = new Date(),
				morning = new Date(),
				evening = new Date(),
				start_at_list = [
					'completion_date_custom',
					'grace_at',
					'due_at',
					'target_due_at',
					'created_at'
				],
				prepared_event
			;

			var startCalendarTime = $rootScope.convertConfigurationDate($scope.config.CalendarStartTime),
				startEndTime = $rootScope.convertConfigurationDate($scope.config.CalendarEndTime)
			;


			morning.setHours(startCalendarTime.hour,startCalendarTime.minute,0,0);
			evening.setHours(startEndTime.hour,startEndTime.minute,0,0);

			$scope.resourceListRetrievedEvent = event;
			$scope.resourceList = args;

			//$scope.resourceListSorted = $filter('orderBy')($scope.resourceList, 'due_at');
			//$scope.resourceListSorted.reverse();

			for (i = $scope.resourceList.length - 1; i >= 0; i--) {

				completion_date_custom = false;

				// If learning result has completion_date_custom set, use it in calendar/tasks
				if (
					$scope.resourceList[i].completion_date_custom &&
					$scope.resourceList[i].completion_date_custom !== null
				) {
					completion_date_custom = getJsDate($scope.resourceList[i].completion_date_custom);
				}

				// If resource is attached to event assigned to learner, and completion_date_custom is set for that resource, make it main completion_date_custom
				if (
					$scope.resourceList[i].module &&
					$scope.resourceList[i].module.schedule_links &&
					angular.isArray($scope.resourceList[i].module.schedule_links) &&
					$scope.resourceList[i].module.schedule_links[0] &&
					$scope.resourceList[i].module.schedule_links[0].completion_date_custom
				) {
					completion_date_custom = getJsDate($scope.resourceList[i].module.schedule_links[0].completion_date_custom);
				}

				if (
					completion_date_custom &&
					completion_date_custom instanceof Date
				) {
					custom_hours.setHours(completion_date_custom.getHours(),completion_date_custom.getMinutes(),0,0);
				} else {
					completion_date_custom = false;
				}

				if (
					!( // Ignore lessons added by schedule
						$scope.resourceList[i].module &&
						$scope.resourceList[i].module.schedule_lesson_links &&
						$scope.resourceList[i].module.schedule_lesson_links.length > 0
					)
				) {

					for (k = 0; k < start_at_list.length; k++) {
						if (
							$scope.resourceList[i][start_at_list[k]] &&
							$scope.resourceList[i][start_at_list[k]] !== null
						) {
							start_at_date = getJsDate($scope.resourceList[i][start_at_list[k]]);
							break;
						}
					}


					prepared_event = {
						id: $scope.resourceList[i].module.id,
						title: $scope.resourceList[i].module.name + ' ' + $scope.formatEventStatus($scope.resourceList[i].completion_status), // The title of the event with status
						startsAt: (completion_date_custom || start_at_date), // A javascript date object for when the event starts
						showTask:true,
						showSchedule:true,
						color: { // can also be calendarConfig.colorTypes.warning for shortcuts to the deprecated event types
							primary: '#525252', // the primary event color (should be darker than secondary)
							secondary: ($rootScope.rs.fffUI() ? '#e2e2e2' : '#292929') // the secondary event color (should be lighter than primary)
						},
						// Constructing a link for click event in calendar, a start, there will be different modules to take into account
						link: 'learner/resources/' + ($scope.resourceList[i].learning_module_id || $scope.resourceList[i].id),
						actions: $scope.eventActions,
						type: ($scope.resourceList[i].module.type || {name: 'Lesson', slug: 'resource_lesson'}),
						completion_status: $scope.resourceList[i].completion_status,
						completion_date_custom: $scope.resourceList[i].completion_date_custom
					};

					// Do not add resources that can be enrolled into calendar
					if (
						start_at_date &&
						$scope.resourceList[i].module.track_progress &&
						!$scope.resourceList[i].HideNotApplicableLearning
					) {
						$scope.taskEvents.push(prepared_event);

						// check if resource is part of schedule assigned to user, if so, do not add to calendarEvents
						if (
							$scope.resourceList[i].completion_status !== 'completed' &&
							$scope.resourceList[i].completion_status !== ('%%event_completion_state_completed%%').toLowerCase() &&
							(
								!$scope.resourceList[i].module.schedule_links ||
								$scope.resourceList[i].module.schedule_links.length < 1 ||
								(
									completion_date_custom &&
									custom_hours >= morning &&
									custom_hours <= evening
								)
							)
						) {
                            if(!$rootScope.config.TasksShowByDefaultCalendarEntriesOnly){
							  $scope.calendarEvents.push(prepared_event);
                            }
						}
					}

				}
			}
			// Evidence learning resources have option to set up meeting, get them all that was create dby this user and add into calendar.
			$http({
				method: 'GET',
				url: '<?=$LMSUri?>learner/meeting/list'
			}).then(function successCallback(response) {
				for (i = response.data.length - 1; i >= 0; i--) {
					// Assume meetings are scheduled/upcoming by default
					let meetingStatus = $scope.formatEventStatus(response.data[i].status || 'scheduled');
					prepared_event = {
						title: response.data[i].name + ' ' + meetingStatus, // The title of the event with status
						showTask:true,
						showSchedule:true,
						startsAt: getJsDate(response.data[i].preferred_time), // A javascript date object for when the event starts
						color: { // can also be calendarConfig.colorTypes.warning for shortcuts to the deprecated event types
							primary: '#525252', // the primary event color (should be darker than secondary)
							secondary: ($rootScope.rs.fffUI() ? '#e2e2e2' : '#292929') // the secondary event color (should be lighter than primary)
						},
						type: {
							name: 'Meeting',
							slug: 'resource_meeting'
						},
						// Constructing a link for click event in calendar, a start, there will be different modules to take into account
						link: 'learner/resources/' + response.data[i].learning_modules_id + '/meetings',
						actions: $scope.eventActions
					};
					$scope.taskEvents.push(prepared_event);
					$scope.calendarEvents.push(prepared_event);
					$scope.eventsList.push(prepared_event);
				}
				if (!$rootScope.isApprentix) {
					$scope.isLoading = false;
				}
			});

			// If Schedule is enabled, request them!
			if ($rootScope.config.enableSchedule) {
				$http({
					method: 'GET',
					url: '<?=$LMSUri?>schedule/all'
				}).then(function successCallback(response) {

					var start_date,
						duration,
						end_date,
						start_date_moment,
						end_date_moment,
						start_date_tomorrow_moment,
						multi_days,
						prepared_event_added_id = false,
						start_date_duration,
						deadline_at,
						drop_off_deadline_at
					;

					for (i = response.data.length - 1; i >= 0; i--) {
						var enrolledUsers = [];
						var waitingApproval = true;
						start_date = getJsDate(response.data[i].start_date);
						end_date = getJsDate(response.data[i].end_date);
						deadline_at = response.data[i].deadline_at ? getJsDate(response.data[i].deadline_at) : null;
						drop_off_deadline_at = response.data[i].drop_off_deadline_at ? getJsDate(response.data[i].drop_off_deadline_at) : null;
						duration = response.data[i].duration;
            start_date_time= new Date(response.data[i].start_date);
            end_date_time= new Date(response.data[i].end_date);
						if (duration < 30) {
							duration = 30;
						}
						start_date_duration = new Date(start_date.getTime() + duration * 60 * 1000);
						let color_red = false;
						let waiting_for_approval = false;
						if (response.data[i].users) {
							response.data[i].users.forEach(element => {
								enrolledUsers.push(element.id);
								if (element.id === $rootScope.currentUser.id) {
									waitingApproval = element.approved;
									color_red = !element.approved;
								}
							});
						}
let waiting_list = false;
						let title = response.data[i].name;
						let eventStatus = '';
						if (response.data[i].waiting.find(function(e){return e.id==$rootScope.currentUser.id})) {
              if (response.data[i].approval==1 && response.data[i].waiting.find(function(e){return e.id==$rootScope.currentUser.id}).approved==0){
                waiting_list = true;
                eventStatus = $scope.formatEventStatus('waiting list', null, true, false);
				waiting_for_approval = true;
              }
              else{
                                waiting_list = true;
                eventStatus = $scope.formatEventStatus('waiting list', null, true, true);
              }
							color_red = true;
						}
						// Add status to title if not already included in old format
						if (eventStatus && !title.includes('(Waiting')) {
							title += ' ' + eventStatus;
						}

						prepared_event = {
							id: response.data[i].id,
							title: title, // The title of the event
							description: response.data[i].description,
							cost: response.data[i].cost,
							waiting_for_approval: waiting_for_approval,
							duration: duration,
							enrolledUsers: enrolledUsers.length,
							showTask: (response.data[i].visible_learner_task || response.data[i].type === 'lesson'), // Task show
							showSchedule: response.data[i].visible_learner, //Schedule show
							startsAt: start_date, // A javascript date object for when the event starts
							endsAt: start_date_duration, // A javascript date object for when the event starts
							end_date: end_date,
							all_day_event: response.data[i].all_day_event,
							color: { // can also be calendarConfig.colorTypes.warning for shortcuts to the deprecated event types
								primary: '#525252', // the primary event color (should be darker than secondary)
								secondary: ($rootScope.rs.fffUI() ? '#e2e2e2' : '#292929') // the secondary event color (should be lighter than primary)
							},
							type: {
								name: response.data[i].type,
								slug: 'schedule_' + response.data[i].type
							},
							enrole_any_learner: response.data[i].enrole_any_learner,
							approved: waitingApproval,
							maxclass: response.data[i].maxclass,
							approval: response.data[i].approval,
							minclass: response.data[i].minclass,
							// Constructing a link for click event in calendar, a start, there will be different modules to take into account

							link: (response.data[i].type === 'lesson' && response.data[i].lessons && response.data[i].lessons[0] ? 'learner/resources/' + response.data[i].lessons[0].id + '-' + response.data[i].id : ''),
							whole_object: (response.data[i].type !== 'lesson' ? response.data[i] : false),
							actions: $scope.eventActions,
							draggable: true,
							resizable: true,
							deadlineDate: deadline_at,
							dropOffDeadlineAt: drop_off_deadline_at,
							venue_name: response.data[i]?.venue_deatils ? response.data[i].venue_deatils.name : '',
              start_date_time: start_date_time,
              end_date_time: end_date_time,
                                waiting_list
						};
						if (color_red) {
							prepared_event.color.secondary = color_red ? '#592929' : '#292929';
							prepared_event.color.text = '#fff';
						}

						// Lessons should be on task list all the time, any other type needs visible_learner_task set to be true;
						if (
							response.data[i].visible_learner_task ||
							response.data[i].type === 'lesson'
						) {
							$scope.taskEvents.push(prepared_event);
							prepared_event_added_id = prepared_event.id;
						}
						if (response.data[i].visible_learner) {
							if (prepared_event.enrole_any_learner) {

								prepared_event.position = 'not enrolled';
								response.data[i].users.forEach(user => {
									if (user.id === $rootScope.currentUser.id) {
										prepared_event.position = 'enrolled';
									}
								});
								var x = 1;
								let color_red = false;
								response.data[i].waiting.forEach(user => {
									if (user.id === $rootScope.currentUser.id) {
										if(user.is_paid=="0" && user.approved=="1"){
											prepared_event.position = 'Not Paid';
										}else{
										 prepared_event.position = x;
										}
										prepared_event.completion_status = 'waiting list';
									}
									x++;
								});
								if (!prepared_event.approved) {
									color_red = true;
								}
								prepared_event.color.primary = '#626262';
								if (color_red) {
									prepared_event.color.secondary = '#592929'
								}
								if (prepared_event.enrole_any_learner && prepared_event.position == 'not enrolled'){
									prepared_event.color.secondary = '#626262';
								}
								
								// Add enrollment status to title if not already added via waiting list logic
								if (!eventStatus) {
									let enrollmentStatus = $scope.formatEventStatus(prepared_event.completion_status, prepared_event.position, waiting_list, prepared_event.approved);
									if (enrollmentStatus) {
										prepared_event.title += ' ' + enrollmentStatus;
									}
								}
								$scope.calendarEvents.push(prepared_event);
								$scope.eventsList.push(prepared_event);
								prepared_event.showTask = true;
								if (
									!prepared_event_added_id ||
									prepared_event_added_id !== prepared_event.id
								) {
									$scope.taskEvents.push(prepared_event);
								}
							} else {
								$scope.calendarEvents.push(prepared_event);
								$scope.eventsList.push(prepared_event);
								// If event is multi day, clone prepared event, update startsAt +1 day and add.

								if (response.data[i].all_day_event) {
									start_date_moment = moment(start_date);
									end_date_moment = moment(end_date);
									multi_days = end_date_moment.diff(start_date_moment, 'days') + 1;

									for (var ii = 1; ii < multi_days; ii++) {
										start_date_tomorrow_moment = start_date_moment.clone().add(ii, 'days');
										prepared_event = angular.copy(prepared_event);
										prepared_event.startsAt = start_date_tomorrow_moment.toDate(); // Convert to Date
										prepared_event.endsAt = start_date_tomorrow_moment.clone().add(duration, 'minutes').toDate(); // Convert to Date
										$scope.calendarEvents.push(prepared_event);
									}
								}
							}

							if (response.data[i]?.users) {
								// check if login user is part of the event
								const userData = response.data[i]?.users?.find(data => data.id == $rootScope.currentUser.id);
								if (userData?.schedule_link?.completion_status) {
									prepared_event.completion_status = userData.schedule_link.completion_status;
								}
							}

						}

						for (j = response.data[i].children.length - 1; j >= 0; j--) {
							start_date = getJsDate(response.data[i].children[j].start_date);
							duration = response.data[i].children[j].duration;
							if (duration < 30) {
								duration = 30;
							}
							end_date = new Date(start_date.getTime() + duration * 60 * 1000);
							let color_red = false;
							response.data[i].users.forEach(element => {
								enrolledUsers.push(element.id);
								if (element.id === $rootScope.currentUser.id) {
									color_red = !element.approved;
								}
							});
							prepared_event = {
								id: response.data[i].id,
								title: response.data[i].name, // The title of the event
								description: response.data[i].description,
								duration: response.data[i].duration,
								enrolledUsers: enrolledUsers.length,
								showTask:true,
								showSchedule:true,
								startsAt: start_date, // A javascript date object for when the event starts
								endsAt: end_date, // A javascript date object for when the event starts
								color: { // can also be calendarConfig.colorTypes.warning for shortcuts to the deprecated event types
									primary: '#525252', // the primary event color (should be darker than secondary)
									secondary: ($rootScope.rs.fffUI() ? '#e2e2e2' : '#292929') // the secondary event color (should be lighter than primary)
								},
								type: {
									name: response.data[i].type,
									slug: 'schedule_' + response.data[i].type
								},
								enrole_any_learner: response.data[i].enrole_any_learner,
								maxclass: response.data[i].maxclass,
								minclass: response.data[i].minclass,
								// Constructing a link for click event in calendar, a start, there will be different modules to take into account
								//link: 'learner/resources/' + response.data[i].lessons[0].id,
								//link: (response.type === 'lesson' ? 'learner/resources/' + response.data[i].lessons[0].id : ''),
								link: (response.data[i].type === 'lesson' && response.data[i].lessons && response.data[i].lessons[0] ? 'learner/resources/' + response.data[i].lessons[0].id + '-' + response.data[i].id : ''),
								whole_object: (response.data[i].type !== 'lesson' ? response.data[i] : false),
								actions: $scope.eventActions,
								deadlineDate: deadline_at,
								dropOffDeadlineAt: drop_off_deadline_at,
								venue_name: response.data[i]?.venue_deatils ? response.data[i].venue_deatils.name : ''
							};
							if (color_red) {
								prepared_event.color.secondary = color_red ? '#592929' : '#292929';
							}
							$scope.taskEvents.push(prepared_event);
							if (prepared_event.enrole_any_learner) {
								if (enrolledUsers.length < response.data[i].maxclass) {
									$scope.calendarEvents.push(prepared_event);
									$scope.eventsList.push(prepared_event);
								}
							} else {
								$scope.calendarEvents.push(prepared_event);
								$scope.eventsList.push(prepared_event);
							}

						}
					}
				});
			}

			// if Apprentix get also all "Training, Development, Progress Reviews" for user and attach to calendar.
			if ($rootScope.isApprentix) {
				$http({
					method: 'GET',
					url: '<?=$LMSUri?>apprenticeshipissues/user/review/' + $rootScope.currentUser.id
				}).then(function successCallback(response) {
					for (i = response.data.length - 1; i >= 0; i--) {
						// Show only incompleted in calendar.
						let reviewStatus = $scope.formatEventStatus(response.data[i].completion_status);
						prepared_event = {
							title: response.data[i].visit_type + (response.data[i].programme ? (' - ' + response.data[i].programme.name) : '') + ' ' + reviewStatus,
							showTask:true,
							showSchedule:true,
							startsAt: getJsDate(response.data[i].date),
							color: {
								primary: '#525252', // the primary event color (should be darker than secondary)
								secondary: ($rootScope.rs.fffUI() ? '#e2e2e2' : '#292929') // the secondary event color (should be lighter than primary)
							},
							link: 'learner/reviews/' + response.data[i].id,
							actions: $scope.eventActions,
							whole_object: response.data[i],
							type: {
								name: response.data[i].visit_type,
								slug: response.data[i].visit_type
							},
							completion_status: response.data[i].completion_status,
							visitor: response.data[i].visitor
						};
						$scope.taskEvents.push(prepared_event);
						$scope.calendarEvents.push(prepared_event);
					}
					$scope.isLoading = false;
				});
			}
		});

		// Load first 400 resources associated with this learner, call for it is in service, and it broadcasts when ready.
		$learnerOperations.getResourceList(false);

		if (
			$routeParams.view &&
			$routeParams.view === 'list'
		) {
			$scope.showAllTasks(true);
		}

		$scope.switchTab = (tabName) => {
			if (tabName === 'learning_resources') {
				$scope.user_learnings_list.refresh++;
			}

			$scope.tabselector = tabName;
		};

		$scope.combined_types_default = $rootScope.config.TasksDefaultSelectedResourceType;


		dynamicLoadingService.get('types', '<?=$LMSUri?>learning/types/all').then(function(data) {
			$rootScope.types = data;
			$scope.combined_types = angular.copy($rootScope.types);
			$scope.combined_types.push({id: 0, name: '%%lesson%%', slug: 'lesson'});
		});

		// Listen for theme changes and refresh calendar colors
		$scope.$on('themeChanged', function() {
			// Trigger the resource list retrieval again to refresh colors
			if ($scope.resourceListRetrievedEvent && $scope.resourceList) {
				// Clear existing events
				$scope.calendarEvents = [];
				$scope.taskEvents = [];
				$scope.eventsList = [];
				
				// Re-trigger the resource list processing with updated theme colors
				$scope.$emit('resource-list-retrieved', $scope.resourceList);
			}
		});

		$scope.user_events_list = {
			"all": [],
			'loading' : false,
			"refresh" : 0
		};

		$scope.user_learnings_list = {
			"all": [],
			'loading' : false,
			"refresh" : 0,
			"request_completed": false
		};
        $scope.event_alerts_list = {
            "all": [],
            'loading' : false,
            "refresh" : 0,
            "request_completed": false
        };

		$scope.showEportfolio = $location.path() == '/learner/resources';
		$scope.switchStatus = false;

		// $scope.userEventsTable = [];
		// $scope.userLearningsTable = [];

		$scope.callServerUserEventsList = function (tableState) {
			tableState.table = 'events';
			updateReport(tableState, "<?=$LMSUri?>schedule/list/user/" + $rootScope.currentUser.id, $scope, $http, function () {

				const data = $scope.data.filter((data) => !!data.schedule?.visible_learner);

				$scope.user_events_list.all = data;
				$scope.user_events_list.loading = false;
			});
		};

		$scope.callServerUserLearningsList = function (tableState) {
			tableState.table = 'learning_resources';
			updateReport(tableState, "<?=$LMSUri?>mylearning/list/user/" + $rootScope.currentUser.id, $scope, $http, function () {
				$scope.user_learnings_list.all = $scope.data;
				$scope.user_learnings_list.loading = false;
				$scope.user_learnings_list.request_completed = true;
			});
		};
        $scope.callServerEventAlertsList = function(tableState){
            tableState.table = 'event_alerts';
            updateReport(tableState, "<?=$LMSUri?>schedule/event-alerts/"+$rootScope.currentUser.id, $scope, $http, function () {
                $scope.event_alerts_list.all = $scope.data;
                $scope.event_alerts_list.loading = false;
                $scope.event_alerts_list.request_completed = true;
            });
        }

		$scope.ePortfolio = function() {
			$timeout(function () {
				$location.path('/learner/resources');
			}, 100);
		};

		$scope.showToggle = function () {
			return $location.path() == '/learner/tasks/list' || $location.path() == '/learner/resources';
		}

        $scope.openAlerts = function(alert){
            $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: '<?=$LMSTplsUriHTML?>modal-view_alerts.html',
                controller: 'ModalViewAlerts',
                size: 'lg',
                backdrop: 'static',
                resolve: {
                    data: function () {
						return {
							alert:alert
						};
                    }
                }
            });
        }

		$scope.addStatus = function (alert) {
			$http({
				method: "PUT",
				url: "<?=$LMSUri?>schedule/schedule-alerts/" + alert.id,
				data: {},
			}).then(function successCallback(res) {
				$scope.user_events_list.refresh++;
				$scope.alerts = [{
					type: "success",
					msg: "Status Updated Successfully",
				}];
			});
		};

		$scope.closeAlert = function (index) {
			$scope.alerts.splice(index, 1);
		};
	})
;
