angular.module('lmsApp').controller('ModalEditResource', function ($scope, $uibModalInstance, data, $http, $timeout, smoothScroll, Upload, $confirm, $rootScope, $resourcesHelper, $tinyMce, $fileService, $ngConfirm, $learningService, dynamicLoadingService, $accordionService ) {


		$scope.passData = data;
		$scope.ls = $learningService;
        $scope.credlyBadges = [];
        $scope.linked = [];
        $scope.link ={};

        if($rootScope.config.enableCredlyBadge){
            $http.get('<?=$LMSUri?>credly/badges').then(res=>{
                if(res.data)
                {
                    $scope.credlyBadges = res.data.map(badge=>{
                        return {
                            id:badge.id,
                            name:badge.name
                        }
                    });
                    $scope.bindcredlyBadges();
                }
            })
        }
		$scope.closeAlert = function () {
			$scope.alerts = [];
		};
		$scope.closeModal = function () {
			// Cleanup TinyMCE editors before modal close
			if ($scope.tinymceEditors && Array.isArray($scope.tinymceEditors)) {
				$scope.tinymceEditors.forEach(function(editor) {
					if (editor && editor.remove) {
						editor.remove();
					}
				});
			}
			$uibModalInstance.close();
		};
		$scope.cancelModal = function () {
			// Cleanup TinyMCE editors before modal close
			if ($scope.tinymceEditors && Array.isArray($scope.tinymceEditors)) {
				$scope.tinymceEditors.forEach(function(editor) {
					if (editor && editor.remove) {
						editor.remove();
					}
				});
			}
			$uibModalInstance.dismiss('cancel');
		};

		$scope.removeEntry = function (index, list) {
			list.splice(index, 1);
		};
		$scope.isArray = angular.isArray;
		$scope.loadingText = 'Please wait ...';

		////////////////////////////////////////////////////////////////////////////

		var learningModuleId = $scope.passData.resource.id,
			d,
			n_asm_cat_q
		;

		dynamicLoadingService.get('target_catalogues', '<?=$LMSUri?>target-catalogue/all').then(function(data) {
			$rootScope.target_catalogues = data;
		});
		dynamicLoadingService.get('evidence_types', '<?=$LMSUri?>evidence-type/all').then(function(data) {
			$rootScope.evidence_types = data;
		});
		dynamicLoadingService.get('categories', '<?=$LMSUri?>learningcategory/all').then(function(data) {
			$rootScope.categories = data;
		});
		dynamicLoadingService.get('providers', '<?=$LMSUri?>learningprovider/all').then(function(data) {
			$rootScope.providers = data;
		});

		dynamicLoadingService.get('managers', '<?=$LMSUri?>manager/all').then(function(data) {
			$rootScope.managers = data;

			$scope.managerNames = [];
			angular.forEach($rootScope.managers, function (m) {
				this.push(m.fname + ' ' + m.lname);
			}, $scope.managerNames);
		});

		dynamicLoadingService.get('competencies', '<?=$LMSUri?>competency/all').then(function(data) {
			$rootScope.competencies = data;
		});

		dynamicLoadingService.get('event_types', '<?=$LMSUri?>event-type/all').then(function(data) {
			$rootScope.event_types = data;
		});

		dynamicLoadingService.get('types', '<?=$LMSUri?>learning/types/all').then(function(data) {
			$rootScope.types = data;
		});

		dynamicLoadingService.get('companies', '<?=$LMSUri?>company/all').then(function(data) {
			$rootScope.companies = data;
		});

		$scope.resourceObj = $scope.passData.resource;

		$scope.datePickerOptions = {
			startingDay: 1,
			minDate: new Date(new Date().setDate(new Date().getDate() + 1))
		};


		$scope.fls = $fileService;

		$resourcesHelper.setUp($scope);
		$scope.actionType = 'edit';
		$scope.alerts = [];
		$scope.learning = {"id": 0, "due_after_period": 7};
		$scope.newCompetency = {};
		$scope.newPrerequisite = {};
		$scope.learning.competencies = [];
		//$scope.competencies = [];
		$scope.prerequisites = [];
		$scope.learning.prerequisites = [];
		$scope.learning.material = {};
		$scope.learning.material.sessions = [];
		$scope.newsession = {};
		$scope.newsessionDTPickerOpen = {opened: false};
		$scope.managerNames = [];
		$scope.files = {};
		$tinyMce.init($scope);
		$resourcesHelper.lodingBar = false;
		/*Learning Resource Type Version Management*/
		$scope.showSaveVerion = false;
		$scope.learning.update_scorm_file = false;
		$scope.learning.active_version = 1;
		$scope.changedVersionIndex = 0;
		$scope.learningModuleVerions = [];
		$scope.avilableVersion = [];
		$scope.versionExist = false;
		$scope.selectedVersion = null;
		$scope.disableVersionSave = false;
		$scope.activeVersionCheck = false;
		$scope.invalidVersion = false;
		$scope.formatDate = function(){

		};

		$scope.setVersionField =function(index){
			$scope.learning.material = $scope.learningModuleVerions[index].material;
			$scope.learning.version = $scope.learningModuleVerions[index].version;
			$scope.learning.player_width = $scope.learningModuleVerions[index].player_width;
			$scope.learning.player_height = $scope.learningModuleVerions[index].player_height;
			$scope.learning.is_skillscan = $scope.learningModuleVerions[index].is_skillscan;
			$scope.learning.evidence_type_id = $scope.learningModuleVerions[index].evidence_type_id;
			$scope.learning.require_management_signoff = $scope.learningModuleVerions[index].require_management_signoff;
			$scope.learning.learning_module_evidences = $scope.learningModuleVerions[index].learning_module_evidences;
			$scope.learning.version_created_at = $scope.learningModuleVerions[index].version_created_at?$scope.learningModuleVerions[index].version_created_at:$scope.learning.version_created_at;
			$scope.learning.version_updated_at = $scope.learningModuleVerions[index].version_updated_at?$scope.learningModuleVerions[index].version_updated_at:$scope.learning.version_updated_at;
			$scope.learning.version_created_user = $scope.learningModuleVerions[index].created_user?$scope.learningModuleVerions[index].created_user.version_created_user:null;
			$scope.learning.version_updated_user = $scope.learningModuleVerions[index].updated_user?$scope.learningModuleVerions[index].updated_user.version_updated_user:null;
			$scope.learning.version_created_by = $scope.learningModuleVerions[index].created_user?$scope.learningModuleVerions[index].created_user.id:null;
			$scope.learning.version_updated_by = $scope.learningModuleVerions[index].updated_user?$scope.learningModuleVerions[index].updated_user.id:null;
		};

		/*Fetch all Versions and set default when required*/
		$scope.getSetLearningModuleVersion = function (version) {
			$http.get("<?=$LMSUri?>learning/get_versions/" + learningModuleId).then(
				function (response) {
					$scope.learningModuleVerions = response.data;
				   if(version){
						$scope.setVersion(version);
				   }
				}
			);
		};

		$scope.showSaveVersionFun = function () {
			$scope.showSaveVerion = !$scope.showSaveVerion;
			$scope.clonedVersion = $scope.learning.version;
			if($scope.clonedVersion){
				$scope.learning.version = null;
			}

		};
		// Learning resource type based version initilisation
		$scope.setVersion = function (version) {
			$scope.avilableVersion = [];
			$scope.learningModuleVerions.filter(function(element, n) {
				$scope.avilableVersion.push($scope.learningModuleVerions[n].version);
				if (element.version === version) {
					$scope.setVersionField(n);
					$scope.changedVersionIndex = n;
			   }
			});
		};

		$scope.nextVersion = function (cversion) {
			var n, index;
			index = $scope.learningModuleVerions.length - 1;
			n = cversion + 1;
			if (n <= index) {
				$scope.setVersionField(n);
				$scope.changedVersionIndex = n;
			}
		};
	$scope.addToTemplate = function () {
		$scope.template_submited = true;
		//use $ngConfirm to get Template Name
		$ngConfirm({
			title: "Add to Template",
			content:
				'<div class="form-group' +
				'"><label for="template_name">Template Name</label>' +
				'<input type="text" class="form-control" id="template_name" ng-model="template_name" required>' +
				"</div>",
			scope: $scope,
			buttons: {
				save: {
					btnClass: "btn btn-primary",
					action: function (scope) {
						scope.saveTemplate();
					},
				},
				cancel: {
					btnClass: "btn btn-danger",
					action: function (scope) {
						scope.template_name = "";
						scope.template_submited = false;
					},
				},
			},
		});
	};
	$scope.saveTemplate = function () {
		let data = {
			name: $scope.template_name,
			linked: $scope.learning.linked,
		};
		$http
			.post("<?=$LMSUri?>learning/save-linked-template", data)
			.then((res) => {
				$scope.template_alerts = [{ type: "success", msg: "Template saved successfully" }];
				$scope.template_name = "";
				$scope.template_submited = false;
			})
			.catch(() => {
				$scope.template_alerts = [
					{ type: "danger", msg: "Error while saving template" },
				];
				$scope.template_submited = false;
			});
	};
	$scope.getTemplate = function () {
		$http.get("<?=$LMSUri?>learning/get-linked-template").then((res) => {
			$scope.linked_templates = res.data;
		});
	};
	$scope.addLinkedTemplate = function () {
		if ($scope.link_template) {
			$scope.learning.linked = $scope.link_template.template;
		}
	};
	$scope.getTemplate();

		$scope.previousVersion = function (cversion) {
			var index = $scope.learningModuleVerions.length - 1,
				n
			;
			if (cversion === 0) {
				n = cversion;
			} else{
				n = cversion - 1;
			}
			if (n < index && n >= 0) {
				$scope.setVersionField(n);
				$scope.changedVersionIndex = n;
			}
		};

		$scope.checkVersionExist = function(newVersion) {
		   $scope.learningModuleVerions.filter(function(element) {
				if (element.version === newVersion) {
					$scope.versionExist = true;
				} else {
					$scope.versionExist = false;
				}
			});
		};

		$scope.setVersionjackdawHtml5 = function(version){
			$scope.learning.version = version;
			$scope.lodingBar = true;
			$http({
				method: 'PUT',
				url: "<?=$LMSUri?>learning/module/" + learningModuleId,
				data: $scope.learning
			}).then(function successCallback() {

				$scope.lodingBar = false;
				$scope.ls.jackdawHtml5($scope.resourceObj);
				$scope.closeModal();
			}, function errorCallback() {
				$timeout(function () {
					$('[uib-modal-window]').animate({
						scrollTop: $('[uib-alert]').offset().top
					}, 300);
				}, 0);
			});
		};

		$scope.saveNewVersion = function () {

			if(
				(!$scope.learning.version && !isNaN($scope.learning.version) ||
				isNaN($scope.learning.version))
			) {
				$scope.invalidVersion = true;
				return;
			} else {
				$scope.invalidVersion = false;
			}

			if(!$scope.avilableVersion.includes($scope.learning.version)){
				$scope.uploadVersionScormFile();
			} else {
				//todo: all confirms need to be combined to asingle function
				$ngConfirm({
					title: 'Alert!',
					content: 'Version number(s) '+$scope.avilableVersion+' already exist, Please Enter new version number',
					type: 'red',
					typeAnimated: true,
					buttons: {
						tryAgain: {
							text: 'Okay',
							btnClass: '',
							action: function(){
								$scope.learning.version =null;
							}
						},
					}
				});
			}
		};
		$scope.saveVersionAction =function(){
			$http({
				method: 'POST',
				url: "<?=$LMSUri?>learning/add_version",
				data: $scope.learning,
			}).then(function (response) {
				$scope.selectedVersion =response.data;
				if($scope.learning.learning_module_evidences.length >0){
					$scope.copyFiles($scope.learning.learning_module_evidences);
				}
				if (($scope.evidenceFiles && $scope.evidenceFiles.length > 0)) {
					$scope.uploadFiles($scope.evidenceFiles,$scope.learning.learning_module_evidences);
				}

				$scope.showSaveVerion = !$scope.showSaveVerion;
				$scope.disableVersionSave = false;
			}, function errorCallback() {
				$scope.disableVersionSave = false;
			}).finally(function(){
				$scope.getSetLearningModuleVersion( $scope.selectedVersion);
				$scope.disableVersionSave = false;

			});
		};

		$scope.removeVersion = function (version) {
		   if (version === $scope.learning.active_version) {
			$ngConfirm({
				title: 'Alert!',
				content: "Current active version can`t be deleted. Please update the active version before deleting this version.",
				type: 'red',
				typeAnimated: true,
				buttons: {
					tryAgain: {
						text: 'Okay',
						btnClass: '',
						action: function(){
						}
					},
				}
			});

			} else{
				$http({
					method: 'POST',
					url: "<?=$LMSUri?>learning/remove_version",
					data: {
						"id": learningModuleId,
						"version": version
					}
				}).then(function () {
					if (
						$scope.learning.learning_module_evidences &&
						$scope.learning.learning_module_evidences.length > 0
					) {
						$scope.learning.learning_module_evidences.forEach(function(e,index) {
							$scope.deleteEvidence(e,index);
						});
					}
					$scope.getSetLearningModuleVersion($scope.learning.active_version);
					// $scope.nextVersion()
					//Delete Evidences $scope.learning.learning_module_evidences
				});
		   }

		};

		$scope.cancelSaveNewVersion = function () {
			$scope.showSaveVerion = !$scope.showSaveVerion;
			$scope.learning.version = $scope.clonedVersion;
			$scope.getSetLearningModuleVersion($scope.learning.version);
			$scope.invalidVersion = false;
		};

		/*Learning Resource Type Version Management Ends Here*/


		$scope.compareMinMacClassSizes = function () {
			$scope.newsession.max_class_size = $scope.newsession.max_class_size < $scope.newsession.min_class_size ? $scope.newsession.min_class_size : $scope.newsession.max_class_size;
		};

		$scope.openNewSessionDTPicker = function () {
			$scope.newsessionDTPickerOpen.opened = true;
		};

		$scope.dataLoading = false;
		d = new Date();
		d.setHours(8);
		d.setMinutes(0);
		d.setSeconds(0);
		$scope.newsession.time = d;

		$scope.DTOptions = {
			formatYear: 'yyyy',
			initDate: new Date(),
			maxDate: new Date(2050, 1, 1),
			minDate: new Date(2000, 1, 1),
			startingDay: 1
		};

		$scope.addAssessmentCategoryQuestions = function (asm_cat) {

			if (asm_cat.all_questions === undefined) {
				asm_cat.all_questions = [];
			}

			angular.forEach($scope.learning.assessment_questions, function (asm_q) {
				n_asm_cat_q = angular.copy(asm_q);
				angular.forEach(asm_cat.questions, function (asm_cat_q) {
					if (asm_cat_q.question_id === n_asm_cat_q.question_id) {
						n_asm_cat_q.ticked = true;
					}
				});
				asm_cat.all_questions.push(n_asm_cat_q);
			});
		};
    $scope.bindcredlyBadges = function(){
        if($rootScope.config.enableCredlyBadge)
        {
            if($scope.learning && $scope.learning.credly_badges.length > 0)
            {
                $scope.credlyBadges = $scope.credlyBadges.map((badge)=>{
                    if($scope.learning.credly_badges.find(e=>e.badge_template_id==badge.id)){
                        return {
                            ...badge,...{ticked:true}
                        };
                    }
                    else{
                        return {...badge};
                    }
                })
            }
        }
        }
		$scope.getLearningModule = function () {
			$http.get("<?=$LMSUri?>learning/module/" + learningModuleId).then(
				function (response) {
					$scope.learning = response.data;
					if (!$scope.learning.self_enroll) {
						$scope.learning.self_enroll_access = $rootScope.config.sharedClients ==  false ? 0 : 1;
					}
					
                    $scope.bindcredlyBadges();
					$scope.learning.original_refresh_period = $scope.learning.refresh_period;
					$scope.learning.original_due_after_period = $scope.learning.due_after_period;
					if ($scope.learning.expiration_date) {
						$scope.learning.expiration_date = getJsDate($scope.learning.expiration_date);
					}
					if ($scope.learning.refresh_date) {
						$scope.learning.refresh_date = getJsDate($scope.learning.refresh_date);
					}
					$scope.learning.updated_at = getJsDate($scope.learning.updated_at);
					$scope.changedVersion = $scope.learning.version;
					angular.forEach($scope.learning.competencies, function (c) {
						c.points = c.pivot.points;
					});

					angular.forEach($scope.learning.assessment_categories, function (asm_cat) {
						$scope.addAssessmentCategoryQuestions(asm_cat);
					});

					// if template for refresh emails are empty, apply default one
					if (!$scope.learning.refresh_custom_email_body) {
						$scope.learning.refresh_custom_email_body =
							'<p>Dear %%USER_FNAME%% %%USER_LNAME%%,</p>' +
							'<p>It is time to take %%COURSE_NAME%% again.</p>' +
							'<p>Click <a href="%%CONFIG_LMSUrl%%app/learner/resources/%%COURSE_ID%%">here</a> to proceed.</p>';
						$scope.learning.refresh_custom_email_body_warning = true;
					}

					$scope.learning.randomStr = Math.round(Math.random() * 999999);
					$scope.learning.active_version = $scope.learning.version;
					if(!$scope.selectedVersion){
						$scope.getSetLearningModuleVersion($scope.learning.active_version);
					}else{
						$scope.getSetLearningModuleVersion($scope.selectedVersion);
					}
					$scope.completion_percentage = !!$scope.learning?.material?.completion_percentage;
				}
			);
		};
		$scope.getLearningModule();

		$http.get("<?=$LMSUri?>learning/module/all").then(
			function (response) {
				angular.forEach(response.data, function (p) {
					if (!parseInt(p.is_course, 10)) {
						this.push({'id': p.id, 'name': p.name});
					}
                }, $scope.prerequisites);
                angular.forEach(response.data, function(p) {
					this.push({'id': p.id, 'name': p.name});
			    }, $scope.linked);

			}
		);

		$scope.companies = angular.copy($rootScope.companies);
		$scope.companies.unshift({id: "NULL", name: ""});

		$scope.checkResourceTypeErrors = function () {
			var state = true,
				element;
			if (($scope.learning.type.slug === 'classroom' || $scope.learning.type.slug === 'on_the_job') && $scope.learning.material.sessions.length === 0) {
				$scope.learningResourcealerts = [{
					type: 'danger',
					msg: 'You must add content for selected %%learning_resource%% Type.'
				}];
				element = document.querySelector('.learning-type-id');
				smoothScroll(element);
				state = false;
			}
			return state;
		};

		$scope.deleteImage = function (mode) {
			var deleteRequest = function (mode, image) {
					$scope.dataLoading = true;
					$http.put(
						"<?=$LMSUri?>learning/deleteimage/module/" + learningModuleId,
						{
							field: mode,
							image: image
						}
					).then(function () {
						$scope.dataLoading = false;
						$scope.alerts = [{type: 'success', msg: 'Image file has been removed'}];
						$scope.learning[mode] = '';
					}, function (response) {
						$scope.dataLoading = false;
						$scope.alerts = [{type: 'danger', msg: response.data + '\nImage file can not be removed.'}];
					});
				},
				removeParameters = function (file) {
					return file.substring(0, file.indexOf('?')) || file;
				};

			deleteRequest(mode, removeParameters($scope.learning[mode]));
		};

		$scope.save = function (saveAction) {
			if ($scope.showSaveVerion) {
				$ngConfirm({
					title: 'Alert!',
					content: "Please save the new version details.",
					type: 'red',
					typeAnimated: true,
					buttons: {
						tryAgain: {
							text: 'Okay',
							btnClass: '',
							action: function(){
							}
						},
					}
				});
			} else {
				$scope.alerts = [];
				$scope.uploadedFiles = [];
				$scope.saveAction = saveAction;
				$scope.$broadcast('show-errors-check-validity');
				if (
					$scope.learningForm.$valid &&
					$scope.checkResourceTypeErrors()
				) {
					$scope.dataLoading = true;
					$scope.uploadScormFile(); // Check if SCORM file needs to be uploaded.
				} else {
					if (!$scope.learningForm.$valid) {
						$scope.alerts = [{type: 'danger', msg: 'Please correct the errors.'}];
						$timeout(function () {
							$('[uib-modal-window]').animate({
								scrollTop: $('[uib-alert]').offset().top
							}, 300);
						}, 0);
					}
				}
			}
		};

		// Upload Scorm File
		$scope.uploadScormFile = function () {
			$scope.learning.update_scorm_file = false;
			if ($scope.files.zipFile !== undefined && !$scope.zipFileUploaded) {
				var formData = new FormData();
				$scope.alerts = [{
					type: 'warning',
					msg: 'Uploading lesson files will take a while, do not navigate away from this page.'
				}];

				formData.append("zipfile", $scope.files.zipFile);
				formData.append("name", $scope.learning.name);
				formData.append("module_id", learningModuleId);
				formData.append("version", $scope.learning.version);
				$http.post(
					"<?=$LMSUri?>learning/module/uploadscormfile",
					formData,
					{
						transformRequest: angular.identity,
						headers: {'Content-Type': undefined}
					}
				).then(function (response) {

					$scope.learning.material.zip_file = response.data.fileName;
					$scope.learning.update_scorm_file = true;
					$scope.zipFileUploaded = true;
					$scope.saveModule(); // Save resource to database
				}, function (response) {
					$scope.dataLoading = false;
					$scope.alerts = [{type: 'danger', msg: response.data}];
				});
			} else {
				// There is no zip file, save resource to DB.
				$scope.saveModule();
			}
		};
		// Upload Scorm File used when saving version
		$scope.uploadVersionScormFile = function () {
			if ($scope.learning.type_id === 1 ) {
				var formData = new FormData();
			   $scope.disableVersionSave = true;
				$scope.alerts1 = [{
					type: 'warning',
					msg: 'Uploading lesson files to version will take a while, do not navigate away from this page.'
				}];
				if ($scope.files.zipFile) {
					formData.append("zipfile", $scope.files.zipFile);

				}
				formData.append("name", $scope.learning.name);
				formData.append("module_id", learningModuleId);
				formData.append("version", $scope.learning.version);
				formData.append("cloned_version", $scope.clonedVersion);
				formData.append("active_version", $scope.learning.active_version);
				formData.append("cloned_version_zip_file", $scope.learning.material.zip_file);

				$http.post(
					"<?=$LMSUri?>learning/module/version/uploadscormfile",
					formData,
					{
						transformRequest: angular.identity,
						headers: {'Content-Type': undefined}
					}
				).then(function (response) {
					$scope.alerts1 = [];

					$scope.learning.material.zip_file = response.data.zip_file ;
					$scope.zipFileUploaded = response.data.zip_file;
					angular.element($('#learning-files-zip_file')).val(null);
					if ($scope.files) {
						$scope.files.zipFile = null;
					}
					$scope.saveVersionAction();


				}, function (response) {
					$scope.disableVersionSave = false;
					$scope.showSaveVerion = !$scope.showSaveVerion;

					$scope.dataLoading = false;
					$scope.alerts1 = [{type: 'danger', msg: response.data}];
					setTimeout(function(){  $scope.alerts1=[]; }, 3000);
					$ngConfirm({
						title: 'Failed',
						content: response.data,
						type: 'red',
						typeAnimated: true,
						buttons: {
							tryAgain: {
								text: 'Ok',
								btnClass: '',
								action: function(){
								  $scope.getSetLearningModuleVersion($scope.learning.active_version);
								  $scope.learning.version = $scope.learning.active_version;
								}
							},
						}
					});
				});
			} else {
				$scope.saveVersionAction();
				$scope.disableVersionSave = false;


				// There is no zip file, save resource to DB.
				// $scope.saveModule();//(Changes has been commented, not aware of the functionality)
			}
		};

		// After scorm file check, save
		$scope.saveModule = function () {
			$scope.learning.update_due_at = false;
			if ($scope.copyAvailableModuleId > 0) {
				$scope.learning.availableModuleId = $scope.copyAvailableModuleId;
			}
			// if refresh period has changed, offer option to update all learning_results with new due_date
			if (
				$scope.learning.original_refresh_period !== $scope.learning.refresh_period ||
				$scope.learning.original_due_after_period !== $scope.learning.due_after_period
			) {
				$scope.updateDueAt();
			} else {
				$scope.pushData();
			}
		};

		$scope.updateDueAt = function () {
			//$scope.dataLoading = false;
			$confirm({
				text: 'The number of days after which the %%learning_resource%% is due has changed, do you wish to consequently alter the dates when these courses are next due?',
				title: 'Update Due At dates.',
				ok: 'Yes',
				cancel: 'No'
			}).then(function () {
				$scope.learning.update_due_at = true;
				$scope.pushData();
			}, function () {
				$scope.pushData();
			});
		};

		$scope.pushData = function () {
			if (
				$scope.learning.type &&
				$scope.learning.type.id
			) {
				$scope.learning.type_id = $scope.learning.type.id;
			}

			$http({
				method: 'PUT',
				url: "<?=$LMSUri?>learning/module/" + learningModuleId,
				data: $scope.learning
			}).then(function successCallback() {
				$scope.checkFileUploadList();
				$rootScope.$broadcast("entry-refresh");
			}, function errorCallback(response) {
				$scope.dataLoading = false;
				$scope.alerts = [{
					type: 'danger',
					msg: response.data + '\nThe %%learning_resource%% has not been updated, please contact administration.'
				}];
				$timeout(function () {
					$('[uib-modal-window]').animate({
						scrollTop: $('[uib-alert]').offset().top
					}, 300);
				}, 0);
			});
		};

		// logic to check what files to upload and fire relevant function.
		// Room for improvenment!!!!
		$scope.checkFileUploadList = function () {
			if ($scope.files.thumbnail !== undefined && $scope.uploadedFiles.indexOf('thumbnail') === -1) {
				$scope.uploadImageFile('thumbnail');
			} else if ($scope.files.promo_image !== undefined && $scope.uploadedFiles.indexOf('promo_image') === -1) {
				$scope.uploadImageFile('promo_image');
			} else if ($scope.files.highlight_image !== undefined && $scope.uploadedFiles.indexOf('highlight_image') === -1) {
				$scope.uploadImageFile('highlight_image');
			} else if ($scope.files.accreditation_main_logo !== undefined && $scope.uploadedFiles.indexOf('accreditation_main_logo') === -1) {
				$scope.uploadImageFile('accreditation_main_logo');
			} else if ($scope.files.accreditation_logo !== undefined && $scope.uploadedFiles.indexOf('accreditation_logo') === -1) {
				$scope.uploadImageFile('accreditation_logo');
			} else if ($scope.evidenceFiles && $scope.evidenceFiles.length > 0) {
				$scope.uploadFiles($scope.evidenceFiles);
			} else {
				$scope.afterSave();
			}
		};

		// Uploads promo.tumbnail and accreditation images
		$scope.uploadImageFile = function (file) {
			var formData = new FormData();

			$scope.imageAlerts = [{type: 'warning', msg: 'Uploading image.'}];

			formData.append(file, $scope.files[file]);
			formData.append("field", file);
			formData.append("id", $scope.learning.id);
			$http.post(
				"<?=$LMSUri?>learning/module/uploadimage",
				formData,
				{
					transformRequest: angular.identity,
					headers: {'Content-Type': undefined}
				}
			).then(function (response) {
				$scope.learning[file] = response.data;
				$scope.uploadedFiles.push(file);
				$scope.checkFileUploadList();
				$scope.imageAlerts = [];
			}, function (response) {
				var errorMsg = response.data && response.data.error
				? response.data.error
				: 'Something went wrong during image upload.';

				$scope.dataLoading = false;
				$scope.imageAlerts = [{type: 'danger', msg: errorMsg}];
				smoothScroll(document.querySelector('.thumbnail-upload'));
			});
		};

		// After all scorm/image files are uploaded and resource saved, show success and redirect back if needed.
		$scope.afterSave = function () {
			$scope.alerts = [{type: 'success', msg: 'The %%learning_resource%%/its version has been updated.'}];
			$scope.evidenceFiles = [];
			$rootScope.$broadcast("resource-list-needs-refresh");
			if ($scope.saveAction === 'exit') {
				$timeout(function () {
					$scope.dataLoading = false;
					$scope.closeModal();
				}, 750);
			} else {
				// reload resource data so that you can see updated attached evidence.
				$scope.getLearningModule();
				$scope.dataLoading = false;
			}
			$scope.learning.refresh_custom_email_body_warning = false;
		};

		$scope.closeImageAlert = function () {
			$scope.imageAlerts = [];
		};

		$scope.closeResourceAlert = function () {
			$scope.learningResourcealerts = [];
		};

		$scope.deleteSession = function (item) {
			// index can not be used because of srot functionality in angular that skews index.
			$scope.learning.material.sessions.splice($scope.learning.material.sessions.indexOf(item), 1);
		};

		$scope.addSession = function (session) {
			$scope.session_date_required = (session.date === undefined || session.date === "");
			$scope.session_date_expired = (getJsDate(session.date) < new Date());
			$scope.session_time_required = (session.time === undefined || session.time === "");
			$scope.session_trainer_required = ((session.trainer === undefined || session.trainer === "") && (session.trainer_full === undefined || session.trainer_full === ""));
			$scope.session_location_required = (session.location === undefined || session.location === "");
			$scope.session_duration_required = (session.duration === undefined || session.duration === "");
			$scope.session_min_class_size_required = (session.min_class_size === undefined || session.min_class_size === "") && $scope.learning.type.slug === 'classroom';
			$scope.session_max_class_size_required = (session.max_class_size === undefined || session.max_class_size === "") && $scope.learning.type.slug === 'classroom';

			if (
				$scope.session_date_required ||
				$scope.session_date_expired ||
				$scope.session_time_required ||
				$scope.session_trainer_required ||
				$scope.session_location_required ||
				$scope.session_duration_required ||
				$scope.session_min_class_size_required ||
				$scope.session_max_class_size_required
			) {
				return;
			}

			if (
				session.trainer_full
			) {
				session.trainer = session.trainer_full.fname + ' ' + session.trainer_full.lname;
				delete session.trainer_full.username;
				delete session.trainer_full.email;
			}

			$scope.learning.material.sessions.push({
				'date': session.date,
				'time': session.time,
				'trainer': session.trainer,
				'trainer_full': session.trainer_full,
				'location': session.location,
				'duration': session.duration,
				'min_class_size': session.min_class_size,
				'max_class_size': session.max_class_size,
				'session_uid': generateUUID(),
			});
		};

		$scope.addCompetency = function (competency) {
			competency.competency_required = (competency.id === undefined || competency.id === "");
			competency.points_required = (competency.points === undefined || competency.points === "");
			var found = $scope.learning.competencies.filter(function (c) {
				return c.id === competency.id;
			});

			competency.already_added = found.length > 0;

			if (competency.competency_required || competency.points_required || competency.already_added) {
				return;
			}


			$scope.competencies.filter(function (c) {
				if (c.id === competency.id) {
					$scope.learning.competencies.push({
						"id": competency.id,
						"points": competency.points,
						"name": c.name,
					});
					competency.id = "";
					competency.points = "";
					return;
				}
			});

		};

		$scope.deleteCompetency = function (c_i) {
			$scope.learning.competencies.splice(c_i, 1);
		};
    $scope.addLinkedResource = function (link) {
        link.required = false;
        link.assign_required = false;
        link.days_required = false;
        if(link.id === undefined || link.id === "")
        {
            link.required = true;
            return;
        }
        if(link.assign === undefined || link.assign === "")
        {
            link.assign_required = true;
            return;
        }
        if((link.days === undefined || link.days === "") && link.assign=='post_completion_after'){
            link.days_required = true;
            return;
        }
        var found = $scope.learning.linked.filter(function(c) {
            return c.id === link.id;
        });
        link.already_added = found.length > 0;
        if (link.already_added) {
            return;
        }
        $scope.linked.filter(function (c) {
          if (c.id === link.id) {
            $scope.learning.linked.push({
              id: link.id,
              name: c.name,
              assign: link.assign,
              days: link.days
            });
            link.id = "";
            link.assign = "immediately";
            link.days = "";
            return;
          }
        });
	};
    $scope.deleteLink = function(p_i) {
		$scope.learning.linked.splice(p_i, 1);
	};
    $scope.capilzeWords = function (value) {
        let str = value.replaceAll("_", " ");
        return str.replace(/\w\S*/g, function(txt){return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();});
    }
		$scope.addPrerequisite = function (prerequisite) {
			prerequisite.required = (prerequisite.id === undefined || prerequisite.id === "");
			var found = $scope.learning.prerequisites.filter(function (p) {
				return p.id === prerequisite.id;
			});
			prerequisite.already_added = found.length > 0;

			if (prerequisite.required || prerequisite.already_added) {
				return;
			}

			$scope.prerequisites.filter(function (p) {
				if (p.id === prerequisite.id) {
					$scope.learning.prerequisites.push({
						"id": prerequisite.id,
						"name": p.name,
					});
					prerequisite.id = "";
					prerequisite.points = "";
					return;
				}
			});
		};

		$scope.deletePrerequisite = function (p_i) {
			$scope.learning.prerequisites.splice(p_i, 1);
		};


		$scope.addAssessmentCategory = function (new_asm_category_name) {
			$scope.new_asm_category_required = (new_asm_category_name === undefined || new_asm_category_name === "");
			var found = $scope.learning.assessment_categories.filter(function (asm_cat) {
					return asm_cat.title === new_asm_category_name;
				}),
				new_asm_cat
			;
			$scope.new_asm_category_already_added = found.length > 0;
			if ($scope.new_asm_category_required || $scope.new_asm_category_already_added) {
				return;
			}
			new_asm_cat = {
				"id": Math.floor(Math.random() * 1000000),
				"course_id": $scope.learning.id,
				"title": new_asm_category_name,
				"questions": [],
			};

			$scope.addAssessmentCategoryQuestions(new_asm_cat);
			$scope.learning.assessment_categories.push(new_asm_cat);
		};

		$scope.getAssessmentQuestions = function () {
			//a = angular.copy($scope.learning.assessment_questions);
			return $scope.learning.assessment_questions;
		};

		$scope.removeAssessmentCategory = function (asm_category_id) {
			$scope.learning.assessment_categories = $scope.learning.assessment_categories.filter(function (asm_cat) {
				return asm_cat.id !== asm_category_id;
			});
		};

		//Copy evidence files
		$scope.copyFiles = function (evidences){
			$http({
				method: 'POST',
				url: "<?=$LMSUri?>learner/evidence/copy_evidence",
				data: {
					'evidences':evidences,
					'module_id':$scope.learning.version,
					'version':$scope.selectedVersion
				},
			});
		};

		// Upload Evidence files.
		$scope.log = '';
		$scope.uploadFiles = function (files) {
			$scope.filesUploaded = 0;
			$scope.filesTotal = files.length;
			$scope.upload = {};
			var i = 0,
				file,
				uploadAction = function (file) {
					$scope.upload[file.name] = Upload.upload({
						url: '<?=$LMSUri?>learner/evidence/add/file',
						data: {
							learningId: learningModuleId,
							version: $scope.learning.version,
							file: file,
						}
					});

					$scope.upload[file.name].then(function (resp) {
						$timeout(function () {
							$scope.log = 'file: ' +
								resp.config.data.file.name +
								', Response: ' + JSON.stringify(resp.data) +
								'\n' + $scope.log;

						});
					}, null, function (evt) {
						var progressPercentage = parseInt(100.0 * evt.loaded / evt.total, 10);
						file.percentage = progressPercentage;

						$scope.log = 'progress: ' + progressPercentage +
							'% ' + evt.config.data.file.name + '\n' +
							$scope.log;
					});

					$scope.upload[file.name].finally(function () {
						$scope.filesUploaded++;
						if ($scope.filesTotal === $scope.filesUploaded) {
							$scope.evidenceFiles = [];
							$scope.checkFileUploadList(); // check again if some files are needed to be uploaded
						}
					});
				};

			if (files && files.length) {
				for (i = 0; i < files.length; i++) {
					file = files[i];
					if (!file.$error) {
						uploadAction(file);
					}
				}
			}
		};

		// remove to-be uploaded file from list
		$scope.removeFile = function (index) {
			if ($scope.upload && $scope.upload[$scope.evidenceFiles[index].name]) {
				$scope.upload[$scope.evidenceFiles[index].name].abort();
			}
			$scope.evidenceFiles.splice(index, 1);
		};

		// remove already uploaded files
		$scope.deleteEvidence = function (evidence, index) {
			if (evidence.id) {
				$http({
					method: 'GET',
					url: '<?=$LMSUri?>learner/evidence/remove/' + evidence.id
				}).then(function successCallback() {
					$scope.learning.learning_module_evidences.splice(index, 1);
				}, function errorCallback(response) {
					$scope.alerts = [{type: 'danger', msg: response.data}];
					$timeout(function () {
						$('[uib-modal-window]').animate({
							scrollTop: $('[uib-alert]').offset().top
						}, 300);
					}, 0);
				});
			} else {
				$scope.removeEntry(index, $scope.learning.learning_module_evidences);
			}
		};

		$scope.addEvidenceComment = function () {
			$http({
				method: 'POST',
				url: '<?=$LMSUri?>learning/module/' + $scope.learning.id + '/comment',
				data: {
					comment_url: $scope.comment_url,
					version: $scope.learning.version ? $scope.learning.version : 1
				}
			}).then(function successCallback(response) {
				$scope.learning.learning_module_evidences.push(response.data);
				$scope.comment_url = '';
			}, function errorCallback(response) {
				$scope.alerts = [{ type: 'danger', msg: response.data + '\nComment could not be added, please contact administration.' }];
			});
		};

		$scope.customType = {
			castValues: function () {
				// Check for type and learning_module_type_parameter collection, if exists, cast values on existing material data
				if (
					$scope.learning.type.type === 1 &&
					$scope.learning.material &&
					angular.isObject($scope.learning.material) &&
					Object.keys($scope.learning.material).length > 0
				) {
					angular.forEach($scope.learning.type.learning_module_type_parameter, function (param) {
						if (
							param.parametertype === 'date' &&
							$scope.learning.material[param.parameterslug]
						) {
							$scope.learning.material[param.parameterslug] = getJsDate($scope.learning.material[param.parameterslug]);
						}
					});
				}
			}
		};

		$scope.recalculationInProgress = false;
		$scope.recalculateCourseUserResult = function () {
			$scope.recalculationInProgress = true;
			$http({
				method: 'GET',
				url: '<?=$LMSUri?>learning/module/' + $scope.learning.id + '/recalculate/' + $scope.learning.material.min_passing_percentage
			}).then(function successCallback(response) {
				$ngConfirm({
					title: 'Recalculation completed!',
					content: response.data,
					buttons: {
						tryAgain: {
							text: 'ok',
							btnClass: 'btn-blue',
							action: function() {
							}
						},
					}
				});
				$scope.recalculationInProgress = false;
			}, function errorCallback(response) {
				$ngConfirm({
					title: 'Recalculation failed!',
					content: response.data,
					buttons: {
						tryAgain: {
							text: 'ok',
							btnClass: 'btn-blue',
							action: function() {
							}
						},
					}
				});
				$scope.recalculationInProgress = false;
			});
		};

		$scope.downloadInProgress = false;
		$scope.downloadCourseFiles = () =>
		{
			$scope.downloadInProgress = true;

			fetch('<?=$LMSUri?>learning/module/download/' + $scope.learning.id, { method: 'GET' })
			.then(res => {
				if (res.ok) res.blob().then(blob =>
					{
						$scope.downloadInProgress = false;
						var file = window.URL.createObjectURL(blob);
						window.location.assign(file);
					});
				else
				{
					$ngConfirm({
						title: 'Download failed!',
						content: res.statusText,
						buttons: {
							tryAgain: {
								text: 'ok',
								btnClass: 'btn-blue',
								action: function() {
								}
							},
						}
					});
					$scope.downloadInProgress = false;
				}
			})

		};

		$scope.accordions = {
			mandatory: true
		};

		$scope.toggleSection = function($event, element_id) {
			$accordionService.toggleSection($scope, $event, element_id);
		};
	}
);
