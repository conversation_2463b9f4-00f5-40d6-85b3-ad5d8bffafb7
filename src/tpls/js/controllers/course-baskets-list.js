
angular.module('lmsApp')
	.controller('CourseBasketsListController', function ($scope, $rootScope, $http, $confirm, CourseBasketService
	) {
		// Initialize data
        $scope.cartItems = [];
        $scope.isProcessing = false;
        var baseUrl = "<?=$LMSUri?>course-basket/";

        // Function to load cart items on page load
        function loadCartItems() {
            
            $http.get(baseUrl +'list') //Replace with your actual API endpoint
                .then(function (response) {
                    $scope.cartItems = response.data;
                    
                })
                .catch(function (error) {
                    console.error('Error fetching cart items:', error);
                });
        }

        // Call loadCartItems when controller initializes
        loadCartItems();

        $scope.removeFromCart = function (itemId) {
            if (!itemId) return;
        
            $http.post(baseUrl +'remove', { id: itemId })
                .then(function (response) {
                    // Filter out removed item from local cart view
                    $scope.cartItems = $scope.cartItems.filter(function (item) {
                        return item.id !== itemId;
                    });
                    $rootScope.multiCartItemCount = response.data.count;
                }, function (error) {
                    console.error('Failed to remove item:', error);
                });
        };

        $scope.getCartSubtotal = function () {
            if (!$scope.cartItems || $scope.cartItems.length === 0) {
                return 0;
            }
            
            return $scope.cartItems.reduce(function (total, course) {
                var cost = parseFloat(course.item.cost);
                return total + (isNaN(cost) ? 0 : cost);
            }, 0);
        };

        $scope.clearCart = function () {
            $confirm({
                text: 'Are you sure you want to clear your cart ?',
                title: 'Clear cart',
            }).then(function () {
                $scope.alerts = [];
                $http.post(baseUrl + 'clear')
                .then(function successCallback(response) {
                    $rootScope.multiCartItemCount = response.data.count;
                    $scope.cartItems = [];
                    $scope.alerts = [{type: 'success', msg: 'Dataset has been deleted.'}];
                }, function errorCallback() {
                    $scope.alerts = [{type: 'danger', msg: 'Dataset has been deleted.'}];
                });
            });
        };

        //  checkout 
        $scope.checkout = function () {
            if ($scope.cartItems.length === 0) {
                $scope.error = 'Your cart is empty';
                return;
            }
            $scope.isProcessing = true;
            $scope.error = null;
            CourseBasketService.checkout($scope.cartItems)
            // .then(function() {
                
            // })
            // .catch(function(error) {
            //     $scope.error = error;
            //     $scope.isProcessing = false;
            // });
        };
		
    }); //end controller
