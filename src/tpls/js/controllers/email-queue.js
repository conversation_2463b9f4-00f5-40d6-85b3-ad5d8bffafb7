angular.module('lmsApp')
	.controller('EmailQueueController', function ($scope, $rootScope, $http, $learnerProgrammes) {

		$scope.data = [];
		$scope.previousTableState = false;
		$scope.searchStatus = 1;
		$scope.refreshTable = 0;
		$scope.lp = $learnerProgrammes;
    $scope.disableBtn = false;
		$scope.closeAlert = function() {
			$scope.alerts = [];
		};

		$scope.callServer = function (tableState) {
			updateReport(tableState, "<?=$LMSUri?>email-queue/list", $scope, $http, function() {
				$rootScope.dashboard.pageLoaded = true;
			});
		};
    $scope.bulkAprrove = function(){
      $scope.disableBtn = true;
      $http({
        method:'PUT',
        url:'<?=$LMSUri?>email-queue/approve-all',
      }).then(()=>{
        $scope.alerts = [{type:'success',msg: 'All Queue items approved. Mail send soon'}];
        $scope.refreshTable++;
        $scope.disableBtn = false;
      }).catch(()=>{
        $scope.alerts = [{ type: 'danger', msg: 'Error approving Queue items.' }];
        $scope.disableBtn = false;
      })
    }
    $scope.bulkDelete = function(){
      $scope.disableBtn = true;
      $http({
        method:'DELETE',
        url:'<?=$LMSUri?>email-queue/all',
      }).then(()=>{
        $scope.alerts = [{type:'success',msg: 'All Queue items deleted'}];
        $scope.refreshTable++;
        $scope.disableBtn = false;
      }).catch(()=>{
        $scope.alerts = [{ type: 'danger', msg: 'Error deleting Queue items.' }];
        $scope.disableBtn = false;
      })
    }
		$scope.approve = function(id) {
			$http({
				method: 'POST',
				url: '<?=$LMSUri?>email-queue/approve/'+ id,
			}).then(function successCallback() {
				$scope.alerts = [{ type: 'success', msg: 'Queue item is approved.' }];
				$scope.refreshTable++;
			}, function errorCallback() {
				$scope.alerts = [{ type: 'danger', msg: 'Error approving Queue item.' }];
			});
		};

		$scope.disapprove = function(id) {
			$http({
				method: 'POST',
				url: '<?=$LMSUri?>email-queue/disapprove/'+ id,
			}).then(function successCallback() {
				$scope.alerts = [{ type: 'success', msg: 'Queue item is disapproved.' }];
				$scope.refreshTable++;
			}, function errorCallback() {
				$scope.alerts = [{ type: 'danger', msg: 'Error disapproving Queue item.' }];
			});
		};
	});
