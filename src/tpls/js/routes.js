angular.module('lmsApp')
	.config(function($routeProvider, $locationProvider) {
		$routeProvider
			.when('/user/import', {
				templateUrl: '<?=$LMSTplsUriHTML?>importusers.html?v=<?=$version?>',
				controller: 'ImportUsersController',
			})
			.when('/country/add', {
				templateUrl: '<?=$LMSTplsUriHTML?>addcountry.html?v=<?=$version?>',
				controller: 'AddCountryController',
			})
			.when('/country/:countryId', {
				templateUrl: '<?=$LMSTplsUriHTML?>editcountry.html?v=<?=$version?>',
				controller: 'EditCountryController',
			})
			// combined add/edit city template and controllers.
			.when('/city/:cityId', {
				templateUrl: '<?=$LMSTplsUriHTML?>cityactions.html?v=<?=$version?>',
				controller: 'CityActionsController',
			})
			.when('/location/add', {
				templateUrl: '<?=$LMSTplsUriHTML?>addlocation.html?v=<?=$version?>',
				controller: 'AddLocationController',
			})
			.when('/location/:locationId', {
				templateUrl: '<?=$LMSTplsUriHTML?>editlocation.html?v=<?=$version?>',
				controller: 'EditLocationController',
			})
			.when('/competency/add', {
				templateUrl: '<?=$LMSTplsUriHTML?>addcompetency.html?v=<?=$version?>',
				controller: 'AddCompetencyController',
			})
			.when('/competency/:competencyId', {
				templateUrl: '<?=$LMSTplsUriHTML?>editcompetency.html?v=<?=$version?>',
				controller: 'EditCompetencyController',
			})
			.when('/changepassword', {
				templateUrl: '<?=$LMSTplsUriHTML?>changepassword.html?v=<?=$version?>',
				controller: 'ChangepasswordController',
			})
			.when('/refreshlearning', {
				templateUrl: '<?=$LMSTplsUriHTML?>refreshlearning.html?v=<?=$version?>',
				controller: 'RefreshLearningController',
			})
			.when('/learningprovider/add', {
				templateUrl: '<?=$LMSTplsUriHTML?>addlearningprovider.html?v=<?=$version?>',
				controller: 'AddLearningProviderController',
			})
			.when('/learningprovider/:learningproviderId', {
				templateUrl: '<?=$LMSTplsUriHTML?>editlearningprovider.html?v=<?=$version?>',
				controller: 'EditLearningProviderController',
			})
			.when('/learningcategory/add', {
				templateUrl: '<?=$LMSTplsUriHTML?>addlearningcategory.html?v=<?=$version?>',
				controller: 'AddLearningCategoryController',
			})
			.when('/learningcategory/:learningcategoryId', {
				templateUrl: '<?=$LMSTplsUriHTML?>editlearningcategory.html?v=<?=$version?>',
				controller: 'EditLearningCategoryController',
			})
			.when('/learningresourcesmaintenance', {
				templateUrl: '<?=$LMSTplsUriHTML?>learningresourcesmaintenance.html?v=<?=$version?>',
				controller: 'LearningResourcesMaintenanceController',
			})
			.when('/learning/installlearning', {
				templateUrl: '<?=$LMSTplsUriHTML?>installlearning.html?v=<?=$version?>',
				controller: 'InstallLearningController',
			})
			.when('/learner', {
				templateUrl: '<?=$LMSTplsUriHTML?>learner.html?v=<?=$version?>',
				controller: 'LearnerController',
			})
			// resource parameter will be "learning_modules.id" and if that module is course and linked with event it can have "-schedules.id" added to it
			.when('/learner/resources/:resource?/:tab?', {
				templateUrl: '<?=$LMSTplsUriHTML?>learnerresources.html?v=<?=$version?>',
				controller: 'LearnerResourcesController',
				reloadOnSearch: false,
				reloadOnUrl: false
			})
			.when('/learner/progress', {
				templateUrl: '<?=$LMSTplsUriHTML?>learnerprogress.html?v=<?=$version?>',
				controller: 'LearnerProgressController',
			})
			.when('/learner/tasks/calendar', {
				templateUrl: '<?=$LMSTplsUriHTML?>learnertasks.html?v=<?=$version?>',
				controller: 'LearnerTasksController',
				reloadOnSearch: false,
				reloadOnUrl: false
			})
			.when('/learner/tasks/:view?', {
				templateUrl: '<?=$LMSTplsUriHTML?>learnertasks.html?v=<?=$version?>',
				controller: 'LearnerTasksController',
				reloadOnSearch: false,
				reloadOnUrl: false
			})
			.when('/learner/distribute', {
				templateUrl: '<?=$LMSTplsUriHTML?>learner-distribute.html?v=<?=$version?>',
				controller: 'LearnerDistribute',
			})
			.when('/learner/categories', {
				templateUrl: '<?=$LMSTplsUriHTML?>learner-categories-landing.html?v=<?=$version?>',
				controller: 'LearnerCategoriesLanding',
			})
			.when('/learner/reports', {
				templateUrl: '<?=$LMSTplsUriHTML?>learner-reports.html?v=<?=$version?>',
				controller: 'AssignedReports',
			})
			.when('/mylearning/:group?', {
				templateUrl: '<?=$LMSTplsUriHTML?>mylearning.html?v=<?=$version?>',
				controller: 'LessonsAndLearningResources',
			})
			.when('/myassessment/:assessmentId', {
				templateUrl: '<?=$LMSTplsUriHTML?>myassessment.html?v=<?=$version?>',
				controller: 'MyAssessmentController',
			})
			.when('/skill-scan/:learningResultId', {
				templateUrl: '<?=$LMSTplsUriHTML?>skillscan.html?v=<?=$version?>',
				controller: 'SkillScanController',
			})
			.when('/skill-scan/answer/:answerId', {
				templateUrl: '<?=$LMSTplsUriHTML?>skillscananswer.html?v=<?=$version?>',
				controller: 'SkillScanAnswerController',
			})
			.when('/myassessment/:assessmentId/:taskId', {
				templateUrl: '<?=$LMSTplsUriHTML?>myassessmenttask.html?v=<?=$version?>',
				controller: 'MyAssessmentTaskController',
			})
			.when('/myenroll', {
				templateUrl: '<?=$LMSTplsUriHTML?>myenroll.html?v=<?=$version?>',
				controller: 'MyEnrollController',
			})
			.when('/bookmodule/:learningModuleId/:userId', {
				templateUrl: '<?=$LMSTplsUriHTML?>bookmodule.html?v=<?=$version?>',
				controller: 'BookModuleController',
			})
			.when('/learning/:learningModuleId/view', {
				templateUrl: '<?=$LMSTplsUriHTML?>viewlearning.html?v=<?=$version?>',
				controller: 'ViewModuleController',
			})
			.when('/learning/:learningModuleId/:userId/:learningResultId', {
				templateUrl: '<?=$LMSTplsUriHTML?>viewlearning.html?v=<?=$version?>',
				controller: 'ViewLearningController',
			})
			.when('/labels', {
				templateUrl: '<?=$LMSTplsUriHTML?>labels.html?v=<?=$version?>',
				controller: 'LabelsController',
			})
			.when('/label/add', {
				templateUrl: '<?=$LMSTplsUriHTML?>addlabel.html?v=<?=$version?>',
				controller: 'AddLabelController',
			})
			.when('/label/:labelId', {
				templateUrl: '<?=$LMSTplsUriHTML?>editlabel.html?v=<?=$version?>',
				controller: 'EditLabelController',
			})
			.when('/learners/:id', {
				templateUrl: '<?=$LMSTplsUriHTML?>learners.html?v=<?=$version?>',
				controller: 'learnersController',
			})

			.when('/cdashboard', {
				templateUrl: '<?=$LMSTplsUriHTML?>customdashboard.html?v=<?=$version?>',
				controller: 'CustomDashboardController',
			})

			// Revisited interface additions, preserving previous stucture as much as possible for now
			// Subject to massive changes and improvenments welcome!
			.when('/dashboard/:groupKey?/:tabKey?/:pageKey?', {
				templateUrl: '<?=$LMSTplsUriHTML?>dashboard.html?v=<?=$version?>',
				controller: 'dashboardController',
				reloadOnSearch: false,
				reloadOnUrl: false
			})

			// Personal knowledge file implementation, all that "SMCR" thing
			.when('/personal-knowledge-file', {
				templateUrl: '<?=$LMSTplsUriHTML?>personal-knowledge-file.html?v=<?=$version?>',
				controller: 'PersonalKnowledgeFile',
			})
			.when('/course-baskets/list', {
				templateUrl: '<?=$LMSTplsUriHTML?>course-baskets-list.html',
				controller: 'CourseBasketsListController',
			})
			.when('/course-basket/stripe/payment/success', {
				templateUrl: '<?=$LMSTplsUriHTML?>course-basket-payment-success.html',
				controller: 'CourseBasketsListController',
			})
			.when('/course-basket/stripe/payment/failure', {
				templateUrl: '<?=$LMSTplsUriHTML?>course-basket-payment-failure.html',
				controller: 'CourseBasketsListController',
			})

			.when('/learner/form-view', {
				templateUrl: '<?=$LMSTplsUriHTML?>learnerresources.html',
				controller: 'ModalFormView'
			})
			.when('/learner/workflow-view', {
				templateUrl: '<?=$LMSTplsUriHTML?>workflow-view.html',
				controller: 'AssignedReports'
			})
		;
		$locationProvider.html5Mode(true);
	});
