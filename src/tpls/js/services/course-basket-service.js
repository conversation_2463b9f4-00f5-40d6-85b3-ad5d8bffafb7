angular.module('lmsApp').factory('CourseBasketService', function($http, $q, $rootScope, $uibModal) {
    
    var baseUrl = "<?=$LMSUri?>course-basket/";
    var appUrl = "<?=$LMSUri?>app/";
    const API_BASE_GLOBAL = '<?=$LMSUri?>payment-gateway/globalpayment';
    let receiveMessage = function(data){
        $rootScope.$broadcast("enablePaymentLoading",false); 
    }
    return {
        
        addToBasket: function(itemId, itemType) {
            
            $http({
                method: 'POST',
                url: "<?=$LMSUri?>course-basket/add-to-basket",
                data: {
                    item_id: itemId,
                    item_type: itemType
                }
            }).then(function successCallback(response) {
                alert("Item added to basket!");
                $rootScope.multiCartItemCount = response.data.count;
            }).catch(function(err) {
                alert(err?.data?.error || "Something went wrong");
            });
        },
        getCount: function() {
            return $http.get(baseUrl + "basket-count").then(function (response) {
                return response.data.count;
            });
        },
        checkout: function (cart) {
            if ($rootScope.config.isCivicaPaymentsEngine) {
                this.civicaPay(cart);
            }
            else if ($rootScope.config.enableStripePayment){
                console.log('stripe payment');
                
                this.stripePay(cart);
            }
            else if ($rootScope.config.enablePay360){
                this.pay360(cart)
            }
            else if ($rootScope.config.isGlobalPaymentsEngine){
                this.globalPay(cart);
            }
            else {
                alert("No payment gateway configured.");
            }
        },

        stripePay: function(cart){

            var deferred = $q.defer();
            var cartData = [];
            const typeMap = {
                'Models\\LearningModule': 'learning_modules',
                'Models\\Schedule': 'schedules',
                'Models\\ApprenticeshipStandard': 'programme'
            };
            
            cart.forEach((value, index) => {
                cartData.push({
                    item_id: value.item.id,
                    item_type: typeMap[value.item_type],
                    coupon : null,
                    user_id: null
                })
            });

            $http({
                method: 'POST',
                url: `${baseUrl}stripe/payment`,
                data: cartData
            }).then(function successCallback(response) {
                if (response.data.status === 1) {
                    // Store success/fail URLs in localStorage
                    localStorage.setItem('course_success_url', `${appUrl}course-basket/stripe/payment/success`);
                    localStorage.setItem('course_failed_url', `${appUrl}course-basket/stripe/payment/failure`);
                    
                    // Redirect to Stripe checkout
                    var stripe = Stripe($rootScope.config.stripePublishableKey);
                    
                    stripe.redirectToCheckout({
                        sessionId: response.data.sessionId
                    }).then(function(result) {
                        console.log('Redirected to:', result.url); 
                        if (result.error) {
                            deferred.reject(result.error.message);
                        }
                    });
                } else {
                    deferred.reject(response.data.error || 'Payment processing failed');
                }
            }).catch(function(error) {
                deferred.reject(error.data && error.data.error ? error.data.error : 'Payment processing failed');
            });
            return deferred.promise;
        },
        globalPay: function(cart) {
            localStorage.setItem('course_success_url', `${appUrl}course-basket/stripe/payment/success`);
            localStorage.setItem('course_failed_url', `${appUrl}course-basket/stripe/payment/failure`);
            var deferred = $q.defer();
            var modalInstance = $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: '<?=$LMSTplsUriHTML?>modal-global-payment.html',
                controller: 'modalGlobalPayment',
                size: 'lg',
                windowClass: 'u-modal--full-width',
                backdrop: 'static',
                resolve: {
                  data: function() {
                    return {
                        cart,
                        multiple: true
                    };
                  }
                }
            });
             // Handle modal results
             modalInstance.result.then(function(response) {
                deferred.resolve(response); // Payment succeeded
            }).catch(function(error) {
                if (error === 'cancel') {
                    deferred.reject('Payment was cancelled');
                } else {
                    deferred.reject(error || 'Payment processing failed');
                }
            });
            
            return deferred.promise;
        },
        globalMakePayment: function(data={},closeModal = false,$scope){
            $rootScope.$broadcast("enablePaymentLoading",true);
          
            $http({
              method: 'POST',
              url: '<?=$LMSUri?>course-basket/global/payment',
              data:data
            }).then(function successCallback(response) {
                console.log(response);
                
              if (response.data.error){
                $rootScope.$broadcast("enablePaymentLoading",false);
                $scope.alerts = [{ type: 'danger', msg: response.data.error }];
                return;
              }
              $scope.amount = response.data.AMOUNT/100;
               document.getElementById('paymentDetails').style.display="none";
               document.getElementById("testPayment").style.display="block";
              document.getElementById("paymentID").style.display="block";
              RealexHpp.setHppUrl($rootScope.config.globalPaymentsEngineRequestURL);
              // When using full page redirect your Response URL must be an absolute link
              // RealexHpp.redirect.init("payButtonId", "<?=$LMSUri?>payment-gateway/globalpayment/response", response.data);
              // RealexHpp.redirect.init("payButtonId", "https://testdb.strikerlulu.me/payment-gateway/globalpayment/response", response.data);
        
              //RealexHpp.lightbox.init("autoload", "<?=$LMSUri?>payment-gateway/globalpayment/response", response.data);
              if (closeModal) {
                closeModal();
              }
              const lmsUri = "<?=$LMSUri?>";
              const baseUrl = window.location.origin + (lmsUri.endsWith("/") ? lmsUri : lmsUri + "/");
              RealexHpp.embedded.init(
                    "paynow",
                    "testPayment",
                    baseUrl + "course-basket/global/payment/response",
                    response.data
                );
              document.getElementById("paynow").click();
               if (window.addEventListener) {
                   window.addEventListener('message', receiveMessage, false);
               } else {
                   window.attachEvent('message', receiveMessage);
               }
            });
        },
        pay360: function(cart) {
            $http.post(baseUrl +'pay360/payment', { cartItems: cart })
            .then(function(response) {
                if (response.data.redirectUrl) {
                    // Store success/fail URLs in localStorage
                    localStorage.setItem('course_success_url', `${appUrl}course-basket/stripe/payment/success`);
                    localStorage.setItem('course_failed_url', `${appUrl}course-basket/stripe/payment/failure`);
                    // Redirect to Pay360 hosted payment page
                    window.location.href = response.data.redirectUrl;
                } else {
                    alert("Unexpected response from payment gateway.");
                }
            }, function(error) {
                alert("Failed to initiate Pay360 payment.");
                console.error(error);
            });
            
        },
        civicaPay: function(cart) {
            console.log('civicaPay');
            localStorage.setItem('course_success_url', `${appUrl}course-basket/stripe/payment/success`);
            localStorage.setItem('course_failed_url', `${appUrl}course-basket/stripe/payment/failure`);
            var deferred = $q.defer();
        }

    };
});
