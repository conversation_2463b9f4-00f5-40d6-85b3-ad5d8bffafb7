angular.module('lmsApp')
    .service('chatService', function($sce, $timeout) {
        const CHAT_URL = "<?=$LMSUri?>chat-bot/stream-response";

        // ✅ Markdown formatting
        this.formatMarkdown = function(text) {
            if (!text || !text.trim()) return text;
            let formatted = text.trim();

            formatted = formatted.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');
            formatted = formatted.replace(/\*([^*]+)\*/g, '<em>$1</em>');
            formatted = formatted.replace(/^(\d+)\.\s+(.+)$/gm, '<div class="chat-list-item"><span class="chat-list-number">$1.</span> $2</div>');
            formatted = formatted.replace(/^[\-\*]\s+(.+)$/gm, '<div class="chat-list-item">• $1</div>');
            formatted = formatted.replace(/\n{3,}/g, '\n\n');
            formatted = formatted.replace(/^\s*\n/gm, '\n');
            formatted = formatted.replace(/\n/g, '<br>');
            formatted = formatted.replace(/(<br>\s*){3,}/g, '<br><br>');

            return '<div class="chat-formatted-response">' + formatted + '</div>';
        };

        // ✅ Streaming AI response handler
        this.streamResponse = function({ message, streamType = 'word', onChunk, onComplete, onError }) {
            let fullResponse = '';
            let completed = false;
            const self = this;

            fetch(CHAT_URL, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    message: message,
                    stream: true,
                    stream_type: streamType,
                    format: 'markdown'
                })
            }).then(response => {
                
                if (!response.ok) throw new Error('Network response error');

                //Check if server returned early JSON error
                const contentType = response.headers.get('content-type') || '';
                if (contentType.includes('application/json')) {
                    const json = response.json();
                    if (json.error && json.reply) {
                        onError(json.reply);
                        return;
                    }
                }


                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';

                function readStream() {
                    reader.read().then(({ done, value }) => {
                        if (done || completed) return;

                        buffer += decoder.decode(value, { stream: true });
                        const lines = buffer.split('\n');
                        buffer = lines.pop(); // retain partial line

                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                try {
                                    const data = JSON.parse(line.slice(6));
                                    switch (data.type) {
                                        case 'chunk':
                                            fullResponse += data.content;
                                            onChunk($sce.trustAsHtml(self.formatMarkdown(fullResponse)));
                                            break;

                                        case 'complete':
                                            completed = true;
                                            onComplete(fullResponse);
                                            break;

                                        case 'error':
                                            completed = true;
                                            onError(data.message || 'Unknown error');
                                            break;
                                        
                                    }
                                } catch (e) {
                                    console.error('SSE JSON parse error:', e);
                                }
                            }
                        }

                        if (!completed) readStream();
                    }).catch(error => {
                        if (!completed) {
                            completed = true;
                            onError('Streaming error: ' + error.message);
                        }
                    });
                }

                readStream();
            }).catch(error => {
                onError('Connection failed: ' + error.message);
            });

            // ✅ Timeout fallback
            $timeout(() => {
                if (!completed) {
                    completed = true;
                    onError('AI response timed out after 30 seconds.');
                }
            }, 30000);
        };
    });
