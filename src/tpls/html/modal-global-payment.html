<div class="modal-header">
  <h3 class="modal-title pull-left">
    Payment
  </h3>
  <div class="pull-right">
    <button class="btn btn-primary" ng-click="payment()" ng-if="billing_address">
      Proceed Payment
    </button>
    <button class="btn btn-danger c-btn-cancel" type="button" ng-click="closeModal()">Close</button>
  </div>

</div>
<div class="modal-body">
  <a id="topPosition"></a>
  <div class="x_panel">
    <div uib-alert ng-repeat="alert in alerts" class="u-white--pre-line" type="{{alert.type}}"
      ng-class="'alert-' + (alert.type || 'warning')" close="closeAlert($index)" ng-bind="alert.msg"></div>
    <span loading-bar ng-if="enableLoading">Loading...</span>
    <div class="x_content">
      <form id="paymentDetails" name="paymentDetails" class="form-horizontal form-label-left" novalidate>
        <div class="form-group" ng-class="!paymentDetails.email.$valid?'has-error':''">
          <label class="control-label col-xs-12 col-sm-3">
            Email Address
          </label>
          <div class="col-sm-6 col-xs-12">
            <input class="form-control" id="email" name="email" ng-model="billingData.email" placeholder="Email"
              type="email" ng-required="true" />
            <p class="text-danger u-padding--five bg-danger" ng-if="paymentDetails.email.$error.required">
              Email is required!
            </p>
            <p class="text-danger u-padding--five bg-danger" ng-if="paymentDetails.email.$error.email">
              Email is not a valid email address!
            </p>
          </div>
        </div>
        <div class="form-group" ng-class="!paymentDetails.address1.$valid?'has-error':''">
          <label class="control-label col-xs-12 col-sm-3">
            Address 1
          </label>
          <div class="col-sm-6 col-xs-12">
            <textarea class="form-control" id="address1" name="address1" ng-required="true"
              ng-model="billingData.address1" placeholder="Address 1" rows="3"></textarea>
            <p class="text-danger u-padding--five bg-danger" ng-if="paymentDetails.address1.$error.required">
              Address 1 is required!
            </p>
          </div>
        </div>
        <div class="form-group">
          <label class="control-label col-xs-12 col-sm-3">
            Address 2
          </label>
          <div class="col-sm-6 col-xs-12">
            <textarea class="form-control" id="address2" name="address2"
              ng-model="billingData.address2" placeholder="Address 2" rows="3"></textarea>
            <p class="text-danger u-padding--five bg-danger" ng-if="paymentDetails.address2.$error.required">
              Address 2 is required!
            </p>
          </div>
        </div>
        <div class="form-group" ng-class="!paymentDetails.city.$valid?'has-error':''">
          <label class="control-label col-xs-12 col-sm-3">
            %%city%%
          </label>
          <div class="col-sm-6 col-xs-12">
            <input class="form-control" id="city" name="city" ng-model="billingData.city" placeholder="City" type="text"
              ng-required="true" />
            <p class="text-danger u-padding--five bg-danger" ng-if="paymentDetails.city.$error.required">
              %%city%% is required!
            </p>
          </div>
        </div>
        <div class="form-group" ng-class="!paymentDetails.country.$valid?'has-error':''">
          <label class="control-label col-xs-12 col-sm-3">
            %%country%%
          </label>
          <div class="col-sm-6 col-xs-12">
            <select class="form-control" id="country" name="country" ng-model="billingData.country" ng-required="true" ng-change="changeCountry()">
              <option value="">--Select %%country%%--</option>
              <option ng-repeat="value in countries" value="{{value.code}}">{{value.name}}</option>
            </select>
            <p class="text-danger u-padding--five bg-danger" ng-if="paymentDetails.country.$error.required">
              %%country%% is required!
            </p>
          </div>
        </div>
        <div class="form-group" ng-class="!paymentDetails.postal_code.$valid?'has-error':''">
          <label class="control-label col-xs-12 col-sm-3">
            Postal Code
          </label>
          <div class="col-sm-6 col-xs-12">
            <input class="form-control" id="postal_code" name="postal_code" ng-model="billingData.postal_code"
              placeholder="Postal Code" type="text" ng-required="true" />
            <p class="text-danger u-padding--five bg-danger" ng-if="paymentDetails.postal_code.$error.required">
              Postal Code is required!
            </p>
          </div>
        </div>
        <div class="form-group" ng-class="(!paymentDetails.phone_code.$valid || !paymentDetails.phone_number.$valid)?'has-error':''">
          <label class="control-label col-xs-12 col-sm-3">
            Phone Number
          </label>
          <div class="col-sm-1 col-xs-3">
            <input class="form-control" id="phone_code" name="phone_code" ng-model="billingData.phone_code"
              placeholder="Code" type="text" ng-required="true" ng-pattern="/^\d{1,3}$/" />
            <p class="text-danger u-padding--five bg-danger" ng-if="paymentDetails.phone_code.$error.required">
              Phone code is required!
            </p>
            <p class="text-danger u-padding--five bg-danger" ng-if="paymentDetails.phone_code.$error.pattern">
              Invalid Phone code
            </p>
          </div>
          <div class="col-sm-5 col-xs-8">
            <input class="form-control" id="phone_number" name="phone_number" ng-model="billingData.phone_number"
              placeholder="Phone" max="999999999999999" type="text" ng-required="true" ng-pattern="/^\d{10,15}$/" />
            <p class="text-danger u-padding--five bg-danger" ng-if="paymentDetails.phone_number.$error.required">
              Phone number is required!
            </p>
            <p class="text-danger u-padding--five bg-danger" ng-if="paymentDetails.phone_number.$error.pattern">
              Invalid Phone Number
            </p>
          </div>
        </div>
      </form>
      <div class="container" id="paymentID" style="display:none">
        <div class="row">
          <div class="col-md-6">
            <div class="card" style="box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);transition: 0.2s;">
              <div class="card-body" style="background-color: #f8f9fa;padding: 1.25rem;border: 1px solid #ddd;border-radius: 4px;height:565px;">
                <div class="passData.calendarEvent" ng-if="!passData.multiple">
                <h3 class="card-title" style="color: #333;margin-bottom: 1rem;">Product Information</h3>
                <p ng-if="passData.calendarEvent.title || passData.calendarEvent.name">
                  <span ><strong>Name</strong>:
                    {{passData.calendarEvent.title?passData.calendarEvent.title:passData.calendarEvent.name}}</span>
                </p>
                <p ng-if="passData.calendarEvent.description">
                  <span ><strong>Description</strong>:
                    {{passData.calendarEvent.description}}</span>
                </p>
                <p ng-if="passData.calendarEvent.cost">
                  <strong>%%cost%%</strong>: {{config.currencySymbol}}{{amount}}
                </p>
                <p ng-if="passData.calendarEvent.startsAt">
                  <strong>Date</strong>: {{passData.calendarEvent.startsAt | date: config.defaultDateFormat}}
                </p>
                <p ng-if="passData.calendarEvent.startsAt">
                  <strong>Duration</strong>: {{passData.calendarEvent.duration}} Minutes
                </p>
                <p ng-if="passData.calendarEvent.maxclass">
                  <strong>Enrolled</strong>: {{passData.calendarEvent.enrolledUsers}} / {{ passData.calendarEvent.maxclass
                  }}
                </p>
                <span ng-if="passData.calendarEvent.venueDetails">
                  <p>
                    <strong>Venue Details</strong>:
                  </p>
                  <ul>
                    <li ng-if="passData.calendarEvent.venueDetails.name"> <strong>Name</strong>:
                      {{passData.calendarEvent.venueDetails.name}}</li>
                    <li ng-if="passData.calendarEvent.venueDetails.address"> <strong>Address</strong>:
                      {{passData.calendarEvent.venueDetails.address}}, {{passData.calendarEvent.venueDetails.postcode}}
                    </li>
                    <li ng-if="passData.calendarEvent.venueDetails.instructions"> <strong>Instructions</strong>:
                      {{passData.calendarEvent.venueDetails.instructions}}</li>
                    <li ng-if="passData.calendarEvent.venueDetails.image_url" style="list-style: none">
                      <img ng-src="{{passData.calendarEvent.venueDetails.image_url}}" width="320px" height="auto" />
                    </li>
                  </ul>
                </span>
                </div>
                <div class="passData.calendarEvent" ng-if="passData.multiple">
                  <div class="panel panel-default">
                    <div class="panel-heading">
                      <h3 class="panel-title" style="color: #333; margin: 0;">Product Information</h3>
                    </div>
                    <div class="panel-body">
                      <div class="row">
                        <div class="col-md-12" ng-repeat="cart in passData.cart">
                          <div class="product-card" style="margin-bottom: 15px; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                            <!-- Name -->
                            <div ng-if="cart.item.title || cart.item.name" class="detail-row">
                              <strong>Name:</strong> {{cart.item.title || cart.item.name}}
                            </div>
                            
                            <!-- Description -->
                            <div ng-if="cart.item.description" class="detail-row">
                              <strong>Desc:</strong> {{cart.item.description}}
                            </div>
                            
                            <!-- Cost -->
                            <div ng-if="cart.item.cost" class="detail-row">
                              <strong>Cost:</strong> {{config.globalPaymentCurrencySymbol}}{{cart.item.cost}}
                            </div>
                            
                            <!-- Date and Duration -->
                            <div ng-if="cart.item.startsAt" class="detail-row">
                              <strong>Date:</strong> {{cart.item.startsAt | date: config.defaultDateFormat}}
                              <span class="detail-separator">|</span>
                              <strong>Dur:</strong> {{cart.item.duration}} mins
                            </div>
                            
                            <!-- Enrolled -->
                            <div ng-if="cart.item.maxclass" class="detail-row">
                              <strong>Enrolled:</strong> {{cart.item.enrolledUsers}}/{{cart.item.maxclass}}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="card"  style="box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);transition: 0.2s;">
              <div class="card-body" style="background-color: #f8f9fa;padding: 1.25rem;border: 1px solid #ddd;border-radius: 4px;">
                <div class="embed-responsive embed-responsive-16by9" style="height:538px;">
                  <iframe class="embed-responsive-item" style="display: none;" id="testPayment"></iframe>
                  <button id="paynow" style="display: none;" class="btn btn-danger">PayNow</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="modal-footer">
  <button class="btn btn-primary" ng-click="payment()" ng-if="billing_address">
    Procced Payment
  </button>
  <button class="btn btn-danger c-btn-cancel" type="button" ng-click="closeModal()">Close</button>
</div>
<script src="<?=$LMSUri?>js/rxp-js.js"></script>
