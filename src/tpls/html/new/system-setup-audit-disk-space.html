<div ng-controller="DiskSpaceAuditController" ng-init="init()">
	<div class="x_panel">
		<div uib-alert ng-repeat="alert in alerts" type="{{alert.type}}" ng-class="'alert-' + (alert.type || 'warning')" close="closeAlert($index)">{{alert.msg}}</div>

		<div class="x_title">
			<h2>
				Disk Space Usage
				<small ng-if="!isLoadingStats">
					({{stats.total.size_formatted}} Total)
				</small>
			</h2>
			<ul class="nav navbar-right panel_toolbox">
				<li>
					<button type="button" class="btn btn-primary btn-sm" ng-click="refreshStats()" ng-disabled="isLoadingStats">
						<i class="fa fa-refresh" ng-class="{'fa-spin': isLoadingStats}"></i>
						Refresh
					</button>
				</li>
			</ul>
			<div class="clearfix"></div>
		</div>

		<div class="x_content">
			<!-- Summary Statistics -->
			<div class="row" ng-if="!isLoadingStats && stats">
				<div class="col-md-12">
					<div class="info-box">
						<span class="info-box-icon bg-blue"><i class="fa fa-database"></i></span>
						<div class="info-box-content">
							<span class="info-box-text">Total Installation Storage</span>
							<span class="info-box-number">{{stats.total.size_formatted}}</span>
						</div>
					</div>
				</div>
			</div>

			<!-- Directory Breakdown -->
			<div class="row" ng-if="!isLoadingStats && stats">
				<div class="col-md-12">
					<h3>Directory Usage Breakdown</h3>
					<table class="table table-striped">
						<thead>
							<tr>
								<th>Directory</th>
								<th>Size</th>
								<th>File Count</th>
								<th>Percentage of Total</th>
								<th>Category</th>
							</tr>
						</thead>
						<tbody>
							<tr ng-repeat="dir in stats.directories | orderBy:'-size'"
								ng-class="{'danger': (dir.size / stats.total.size) > 0.3, 'warning': (dir.size / stats.total.size) > 0.1 && (dir.size / stats.total.size) <= 0.3}">
								<td>
									<a href="" ng-click="toggleEvidenceDetails(dir.name)" ng-if="isEvidenceDirectory(dir.name)" class="u-clickable-link" title="Click to view evidence details">
										<strong ng-if="(dir.size / stats.total.size) > 0.1">{{dir.name}}</strong>
										<span ng-if="(dir.size / stats.total.size) <= 0.1">{{dir.name}}</span>
										<i class="fa fa-chevron-{{showEvidenceDetails ? 'up' : 'down'}} u-margin--left-ten"></i>
									</a>
									<strong ng-if="!isEvidenceDirectory(dir.name) && (dir.size / stats.total.size) > 0.1">{{dir.name}}</strong>
									<span ng-if="!isEvidenceDirectory(dir.name) && (dir.size / stats.total.size) <= 0.1">{{dir.name}}</span>
								</td>
								<td>
									<strong ng-if="(dir.size / stats.total.size) > 0.1">{{dir.size_formatted}}</strong>
									<span ng-if="(dir.size / stats.total.size) <= 0.1">{{dir.size_formatted}}</span>
								</td>
								<td>{{dir.file_count | number}}</td>
								<td>
									<div class="progress" style="margin-bottom: 0;">
										<div class="progress-bar" role="progressbar"
											ng-class="{'progress-bar-danger': (dir.size / stats.total.size) > 0.3, 'progress-bar-warning': (dir.size / stats.total.size) > 0.1 && (dir.size / stats.total.size) <= 0.3, 'progress-bar-success': (dir.size / stats.total.size) <= 0.1}"
											ng-style="{'width': ((dir.size / stats.total.size) * 100) + '%'}"
											aria-valuenow="{{(dir.size / stats.total.size) * 100}}"
											aria-valuemin="0" aria-valuemax="100">
											{{((dir.size / stats.total.size) * 100 | number:1)}}%
										</div>
									</div>
								</td>
								<td>
									<span class="label"
										ng-class="{'label-danger': (dir.size / stats.total.size) > 0.3, 'label-warning': (dir.size / stats.total.size) > 0.1 && (dir.size / stats.total.size) <= 0.3, 'label-info': (dir.size / stats.total.size) <= 0.1 && (dir.size / stats.total.size) > 0.01, 'label-default': (dir.size / stats.total.size) <= 0.01}">
										<span ng-if="(dir.size / stats.total.size) > 0.3">Major Consumer</span>
										<span ng-if="(dir.size / stats.total.size) > 0.1 && (dir.size / stats.total.size) <= 0.3">Medium Consumer</span>
										<span ng-if="(dir.size / stats.total.size) <= 0.1 && (dir.size / stats.total.size) > 0.01">Small Consumer</span>
										<span ng-if="(dir.size / stats.total.size) <= 0.01">Minimal</span>
									</span>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>

			<!-- Database File References -->
			<div class="row" ng-if="!isLoadingStats && stats && showEvidenceDetails">
				<div class="col-md-12">
					<h3>Database File References</h3>
					<div class="alert alert-info" ng-if="updateFileSizesResult">
						<i class="fa fa-info-circle"></i>
						{{updateFileSizesResult.message}}
					</div>
					<div class="row u-margin--bottom-ten">
						<div class="col-md-12">
							<button type="button" class="btn btn-primary btn-sm"
								ng-click="updateFileSizes()"
								ng-disabled="isUpdatingFileSizes"
								title="Scan evidence files and update missing file sizes in database">
								<i class="fa fa-refresh" ng-class="{'fa-spin': isUpdatingFileSizes}"></i>
								Update Missing File Sizes
							</button>
							<span ng-if="isUpdatingFileSizes" class="u-margin--left-ten">
								<i class="fa fa-spinner fa-spin"></i>
								Processing evidence files...
							</span>
						</div>
					</div>
					<table class="table table-striped">
						<thead>
							<tr>
								<th>File Type</th>
								<th>Count</th>
								<th>Total Size</th>
							</tr>
						</thead>
						<tbody>
							<tr ng-repeat="dbStat in stats.database">
								<td>{{dbStat.name}}</td>
								<td>{{dbStat.count | number}}</td>
								<td>{{dbStat.total_size_formatted}}</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>

			<!-- Orphaned Files Section -->
			<div class="row" ng-show="showEvidenceDetails">
				<div class="col-md-12">
					<h3>Orphaned Evidence Files</h3>
					<div class="info-box" ng-if="orphanedFilesStats">
						<span class="info-box-icon bg-red"><i class="fa fa-exclamation-triangle"></i></span>
						<div class="info-box-content">
							<span class="info-box-text">Files without Database Records</span>
							<span class="info-box-number">
								{{orphanedFilesStats.total_orphaned_count}} files
								({{orphanedFilesStats.total_orphaned_size_formatted}})
							</span>
						</div>
					</div>
					<div class="row">
						<div class="col-md-12">
							<button type="button" class="btn btn-warning btn-sm" ng-click="loadOrphanedFiles()" ng-disabled="isLoadingOrphanedFiles">
								<i class="fa fa-search" ng-class="{'fa-spin': isLoadingOrphanedFiles}"></i>
								Scan for Orphaned Files
							</button>
							<button type="button" class="btn btn-default btn-sm" ng-click="refreshOrphanedFiles()" ng-disabled="isLoadingOrphanedFiles || !orphanedFilesStats">
								<i class="fa fa-refresh" ng-class="{'fa-spin': isLoadingOrphanedFiles}"></i>
								Refresh
							</button>
						</div>
					</div>
					<div ng-if="orphanedFiles.length > 0" class="u-margin--top-ten">
						<table st-table="orphanedFiles" class="table table-striped report">
							<thead>
								<tr>
									<th st-sort="file_name" st-ratio="30">File Name</th>
									<th st-sort="file_size" st-ratio="12">Size</th>
									<th st-sort="file_type" st-ratio="8">Type</th>
									<th st-sort="modified_date" st-ratio="25" st-sort-default="reverse">Modified Date</th>
									<th st-ratio="25">Actions</th>
								</tr>
								<tr>
									<th>
										<input st-search="file_name" placeholder="File name" class="input-sm form-control" type="search"/>
									</th>
									<th></th>
									<th>
										<input st-search="file_type" placeholder="Type" class="input-sm form-control" type="search"/>
									</th>
									<th></th>
									<th></th>
								</tr>
							</thead>
							<tbody ng-class="{'u-disabled': isLoadingOrphanedFiles}">
								<tr ng-repeat="file in orphanedFiles">
									<td>{{file.file_name}}</td>
									<td>{{file.file_size_formatted}}</td>
									<td>{{file.file_type}}</td>
									<td>{{file.modified_date | dateToISO | date: config.defaultDateFormat + ' HH:mm'}}</td>
									<td>
										<button type="button" class="btn btn-info btn-xs"
											ng-click="viewOrphanedFile(file)"
											title="View this orphaned file"
											style="margin-right: 5px;">
											<i class="fa fa-eye"></i>
											View
										</button>
										<button type="button" class="btn btn-danger btn-xs"
											ng-click="confirmDeleteOrphanedFile(file)"
											ng-disabled="file.deleting"
											title="Delete this orphaned file permanently">
											<i class="fa fa-trash" ng-class="{'fa-spin': file.deleting}"></i>
											Delete
										</button>
									</td>
								</tr>
							</tbody>
							<tfoot>
								<tr>
									<td colspan="5" class="text-center">
										<div st-items-by-page="25" st-pagination="" st-template="<?=$LMSTplsUriHTML?>pagination.html"></div>
									</td>
								</tr>
							</tfoot>
						</table>
					</div>
					<div ng-if="orphanedFilesStats && orphanedFilesStats.total_orphaned_count === 0" class="alert alert-success">
						<i class="fa fa-check-circle"></i>
						No orphaned files found. All files have corresponding database records.
					</div>
				</div>
			</div>

			<!-- Detailed File List -->
			<div class="row" ng-show="showEvidenceDetails">
				<div class="col-md-12">
					<h3>Learning Module Evidence Files</h3>
					<div class="row u-margin--bottom-ten">
						<div class="col-md-12">
							<button type="button" class="btn btn-default btn-sm"
								ng-click="refreshLearningModuleFiles()"
								ng-disabled="isLoading"
								title="Refresh the Learning Module Evidence Files list">
								<i class="fa fa-refresh" ng-class="{'fa-spin': isLoading}"></i>
								Refresh
							</button>
						</div>
					</div>
					<table st-pipe="callServer" st-table="data" class="table table-striped report">
						<thead>
							<tr>
								<th st-sort="id" st-ratio="5">ID</th>
								<th st-sort="learning_module_evidences__evidence" st-ratio="18">File Name</th>
								<th st-sort="file_size" st-ratio="8">Size</th>
								<th st-sort="file_type" st-ratio="8">Type</th>
								<th st-sort="module_name" st-ratio="18">Learning Module</th>
								<th st-sort="uploaded_by" st-ratio="15">Uploaded By</th>
								<th st-sort="created_at" st-ratio="12" st-sort-default="reverse">Upload Date</th>
								<th st-ratio="16">Actions</th>
							</tr>
							<tr style="display: none;">
								<td>
									<input search-watch-model="refreshTable" st-search="refresh" type="hidden"/>
								</td>
							</tr>
							<tr>
								<th>
									<input st-search="learning_module_evidences__id" placeholder="ID" class="input-sm form-control" type="number"/>
								</th>
								<th>
									<input st-search="learning_module_evidences__evidence" placeholder="File name" class="input-sm form-control" type="search"/>
								</th>
								<th></th>
								<th>
									<input st-search="learning_module_evidences__evidence_type" placeholder="Type" class="input-sm form-control" type="search"/>
								</th>
								<th>
									<input st-search="learning_modules__name" placeholder="Module name" class="input-sm form-control" type="search"/>
								</th>
								<th>
									<input st-search="users__fnameAAAusers__lname" placeholder="Uploaded by" class="input-sm form-control" type="search"/>
								</th>
								<th></th>
								<th></th>
							</tr>
						</thead>
						<tbody ng-class="{'u-disabled': isLoading && evidenceDataLoaded}">
							<tr ng-if="!evidenceDataLoaded">
								<td colspan="8" class="text-center text-muted">
									<i class="fa fa-info-circle"></i>
									Click on "Evidence" in the Directory Usage Breakdown above to load Learning Module Evidence Files
								</td>
							</tr>
							<tr ng-repeat="file in data" ng-class="{'text-muted': file.status == 0}">
								<td>{{file.id}}</td>
								<td>{{file.file_name}}</td>
								<td>{{formatFileSize(file.file_size)}}</td>
								<td>{{file.file_type}}</td>
								<td>{{file.module_name}}</td>
								<td>
									<a href="" ng-click="lp.viewLearner(file.user_id)">
										<i class="glyphicon glyphicon-eye-open"></i>
									</a>
									{{file.uploaded_by}}
								</td>
								<td>
									{{file.created_at | dateToISO | date: config.defaultDateFormat + ' HH:mm'}}
								</td>
								<td>
									<button type="button" class="btn btn-info btn-xs"
										ng-click="viewLearningModuleFile(file)"
										title="View this evidence file"
										style="margin-right: 5px;">
										<i class="fa fa-eye"></i>
										View
									</button>
									<button type="button" class="btn btn-danger btn-xs"
										ng-click="confirmDeleteLearningModuleFile(file)"
										ng-disabled="file.deleting"
										title="Delete this evidence file permanently">
										<i class="fa fa-trash" ng-class="{'fa-spin': file.deleting}"></i>
										Delete
									</button>
								</td>
							</tr>
						</tbody>
						<tfoot>
							<tr>
								<td colspan="8" class="text-center">
									<div st-items-by-page="25" st-pagination="" st-template="<?=$LMSTplsUriHTML?>pagination.html"></div>
								</td>
							</tr>
						</tfoot>
					</table>
				</div>
			</div>

			<!-- Loading overlay -->
			<div class="u-loading--overlay" ng-if="isLoadingStats">
				<div class="u-loading--overlay-content">
					<i class="fa fa-spinner fa-spin fa-3x"></i>
					<p>Loading disk space statistics...</p>
				</div>
			</div>
		</div>
	</div>

</div>