<div ng-controller="EmailQueueController">
	<div class="x_panel">
		<div uib-alert ng-repeat="alert in alerts" type="{{alert.type}}" ng-class="'alert-' + (alert.type || 'warning')" close="closeAlert($index)">{{alert.msg}}</div>

		<div class="x_title">
			<h2>
				Email Queue
				<small>
					({{isLoading ? '...' : total}})
				</small>

			</h2>
      <button class="btn btn-small btn-default pull-right" confirm="Are you sure you want to delete all  items below? This action cannot be undone." ng-disabled="disableBtn" ng-click="bulkDelete()">
        <i class="glyphicon glyphicon-trash" ng-if="!disableBtn"></i>
        <span class="glyphicon glyphicon-refresh" ng-if="disableBtn"></span>
        Delete Queue
      </button>

      <button class="btn btn-small btn-default pull-right" ng-if="config.approveEmailQueueItems" confirm="Do you want approve all items below ?" ng-click="bulkAprrove()" ng-disabled ="disableBtn">
        <i class="glyphicon glyphicon-envelope" ng-if="!disableBtn" ></i>
        <span class="glyphicon glyphicon-refresh" ng-if="disableBtn"></span>
        Send Queue
      </button>
			<div class="clearfix"></div>
		</div>

		<div class="x_content">

			<table st-pipe="callServer" st-table="email_log" class="table table-striped report">
				<thead>
					<tr>
						<th st-sort="id" st-ratio="5" st-sort-default="reverse">ID</th>
						<th st-ratio="25">To</th>
						<th st-sort="subject" st-ratio="15">Subject</th>
						<th st-ratio="15">From</th>
						<th st-sort="approved" st-ratio="10" ng-if="config.approveEmailQueueItems">Approval</th>
						<th st-sort="processed" st-ratio="10">Status</th>
						<th st-sort="created_at" st-ratio="10">Created at</th>
						<th st-ratio="10" ng-if="config.approveEmailQueueItems">&nbsp;</th>
					</tr>
					<tr>
						<th>
							<input st-search="id" placeholder="id" class="input-sm form-control" type="number"/>
						</th>
						<th>
						</th>
						<th>
							<input st-search="subject" placeholder="Subject" class="input-sm form-control" type="search"/>
						</th>
						<th>
							<input st-search="from" placeholder="From" class="input-sm form-control" type="search"/>
						</th>

						<th ng-if="config.approveEmailQueueItems">
							<select st-search="approved" class="form-control">
								<option value=""></option>
								<option value="0">Not Approved</option>
								<option value="1">Approved</option>
							</select>
						</th>

						<th>
							<select st-search="processed" class="form-control">
								<option value=""></option>
								<option value="0">Not Sent</option>
								<option value="1">Sent</option>
							</select>
						</th>

						<th>

						</th>

						<th>
							<input search-watch-model="refreshTable" st-search="refresh" type="hidden"/>
						</th>
					</tr>
				</thead>
				<tbody ng-show="!isLoading">
					<tr st-select-row="row" ng-repeat="entry in data">
						<td>
							{{entry.id}}
						</td>
						<td>
							<span class="label label-default u-pointer" ng-repeat-start="recipient in entry.recipients_list | limitTo: 50" ng-click="lp.viewLearner(recipient.id)">
								{{recipient.fname}} {{recipient.lname}}<small>({{recipient.id}})</small>
							</span> <span ng-repeat-end>&nbsp;</span>
							<div class="bg-danger u-padding--five u-margin--five" ng-if="entry.recipients_list.length > 50">Showing only first 50 recipients, there are <strong>{{entry.recipients_list.length}}</strong> recipients total</div>


							<span class="label label-default u-pointer" ng-repeat-start="recipient_email in entry.recipient_emails_list | limitTo: 50">
								{{recipient_email}}
							</span> <span ng-repeat-end>&nbsp;</span>
						</td>
						<td>
							<b>{{entry.template.subject}}</b><br/>
							<small>{{entry.template.name}}</small>
						</td>
						<td>
							<span ng-if="entry.from_user">
								{{entry.from_user.email}}<br>
								<small class="label label-default u-pointer" ng-click="lp.viewLearner(entry.from_user.id)">{{entry.from_user.fname}} {{entry.from_user.lname}}</small>
							</span>
							<span ng-if="!entry.from_user">
								System
							</span>
						</td>
						<td ng-if="config.approveEmailQueueItems">
							{{entry.approved ? 'Approved' : 'Not approved'}}
						</td>
						<td>
							{{entry.processed ? 'Sent': 'Not Sent'}}
						</td>
						<td>
							{{entry.created_at | dateToISO | date: config.defaultDateFormat + ' HH:mm'}}
						</td>
						<td nowrap ng-if="config.approveEmailQueueItems">
							<div class="btn-group" uib-dropdown is-open="entry.isopen">
								<button id="single-button" type="button" class="btn btn-default" uib-dropdown-toggle ng-disabled="disabled">
									Actions
									<span class="caret"></span>
								</button>
								<ul class="dropdown-menu dropdown-menu-right" uib-dropdown-menu role="menu" aria-labelledby="single-button">
									<li role="menuitem" ng-if="config.approveEmailQueueItems && rs.check('is_admin') && !entry.approved">
										<a href="" ng-click="approve(entry.id)">
											<i class="glyphicon glyphicon-ok"></i>
											Approve
										</a>
									</li>
									<li role="menuitem" ng-if="config.approveEmailQueueItems && rs.check('is_admin') && entry.approved">
										<a href="" ng-click="disapprove(entry.id)">
											<i class="glyphicon glyphicon-remove"></i>
											Dissapprove
										</a>
									</li>
								</ul>
							</div>
						</td>
					</tr>
				</tbody>
				<tbody ng-show="isLoading">
					<tr>
						<td colspan="9" class="text-center" loading-bar>Loading ... </td>
					</tr>
				</tbody>
				<tfoot>
					<tr>
						<td colspan="9" class="text-center">
							<div st-items-by-page="10" st-pagination="" st-template="<?=$LMSTplsUriHTML?>pagination.html?v=<?=$version?>"></div>
						</td>
					</tr>
				</tfoot>
			</table>
		</div>
	</div>
</div>
