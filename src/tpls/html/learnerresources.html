<div class="learner-resource__finder panel-collapse collapse" uib-collapse="!search.term">
	<div class="x_content">
		<form name="resourceResultsForm" class="form-horizontal form-label-left" novalidate>
			<div class="form-group" ng-if="search.term">
				<div class="col-xs-12">
					<div class="learner-resource__resources learner-resource__resources--search-results">
						<div learner-resource class="learner-resource__resource learner-resource__resource--search-result" resource-title="Search Results" resource-key="searchResults" resource-list="moduleSearch.collection()" resource-update="{{search.term}}"></div>
					</div>
				</div>
			</div>
		</form>
	</div>
	<div class="learner-resource__finder--close" ng-click="search.term = ''">
		CLOSE<i class="fa fa-angle-down" aria-hidden="true"></i>
	</div>
</div>

<div class="learner-resource u-flexbox--wrap u-flex--grow" ng-class="{'u-flexbox u-flexbox--direction-column' : !config.setSmallPromoImage}">
	<div class="learner-resource__content u-flex--grow u-flexbox u-flexbox--wrap" ng-class="{'u-flexbox--direction-column' : !config.setSmallPromoImage}" ng-hide="hideResource">
		<div class="learner-resource__wrap u-flexbox u-flex--grow">

			<div class="learner-resource__screen-wrap learner-resource__overview u-flexbox u-flex--grow u-flex--no-shrink" ng-class="activeTab.class" ng-if="activeResource">

				<!-- Overview 1 -->
				<div class="learner-resource__screen u-flexbox u-flex--grow u-flex--no-shrink">
					<div class="learner-resource__sidebar u-flexbox u-flexbox--wrap u-flexbox--direction-column u-flex--no-shrink"
						 ng-class="{'learner-resource__sidebar__shadow' : (config.promoGradient && !currentUser.accessible_ui), 'small-promo' : config.setSmallPromoImage}">
						<div class="u-flex--grow">

							<div class="heading1" ng-if="getResourceProperty('name')">
								{{getResourceProperty('name')}}
								<span class="fa learner-resource__favorite" ng-class="{'fa-star' : activeResource.favorite, 'fa-star-o' : !activeResource.favorite}" ng-click="resources.favorite(activeResource)" uib-tooltip="{{activeResource.favorite ? '%%learner__favorite_remove%%' : '%%learner__favorite_add%%'}}" tooltip-class="white-right" tooltip-placement="right" ng-if="!(activeResource.assigned === 0)"></span>
								<span class="fa fa-share-alt" ng-click="resources.copySsoUrl(true)" ng-if="config.showShareResourceSsoUrl"></span>
								<span class="fa fa-share-alt" style="font-size: 0.8em; cursor: pointer;" ng-click="resources.copySsoUrl(false)" ng-if="config.showShareResourceUrl"></span>
							</div>

							<!-- PIC FOR MOBILE -->
							<img class="hidden-md hidden-lg pull-left" alt="" ng-src="{{getResourceProperty('safe_promo')}}" ng-if="getResourceProperty('safe_promo')" style="max-width: 50%; margin-bottom: 20px;">
							<!-- -------------- -->

							<p ng-if="activeResourceDetails.learning_result.module.evidence_type && activeResourceDetails.learning_result.module.type.slug == 'upload'">
								<span class="learner-resource__category">
									Type: <b>{{activeResourceDetails.learning_result.module.evidence_type.name}}</b>
								</span>
							</p>
							<p ng-repeat="(key, value) in categories" ng-if="activeResource.module.category_id == value.id">
								<span class="learner-resource__category" ng-class="safeCategoryClass(value.name)">
									%%category%%: <b>{{value.name}}</b>
								</span>
							</p>
							<p ng-if="getResourceProperty('discounted_cost') && config.PaymentsEngine">
								<span class="learner-resource__category">
									%%cost_currency%% :<b>{{ getResourceProperty('discounted_cost')}}</b>
								</span>
							</p>
							<p ng-if="canRetake()">
								<span class="learner-resource__category">
									Retake Fee &pound :<b>{{ getResourceProperty('retake_fee')}}</b>
								</span>
							</p>
							<p ng-if="activeResourceDetails.learning_result.credits">
								<span class="learner-resource__category">
									Credits: <b>{{activeResourceDetails.learning_result.credits}}</b>
								</span>
							</p>
							<p class="learner-resource__status-parent" ng-if="resources.showCompletionState() && !config.hideLearnerStatus">
								<span class="learner-resource__status" ng-class="safeStatusClass(activeResource.completion_status)">
									Status: <b>{{resources.progress()}}</b>
								</span>
								<i class="fa fa-check" ng-class="statussCheck(activeResourceDetails.learning_result)" ng-if="activeResource.module.track_progress"></i>
							</p>

							<p class="learner-resource__status-parent" ng-if="activeResource.completion_status && getDueDate() && !config.hideLearnerDueDate && activeResource.module.track_progress">
								<span class="learner-resource__status" ng-class="safeStatusClass(activeResource.completion_status)">
									{{getResourceProperty('all_day_event') ? '%%learner__event__start_date%%' : 'Due'}}: {{getDueDate()}}
								</span>
							</p>

							<p class="learner-resource__status-parent" ng-if="getResourceProperty('all_day_event')">
								<span class="learner-resource__status">
									%%learner__event__end_date%%: {{getEndDate()}}
								</span>
							</p>

							<div ng-if="activeResourceDetails.venue_details.name">
								<p class="learner-resource__status-parent" >
									<span class="learner-resource__status">
										Venue Details :
									</span>
								</p>
								<ul>
									<li ng-if="activeResourceDetails.venue_details.name">Name :  {{activeResourceDetails.venue_details.name}}</li>
									<li ng-if="activeResourceDetails.venue_details.address">Address :  {{activeResourceDetails.venue_details.address}}, {{activeResourceDetails.venue_details.postcode}}</li>
									<li ng-if="activeResourceDetails.venue_details.instructions">Instructions :  {{activeResourceDetails.venue_details.instructions}}</li>
								</ul>
							</div>

							<p class="learner-resource__status-parent" ng-if="activeResource.qa && isApprentix && config.isLearnerQAFilter">
								<span class="learner-resource__status" ng-class="safeStatusClass(activeResource.completion_status)">
									QA Status: <b>{{activeResource.qa}}</b>
								</span>
								<i class="fa" ng-class="statussCheck(activeResource, true)"></i>
							</p>

							<p ng-if="activeResource.module.type.name" ng-class="safeTypeClass(activeResource.module.type.name)">
								<span class="learner-resource__type">
									Type: <b>{{activeResource.module.type.name}}
												{{activeResource.module.event_type ? ' - ' + activeResource.module.event_type.name : ''}}</b>
								</span>
							</p>
							<p ng-if="activeResource.module.type.name == 'book_cd_dvd'">
								<b>Author</b>: {{activeResource.module.material.author}}<br>
								<b>Title</b>: {{activeResource.module.material.title}}
							</p>
							<div ng-if="activeResourceDetails.learning_result.module.schedule_lesson_link.schedule.description || getResourceProperty('description')">
								<div class="learner-resource__description u-multi-line-ellipsis six_lines u-white--pre-line description"
									  id="learning-resource-mini-description" ng-bind-html="activeResourceDetails.learning_result.module.schedule_lesson_link.schedule.description || getResourceProperty('description')">
								</div>
								<div class="u-padding--top-ten u-padding--bottom-ten learner-resource__description__read-more" ng-if="showReadMoreDesciption">
									<a href="" ng-click="setActiveTab(resourceTabs[1])">
										read more...
									</a>
								</div>
							</div>
						</div>

						<div
							class="learner-resource__active-action"
							ng-if="events.showCancel(activeResourceDetails.learning_result.module.schedule_lesson_links[activeResource.schedule_index])"
						>
							<a href="" class="btn btn-danger btn-lg btn-block" ng-click="events.cancel(activeResourceDetails.learning_result.module.schedule_lesson_links[activeResource.schedule_index])">
								Cancellation
							</a>
						</div>

						<div class="learner-resource__active-action" ng-if="isJackdawCloud && currentUser.role.jackdaw_type && ((activeResource.module.created_by == currentUser.id && activeResource.module.jackdaw_resource) || (activeResource.module.created_by_group && config.jackdaw.group_id && activeResource.module.created_by_group == config.jackdaw.group_id))" ng-controller="JackdawCloud">
							<a href="#" id="edit-resource" class="btn btn-launch btn-lg btn-block" ng-click="ls.jackdawHtml5(activeResource.module)">
								Edit
							</a>
						</div>

						<div class="learner-resource__active-action" ng-if="canRetake()">
							<a href="#" class="btn btn-launch btn-lg btn-block" ng-click="retake()">
								Retake
							</a>
						</div>

						<div class="learner-resource__active-action"
							ng-if="
							(
								(
									!hideLaunchButton &&
									activeResource &&
									(
										(
											activeResourceDetails.learning_result.approved != 0 &&
											activeResourceDetails.learning_result.module.schedule_lesson_links.length == 0 &&
											(
												activeResourceDetails.learning_result.is_paid != 0 ||
												activeResourceDetails.learning_result.module.cost <= 0
											)
										) ||
										activeResource.completion_status == 'Not Enrolled'
									)
								) ||
								(
									!hideLaunchButton &&
									activeResource &&
									activeResourceDetails.learning_result.module.schedule_lesson_links.length != 0 &&
									activeResourceDetails.learning_result.module.schedule_lesson_links[activeResource.schedule_index].schedule.user_link.approved != 0 &&
									activeResourceDetails.learning_result.module.schedule_lesson_links[activeResource.schedule_index].schedule.user_link.is_paid != 0
								)
							)
							&& !(
								!config.allowLearnerRefreshLearning
								&& activeResourceDetails.learning_result.survey != null
							)"
						>
							<a href="" class="btn btn-launch btn-lg btn-block" ng-if="activeResourceDetails.is_recommended" ng-click="dismissRecommendation()">
								Dismiss Recommendation
							</a>
							<a
								class="btn btn-launch btn-lg btn-block"
								ng-if="config.isMultiSelectShoppingBasket && !activeResourceDetails.learning_result.id"
								ng-click="addToShoppingBasket($event)"
								role="button"
								tabindex="0"
								aria-label="Add to Basket"
							>
								Add to Basket
							</a>

							<a href="" ng-if="
								(
									config.hideLaunchSkills != true &&
									activeResourceDetails.learning_result.module.is_skill !=1
								) ||
								(
									activeResourceDetails.learning_result.module.is_skill != 1 &&
									loadingResource == false
								)
								&& (
									(
										activeResourceDetails.learning_result.module.type.slug == 'e_learning' &&
										(
											activeResourceDetails.learning_result.passing_status != 'failed' ||
											activeResourceDetails.learning_result.attempts_left > 0 ||
											config.allowLearnerRefreshLearning
										)
									) ||
									activeResourceDetails.learning_result.module.type.slug != 'e_learning'
								)
							"
								class="btn btn-launch btn-lg btn-block"
								ng-click="launchResource($event)"
								ng-href="{{launchResourceLink}}"
								target="{{launchResourceTarget}}"
								title="{{(activeResourceDetails.learning_result.passing_status == 'failed' && activeResourceDetails.learning_result.attempts_left > 0) ? 'Re-Attempt '+getResourceProperty('name')+' Learning' : launchResourceText+' named: '+getResourceProperty('name')}}"
							>
								{{(activeResourceDetails.learning_result.passing_status == 'failed' && activeResourceDetails.learning_result.attempts_left > 0) ? 'Re-Attempt Learning' : launchResourceText}}
							</a>
						</div>
						<div class="learner-resource__active-action"
							ng-if="
							checkInWaitingList() == false &&
							(
								(
									activeResourceDetails.learning_result.module.schedule_lesson_links.length != 0 &&
									activeResourceDetails.learning_result.module.schedule_lesson_links[activeResource.schedule_index].schedule.user_link.approved == 0
								) ||
								(
									activeResourceDetails.learning_result.module.schedule_lesson_links.length == 0 &&
									activeResourceDetails.learning_result.approved == 0 &&
									activeResourceDetails.learning_result.is_paid != 0
								) ||
								(activeResource.waiting[0].id === currentUser.id && activeResource.waiting[0].approved!=1) ||
								(activeResourceDetails.learning_result.module.schedule_lesson_links[activeResource.schedule_index].schedule.waiting[0].id === currentUser.id &&
								activeResourceDetails.learning_result.module.schedule_lesson_links[activeResource.schedule_index].schedule.waiting[0].approved!=1
							))"
						>
							<a href="" disabled class="btn btn-launch btn-lg btn-block" ng-click="" ng-href="">
								%%launch_resource_text__waiting_approval%%
							</a>
						</div>

						<div class="learner-resource__active-action"
							ng-if="(
								(
									config.isCivicaPaymentsEngine ||
									config.isGlobalPaymentsEngine ||
									config.enableStripePayment ||
									config.enablePay360
								) &&
								(
									activeResourceDetails.learning_result.module.schedule_lesson_links[activeResource.schedule_index].schedule.user_link.approved != 0 &&
									activeResourceDetails.learning_result.module.schedule_lesson_links[activeResource.schedule_index].schedule.user_link.is_paid == '0' &&
									activeResourceDetails.learning_result.module.schedule_lesson_links[activeResource.schedule_index].schedule.cost != null &&
									activeResourceDetails.learning_result.module.schedule_lesson_links[activeResource.schedule_index].schedule.cost != 0
								) || (
									activeResourceDetails.learning_result.approved != 0 &&
									activeResourceDetails.learning_result.is_paid == 0 &&
									activeResourceDetails.learning_result.module.cost > 0
								)
							)">
							<a href="" class="btn btn-launch btn-lg btn-block"
								ng-if="activeResourceDetails.learning_result.module.schedule_lesson_links.length != 0"
								ng-click="makePayment(activeResourceDetails.learning_result.module.schedule_lesson_links[activeResource.schedule_index].schedule)">
								%%make_payment%%
							</a>
							<a href="" class="btn btn-launch btn-lg btn-block"
								ng-if="activeResourceDetails.learning_result.module.schedule_lesson_links.length == 0"
								ng-click="makePayment(activeResourceDetails.learning_result.module)">
								%%make_payment%%
							</a>
						</div>

						<div class="learner-resource__active-action" ng-if="(config.isCivicaPaymentsEngine || config.enablePay360) && checkInWaitingList()">
							<a href="" class="btn btn-launch btn-lg btn-block"
								ng-click="makePayment(activeResourceDetails.learning_result.module.schedule_lesson_links[activeResource.schedule_index].schedule)" ng-href="">
								%%make_payment%%
							</a>
						</div>

						<!-- coupon apply block -->
						<div ng-if="couponExists">
							<div>
								<label>
									Have Coupon ? <input type="checkbox" class="form-group-lg" ng-model="couponOpt.enabled" id="use_coupon">
								</label>
							</div>
							<div ng-if="couponOpt.enabled">
								<label>Coupon code</label>
								<div class="u-flexbox">
									 <input type="text" ng-model="couponOpt.code" id="coupon_code"
													   placeholder="Enter Code here..." class="form-control">
									<button ng-click="applyCoupon()" class="u-pointer btn btn-default">Apply</button>
								</div>
								<span ng-if="dataLoading">
									<span class="glyphicon glyphicon-refresh u-spinner"></span>
								</span>

								<div ng-if="discounts.coupon_discount" class="u-margin--top-five"><b>Coupon Applied: </b> -{{discounts.coupon_discount}}% = €{{discounts.cost_after_coupon_apply}}

									<div ng-if="discounts.user_discount || discounts.company_discount || discounts.department_discount"><b>User + %%company%% + %%department%% discount percentage any:</b>
										{{discounts.user_discount + "% + " + discounts.company_discount + "% + " + discounts.department_discount
										}}% =
										{{discounts.user_discount + discounts.company_discount + discounts.department_discount }}%
									</div>
									<div ng-if="discounts.coupon_discount"><b>Total payable amount after discounts: </b> €{{discounts.total}}
									</div>
								</div>
							</div>

						</div>

						<div class="learner-resource__expired u-position--relative" ng-if="disabledText || activeResourceDetails.learning_result.module.expired == 1">
							<span class="glyphicon glyphicon-exclamation-sign u-position--absolute u-position--top-right-minus-ten u-text--red u-font-size--30"></span>
							{{disabledText}}
						</div>

						<div class="learner-resource__active-action"
							 ng-if="
							 checkPrint() &&
							 (
								(
									activeResourceDetails.learning_result.module.print_certificate && activeResourceDetails.learning_result.module.is_course==0
								) ||
								(
									activeResourceDetails.learning_result.module.print_lesson && activeResourceDetails.learning_result.module.is_course==1
								)
							 )">
							<a href="#" id="print-sertificate" target="_blank" class="btn btn-primary btn-lg btn-block" ng-href="<?=$LMSUri?>mylearning/certificate/{{activeResourceDetails.learning_result.id}}{{activeResourceDetails.learning_result.module.schedule_lesson_links[activeResource.schedule_index].schedule ? '/' + activeResourceDetails.learning_result.module.schedule_lesson_links[activeResource.schedule_index].schedule.id : ''}}">
								Print Certificate
							</a>
						</div>
					</div>
					<div class="hidden-xs hidden-sm learner-resource__promo u-flex--grow" ng-style="config.setSmallPromoImage ? {} : getResourceProperty('safe_promo') ? {'background-image': 'url(' + getResourceProperty('safe_promo') +  ')'} : {}">
						<img ng-if="getResourceProperty('safe_promo')" role="presentation" alt="" ng-src="{{getResourceProperty('safe_promo')}}" style="width: 100%; height: 100%; object-fit: cover; max-height: 40vh;" ng-hide="!config.setSmallPromoImage">
						<a href="" class="learner-resource__promo-close" ng-click="scrollToResources()" title="Skip to Resources List" aria-label="Skip to Resources List" ng-if="hideOtherResources === false">
							&times;
						</a>
					</div>
				</div>

				<!-- Details 2 -->
				<div class="learner-resource__screen learner-resource__details u-flexbox u-flex--grow u-flex--no-shrink" ng-include="'<?=$LMSTplsUriHTML?>learnerresourcesdetails.html?v=<?=$version?>'">
				</div>


				<!-- Booking 3 -->
				<div class="learner-resource__screen learner-resource__book-learning-module u-flexbox u-flex--grow u-flex--no-shrink" ng-show="showBookLearningModulePage">
					<div ng-include="'<?=$LMSTplsUriHTML?>booklearningmodule.html?v=<?=$version?>'" class="u-flex--grow"></div>
				</div>

				<!-- Meetings 4 -->
				<div class="learner-resource__screen learner-resource__meetings u-flexbox u-flex--grow u-flex--no-shrink" ng-show="showEvidenceMeetings" ng-include="'<?=$LMSTplsUriHTML?>evidencemeetings.html?v=<?=$version?>'">
				</div>

				<!-- %%sign_off%% 5 -->
				<div class="learner-resource__screen learner-resource__sign-off u-flexbox u-flex--grow u-flex--no-shrink" ng-show="showSignOffPage" ng-include="'<?=$LMSTplsUriHTML?>learnerresourcessignoff.html?v=<?=$version?>'">
				</div>

				<!-- Pre requisite resources -->
				<div class="learner-resource__screen learner-resource__pre-requisite-resources u-flexbox u-flex--grow u-flex--no-shrink" ng-if="showPrereQuisitePage">
					<div learner-resource class="u-flex--grow" resource-key="preRequisite" resource-list="activeResourceDetailsPrerequisites" resource-update="{{prerequisite_counter}}"></div>
				</div>

				<!-- Required modules -->
				<div class="learner-resource__screen learner-resource__required-modules u-flexbox u-flex--grow u-flex--no-shrink" ng-show="showRequiredModulesPage">
					<div learner-resource class="u-flex--grow" resource-key="preRequisite" resource-list="activeResourceDetails.learning_result.module.required_modules" resource-update="{{activeResourceDetails.learning_result.module.required_modules}}"></div>
				</div>

				<!-- Lesson resources -->
				<div class="learner-resource__screen learner-resource__lesson-info u-flexbox u-flex--grow u-flex--no-shrink" ng-show="showLessonInfoPage">
					<div learner-resource class="u-flex--grow" resource-key="preRequisite" resource-list="activeResourceDetails.learning_result.module.modules" resource-update="{{activeResourceDetails.learning_result.module.modules.length}}"></div>
				</div>


			</div>
		</div>
		<div class="learner-resource__tabs" ng-if="hideOtherResources === false">
			<div class="u-flexbox u-flexbox--justify-space">
				<div>
					<ul class="u-flexbox">
						<li ng-repeat="resourcetab in resourceTabs" ng-class="{active : activeTab === resourcetab}" ng-if="(resourcetab.name !== 'Details' || !config.hideLearnerDetails) && (resourcetab.name !== 'Sign Off' || !config.hideLearnerSignOff)">
							<a href="" ng-href="learner/resources/{{resource_param}}/{{resourcetab.url}}">
								{{resourcetab.name}}
							</a>
						</li>
					</ul>
				</div>
			</div>
		</div>
	</div>

	<!-- ------------------ hide this code when loading from mobile application, as the app doesn't have to see other resources but the selected resource only ------------------ -->

	<div class="learner-resource__footer" ng-if="hideOtherResources === false">
		<div class="learner-resource__browse" title="Browse all" ng-click="scrollToResources()" ng-hide="hideResource">
			browse all resources
		</div>
		<div style="height: 30px;" ng-hide="!hideResource"></div>

		<div class="u-flexbox u-flexbox--justify-space u-flexbox--align-center u-margin--bottom-twenty" style="gap: 2rem; padding: 0 2rem;">

			<div class="u-flexbox u-flexbox--wrap u-flexbox--align-center u-flexbox--justify-start learner-resource__includes learner-portfolio-top-menus">

				<div class="filter-label">Filter Options:</div>

				<button class="filter hidden-xs" ng-class="!resources.hideSideBar ? 'icon-programmes-fill' : 'icon-programmes'" aria-pressed="{{!resources.hideSideBar}}" ng-click="resources.sideBarToggle()" uib-tooltip="%%programme%% filter" aria-label="%%programme%% filter" ng-if="config.isProgrammeFilterVisible"></button>

				<div class="btn-group" uib-dropdown is-open="status.isopen" ng-if="config.isCategroyFilter">
					<button type="button"
							class="filter-dropdown-toggle-btn u-flexbox u-flexbox--align-center u-margin--right-twenty u-margin--left-ten"
							uib-dropdown-toggle
							ng-disabled="disabled"
							uib-tooltip="Filter by Categories"
							aria-label="Filter by Categories: {{ lif.categoryFilter.selected.name || 'All' }}">
						<span class="filter"
							style="margin-left: -0.5rem;"
							ng-class="lif.categoryFilter.selected.name ? 'icon-categories-fill' : 'icon-categories'"
							aria-hidden="true"></span>
						<span class="filter-dropdown-text">
							{{lif.categoryFilter.selected.name || 'All'}}
						</span>
						<span class="caret" aria-hidden="true"></span> <!-- Caret is decorative -->
					</button>
					<ul class="dropdown-menu u-larger-font-up-to-md" uib-dropdown-menu role="menu">
						<li role="presentation">
							<a href="" role="menuitem" ng-click="resources.categoryFilter.select(false)">
								All
							</a>
						</li>
						<li ng-repeat="category in categories_all | orderBy: 'name'" ng-if="category.modules_count > 0" role="presentation">
							<a href="#" role="menuitem" ng-click="resources.categoryFilter.select(category)" ng-if="category.name">
								<span ng-class="{'u-bold': category.id == lif.categoryFilter.selected.id}">
									{{category.name}}
								</span>
							</a>
						</li>
					</ul>
				</div>

				<button class="filter" ng-class="resources.showAll ? 'icon-showall-fill' : 'icon-showall'" ng-click="resources.showAllToggle()" uib-tooltip="Include learning outside %%programme%%" aria-label="Filter: Include learning outside %%programme%%" aria-pressed="{{resources.showAll}}" ng-if="config.isLearningOutsideProgrammeFilterVisible"></button>
				<button class="filter" ng-class="resources.show['completed'] ? 'icon-completed-fill' : 'icon-completed'" ng-click="resources.toggle('completed')" uib-tooltip="%%resource_completion_state__completed%%" aria-label="Filter: %%resource_completion_state__completed%%" aria-pressed="{{resources.show['completed']}}" ng-if="config.isCompletedLearningFilterVisible"></button>
				<button class="filter" ng-class="resources.show['inprogress'] ? 'icon-inprogress-fill' : 'icon-inprogress'" aria-pressed="{{resources.show['inprogress']}}" ng-click="resources.toggle('inprogress')" uib-tooltip="%%resource_completion_state__in_progress%%" aria-label="Filter: %%resource_completion_state__in_progress%%" ng-if="config.isInProgressLearningFilterVisible"></button>
				<button class="filter" ng-class="resources.show['notstarted'] ? 'icon-notstarted-fill' : 'icon-notstarted'" aria-pressed="{{resources.show['notstarted']}}" ng-click="resources.toggle('notstarted')" uib-tooltip="%%resource_completion_state__not_started%%" aria-label="Filter: %%resource_completion_state__not_started%%" ng-if="config.isNotStartedLearningFilterVisible"></button>
				<button class="filter" ng-class="resources.show['failed'] ? 'icon-failed-fill' : 'icon-failed'" aria-pressed="{{resources.show['failed']}}" ng-click="resources.toggle('failed')" uib-tooltip="%%resource_completion_state__failed%%" aria-label="Filter: %%resource_completion_state__failed%%" ng-if="config.isNotStartedLearningFilterVisible"></button>
				<button class="filter" ng-class="resources.show['locked'] ? 'icon-locked-fill' : 'icon-locked'" aria-pressed="{{resources.show['locked']}}" ng-click="resources.toggle('locked')" uib-tooltip="%%learning_filter__prerequisite%%" aria-label="Filter: %%learning_filter__prerequisite%%" ng-if="config.isLockedLearningFilterVisible"></button>
				<button class="filter" ng-class="resources.show['available'] ? 'icon-library-fill' : 'icon-library'" aria-pressed="{{resources.show['available']}}" ng-click="resources.toggle('available')" uib-tooltip="%%learning_filter__enrollment%%" aria-label="Filter: %%learning_filter__enrollment%%"></button>
				<button class="filter" ng-class="resources.show['refresher'] ? 'icon-refresher-fill' : 'icon-refresher'" aria-pressed="{{resources.show['refresher']}}" ng-click="resources.toggle('refresher')" uib-tooltip="%%learning_filter__refreshed_training_scheduled%%" aria-label="Filter: %%learning_filter__refreshed_training_scheduled%%"  ng-if="config.isRefresherLearningFilterVisible"></button>
				<button class="filter" ng-class="resources.show['mandatory'] ? 'icon-mandatory-fill' : 'icon-mandatory'" aria-pressed="{{resources.show['mandatory']}}" ng-click="resources.toggle('mandatory')" uib-tooltip="%%learning_filter__compulsory%%" aria-label="Filter: %%learning_filter__compulsory%%" ng-if="config.isMandatoryLearningFilterVisible"></button>
				<button class="filter" ng-class="resources.show['favorite'] ? 'icon-favorite-fill' : 'icon-favorite'" aria-pressed="{{resources.show['favorite']}}" ng-click="resources.toggle('favorite')" uib-tooltip="%%learning_filter__favorites%%" aria-label="Filter: %%learning_filter__favorites%%" ng-if="config.isFavouriteLearningFilterVisible"></button>

				<!--New Sorting option-->
				<div id="sort-group" class="btn-group" uib-dropdown is-open="status.isopenmain">
					<button type="button"
                            id="sort-button"
                            class="filter-dropdown-toggle-btn u-flexbox u-flexbox--align-center u-margin--right-twenty u-margin--left-ten"
                            uib-dropdown-toggle
                            ng-disabled="disabled"
                            uib-tooltip="Sort By">
						<span class="filter"
							style="margin-left: -0.5rem;"
                            ng-class="sortResourceOrder == 'module.sort_order' ? 'icon-sorting' : 'icon-sorting-fill'"
                            aria-hidden="true"></span>
						<span class="filter-dropdown" id="filter-menu-2">
							<span ng-if="sortResourceOrder == 'module.name'">A to Z</span>
							<span ng-if="sortResourceOrder == '-module.name'">Z to A</span>
							<span ng-if="sortResourceOrder == '-date_sort'">Date (descending)</span>
							<span ng-if="sortResourceOrder == 'date_sort'">Date (ascending)</span>
						</span>
						<span class="caret" aria-hidden="true"></span>
					</button>
					<ul class="dropdown-menu u-larger-font-up-to-md" uib-dropdown-menu role="menu" aria-labelledby="filter-menu-2">
						<li>
							<a href="" ng-click="resources.sortingFilter('module.name')" role="menuitem">
								A to Z
							</a>
						</li>
						<li>
							<a href="" ng-click="resources.sortingFilter('-module.name')" role="menuitem">
								Z to A
							</a>
						</li>
						<li>
							<a href="" ng-click="resources.sortingFilter('-date_sort')" role="menuitem">
								Date (descending)
							</a>
						</li>
						<li>
							<a href="" ng-click="resources.sortingFilter('date_sort')" role="menuitem">
								Date (ascending)
							</a>
						</li>
					</ul>
				</div>
			</div>

			<div class="btn-group learner-toggle-group" role="group" aria-label="Toggle View">
				<button type="button" class="btn btn-default">
					ePortfolio
				</button>
				<button type="button" class="btn btn-primary" ng-click="taskList()">
					List
				</button>
			</div>

		</div>


		<div class="learner-resource__resources u-flexbox" >
			<ng-include ng-if="isApprentix && standards.length > 0" src="'<?=$LMSTplsUriHTML;?>apprentix-search.html?v=<?=$version?>'" class="u-position--relative hidden-xs hidden-sm"></ng-include>

			<div class="u-flex--grow">

				<div class="learner-resource__resource latest_releases hidden-xs hidden-sm" ng-if="config.showLatestReleases && (resourceListSorted|filter: latestReleases).length > 0 && !lif.categoryFilter.selected" ng-class="safeResourceName(resourceTitle)" ng-init="resourceTitle='Latest Releases'; resourceKey='latest_releases'; resourceFilter=latestReleases; resourceLimit=10; resourceOrder='-module.updated_at';" ng-include="'<?=$LMSTplsUriHTML;?>?v=<?=$version?>'"></div>

				<!--Latest News-->
				<div class="dummy-class-test learner-resource__resource anders_briefings" ng-if="andersPink.briefings.length > 0 && !lif.categoryFilter.selected" ng-include="'<?=$LMSTplsUriHTML;?>anders-briefings-inc.html?v=<?=$version?>'"></div>

				<!--Recommendations-->
				<div class="dummy-class-test learner-resource__resource" ng-if="config.showRecommendations && (resourceListSorted|filter: recommendationsFilter).length > 0 && !rs.isDistributor() && !rs.check('is_demo') && !lif.categoryFilter.selected" ng-class="safeResourceName(resourceTitle)" ng-init="resourceTitle='Recommendations'; resourceKey='show'; resourceFilter=recommendationsFilter; show_expand = true; resourceOrder='module.sort_order';" ng-include="'<?=$LMSTplsUriHTML;?>learnerresources-module-inc.html?v=<?=$version?>'"></div>

				<!--In Progress Items-->
				<div class="dummy-class-test learner-resource__resource" ng-if="(resourceListSorted|filter: inProgressFilter).length > 0 && !rs.isDistributor() && !rs.check('is_demo') && !lif.categoryFilter.selected" ng-class="safeResourceName(resourceTitle)" ng-init="resourceTitle='In Progress'; resourceKey='inprogress'; resourceFilter=inProgressFilter; show_expand = true; resourceOrder='module.sort_order';" ng-include="'<?=$LMSTplsUriHTML;?>learnerresources-module-inc.html?v=<?=$version?>'"></div>

				<!--Lessons learner resources-module-->
				<div class="dummy-class-test learner-resource__resource" ng-if="(resourceListSorted|filter: categoryFilter(category, true)).length > 0 && (!lif.categoryFilter.selected || lif.categoryFilter.selected.id == category.id) && resources.categoryCheck(category,resourceListSorted)" ng-class="safeResourceName(category.name)" ng-init="resourceTitle=category.name; resourceKey=category.name; resourceFilter=categoryFilter(category, true); resourceOrder= sortResourceOrder;" ng-include="'<?=$LMSTplsUriHTML;?>learnerresources-module-inc.html?v=<?=$version?>'" ng-repeat="category in categories | orderBy: [categoryOrderSort, 'name']" ></div>

				<!-- Resources without categories -->
				<div class="learner-resource__resource" ng-if="(resourceListSorted|filter: noCategoryFilter).length > 0 && !lif.categoryFilter.selected" ng-class="safeResourceName(resourceTitle)" ng-init="resourceTitle='No category specified'; resourceKey='no_category'; resourceFilter=noCategoryFilter; resourceOrder= sortResourceOrder;" ng-include="'<?=$LMSTplsUriHTML;?>learnerresources-module-inc.html?v=<?=$version?>'"></div>

				<div class="alert alert-warning u-text--center u-font-size--22" role="alert" ng-if="allowNoResultsMessage && (resourceListSorted | filter: allResourcesFilter).length == 0">
					No results
				</div>
			</div>
		</div>
	</div>

	<div class="popup-resources" ng-class="{show: launch.popup}" ng-include="'<?=$LMSTplsUriHTML?>popup-resources.html?v=<?=$version?>'"></div>
</div>



<div class="u-position--absolute learner-resource__loading">
	<div class="loading-splash sub-loading" ng-class="{ in : activeResource || missingResource || isCatRoute() || isEventRoute() }" ng-init="">
		<div class="loading-splash--animated loading-splash-resource">
			<img width="256" height="256" role="none" alt="loader animation" ng-src="<?=$LMSUri?>images/loading/<?=\APP\Tools::getConfig('loadingAnimation')?>"/>
		</div>
	</div>
	<div class="learner-resource__missing" ng-class="{'learner-resource__missing--visible' : missingResource}">
		<p ng-if="resourceList.length == 0 || !resourceList">
			You have no resources assigned.
		</p>
		<p ng-if="resourceList.length > 0">
			Resource URL you are viewing is incorrect.
		</p>
		<p>
			Click "<a href="learner/resources"><u>here</u></a>" to try again or contact administration.
		</p>
	</div>
</div>

<div ng-include="'<?=$LMSTplsUriHTML?>chat-bot.html?v=<?=$version?>'" ng-if="config.isLearningAI && config.chatBotApiBaseUrl"></div>
