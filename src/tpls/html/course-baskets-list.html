<div ng-controller="CourseBasketsListController">
    <div class="cart-container">
        <div class="cart-header">
            <h1>Course Basket</h1>
        </div>

        <div class="cart-body" ng-if="cartItems.length > 0">
            <div class="cart-item" ng-repeat="course in cartItems">
                <div  class="cart-image">
                    <img ng-src="{{ course.item.highlight  || course.item.safe_thumbnail }}"
                         alt="{{ (course.item_type.includes('ApprenticeshipStandard')) ? 'Programme' : 'Thumbnail' }}"
                         class="cart-thumbnail">
                </div>

                <div class="cart-details">
                    <h5 class="cart-title">{{ course.item.name }}</h5>
                    <p class="cart-description text-muted">{{ course.item.description | stripHtml }}</p>
                    <p class="cart-category text-muted">{{ course.item.category.name }}</p>
                    <p class="cart-price">£{{ course.item.cost }}</p>
                </div>

                <div class="cart-actions">
                    <button type="button"
                            class="btn btn-danger btn-xs remove-from-cart-btn"
                            data-key="{{ course.id }}" ng-click="removeFromCart(course.id)">
                        <span class="glyphicon glyphicon-trash"></span>
                    </button>
                </div>
            </div>

            <!-- Cart total and actions -->
            <div class="cart-footer row">
                <div class="col-md-6 col-sm-12">
                    <div id="cart-total" style="font-weight: bold; font-size: 18px;">
                        Subtotal ({{ cartItems.length }} {{ cartItems.length === 1 ? 'item' : 'items' }}): 
                        £{{ getCartSubtotal() | number:2 }}
                    </div>
                </div>

                <div class="col-md-6 text-right">
                    <div class="row">
                        <div class="col-sm-6 col-xs-12">
                            <button type="button" class="btn btn-warning btn-block" ng-click="clearCart()">
                                <span class="glyphicon glyphicon-remove-circle"></span> Clear Cart
                            </button>
                        </div>
                        <div class="col-sm-6 col-xs-12">
                            <button ng-click="checkout()" type="button" id="checkout" class="btn btn-success btn-block">
                                <span class="glyphicon glyphicon-credit-card"></span> Proceed to Checkout
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="alert alert-info text-dark mt-5" ng-if="cartItems.length === 0" style="font-size: 16px; padding: 20px;">
            <strong>Your cart is currently empty.</strong><br>
            Looks like you haven’t added any courses yet. 
            Explore our wide selection of learning resources to get started.<br><br>
            <a href="learner/resources" class="btn btn-primary">
                <span class="glyphicon glyphicon-book"></span> Browse Courses
            </a>
        </div>
        
    </div>
</div>

<style>
    .cart-container {
    background-color: #FFA500;
    color: #000;
    min-height: 100vh;
    padding: 20px;
}

.cart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.cart-body {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.cart-item {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    background-color: #fff;
    border-radius: 5px;
    padding: 7px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    color: #000;
    padding-bottom: 0;
}

.cart-image {
    flex: 0 0 120px;
    margin-right: 20px;
    text-align: center;
    height: 100px;
}

.cart-thumbnail {
    width: 100%;
    height: 100%;
    max-height: 100px;
    object-fit: cover;
    border-radius: 4px;
}

.cart-details {
    flex: 1;
}

.cart-title {
    margin-bottom: 5px;
    color: #333;
    font-weight: bold;
}

.cart-description,
.cart-category {
    margin-bottom: 5px;
}

.cart-price {
    margin-top: 5px;
    font-weight: bold;
    color: green;
}

.cart-actions {
    flex: 0 0 auto;
    margin-left: auto;
}

.cart-footer {
    margin-top: 30px;
    padding-top: 15px;
    border-top: 1px solid rgba(255, 255, 255, 0.3);
}

.cart-total {
    font-weight: bold;
    font-size: 18px;
}

</style>