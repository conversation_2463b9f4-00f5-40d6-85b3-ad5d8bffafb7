<div>
	<div class="pull-right">
		<a class="btn btn-default" href="#" role="button" ng-click="searchStatus= searchStatus == '0' ? '1' :'0'">
			<span ng-show="searchStatus=='0'"><i class="glyphicon glyphicon-ok-circle"></i> Show enabled</span>
			<span ng-show="searchStatus=='1'"><i class="glyphicon glyphicon-ban-circle"></i> Show disabled</span>
		</a>
	</div>
	<div class="pull-left">
		<a class="btn btn-default pull-right" href="" ng-click="clrs.open(false, 'custom-learning-resources-types', true)" role="button">
			<i class="glyphicon glyphicon-plus"></i>
			Add Resource Type
		</a>
	</div>
</div>
<table st-pipe="callServer" st-table="custom-programme-status" class="table table-striped report">
	<thead>
		<tr>
			<th st-sort="id" st-ratio="10">ID</th>
			<th st-sort="name" st-ratio="70">Name</th>
			<th st-sort="slug" st-ratio="10">Slug</th>
			<th st-sort="slug" st-ratio="10">Qualification Evidence</th>
			<th st-ratio="10">Options</th>
		</tr>
		<tr>
			<th>
				<input st-search="id" placeholder="id" class="input-sm form-control" type="number"/>
			</th>
			<th>
				<input st-search="name" class="input-sm form-control" type="search"/>
			</th>
			<th>
				<input st-search="slug" class="input-sm form-control" type="search"/>
			</th>
			<th>
			</th>
			<th>
				<input search-watch-model="searchStatus" st-search="status" type="hidden"/>
				<input search-watch-model="refreshTable" st-search="refresh" type="hidden"/>
			</th>
		</tr>
	</thead>
	<tbody ng-show="!isLoading">
		<tr st-select-row="row" st-select-mode="multiple"  ng-repeat="entry in data">
			<td>
				{{entry.id}}
			</td>
			<td>
				{{entry.name}} <span ng-if="!entry.custom" class="u-disabled pull-right u-padding--right-twenty">system default type</span>
			</td>
			<td>
				{{entry.slug}}
			</td>
			<td>
				<svg ng-if="entry.fit_for_evidence" style="background-color: green; border-radius: 1rem; width: 2rem; height: 2rem; color: white; padding: 0.2rem;" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor"><path fill-rule="evenodd" d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z" clip-rule="evenodd" /></svg>
				<svg ng-if="!entry.fit_for_evidence" style="width:2rem; height:2rem; color:grey;" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor"><path d="M5.28 4.22a.75.75 0 0 0-1.06 1.06L6.94 8l-2.72 2.72a.75.75 0 1 0 1.06 1.06L8 9.06l2.72 2.72a.75.75 0 1 0 1.06-1.06L9.06 8l2.72-2.72a.75.75 0 0 0-1.06-1.06L8 6.94 5.28 4.22Z" /></svg>
			</td>

			<td nowrap>
				<div class="btn-group" uib-dropdown is-open="entry.isopen">
					<button id="single-button" type="button" class="btn btn-default" uib-dropdown-toggle ng-disabled="disabled">
						Actions
						<span class="caret"></span>
					</button>
					<ul class="dropdown-menu dropdown-menu-right" uib-dropdown-menu role="menu" aria-labelledby="single-button">
						<li role="menuitem">
							<a href="" ng-click="clrs.open(entry.id, 'custom-learning-resources-types', entry.custom, entry.fit_for_evidence)">
								<i class="glyphicon glyphicon-edit"></i>
							Edit
						</a>
						</li>
						<li role="separator" class="divider"></li>
						<li role="menuitem">
							<a ng-show="entry.status==1" href="" ng-click="updateStatus(entry.id, 'disable')" confirm="Are you sure you want to disable {{entry.name}}? This will hide all learning resources of this type from learners and prevent new resources from being created." ><i class="glyphicon glyphicon-ban-circle"></i> Disable</a>
							<a ng-show="entry.status==0" href="" ng-click="updateStatus(entry.id, 'enable')" confirm="Are you sure you want to enable {{entry.name}}? This will make this resource type available for creating new learning resources." ><i class="glyphicon glyphicon-ok-circle"></i> Enable</a>
						</li>
					</ul>
				</div>
			</td>
		</tr>
	</tbody>
	<tbody ng-show="isLoading">
		<tr>
			<td colspan="6" class="text-center" loading-bar>Loading ... </td>
		</tr>
	</tbody>
	<tfoot>
		<tr>
			<td colspan="6" class="text-center">
				<div st-items-by-page="10" st-pagination="" st-template="<?=$LMSTplsUriHTML?>pagination.html?v=<?=$version?>"></div>
			</td>
		</tr>
	</tfoot>
</table>