    <!-- Floating <PERSON><PERSON><PERSON> -->
 <div ng-app="chatApp" ng-controller="ChatBotController">
    <!-- <div class="chat-launcher" ng-click="toggleChat()" ng-if="!chatOpen">
        💬
    </div> -->
   <div class="chat-panel" ng-app="chatApp"  ng-show="chatOpen">
        <div class="chat-header">
            <!-- <div class="chat-close " ng-click="toggleChat()">✖</div> -->
            <div class="chat-title text-center text-black">Q&A</div>
            <div class="chat-subtitle text-center text-black mt-1">%%chat_bot__box_subtitle%%</div>
            <div class="chat-controls">
                <button class="chat-control-btn" ng-click="toggleStreaming()" title="Toggle streaming responses" ng-if="false">
                    <i class="fa" ng-class="streamingEnabled ? 'fa-bolt' : 'fa-clock-o'"></i>
                    {{ streamingEnabled ? 'Fast' : 'Standard' }}
                </button>
            </div>
        </div>

        <div class="intro-bubbles" ng-if="suggestionsVisible && messages.length === 0">
            <div class="bubble">%%chat_bot__suggestion_question_1%%</div>
            <div class="bubble">%%chat_bot__suggestion_question_2%%</div>
        </div>

        <div class="suggested-questions" ng-if="suggestionsVisible && messages.length === 0">
            <button ng-repeat="q in suggestions" ng-click="sendSuggestion(q)">
                {{ q }}
            </button>
        </div>

        <div class="chat-body" id="chatScroll">
            <div ng-repeat="msg in messages" ng-class="getClassForMessage(msg)">
                <div class="chat-message">
                    <div ng-if="shouldShowHtml(msg)" ng-bind-html="msg.trustedHtml"></div>
                    <div ng-if="!shouldShowHtml(msg)">{{ msg.text }}</div>
                </div>
            </div>
            <div class="typing" ng-if="isTyping">
                <span class="dot"></span>
                <span class="dot"></span>
                <span class="dot"></span>
            </div>
        </div>

        <div class="chat-footer">
            <input class="text-black" type="text" ng-model="userMessage" placeholder="Send a message..." ng-keypress="checkEnter($event)"  ng-disabled="isTyping"/>
            <button ng-click="sendMessage()" ng-disabled="isTyping">
                <i class="fa fa-paper-plane"></i>
            </button>
        </div>
    </div>
</div>


