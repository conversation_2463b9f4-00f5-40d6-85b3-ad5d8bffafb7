<div class="modal-header">
	<h3 class="modal-title pull-left">
		{{passData.calendarEvent.title}}
	</h3>
	<div class="pull-right">
		<button ng-if="!passData.calendarEvent.position || passData.calendarEvent.position == 'not enrolled'" class="btn btn-primary" type="submit" ng-disabled="dataLoading"
		ng-click="save(passData.calendarEvent, 'schedules')" confirm="{{(passData.calendarEvent.enrolledUsers >= passData.calendarEvent.maxclass && passData.calendarEvent.maxclass != 0) ? 'You are trying to enrol on an event which is fully booked - would you like to be added to a waiting list?' : 'Do you want to Enrol?'}}">
			Enrol
		</button>
		<button ng-if="(!passData.calendarEvent.position || passData.calendarEvent.position == 'not enrolled') && isMultiSelectShoppingBasket" 
				class="btn btn-primary" 
				type="submit" 
				ng-disabled="dataLoading"
				ng-click="addToBasket(passData.calendarEvent)">
			Add to Basket 
		</button>

		<button class="btn btn-danger c-btn-cancel" type="button" ng-click="closeModal()">Close</button>
	</div>
</div>

<div class="modal-body" ng-if="!passData.calendarEvent.position || passData.calendarEvent.position == 'not enrolled'">
	<div uib-alert ng-repeat="alert in alerts" type="{{alert.type}}" ng-class="'alert-' + (alert.type || 'warning')" close="closeAlert($index)">{{alert.msg}}</div>
	<p ng-if="passData.calendarEvent.description">
		<span class="u-white--pre-wrap"><strong>Description</strong>:</span>
	</p>
	<div ng-if="passData.calendarEvent.description" ng-bind-html="trustHtml(passData.calendarEvent.description)"></div>
	<p ng-if="passData.calendarEvent.description"></p>
    <p ng-if="passData.calendarEvent.discounted_cost || passData.calendarEvent.cost">
		<strong>%%cost%%</strong>: {{config.billingCurrencySymbol?config.billingCurrencySymbol:config.currencySymbol}}{{passData.calendarEvent.discounted_cost?passData.calendarEvent.discounted_cost:passData.calendarEvent.cost}}
	</p>
  <p ng-if="passData.calendarEvent.startsAt">
		<strong>Date</strong>: {{passData.calendarEvent.start_date_time | date: config.defaultDateFormat + ' HH:mm'}}
		<span ng-if="!checkIfDateSame(passData.calendarEvent.startsAt,passData.calendarEvent.end_date)">- {{passData.calendarEvent.end_date_time | date: config.defaultDateFormat + ' HH:mm'}}</span>
	</p>
	<p ng-if="passData.calendarEvent.deadlineDate">
		<strong>Booking Deadline Date</strong>: {{passData.calendarEvent.deadlineDate | date: config.defaultDateFormat + ' HH:mm'}}
	</p>
	<p ng-if="passData.calendarEvent.dropOffDeadlineAt">
		<strong>Booking Cancellation Date</strong>: {{passData.calendarEvent.dropOffDeadlineAt | date: config.defaultDateFormat + ' HH:mm'}}
	</p>
	<p ng-if="passData.calendarEvent.startsAt">
		<strong>Duration</strong>: {{passData.calendarEvent.duration}} Minutes
	</p>
	<p ng-if="passData.calendarEvent.maxclass">
		<strong>Enrolled</strong>: {{passData.calendarEvent.enrolledUsers}} / {{ passData.calendarEvent.maxclass }}
	</p>
	<div ng-if="passData.calendarEvent.venueDetails">
		<p>
			<strong>Venue Details</strong>:
		</p>
		<ul>
			<li ng-if="passData.calendarEvent.venueDetails.name"> <strong>Name</strong>: {{passData.calendarEvent.venueDetails.name}}</li>
			<li ng-if="passData.calendarEvent.venueDetails.address"> <strong>Address</strong>: {{passData.calendarEvent.venueDetails.address}}, {{passData.calendarEvent.venueDetails.postcode}}</li>
			<li ng-if="passData.calendarEvent.venueDetails.instructions"> <strong>Instructions</strong>: {{passData.calendarEvent.venueDetails.instructions}}</li>
			<li ng-if="passData.calendarEvent.venueDetails.image_url" style="list-style: none">
				<img ng-src="{{passData.calendarEvent.venueDetails.image_url}}" alt="Venue promo image" width="320" />
			</li>
		</ul>
	</div>

	<p>
		<strong>%%learner_interface__special_requirements%%</strong>:
		<textarea class="form-control" name="learner_requirement" id="learner-requirement" ng-model="passData.calendarEvent.learner_requirement" rows="3"></textarea>
	</p>

	<div ng-if="passData.calendarEvent.coupon_exists && passData.calendarEvent.discounted_cost > 0">
		<div>
			<label>
				Have Coupon ? <input type="checkbox" class="form-group-lg" ng-model="couponOpt.enabled" id="use_coupon">
			</label>
		</div>
		<div ng-if="couponOpt.enabled">
			<label>
				Coupon code <input type="text" ng-model="couponOpt.code" id="coupon_code"
								   placeholder="Enter Code here...">
				<button ng-click="applyCoupon()" class="u-pointer">Apply</button>
			</label>
			<span ng-if="dataLoading">
                <span class="glyphicon glyphicon-refresh u-spinner"></span>
            </span>

			<div ng-if="discounts.coupon_discount"><b>Coupon Applied: </b> -{{discounts.coupon_discount}}% = €{{discounts.cost_after_coupon_apply}}

				<div ng-if="discounts.user_discount || discounts.company_discount || discounts.department_discount"><b>User + %%company%% + %%department%% discount percentage any:</b>
					{{discounts.user_discount + "% + " + discounts.company_discount + "% + " + discounts.department_discount
					}}% =
					{{discounts.user_discount + discounts.company_discount + discounts.department_discount }}%
				</div>
				<div ng-if="discounts.coupon_discount"><b>Total payable amount after discounts: </b> €{{discounts.total}}
				</div>
			</div>
		</div>
	</div>
</div>
<div ng-if="passData.calendarEvent.position > 0" class="modal-body">
	<div uib-alert ng-repeat="alert in alerts" type="{{alert.type}}" ng-class="'alert-' + (alert.type || 'warning')" close="closeAlert($index)">{{alert.msg}}</div>
	<p>
		<strong>Already enrolled and the position in waiting list is : {{ passData.calendarEvent.position }}</strong>
	</p>
</div>
<div class="modal-footer">
	<button ng-if="!passData.calendarEvent.position || passData.calendarEvent.position == 'not enrolled'" class="btn btn-primary" type="submit" ng-disabled="dataLoading"
	ng-click="save(passData.calendarEvent, 'schedules')" confirm="{{(passData.calendarEvent.enrolledUsers >= passData.calendarEvent.maxclass && passData.calendarEvent.maxclass != 0) ? 'You are trying to enrol on an event which is fully booked - would you like to be added to a waiting list?' : 'Do you want to Enrol?'}}">
		Enrol
	</button>
	<button ng-if="(!passData.calendarEvent.position || passData.calendarEvent.position == 'not enrolled') && isMultiSelectShoppingBasket" 
			class="btn btn-primary" 
			type="submit" 
			ng-disabled="dataLoading"
			data-test="{{  }}"
			ng-click="addToBasket(passData.calendarEvent)">
		Add to Basket 
	</button>
	<button class="btn btn-danger c-btn-cancel" type="button" ng-click="closeModal()">Close</button>
</div>
