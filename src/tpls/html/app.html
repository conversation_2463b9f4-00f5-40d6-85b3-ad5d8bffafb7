<!doctype html>
<!--  NOTE: isMobileRequest is used to remove the top padding and margin when opening in iframe from mobile application -->
<html ng-app="lmsApp" class="<?=$colorScheme?>" ng-class="{'u-overflow--hidden': hidden<PERSON>ody<PERSON>croll, 'lms-learner' : !rs.interface('admin') && !isMobileRequest, 'print-report' : printReport, 'disable-background-image' : currentUser.accessible_ui, 'accessible' : currentUser.accessible_ui}" lang="en" ng-controller="lmsController" ng-style="{'padding-top': appMode ? 0 : '', '--bg-image': rs.interface('learner') ? bgImage : ''}">
	<head>

		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=0.5, maximum-scale=4">
		<title><?=$LMSTitle?></title>

		<link rel="icon" type="image/png" href="<?=$LMSUri?>images/favicon.png" />

		<script>
		(function () {
			window.logClientError = function(errorData) {
				try {
					var message = (errorData.message || '').toString();
					var type = (errorData.type || '').toString();
					var skip = false;

					// === START: SKIP CONDITIONS ===

					// Skip generic HTTP errors
					if (message.includes('"status":-1') && message.includes('"statusText":""')) {
						skip = true;
					}

					// Skip Forbidden or 403 errors
					if (message.includes('"status":403') || message.includes('403 Forbidden')) {
						skip = true;
					}

					// Skip specific known phrases
					const skipPhrases = [
						'Error loading',
						'Possibly unhandled rejection',
						'play() failed',
						'The play method',
						'play@[native code]',
						'Node cannot be null or undefined',
						'show-errors element has no child input elements',
					];
					for (var i = 0; i < skipPhrases.length; i++) {
						if (message.includes(skipPhrases[i])) {
							skip = true;
							break;
						}
					}

					// Skip all http errors if tagged as such
					if (type === 'http_response_error') {
						skip = true;
					}

					// === END: SKIP CONDITIONS ===

					if (skip) return;

					// Send the error
					var xhr = new XMLHttpRequest();
					xhr.open('POST', '<?=$LMSUri?>log/log-js-error', true);
					xhr.setRequestHeader('Content-Type', 'application/json');
					xhr.send(JSON.stringify({
						type: type,
						message: message,
						source: errorData.source || '',
						line: errorData.line || '',
						column: errorData.column || '',
						stack: errorData.stack || '',
						userAgent: navigator.userAgent,
						url: window.location.href,
						timestamp: new Date().toISOString()
					}));
				} catch (e) {
					// fail silently
				}
			};

			window.onerror = function (message, source, lineno, colno, error) {
				window.logClientError({
					type: 'error',
					message: message,
					source: source,
					line: lineno,
					column: colno,
					stack: (error && error.stack) || null
				});
			};

			window.onunhandledrejection = function (event) {
				var reason = event && event.reason;
				window.logClientError({
					type: 'unhandledrejection',
					message: (reason && reason.message) || String(reason),
					stack: (reason && reason.stack) || null
				});
			};
		})();
		</script>

		<?=$googleAnalyticsCode?>

		<link rel="stylesheet" href="<?=$LMSUri?>css/autocomplete.css">
		<link rel='stylesheet' href='<?=$LMSUri?>css/font-awesome.min.css'>
		<link rel='stylesheet' href='<?=$LMSUri?>css/font-awesome-animation.min.css'>
		<link rel='stylesheet' href='<?=$LMSUri?>css/fonts.css'>
		<link rel="stylesheet" href="<?=$LMSUri?>css/main.css?v=<?=$version?>">
		<link rel="stylesheet" href="<?=$LMSUri?>css/ng-hierarchical-selector.0.4.3.min.css">
		<link rel='stylesheet' href='<?=$LMSUri?>client/client.css'>
		<link href="https://fonts.googleapis.com/css2?family=Yantramanav:wght@100;200;300;400;500;600;700;800;900&&display=swap" rel="stylesheet" />

		<link rel="stylesheet" href="<?=$LMSUri?>css/angular-toggle-switch-bootstrap.css">
		<link rel="stylesheet" href="<?=$LMSUri?>css/angular-toggle-switch.css">
		<link rel='stylesheet' href='<?=$LMSUri?>js/jcrop/jcrop.css'>

		<!-- TODO: replace with a self hosted -->
		<link rel="preconnect" href="https://fonts.googleapis.com">
		<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="true">
		<link href="https://fonts.googleapis.com/css2?family=Roboto:wght@100;200;300;400;500;600;700;800;900&amp;display=swap" rel="stylesheet">

		<script src="<?=$LMSUri?>js/jquery-3.6.1.min.js"></script>
		<script src="<?=$LMSUri?>js/angular.min.js"></script>
		<style>:root {interpolate-size: allow-keywords;}</style>
		<script type="text/javascript" src="<?=$LMSUri?>js/angular-wizard.min.js"></script>
		<link rel="stylesheet" href="<?=$LMSUri?>css/angular-wizard.min.css">
		<script type="text/javascript">
			document.baseTitle = "<?=$LMSTitle?>";
			angular.element(document.getElementsByTagName('head')).append(angular.element('<base href="<?=$LMSAppUri?>" />'));
		</script>
	</head>
	<body class="faded u-flexbox" ng-class="[{in: ngLoaded, 'disable-background-image' : currentUser.accessible_ui && !rs.interface('admin')}, 'dashboard__body-bg--' + dashboard.selected.group.key]">
		<role-name class="u-invisible" id="<?=$role_name_slug?>"><?=$role_name?></role-name>
		<!-- Loading animation/screen -->
		<preloader class="loading-splash">
			<div class="loading-splash--animated">
				<img width="256" height="256" role="none" alt="loader animation" src="<?=$LMSUri?>images/loading/<?=\APP\Tools::getConfig('loadingAnimation')?>"/>
			</div>
		</preloader>

		<main class="container-fluid lms-container u-flexbox" id="lms-main">

			<!-- Administration header -->
			<div class="lms-header u-flex--no-shrink"
				ng-if="rs.interface('admin')"
				style="display: flex; justify-content: space-between;"
			>
				<!-- LOGO -->
				<div class="lms-header__logo pull-left u-flex--no-shrink">
					<span class="u-position--relative u-flexbox">
						<a href="" ng-href="{{config.hideHomeButton ? workUrl : '<?=$logoURL?>'}}" ng-click="setGroup(config.hideHomeButton ? workUrlKey : dashboardUrlKey)" aria-label="link to the home screen" ng-class="config.hideHomeButton ? 'u-flex--grow' : 'u-max-width'"><img ng-src="{{config.logo}}" alt="" class="u-max-width "></a>
					</span>
				</div>
				<div style="display: flex; gap: 1rem; margin: 0 2rem; height: 72px; overflow: hidden;" ng-controller="ShadowRole" ng-if="config.rolesAtPageHeader">
					<div
						ng-repeat="role in shadow_roles | orderBy : 'name'"
						style="display: flex; flex-direction: column; align-items: center; gap: 6px; cursor: pointer;"
						ng-click="shadow.set(role)"
					>
						<img width="42" height="42" ng-src="{{role.default_image ? '<?=$LMSUrl?>/images/roles/' + role.image : '<?=$LMSUrl;?>role/image/' + role.image}}" alt="">
						<div
							style="text-shadow: 1px 1px 1px #0005; font-size: 10px; max-width: 16ch; color: #fff; text-transform: uppercase; letter-spacing: .5px; text-align: center; overflow: hidden; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical;"
							ng-style="role.id == shadow.role.id ? {'font-weight': 800} : {}"
						>
							{{role.name}}
						</div>
					</div>
				</div>

				<div class="lms-header__admin pull-right u-flex--no-shrink">
					<ol class="learner__top-icons learner__top-icons--dark">
						<li class="learner__top-icon" ng-repeat="group in dashboard.structure | orderBy: 'order'" ng-class="{'hidden' : group.id == 103}">
							<a ng-hide="hideTopIcon(group)" ng-href="dashboard/{{group.key}}{{group.children[0].key ? '/' + group.children[0].key : ''}}{{group.children[0].children[0].key ? '/' + group.children[0].children[0].key : ''}}" title="{{group.name}}" ng-class="{'active' : dashboard.selected.group.key == group.key}" ng-click="setGroup(group.key)" style="background: {{dashboard.selected.group.key == group.key ? 'var('+dashboard.selected.group.color+')' : '#88888864'}};">
								<img ng-src="<?=$LMSIconsUri?>{{group.icon}}" alt="">
								<br/><div>{{group.name}}</div>
							</a>
						</li>
					</ol>

					<div class="lms-header__profile u-text--initial">
						<a href="javascript:;" class="user-profile" aria-expanded="false"><img ng-src="<?=$LMSUri?>{{currentUser.image ? 'user/files/image/' + currentUser.id : 'images/test-user.jpg'}}" alt="Avatar"><span class="user--full-name">{{currentUser.fname}} {{currentUser.lname}}</span><span class="fa fa-angle-down"></span></a>
						<ul class="dropdown-menu dropdown-usermenu pull-right">
							<li>
								<a href="" ng-click="myProfile()">
									<i class="fa fa-user pull-right"></i> Profile
								</a>
							</li>
							<li>
								<a href="" ng-click="changePassword()">
									<i class="fa fa-asterisk pull-right"></i> Change Password
								</a>
							</li>
							<li ng-if="config.googleTranslate">
								<a href="">
									<i class="fa fa-language pull-right"></i>
									<div id="google_translate_element"></div>
								</a>
							</li>
							<li ng-if="rs.interface('admin')">
								<a ng-href="<?=$licensing['manual']?>" target="_blank">
									<i class="fa fa-question-circle pull-right"></i> Manual
								</a>
							</li>

							<li ng-if="rs.interface('admin') && config.AIChatLink && rs.check('is_admin')">
								<a ng-href="{{config.AIChatLink}}" target="_blank">
									<i class="fa fa-comments-o pull-right"></i> AI Chat
								</a>
							</li>

							<li ng-if="currentUser && shadow_roles.length > 1" ng-controller="ShadowRole">
								<a href="">
									Current role:<br>
									<select class="dashboard-role__dropdown u-max-width" aria-label="Current role" ng-model="shadow.role" ng-options="role as role.name for role in shadow_roles | orderBy : 'name'" ng-change="shadow.set()">
									</select>
								</a>
							</li>
							<li ng-controller="ShadowRole" ng-show="linkedAccounts.length > 1 && showAccounts" ng-init="getLinkedAccounts()">
								<a href="">
									Switch Account:{{rs.user.role}}<br>
									<select class="dashboard-role__dropdown" aria-label="Switch Account" ng-model="selectedAccount" ng-options="(selectedAccount.account_type ? selectedAccount.account_type.value : selectedAccount.username) for selectedAccount in linkedAccounts track by selectedAccount.id " ng-change="us.impersonate(selectedAccount, true)">
									</select>
								</a>
							</li>
							<li ng-if="config.sharedClients">
								<a href="" ng-click="bs.open()">
									<i class="fa pull-right" ng-class="'fa-' + config.billingMenuSymbol"></i> Billing
								</a>
							</li>
							<li>
								<a href="javascript:;" ng-click="logout()">
									<i class="fa fa-sign-out pull-right"></i> Log Out
								</a>
							</li>
							<li>
								<a href="" class="u-text--right u-background--white u-font-family--courier">
									<small>
										{{config.release}}
									</small>
								</a>
							</li>
							<li ng-if="rs.check('is_admin')">
								<div class="lms-concat">
									<span class="btn btn-default btn-xs" style="background-color: black;" href="#" ng-click="reloadApp(true)" role="button">
										<i style="color: red;" class="glyphicon glyphicon-refresh"></i>
									</span>
									<span class="btn btn-default btn-xs" style="background-color: black;" href="#" ng-click="reloadApp(false)" role="button">
										<i style="color: white;" class="glyphicon glyphicon-refresh"></i>
									</span>
								</div>
							</li>
						</ul>
					</div>
					<div id="docsbot-icon" uib-tooltip="<?=$docs_bot_tooltip?>" tooltip-placement="left" ng-click="toggleDocsBot()" ng-if="config.AIPoweredHelpAssistant">
						<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 25" stroke-width="1" stroke="currentColor" class="h-8 w-8 text-cyan-100 sm:h-10 sm:w-10" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" d="M 12.017883 3.035019 C 12.680645 3.035019 13.216095 3.536776 13.216095 4.157831 L 13.216095 6.403454 L 17.709389 6.403454 C 19.199665 6.403454 20.405365 7.533283 20.405365 8.929779 L 20.405365 18.473675 C 20.405365 19.870171 19.199665 21 17.709389 21 L 6.326378 21 C 4.836102 21 3.630402 19.870171 3.630402 18.473675 L 3.630402 8.929779 C 3.630402 7.533283 4.836102 6.403454 6.326378 6.403454 L 10.819673 6.403454 L 10.819673 4.157831 C 10.819673 3.536776 11.355123 3.035019 12.017883 3.035019 Z M 7.824143 16.508755 C 7.494635 16.508755 7.225037 16.761387 7.225037 17.07016 C 7.225037 17.378933 7.494635 17.631565 7.824143 17.631565 L 9.022355 17.631565 C 9.351863 17.631565 9.621461 17.378933 9.621461 17.07016 C 9.621461 16.761387 9.351863 16.508755 9.022355 16.508755 L 7.824143 16.508755 Z M 11.418778 16.508755 C 11.089271 16.508755 10.819673 16.761387 10.819673 17.07016 C 10.819673 17.378933 11.089271 17.631565 11.418778 17.631565 L 12.616989 17.631565 C 12.946497 17.631565 13.216095 17.378933 13.216095 17.07016 C 13.216095 16.761387 12.946497 16.508755 12.616989 16.508755 L 11.418778 16.508755 Z M 15.013412 16.508755 C 14.683905 16.508755 14.414307 16.761387 14.414307 17.07016 C 14.414307 17.378933 14.683905 17.631565 15.013412 17.631565 L 16.211624 17.631565 C 16.541132 17.631565 16.81073 17.378933 16.81073 17.07016 C 16.81073 16.761387 16.541132 16.508755 16.211624 16.508755 L 15.013412 16.508755 Z M 9.921014 12.017509 C 9.921014 11.242371 9.250442 10.613996 8.423249 10.613996 C 7.596056 10.613996 6.925484 11.242371 6.925484 12.017509 C 6.925484 12.792649 7.596056 13.421023 8.423249 13.421023 C 9.250442 13.421023 9.921014 12.792649 9.921014 12.017509 Z M 15.612518 13.421023 C 16.439711 13.421023 17.110283 12.792649 17.110283 12.017509 C 17.110283 11.242371 16.439711 10.613996 15.612518 10.613996 C 14.785326 10.613996 14.114754 11.242371 14.114754 12.017509 C 14.114754 12.792649 14.785326 13.421023 15.612518 13.421023 Z M 1.833085 10.894698 L 2.432191 10.894698 L 2.432191 17.631565 L 1.833085 17.631565 C 0.840816 17.631565 0.035768 16.877178 0.035768 15.94735 L 0.035768 12.578916 C 0.035768 11.649087 0.840816 10.894698 1.833085 10.894698 Z M 22.202682 10.894698 C 23.194952 10.894698 24 11.649087 24 12.578916 L 24 15.94735 C 24 16.877178 23.194952 17.631565 22.202682 17.631565 L 21.603577 17.631565 L 21.603577 10.894698 L 22.202682 10.894698 Z"></path></svg>
					</div>
				</div>
			</div>
			<!-- EOF Administration header -->
			<!-- AI RESULTS CONTAINER -->
			<div ng-if="showAIResults && config.isLearningAI" id="ai-search-results" class="ai-results u-margin--top-ten" role="region" aria-label="AI-generated suggestions">
				<span class="fa fa-times ai-close-icon float-right" ng-click="$root.showAIResults = false" aria-label="Close" title="Close" style="cursor: pointer; margin: 1rem;"></span>
				<div class="ai-result-card" ng-repeat="result in ($root.showingAllAIResults ? $root.aiResults : $root.aiResults.slice(0, 3))">
					<div class="ai-result-card__header">
						<span class="fa fa-magic"></span> {{ result.title || 'Suggested Answer' }}
					</div>

					<div class="ai-result-card__content"
						ng-class="{'expanded': $root.showingAllAIResults}"
						ng-style="!$root.showingAllAIResults && {'max-height': '200px', 'overflow': 'hidden'}"
						ng-bind-html="result.trustedHtml">
					</div>

					<div class="ai-toggle-btn-container">
						<button class="ai-toggle-btn" ng-click="$root.toggleShowAllAIResults()" aria-expanded="{{ $root.showingAllAIResults }}">
							{{ $root.showingAllAIResults ? 'Show Less' : 'Show More' }}
						</button>
					</div>
				</div>
			</div>
			<!-- EOF AI RESULTS CONTAINER -->

			<!-- Learner header -->
			<div class="lms-header u-flexbox u-flex--no-shrink lms-header--learner"  ng-if="rs.interface('learner')" ng-show="!appMode && !isMobileRequest">
				<a href="<?=$logoURL?>" title="Click here to return to the home page" ng-click="resetTasks()" class="lms-header__logo u-flex--no-shrink u-margin--right-ten"><img ng-src="{{config.logo}}" alt="Logo" class="u-max-width" style="max-width: 50vw;"></a>

				<a class="skip-to-content-link" href="#" ng-click="skip2main()">Skip to content</a>

				<form ng-controller="learnerSearchBar" class="u-flex--no-grow lms-header__search hidden-xs hidden-sm" ng-submit="submitSearch()" ng-if="config.isSearchTabVisible && (!isSMCR || !dashboard.pkf)" style="width: 43rem; max-width: 50rem;">
					<div class="input-group">
						<div class="input-group-btn">
							<div class="btn-group" uib-dropdown is-open="status.isopen">
								<button id="single-button1" type="button" class="btn btn-default" uib-dropdown-toggle ng-disabled="disabled" aria-label="Select a category to search for">
									{{selectedType.name || 'All'}}
									<span class="caret"></span>
								</button>
								<ul class="dropdown-menu u-height-limit--overflow-y-60vh" uib-dropdown-menu role="menu" aria-labelledby="single-button1">
									<li role="menuitem" ng-click="updateType(false)"><a href="#">All</a></li>
									<li role="menuitem" ng-click="updateType(type)" ng-repeat="type in types | orderBy : 'name'">
										<a href="#">{{type.name}}</a>
									</li>
								</ul>
							</div>
						</div><!-- /btn-group -->
						<label for="resource_search_term" class="hidden">Search input </label>
						<input type="text" style="min-width: 14ch;" class="form-control" id="resource_search_term" name="resource_search_term" ng-model="resourceSearch" placeholder="Type to search courses or ask AI…" aria-label="Type to search courses, or ask AI…" 
							ng-change="updateResourceSearch()" ng-model-options="{ debounce: 500 }" ng-keypress="handleSearchKeyPress($event)" />
						<span class="input-group-btn">
							<button class="btn btn-warning" type="submit" aria-label="Submit Search" title="Submit Search">
								<span class="fa fa-search"></span>
							</button>
						</span>
					</div>
				</form>


				<ol class="learner__top-icons u-flex--grow u-flexbox ">
					<li class="learner__top-icon u-position--relative" ng-if="config.allowLearnerUploads && (((isApprentix || (isOpenElmsTMS && config.allowAddBlogEntry)) && !isSMCR) || (isSMCR && !dashboard.pkf))" ng-controller="UploadResources">
						<a href="" ng-click="uploadResources()" uib-tooltip="Upload Resources" tooltip-class="{{!rs.fffUI() ? 'white-right' : ''}} white-right--static" tooltip-placement="right" title="Upload Resources" class="upload">
							<span class="fa fa-plus-circle"></span>
						</a>
					</li>

					<li class="learner__top-icon" ng-if="isSMCR">
						<a href="personal-knowledge-file" ng-if="!dashboard.pkf" uib-tooltip="F&P Activities File" tooltip-class="{{!rs.fffUI(5) ? 'white-right' : ''}}" tooltip-placement="right" class="u-flex--middle u-hide-hover">
							<span class="fa fa-user pkf-activate"></span>
						</a>
					</li>
					<li class="learner__top-icon" ng-if="rs.interface('jackdaw') && !rs.isDistributor() && !dashboard.pkf && !config.jackdaw.disabled" ng-controller="JackdawCloud" uib-tooltip="{{config.jackdaw.addLimit && config.jackdaw.add <= 0 ? 'You can\'t add any more e-Learning' : ''}}" tooltip-class="{{!rs.fffUI() ? 'white-right' : ''}}" tooltip-placement="right">
						<a href="" title="Create e-Learning" ng-class="{'u-disabled' : config.jackdaw.addLimit && config.jackdaw.add <= 0 }" ng-click="jc.prompt()">
							<img src="<?=$LMSIconsUri?>create_elearning2<?=$colorSchemePre?>.svg" class="u-max-height--twenty-six" alt="" border="0">
						</a>
					</li>
					<li class="learner__top-icon" ng-if="!rs.isDistributor() && las.list.length > 0 && !config.hideResourcesNeedAttentionHeaderButton">
						<a href="" ng-click="las.open()" uib-tooltip="Resources need attention" title="Resources need attention" tooltip-class="{{!rs.fffUI() ? 'white-right' : ''}}" tooltip-placement="right">
							<div class="fa fa-bell faa-ring animated faa-slow" ng-class="{'animated': las.animate}">
								<span class="due-count">{{las.list.length}}</span>
							</div>
						</a>
					</li>
					<li class="learner__top-icon hidden-xs hidden-sm" ng-if="rs.isDistributor() && !dashboard.pkf">
						<a ng-href="learner/distribute" title="Distribute" uib-tooltip="Distribute" tooltip-class="{{!rs.fffUI() ? 'white-right' : ''}}" tooltip-placement="right">
							<span class="glyphicon glyphicon-download-alt"></span>
						</a>
					</li>
					<li class="learner__top-icon hidden-xs hidden-sm" ng-if="config.isPeertoPeerVideo">
						<a ng-controller="SkypeContacts" aria-label="Contact your trainers" ng-if="!dashboard.pkf" ng-click="showSkypeContacts()" href="" uib-tooltip="Launch a conversation" tooltip-class="{{!rs.fffUI() ? 'white-right' : ''}}" tooltip-placement="right" class="u-flex--middle u-hide-hover">
							<span class="glyphicon glyphicon-comment pkf-activate" style="color:#00AFF0"></span>
						</a>
					</li>
					<li class="learner__top-icon hidden-xs hidden-sm" ng-if="!rs.isDistributor() && !config.hideHelpHeaderButton">

						<a ng-href="<?=$LMSUri?>learner/learnerwalkthrough" target="_blank" class="fa fa-question-circle lms learner-info-icon" title="<?=\APP\Templates::translate('%%open_introduction_in_new_window_message%%')?>" uib-tooltip="<?=\APP\Templates::translate('%%open_introduction_in_new_window_message%%')?>" tooltip-class="{{!rs.fffUI() ? 'white-right' : ''}}" tooltip-placement="right" ng-if="!isSMCR && !config.HelpVideoURL"></a>

						<a ng-href="<?=$LMSUri?>learner/smcr-help" target="_blank" class="fa fa-question-circle lms learner-info-icon" title="<?=\APP\Templates::translate('%%open_introduction_in_new_window_message%%')?>" uib-tooltip="<?=\APP\Templates::translate('%%open_introduction_in_new_window_message%%')?>" tooltip-class="{{!rs.fffUI() ? 'white-right' : ''}}" tooltip-placement="right" ng-if="isSMCR && !config.HelpVideoURL"></a>

						<a ng-href="{{config.HelpVideoURL}}" target="_blank" class="fa fa-question-circle lms learner-info-icon" title="<?=\APP\Templates::translate('%%open_introduction_in_new_window_message%%')?>" uib-tooltip="<?=\APP\Templates::translate('%%open_introduction_in_new_window_message%%')?>" tooltip-class="{{!rs.fffUI() ? 'white-right' : ''}}" tooltip-placement="right" ng-if="config.HelpVideoURL"></a>
					</li>
					<li class="learner__top-icon hidden-xs hidden-sm" ng-if="rs.isDistributor() && !config.hideHelpHeaderButton">
						<a ng-href="<?=$LMSUri?>learner/distributorwalkthrough" target="_blank" class="fa fa-question-circle lms learner-info-icon" ng-if="rs.isDistributor()" title="<?=\APP\Templates::translate('%%open_introduction_in_new_window_message%%')?>" uib-tooltip="<?=\APP\Templates::translate('%%open_introduction_in_new_window_message%%')?>" tooltip-class="{{!rs.fffUI() ? 'white-right' : ''}}" tooltip-placement="right"></a>
					</li>
					<li class="learner__top-icon hidden-xs hidden-sm" ng-if="isApprentix && standards.length > 0 && config.learnerProgressGauge" ng-controller="learnerStandardProgress" style="height: 35px;">
						<div class="u-position--relative learner__gauge" title="{{gauge.selected.name}}" ng-class="{'visible': visibleGauge}" ng-click="visibleGauge = !visibleGauge">
							<ng-gauge max="gauge.max" size="{{gauge.size}}" cap="{{gauge.cap}}" thick="{{gauge.thick}}" type="{{gauge.type}}" value="gauge.value" thresholds="gauge.thresholds" background-color="{{gauge.backgroundColor}}" append="%" ng-if="gauge.init"></ng-gauge>
							<ul class="dropdown-menu">
								<li ng-repeat="standard in standards">
									<a href="" ng-class="{'selected' : gauge.selected.id == standard.id}" ng-click="setGaugeStandard(standard)" title="{{standard.name}} ({{standard.standarduser.time_spent | number : 0}}h of {{standard.standarduser.working_hours}}h)">{{standard.name}} ({{standard.standarduser.time_spent | number : 0}}h of {{standard.standarduser.working_hours}}h)</a>
								</li>
							</ul>
						</div>
					</li>
					<!-- Download mobile app -->
					<li class="learner__top-icon hidden-xs hidden-sm" ng-if="currentUser.phone && config.MobilePhoneAuthentication" >
						<a href="" ng-controller="AddPhoneNumber" ng-click="AddPhoneNumber()" title="Download Mobile Application" tooltip-class="{{!rs.fffUI() ? 'white-right' : ''}} white-right--static" tooltip-placement="right" >
							<span class="fa fa-mobile"></span>
						</a>
					</li>
				</ol>

				<div style="display: flex; gap: 1rem; margin: 0 2rem; height: 72px; overflow: hidden;" ng-controller="ShadowRole" ng-if="config.rolesAtPageHeaderLearner && shadow_roles.length > 1">
					<div
						ng-repeat="role in shadow_roles | orderBy : 'name'"
						style="display: flex; flex-direction: column; align-items: center; gap: 6px; cursor: pointer;"
						ng-click="shadow.set(role)"
					>
						<img width="42" height="42" ng-src="{{role.default_image ? '<?=$LMSUrl?>/images/roles/' + role.image : '<?=$LMSUrl;?>role/image/' + role.image}}" alt="">
						<div
							style="text-shadow: 1px 1px 1px #0005; font-size: 10px; max-width: 16ch; color: #fff; text-transform: uppercase; letter-spacing: .5px; text-align: center; overflow: hidden; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical;"
							ng-style="role.id == shadow.role.id ? {'font-weight': 800} : {}"
						>
							{{role.name}}
						</div>
					</div>
				</div>

				<div ng-controller="LearnerTextMenu">
					<!-- Flexy space -->
					<ol class="learner__top-icons learner__top-icons--dark hidden-xs hidden-sm">
						<li class="learner__top-icon" ng-repeat="entry in menuItems | filter : {enabled: true} | orderBy: 'order'">
							<a ng-href="{{entry.url}}" ng-class="{'active' : (activeMenuItem === entry)}" ng-click="setMenuItem(entry)" style="background: {{(activeMenuItem === entry) ? '#2578cf' : '#88888864'}};">
								<img ng-src="<?=$LMSIconsUri?>{{entry.icon}}" alt="menu item" ng-if="!entry.glyphicon">
								<span ng-if="entry.glyphicon" class="glyphicon" ng-class="entry.glyphicon"></span>
								 <!-- Cart Count Badge -->
								<div class="cart" ng-show="entry.slug == 'learner_basket'" ng-if="entry.slug == 'learner_basket'">
									{{ multiCartItemCount }}
								</div>
								<!-- <br/> -->
								<div>{{entry.name}}</div>
								
							</a>
							<!-- <span ng-if="entry.slug == 'LEARNER_BASKET'" >12</span> -->
						</li>
					</ol>
					<!-- EOF Flexy space -->

					<div>
						<span uib-dropdown on-toggle="toggled(open)" auto-close="outsideClick" class="dropdown" is-open="status.isopen">
							<a href uib-dropdown-toggle class="user-profile dropdown-cont" aria-expanded="false" id="simple-dropdown">
								<img ng-src="<?=$LMSUri?>{{currentUser.image ? 'user/files/image/' + currentUser.id : 'images/test-user.jpg'}}" alt="Avatar" class="hidden-xs hidden-sm">
								<span class="user--full-name hidden-xs hidden-sm">{{currentUser.fname}} {{currentUser.lname}}</span>
								<span class="fa fa-angle-down hidden-xs hidden-sm"></span>
								<span class="fa fa-bars hidden-md hidden-lg" ng-click="visibleDropDown = !visibleDropDown"></span>
							</a>

							<ul uib-dropdown-menu class="dropdown-menu dropdown-menu-right" aria-labelledby="simple-dropdown" ng-click="hideMenu()" id="dropdown-menu">

								<!-- MOBILE MENU -->

								<li class="mobile-menu-label hidden-md hidden-lg">
									{{currentUser.fname}} {{currentUser.lname}}
								</li>

								<li class="hidden-md hidden-lg">
									<a onblur="menuBlur(event)" title="hideMenu" href="" ng-href="{{entry.url}}" ng-click="setMenuItem(entry)" ng-class="{'active_item': (activeMenuItem === entry)}" ng-repeat="entry in menuItems | filter : {enabled: true}">{{entry.name}}</a>
								</li>

								<li class="hidden-md hidden-lg" ng-if="rs.isDistributor() && !dashboard.pkf">
									<a title="hideMenu" ng-href="learner/distribute">
										<i class="fa fa-download pull-right"></i> Distribute
									</a>
								</li>
								<li class="hidden-md hidden-lg">
									<a title="hideMenu" ng-controller="SkypeContacts" ng-if="!dashboard.pkf" ng-click="showSkypeContacts()" href="">
										<i class="fa fa-comments pull-right"></i> Contacts
									</a>
								</li>
								<li class="hidden-md hidden-lg" ng-if="!rs.isDistributor()">
									<a title="hideMenu" href="<?=$LMSUri?>learner/learnerwalkthrough" target="_blank" ng-if="!isSMCR && !config.HelpVideoURL" >
										<i class="fa fa-question-circle pull-right"></i> Open introduction
									</a>
									<a title="hideMenu" href="<?=$LMSUri?>learner/smcr-help" target="_blank" ng-if="isSMCR && !config.HelpVideoURL">
										<i class="fa fa-question-circle pull-right"></i> Open introduction
									</a>
									<a title="hideMenu" ng-href="{{config.HelpVideoURL}}" target="_blank" ng-if="config.HelpVideoURL">
										<i class="fa fa-question-circle pull-right"></i> Open introduction
									</a>
								</li>
								<li class="hidden-md hidden-lg" ng-if="rs.isDistributor()">
									<a title="hideMenu" href="<?=$LMSUri?>learner/distributorwalkthrough" target="_blank" ng-if="rs.isDistributor()">
										<i class="fa fa-question-circle pull-right"></i> Open walkthrough
									</a>
								</li>

								<!-- EOF MOBILE MENU  -->

								<li>
									<a title="hideMenu" href="" ng-click="myProfile()" onblur="menuBlur(event)">
										<i class="fa fa-user pull-right"></i> Profile
									</a>
								</li>
								<li>
									<a title="hideMenu" ng-click="changePassword()">
										<i class="fa fa-asterisk pull-right"></i> Change Password
									</a>
								</li>
								<li ng-if="config.googleTranslate">
									<a href="">
										<i class="fa fa-language pull-right"></i>
										<div id="google_translate_element"></div>
									</a>
								</li>

								<li>
									<a>
										Default Screen:<br>
										<select class="dashboard-role__dropdown" aria-label="Default Screen" ng-model="currentUser.default_screen" ng-change="setDefaultScreen()">
											<option value="eportfolio">ePortfolio</option>
											<option value="list">List</option>
											<option value="calendar">Calendar</option>
										</select>
									</a>
								</li>

								<li>
									<a>
										<label for="image">Background Image:</label>
											<input type="file"
												   class="dashboard-role__dropdown"
												   valid-file file-model="files.background_image"
												   name="image" id="image"
												   ng-click="$event.stopPropagation()"
												   ng-model="currentUser.files.background_image">
										<div class="col-sm-6 col-xs-12">

												<div>
													<a ng-if="currentUser.files.background_image" href=""
													   class="btn btn-primary btn-lrg u-margin--bottom-five"
													   ng-disabled="bgImageUpdating"
													   ng-click="uploadBackgroundImage()">
														<span ng-if="!bgImageUpdating" class="glyphicon glyphicon-cloud-upload"></span>
														<span ng-if="bgImageUpdating"
															  class="glyphicon glyphicon-refresh"></span>
														Upload
													</a>
													<a ng-if="currentUser.background_image" href=""
													   class="btn btn-danger btn-lrg"
													   confirm="Do you want to delete image?"
													   ng-disabled="bgImageDeleting"
													   ng-click="deleteBackgroundImage()">
														<span ng-if="!bgImageDeleting" class="glyphicon glyphicon-remove-circle"></span>
														<span ng-if="bgImageDeleting"
															  class="glyphicon glyphicon-refresh"></span>
														Delete Image
													</a>
												</div>

										</div>

									</a>
								</li>

								<li>
									<a>
										<label for="AccessibleUI">Accessibility Mode:</label>
                                                <input type="checkbox" ng-model="currentUser.accessible_ui"
													   id="AccessibleUI" ng-change="setAccessibleUI()"
													   ng-click="$event.stopPropagation()">
									</a>
								</li>

								<li>
									<a>
										Date Format:<br>
										<select class="dashboard-role__dropdown" aria-label="Date Format" ng-model="currentUser.default_date_format" ng-change="setDateFormat()">
											<option value="d/m/Y">DD/MM/YYYY (09/02/2024)</option>
											<option value="Y/m/d">YYYY/MM/DD (2024/02/09)</option>
											<option value="m/d/Y">MM/DD/YYYY (02/09/2024)</option>
											<option value="d.m.Y">DD.MM.YYYY (09.02.2024)</option>
											<option value="d-m-Y">DD-MM-YYYY (09-02-2024)</option>
											<option value="Y-m-d">YYYY-MM-DD (2024-02-09)</option>
										</select>
									</a>
								</li>


								<li>
									<a>
										Color scheme:<br>
										<select class="dashboard-role__dropdown" aria-label="Color scheme" ng-model="currentUser.color_scheme" ng-change="setColorScheme()">
											<option value="light">Light</option>
											<option value="dark">Dark</option>
										</select>
									</a>
								</li>

								<li ng-if="currentUser && shadow_roles.length > 1" ng-controller="ShadowRole">
									<a>
										Current role:<br>
										<select class="dashboard-role__dropdown" aria-label="Current role" ng-model="shadow.role" ng-options="role as role.name for role in shadow_roles | orderBy : 'name'" ng-change="shadow.set()">
										</select>
									</a>
								</li>

								<li ng-controller="ShadowRole" ng-show="linkedAccounts.length > 1 && showAccounts" ng-init="getLinkedAccounts()">
									<a>
										Switch Account:{{rs.user.role}}<br>
										<select class="dashboard-role__dropdown" aria-label="Switch Account" ng-model="selectedAccount" ng-options="(selectedAccount.account_type ? selectedAccount.account_type.value : selectedAccount.username) for selectedAccount in linkedAccounts track by selectedAccount.id " ng-change="us.impersonate(selectedAccount, true)">
										</select>
									</a>
								</li>

								<li>
									<a title="hideMenu" href="javascript:;" ng-click="logout()">
										<i class="fa fa-sign-out pull-right"></i> Log Out
									</a>
								</li>
								<li>
									<a href="" class="u-text--right u-font-family--courier" onblur="menuBlur(event)">
										<small>
											{{config.release}}
										</small>
									</a>
								</li>
							</ul>
						</span>
						<!-- MOBILE MENU BACKDROP -->
						<div uib-modal-backdrop="modal-backdrop" ng-class="{'modal-backdrop': visibleDropDown}" class="hidden-md hidden-lg" uib-modal-animation-class="none" modal-in-class="in" modal-animation="false" aria-hidden="true" style="z-index: auto !important;" ng-click="visibleDropDown = !visibleDropDown"></div>
						<!-- EOF MOBILE MENU BACKDROP -->
					</div>
					<div id="docsbot-icon" uib-tooltip="<?=$docs_bot_tooltip?>" tooltip-placement="left" ng-click="toggleDocsBot()" ng-if="config.AIPoweredHelpAssistant">
						<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 25" stroke-width="1" stroke="currentColor" class="h-8 w-8 text-cyan-100 sm:h-10 sm:w-10" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" d="M 12.017883 3.035019 C 12.680645 3.035019 13.216095 3.536776 13.216095 4.157831 L 13.216095 6.403454 L 17.709389 6.403454 C 19.199665 6.403454 20.405365 7.533283 20.405365 8.929779 L 20.405365 18.473675 C 20.405365 19.870171 19.199665 21 17.709389 21 L 6.326378 21 C 4.836102 21 3.630402 19.870171 3.630402 18.473675 L 3.630402 8.929779 C 3.630402 7.533283 4.836102 6.403454 6.326378 6.403454 L 10.819673 6.403454 L 10.819673 4.157831 C 10.819673 3.536776 11.355123 3.035019 12.017883 3.035019 Z M 7.824143 16.508755 C 7.494635 16.508755 7.225037 16.761387 7.225037 17.07016 C 7.225037 17.378933 7.494635 17.631565 7.824143 17.631565 L 9.022355 17.631565 C 9.351863 17.631565 9.621461 17.378933 9.621461 17.07016 C 9.621461 16.761387 9.351863 16.508755 9.022355 16.508755 L 7.824143 16.508755 Z M 11.418778 16.508755 C 11.089271 16.508755 10.819673 16.761387 10.819673 17.07016 C 10.819673 17.378933 11.089271 17.631565 11.418778 17.631565 L 12.616989 17.631565 C 12.946497 17.631565 13.216095 17.378933 13.216095 17.07016 C 13.216095 16.761387 12.946497 16.508755 12.616989 16.508755 L 11.418778 16.508755 Z M 15.013412 16.508755 C 14.683905 16.508755 14.414307 16.761387 14.414307 17.07016 C 14.414307 17.378933 14.683905 17.631565 15.013412 17.631565 L 16.211624 17.631565 C 16.541132 17.631565 16.81073 17.378933 16.81073 17.07016 C 16.81073 16.761387 16.541132 16.508755 16.211624 16.508755 L 15.013412 16.508755 Z M 9.921014 12.017509 C 9.921014 11.242371 9.250442 10.613996 8.423249 10.613996 C 7.596056 10.613996 6.925484 11.242371 6.925484 12.017509 C 6.925484 12.792649 7.596056 13.421023 8.423249 13.421023 C 9.250442 13.421023 9.921014 12.792649 9.921014 12.017509 Z M 15.612518 13.421023 C 16.439711 13.421023 17.110283 12.792649 17.110283 12.017509 C 17.110283 11.242371 16.439711 10.613996 15.612518 10.613996 C 14.785326 10.613996 14.114754 11.242371 14.114754 12.017509 C 14.114754 12.792649 14.785326 13.421023 15.612518 13.421023 Z M 1.833085 10.894698 L 2.432191 10.894698 L 2.432191 17.631565 L 1.833085 17.631565 C 0.840816 17.631565 0.035768 16.877178 0.035768 15.94735 L 0.035768 12.578916 C 0.035768 11.649087 0.840816 10.894698 1.833085 10.894698 Z M 22.202682 10.894698 C 23.194952 10.894698 24 11.649087 24 12.578916 L 24 15.94735 C 24 16.877178 23.194952 17.631565 22.202682 17.631565 L 21.603577 17.631565 L 21.603577 10.894698 L 22.202682 10.894698 Z"></path></svg>
					</div>
				</div>
			</div>

			<!-- MOBILE SEARCH ON SECOND LINE -->
			<!-- NOTE: isMobileRequest is to hide search box in resource details page when opening in iframe from mobile application  -->
			<div class="u-flexbox hidden-md hidden-lg mobile-search-line" ng-if="rs.interface('learner')" ng-show="!isMobileRequest">
				<form ng-controller="learnerSearchBar" class="u-flex--no-grow lms-header__search" ng-submit="submitSearch()" ng-if="!isSMCR || !dashboard.pkf">
					<div class="input-group">
						<div class="input-group-btn">
							<div class="btn-group" uib-dropdown is-open="status.isopen">
								<button id="single-button2" type="button" class="btn btn-default" uib-dropdown-toggle ng-disabled="disabled" aria-label="Select a category to search for">
									{{selectedType.name || 'All'}}
									<span class="caret"></span>
								</button>
								<ul class="dropdown-menu" uib-dropdown-menu role="menu" aria-labelledby="single-button2">
									<li role="menuitem" ng-click="updateType(false)"><a href="#">All</a></li>
									<li role="menuitem" ng-click="updateType(type)" ng-repeat="type in types | orderBy : 'name'">
										<a href="#">{{type.name}}</a>
									</li>
								</ul>
							</div>
						</div><!-- /btn-group -->
						<input type="text" class="form-control" id="resource_search_term_mob" name="resource_search_term" ng-model="resourceSearch" placeholder="Search here!" aria-label="Search here!" ng-change="updateResourceSearch()" ng-model-options="{ debounce: 500 }" />
						<span class="input-group-btn">
							<button class="btn btn-warning" type="submit" aria-label="Submit Search" title="Submit Search">
								<span class="fa fa-search"></span>
							</button>
						</span>
					</div>
				</form>
			</div>
			<!-- EOF MOBILE SEARCH ON SECOND LINE -->


			<!-- EOF Learner header -->

			<!-- Body ciontaining side-menu and content -->
			<!-- CHANGE TO BIG ICONS on home-page, then use tabs/subtabs to seperate page types -->
			<div class="lms-body u-flexbox u-flexbox--no-ie u-flex--grow" ng-if="currentUser">
				<!-- Content -->
				<div class="lms-content u-max-width u-flexbox u-flexbox--no-ie u-flex--grow" ng-class="{'lms-content--learner' : !rs.interface('admin'), 'brand-<?=$licensing['brand']?>' : <?=$licensing['brand'] ? 'true' : 'false' ?> && currentUrl == '/managamentchoice'}">
					<div ng-view class="u-flexbox u-max-width u-flexbox--no-ie u-flexbox--direction-column u-flex--grow"></div>
				</div>
			</div>
		</main>

		<aside id="docsbot-widget" class="resizer" ng-show="isDocsBotVisible">
			<div id="docsbot-widget-embed" class="resizer">
			</div>
			<div class="close-button" ng-click="toggleDocsBot()">
				<span class="glyphicon glyphicon-remove"></span>
			</div>
		</aside>

		<div id="sidebar-resizer"
			resizer="vertical"
			resizer-width="6"
			resizer-right=".resizer"
			resizer-max="800"
			resizer-min="350"
			resizer-close=".close-button"
			ng-show="isDocsBotVisible"
		>
		</div>

		<script src="<?=$LMSUri?>js/isteven-multi-select.js"></script>
		<script src="<?=$LMSUri?>js/angular-route.min.js"></script>
		<script src="<?=$LMSUri?>js/angular-cookies.min.js"></script>
		<script src="<?=$LMSUri?>js/angular-smooth-scroll.min.js"></script>
		<script src="<?=$LMSUri?>js/angular-filter.min.js"></script>
		<script src="<?=$LMSUri?>js/angular-rzslider.min.js"></script>

		<script src="<?=$LMSUri?>js/ng-file-upload.min.js"></script>
		<script src="<?=$LMSUri?>js/ng-file-upload-shim.min.js"></script>

		<script src="<?=$LMSUri?>js/ui-bootstrap-tpls-2.5.0.min.js"></script>
		<script src="<?=$LMSUri?>js/lodash.min.js"></script>
		<script src="<?=$LMSUri?>js/autocomplete.min.js"></script>
		<script src="<?=$LMSUri?>js/smart-table.min.js"></script>
		<script src="<?=$LMSUri?>js/angular-confirm.min.js"></script>
		<script src="<?=$LMSUri?>js/angular-confirm-new.min.js"></script>
		<script src="<?=$LMSUri?>js/showErrors.min.js"></script>
		<script src="<?=$LMSUri?>js/chart.min.js"></script>
		<script src="<?=$LMSUri?>js/angular-chart.min.js"></script>
		<script src="<?=$LMSUri?>js/chart.piecelabel.min.js"></script>
		<script src="<?=$LMSUri?>js/moment.min.js"></script>
		<script src="<?=$LMSUri?>js/timeago.min.js"></script>
		<script src="<?=$LMSUri?>js/ng-hierarchical-selector.0.4.3.min.js"></script>
		<script src="<?=$LMSUri?>js/angularjs-gauge.min.js"></script>

		<script src="<?=$LMSUri?>js/angular-bootstrap-calendar-tpls.js"></script>
		<script src="<?=$LMSUri?>js/angular-sortable-bulk.min.js"></script>

		<script src="<?=$LMSUri?>js/angular-animate.min.js"></script>
		<script src="<?=$LMSUri?>js/angular-sanitize.min.js"></script>

		<script src="<?=$LMSUri?>js/tinymce/tinymce.min.js"></script>
		<script src="<?=$LMSUri?>js/tinymce/tinymce-bind.js"></script>
		<script src="<?=$LMSUri?>js/ckeditor.js"></script>

		<script src="<?=$LMSUri?>js/clipboard.min.js"></script>
		<script src="<?=$LMSUri?>js/ngclipboard.min.js"></script>

		<script src="<?=$LMSUri?>js/angular-toggle-switch.min.js"></script>
		<?php
        $stripeConfig = \APP\Tools::getConfig('enableStripePayment');
        if($stripeConfig){?>
		<script src="https://js.stripe.com/v3/"></script>
		<?php }
        ?>

		<script src="<?=$LMSTplsUri?>js/app.js?v=<?=$version?>"></script>
		<script src="<?=$LMSTplsUri?>js/routes.js?v=<?=$version?>"></script>
		<script src="<?=$LMSTplsUri?>js/tools.js?v=<?=$version?>"></script>

		<script src="<?=$LMSTplsUriCombined?>js/filters.min.js?v=<?=$version?>"></script>
		<script src="<?=$LMSTplsUriCombined?>js/controllers.min.js?v=<?=$version?>"></script>
		<script src="<?=$LMSTplsUriCombined?>js/factories.min.js?v=<?=$version?>"></script>
		<script src="<?=$LMSTplsUriCombined?>js/directives.min.js?v=<?=$version?>"></script>
		<script src="<?=$LMSTplsUriCombined?>js/services.min.js?v=<?=$version?>"></script>


		<script src='<?=$LMSUri?>client/client.js'></script>

		<script src="<?=$LMSTplsUriCombined?>js/ng-google-chart.js?v=<?=$version?>" type="text/javascript"></script>

		<?=$olarkCode?>

		<script type="text/javascript" ng-if="config.AIPoweredHelpAssistant">window.DocsBotAI=window.DocsBotAI||{},DocsBotAI.init=function(c){return new Promise(function(e,o){var t=document.createElement("script");t.type="text/javascript",t.async=!0,t.src="https://widget.docsbot.ai/chat.js";var n=document.getElementsByTagName("script")[0];n.parentNode.insertBefore(t,n),t.addEventListener("load",function(){window.DocsBotAI.mount({id:c.id,supportCallback:c.supportCallback,identify:c.identify,options:c.options,signature:c.signature});var t;t=function(n){return new Promise(function(e){if(document.querySelector(n))return e(document.querySelector(n));var o=new MutationObserver(function(t){document.querySelector(n)&&(e(document.querySelector(n)),o.disconnect())});o.observe(document.body,{childList:!0,subtree:!0})})},t&&t("#docsbotai-root").then(e).catch(o)}),t.addEventListener("error",function(t){o(t.message)})})};</script>

		<script>
			document.addEventListener('DOMContentLoaded', function() {
				if (document.documentElement.classList.contains('accessible')) {
					document.documentElement.classList.add('has-accessible');
				}
			});
		</script>
	</body>
</html>
