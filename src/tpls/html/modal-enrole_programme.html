<div class="modal-header">
    <h3 class="modal-title pull-left">
        {{passData.standard.name}}
    </h3>
    <div class="pull-right">
        <button ng-if="!passData.standard.position || passData.standard.position == 'not enrolled'"
                class="btn btn-primary" type="submit" ng-disabled="dataLoading"
                ng-click="save(passData.standard)" confirm="'Do you want to Enrol?'}}">
            Enrol
        </button>
        <button ng-if="(!passData.standard.position || passData.standard.position == 'not enrolled') && isMultiSelectShoppingBasket" 
                class="btn btn-primary" 
                type="submit" 
                ng-disabled="dataLoading"
                ng-click="addToBasket(passData.standard)">
            Add to Basket
        </button>
        <button class="btn btn-danger c-btn-cancel" type="button" ng-click="closeModal()">Close</button>
    </div>
</div>

<div class="modal-body" ng-if="!passData.standard.position || passData.standard.position == 'not enrolled'">
    <div uib-alert ng-repeat="alert in alerts" type="{{alert.type}}" ng-class="'alert-' + (alert.type || 'warning')"
         close="closeAlert($index)">{{alert.msg}}
    </div>
    <p ng-if="passData.standard.description">
        <span class="u-white--pre-wrap"><strong>Description</strong>: {{passData.standard.description}}</span>
    </p>
    <p ng-if="passData.standard.discounted_cost">
        <strong>%%cost%%</strong>: {{config.currencySymbol}}{{passData.standard.discounted_cost}}
    </p>
    <p ng-if="passData.standard.completion_months">
        <strong>Duration</strong>: {{passData.standard.completion_months}} Months
    </p>

    <div ng-if="passData.standard.coupon_exists && passData.standard.discounted_cost > 0">
        <div>
            <label>
                Have Coupon ? <input type="checkbox" class="form-group-lg" ng-model="couponOpt.enabled" id="use_coupon">
            </label>
        </div>
        <div ng-if="couponOpt.enabled">
            <label>
                Coupon code <input type="text" ng-model="couponOpt.code" id="coupon_code"
                                   placeholder="Enter Code here...">
                <button ng-click="applyCoupon()" class="u-pointer">Apply</button>
            </label>
            <span ng-if="dataLoading">
                <span class="glyphicon glyphicon-refresh u-spinner"></span>
            </span>

            <div ng-if="discounts.coupon_discount"><b>Coupon Applied: </b> -{{discounts.coupon_discount}}% = €{{discounts.cost_after_coupon_apply}}

                <div ng-if="discounts.user_discount || discounts.company_discount || discounts.department_discount"><b>User + %%company%% + %%department%% discount percentage any:</b>
                    {{discounts.user_discount + "% + " + discounts.company_discount + "% + " + discounts.department_discount
                    }}% =
                    {{discounts.user_discount + discounts.company_discount + discounts.department_discount }}%
                </div>
                <div ng-if="discounts.coupon_discount"><b>Total payable amount after discounts: </b> €{{discounts.total}}
                </div>
            </div>
        </div>

</div>

<div class="modal-footer">
    <button ng-if="!passData.standard.position || passData.standard.position == 'not enrolled'" class="btn btn-primary"
            type="submit" ng-disabled="dataLoading"
            ng-click="save(passData.standard)"
            confirm="Do you want to Enrol?">
        Enrol
    </button>
    <button ng-if="(!passData.standard.position || passData.standard.position == 'not enrolled') && isMultiSelectShoppingBasket" 
            class="btn btn-primary" 
            type="submit" 
            ng-disabled="dataLoading"
            ng-click="addToBasket(passData.standard)">
        Add to Basket
    </button>
    <button class="btn btn-danger c-btn-cancel" type="button" ng-click="closeModal()">Close</button>
</div>
