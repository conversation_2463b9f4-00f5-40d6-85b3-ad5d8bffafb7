<?php

declare(strict_types=1);

use Slim\App;

return function (App $app) use ($settings) {
	// Define route files in order (developers can easily modify this list)
	$routeFiles = [
		'app.php',
		'tpl.php',
		'menu.php',
		'department.php',
		'designation.php',
		'company.php',
		'user.php',
		'country.php',
		'city.php',
		'location.php',
		'group.php',
		'manager.php',
		'competency.php',
		'learning.php',
		'learningcategory.php',
		'learningprovider.php',
		'learningusers.php',
		'usercustomreview.php',
		'mylearning.php',
		'myassessment.php',
		'myenroll.php',
		'booking.php',
		'refreshlearning.php',
		'myprofile.php',
		'changepassword.php',
		'role.php',
		'emails.php',
		'label.php',
		'default-label.php',
		'timings.php',
		'configuration.php',
		'learningreports.php',
		'apprentixapprenticereports.php',
		'apprentixassesorreports.php',
		'fallingbehindapprentixassessorreports.php',
		'fallingbehindapprentixreports.php',
		'taskassessmentreports.php',
		'trainingimpactreports.php',
		'trainingimpactapprentixapprentice.php',
		'trainingimpactapprentixassessorcoach.php',
		'modulefeedback.php',
		'eventpopularityreports.php',
		'learningresourcepopularityreports.php',
		'formprogressreports.php',
		'trainingschedulereports.php',
		'tincan.php',
		'jackdaw.php',
		'creator.php',
		'learningdistribution.php',
		'calendar.php',
		'learner.php',
		'apprenticeshipstandards.php',
		'apprenticeshipissuecategories.php',
		'apprenticeshipissues.php',
		'apprenticeship-route.php',
		'outcome-group.php',
		'costanalysis.php',
		'logauthentications.php',
		'invoice.php',
		'print.php',
		'export.php',
		'customreview.php',
		'backend.php',
		'api-setup.php',
		'mobile.php',
		'smcr-staff-type.php',
		'smcr-staff-function-responsibility.php',
		'smcr-function-responsibility.php',
		'smcr-committee.php',
		'smcr-committee-role.php',
		'smcr-committee-role-person.php',
		'smcr-users.php',
		'smcr-reports.php',
		'smcr-f-p-category.php',
		'evidence-type.php',
		'dashboards.php',
		'powerbi-dashboards.php',
		'powerbi-reports.php',
		'powerbi-datasets.php',
		'powerbi-course-sets.php',
		'qa.php',
		'favorite.php',
		'comment.php',
		'file.php',
		'meeting.php',
		'learners-interface.php',
		'skill-scan.php',
		'survey.php',
		'quizanalysis.php',
		'ilr.php',
		'cron.php',
		'batch-report.php',
		'batch-report-data.php',
		'gateway-readiness.php',
		'db.php',
		'schedule.php',
		'event-type.php',
		'event-visit-type.php',
		'powerbi.php',
		'lti.php',
		'zoom.php',
		'teams.php',
		'holiday.php',
		'turnitin.php',
		'anders-pink.php',
		'api-badgr.php',
		'venue.php',
		'picklist.php',
		'table-extension-field.php',
		'register.php',
		'forum.php',
		'h5p.php',
		'resource-query-builder.php',
		'country-group.php',
		'qa-control.php',
		'jamboard.php',
		'custom-programme-status.php',
		'custom-learning-resources-types.php',
		'form-types.php',
		'custom-query.php',
		'user-programme-status.php',
		'learning-result.php',
		'resource-assignment.php',
		'payment-gateway.php',
		'table-history.php',
		'custom-field.php',
		'document-template.php',
		'form-workflow.php',
		'ai-creator.php',
		'email-logs.php',
		'email-queue.php',
		'form-logs.php',
		'custom-report.php',
		'graph.php',
		'coupon.php',
		'career-path.php',
		'assignment.php',
		'failed-authentication.php',
		'log.php',
		'disk-space.php',
		'credly.php',
		'rejection-reasons.php',
		'target-catalogue.php',
		'delivery-provider-type.php',
		'group-department-code.php',
		'watch.php',
		'import.php',
		'stripe-licenses.php',
		'pay360.php',
		'mapping-to-programme.php',
		'user-download.php',
		'signposting.php',
		'grading.php',
		'mobileverify.php',
		'mobile-v2.php',
		'payment-transaction.php',
		'mobile-course-purchase.php',
		'chat-bot.php',
		'course-basket.php'
	];
	
	// Use RouteCache for improved performance
	// In production: caches file list above for faster loading
	// In development: loads files directly for easier debugging
	\APP\RouteCache::load($app, $settings, $routeFiles);
};
