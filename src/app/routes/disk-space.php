<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/disk-space", function ($group) {

    $group->get("/stats", function (Request $request, Response $response, array $args) {
        $stats = [];

        // Get the base paths from configuration
        $privatePath = $this->get('settings')['LMSPrivatePath'];
        $publicPath = $this->get('settings')['LMSPublicPath'];
        $tempPath = $this->get('settings')['LMSTempPath'];

        // Calculate disk space for different directories (client-relevant storage)
        $directories = [
            // Major storage consumers (GB/TB level)
            'evidence' => $privatePath . 'evidence/',
            'form_files' => $privatePath . 'form_files/',
            'form_logs' => $privatePath . 'form_logs/',
            'files' => $privatePath . 'files/',
            'scorm_data' => $publicPath . 'scormdata/',

            // Medium storage consumers (MB/GB level)
            'temp_files' => $tempPath,
            'batch_report_data' => $privatePath . 'batch_report_data/',
            'logs' => $this->get('settings')['AppFilePath'] . 'logs/',
        ];

        $totalSize = 0;
        $stats['directories'] = [];

        foreach ($directories as $name => $path) {
            if (is_dir($path)) {
                $size = \APP\Tools::getDirectorySize($path);
                $stats['directories'][] = [
                    'name' => ucwords(str_replace('_', ' ', $name)),
                    // 'path' => $path,
                    'size' => $size,
                    'size_formatted' => \APP\Tools::formatBytes($size),
                    'file_count' => \APP\Tools::getFileCount($path)
                ];
                $totalSize += $size;
            }
        }

        // Get database statistics for file references
        $stats['database'] = [];

        try {
            // Learning Module Evidence files (all records as files exist regardless of status)
            $evidenceCount = DB::table('learning_module_evidences')
                ->whereNotNull('file_size') // Only files with size data
                ->where('file_size', '>', 0) // Only files with actual size
                ->count();

            $evidenceTotalSize = DB::table('learning_module_evidences')
                ->sum('file_size');

            $stats['database'][] = [
                'name' => 'Learning Module Evidence Files',
                'count' => $evidenceCount,
                'total_size' => $evidenceTotalSize,
                'total_size_formatted' => \APP\Tools::formatBytes($evidenceTotalSize ?: 0)
            ];
        } catch (\Exception $e) {
            $stats['database'][] = [
                'name' => 'Learning Module Evidence Files',
                'count' => 'N/A (table not found)',
                'total_size' => 0,
                'total_size_formatted' => 'N/A'
            ];
        }

        // Total statistics (client storage only)
        $stats['total'] = [
            'size' => $totalSize,
            'size_formatted' => \APP\Tools::formatBytes($totalSize)
        ];

        $response->getBody()->write(json_encode($stats));
        return $response->withHeader('Content-Type', 'application/json');
    })->add(\APP\Auth::getStructureAccessCheck(['system-setup-audit-disk-space'], 'select'));

    $group->post('/list', function (Request $request, Response $response, array $args) {
        $params = $request->getParsedBody();

        // Remove refresh parameter before passing to SmartTable
        if (isset($params["search"]["refresh"])) {
            unset($params["search"]["refresh"]);
        }

        // For now, return empty results if tables don't exist
        // This can be enhanced later when the database schema is available
        try {
            // Get detailed file information from learning_module_evidences table (all records)
            $query = \Models\LearningModuleEvidence
                ::select([
                    'learning_module_evidences.id',
                    'learning_module_evidences.evidence as file_name',
                    'learning_module_evidences.file_size',
                    'learning_module_evidences.evidence_type as file_type',
                    'learning_module_evidences.created_at',
                    'users.username',
                    'users.id as user_id',
                    DB::raw("CONCAT(users.fname, ' ', users.lname) as uploaded_by"),
                    'learning_modules.name as module_name',
                    DB::raw("'Learning Module Evidence' as file_category"),
                    DB::raw("CASE WHEN learning_module_evidences.evidence_type = 'file' THEN
                        (CASE WHEN learning_module_evidences.evidence IS NOT NULL AND learning_module_evidences.evidence != '' THEN 'Has DB Record' ELSE 'No DB Record' END)
                        ELSE 'Not a file' END as orphan_status")
                ])
                ->leftJoin('users', 'learning_module_evidences.user_id', '=', 'users.id')
                ->leftJoin('learning_modules', 'learning_module_evidences.learning_modules_id', '=', 'learning_modules.id')
                ->whereNotNull('learning_module_evidences.file_size') // Only files with size data
                ->where('learning_module_evidences.file_size', '>', 0); // Only files with actual size

            $p = \APP\SmartTable::searchPaginate($params, $query);

            $response->getBody()->write($p->toJson());
            return $response->withHeader('Content-Type', 'application/json');

        } catch (\Exception $e) {
            // Table doesn't exist, return empty result
            $emptyResult = (object) [
                'data' => [],
                'total' => 0,
                'numberOfPages' => 0
            ];

            $response->getBody()->write(json_encode($emptyResult));
            return $response->withHeader('Content-Type', 'application/json');
        }
    })->add(\APP\Auth::getStructureAccessCheck(['system-setup-audit-disk-space'], 'select'));

    // New endpoint to get orphaned files (all files returned for client-side sorting)
    $group->get('/orphaned-files', function (Request $request, Response $response, array $args) {
        $privatePath = $this->get('settings')['LMSPrivatePath'];
        $evidencePath = $privatePath . 'evidence/';

        $orphanedFiles = [];
        $totalOrphanedSize = 0;

        try {
            // Get all file references from database
            // Include both hash.extension format and just hash
            $dbFilesWithExt = DB::table('learning_module_evidences')
                ->where('evidence_type', 'file')
                ->whereNotNull('hash')
                ->whereNotNull('extension')
                ->where('hash', '!=', '')
                ->where('extension', '!=', '')
                ->select(DB::raw("CONCAT(hash, '.', extension) as filename"))
                ->pluck('filename')
                ->toArray();

            // Also get just the hashes for files that might not have extensions
            $dbHashes = DB::table('learning_module_evidences')
                ->where('evidence_type', 'file')
                ->whereNotNull('hash')
                ->where('hash', '!=', '')
                ->pluck('hash')
                ->toArray();

            // Combine both arrays
            $dbFiles = array_merge($dbFilesWithExt, $dbHashes);

            // Convert to hash map for faster lookup
            $dbFileMap = array_flip($dbFiles);

            // Scan evidence directory
            if (is_dir($evidencePath)) {
                $iterator = new \RecursiveIteratorIterator(
                    new \RecursiveDirectoryIterator($evidencePath, \RecursiveDirectoryIterator::SKIP_DOTS),
                    \RecursiveIteratorIterator::LEAVES_ONLY
                );

                foreach ($iterator as $file) {
                    if ($file->isFile()) {
                        $relativePath = str_replace($evidencePath, '', $file->getPathname());
                        $fileName = basename($relativePath);

                        // Skip .gitignore files and other system files
                        if ($fileName === '.gitignore' || $fileName === '.DS_Store' || $fileName === 'Thumbs.db') {
                            continue;
                        }

                        // Check if this file exists in database
                        if (!isset($dbFileMap[$fileName])) {
                            $fileSize = $file->getSize();
                            $orphanedFiles[] = [
                                'file_name' => $fileName,
                                'file_size' => $fileSize,
                                'file_size_formatted' => \APP\Tools::formatBytes($fileSize),
                                'modified_date' => date('Y-m-d H:i:s', $file->getMTime()),
                                'file_type' => pathinfo($fileName, PATHINFO_EXTENSION)
                            ];
                            $totalOrphanedSize += $fileSize;
                        }
                    }
                }
            }

            $result = [
                'orphaned_files' => $orphanedFiles,
                'total_orphaned_count' => count($orphanedFiles),
                'total_orphaned_size' => $totalOrphanedSize,
                'total_orphaned_size_formatted' => \APP\Tools::formatBytes($totalOrphanedSize)
            ];

            $response->getBody()->write(json_encode($result));
            return $response->withHeader('Content-Type', 'application/json');

        } catch (\Exception $e) {
            $errorResult = [
                'error' => 'Failed to scan for orphaned files: ' . $e->getMessage(),
                'orphaned_files' => [],
                'total_orphaned_count' => 0,
                'total_orphaned_size' => 0,
                'total_orphaned_size_formatted' => '0 B'
            ];

            $response->getBody()->write(json_encode($errorResult));
            return $response->withHeader('Content-Type', 'application/json');
        }
    })->add(\APP\Auth::getStructureAccessCheck(['system-setup-audit-disk-space'], 'select'));

    // Delete orphaned file endpoint (by filename)
    $group->delete('/orphaned-files/{fileName:.+}', function (Request $request, Response $response, array $args) {
        $privatePath = $this->get('settings')['LMSPrivatePath'];
        $evidencePath = $privatePath . 'evidence/';
        $fileName = urldecode($args['fileName']);

        try {
            // Log for debugging
            error_log("Attempting to delete file: " . $fileName);
            error_log("Evidence path: " . $evidencePath);

            // Parse filename to extract hash and extension
            $fileParts = pathinfo($fileName);
            $extension = $fileParts['extension'] ?? '';
            $hash = $fileParts['filename'] ?? '';

            // Check if evidence directory exists
            if (!is_dir($evidencePath)) {
                error_log("Evidence directory does not exist: " . $evidencePath);
                $response->getBody()->write(json_encode(['error' => 'Evidence directory not found']));
                return $response
                    ->withStatus(404)
                    ->withHeader('Content-Type', 'application/json');
            }

            // Skip system files
            if ($fileName === '.gitignore' || $fileName === '.DS_Store' || $fileName === 'Thumbs.db') {
                $response->getBody()->write(json_encode(['error' => 'Cannot delete system files']));
                return $response
                    ->withStatus(400)
                    ->withHeader('Content-Type', 'application/json');
            }

            // Directly check if the specific file exists
            $fileToDelete = $evidencePath . $fileName;

            if (!is_file($fileToDelete)) {
                error_log("File not found: " . $fileToDelete);
                $response->getBody()->write(json_encode(['error' => 'File not found']));
                return $response
                    ->withStatus(404)
                    ->withHeader('Content-Type', 'application/json');
            }

            // Check if file is orphaned by doing a direct database query for this specific file
            $dbFile = DB::table('learning_module_evidences')
                ->where('evidence_type', 'file')
                ->where('hash', $hash)
                ->where('extension', $extension)
                ->first();

            if ($dbFile) {
                error_log("File is not orphaned - has database record: " . $fileName);
                $response->getBody()->write(json_encode(['error' => 'File is not orphaned - has database record']));
                return $response
                    ->withStatus(400)
                    ->withHeader('Content-Type', 'application/json');
            }

            error_log("Found orphaned file to delete: " . $fileToDelete);

            // Security check - ensure file path is within evidence directory
            $realPath = realpath($fileToDelete);
            $realEvidencePath = realpath($evidencePath);

            if (!$realPath || !$realEvidencePath || strpos($realPath, $realEvidencePath) !== 0) {
                error_log("Security check failed - invalid file path: " . $fileToDelete);
                $response->getBody()->write(json_encode(['error' => 'Invalid file path']));
                return $response
                    ->withStatus(400)
                    ->withHeader('Content-Type', 'application/json');
            }

            // Delete the file
            if (unlink($fileToDelete)) {
                error_log("Successfully deleted file: " . $fileToDelete);
                $response->getBody()->write(json_encode(['success' => true, 'message' => 'File deleted successfully']));
                return $response->withHeader('Content-Type', 'application/json');
            } else {
                error_log("Failed to unlink file: " . $fileToDelete);
                $response->getBody()->write(json_encode(['error' => 'Failed to delete file - unlink failed']));
                return $response
                    ->withStatus(500)
                    ->withHeader('Content-Type', 'application/json');
            }

        } catch (\Exception $e) {
            error_log("Exception in file deletion: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());
            $response->getBody()->write(json_encode(['error' => 'Failed to delete file: ' . $e->getMessage()]));
            return $response
                ->withStatus(500)
                ->withHeader('Content-Type', 'application/json');
        }
    })->add(\APP\Auth::getStructureAccessCheck(['system-setup-audit-disk-space'], 'disable'));

    // View orphaned file endpoint (secure proxy)
    $group->get('/orphaned-files/view/{fileName:.+}', function (Request $request, Response $response, array $args) {

        try {
            $fileName = urldecode($args['fileName']);

            // Check if settings exist
            if (!isset($this->get('settings')['LMSPrivatePath'])) {
                $response->getBody()->write(json_encode(['error' => 'Configuration error: LMSPrivatePath not set']));
                return $response
                    ->withStatus(500)
                    ->withHeader('Content-Type', 'application/json');
            }

            $privatePath = $this->get('settings')['LMSPrivatePath'];
            $evidencePath = $privatePath . 'evidence/';

            // Check if evidence directory exists
            if (!is_dir($evidencePath)) {
                $response->getBody()->write(json_encode(['error' => 'Evidence directory not found']));
                return $response
                    ->withStatus(404)
                    ->withHeader('Content-Type', 'application/json');
            }

            // Parse filename to extract hash and extension
            $fileParts = pathinfo($fileName);
            $extension = $fileParts['extension'] ?? '';
            $hash = $fileParts['filename'] ?? '';

            // Skip system files
            if ($fileName === '.gitignore' || $fileName === '.DS_Store' || $fileName === 'Thumbs.db') {
                $response->getBody()->write(json_encode(['error' => 'Cannot view system files']));
                return $response
                    ->withStatus(400)
                    ->withHeader('Content-Type', 'application/json');
            }

            // Directly check if the specific file exists
            $fileToView = $evidencePath . $fileName;

            if (!is_file($fileToView)) {
                $response->getBody()->write(json_encode(['error' => 'File not found']));
                return $response
                    ->withStatus(404)
                    ->withHeader('Content-Type', 'application/json');
            }

            // Check if file is orphaned by doing a direct database query for this specific file
            $dbFile = DB::table('learning_module_evidences')
                ->where('evidence_type', 'file')
                ->where('hash', $hash)
                ->where('extension', $extension)
                ->first();

            if ($dbFile) {
                $response->getBody()->write(json_encode(['error' => 'File is not orphaned - has database record']));
                return $response
                    ->withStatus(400)
                    ->withHeader('Content-Type', 'application/json');
            }


            // Security check - ensure file path is within evidence directory
            $realPath = realpath($fileToView);
            $realEvidencePath = realpath($evidencePath);

            if (!$realPath || !$realEvidencePath || strpos($realPath, $realEvidencePath) !== 0) {
                $response->getBody()->write(json_encode(['error' => 'Invalid file path']));
                return $response
                    ->withStatus(400)
                    ->withHeader('Content-Type', 'application/json');
            }

            // Check if file exists and is readable
            if (!file_exists($fileToView) || !is_readable($fileToView)) {
                $response->getBody()->write(json_encode(['error' => 'File not found or not readable']));
                return $response
                    ->withStatus(404)
                    ->withHeader('Content-Type', 'application/json');
            }

            // Determine content type based on file extension
            $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
            $contentType = 'application/octet-stream'; // Default

            switch ($extension) {
                case 'pdf':
                    $contentType = 'application/pdf';
                    break;
                case 'jpg':
                case 'jpeg':
                    $contentType = 'image/jpeg';
                    break;
                case 'png':
                    $contentType = 'image/png';
                    break;
                case 'gif':
                    $contentType = 'image/gif';
                    break;
                case 'txt':
                    $contentType = 'text/plain';
                    break;
                case 'html':
                case 'htm':
                    $contentType = 'text/html';
                    break;
                case 'css':
                    $contentType = 'text/css';
                    break;
                case 'js':
                    $contentType = 'application/javascript';
                    break;
                case 'json':
                    $contentType = 'application/json';
                    break;
                case 'xml':
                    $contentType = 'application/xml';
                    break;
                case 'doc':
                    $contentType = 'application/msword';
                    break;
                case 'docx':
                    $contentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
                    break;
                case 'xls':
                    $contentType = 'application/vnd.ms-excel';
                    break;
                case 'xlsx':
                    $contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
                    break;
                case 'mp4':
                    $contentType = 'video/mp4';
                    break;
                case 'mp3':
                    $contentType = 'audio/mpeg';
                    break;
                case 'zip':
                    $contentType = 'application/zip';
                    break;
            }

            // Get file size
            $fileSize = filesize($fileToView);

            // Read file contents into response body
            
            
            $stream = fopen($fileToView, 'rb');
            if ($stream) {
                $body = $response->getBody();
                while (!feof($stream)) {
                    $body->write(fread($stream, 8192));
                }
                fclose($stream);
            }

            // Return properly formatted response with headers
            return $response
                ->withHeader('Content-Type', $contentType)
                ->withHeader('Content-Length', (string)$fileSize)
                ->withHeader('Content-Disposition', 'inline; filename="' . addslashes($fileName) . '"')
                ->withHeader('Cache-Control', 'private, max-age=3600')
                ->withHeader('X-Content-Type-Options', 'nosniff');

        } catch (\Exception $e) {
            error_log("Exception in file viewing: " . $e->getMessage());
            $response->getBody()->write(json_encode(['error' => 'Failed to view file: ' . $e->getMessage()]));
            return $response
                ->withStatus(500)
                ->withHeader('Content-Type', 'application/json');
        }

    })->add(\APP\Auth::getStructureAccessCheck(['system-setup-audit-disk-space'], 'select'));

    // Update missing file sizes endpoint
    $group->post('/update-file-sizes', function (Request $request, Response $response, array $args) {
        $privatePath = $this->get('settings')['LMSPrivatePath'];
        $evidencePath = $privatePath . 'evidence/';

        try {
            // Get all evidence entries that are files but have no file size
            $evidenceEntries = DB::table('learning_module_evidences')
                ->where('evidence_type', 'file')
                ->where(function($query) {
                    $query->whereNull('file_size')
                          ->orWhere('file_size', 0)
                          ->orWhere('file_size', '');
                })
                ->whereNotNull('hash')
                ->where('hash', '!=', '')
                ->select(['id', 'hash', 'extension', 'evidence'])
                ->get();

            $totalEntries = count($evidenceEntries);
            $updatedCount = 0;
            $notFoundCount = 0;
            $errors = [];

            foreach ($evidenceEntries as $entry) {
                $fileFound = false;
                $fileSize = 0;
                $actualFileName = null;

                // First try exact matches
                // Try 1: hash.extension format (if extension exists)
                if (!empty($entry->extension)) {
                    $fileName = $entry->hash . '.' . $entry->extension;
                    $filePath = $evidencePath . $fileName;

                    if (is_file($filePath)) {
                        $fileFound = true;
                        $fileSize = filesize($filePath);
                        $actualFileName = $fileName;
                    }
                }

                // Try 2: just hash without extension
                if (!$fileFound) {
                    $fileName = $entry->hash;
                    $filePath = $evidencePath . $fileName;

                    if (is_file($filePath)) {
                        $fileFound = true;
                        $fileSize = filesize($filePath);
                        $actualFileName = $fileName;
                    }
                }

                // Try 3: Search for any file that starts with the hash
                if (!$fileFound && is_dir($evidencePath)) {
                    $pattern = $evidencePath . $entry->hash . '*';
                    $matches = glob($pattern);

                    if (!empty($matches)) {
                        // Use the first match
                        $filePath = $matches[0];
                        if (is_file($filePath)) {
                            $fileFound = true;
                            $fileSize = filesize($filePath);
                            $actualFileName = basename($filePath);

                            // If multiple matches found, log it
                            if (count($matches) > 1) {
                                error_log("Multiple files found for hash " . $entry->hash . ": " . implode(', ', array_map('basename', $matches)));
                            }
                        }
                    }
                }

                // Update database if file was found
                if ($fileFound) {
                    $updateData = ['file_size' => $fileSize];

                    // If the actual file has an extension but DB doesn't, update it
                    if (empty($entry->extension) && $actualFileName) {
                        $fileParts = pathinfo($actualFileName);
                        if (!empty($fileParts['extension']) && $fileParts['filename'] === $entry->hash) {
                            $updateData['extension'] = $fileParts['extension'];
                            error_log("Updated missing extension for hash " . $entry->hash . " to: " . $fileParts['extension']);
                        }
                    }

                    DB::table('learning_module_evidences')
                        ->where('id', $entry->id)
                        ->update($updateData);

                    $updatedCount++;
                } else {
                    $notFoundCount++;
                    // Log files that don't exist in any format
                    error_log("Evidence file not found in any format for hash: " . $entry->hash . " (ID: " . $entry->id . ")");
                }
            }

            $result = [
                'success' => true,
                'total_entries' => $totalEntries,
                'updated_count' => $updatedCount,
                'not_found_count' => $notFoundCount,
                'message' => "Updated file sizes for $updatedCount entries. $notFoundCount files were not found."
            ];

            $response->getBody()->write(json_encode($result));
            return $response->withHeader('Content-Type', 'application/json');

        } catch (\Exception $e) {
            error_log("Error updating file sizes: " . $e->getMessage());
            $response->getBody()->write(json_encode([
                'error' => 'Failed to update file sizes: ' . $e->getMessage()
            ]));
            return $response
                ->withStatus(500)
                ->withHeader('Content-Type', 'application/json');
        }
    })->add(\APP\Auth::getStructureAccessCheck(['system-setup-audit-disk-space'], 'update'));

    // View learning module evidence file endpoint
    $group->get('/learning-module-files/view/{fileId}', function (Request $request, Response $response, array $args) {
        try {
            $fileId = $args['fileId'];

            // Get file details from database
            $file = DB::table('learning_module_evidences')
                ->where('id', $fileId)
                ->where('evidence_type', 'file')
                ->whereNotNull('hash')
                ->first();

            if (!$file) {
                $response->getBody()->write(json_encode(['error' => 'File not found in database']));
                return $response
                    ->withStatus(404)
                    ->withHeader('Content-Type', 'application/json');
            }

            $privatePath = $this->get('settings')['LMSPrivatePath'];
            $evidencePath = $privatePath . 'evidence/';

            // Check if evidence directory exists
            if (!is_dir($evidencePath)) {
                $response->getBody()->write(json_encode(['error' => 'Evidence directory not found']));
                return $response
                    ->withStatus(404)
                    ->withHeader('Content-Type', 'application/json');
            }

            // Try to find the file using different naming patterns
            $fileFound = false;
            $fileToView = null;
            $fileName = null;

            // Try 1: hash.extension format (if extension exists)
            if (!empty($file->extension)) {
                $fileName = $file->hash . '.' . $file->extension;
                $fileToView = $evidencePath . $fileName;

                if (is_file($fileToView)) {
                    $fileFound = true;
                }
            }

            // Try 2: just hash without extension
            if (!$fileFound) {
                $fileName = $file->hash;
                $fileToView = $evidencePath . $fileName;

                if (is_file($fileToView)) {
                    $fileFound = true;
                }
            }

            // Try 3: Search for any file that starts with the hash
            if (!$fileFound) {
                $pattern = $evidencePath . $file->hash . '*';
                $matches = glob($pattern);

                if (!empty($matches)) {
                    $fileToView = $matches[0];
                    $fileName = basename($fileToView);
                    $fileFound = true;
                }
            }

            if (!$fileFound) {
                $response->getBody()->write(json_encode(['error' => 'Physical file not found on disk']));
                return $response
                    ->withStatus(404)
                    ->withHeader('Content-Type', 'application/json');
            }

            // Security check - ensure file path is within evidence directory
            $realPath = realpath($fileToView);
            $realEvidencePath = realpath($evidencePath);

            if (!$realPath || !$realEvidencePath || strpos($realPath, $realEvidencePath) !== 0) {
                $response->getBody()->write(json_encode(['error' => 'Invalid file path']));
                return $response
                    ->withStatus(400)
                    ->withHeader('Content-Type', 'application/json');
            }

            // Determine content type based on file extension
            $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
            $contentType = 'application/octet-stream'; // Default

            switch ($extension) {
                case 'pdf':
                    $contentType = 'application/pdf';
                    break;
                case 'jpg':
                case 'jpeg':
                    $contentType = 'image/jpeg';
                    break;
                case 'png':
                    $contentType = 'image/png';
                    break;
                case 'gif':
                    $contentType = 'image/gif';
                    break;
                case 'txt':
                    $contentType = 'text/plain';
                    break;
                case 'html':
                case 'htm':
                    $contentType = 'text/html';
                    break;
                case 'doc':
                    $contentType = 'application/msword';
                    break;
                case 'docx':
                    $contentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
                    break;
                case 'xls':
                    $contentType = 'application/vnd.ms-excel';
                    break;
                case 'xlsx':
                    $contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
                    break;
                case 'mp4':
                    $contentType = 'video/mp4';
                    break;
                case 'mp3':
                    $contentType = 'audio/mpeg';
                    break;
                case 'zip':
                    $contentType = 'application/zip';
                    break;
            }

            // Get file size
            $fileSize = filesize($fileToView);

            // Read file contents into response body
            $stream = fopen($fileToView, 'rb');
            if ($stream) {
                $body = $response->getBody();
                while (!feof($stream)) {
                    $body->write(fread($stream, 8192));
                }
                fclose($stream);
            }

            // Return properly formatted response with headers
            return $response
                ->withHeader('Content-Type', $contentType)
                ->withHeader('Content-Length', (string)$fileSize)
                ->withHeader('Content-Disposition', 'inline; filename="' . addslashes($fileName) . '"')
                ->withHeader('Cache-Control', 'private, max-age=3600')
                ->withHeader('X-Content-Type-Options', 'nosniff');

        } catch (\Exception $e) {
            error_log("Exception in learning module file viewing: " . $e->getMessage());
            $response->getBody()->write(json_encode(['error' => 'Failed to view file: ' . $e->getMessage()]));
            return $response
                ->withStatus(500)
                ->withHeader('Content-Type', 'application/json');
        }
    })->add(\APP\Auth::getStructureAccessCheck(['system-setup-audit-disk-space'], 'select'));

    // Delete learning module evidence file endpoint
    $group->delete('/learning-module-files/{fileId}', function (Request $request, Response $response, array $args) {
        try {
            $fileId = $args['fileId'];

            // Get file details from database
            $file = DB::table('learning_module_evidences')
                ->where('id', $fileId)
                ->where('evidence_type', 'file')
                ->whereNotNull('hash')
                ->first();

            if (!$file) {
                $response->getBody()->write(json_encode(['error' => 'File not found in database']));
                return $response
                    ->withStatus(404)
                    ->withHeader('Content-Type', 'application/json');
            }

            $privatePath = $this->get('settings')['LMSPrivatePath'];
            $evidencePath = $privatePath . 'evidence/';

            // Check if evidence directory exists
            if (!is_dir($evidencePath)) {
                $response->getBody()->write(json_encode(['error' => 'Evidence directory not found']));
                return $response
                    ->withStatus(404)
                    ->withHeader('Content-Type', 'application/json');
            }

            // Try to find the file using different naming patterns
            $fileFound = false;
            $fileToDelete = null;
            $fileName = null;

            // Try 1: hash.extension format (if extension exists)
            if (!empty($file->extension)) {
                $fileName = $file->hash . '.' . $file->extension;
                $fileToDelete = $evidencePath . $fileName;

                if (is_file($fileToDelete)) {
                    $fileFound = true;
                }
            }

            // Try 2: just hash without extension
            if (!$fileFound) {
                $fileName = $file->hash;
                $fileToDelete = $evidencePath . $fileName;

                if (is_file($fileToDelete)) {
                    $fileFound = true;
                }
            }

            // Try 3: Search for any file that starts with the hash
            if (!$fileFound) {
                $pattern = $evidencePath . $file->hash . '*';
                $matches = glob($pattern);

                if (!empty($matches)) {
                    $fileToDelete = $matches[0];
                    $fileName = basename($fileToDelete);
                    $fileFound = true;
                }
            }

            if (!$fileFound) {
                // File doesn't exist on disk, but we can still remove the database record
                error_log("Physical file not found for learning module evidence ID {$fileId}, removing database record only");
            }

            // Security check if file exists - ensure file path is within evidence directory
            if ($fileFound) {
                $realPath = realpath($fileToDelete);
                $realEvidencePath = realpath($evidencePath);

                if (!$realPath || !$realEvidencePath || strpos($realPath, $realEvidencePath) !== 0) {
                    $response->getBody()->write(json_encode(['error' => 'Invalid file path']));
                    return $response
                        ->withStatus(400)
                        ->withHeader('Content-Type', 'application/json');
                }
            }

            // Begin transaction for data integrity
            DB::beginTransaction();

            try {
                // Delete the database record first
                DB::table('learning_module_evidences')
                    ->where('id', $fileId)
                    ->delete();

                // Delete the physical file if it exists
                if ($fileFound && !unlink($fileToDelete)) {
                    // If physical deletion fails, rollback database deletion
                    DB::rollback();
                    error_log("Failed to delete physical file for learning module evidence ID {$fileId}: {$fileToDelete}");
                    $response->getBody()->write(json_encode(['error' => 'Failed to delete physical file']));
                    return $response
                        ->withStatus(500)
                        ->withHeader('Content-Type', 'application/json');
                }

                // Commit the transaction
                DB::commit();

                $message = $fileFound
                    ? 'File and database record deleted successfully'
                    : 'Database record deleted successfully (physical file was already missing)';

                error_log("Successfully deleted learning module evidence ID {$fileId}" . ($fileFound ? " and file {$fileName}" : " (no physical file)"));

                $response->getBody()->write(json_encode(['success' => true, 'message' => $message]));
                return $response->withHeader('Content-Type', 'application/json');

            } catch (\Exception $e) {
                DB::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            error_log("Exception in learning module file deletion: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());
            $response->getBody()->write(json_encode(['error' => 'Failed to delete file: ' . $e->getMessage()]));
            return $response
                ->withStatus(500)
                ->withHeader('Content-Type', 'application/json');
        }
    })->add(\APP\Auth::getStructureAccessCheck(['system-setup-audit-disk-space'], 'disable'));

});