<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/designation",  function ($group) {

	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$designation = \Models\Designation::find($args["id"]);
		$designation->status = 0;
		$designation->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-jobs', 'disable'));

	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$designation = \Models\Designation::find($args["id"]);
		$designation->status = 1;
		$designation->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-jobs', 'disable'));

	$group->get('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$designation = \Models\Designation //::find($args["id"]);
			::where('id', $args["id"])
			->with('Competencies')
            ->with('Standards')
		;
		// if SMCR, add resources!
		if ($this->get('settings')['licensing']['isSMCR']) {
			$designation = $designation->with('Modules.FPCategory');
		} else {
			$designation = $designation->with('Modules.Category');
		}

		$designation = $designation->first();

		$response->getBody()->write(json_encode($designation));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-jobs', 'select'));

	/* Add new job/designation */
	$group->post('/new', function (Request $request, Response $response) {
		$data = $request->getParsedBody();

		$designation = new \Models\Designation;
		$designation->name = $data["name"];

		if (isset($data["order_resources"])) {
			$designation->order_resources = $data["order_resources"];
		}

		$designation->status = 1;
		$designation->save();

		if (
			isset($data['competencies']) &&
			is_array($data['competencies'])
		) {
			\Models\DesignationCompetency::where('designation_id', $designation->id)->delete();
			// Assign passed access
			foreach ($data['competencies'] as $key => $competency) {
				if (isset($competency['id'])) {
					$new_designation_competency = new \Models\DesignationCompetency;
					$new_designation_competency->designation_id = $designation->id;
					$new_designation_competency->competency_id = $competency['id'];
					$new_designation_competency->save();
				}
			}
		}

		// assing/remove resources to users
		\Models\Designation::AssignCheck($designation->id, $data, $this->get('settings'));

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-jobs', 'insert'));

	/* Update job/designation */
	$group->put('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$designation = \Models\Designation::find($args["id"]);

		$designation->name = $data["name"];

		if (isset($data["order_resources"])) {
			$designation->order_resources = $data["order_resources"];
		}

		$designation->save();

		if (
			isset($data['competencies']) &&
			is_array($data['competencies'])
		) {
			\Models\DesignationCompetency::where('designation_id', $designation->id)->delete();
			// Assign passed access
			foreach ($data['competencies'] as $key => $competency) {
				if (isset($competency['id'])) {
					$new_designation_competency = new \Models\DesignationCompetency;
					$new_designation_competency->designation_id = $designation->id;
					$new_designation_competency->competency_id = $competency['id'];
					$new_designation_competency->save();
				}
			}
		}

		// assing/remove resources to users
		\Models\Designation::AssignCheck($designation->id, $data, $this->get('settings'));

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-jobs', 'update'));

	$group->get('/all', function (Request $request, Response $response) {
		session_write_close();

		$query = \Models\Designation
			::where("status",">",0)
		;

		if (
			\APP\Auth::isLearner()
		) {
			$query = $query
				->whereIn('id', [\APP\Auth::getUserDesignationId()])
			;
			$query = $query->get();
		} else {
			$query = cache()->remember('designation_all', 600, function () use ($query) {
				return $query->get()->toArray();
			});
		}


		$response->getBody()->write(gzencode(json_encode($query)));
		return $response
			->withHeader('Content-Encoding', 'gzip')
			->withHeader('Content-Type', 'application/json')
		;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-jobs', 'select'));

	$group->post('/list{download:[\/a-z]*}', function (Request $request, Response $response, array $args) {

		$params = $request->getParsedBody();

		$query = \Models\Designation::where("id", ">", "0");

		if (isset($params["search"]) && is_array($params["search"])) {
			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}
			foreach($params["search"] as $field => $value) {

			    if($field == 'assigned' || $field == 'career_path_ids') continue;

                if (is_int($value)) {
                    $query->where($field, "=", $value);
                } else {
					$query->where($field, "LIKE", "%{$value}%");
				}
			}
		}

        if(!empty($params['search']['career_path_ids'])) {
            $query->with(['CareerPaths' => function ($query) use ($params) {
                $query->whereIn('career_paths.id', $params['search']['career_path_ids']);
            }]);
        }

		if(!empty($params["search"]) && isset($params["search"]['assigned'])) {
		    if(!empty($params['search']['career_path_ids'])) {
                if($params["search"]['assigned'] == 1) {
                    $query->whereHas('CareerPaths', function ($query) use ($params) {
                        $query->whereIn('career_paths.id', $params['search']['career_path_ids']);
                    });
                } else if ($params["search"]['assigned'] == 0) {
                    $query->whereDoesntHave('CareerPaths', function ($query) use ($params) {
                        $query->whereIn('career_paths.id', $params['search']['career_path_ids']);
                    });
                }
            } else {
                if($params["search"]['assigned'] == 1) {
					$response->getBody()->write(json_encode([]));
					return $response->withHeader('Content-Type', 'application/json');
				}
            }
        }

        unset($params['search']['assigned'], $params['search']['career_path_ids']);

		if (isset($args["download"]) && $args["download"] == "/download") {
			$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

			$export_fields = [
				"ID" => "id",
				"Job Title" => "name",
			];


			$download_file_name = uniqid("designations.list.") . ".xlsx";

			\APP\Tools::generateExcelDownload(
					$data,
					$export_fields,
					$this->get('settings')["LMSTempPath"] . $download_file_name
				)
			;

			$response->getBody()->write(json_encode($download_file_name));
			return $response->withHeader('Content-Type', 'application/json');
		} else {
			$p = \APP\SmartTable::searchPaginate($params, $query);
		}

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-jobs', 'select'));

    $group->post('/list-jobs/{programme_id:[0-9]+}', function (Request $request, Response $response, array $args) {
        $params = $request->getParsedBody();
        $query = \Models\Designation::where("id", ">", "0");
        if (isset($params["search"]) && is_array($params["search"])) {
            if (isset($params["search"]["refresh"])) {
                unset($params["search"]["refresh"]);
            }
            foreach ($params["search"] as $field => $value) {
                if (is_int((int)$value)) {
                    if($field === 'assigned') {
                        if($value == 1) {
                            $query->whereHas('Programmes', function($query) use ($args){
                                $query->where('standard_id', '=', $args['programme_id']);
                            });
                        } else if($value == 0) {
                            $query->whereNotExists(function($query) use ($args) {
                                $query->select(DB::raw(1))
                                    ->from('apprenticeship_designations')
                                    ->join('apprenticeship_standards', 'apprenticeship_standards.id', '=', 'apprenticeship_designations.standard_id')
                                    ->whereRaw('designations.id = apprenticeship_designations.designation_id')
                                    ->where('standard_id', $args['programme_id']);
                            });
                        }
                        unset($params['search']['assigned']);
                    } else {
                        $query->where($field, "=", $value);
                    }
                } else {
                    $query->where($field, "LIKE", "%{$value}%");
                }
            }
        }
        $data = \APP\SmartTable::searchPaginate($params, $query);
        $existingDesignationIds = \Models\ApprenticeshipDesignations::where('standard_id', $args['programme_id'])->pluck('designation_id')->toArray();
        foreach ($data as $p) {
            $p->selected = in_array($p->id, $existingDesignationIds);
        }

        $response->getBody()->write($data->toJson());
		return $response->withHeader('Content-Type', 'application/json');

    });

	// Return User Job Requrements
	$group->get('/user/{id:[0-9]+}', function (Request $request, Response $response, $args)
	{
		if (!\Models\Role::getRoleParam('lfp_show_required_competencies')) return $response->withStatus(403)->withHeader('Content-Type', 'text/html');

		if (\APP\Auth::isLearner()) {
			$args["id"] = \App\Auth::getUserId();
		}

		if (
			isset($args['id']) &&
			intval($args['id']) > 0
		) {

			$returnData = \Models\User
				::where('id', $args["id"])
				->with(['designation' => function ($query) use ($args) {
					$query = $query
						->with(['competencies' => function ($query) use ($args) {
							$query = $query
								->select(
									'competencies.id',
									'name',
									DB::raw(
										"
											(
												case when (
													select count(*) from user_competencies where user_id = " . $args['id'] . " and user_competencies.points >= competencies.required_points and user_competencies.competency_id = competencies.id
												) > 0
													then true
													else false
												end
											) as acquired
										"
									)
								);
							;
						}])
					;
				}])
				->select('username', 'designation_id', 'id')
				->first()
			;

			$response->getBody()->write(json_encode($returnData));
		return $response->withHeader('Content-Type', 'application/json');
		}
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-jobs', 'select'));

});