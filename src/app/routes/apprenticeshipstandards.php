<?php

use APP\Form;
use APP\Auth;
use APP\Services\UserApprenticeshipStandardService;
use APP\Tools;
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;
use Models\ApprenticeshipStandard;
use Models\GroupStandards;
use Models\DepartmentStandard;
use Models\Import;
use Models\ManagerUser;
use Models\User;
use Slim\Psr7\Factory\StreamFactory;
use GuzzleHttp\Client;
use Models\LearningModule;


$GLOBALS['openAIauth'] = "Bearer ***************************************************";	//Open-eLMS

$app->group("/apprenticeshipstandards",  function ($group) {

	// Export Standard to file for import in different installation
	$group->get("/export/{id:[0-9]+}" , function (Request $request, Response $response, $args) {

		$standard = \Models\ApprenticeshipStandard
			::where('id', $args["id"])
			->with('category')
			->with(["issuecategories" => function ($query) {
				$query
					->where('status', 1)
					->with(["issues" => function ($query) {
						$query
							->where('status', true)
							->where('parent_id', 0)
							->with(["children" => function ($query) {
								$query
									->where('status', true)
									->where('parent_id', '>', 0)
								;
								$query = \Models\ApprenticeshipStandard::exportModulesQuery($query);
							}])
						;
						$query = \Models\ApprenticeshipStandard::exportModulesQuery($query);
					}])
				;
			}])
			->first()
		;

		// Put temp file in system, will be used as zip archive.
		$temp_file = tempnam(sys_get_temp_dir(), 'zip');

		// Also temporary store json file, no other temp file should be created,
		$temp_json_file = tempnam(sys_get_temp_dir(), 'json');

		file_put_contents(
			$temp_json_file,
			json_encode($standard)
		);


		$zip = new ZipArchive();
		$zip->open($temp_file, ZipArchive::OVERWRITE);
		$zip->addFile($temp_json_file, \APP\Tools::safeName($standard->name, '_', true) .'.json');

		foreach ($standard->issuecategories as $issuecategory_key => $issuecategory) {
			foreach ($issuecategory->issues as $issue_key => $issue) {

				// This will be used for child issue, reuse!
				foreach ($issue->modules as $issue_module_key => $issue_module) {
					$zip = \Models\ApprenticeshipStandard::exportModulesFiles($issue_module, $zip);
				}

				foreach ($issue->children as $issue_child_key => $issue_child) {
					foreach ($issue_child->modules as $issue_child_module_key => $issue_child_module) {
						$zip = \Models\ApprenticeshipStandard::exportModulesFiles($issue_child_module, $zip);
					}
				}
			}
		}
		// Loop trough all resources, pick up thumbnail/promo/higlight images and put in zip also!

		$zip->close();


		$streamFactory = new StreamFactory();
		$fileStream = $streamFactory->createStreamFromFile($temp_file, 'r');

		return $response
			->withBody($fileStream)
			->withHeader('Content-Disposition', 'attachment; filename=' . \APP\Tools::safeName($standard->name, '_', true) . '.zip;')
			->withHeader('Content-Type', mime_content_type($temp_file))
			->withHeader('Content-Length', (string)filesize($temp_file))
		;

	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'select'));


	// Importing Standard from ZIP file, that was saved by /export
	// Import Standard by excel file, example is available on front end import window.
	$group->post('/import', function(Request $request, Response $response) {
		$data = $request->getParsedBody();

		if (isset($_FILES['importStandardFile'])) {

			$allowed_types = [
				'excel' => [
					'mime' => \APP\Tools::documentMime('excel'),
					'extension' => \APP\Tools::allowExtensions('excel'),
					'validation_message' => 'Invalid file type. You must upload a Excel file.',
				],
				'json' => [
					'mime' => \APP\Tools::documentMime('zip'),
					'extension' => 'zip',
					'validation_message' => 'Invalid file type. You must upload a ZIP file.',
                ],
                'rawjson' => [
                    'mime' => \App\Tools::documentMime('json'),
                    'extension' => 'json',
                    'validation_message' => 'Invalid file type. You must upload a JSON file.',
                ]
			];
			if (
				empty($data['import_type']) ||
				empty($allowed_types[$data['import_type']])
			) {
				return \APP\Tools::returnCode($request, $response, '403', 'Incorrect file uploaded.');
			}

			//upload file
			$storage = new \Upload\Storage\FileSystem($this->get('settings')["LMSTempPath"]);
			$import_file = new \Upload\File('importStandardFile', $storage);
			$import_file_id = uniqid();
			$original_name = $import_file->getNameWithExtension();
			$import_file->setName($import_file_id);

			$fileTypeValidation = new \Upload\Validation\Mimetype($allowed_types[$data['import_type']]['mime']);
			$fileTypeValidation->setMessage($allowed_types[$data['import_type']]['validation_message']);
			$import_file->addValidations([
				$fileTypeValidation,
				new \Upload\Validation\Size('800M'),
				new \Upload\Validation\Extension($allowed_types[$data['import_type']]['extension'])
			]);

			try {
				$import_file->upload();
			} catch (\Upload\Exception\UploadException $e) {
				$errors = $import_file->getErrors();
				return \APP\Tools::returnCode($request, $response, 500, implode("\n", $errors));
			}


			$uploaded_file = $this->get('settings')["LMSTempPath"] . $import_file->getNameWithExtension();
			$extracted_directory = $this->get('settings')["LMSTempPath"] . $import_file->getName();

			if ($data['import_type'] == 'excel') {
				$spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($uploaded_file);
				$sheet = $spreadsheet->getActiveSheet();
				$rows = $sheet->getRowIterator(1);

				$fields = [
					"A" => "name",
					"B" => "route",
					"C" => "reference_code",
					"D" => "disabled",
					"E" => "type",
					"F" => "periodic_repeat_months",
					"G" => "category",
					"H" => "completion_months",
				];

				$insert_data = [];

				foreach ($rows as $row) {
					$row_i = $row->getRowIndex();
					$cells = $row->getCellIterator();
					$cells->setIterateOnlyExistingCells($row_i == 1);
					foreach ($cells as $cell_i => $cell) {
						if ($row_i != 1) {
							if (
								empty($fields[$cell_i]) ||
								!$cell->getFormattedValue()
							) {
								continue;
							}
							$insert_data[$row_i][$fields[$cell_i]] = $cell->getFormattedValue();
						}
					}
				}

				$import_cnt = 0;

				foreach ($insert_data as $key => $entry) {
					if (!empty($entry['name'])) {
						$new_entry = new \Models\ApprenticeshipStandard;
						$new_entry->name = $entry['name'];

						if (isset($entry['route'])) {
							$new_entry->route = $entry['route'];
						}

						if (isset($entry['reference_code'])) {
							$new_entry->reference_code = $entry['reference_code'];
						}

						$new_entry->status = isset($entry['disabled']) ? 0 : 1;

						if (isset($entry['type'])) {
							$new_entry->type = $entry['type'];
						}

						if (isset($entry['completion_months'])) {
							$new_entry->completion_months = $entry['completion_months'];
						}

						if (isset($entry['periodic_repeat_months'])) {
							if (
								$new_entry->type &&
								$new_entry->type == 'Skills Monitoring'
							) {
								$new_entry->repetition_period = $entry['periodic_repeat_months'];
							} else {
								$new_entry->periodic_repeat = true;
								$new_entry->periodic_repeat_months = $entry['periodic_repeat_months'];
							}
						}

						if (isset($entry['category'])) {
							$existing_category = \Models\LearningModuleCategory
								::where('name', $entry['category'])
								->first()
							;
							if ($existing_category) {
								$new_entry->category_id = $existing_category->id;
							}
						}

						$new_entry->save();

						// Add one outcome as example
						$outcome = new \Models\ApprenticeshipIssueCategories;
						$outcome->name = 'Evidence';
						$outcome->standard_id = $new_entry->id;
						$outcome->status = 1;
						$outcome->save();

						// Add one criteria as example
						$criteria = new \Models\ApprenticeshipIssues;
						$criteria->name = 'Criteria';
						$criteria->issue_category_id = $outcome->id;
						$criteria->start_day = 0;
						$criteria->end_day = 20; // default

						// Skills monitoring will set end day to end of repetition period
						if (
							$new_entry->type &&
							$new_entry->type == 'Skills Monitoring' &&
							$new_entry->repetition_period
						) {
							$criteria->end_day = round(($new_entry->repetition_period / 12) * 365);
						}

						// If completion months are present and end day calculated is larger than 20, but less than end_day set for skills, then set that!
						if (
							$new_entry->completion_months &&
							(
								$criteria->end_day == 20 ||
								$criteria->end_day < round(($new_entry->completion_months / 12) * 365)
							)
						) {
							$criteria->end_day = round(($new_entry->completion_months / 12) * 365);
						}

						$criteria->status = 1;
						$criteria->save();

						$import_cnt++;
					}
				}

				$response->getBody()->write($import_cnt . ' entries imported!');
				return $response;
			}

			if ($data['import_type'] == 'json') {
				$zip = new ZipArchive;
				$res = $zip->open($uploaded_file);
				if ($res === TRUE) {
					$zip->extractTo($extracted_directory);
					$zip->close();
				} else {
					// Failed to recognize zip file, delete it
					if (is_file($uploaded_file)) {
						unlink($uploaded_file);
					}

					return \APP\Tools::returnCode($request, $response, 404);
				}

				// Find json file in directory
				$json_file = false;
				$json_file_name = false;
				if (is_dir($extracted_directory)) {
					if ($dh = opendir($extracted_directory)) {
						while (($file = readdir($dh)) !== false) {
							if (is_file($extracted_directory . '/' . $file)) {
								$ext = pathinfo($file, PATHINFO_EXTENSION);
								if ($ext == 'json') {
									$json_file = $extracted_directory . '/' . $file;
									$json_file_name = $file;
								}
							}
						}
						closedir($dh);
					}
				}

				if ($json_file) {
					// load file as JSON object
					$json = json_decode(
						file_get_contents($json_file),
						false
					);

					// Log imports!
					\Models\LogExportImport::insertRecord(file_get_contents($json_file), '.json', $json_file_name, false, 'imports');

					// Recreate standard and structure
					$standard = new \Models\ApprenticeshipStandard;
					$standard->route = isset($json->route) ? $json->route : '';
					$standard->name = $json->name . " (imported at '" . date("Y-m-d H:i:s") . "')";
					// category_id
					if ($json->category && $json->category->name) {
						// create new category if does not exists
						$standard_category = \Models\LearningModuleCategory::firstOrCreate(
							['name' => $json->category->name],
							['status' => 1]
						);
						if ($standard_category) {
							$standard->category_id = $standard_category->id;
						}
					}
					$standard->level = isset($json->level) ? $json->level : null;
					$standard->funding = isset($json->funding) ? $json->funding : 0;
					$standard->reference_code = isset($json->reference_code) ? $json->reference_code : '';
					$standard->periodic_review = isset($json->periodic_review) ? $json->periodic_review : 0;
					$standard->review_interval = isset($json->review_interval) ? $json->review_interval : null;
					$standard->ilr_learning_delivery = isset($json->ilr_learning_delivery) ? $json->ilr_learning_delivery : 0;
					$standard->learning_delivery_type = isset($json->learning_delivery_type) ? $json->learning_delivery_type : 0;
					$standard->delivery = isset($json->delivery) ? $json->delivery : '';
					$standard->status = 1;
					$standard->type = isset($json->type) ? $json->type : '';
					$standard->completion_months = $json->completion_months;
					$standard->periodic_repeat = isset($json->periodic_repeat) ? $json->periodic_repeat : 0;
					$standard->periodic_repeat_months = isset($json->periodic_repeat_months) ? $json->periodic_repeat_months : null;
					$standard->working_hours = isset($json->working_hours) ? $json->working_hours : null;

					$standard->save();

					// Recreate Standard categories/outcomes
					foreach ($json->issuecategories as $issueCategoryKey => $issueCategoryValue) {

						$issueCategory = new \Models\ApprenticeshipIssueCategories;
						$issueCategory->name = $issueCategoryValue->name;
						$issueCategory->standard_id = $standard->id;
						$issueCategory->status = 1;
						$issueCategory->sort = $issueCategoryValue->sort;
						$issueCategory->exclude_outcome = $issueCategoryValue->exclude_outcome;
						$issueCategory->disabled = $issueCategoryValue->disabled;
						$issueCategory->hide_progressbar = $issueCategoryValue->hide_progressbar;

						$issueCategory->save();


						// Recreate Category Issues/criteria
						foreach ($issueCategoryValue->issues as $issueKey => $issueValue) {

							$issue = new \Models\ApprenticeshipIssues;
							$issue->name = $issueValue->name;
							$issue->issue_category_id = $issueCategory->id;
							$issue->status = 1;
							$issue->hide_learner = $issueValue->hide_learner;
							$issue->hide_progressbar = $issueValue->hide_progressbar;
							$issue->sort = intval($issueValue->sort);
							$issue->end_day = $issueValue->end_day;
							$issue->start_day = $issueValue->start_day;
							$issue->guidelines = isset($issueValue->guidelines) ? $issueValue->guidelines : '';
							$issue->visible_resource = isset($issueValue->visible_resource) ? $issueValue->visible_resource : 0;
							$issue->save();

							//Recreate resources added to issues
							foreach ($issueValue->modules as $moduleKey => $moduleValue) {
								\Models\LearningModule::importModule($moduleValue, $issue, $this->get('settings'), $extracted_directory);
							}

							foreach ($issueValue->children as $childrenKey => $childrenValue) {
								$childrenIssue = new \Models\ApprenticeshipIssues;
								$childrenIssue->name = $childrenValue->name;
								$childrenIssue->issue_category_id = $issueCategory->id;
								$childrenIssue->status = 1;
								$childrenIssue->hide_learner = $childrenValue->hide_learner;
								$childrenIssue->hide_progressbar = $childrenValue->hide_progressbar;
								$childrenIssue->sort = intval($childrenValue->sort);
								$childrenIssue->end_day = $childrenValue->end_day;
								$childrenIssue->start_day = $childrenValue->start_day;
								$childrenIssue->parent_id = $issue->id;
								$childrenIssue->guidelines = isset($childrenValue->guidelines) ? $childrenValue->guidelines : '';
								$childrenIssue->visible_resource = isset($childrenValue->visible_resource) ? $childrenValue->visible_resource : 0;
								$childrenIssue->save();

								//Recreate resources added to issues
								foreach ($childrenValue->modules as $childrenModuleKey => $childrenModuleValue) {
									\Models\LearningModule::importModule($childrenModuleValue, $childrenIssue, $this->get('settings'), $extracted_directory);
								}
							}
						}
					}

					unlink($uploaded_file);
					\APP\Tools::delDirTree($extracted_directory);

					$response->getBody()->write('Standard "' . $standard->name . '" imported!');
					return $response;

				} else {
					return \APP\Tools::returnCode($request, $response, 404);
				}
            }elseif($data['import_type']=='rawjson'){
                Import::create(['file'=>$uploaded_file,'params'=>[],'cron_run'=>true,'type'=>'programme']);
				$response->getBody()->write('Import will be processed in the background');
				return $response;
            }
		}

	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'insert'));


	/*
		Retrieves standard with all categories/issues/modules, with all assigned evidence if trainee requests this.
	*/

	$group->post("/user-standards/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		if (
			isset($data['user_id'])
			&& $data['user_id']
		) {
			$user = \Models\User::find($data['user_id']);
			if (
				\APP\Auth::isAdmin() ||
				\APP\Auth::isManagerOf($user->id) ||
				\APP\Auth::accessAllLearners() ||
				$user->id == \APP\Auth::getUserId()
			) {
				$user_id = $user->id;
			} else {
				return \APP\Tools::returnCode($request, $response, 403);
			}
		} else {
			$user_id = \APP\Auth::getUserId();
		}

		$user_full = \Models\User
			::where('id', $user_id)
			->with(['standards' => function($query) use ($user_id, $args, $data) {
				$query
					->where('apprenticeship_standards.id', $args["id"])
					->where('apprenticeship_standards.status', true)
					->with(['issuecategories' => function($query) use ($user_id, $data) {
						$query
							->orderBy('sort', 'ASC')
							->where('apprenticeship_issue_categories.status', true)
							->with(["issues" => function ($query) use ($user_id, $data) {
								if (\App\Auth::isLearner()) {
									$query->where('hide_learner', 0);
								}
								$query
									->where("status", 1)
									->where('parent_id', 0)
									->orderBy('sort', 'ASC')
									->with(["children" => function ($query) use ($user_id, $data) {
										\Models\ApprenticeshipIssues::getIssueModules($data, $query, $user_id);
										$query
											->where("status", 1)
											->with(["children" => function ($query) use ($user_id, $data) {
												$query
													->where("status", 1)
												;
												\Models\ApprenticeshipIssues::getIssueModules($data, $query, $user_id);
											}]);
										;
									}]);

								// issues listed at evidence module page, only relation to evidence modules are needed
								\Models\ApprenticeshipIssues::getIssueModules($data, $query, $user_id);
							}])
						;
						if (
							!\APP\Auth::isAdmin() &&
							!\APP\Auth::isManager()
						) {
							$query = $query
								->whereHas('issues', function ($query) use ($user_id) {
									$query
										->where("status", 1)
										->whereDoesntHave('disabled', function ($query) use ($user_id) {
											$query
												->where('user_id', $user_id)
											;
										});
									if (\App\Auth::isLearner()) {
										$query->where('hide_learner', 0);
									}
								})
							;
						}
					}])
				;
			}])
			->first()
		;
		if ($user_full) {
			if (isset($user_full->standards[0])) {
				\Models\TableExtension::returnAllFields('apprenticeship_standards', $user_full->standards[0]->id, $user_full->standards[0]);
			}
			// Incompatible here, but as a reference, might need to unify all these different requests.
			// Learners file in administration interface also requests all standards and categories/issues/modules, can be used here too.
			\Models\User::calculateStandardProgress($user_full->standards);
		}

		$response->getBody()->write(json_encode($user_full->standards[0]));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());



	$group->post("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$user_id = false;
		if (
			isset($data['user_id']) &&
			$data['user_id']
		) {
			$user = \Models\User::find($data['user_id']);
			if (
				\APP\Auth::isAdmin() ||
				\APP\Auth::isManagerOf($user->id) ||
				\APP\Auth::accessAllLearners() ||
				$user->id == \APP\Auth::getUserId()
			) {
				$user_id = $user->id;
			} else {
				return \APP\Tools::returnCode($request, $response, 403);
			}
		} else if (\APP\Auth::isLearner()) {
			$user_id = \APP\Auth::getUserId();
		}

		$apprenticeshipstandard = \Models\ApprenticeshipStandard
			::with(["issuecategories" => function ($query) use ($user_id, $args, $data) {
				$query
					->where("status", "=", 1)
					->orderBy('sort', 'ASC')
					->with(["issues" => function ($query) use ($user_id, $args, $data) {
						if (\App\Auth::isLearner()) {
							$query->where('hide_learner', 0);
						}
						$query
							->where("status", 1)
							->where('parent_id', 0)
							->orderBy('sort', 'ASC')
							->with(["Children" => function ($query) use ($user_id, $args, $data) {
								// AAAAnd repeat all that module thing here as well...
								\Models\User::getIssueModules($query, $args["id"]);
								$query
									->where("status", 1)

									->with(["Children" => function ($query) use ($user_id, $args, $data) {
										\Models\User::getIssueModules($query, $args["id"]);
										// AAAAnd repeat all that module thing here as well...
										$query
											->where("status", 1);

										\Models\ApprenticeshipIssues::getIssueModules($data, $query, $user_id);
									}]);

								\Models\ApprenticeshipIssues::getIssueModules($data, $query, $user_id);
							}]);

						// issues listed at evidence module page, only relation to evidence modules are needed
						\Models\ApprenticeshipIssues::getIssueModules($data, $query, $user_id);


					}]);

				if (
					!\APP\Auth::isAdmin() &&
					!\APP\Auth::isManager() &&
					!\APP\Auth::isCd() &&
					$user_id
				) {
					$query = $query
						->whereHas('issues', function ($query) use ($user_id) {
							$query
								->where("status", 1)
								->whereDoesntHave('disabled', function ($query) use ($user_id) {
									$query->where('user_id', '=', $user_id);
								});
							if (\App\Auth::isLearner()) {
								 $query->where('hide_learner', 0);
							}
						})
					;
				}
			}])
			->find($args["id"])
		;

		$resource_queries = \Models\ResourceQuery::where([['type', 'programmes'],['type_id', $args["id"]]])->orderByDesc('updated_at')->first();

		$apprenticeshipstandard->resource_query = $resource_queries;



		\Models\TableExtension::returnAllFields('apprenticeship_standards', $apprenticeshipstandard->id, $apprenticeshipstandard);

		$response->getBody()->write(json_encode($apprenticeshipstandard));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());



	/*
		Get all evidences matched to standards.
	*/
	$group->get("/user-evidence/{user_id:[0-9]+}/{evidence_id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$user = \Models\User::find($args["user_id"]);
		if (
			\APP\Auth::isAdmin() ||
			\APP\Auth::isManagerOf($user->id) ||
			\APP\Auth::accessAllLearners() ||
			$user->id == \APP\Auth::getUserId()
		) {
			// Needs revision, add also isses this resource is assigned to.
			$data = \Models\ApprenticeshipIssues
				::where(function ($query) use ($user, $args) {
					$query
						->whereHas('attachedevidences', function ($query) use ($user, $args) {
							$query
								->where('user_id', $user->id)
								->where('learning_modules_id', $args['evidence_id'])
								->whereHas('module', function ($query) {
									$query
										->where('status', true)
									;
								})
							;
						})
						->orWhereHas('UserLearningModules', function ($query) use ($user, $args) {
							$query
								->where('user_id', $user->id)
								->where('learning_modules_id', $args['evidence_id'])
								->whereHas('module', function ($query) {
									$query
										->where('status', true)
									;
								})
							;
						})
						->orWhereHas('LearningModules', function ($query) use ($args) {
							$query
								->where('learning_modules_id', $args['evidence_id'])
								->whereHas('module', function ($query) {
									$query
										->where('status', true)
									;
								})
							;
						})
					;
				})
				->select(
					'id',
					'name'
				)
				->selectRaw(
					"(
					select count(*) from apprenticeship_issues_evidence
						where apprenticeship_issues_evidence.apprenticeship_issues_id = apprenticeship_issues.id
						and apprenticeship_issues_evidence.user_id = ?
						and apprenticeship_issues_evidence.learning_modules_id = ?
				) as deletable",
					[$user->id, $args['evidence_id']]
				)
				->whereIn('issue_category_id',
					\Models\ApprenticeshipIssueCategories
						::select('id')
						->where('status', true)
						->whereIn('standard_id',
							\Models\ApprenticeshipStandard
								::select('id')
								->where('status', true)
								->whereIn('id',
									\Models\ApprenticeshipStandardUser
									::select('standard_id')
									->where('user_id', $user->id)
									->get()
								)
								->get()
						)
						->get()
				)
				->where('status', true)
				->get();
			;
		} else {
			return \APP\Tools::returnCode($request, $response, 403);
		}

		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());

	// Standard cloning action
	$group->post("/clone" , function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		// Get full standard details here
		$oldstandard = \Models\ApprenticeshipStandard::where("status", true)
			->where('id', $data['id'])
			->with(["issuecategories" => function ($query) {
				$query
					->where('status', true)
					->with(["issues" => function ($query) {
						$query
							->where('status', true)
							->where('parent_id', 0)
							->with(["children" => function ($query) {
								$query
									->where('status', true)
									->where('parent_id', '>', 0)
									->with(["modules" => function ($query) {
										$query
											->where('status', true)
										;
									}])
								;
							}])
							->with(["modules" => function ($query) {
								$query
									->where('status', true)
								;
							}])
						;
					}])
				;
			}])
			->first()
		;


		// create new standard
		$newstandard = new \Models\ApprenticeshipStandard;
		$newstandard->name = $data["name"];
		if ($oldstandard->route) {
			$newstandard->route = $oldstandard->route;
		}
		if ($oldstandard->category_id) {
			$newstandard->category_id = $oldstandard->category_id;
		}
		$newstandard->level = $oldstandard->level;
		$newstandard->funding = $oldstandard->funding;
		$newstandard->reference_code = $oldstandard->reference_code;
		if ($oldstandard->periodic_review) {
			$newstandard->periodic_review = $oldstandard->periodic_review;
		}
		$newstandard->review_interval = $oldstandard->review_interval;
		if ($oldstandard->ilr_learning_delivery) {
			$newstandard->ilr_learning_delivery = $oldstandard->ilr_learning_delivery;
		}
		if ($oldstandard->learning_delivery_type) {
			$newstandard->learning_delivery_type = $oldstandard->learning_delivery_type;
		}
		if ($oldstandard->delivery) {
			$newstandard->delivery = $oldstandard->delivery;
		}
		$newstandard->sort = $oldstandard->sort;
		if ($oldstandard->type) {
			$newstandard->type = $oldstandard->type;
		}
		if ($oldstandard->completion_months) {
			$newstandard->completion_months = $oldstandard->completion_months;
		}
		if ($oldstandard->periodic_repeat) {
			$newstandard->periodic_repeat = $oldstandard->periodic_repeat;
		}
		if ($oldstandard->periodic_repeat_months) {
			$newstandard->periodic_repeat_months = $oldstandard->periodic_repeat_months;
		}
		$newstandard->working_hours = $oldstandard->working_hours;
		$newstandard->status = 1;
		$newstandard->save();


		// loop all old standard categories and create new ones assigned to new stanrad

		foreach ($oldstandard->issuecategories as $key => $issuecategory) {
			$apprenticeshipcategory = new \Models\ApprenticeshipIssueCategories;
			$apprenticeshipcategory->name = $issuecategory->name;
			$apprenticeshipcategory->sort = $issuecategory->sort;
			$apprenticeshipcategory->standard_id = $newstandard->id;
			$apprenticeshipcategory->status = 1;
			$apprenticeshipcategory->save();

			//loop all issues and recreate them for new categories
			foreach ($issuecategory->issues as $key => $issue) {
				$apprenticeshipissues = new \Models\ApprenticeshipIssues;
				$apprenticeshipissues->name = $issue->name;
				$apprenticeshipissues->issue_category_id = $apprenticeshipcategory->id;
				$apprenticeshipissues->start_day = $issue->start_day;
				$apprenticeshipissues->end_day = $issue->end_day;
				$apprenticeshipissues->sort = $issue->sort;
				$apprenticeshipissues->guidelines = $issue->guidelines;
				$apprenticeshipissues->visible_resource = $issue->visible_resource;
				$apprenticeshipissues->status = 1;
				$apprenticeshipissues->save();

				// loop all modules and attach them to new issues
				foreach ($issue->modules as $key => $module) {
					$issueModule = new \Models\ApprenticeshipIssuesLearningModules;
					$issueModule->apprenticeship_issues_id = $apprenticeshipissues->id;
					$issueModule->learning_modules_id = $module->id;
					$issueModule->sort = $module->pivot->sort;
					$issueModule->custom_work_window = $module->pivot->custom_work_window;
					$issueModule->start_day = $module->pivot->start_day;
					$issueModule->end_day = $module->pivot->end_day;
					$issueModule->save();
				}

				// Loop sub issue
				foreach ($issue->children as $key => $issue_child) {
					$childissue = new \Models\ApprenticeshipIssues;
					$childissue->name = $issue_child->name;
					$childissue->issue_category_id = $apprenticeshipcategory->id;
					$childissue->start_day = $issue_child->start_day;
					$childissue->end_day = $issue_child->end_day;
					$childissue->sort = $issue_child->sort;
					$childissue->parent_id = $apprenticeshipissues->id;
					$childissue->guidelines = $issue_child->guidelines;
					$childissue->visible_resource = $issue_child->visible_resource;
					$childissue->status = 1;
					$childissue->save();

					// loop all modules and attach them to new issues
					foreach ($issue_child->modules as $key => $sub_module) {
						$sub_issueModule = new \Models\ApprenticeshipIssuesLearningModules;
						$sub_issueModule->apprenticeship_issues_id = $childissue->id;
						$sub_issueModule->learning_modules_id = $sub_module->id;
						$sub_issueModule->sort = $sub_module->pivot->sort;
						$sub_issueModule->custom_work_window = $sub_module->pivot->custom_work_window;
						$sub_issueModule->start_day = $sub_module->pivot->start_day;
						$sub_issueModule->end_day = $sub_module->pivot->end_day;
						$sub_issueModule->save();
					}
				}

			}
		}

		$response->getBody()->write(json_encode($newstandard));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'insert'));

	// Get all modules from standard
	$group->get("/{standard:[0-9]+}/modules/list" , function (Request $request, Response $response, $args) {
		$apprenticeshipstandard = \Models\ApprenticeshipStandard
			::where('id', '=', $args["standard"])
			->with(['issuecategories' => function($query) {
				$query
					->where('apprenticeship_issue_categories.status', '=', 1)
					->with(['issues' => function($query) {
						$query
							->with(['modules' => function($query) {
								$query
									->where('learning_modules.status', '=', 1)
								;
							}])
							->where('apprenticeship_issues.status', '=', 1)
						;
					}])
				;
			}])
			->first()
		;

		$all_standard_modules = [];

		function findObjectById($id, $array){

			foreach ( $array as $element ) {
				if ( $id == $element->id ) {
					return $element;
				}
			}

			return false;
		}

		// loop all issues for current standard
		foreach ($apprenticeshipstandard->issuecategories as $category) {
			foreach ($category->issues as $issue)  {
				// loop all modules in each issue and put Id's in array
				foreach ($issue->modules as $module) {
					if (!findObjectById($module->id, $all_standard_modules)) {
						$module_loop = new stdClass(); // cheating?
						$module_loop->id = $module->id;
						$module_loop->name = $module->name;
						$all_standard_modules[] = $module_loop;
					}
				}
			}
		}

		$response->getBody()->write(json_encode($all_standard_modules));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'select'));

	// Update standard
	$group->put("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$apprenticeshipstandard = \Models\ApprenticeshipStandard::find($args["id"]);
		$data = $request->getParsedBody();
		if(isset($data['custom_field']))
		{
			Form::saveCustomForm($data['custom_field'],'programme',$apprenticeshipstandard->id);
		}
		$fields = [
			"route",
			"name",
			"completion_months",
			"category_id",
			"level",
			"funding",
			"status",
			"sort",
			"reference_code",
			"periodic_review",
			"review_interval",
			"ilr_learning_delivery",
			"learning_delivery_type",
			"delivery",
			"working_hours",
			"type",
			"periodic_repeat",
			"periodic_repeat_months",
			"course_credits",
			"number_of_evidence_expected",
			"badge",
			"repetition_period",
			"default_start_month",
			"default_skill_repetition_period",
			'print_certificate',
			"self_enroll",
			"cost",
			"description",
			"completion_criteria",
			"completion_resources",
			"completion_time",
		];


		$completion_months = $apprenticeshipstandard->completion_months;
		foreach($fields as $field) {
			if (isset($data[$field])) {
				$apprenticeshipstandard->$field = $field == 'delivery' ? json_encode($data[$field]) : $data[$field];
			}
		}
		$apprenticeshipstandard->default_skill_repetition_period = $data['default_skill_repetition_period'];
		$apprenticeshipstandard->repetition_period = $data['repetition_period'];
		$apprenticeshipstandard->default_start_month = $data['default_start_month'];

		// If months are given, get configuration for "Training/work ratio" and calculate working hours.
		// Only if months are updated, else someone might want to have custom working hours.
		if (isset($data['completion_months'])) {
			if ($completion_months != $data['completion_months']) {
				$apprenticeshipstandard->working_hours = \Models\ApprenticeshipStandard::WorkingHours($data['completion_months']);
			}
		}


		$apprenticeshipstandard->save();

		// If "Link to ILR Learning Delivery" is checked and "Learning Delivery Type" chosen add Specified delivery for users assigned to standard.
		/*
		if (
			isset($data['ilr_learning_delivery']) &&
			isset($data['learning_delivery_type']) &&
			isset($data['delivery']['aim']) &&
			isset($data['delivery']['aim_reference'])
		) {
			// check if all assigned users to standard has learning delivery linked to this standard.
			$users = \Models\User
				::whereIn('id',
					\Models\ApprenticeshipStandardUser
						::where('standard_id', $apprenticeshipstandard->id)
						->select('user_id')
						->get()
				)
				->get()
			;
			// loop all users and see if LearningDelivery is populated, if not, make it an array
			foreach ($users as $key => $user) {
				$updated = false;
				if ($user->LearningDelivery) {
					$learninDeliveries = json_decode($user->LearningDelivery);
				} else {
					$learninDeliveries = [];
				}

				// get template from ilr_fields.php depending on "learning_delivery_type" chosen from editing/adding standard
				$template = json_decode($this->get('settings')["ilr_learning_deliveries"][$data['learning_delivery_type']]['template']);
				$template->record = $data['learning_delivery_type'];
				$template->programme = $apprenticeshipstandard->id;

				foreach ($learninDeliveries as $key => $learninDelivery) {
					// if delivery exists for user linked to this standard, update fields if they are present
					if (
						isset($learninDelivery->programme) &&
						$learninDelivery->programme == $apprenticeshipstandard->id
					) {
						// Update this programme's delivery type with actual data.
						if (isset($data['delivery'])) {
							if (isset($data['delivery']['aim'])) {
								$template->AimType = intval($data['delivery']['aim']);
							} else if (isset($learninDelivery->AimType)) {
								$template->AimType = $learninDelivery->AimType;
							}
							if (isset($data['delivery']['aim_reference'])) {
								$template->LearnAimRef = $data['delivery']['aim_reference'];
							} else if (isset($learninDelivery->LearnAimRef)) {
								$template->LearnAimRef = $learninDelivery->LearnAimRef;
							}
							if (isset($data['delivery']['start_date'])) {
								$template->LearnStartDate = $data['delivery']['start_date'];
							} else if (isset($learninDelivery->LearnStartDate)) {
								$template->LearnStartDate = $learninDelivery->LearnStartDate;
							}
							if (isset($data['delivery']['end_date'])) {
								$template->LearnPlanEndDate = $data['delivery']['end_date'];
							} else if (isset($learninDelivery->LearnPlanEndDate)) {
								$template->LearnPlanEndDate = $learninDelivery->LearnPlanEndDate;
							}
						}
						// merge template into learning delivery
						foreach ($template as $key => $field) {
							$learninDelivery->$key = $field;
						}
						$updated = true;
					}
				}

				// If delivery was not present use template to create fresh delivery
				if (!$updated) {
					if (isset($data['delivery'])) {
						if (isset($data['delivery']['aim'])) {
							$template->AimType = intval($data['delivery']['aim']);
						}
						if (isset($data['delivery']['aim_reference'])) {

							$template->LearnAimRef = $data['delivery']['aim_reference'];
						}
						if (isset($data['delivery']['start_date'])) {
							$template->LearnStartDate = $data['delivery']['start_date'];
						}
						if (isset($data['delivery']['end_date'])) {
							$template->LearnPlanEndDate = $data['delivery']['end_date'];
						}
					}
					// Add learning delivery to programme
					$learninDeliveries[] = $template;
				}
				$user->LearningDelivery = json_encode($learninDeliveries);
				$user->save();
			}
		}
		*/


		// add outcome and criteria specified when editing standard too: https://bitbucket.org/emilrw/scormdata/issues/531/4-administration-outcome
		if (isset($data['administrationPaperwork']) && $data['administrationPaperwork']) {

			// Will check if existing paperwork exists, if not, add!

			$outcome_check = \Models\ApprenticeshipIssueCategories
				::where('name', 'Administration Paperwork')
				->where('status', true)
				->where('standard_id', $apprenticeshipstandard->id)
				->count()
			;

			if ($outcome_check == 0) {
				\Models\ApprenticeshipStandard::addAdministrationPaperwork($apprenticeshipstandard->id);
			}
		}

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'update'));


	//Change user start date for standard
	$group->put("/update-user-start-date" , function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		// If current user is admin or manager of user being updated.
		if (
			\APP\Auth::isAdmin() ||
			\APP\Auth::isManagerOf($data["user_id"]) ||
			\APP\Auth::accessAllLearners()
		) {
			if (isset($data['attach'])) {
				$attach = $data['attach'];
			} else {
				$attach = false;
			}
			\Models\ApprenticeshipStandard::UpdateUserStartDate($data["user_id"], $data['standard_id'], $data["start_at"], $data['id'], false, false, false, false, false, $attach);
		} else {
			return \APP\Tools::returnCode($request, $response, 403);
		}
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'update'));


	// Assign user to standard
	$group->put("/{standard_id:[0-9]+}/user/{user_id:[0-9]+}" , function (Request $request, Response $response, $args) {
        $data = $request->getParsedBody();

		\Models\ApprenticeshipStandardUser::assignToStandard($args['user_id'], $args["standard_id"], $data["start_at"]);

		return $response;
	})->add(\APP\Auth::getSessionCheck());

	// Reset custom completion dates
	$group->post("/reset-custom-completion-dates/{mode:.+}/{standard_id:[0-9]+}" , function (Request $request, Response $response, $args) {

		$data = $request->getParsedBody();
		$user_ids = $data["user_ids"];
		$module_ids = $data["module_ids"];

		if ($args["mode"] == "all"){
			switch(\APP\Tools::getConfig('showResetCustomDatesButton')){
				case "admin":
					if (!\APP\Auth::isAdmin(true)){
						return $response;
					}
					break;
				case "manager":
					if (!\APP\Auth::isAdminInterface()){
						return $response;
					}
					break;
				default:
						return $response;
					break;
			}
		} elseif ($args["mode"] == "user") {
			if ((!\APP\Tools::getConfig('showUserResetCustomDatesButton')) || (count($user_ids) > 1)){
				return $response;
			}
		}

		$query = \Models\LearningResult
			::whereIn("user_id", $user_ids)
			->where("refreshed", 0)
			->where(function($query) use ($module_ids) {
				$query
					->whereIn("learning_module_id", function($query2) use ($module_ids) {
						$query2
							->select("learning_module_id")
							->from("learning_course_modules")
							->whereIn("learning_course_id", $module_ids)
						;
					})
					->orWhereIn("learning_module_id", $module_ids);
			});
		// print_r($query->toSql());
		// print_r($query->getBindings());

		$query->update(['completion_date_custom' => null, "completion_date_custom_days" => null]);

		return
			$response
		;

	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'update'));

	// assign standard to department users
	$group->put("/{standard_id:[0-9]+}/department/{department_id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		$start_at = null;
		if (
			isset($data["start_at"])
		) {
			$start_at = \Carbon\Carbon::parse($data["start_at"]);
		}

		DepartmentStandard::updateOrCreate(['department_id'=>$args["department_id"],'standard_id'=>$args["standard_id"], 'assigner_id'=>\APP\Auth::getUserId()],['start_at' => $start_at, 'cron_task'=>true, 'is_assigner_admin'=>\APP\Auth::isAdmin(), 'removed_status'=>false]);

		return
			$response
		;

	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'update'));

    // assign standard to designation users
    $group->put("/{standard_id:[0-9]+}/designation/{designation_id:[0-9]+}" , function (Request $request, Response $response, $args) {
        $data = $request->getParsedBody();

        $start_at = null;
        if (
            isset($data["start_at"])
        ) {
            $start_at = \Carbon\Carbon::parse($data["start_at"]);
        }

        \Models\DesignationStandard::updateOrCreate([
            'designation_id' => $args["designation_id"],
            'standard_id' => $args["standard_id"],
            'assigner_id' => \APP\Auth::getUserId()
        ], [
            'start_at' => $start_at,
            'cron_task' => true,
            'is_assigner_admin' => \APP\Auth::isAdmin(),
            'removed_status' => false
        ]);

        return $response;

    })->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'update'));

    // Remove designation and users from standard
    $group->delete("/{standard_id:[0-9]+}/designation/{designation_id:[0-9]+}" , function (Request $request, Response $response, $args) {
        $data = $request->getParsedBody();


        \Models\DesignationStandard::where("standard_id",$args["standard_id"])
            ->where(function ($query) {
				if (!\APP\Auth::isAdmin()) {
					$query
						->where("assigner_id", \APP\Auth::getUserId())
						->orwhere("assigner_id", null)
					;
				}
            })
            ->where("designation_id", $args["designation_id"])
            ->update(["removed_status" => "1", "cron_task" => true, "assigner_id" => \APP\Auth::getUserId(),"is_assigner_admin" => \APP\Auth::isAdmin()]);

        return $response;
    })->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'disable'));


    $group->post('/designations/{standard:[0-9]+}', function (Request $request, Response $response, $args) {

        $params = $request->getParsedBody();

        if (isset($params["search"]["standard_id"])) {
            unset($params["search"]["standard_id"]);
        }

        $query = \Models\Designation
            ::where("designations.status", 1)
            // ->with(["company" => function($query)  {
            //     $query->select(["companies.id", "companies.name"]);
            // }])
            ->with(['designationStandards' => function($query) use ($args) {
                $query
                    ->where('apprenticeship_standards.id', $args["standard"])
                ;
            }])
            ->select('id', 'name')
        ;

        if (isset($params["search"]) && isset($params["search"]["show_assigned"])) {
            $query->whereHas('standards', function ($query) use ($args) {
                $query
                    ->where('apprenticeship_standards.id', $args["standard"])
                ;
            });
            unset($params["search"]["show_assigned"]);
        }

        $p = \APP\SmartTable::searchPaginate($params, $query);
		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

    })->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'select'));

	// assign standard to group users
	$group->put("/{standard_id:[0-9]+}/group/{group_id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		// Get all department users.
		$group = \Models\Group
			::where('id', $args["group_id"])
			->where('groups.status', true)
			->with(["users" => function($query)  {
				$query
					->where('users.status', true)
				;
				$query = \Models\Role::userAccessList($query);
			}])
			->first()
		;

		$start_at = null;
		if (
			isset($data["start_at"])
		) {
			$start_at = \Carbon\Carbon::parse($data["start_at"]);
		}

		// $group->standards()->attach([$args["standard_id"] => ['start_at' => $start_at,'cron_task'=>true, ]]);
		GroupStandards::updateOrCreate(['group_id'=>$group->id,'standard_id'=>$args["standard_id"], 'assigner_id'=>\APP\Auth::getUserId()],['start_at' => $start_at, 'cron_task'=>true, 'is_assigner_admin'=>\APP\Auth::isAdmin(), 'removed_status'=>false]);


		// Huge resource problem, need to optimise down the line
		// if ($group) {
		// 	foreach ($group->users as $key => $user) {
		// 		\Models\ApprenticeshipStandardUser::assignToStandard($user->id, $args["standard_id"], $data["start_at"]);
		// 	}
		// }


		return
			$response
		;

	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'update'));

	// assign jobs to programme
	$group->put('/jobs/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$standard = \Models\ApprenticeshipStandard::find($args["id"]);
		$module_ids = isset($data["module_ids"]) ? $data["module_ids"] : [];
		$standard->Designations()->attach($module_ids);
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons-users', 'insert'));

	// remove jobs from programme
	$group->put('/jobs/delete/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$standard = \Models\ApprenticeshipStandard::find($args["id"]);
		$module_ids = isset($data["module_ids"]) ? $data["module_ids"] : [];
		$standard->Designations()->detach($module_ids);
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons-users', 'disable'));


	// Remove user from standard
	$group->delete("/{standard_id:[0-9]+}/user/{user_id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		\Models\ApprenticeshipStandardUser::removeFromStandard($args["user_id"], $args["standard_id"]);

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'disable'));


	// Remove department and users from standard
	$group->delete("/{standard_id:[0-9]+}/department/{department_id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();


		DepartmentStandard::where("standard_id",$args["standard_id"])
			->where(function ($query) {
				if (!\APP\Auth::isAdmin()) {
					$query
						->where("assigner_id", \APP\Auth::getUserId())
						->orwhere("assigner_id", null)
					;
				}
			})
			->where("department_id", $args["department_id"])
			->update(["removed_status" => "1", "cron_task" => true, "assigner_id" => \APP\Auth::getUserId(),"is_assigner_admin" => \APP\Auth::isAdmin()]);

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'disable'));


	// Remove group and users from standard
	$group->delete("/{standard_id:[0-9]+}/group/{group_id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();


		// Get all department users.
		$group = \Models\Group
			::where('id', $args["group_id"])
			->where('groups.status', true)
			->with(["users" => function($query)  {
				$query
					->where('users.status', true)
				;
				$query = \Models\Role::userAccessList($query);
			}])
			->first()
		;

		// $group->standards()->detach([$args["standard_id"]]);

		\Models\GroupStandards
			::where("standard_id", $args["standard_id"])
			->where(function ($query) {
				if (!\APP\Auth::isAdmin()) {
					$query
						->where("assigner_id", \APP\Auth::getUserId())
						->orwhere("assigner_id", null)
					;
				}
			})
			->where("group_id", $args["group_id"])
			->update(
				[
					"removed_status" => "1",
					"cron_task" => true,
					"assigner_id" => \APP\Auth::getUserId(),
					'is_assigner_admin' => \APP\Auth::isAdmin()
				]
			)
		;


		// Huge resource problem, need to optimise down the line
		// if ($group) {
		// 	foreach ($group->users as $key => $user) {
		// 		\Models\ApprenticeshipStandardUser::removeFromStandard($user->id, $args["standard_id"]);
		// 	}
		// }

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'disable'));

	//get all users assigned to standard
	$group->get('/{standard:[0-9]+}/users/list', function (Request $request, Response $response, $args) {
		$query = \Models\ApprenticeshipStandardUser
			::where('standard_id', '=', $args["standard"])
			->get()
		;

		$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'select'));

	// get all users that ae not managers/admins and ar not assigned to any standard.
	$group->post('/missing-users', function (Request $request, Response $response, $args) {

		if (
			!\APP\Auth::isAdmin() &&
			!\APP\Auth::isCd() &&
			!\APP\Auth::isManager()
		) {
			return \APP\Tools::returnCode($request, $response, 403);
		}

		$params = $request->getParsedBody();
		$query = \Models\User
			::whereHas('role', function ($query) {
				$query
					->where('status', true)
				;
			})
			->where('status', true)
			->whereDoesntHave('Standards', function ($query) {
				$query
					->where('apprenticeship_standards.status', 1)
				;
			})
			->select('id', 'username', 'fname', 'lname', 'email', 'role_id', 'company_id')
		;

		$query = \Models\Role::userAccessList($query);

		if (isset($params["search"]["refresh"])) {
			unset($params["search"]["refresh"]);
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'select'));

	// get all users from standard using pagination
	$group->post('/users/{standard:[0-9]+}', function (Request $request, Response $response, $args) {

		$params = $request->getParsedBody();

		if (isset($params["search"]["standard_id"])) {
			unset($params["search"]["standard_id"]);
		}

		$query = \Models\User::selectRaw("users.*")
			->where("users.status", ">", "0")
			->with(["company" => function($query)  {
				$query->select(["companies.id", "companies.name"]);
			}])
			->with(["groups" => function($query) {
				$query->select("groups.id");
			}])
			->with(['standards' => function($query) use ($args) {
				$query
					->where('apprenticeship_standards.id', '=', $args["standard"])
				;
			}])
			->select('id', 'fname', 'lname', 'email')
		;

		if (isset($params["search"]) && isset($params["search"]["show_assigned"])) {
			$query->whereHas('standards', function ($query) use ($args) {
				$query
					->where('apprenticeship_standards.id', '=', $args["standard"])
				;
			});
			unset($params["search"]["show_assigned"]);
		}

		$query = \Models\Role::userAccessList($query);

		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'select'));


	// get all departments from standard using pagination
	$group->post('/departments/{standard:[0-9]+}', function (Request $request, Response $response, $args) {

		$params = $request->getParsedBody();

		if (isset($params["search"]["standard_id"])) {
			unset($params["search"]["standard_id"]);
		}

		$query = \Models\Department
			::where("departments.status", 1)
			->with(["company" => function($query)  {
				$query->select(["companies.id", "companies.name"]);
			}])
			->with(['standards' => function($query) use ($args) {
				$query
					->where('apprenticeship_standards.id', $args["standard"])
				;
			}])
			->select('id', 'name', 'email')
		;

		if (isset($params["search"]) && isset($params["search"]["show_assigned"])) {
			$query->whereHas('standards', function ($query) use ($args) {
				$query
					->where('apprenticeship_standards.id', $args["standard"])
				;
			});
			unset($params["search"]["show_assigned"]);
		}

		$query = \Models\Role::departmentAccessList($query);

		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'select'));

	// Get all groups from standard
	$group->post('/groups/{standard_id:[0-9]+}', function (Request $request, Response $response, $args) {

		$params = $request->getParsedBody();

		if (isset($params["search"]["standard_id"])) {
			unset($params["search"]["standard_id"]);
		}

		$query = \Models\Group
			::where("groups.status", 1)
			->with(['standards' => function($query) use ($args) {
				$query
					->where('apprenticeship_standards.id', $args["standard_id"])
				;
			}])
			->select('id', 'name')
		;

		if (
			isset($params["search"]) &&
			isset($params["search"]["show_assigned"])
		) {
			$query->whereHas('standards', function ($query) use ($args) {
				$query
					->where('apprenticeship_standards.id', $args["standard_id"])
				;
			});
			unset($params["search"]["show_assigned"]);
		}


		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'select'));

	// Disable standard
	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {

        $apprenticeshipstandard = \Models\ApprenticeshipStandard::find($args["id"]);


        $assignedUsers = \Models\ApprenticeshipStandardUser::where("standard_id",$args["id"])->get();
          if($assignedUsers){

            foreach($assignedUsers AS $assignedUser){
                \Models\ApprenticeshipStandardUser::removeFromStandard($assignedUser->user_id, $args["id"],true);

            }
          }
        $apprenticeshipstandard->status = 0;
        $apprenticeshipstandard->save();


		/** Softdelete Learning Modules */
		$user_form_list=\Models\UserFormTemplateWorkflowRelations::
			where(function ($query) use ($args) {
				$query->where("type_id", $args["id"])
				->where("type","programme");
			})
			->get();
		if($user_form_list){
			foreach($user_form_list AS $user_form_list_val){
				\Models\UserForm::deleteUserForms($user_form_list_val->user_form_id);
			}
		}


		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'disable'));

	// Enable standard
	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$apprenticeshipstandard = \Models\ApprenticeshipStandard::find($args["id"]);
		$apprenticeshipstandard->status = 1;
		$apprenticeshipstandard->save();

		/** Softdelete Learning Modules */
		$user_form_list=\Models\UserFormTemplateWorkflowRelations::withTrashed()
			->where(function ($query) use ($args) {
				$query->where("type_id", $args["id"])
				->where("type","programme");
			})
			->get();
		if($user_form_list){
			foreach($user_form_list AS $user_form_list_val){
				\Models\UserForm::deleteUserForms($user_form_list_val->user_form_id, false);
			}
		}

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'disable'));

	// Add new standard
	$group->post('/new', function (Request $request, Response $response) {

		$data = $request->getParsedBody();

		$apprenticeshipstandard = new \Models\ApprenticeshipStandard;

		$fields = [
			"route",
			"name",
			"completion_months",
			"category_id",
			"level",
			"funding",
			"status",
			"sort",
			"reference_code",
			"periodic_review",
			"review_interval",
			"ilr_learning_delivery",
			"learning_delivery_type",
			"delivery",
			"working_hours",
			"type",
			"periodic_repeat",
			"periodic_repeat_months",
			"course_credits",
			"badge",
			"repetition_period",
			"default_start_month",
			"default_skill_repetition_period",
			'print_certificate',
			"self_enroll",
			"cost",
			"description",
			"completion_criteria",
			"completion_resources",
			"completion_time",
		];

		foreach($fields as $field) {
			if (isset($data[$field])) {
				if ($field == 'delivery') {
					$data[$field] = json_encode($data[$field]);
				}
				$apprenticeshipstandard->$field = $data[$field];
			}
		}

		// If months are given, get configuration for "Training/work ratio" and calculate working hours.
		if (isset($data['completion_months'])) {
			$apprenticeshipstandard->working_hours = \Models\ApprenticeshipStandard::WorkingHours($data['completion_months']);
		}

		if ($data['type']=="Skills Monitoring"){
			$apprenticeshipstandard->completion_months = "1";
		}
		$apprenticeshipstandard->default_skill_repetition_period = $data['default_skill_repetition_period'] ?? null;
		$apprenticeshipstandard->repetition_period = $data['repetition_period'] ?? null;
		$apprenticeshipstandard->default_start_month = $data['default_start_month'] ?? null;
		$apprenticeshipstandard->status = 1;
		$apprenticeshipstandard->save();
		if(isset($data['custom_field']))
		{
			Form::saveCustomForm($data['custom_field'],'programme',$apprenticeshipstandard->id);
		}
		// add outcome and criteria specified in: https://bitbucket.org/emilrw/scormdata/issues/320
		if (isset($data['administrationPaperwork']) && $data['administrationPaperwork']) {
			\Models\ApprenticeshipStandard::addAdministrationPaperwork($apprenticeshipstandard->id);
		}

		// If "Link to ILR Learning Delivery" is checked and "Learning Delivery Type" chosen add Specified delivery for users assigned to standard.
		// Not really applicable to when standard/programme is added.
		if (isset($data['ilr_learning_delivery']) && $data['ilr_learning_delivery']) {

		}

		if ($apprenticeshipstandard) {
			$response->getBody()->write((string)$apprenticeshipstandard->id);
			return $response;
		} else {
			return $response;
		}
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'insert'));

	// Get all standards, ALL of them!
	$group->get('/all', function (Request $request, Response $response) {
		session_write_close();
		$user = \APP\Auth::getUser();

		$query = \Models\ApprenticeshipStandard
			::where("status", true)
			->with(['issuecategories' => function ($query) use ($user) {
				$query
					->where('status', true)
				;
			}])
		;

		if (
			!\APP\Auth::isAdminInterface()
		) {
			$query
				->whereHas('users', function ($query) use($user) {
					$query
						->where('users.id', $user->id)
					;
				})
				->with(['standarduser' => function ($query) use ($user) {
					$query
						->where('user_id', $user->id)
						->select('standard_id', 'user_id', 'time_spent', 'percentage', 'percentage_time', 'working_hours')
					;
				}])
			;
		}

		$query = $query
			->get()
		;

		// If user has custom week_hours, recalculate "working_hours".
		if ($user->week_hours && $user->week_hours > 0) {
			foreach ($query as $key => $standard) {
				$standard->working_hours = \Models\ApprenticeshipStandard::WorkingHours($standard->completion_months, $user);
			}
		}

		// If enableProgrammeTitlesLearnerLandingPage is true, find out all categories related to programme via resources
		if (
			\APP\Tools::getConfig('enableProgrammeTitlesLearnerLandingPage') &&
			\APP\Auth::isLearner()
		) {
			foreach ($query as $key => $standard) {
				if (
					$standard->standarduser &&
					isset($standard->standarduser->user_id)
				) {
					$standard->categories = \Models\ApprenticeshipStandard::getResourcesCategories($standard);
				}
			}
		}

		$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'select'));

	// Get all enrollable standards
	$group->get('/enrollable', function (Request $request, Response $response) {
		session_write_close();

		$user = \APP\Auth::getUser();
		$enrollableQuery = \Models\ApprenticeshipStandard::where("status", true)
			->where('self_enroll', true)
			->whereDoesntHave('StandardUser', function ($query) use ($user) {
				$query->where('user_id', $user->id);
			})->with(['issuecategories' => function ($query) use ($user) {
				$query->where('status', true);
			}]);
		$enrollableStandards = $enrollableQuery->get();

		$response->getBody()->write(json_encode($enrollableStandards));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'select'));

	// Get specific standard with categories/issues
	$group->get('/{standard_id:[0-9]+}[{link:[\/a-z]*}{link_id}]', function (Request $request, Response $response, array $args) {
		$user_id = \APP\Auth::getUserId();

		$data = \Models\ApprenticeshipStandard::where("status", true)
			->where('id', $args['standard_id'])
			->with(["Issuecategories" => function ($query) use ($args) {
				$query
					->orderBy('sort', 'ASC')
					->where('status', 1)
					->with(["Issues" => function ($query) use ($args) {
						$query
							->orderBy('sort', 'ASC')
							->where('status', true)
							->where('parent_id', 0)
							->with(["Children" => function ($query) use ($args) {
								$query
									->orderBy('sort', 'ASC')
									->where('status', true)
									->with(["Children" => function ($query) use ($args) {
										$query
											->orderBy('sort', 'ASC')
											->where('status', true)
											->where('parent_id', '>', 0)
										;
										$query = \Models\Schedule::countAndConditions($query, $args);

										// If link is resource, return count of this resource assigned to this issue
										$query = \Models\LearningModule::countAndConditions($query, $args);
									}])
								;

								// If link is resource, return count of this resource assigned to this issue
								$query = \Models\LearningModule::countAndConditions($query, $args);
							}])
						;
						$query = \Models\Schedule::countAndConditions($query, $args);

						// If link is resource, return count of this resource assigned to this issue
						$query = \Models\LearningModule::countAndConditions($query, $args);
					}])
				;
			}])
		;

		if (\APP\Auth::isLearner()) {
			$data = $data->whereHas('users', function ($query) use($user_id) {
				$query
					->where('users.id', '=', $user_id)
				;
			})
			;
		}

		$data = $data
			->first()
		;

		// Loop results and set selected if count is true and individual resource is passed
		if (
			isset($args["link"]) &&
			$args["link"] == '/resources/' &&
			isset($args["link_id"])
		) {
			$resource_ids = json_decode($args["link_id"], TRUE);
			$resource_ids_count = count($resource_ids);
			foreach ($data->Issuecategories as $key => $issuecategory) {
				foreach ($issuecategory->Issues as $key => $issue) {
					$issue->selected = false;
					if ($issue->learning_modules_count == $resource_ids_count) {
						$issue->selected = true;
					}
					foreach ($issue->Children as $key => $subIssue) {
						$subIssue->selected = false;
						if ($subIssue->learning_modules_count == $resource_ids_count) {
							$subIssue->selected = true;
						}
					}
				}
			}
		}


		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'select'));


	// Get all specific user's standards!
	$group->get('/user/{user_id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$user = \Models\User::find($args['user_id']);

		if (
			\APP\Auth::isAdmin() ||
			\APP\Auth::isManagerOf($user->id) ||
			\APP\Auth::accessAllLearners() ||
			$user->id == \APP\Auth::getUserId()
		) {
			$data = \Models\ApprenticeshipStandard::
				where("status", ">", 0)
				->whereHas('users', function ($query) use ($user) {
					$query
						->where('users.id', $user->id)
					;
				})
				->get()
			;
		} else {
			return \APP\Tools::returnCode($request, $response, 403);
		}
		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());


	/*Exclude outcome from progress*/
	$group->post('/exclude_outcome/{id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();
		$outcome = \Models\ApprenticeshipIssueCategories::find($args['id']);
		if ($outcome) {
			$outcome->exclude_outcome = $data["exclude_outcome"];
			$outcome->save();

			$response->getBody()->write(json_encode($outcome->exclude_outcome));
			return $response->withHeader('Content-Type', 'application/json');
		} else {
			return \APP\Tools::returnCode($request, $response, 403);
		}
	})->add(\APP\Auth::getSessionCheck());

	// Enable/Disable "Learning Hours Required"
	$group->put('/working_hours_custom/{id:[0-9]+}/{state:0|1}[/{working_hours:[0-9]+}]', function (Request $request, Response $response, array $args) {

		$query = \Models\ApprenticeshipStandardUser::find($args["id"]);
		$query->working_hours_custom = $args["state"];
		if (
			isset($args["working_hours"])
		) {
			$query->working_hours = $args["working_hours"];
		}
		$query->save();

		// reupdate progresses, statistics, etc
		\Models\ApprenticeshipStandardUser::updateTimeSpent([$query->user_id]);

		return
			$response
		;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'update'));

	// Download standards in excel format
   $group->post('/list{download:[\/a-z]*}', function (Request $request, Response $response, array $args) {

		$params = $request->getParsedBody();

		$query = \Models\ApprenticeshipStandard
			::with(['category' => function($query) {

			}])
			->withCount(['Users' => function ($query) {
				$query
					->where('users.status', true)
				;
			}])
		;

		// If manager, check if there is need to filter out categories
		$query = \Models\ManagerLearningModuleCategory::checkManagerAccessToCategories($query);

		$query = \Models\Schedule::countAndConditions($query, $params);

		if (isset($params["search"]) && is_array($params["search"])) {
			unset($params["search"]["refresh"]);
			foreach($params["search"] as $field => $value) {
				if (is_int($value)) {
					$query->where($field, "=", $value);
				} else {
					$query->where($field, "LIKE", "%{$value}%");
				}
			}
		}


		if (isset($params["sort"]) && isset($params["sort"]["predicate"]) && isset($params["sort"]["reverse"])) {
			$query->orderBy($params["sort"]["predicate"], $params["sort"]["reverse"] ? "DESC" : "ASC");
		}

		if (isset($args["download"]) && $args["download"] == "/download") {
			$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

			$export_fields = [
				"ID" => "id",
				"%%programme%% Name" => "name",
				"Category" => "category.name",
				"Type" => "type",
				"Number of users" => 'users_count',
				"Funding" => 'funding',
			];

			$download_file_name = uniqid("apprenticeshipstandards.list.") . ".xlsx";

			\APP\Tools
				::generateExcelDownload(
					$data,
					$export_fields,
					$this->get('settings')["LMSTempPath"] . $download_file_name
				)
			;

			$response->getBody()->write(json_encode($download_file_name));
			return $response->withHeader('Content-Type', 'application/json');
		} elseif (isset($args["download"]) && $args["download"] == "/print") {
			$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

			$response->getBody()->write(json_encode($data));
			return $response->withHeader('Content-Type', 'application/json');

		} else {
			$p = \APP\SmartTable::searchPaginate($params, $query);
		}

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'select'));

	// update pause programme status/dates
	$group->put("/pause/{asu_id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		$entry = \Models\ApprenticeshipStandardUser::find($args['asu_id']);

		// If current user is admin or manager of user being updated.
		if (
			$entry &&
			\APP\Auth::isAdmin() ||
			\APP\Auth::isManagerOf($entry->user_id) ||
			\APP\Auth::accessAllLearners()
		) {
			$entry->paused = $data['paused'];

			$entry->paused_start = null;
			if (isset($data['paused_start'])) {
				$entry->paused_start = \Carbon\Carbon::parse($data['paused_start']);
			}

			$entry->paused_end = null;
			if (isset($data['paused_end'])) {
				$entry->paused_end = \Carbon\Carbon::parse($data['paused_end']);
			}

			$entry->save();

			if (isset($data['update_end_date']))
			{
				\Models\ApprenticeshipStandard::UpdateUserStartDate($entry->user_id, $entry->standard_id, $entry->start_at, $entry->id);
			}

		} else {
			$response = $response
				->withStatus(403)
				->write('unauthorised')
			;
		}
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'update'));

    $group->post("/time-spend-log/{asu_id:[0-9]+}{download:[\/a-z]*}" , function (Request $request, Response $response, $args) {

        $params = $request->getParsedBody();
        $entry = \Models\ApprenticeshipStandardUser::find($args['asu_id']);

        $query = $entry->timeSpendLogs();
        // $p = \APP\SmartTable::searchPaginate($params, $query);
        // $data = $p->toArray();

        if (isset($args["download"]) && $args["download"] == "/download") {
            $data = \APP\SmartTable::searchPaginate($params, $query, false, false);

            $export_fields = [
                "Name" => "name",
                "Type" => "type",
                "Date Completed" => "date_completed",
                "Time Spent (minutes)" => 'time_spent_minutes',
            ];

            $download_file_name = uniqid("timespendlog.list.") . ".xlsx";

            \APP\Tools::generateExcelDownload($data, $export_fields, $this->get('settings')["LMSTempPath"] . $download_file_name);

            return $response->withHeader('Content-Type', 'application/json')->write(json_encode($download_file_name));

        } elseif (isset($args["download"]) && $args["download"] == "/print") {
            $data = \APP\SmartTable::searchPaginate($params, $query, false, false);
            return $response->withHeader('Content-Type', 'application/json')->write(json_encode($data));
        } else {
            $p = \APP\SmartTable::searchPaginate($params, $query);
        }

        return $response->withHeader('Content-Type', 'application/json')->write($p->toJson());

    })->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'select'));

	$group->post("/skill",function (Request $request,Response $response) {
		$params = $request->getParsedBody();
		$skills = \Models\LearningModule::
			select('id', 'name', 'description')
			->where('is_skill',1)
		;

		if (
			isset($params["skill_subject"])
			&& $params["skill_subject"]
		) {
			$skills = $skills->whereHas('ApprenticeshipIssuesLearningModules',function ($query) use ($params) {
				$query->whereHas('Issue',function ($query) use ($params)  {
					$query->whereHas('IssueCategory',function ($query) use ($params)  {
						$query->whereHas('Standard',function ($query) use ($params)  {
							$query->where('apprenticeship_standards.id','=',$params["skill_subject"]);
						});
					});
				});
			});
		}
		$skills = $skills->get();
		$response->getBody()->write(json_encode($skills));
		return $response->withHeader('Content-Type', 'application/json');
	});

	$group->post("/skill-monitor",function(Request $request,Response $response){
		$user = Auth::getUser();
		$params = $request->getParsedBody();

		$additional_search_params = $params["search"]["additionalSearchParams"] ?? [];
		unset($params["search"]["additionalSearchParams"]);

		if (isset($params["search"]["refresh"])) {
			unset($params["search"]["refresh"]);
		}

		if (isset($params["search"]["user_forms__user_form_status"])) {
			unset($params["search"]["user_forms__user_form_status"]);
		}

		if (isset($params["search"]["enabled"])) {
			unset($params["search"]["enabled"]);
		}

		$skillNameFilter = "";
		if (isset($params["search"]) && isset($params["search"]["skill"]))
		{
			$skillNameFilter = $params["search"]["skill"];
			unset($params["search"]["skill"]);
		}

		$skillStandardFilter = false;
		if (
			isset($additional_search_params)
			&& isset($additional_search_params["skill_subjects"])
			&& $additional_search_params["skill_subjects"] != ""
		) {
			$skillStandardFilter = $additional_search_params["skill_subjects"];
		}


		$query = User::select(
				    'users.id',
				    'users.fname',
				    'users.lname',
				    'users.department_id',
				    'departments.name as department_name'
				)
				->validuser()
				->with('department')
				->leftJoin('departments', 'departments.id', 'users.department_id')
			->whereHas('LearningResults', function ($query) use ($skillStandardFilter, $skillNameFilter) {
				$query
					->with('Module')
					->join("user_learning_modules", function($join) {
					$join
						->on("user_learning_modules.user_id", "=", "learning_results.user_id")
						->on("user_learning_modules.learning_module_id", "=", "learning_results.learning_module_id")
						->whereNull('user_learning_modules.deleted_at')
					;
				})
				->where('learning_results.refreshed', 0)
				->whereHas('Module', function ($query) use ($skillStandardFilter, $skillNameFilter) {
					if ($skillStandardFilter) {
						$query = $query->whereHas('ApprenticeshipIssuesLearningModules', function ($query) use ($skillStandardFilter) {
							$query->whereHas('Issue',function ($query) use ($skillStandardFilter)  {
								$query->whereHas('IssueCategory',function ($query) use ($skillStandardFilter) {
									$query->whereHas('Standard',function ($query) use ($skillStandardFilter) {
										$query->where('apprenticeship_standards.id','=',$skillStandardFilter);
									});
								});
							});
						});
					}
					$query
						->validresource()
						->where('is_skill', '1')
					;

					if ($skillNameFilter != "") {
						$query->where('learning_modules.name', 'LIKE', '%'. $skillNameFilter . '%');
					}

				});
			})
		;

		if (!\APP\Auth::accessAllLearners()) {
            $assignedUser = ManagerUser::join('users', 'users.id', '=', 'manager_users.user_id')
                ->where('manager_users.manager_id', $user->id)
                ->where('users.status', true)
                ->pluck('users.id');

			if (\APP\Tools::getConfig('ManagerAccessToLinkedUsers')) {
				$cross_linked_user_ids = \Models\User
					::whereIn('email',
						\Models\User
							::select('email')
							->whereIn('id', $assignedUser)
							->get()
					)
					->validuser()
					->pluck('id')
					->toArray()
				;
				$assignedUser = $cross_linked_user_ids;

				// Find all manager profiles and add them here as well
				$manager_profiles = \Models\User
					::validuser()
					->where('email', \APP\Auth::getUserEmail())
					->pluck('id')
					->toArray()
				;
				if (count($manager_profiles) > 0) {
					$assignedUser = array_merge($assignedUser, $manager_profiles);
				}
			}
			$query = $query
				->whereIn('users.id', $assignedUser)
			;
		}

	// no of lapsed skills sorting
	  if (isset($params["sort"]) && isset($params["sort"]["predicate"]) && isset($params["sort"]["reverse"]))
	  {
		  if($params["sort"]["predicate"] == "score_first")
		  {
			  $query->withCount(['LearningResults' => function ($query) use ($params, $additional_search_params) {
				  $query->whereHas('Module', function ($query) {
					  $query->where('is_skill', '=', '1');
				  });

				  if (isset($additional_search_params["period_from"])) {
					  $query->where('due_at', '>=', $additional_search_params["period_from"]);
				  }
				  if (isset($additional_search_params["period_to"])) {
					  $query->where('due_at', '<=', $additional_search_params["period_to"]);
				  }

				  $query->whereRaw('DATEDIFF(due_at,CURDATE()) <= 0 AND completion_status != "completed"');
			  }])->orderBy('learning_results_count', $params["sort"]["reverse"] ? "DESC" : "ASC");
			  unset($params["sort"]["predicate"]);
			  // dynamic skills sorting
		  } elseif (preg_match('/skill_*/',$params['sort']['predicate']))
		  {
			  $skill_id = str_replace("skill_", "", $params['sort']['predicate']);;
			  $order = $params["sort"]["reverse"] ? "DESC" : "ASC";

			  $query->leftJoin('learning_results', function ($join) use ($additional_search_params, $skill_id) {
				  $join->on('learning_results.user_id', '=', 'users.id')
					  ->where('learning_results.learning_module_id',$skill_id);

				  if (isset($additional_search_params["period_from"])) {
					  $join->where('due_at', '>=', $additional_search_params["period_from"]);
				  }
				  if (isset($additional_search_params["period_to"])) {
					  $join->where('due_at', '<=', $additional_search_params["period_to"]);
				  }
			  });
			  $query->orderBy("learning_results.due_at", $order);
			  unset($params["sort"]["predicate"]);
		  }
	  }
		if (isset($params["search"]) && isset($params["search"]["department"])) {
			$query->whereHas('Department', function ($query) use ($params) {
				$query->where('name', 'LIKE', '%'. $params["search"]["department"] . '%');
			});
			unset($params["search"]["department"]);
		}


		if (isset($additional_search_params)){
			// Department
			if (isset($additional_search_params["department_id"]) && $additional_search_params["department_id"]) {
				$query = $query->where('users.department_id', '=', $additional_search_params["department_id"]);
			}

			if (isset($additional_search_params["location_id"]) && $additional_search_params["location_id"]) {
				$query = $query->where('users.location_id', '=', $additional_search_params["location_id"]);
			}

			if (isset($additional_search_params["group_id"]) && $additional_search_params["group_id"]) {
				$query->whereHas('Groups', function ($query) use ($additional_search_params) {
					$query->where('groups.id', $additional_search_params["group_id"]);
				});
			}

			// Company
			if (isset($additional_search_params["company_id"]) && $additional_search_params["company_id"]) {
				$query = $query->where('users.company_id', '=', $additional_search_params["company_id"]);
			}

			// Watch
			if (isset($additional_search_params["watch_id"]) && $additional_search_params["watch_id"]) {
				$query = $query->where('users.watch_id', '=', $additional_search_params["watch_id"]);
			}
		}


		$p = \APP\SmartTable::searchPaginate($params, $query);
		$data = $p->toArray();
		$outData = [];

		foreach ($data['data'] as $key => $user) {
			$userData = [
				'id' => $user['id'],
				'fname' => $user['fname'],
				'lname' => $user['lname'],
				'department_name' => $user['department']['name'] ?? null, // Safe fallback
				'skills' => [],
			];


			$user['learning_results'] = \Models\LearningResult
				::select(
					'learning_results.id',
					'learning_results.due_at',
					'learning_results.grade',
					'learning_results.completion_status',
					'learning_results.sign_off_trainee',
					'learning_results.sign_off_manager',
					'learning_results.user_id',
					'learning_results.learning_module_id',
					'learning_results.created_at',
					'learning_results.sign_off_manager_at  as completed_at',
					DB::raw('(SELECT COUNT(*) FROM learning_results AS lr WHERE lr.learning_module_id = learning_results.learning_module_id AND lr.user_id = learning_results.user_id AND lr.refreshed = 1) AS refreshed_count'),
					DB::raw('(select lr.created_at from  learning_results AS lr WHERE lr.learning_module_id = learning_results.learning_module_id AND lr.user_id = learning_results.user_id AND lr.refreshed = 1 ORDER BY lr.id DESC LIMIT 1) as previous_created_at'),
                    DB::raw('(select lr.sign_off_manager_at from  learning_results AS lr WHERE lr.learning_module_id = learning_results.learning_module_id AND lr.user_id = learning_results.user_id AND lr.refreshed = 1 ORDER BY lr.id DESC LIMIT 1) as previous_completed_at'),
                    DB::raw("(
    SELECT CONCAT(u.fname, ' ', u.lname)
    FROM learning_results AS lr
    JOIN users AS u ON u.id = lr.sign_off_manager_by
    WHERE lr.learning_module_id = learning_results.learning_module_id
      AND lr.user_id = learning_results.user_id
      AND lr.refreshed = 1
    ORDER BY lr.id DESC
    LIMIT 1
) as previous_completed_by")
				)
				->where('learning_results.user_id', $user['id'])
				->with('Module')
				->join("user_learning_modules", function($join) {
					$join
						->on("user_learning_modules.user_id", "=", "learning_results.user_id")
						->on("user_learning_modules.learning_module_id", "=", "learning_results.learning_module_id")
						->whereNull('user_learning_modules.deleted_at')
					;
				})
				->where('refreshed', 0)
				->orderBy('due_at')
				->whereHas('Module',function ($query) use ($additional_search_params, $skillNameFilter, $skillStandardFilter) {
					if ($skillStandardFilter) {
						$query = $query->whereHas('ApprenticeshipIssuesLearningModules', function ($query) use ($skillStandardFilter) {
							$query->whereHas('Issue',function ($query) use ($skillStandardFilter)  {
								$query->whereHas('IssueCategory',function ($query) use ($skillStandardFilter)  {
									$query->whereHas('Standard',function ($query) use ($skillStandardFilter)  {
										$query->where('apprenticeship_standards.id','=',$skillStandardFilter);
									});
								});
							});
						});
					}
					if ($skillNameFilter != "")  {
						$query->where('learning_modules.name', 'LIKE', '%'. $skillNameFilter . '%');
					}
					$query
						->where('is_skill','=','1')
						->validresource()
					;

					if (
						isset($additional_search_params["category_id"]) &&
						$additional_search_params["category_id"]
					) {
						$query->where('category_id','=',$additional_search_params["category_id"]);
					}
				})
			;

			if(isset($additional_search_params["period_from"])){
				$user['learning_results']->where('due_at','>=',$additional_search_params["period_from"]);
			}

			if(isset($additional_search_params["period_to"])){
				$user['learning_results']->where('due_at','<=',$additional_search_params["period_to"]);
			}
			$user['learning_results'] = $user['learning_results']->get();
		
			foreach ($user['learning_results'] as $skill)
			{
				
				$dueDate = UserApprenticeshipStandardService::getDueDateFormatted($skill->toArray());
				$priority = UserApprenticeshipStandardService::getSkillPriority($skill->toArray(), $dueDate);
				array_push($userData['skills'], [
					'id' => $skill['id'],
					'due_at' => $skill['due_at'],
					'user_id' => $skill['user_id'],
					'grade' => $skill['grade'],
					'learning_module_id' => $skill['learning_module_id'],
					'completion_status' => $skill['completion_status'],
					'sign_off_trainee' => $skill['sign_off_trainee'],
					'sign_off_manager' => $skill['sign_off_manager'],
					'module_id' => $skill['module']['id'],
					'module_name' => $skill['module']['name'],
					'module_description' => $skill['module']['description'],
					'module_category_id' => $skill['module']['category_id'],
					'is_skill' => true,
					'combined_name' => $user['fname'] . ' ' . $user['lname'] . ' - ' . $skill['module']['name'],
					"refreshed_count" => $skill['refreshed_count'],
					'previous_created_at' => $skill['previous_created_at'],
					'previous_completed_at' => $skill['previous_completed_at'],
					'repetition_period' => $skill['module']['repetition_period'],
					'created_at' => $skill['created_at'],
                    'completed_at' => $skill['completed_at'],
                    'previous_completed_by' => $skill['previous_completed_by'],
					'mandatory_certificate_upload' => $skill['module']['mandatory_certificate_upload'],
					'skill_priority' => $priority, // field for sorting
				]);
			}
			usort($userData['skills'], function ($a, $b) {
				if ($a['skill_priority'] === $b['skill_priority']) {
					return strtotime($a['due_at']) <=> strtotime($b['due_at']);
				}
				return $a['skill_priority'] <=> $b['skill_priority'];
			});

			array_push($outData, $userData);
		}
		$data['data'] = $outData;
		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'select'));

	$group->get('/skill-subjects/all', function (Request $request, Response $response) {
		$data = \Models\ApprenticeshipStandard
			::where("status", 1)
			->where('type', 'Skills Monitoring')
			->get()
		;

		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'select'));

	$group->put("/signoff-skillmonitoring",function(Request $request,Response $response){
		$params = $request->getParsedBody();

		$insertApprenticeShipStandard = \Models\ApprenticeshipStandardUser
			::where('id', '=', $params["id"])
			->first()
		;

		if ($insertApprenticeShipStandard) {
			$insertApprenticeShipStandard->sign_off_manager='1';
			$insertApprenticeShipStandard->sign_off_manager_at= \Carbon\Carbon::now();
			$insertApprenticeShipStandard->sign_off_manager_by= \APP\Auth::getUserId();
			$insertApprenticeShipStandard->save();
		}

		$response->getBody()->write(json_encode($insertApprenticeShipStandard));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'select'));


	$group->put("/signoff-skillmonitoring-log",function(Request $request,Response $response){
		$params = $request->getParsedBody();

		$apprenticeShipStandard = \Models\ApprenticeshipStandardUser
		::where('id', '=', $params["id"])
		->first();
		$user =  \Models\User::find(\APP\Auth::getUserId());
		$role_id = $user->shadow_role_id ? $user->shadow_role_id : $user->role_id;
		$data = [
			'type' => 'Skills Monitoring',
			'type_id' => $params["standard_id"],
			'user_id' => $params['user_id'],
			'comment' => $params['comment'],
			'sign_off_user_id' => \APP\Auth::getUserId(),
			'sign_off_role_id' => $role_id,
			'status' => $params['status'],
			'sign_off_date' => \Carbon\Carbon::now(),
		];

		$signoff_log = \Models\SignoffLog::create($data);
		$apprenticeShipStandard->skill_signoff_logs = \Models\SignoffLog::with(['User', 'Role'])->find($signoff_log->id);


		$response->getBody()->write(json_encode($apprenticeShipStandard));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'select'));

	$group->post("/{id:[0-9]+}/{outcome_id:[0-9]+}/copy" , function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$standardId = $args['id'];
		$outcomeId = $args['outcome_id'];

		$user_id = \APP\Auth::getUserId();

		$apprenticeshipStandardService = new \APP\Services\ApprenticeshipStandardService();

		$copyToStandard = $apprenticeshipStandardService->getApprenticeshipStandard($data['standard_id']);

		$apprenticeshipStandard = ApprenticeshipStandard::query()
			->where('id', '=', $standardId)
			->with(['issuecategories' => function ($query) use ($user_id, $args, $data, $outcomeId) {
				$query
					->where('id', '=', $outcomeId)
					->where("status", "=", 1)
					->orderBy('sort', 'ASC')
					->with(["issues" => function ($query) use ($user_id, $args, $data) {
						$query
							->where("status", 1)
							->where('parent_id', 0)
							->orderBy('sort', 'ASC')
							->with(["Children" => function ($query) use ($user_id, $args, $data) {
								// AAAAnd repeat all that module thing here as well...
								\Models\User::getIssueModules($query, $args["id"]);
								$query
									->where("status", 1)
									->with(["Children" => function ($query) use ($user_id, $args, $data) {
										\Models\User::getIssueModules($query, $args["id"]);
										// AAAAnd repeat all that module thing here as well...
										$query
											->where("status", 1);

										\Models\ApprenticeshipIssues::getIssueModules($data, $query, $user_id);
									}]);
								;

								\Models\ApprenticeshipIssues::getIssueModules($data, $query, $user_id);
							}])
						;

						// issues listed at evidence module page, only relation to evidence modules are needed
						\Models\ApprenticeshipIssues::getIssueModules($data, $query, $user_id);
					}]);

			}])
			->first();


		$apprenticeshipStandardService->copyApprenticeshipStandardIssues($apprenticeshipStandard, $copyToStandard);

		return $response;

	})->add(\APP\Auth::getSessionCheck());
	$group->get('/certificate/{asu_id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$vars = \APP\Templates::getVariables($this->get('settings'));

		$apprenticeshipStandardUser = \Models\ApprenticeshipStandardUser::query()
			->with(['Standard', 'User'])
			->where('id', '=', $args["asu_id"])
			->first();

		$apprenticeshipStandardName = $apprenticeshipStandardUser->Standard->name;

		if ($apprenticeshipStandardUser->completion_status != "completed") {
			return \APP\Tools::returnCode($request, $response, 403);
		}

		$vars["Logo"] = $this->get('settings')["DefaultLogo"];
		$vars["StandardName"] = $apprenticeshipStandardName;
		$vars["CompletionDate"] = \Carbon\Carbon::parse($apprenticeshipStandardUser->completed_at)->format(\APP\Tools::getConfig('defaultDateFormat'));

		$vars["LMSTitle"] = \APP\Tools::getConfig('LMSTitle') ? \APP\Tools::getConfig('LMSTitle') : $this->get('settings')["LMSTitle"];
		$vars["DefaultCertificateMessageTop"] = \APP\Tools::getConfig('CertificateMessageTop');
		$vars["DefaultCertificateMessageBottom1"] = \APP\Tools::getConfig('CertificateMessageBottom1');
		$vars["DefaultCertificateMessageBottom2"] = \APP\Tools::getConfig('CertificateMessageBottom2');

		$vars["UserFname"]  = $apprenticeshipStandardUser->User->fname;
		$vars["UserLname"]  = $apprenticeshipStandardUser->User->lname;
		$vars["UserDepartmentName"]  = $apprenticeshipStandardUser->User->department ? $apprenticeshipStandardUser->User->department->name : '';

		if ($apprenticeshipStandardUser->User->company && $apprenticeshipStandardUser->User->company->name) {
			$vars["UserCompanyName"]  = $apprenticeshipStandardUser->User->company->name;
		}

		return $this->get('view')->render($response, 'html/certificate-programme.html', $vars);

	})->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'select'));


	$group->get("/ai-suggest-structure/{id:[0-9]+}", function (Request $request, Response $res, $args) {

        $client = new Client([
            'base_uri' => Tools::getConfig('ProgrammeCreationAIURL')
        ]);
        $standard = \Models\ApprenticeshipStandard::
        select('name', 'description', 'type')
            ->where("status", 1)
            ->where('id', $args['id'])
            ->first();
        $url = 'https://api.openai.com/v1/chat/completions';
        $model = 'gpt-3.5-turbo';
        $temperature = 0.4;
        $systemMessage = "You are a professional curriculum developer.\nThe task before you is to create the curriculum structure for a learning program provided by the user in the form of a JSON object that has a title and description.\nCreate a list of modules, that would cover the topic of a learning program, then create a list of submodules for each of the modules, to cover the topic of module in greater detail.\nReturn result in a JSON format: list of modules as an array named \"modules\" of objects, each containing a title as \"title\" and a list of submodules as an array of object with title and description(with 500 words about the submodule) named \"submodules\".";
        $userMessage = json_encode($standard);
        $authToken = $GLOBALS['openAIauth'];

            $response =  \APP\Tools::callOpenAI($url, $model, $temperature, $systemMessage, $userMessage, $authToken);

        $response = json_decode($response);

        $data = [];
        if ($response && $response->choices && $response->choices[0] && $response->choices[0]->message && $response->choices[0]->message->content) {
            $json_data = $response->choices[0]->message->content ;
            preg_match( "/`json\n(.*?)\n`/s", $json_data, $matches);
            if (isset($matches[1])) {
                $json_data = $matches[1];

            }
            $data = json_decode($json_data);
            $settings = $this->get('settings');
            $databaseName = isset($settings['database']['database']) ? $settings['database']['database'] : null;
			$lesson_ids = [];
            if($standard->type == 'Skills Monitoring'){
                $type = 'skill';
            }else{
                $type = 'learning_module';
            }

            foreach ($data->modules as $module_key => $module) {
                foreach ($module->submodules  as $submodule_key => $submodule) {
                    $query = $submodule->title.":".$submodule->description ;
                    $response = $client->post('/query', [
                        'json' => [
                            'query' => $query,
                            'client_site' =>  $databaseName,
							'limit'=>intval(Tools::getConfig('ProgrammeCreationAILimit')),
							'similarity'=>floatval(Tools::getConfig('ProgrammeCreationAISimilarity')),
						    'lesson_ids'=> $lesson_ids,
                            'type' => $type,
                        ]
                    ]);
                    $response = json_decode($response->getBody()->getContents());
                    $learningModule =  [];
                    foreach ($response as $lesson) {
						$learning_module = LearningModule::where('id', $lesson->lesson_id)->first();
						if($learning_module && !in_array($lesson->lesson_id,$lesson_ids)){
                        	$learningModule [] =$learning_module;
						}
						$lesson_ids [] = $lesson->lesson_id;
                    }
                    $data->modules[$module_key]->submodules[$submodule_key] = [
                        'title' => $submodule->title,
                        'lessons' => $learningModule
                    ];
                }
            }
		}
		$res->getBody()->write(json_encode($data));
        return $res->withHeader('Content-Type', 'application/json');

    })->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'insert'));

	$group->post('/ai-apply-structure/{id:[0-9]+}', function (Request $request, Response $response, $args)
    {
            $totalSubmodules = 0;
            $data = $request->getParsedBody();
             foreach ($data as $module) {
                if (isset($module['submodules']) && is_array($module['submodules'])) {
                $totalSubmodules += count($module['submodules']);
                }
            }
            $standard = \Models\ApprenticeshipStandard::find($args['id']);
            if($standard->completion_months && $totalSubmodules){
                $issue_complete = ($standard->completion_months*30)/$totalSubmodules;
            }
        $start_of_day= 0;
        foreach ($data as $cat_key => $cat)
        {
            if (isset($cat["title"]))
            {
                $newCat = \Models\ApprenticeshipIssueCategories::firstOrCreate([
                    'name' => $cat["title"],
                    'standard_id' => $args['id'],
                    'status' => 1,
                ]);

                    if (isset($cat["submodules"])) {
                    foreach ($cat["submodules"] as $subcat_key => $subcat)
                    {

                    if ($subcat['title'] != "")
                    {
                        $newSubCat = \Models\ApprenticeshipIssues::firstOrCreate([
                            "name" => $subcat['title'],
                            "issue_category_id" => $newCat->id,
                                    'status' => 1,
                                    'start_day'=>$start_of_day,
                                    'end_day'=>$start_of_day+$issue_complete
                                ]);
                                $start_of_day += $issue_complete;
                        foreach ($subcat['lessons'] as $lesson) {
                                    if($lesson['checked']==true){
                                    \Models\ApprenticeshipIssues::addModuleToIssue($newSubCat->id, $lesson['id']);
                                    }
                        }
                    }
                    }
                    }
            }
        }
		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');

    })->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'insert'));


	$group->get("/refresh/{standard_id:[0-9]+}/{user_id:[0-9]+}",function(Request $request, Response $response, $args) {

		if (
			!\APP\Auth::roleAllowRefreshProgrammes() &&
			!\APP\Auth::isAdmin()
		) {
			return \APP\Tools::returnCode($request, $response, 403);
		}

		\Models\ApprenticeshipStandard::RefreshEntries([$args['user_id']], [$args['standard_id']], true);

		return
			$response
		;
	})->add(\APP\Auth::getSessionCheck());

});
