<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/email-queue",  function ($group) {

   $group->post('/list', function (Request $request, Response $response) {
      try{
		$params = $request->getParsedBody();
		$query = \Models\EmailQueue
			::with(['fromUser' => function ($query) {
				$query
					->select('id', 'fname', 'lname', 'email');
			}])
			->with(['template' => function ($query) {
				$query
					->select('id', 'name', 'subject');
			}])
		;

		if (isset($params["search"]["refresh"])) {
			unset($params["search"]["refresh"]);
		}


		if (isset($params["search"]["subject"])) {
			$subject = $params["search"]["subject"];
			$query = $query
				->whereHas('template', function ($query) use ($subject) {
					$query
						->where("email_templates.name", "LIKE", "%{$subject}%")
						->orWhere("email_templates.subject", "LIKE", "%{$subject}%")
					;
				});
			;
			unset($params["search"]["subject"]);
		}

		if (isset($params["search"]["from"])) {
			$from = $params["search"]["from"];
			$query = $query
				->whereHas('fromUser', function ($query) use ($from) {
					$query
						->whereRaw("CONCAT( users.fname,  ' ', users.lname ) LIKE '%" . $from . "%'")
						->orWhere("users.email", "LIKE", "%{$from}%")
					;
				});
			;
			unset($params["search"]["from"]);
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

		} catch(\Exception $e) {
			print_r($e->getMessage());
		}
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-audit-email-queue', 'select'));

	//approve queue item for sending out
	$group->post('/approve/{id}', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();
		$entry = \Models\EmailQueue
			::find($args['id'])
		;
		if ($entry) {
			$entry->approved = true;
			$entry->save();
		}

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-audit-email-queue', 'disable'));

	//disapprove queue item for sending out
	$group->post('/disapprove/{id}', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();
		$entry = \Models\EmailQueue
			::find($args['id'])
		;
		if ($entry) {
			$entry->approved = false;
			$entry->save();
		}

		return $response;
  })->add(\APP\Auth::getStructureAccessCheck('system-setup-audit-email-queue', 'disable'));

  $group->put('/approve-all', function (Request $request, Response $response) {
		\Models\EmailQueue::where('approved',false)->update(['approved'=>true]);

		return $response;
  })->add(\APP\Auth::getStructureAccessCheck('system-setup-audit-email-queue', 'disable'));

	$group->delete('/all', function (Request $request, Response $response) {
		\Models\EmailQueue::query()->delete();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-audit-email-queue', 'disable'));

});
