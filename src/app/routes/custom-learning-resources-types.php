<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;
use League\Flysystem\Filesystem;
use League\Flysystem\Local\LocalFilesystemAdapter;

$app->group("/custom-learning-resources-types",  function ($group) {

	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$learning_module_type = \Models\LearningModuleType::withoutGlobalScope('type_filter')->find($args["id"]);
		if (!$learning_module_type) {
			return \APP\Tools::returnCode($request, $response, 404, 'Learning resource type not found');
		}
		$learning_module_type->status = 0;
		$learning_module_type->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-custom-learning-resources-types', 'disable'));

	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$learning_module_type = \Models\LearningModuleType::withoutGlobalScope('type_filter')->find($args["id"]);
		if (!$learning_module_type) {
			return \APP\Tools::returnCode($request, $response, 404, 'Learning resource type not found');
		}
		$learning_module_type->status = 1;
		$learning_module_type->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-custom-learning-resources-types', 'disable'));

	$group->get('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$custom_learning_module_type = \Models\LearningModuleType::find($args["id"]);

		$response->getBody()->write(json_encode($custom_learning_module_type));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-custom-learning-resources-types', 'select'));

	$group->post('/new', function (Request $request, Response $response) {
		$data = $request->getParsedBody();

		$slug = \APP\Tools::safeName($data['name']);

		// check if such slug exists already.
		$entry = \Models\LearningModuleType
			::where('slug', $slug)
			->first()
		;
		if ($entry) {
			return \APP\Tools::returnCode($request, $response, 500, 'This Type already exists, please choose a different name.');
		}

		$entry = new \Models\LearningModuleType;
		$entry->slug = $slug;
		$entry->name = $data['name'];
		$entry->status = 1;
		$entry->custom = true;

		if (isset($_FILES['attached_word_form'])) {
			$uploadPath = $this->get('settings')["LMSFilePath"];
			$adapter = new LocalFilesystemAdapter($uploadPath);
			$filesystem = new Filesystem($adapter);

			$uploadedFile = $_FILES['attached_word_form'];
			$safeName = \APP\Tools::safeName($uploadedFile['name']) . '_' . uniqid();
			$fileExtension = pathinfo($uploadedFile['name'], PATHINFO_EXTENSION);
			$fullFileName = $safeName . '.' . $fileExtension;
			$fileSize = $uploadedFile['size'];
			$mimeType = mime_content_type($uploadedFile['tmp_name']);

			// Validate file size and type
			$allowedMimeTypes = \APP\Tools::documentMime();
			if ($fileSize > 800 * 1024 * 1024) { // 800MB
				 return \APP\Tools::returnCode($request, $response, 500, "File size exceeds the limit of 800MB.");
			}

			if (!in_array($mimeType, $allowedMimeTypes)) {
				 return \APP\Tools::returnCode($request, $response, 500, "Invalid file type: '$mimeType'!");
			}

			try {
				 // Check if an old file exists and delete it
				 if (!empty($entry->attached_word_form) && $filesystem->fileExists($entry->attached_word_form)) {
					  $filesystem->delete($entry->attached_word_form);
				 }

				 // Upload the new file
				 $stream = fopen($uploadedFile['tmp_name'], 'r+');
				 $filesystem->writeStream($fullFileName, $stream);
				 fclose($stream);

				 // Set the entry's attached word form to the new file name
				 $entry->attached_word_form = $fullFileName;
			} catch (\Exception $e) {
				 return \APP\Tools::returnCode($request, $response, 500, $e->getMessage());
			}
	  }


		$entry->custom = true;
		$entry->slug = \APP\Tools::safeName($data['name']);
		$entry->save();


		$response->getBody()->write((string)$entry->id);
		return $response;

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-custom-learning-resources-types', 'insert'));

	$group->post('/parameter/add', function (Request $request, Response $response) {
		$data = $request->getParsedBody();

      $last_parameter_position = \Models\LearningModuleTypeParameter
      	::where('learning_module_types_id', $data['type_id'])
      	->orderBy('position', 'DESC')
      	->first()
      ;

		$entry = new \Models\LearningModuleTypeParameter;
		$entry->learning_module_types_id = $data['type_id'];
		$entry->parametername = $data['parametername'];
		$entry->parametertype = $data['parametertype'];
		$entry->mandatorycompletion = $data['mandatorycompletion'];
		$entry->parameterslug = \APP\Tools::safeName($data['parametername']);
		$entry->position = $last_parameter_position->position + 1;
		$entry->save();

		return
			$response
		;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-custom-learning-resources-types', 'insert'));

	$group->post('/parameter/remove', function (Request $request, Response $response) {
		$data = $request->getParsedBody();

		\Models\LearningModuleTypeParameter
			::where('id', $data['id'])
			->delete()
		;

		return
			$response
		;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-custom-learning-resources-types', 'insert'));

		// Delete Word Document
	$group->delete('/attached_word_form/{id:[0-9]+}', function (Request $request, Response $response, $args) {
			$learningtype = \Models\LearningModuleType::find($args["id"]);
			if (is_file($this->get('settings')["LMSFilePath"] . $learningtype->attached_word_form)) {
				unlink($this->get('settings')["LMSFilePath"] . $learningtype->attached_word_form);
			}
			$learningtype->attached_word_form = null;
			$learningtype->save();

			return
				$response
			;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-categories', 'update'));

	$group->post('/moveupdown', function (Request $request, Response $response) {
		$data = $request->getParsedBody();
		$movefrom = \Models\LearningModuleTypeParameter
			::where('id', $data['id'])
			->first()
		;


		if ($movefrom->position == null) {
			$movefrom->position = $data['position'] + 1;
		}

		$position = $movefrom->position;

		if ($data['direction'] == 'up') {
			$to_position = $movefrom->position - 1;
			$to_position_comparison = '<=';
			$to_position_order = 'DESC';
		} else if ($data['direction'] == 'down') {
			$to_position = $movefrom->position + 1;
			$to_position_comparison = '>=';
			$to_position_order = 'ASC';
		}

		$moveto = \Models\LearningModuleTypeParameter
			::where('position', $to_position_comparison, $to_position)
			->where('learning_module_types_id', $movefrom->learning_module_types_id)
			->orderBy('position', $to_position_order)
			->first()
		;

		$movefrom->position = $moveto->position;
		$moveto->position = $position;
		$movefrom->save();
		$moveto->save();

		return
			$response
		;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-custom-learning-resources-types', 'insert'));

	// Update
	$group->post('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$slug = \APP\Tools::safeName($data['name']);

		$entry = \Models\LearningModuleType::find($data['id']);
		if ($entry->custom) $entry->name = $data['name'];
		$entry->fit_for_evidence = (bool) $data['fit_for_evidence'];

		// If name/slug changed, check if slug already exists, if not, change it!
		if ($entry->custom && $entry->slug != $slug) {
			$check_for_slug = \Models\LearningModuleType
				::where('slug', $slug)
				->where('id', '!=', $data['id'])
				->first()
			;
			if ($check_for_slug) {
				return \APP\Tools::returnCode($request, $response, 500, 'This Type already exists, please choose a different name.');
			}
		}
		if ($entry->custom) $entry->slug = \APP\Tools::safeName($data['name']);

		if (isset($_FILES['attached_word_form'])) {
			$storage = new \Upload\Storage\FileSystem($this->get('settings')["LMSFilePath"]);
			$attached_word_file = new \Upload\File('attached_word_form', $storage);
			$attached_word_file->setName(\APP\Tools::safeName($_FILES['attached_word_form']['name']) . '_' . uniqid());
			$fileSizeValidation = new \Upload\Validation\Size('800M');
			$fileTypeValidation = new \Upload\Validation\Mimetype(\APP\Tools::documentMime());
			$fileTypeValidation->setMessage("Invalid file type: '" . $attached_word_file->getMimetype() . "'!");
			$attached_word_file->addValidations([
				$fileTypeValidation,
				$fileSizeValidation
			]);

			try {
				$attached_word_file->upload();
				$entry->attached_word_form = $attached_word_file->getNameWithExtension();
			} catch (\Upload\Exception\UploadException $e) {
				$errors = $attached_word_file->getErrors();
				return \APP\Tools::returnCode($request, $response, 500, implode("\n", $errors));
			}

		}

		$entry->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-custom-learning-resources-types', 'update'));

	$group->get('/parameter/{type_id:[0-9]+}/all', function (Request $request, Response $response, $args) {
		$data = \Models\LearningModuleTypeParameter
			::where("learning_module_types_id", $args['type_id'])
			->get()
		;

		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-custom-learning-resources-types', 'select'));

	$group->post('/list', function (Request $request, Response $response) {
		$params = $request->getParsedBody();

		if (isset($params["search"]["refresh"])) {
			unset($params["search"]["refresh"]);
		}

		$query= \Models\LearningModuleType::withoutGlobalScope('type_filter')
			->where("id",">",0)
		;

		$data = \APP\SmartTable::searchPaginate($params, $query, false);

		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-custom-learning-resources-types', 'select'));

});