<?php

use APP\Auth;
use APP\Controllers\ImportUserController;
use APP\Form;
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;
use \GuzzleHttp\Psr7\LazyOpenStream as OpenStream;
use Models\ImportLog;
use Models\ImportLogError;
use Models\Schedule;
use League\Flysystem\Filesystem;
use League\Flysystem\Local\LocalFilesystemAdapter;
use League\Flysystem\FilesystemException;
use Slim\Psr7\Stream;


$app->group("/user", function ($group) {
	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$user = \Models\User::find($args["id"]);
		$user->status = 0;
		$user->save();
		$author_id=Auth::getUserId();
		Schedule::userStatusUpdate($user->id,$author_id,false);
		// Unassign user from managers
		\Models\ManagerUser
			::where('user_id', $user->id)
			->delete()
	;

	/** Softdelete Learning Modules */
	$user_form_list=\Models\UserForm::where("user_id",$args["id"])->get();
	if($user_form_list){
		foreach($user_form_list AS $user_form_list_val){
			\Models\UserForm::deleteUserForms($user_form_list_val->id);
		}
	}


		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-users', 'disable'));

	$group->put('/approval_status', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$user = \Models\User::find($data["user_id"]);
		$user->approval_status = $data['approval_status'];
		$user->save();
		if($data['approval_status'] == 'Approved'){
			$qa_template = \Models\EmailTemplate
				::where('name', 'Account Approval')
				->where('status', true)
				->first();
		}elseif ($data['approval_status'] == 'Rejected'){
			$qa_template = \Models\EmailTemplate
				::where('name', 'Account Denied')
				->where('status', true)
				->first();
		}
		if($qa_template){
			$email_queue = new \Models\EmailQueue;
			$email_queue->email_template_id = $qa_template->id;
			$email_queue->learning_module_id = null;
			$email_queue->recipients = [$user->id];
			$email_queue->from =\APP\Auth::getUserId();
			$email_queue->custom_variables = json_encode([]);
			$email_queue->save();
		}

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-users', 'disable'));

	$group->put('/approval_status_bulk', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$userTable = (new \Models\User())->getTable();
		$ids =  DB::table($userTable)->where('approval_status', '!=', $data['approval_status'])->whereIn('id',$data['ids'])->pluck('id')->toArray();
		DB::table($userTable)->where('approval_status', '!=', $data['approval_status'])->whereIn('id',$data['ids'])->update(array('approval_status' => $data['approval_status']));
		$name = $data['approval_status'] == 'Approved' ? 'Account Approval':'Account Denied';
		$qa_template = \Models\EmailTemplate
			::where('name', $name)
			->where('status', true)
			->first();
		foreach ($ids as $id)
		{
				if($qa_template){
				$email_queue = new \Models\EmailQueue;
				$email_queue->email_template_id = $qa_template->id;
				$email_queue->learning_module_id = '';
				$email_queue->recipients = [$id];
				$email_queue->from =\APP\Auth::getUserId();
				$email_queue->custom_variables = json_encode([]);
				$email_queue->save();
			}
		}


		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-users', 'disable'));

	$group->put('/force_password_reset', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$userTable = (new \Models\User())->getTable();
		DB::table($userTable)->whereIn('id',$data['ids'])->update(array('password_force_reset' => 1));
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-users', 'disable'));

	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$user = \Models\User::find($args["id"]);
		$user->status = 1;
	$user->save();

	// when user enabled sending mail to user
	\Models\User::notifyUserIsEnabled($user);

	$author_id = Auth::getUserId();
	Schedule::userStatusUpdate($user->id,$author_id,true);
		// when enabled, if user is in department, assign it back to managers(enabled) in department.
		if ($user->department_id > 0) {
			$managers = \Models\ManagerDepartment
				::where('department_id', '=', $user->department_id)
				->get();
			foreach ($managers as $manager) {
				$managerUser = new \Models\ManagerUser;
				$managerUser->manager_id = $manager->manager_id;
				$managerUser->user_id = $user->id;
				$managerUser->save();
			}
		}

		/** Softdelete Users */
	$user_form_list=\Models\UserForm::where("user_id",$args["id"])->withTrashed()->get();
	if($user_form_list){
		foreach($user_form_list AS $user_form_list_val){
			\Models\UserForm::deleteUserForms($user_form_list_val->id,false);
		}
	}

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-users', 'disable'));

	// Disable/Enable multiple users
	$group->put('/{state}-multiple', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		if (
			isset($data['ids']) &&
			is_array($data['ids'])
		) {
			$query = \Models\User
				::whereIn('id', $data['ids'])
			;

			if ($args['state'] == 'disable') {
				$query->update(['status' => false]);

				// Unassign user from managers
				\Models\ManagerUser
					::whereIn('user_id', $data['ids'])
					->delete()
				;
			}

			if ($args['state'] == 'enable') {
				$query->update(['status' => true]);

				// when enabled, if user is in department, assign it back to managers(enabled) in department.
				$users = $query->get();
				foreach ($users as $key => $user) {
					$managers = \Models\ManagerDepartment
						::where('department_id', '=', $user->department_id)
						->get()
					;
					foreach ($managers as $manager) {
						$managerUser = new \Models\ManagerUser;
						$managerUser->manager_id = $manager->manager_id;
						$managerUser->user_id = $user->id;
						$managerUser->save();
					}
				}
			}
		}
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-users', 'disable'));

	// Get current logged in user.
	$group->get('/', function (Request $request, Response $response, $args) {
		$user = \Models\User::findOrFail(\APP\Auth::getUserId());
		$user->password = null;

		$response->getBody()->write(json_encode($user));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());

	// Ethnicity, everyone loggeed in can get list
	$group->get('/ethnicity/all', function (Request $request, Response $response, $args) {
		$ethnicities = \Models\Ethnicity
			::where('status', true)
			->get();

		$response->getBody()->write(json_encode($ethnicities));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());

	// Sex, everyone loggeed in can get list
	$group->get('/sex/all', function (Request $request, Response $response, $args) {
		$ethnicities = \Models\Sex
			::where('status', true)
			->get();

		$response->getBody()->write(json_encode($ethnicities));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());

	// Health problems
	$group->get('/llddhealthproblem/all', function (Request $request, Response $response, $args) {
		$llddhealthproblems = \Models\LlddHealthProblem
			::where('status', true)
			->get();

		$response->getBody()->write(json_encode($llddhealthproblems));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());

	// get LLDD and health problem categories
	$group->get('/llddhealthproblemscategory/all', function (Request $request, Response $response, $args) {
		$llddhealthproblemscategories = \Models\LlddHealthProblemsCategory
			::where('status', true)
			->get();

		$response->getBody()->write(json_encode($llddhealthproblemscategories));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());


	// get Prior attainments
	$group->get('/priorattainment/all', function (Request $request, Response $response, $args) {
		$prior_attainments = \Models\PriorAttainment
			::where('status', true)
			->get();

		$response->getBody()->write(json_encode($prior_attainments));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());

	// get Legacy Prior attainments
	$group->get('/priorattainmentlegacy/all', function (Request $request, Response $response, $args) {
		$prior_attainments_legacy = \Models\PriorAttainmentLegacy
			::where('status', true)
			->get();

		$response->getBody()->write(json_encode($prior_attainments_legacy));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());


	// Upload user's image
	$group->post('/upload/{image_file}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		$file_config = [
			'image' => [
				'size' => 500000,
				'path' => 'LMSUsersImagesPath'
			],
			'e_signature' => [
				'size' => 500000,
				'path' => 'LMSESignatureImagesPath'
			],
            'background_image' => [
                'size' => 2 * 1024 * 1024, # 2mb
                // 'path' => 'LMSUsersBackgroundImagesPath'
                'path' => 'LMSUsersImagesPath'
            ],
		];

		$user = \Models\User::find($data["id"]);

		if (
			$user &&
			isset($data['field']) &&
			$data['field'] &&
			isset($_FILES[$data['field']]) &&
			(
				\APP\Auth::isAdmin() ||
				\APP\Auth::accessAllLearners() ||
				\APP\Auth::isManagerOf($user->id) ||
				(
					\APP\Auth::getUserId() == $user->id &&
					isset($args['image_file']) &&
					(
						(
							$args['image_file'] == 'image' &&
							\APP\Auth::permission('my_profile__edit_image')
						) ||
						$args['image_file'] == 'background_image'
					)

				)
			)
		) {
			if (isset($_FILES[$data['field']])) {
				$file = $_FILES[$data['field']];
				$fileName = preg_replace('/[^a-zA-Z0-9]/', '_', $user->fname . '_' . $user->lname);
				$fileName = $fileName . "_" . $data['field'] . "_" . time();
				$destinationPath = $fileName . '.' . pathinfo($file['name'], PATHINFO_EXTENSION);

				// Validate file size
				if ($file['size'] > $file_config[$data['field']]['size']) {
					return \APP\Tools::returnCode($request, $response, 500, "File size exceeds the allowed limit.");
				}

				// Validate file type
				$allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp'];
				if (!in_array(mime_content_type($file['tmp_name']), $allowedMimeTypes)) {
					return \APP\Tools::returnCode($request, $response, 500, "Invalid file type. You must upload an image file.");
				}

				try {
					$adapter = new LocalFilesystemAdapter($this->get('settings')[$file_config[$data['field']]['path']]);
					$filesystem = new Filesystem($adapter);

					$stream = fopen($file['tmp_name'], 'r+');
					if ($stream === false) {
						throw new \Exception('Failed to open file for reading');
					}

					$filesystem->writeStream($destinationPath, $stream);
					if (is_resource($stream)) {
						fclose($stream);
					}

					$response->getBody()->write($destinationPath);

                if ($data['field'] == 'background_image') {
                    $image_name = \Models\TableExtension::getValue('users', \APP\Auth::getUserId(), 'background_image');
                    if ($image_name) {
                        // $imagepath = $this->get('settings')['LMSEBackgroundImagesPath'] . $image_name;
                        $imagepath = $this->get('settings')['LMSUsersImagesPath'] . $image_name;
                        if (file_exists($imagepath)) {
                            unlink($imagepath);
                        }
                    }
                    \Models\TableExtension::updateField('users', \APP\Auth::getUserId(), 'background_image', $imageFileName);
                }else{
				// delete previous file
					if (is_file($this->get('settings')[$file_config[$data['field']]['path']] . '/' . $user->{$data['field']})) {
						unlink($this->get('settings')[$file_config[$data['field']]['path']] . '/' . $user->{$data['field']});
				}

					$user->{$data['field']} = $destinationPath;
				$user->save();
                }

				} catch (FilesystemException $e) {
					return \APP\Tools::returnCode($request, $response, 500, $e->getMessage());
				} catch (\Exception $e) {
					return \APP\Tools::returnCode($request, $response, 500, $e->getMessage());
				}
			}
		} else {
			$response = \APP\Tools::returnCode($request, $response, 500, "Missing image file or no permission to change image");
		}

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck(['system-setup-organisation-users', 'trainee-edit-profile'], 'update'));


	// display user's image!
	$group->get('/files/{image_file}/{user_id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$user = \Models\User::find($args["user_id"]);

		if (
			$user->Sex &&
			(
				strtolower($user->Sex) == 'm' ||
				strtolower($user->Sex) == 'f'
			)
		) {
			$gender = strtolower($user->Sex);
		} else {
			$genders = 'fm';
			$gender = $genders[mt_rand(0, strlen($genders) - 1)];
		}

		if ($gender == 'f') {
			$random = rand(1, 4);
		} else {
			$random = rand(1, 5);
		}

		$file_config = [
			'image' => [
				'path' => 'LMSUsersImagesPath'
			],
			'e_signature' => [
				'path' => 'LMSESignatureImagesPath'
			]
		];

		// If user requests his managers signature,
		if (
			(
				\APP\Auth::isAdmin() ||
				\APP\Auth::accessAllLearners() ||
				\APP\Auth::isManager() ||
				\APP\Auth::isYourManager($user->id) ||
				\APP\Auth::getUserId() == $user->id
			) &&
			isset($file_config[$args["image_file"]])
		) {
			if (is_file($this->get('settings')[$file_config[$args["image_file"]]['path']] . $user->{$args["image_file"]})) {
				$imageStream = new OpenStream($this->get('settings')[$file_config[$args["image_file"]]['path']] . $user->{$args["image_file"]}, 'r');
				$response = $response
					->withBody($imageStream)
					->withHeader('Content-Type', FILEINFO_MIME_TYPE);
			} else if ($args["image_file"] == 'e_signature') {
				// return 404 if e-signature is not found.
				return
					$response
						->withStatus(404);
			} else {
				if (\APP\Tools::getConfig("neutralAvatarForAll")) $image = $this->get('settings')['LMSImagesPath'] . 'neutral_avatar.png';
				else $image = $this->get('settings')['LMSImagesPath'] . 'rankings/' . $gender . '_' . $random . '.png';

				if (
				is_file($image)
				) {
					$imageStream = new OpenStream($image, 'r');
					$response = $response
						->withBody($imageStream)
						->withHeader('Content-Type', FILEINFO_MIME_TYPE);
				}
			}
		} else {
			// return female or male generic icon!
			$image = $this->get('settings')['LMSImagesPath'] . 'rankings/' . $gender . '_' . $random . '.png';
			if (
			is_file($image)
			) {
				$imageStream = new OpenStream($image, 'r');
				$response = $response
					->withBody($imageStream)
					->withHeader('Content-Type', FILEINFO_MIME_TYPE);
			}
		}
		return $response;
	})->add(\APP\Auth::getSessionCheck());


	// Delete image from user profile
	$group->delete('/delete/{image_file}/{user_id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$user = \Models\User::find($args["user_id"]);

		if (
			\APP\Auth::isAdmin() ||
			\APP\Auth::accessAllLearners() ||
			\APP\Auth::isManagerOf($user->id) ||
			(
				\APP\Auth::getUserId() == $user->id &&
				(
					(
						$args['image_file'] == 'image' &&
						\APP\Auth::permission('my_profile__edit_image')
					) ||
					$args['image_file'] == 'background_image'
				)

			)
		) {

			if ($args['image_file'] == 'image') {
				$imagepath = $this->get('settings')['LMSUsersImagesPath'] . $user->image;
				if (file_exists($imagepath)) {
					unlink($imagepath);
				}
				$user->image = null;
				$user->save();
			}

			if ($args['image_file'] == 'e_signature') {
				$imagepath = $this->get('settings')['LMSESignatureImagesPath'] . $user->e_signature;
				if (file_exists($imagepath)) {
					unlink($imagepath);
				}
				$user->e_signature = null;
				$user->save();
			}

            if ($args['image_file'] == 'background_image') {
                $image_name = \Models\TableExtension::getValue('users', \APP\Auth::getUserId(), 'background_image');

                $imagepath = $this->get('settings')['LMSUsersImagesPath'] . $image_name;
                if (file_exists($imagepath)) {
                    unlink($imagepath);
                }
                \Models\TableExtension::where('name', 'background_image')->where('table', 'users')->where('table_id', $user->id)->delete();
                $user->save();
            }

		} else {
			return \APP\Tools::returnCode($request, $response, 403, "No permission to delete image");
		}
		return $response;

	})->add(\APP\Auth::getSessionCheck());







	// Get user details
	$group->get('/{id:[0-9]+}{options:[\/a-z\-]*}', function (Request $request, Response $response, $args) {
		try {

			if (!\APP\Auth::roleAllowAccessToUserProfile()) {
				return \APP\Tools::returnCode($request, $response, 403);
			}

			if (\APP\Auth::isLearner()) {
				$args["id"] = \App\Auth::getUserId();
			}

			$user = \Models\User
				::select('id', 'fname', 'lname', 'email', 'learning_status', 'zoom_id', 'teams_id', 'skype_id', 'role_id')
				->with('role')
				->where('id', $args["id"])
			;

			if (
				!\APP\Auth::isAdmin() &&
				!\APP\Auth::accessAllLearners() &&
				!\APP\Auth::accessAllCompanies() &&
				\APP\Auth::getUserCompanyId()
			) {
				$user = $user
					->where('users.company_id', \APP\Auth::getUserCompanyId())
				;
			}

			$user = $user->first();

			if (!$user) {
				return \APP\Tools::returnCode($request, $response, 404);
			}


			if (
				!\APP\Auth::isAdmin() &&
				!\APP\Auth::isManagerOf($args["id"]) &&
				!\APP\Auth::accessAllLearners() &&
				\APP\Auth::getUserId() != $args["id"]
			) {
				return \APP\Tools::returnCode($request, $response, 403);
			}

			if (
				isset($args['options']) &&
				$args['options'] == '/ping'
			) {
				$response->getBody()->write(json_encode($user));
				return $response->withHeader('Content-Type', 'application/json');
			}


			$careerPathIDs = explode(',', data_get($request->getQueryParams(), 'career_path_ids', ''));
			$jobIds = explode(',', data_get($request->getQueryParams(), 'job_ids', ''));
			$args['career_path_ids'] = empty(array_filter($careerPathIDs, function ($val) { return $val != ''; })) ? [] : $careerPathIDs;
			$args['job_ids'] = empty(array_filter($jobIds, function ($val) { return $val != ''; })) ? [] : $jobIds;
			$args['curr_page'] = data_get($request->getQueryParams(), 'page');

			$user = \Models\User
				::where('id', $args["id"])
				->with('groups')
				->withCount(['SkillScans' => function ($query) {
					$query->where('status', true);
				}])
				->with(['role' => function($query){
					$query->select('id', 'name', 'is_learner', 'is_admin', 'is_manager', 'is_fa', 'is_cd', 'is_qa');
				}])
				->withCount('LearningResultArchives')
			;
			if (\APP\Tools::getConfig('showSevenDepartmentSubLevels')) {
				$user = $user
					->with(['SubDepartments' => function($query){
						$query
							->with('Department')
						;
					}])
				;
			}

			if (\APP\Auth::isAdminInterface()) {
				$user = $user
					->with(['CreatedBy' => function($query) {
						$query
							->select('id', 'fname', 'lname')
						;
					}])
				;
			}


			// attach standards and evidence to user object if page is requested from learners (admin interface)
			if (isset($args['options']) && ($args['options'] == '/learners' || $args['options'] == '/download' || $args['options'] == '/print')) {

				if (\Models\Role::getRoleParam('lfp_show_learning_programmes')) $user = $user
					//->with('standards.issues.modules.learningresults')
					->with(['standards' => function ($query) use ($args) {
						$query
							->select(
								'apprenticeship_standards.id',
								'apprenticeship_standards.name',
								'apprenticeship_standards.review_interval',
								'apprenticeship_standards.status',
								'apprenticeship_standards.sort',
								'apprenticeship_standards.type',
								'apprenticeship_standards.completion_months',
								'apprenticeship_standards.working_hours',
								'apprenticeship_standards.course_credits',
								'apprenticeship_standards.number_of_evidence_expected',
								'apprenticeship_standards.print_certificate',
								'apprenticeship_standards.self_enroll',
								'apprenticeship_standards.repetition_period',
								'apprenticeship_standards.default_skill_repetition_period'
							)
							->selectRaw(
								'
									(
										select gateway_readiness_id from user_gateway_readiness
											where user_gateway_readiness.user_id = ?
											and user_gateway_readiness.link_id = apprenticeship_standards.id
											and user_gateway_readiness.type = "standard"
									)
									as gateway_readiness
								',
								[
									$args["id"]
								]
							)
							->where('apprenticeship_standards.status', 1)
							->whereNull('apprenticeship_standards_users.deleted_at')

							->with(['issuecategories' => function ($query) use ($args) {
								$query = $query
									->select(
										'apprenticeship_issue_categories.id',
										'name',
										'standard_id',
										'status',
										'exclude_outcome',
										'sort',
										'hide_progressbar',
										'minimum_required_credits'
									)
									->selectRaw(
										'
											(
												select gateway_readiness_id from user_gateway_readiness
													where user_gateway_readiness.user_id = ?
													and user_gateway_readiness.link_id = apprenticeship_issue_categories.id
													and user_gateway_readiness.type = "category"
											)
											as gateway_readiness
										',
										[
											$args["id"]
										]
									)
									->where('status', true)
									->orderBy('sort', 'ASC')
									->with(['issues' => function ($query) use ($args) {
										\Models\User::getIssueModules($query, $args["id"]);
										$query = $query
											->where('parent_id', 0)
											->orderBy('sort', 'ASC')
											->with(['QualityFeedback' => function ($query) use ($args) {
												$query= $query
												->where('user_id', $args["id"])
												->select('*', DB::raw("DATE_FORMAT(quality_controls.created_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS qa_created_at"))
												->with(['qaUser' => function ($query) use ($args) {
													$query
														->select('id', 'fname', 'lname')
													;
												}]);
											}])
											->with(["children" => function ($query) use ($args) {
												\Models\User::getIssueModules($query, $args["id"]);
												$query = $query
													->with(["children" => function ($query) use ($args) {
														\Models\User::getIssueModules($query, $args["id"]);
													}])
												;
											}])
										;
									}])
									->withCount(['issues' => function ($query) use ($args) {
										$query = $query
											->where('parent_id', 0)
										;
									}])
									->withCount(['IssuesDisabled' => function ($query) use ($args) {
										$query = $query
											->where('parent_id', 0)
											->whereHas('disabled', function ($query) use ($args) {
												$query->where('user_id', $args['id']);
											})
										;
									}])
									->with(['OptionalOutcomes' => function ($query) use ($args) {

									}])
								;
								if (\APP\Auth::isAdminInterface()) {
									$query = $query
										->with(['QualityControl' => function ($query) use ($args) {
											$query
												->where('user_id', $args['id'])
												->orderBy('id', 'DESC')
												->with(['qaUser' => function ($query) use ($args) {
													$query
														->select('id', 'fname', 'lname')
													;
												}])
											;
										}])
										->with(['QualityFeedback' => function ($query) use ($args) {
											$query= $query
											->where('user_id', $args['id'])
											->select('*', DB::raw("DATE_FORMAT(quality_controls.created_at,'%d/%m/%Y') AS qa_created_at"))
											->with(['qaUser' => function ($query) use ($args) {
												$query
													->select('id', 'fname', 'lname')
												;
											}]);
										}])
									;
								}
							}])
							#->with(['SkillSignoffLogs.User', 'SkillSignoffLogs.Role'])
/*
							->with(['SkillSignoffLogs' => function ($query) use ($args) {
								$query
									->where('user_id', $args['id'])
									->with('User')
									->with('Role')
								;
							}])
*/
							->with(['Designations' => function ($query) use ($args) {
								if (!empty($args['career_path_ids'])) {
									$query->with(['CareerPaths' => function ($query) use ($args) {
										$query->whereIn('career_paths.id', $args['career_path_ids']);
									}]);
								}
							}]);

						if ($args['curr_page'] === 'future_pathway' && (!empty($args['career_path_ids'] || !empty($args['job_ids'])))) {
							$query->where(function ($query) use ($args) {
								$query->whereHas('Designations.CareerPaths', function ($query) use ($args) {
									$query->whereIn('career_paths.id', $args['career_path_ids']);
								})->orWhereHas('Designations', function ($query) use ($args) {
									$query->whereIn('designations.id', $args['job_ids']);
								});
							});
						}

					}])

					//->with('evidence.learningresults')

					// need to attach also uploaded evidence that is attached to standard.
				;
				if (\APP\Tools::getConfig('enableSchedule') && \Models\Role::getRoleParam('lfp_show_events')) {
					$user = $user
						->with(['ScheduleLinksAndWaiting' => function ($query) {
							$query = $query
								->with('Schedule.Lessons')
								->with('Schedule.VisitType')
								->with(['CreatedBy' => function ($query) {
									$query
										->select(
											'users.id',
											'fname',
											'lname'
										)
										->where('users.status', true)
									;
								}])
								->with(['Schedule.Users' => function ($query) {
									$query
										->select(
											'users.id',
											'fname',
											'lname',
											'role_id',
											'schedule_links.approved'
										)
										->where('users.status', true)
									;
								}])
								->with(['Schedule.Visitors' => function ($query) {
									$query
										->select(
											'users.id',
											'fname',
											'lname',
											'role_id'
										)
										->where('users.status', true)
									;

								}])
								->with('Schedule.Programmes')
								->whereHas('Schedule', function ($query) {
									$query
										->where('status', true);
								});
							if (\APP\Auth::isLearner()) {
								$query = $query
									->whereHas('Schedule', function ($query) {
										$query
											->where('visible_learner', true);
									});
							}

						}])
					;
				}
				if (
					\APP\Tools::getConfig('addCustomProgrammeStatus')
					&& \Models\Role::getRoleParam('lfp_show_programme_status')
				) {
					$user = $user
						->with(['UserCustomProgrammeStatuses' => function ($query) {
							$query
								->where('status', true)
								->select('user_custom_programme_statuses.*',DB::raw("DATE_FORMAT(user_custom_programme_statuses.created_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS created_at_uk"))
								->orderBy('id', 'desc')
								->with(['CreatedBy' => function ($query) {
									$query
										->select('id', 'fname', 'lname');
								}])
								->with(['CustomProgrammeStatus'=>function ($query){
								}]);

						}])->with(['UserActiveProgrammeStatus'=>function ($query){
							$query->with(['CustomProgrammeStatus'=>function ($query){}]);
						}])
					;
				}
			}
			if (isset($args['options']) && $args['options'] == '/training-data') {
				$user = $user
					->with(['learningresults' => function ($query) use ($args) {
						$query
							->where('refreshed', false)
							->with('module')
							->with(['module' => function ($query) {
								$query
									->with('type')
									->with('FPCategory');
							}])
							->whereHas('userlearningmodules', function ($query) use ($args) {
								$query
									->where('user_id', $args["id"]);
							})
							->whereIn('learning_module_id',
								\Models\LearningModule::select('id')
									->where('status', true)
									->get()
							);
					}])
					->with('designation')
					->with('groups')
					->with('company')
					->with('location')
					->with('ManagersCompact');
			}


			if ($this->get('settings')['licensing']['isSMCR']) {
				$user = $user
					->with('StaffType')
					->with(['ReportsTo' => function ($query) use ($args) {
						$query
							->select('id', 'fname', 'lname');
					}])
					->with(['ResponsibleFor' => function ($query) use ($args) {
						$query
							->select('id', 'fname', 'lname', 'report_to');
					}])
					->with(['SmcrCommitteeRolePerson' => function ($query) use ($args) {
						$query
							->with(['SmcrCommitteeRole' => function ($query) use ($args) {
								$query
									->with(['SmcrCommittee' => function ($query) use ($args) {
										$query
											->where('status', true);
									}])
									->where('status', true);
							}])
							->where('status', true);
					}])
					->with(['StaffFunctionResponsibility' => function ($query) use ($args) {
						$query
							->select(
								'smcr_staff_functions_responsibilities.*',
								DB::raw("DATE_FORMAT(smcr_staff_functions_responsibilities.updated_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS updated_at_uk")
							)
							->with(['FunctionResponsibility' => function ($query) use ($args) {
								$query
									->where('status', true);
							}]);
					}])
					->with(['SmcrReports' => function ($query) {
						$query
							->with('Type')
							->with(['CertifiedBy' => function ($query) {
								$query
									->select('id', 'fname', 'lname', 'e_signature');
							}])
						;
						if (\APP\Auth::isLearner()) {
							$query = $query
								->where('status', true)
							;
						}
					}]);
			}

			$user = $user->first();
			$user->accessible_ui = $user->accessible_ui == '1' ? true : false;

			if ($user->designation_id)	// to check if we should show 'Competencies required for...' tab under Users profile
			{
				$designation = \Models\Designation::where('id', $user->designation_id)->with('competencies')->first();
				$user->designationHasCompetencies = ($designation && Count($designation->competencies) > 0);
			}

			// If custom week_hours, recalculate working_hours for each standard
			if ($user->week_hours && $user->week_hours > 0 && isset($args['options']) && $args['options'] == '/learners') {
				foreach ($user->standards as $key => $standard) {
					$standard->working_hours = \Models\ApprenticeshipStandard::WorkingHours($standard->completion_months, $user);
				}
			}

			/** Append manager details to pivot table */
			foreach ($user->standards as $key => $standard) {
				if($standard->type=="Skills Monitoring"){
					$commentModel=\Models\Comment::where("table_row_id",$standard->pivot->id)
					->where("added_by",\APP\Auth::getUserId())->where("table_name","apprenticeship_standards_users")->first();
					$standard->pivot->manager_details=\Models\User::find($standard->pivot->sign_off_manager_by);
					// $standard->pivot->skill_comment=$commentModel?$commentModel->comment:'';
					$standard->pivot->skill_comment= '';
				}
			}

			$user->accessible_ui = $user->accessible_ui == '1' ? true : false;
			// Calculate progress and combine modules server side!
			// Need to calculate "completion_date" now.
			if (
				isset($args['options']) &&
				$args['options'] == '/learners'
			) {
				\Models\User::calculateStandardProgress($user->standards);
			}

			// Baby steps in table extension, add any fields inside table_ext
			\Models\TableExtension::returnAllFields('users', $user->id, $user);
			$userX = json_decode(json_encode($user), false);

			switch($args["options"]) {
				case "/download":

					$export_fields = [
						"Name" => "schedule.name",
						"Programme" => "schedule.programmes",
						"Type" => "schedule.type",
						"Status" => "completion_status",
						"Time" => "schedule.duration",
						"Date Due" => "schedule.start_date"
					];

					if(\APP\Tools::getConfig('showEventVisitTypes')) {
						$export_fields['Visit Type'] = 'schedule.visit_type.name';
					}

					$download_file_name = uniqid("User_Events.report.") . ".xlsx";
					\APP\Tools::generateExcelDownload(
						$userX->schedule_links_and_waiting,
						$export_fields,
						$this->get('settings')["LMSTempPath"] . $download_file_name
					);

					$response->getBody()->write(json_encode($download_file_name));
					return $response->withHeader('Content-Type', 'application/json');
				break;

				case "/print":

            	$userX->schedule_links_and_waiting['showEventVisitTypes'] = \APP\Tools::getConfig('showEventVisitTypes');

					$response->getBody()->write(json_encode($userX->schedule_links_and_waiting));
					return $response->withHeader('Content-Type', 'application/json');
				break;

				

				case "/comments/download":
					if (!\Models\Role::getRoleParam('lfp_show_comment_log')) {
						return $response->withStatus(403)->withHeader('Content-Type', 'text/html');
					}

					$query = Models\LearningResultsComment
						::select(
							'learning_results_comments.*',
							'uone.fname',
							'uone.lname',
							'learning_results_comments.created_at AS start_date',
							'lmone.name'
						)
						->where(function ($query) use ($user) {
							$query
								->where('comment_by_user_id', $user->id)
								->orWhere('created_for_user_id', $user->id)
							;
						})
						->where('learning_results_comments.status', true)
						->join('users as uone', function ($query) {
							$query->on('uone.id', '=', 'learning_results_comments.comment_by_user_id');
						})
						->join("learning_modules as lmone", function ($query) {
							$query->on("lmone.id", "=", "learning_results_comments.learning_module_id");
						})
						->leftjoin("user_learning_modules", function ($query) use ($user) {
							$query->on("user_learning_modules.learning_module_id", "=", "learning_results_comments.learning_module_id")
								->where("user_learning_modules.user_id", $user->id)
								->whereNull('user_learning_modules.deleted_at');
						})
						->leftjoin('learning_results', function ($query) use ($user) {
							$query->on("learning_results.id", "learning_results_comments.learning_results_id")
								->where("learning_results.user_id", $user->id)
								->whereNull('learning_results.deleted_at');
						})
						// Remove the whereNotNull condition to allow comments without learning results
						// ->whereNotNull('learning_results.id')
					;

					

					  
					if (\APP\Auth::isLearner()) {
						$query = $query->where('learning_results_comments.visible_learner', true);
					}

					$comments = $query->get();

					$spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
					$sheet = $spreadsheet->getActiveSheet();

					// Set headers
					$sheet->setCellValue('A1', 'Learning Resource');
					$sheet->setCellValue('B1', 'Comment');
					$sheet->setCellValue('C1', 'Author');
					$sheet->setCellValue('D1', 'Date');

					// Add data
					$row = 2;
					foreach ($comments as $comment) {
						$sheet->setCellValue('A' . $row, $comment->name ?? '');
						$sheet->setCellValue('B' . $row, strip_tags($comment->comment ?? ''));
						$sheet->setCellValue('C' . $row, ($comment->fname ?? '') . ' ' . ($comment->lname ?? ''));
						$sheet->setCellValue('D' . $row, $comment->start_date ? \Carbon\Carbon::parse($comment->start_date)->format(\APP\Tools::getConfig('defaultDateFormat')) : '');
						$row++;
					}

					// Apply auto-filter to header row - this creates the dropdown filters
					if ($row > 2) { // Only apply if we have data
						$sheet->setAutoFilter('A1:D' . ($row - 1));
					}

					$writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);

					$response = $response->withHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
					$response = $response->withHeader('Content-Disposition', 'attachment; filename="user-comments.xlsx"');

					ob_start();
					$writer->save('php://output');
					$content = ob_get_clean();

					$response->getBody()->write($content);
					return $response;
				break;

				case "/comments/print":
					if (!\Models\Role::getRoleParam('lfp_show_comment_log')) {
						return $response->withStatus(403)->withHeader('Content-Type', 'text/html');
					}

					$query = Models\LearningResultsComment
						::select(
							'learning_results_comments.*',
							'uone.fname',
							'uone.lname',
							'learning_results_comments.created_at AS start_date',
							'lmone.name'
						)
						->where(function ($query) use ($user) {
							$query
								->where('comment_by_user_id', $user->id)
								->orWhere('created_for_user_id', $user->id)
							;
						})
						->where('learning_results_comments.status', true)
						->join('users as uone', function ($query) {
							$query->on('uone.id', '=', 'learning_results_comments.comment_by_user_id');
						})
						->join("learning_modules as lmone", function ($query) {
							$query->on("lmone.id", "=", "learning_results_comments.learning_module_id");
						})
						->leftjoin("user_learning_modules", function ($query) use ($user) {
							$query->on("user_learning_modules.learning_module_id", "=", "learning_results_comments.learning_module_id")
								->where("user_learning_modules.user_id", $user->id)
								->whereNull('user_learning_modules.deleted_at');
						})
						->leftjoin('learning_results', function ($query) use ($user) {
							$query->on("learning_results.id", "learning_results_comments.learning_results_id")
								->where("learning_results.user_id", $user->id)
								->whereNull('learning_results.deleted_at');
						})
						// Remove the whereNotNull condition to allow comments without learning results
						// ->whereNotNull('learning_results.id')
					;

					if (\APP\Auth::isLearner()) {
						$query = $query->where('learning_results_comments.visible_learner', true);
					}

					$comments = $query->get();
					// Format data for print template
					$print_data = [];
					foreach ($comments as $comment) {
						$print_data[] = [
							'name' => $comment->name ?? '',
							'comment' => strip_tags($comment->comment ?? ''),
							'fname' => $comment->fname ?? '',
							'lname' => $comment->lname ?? '',
							'start_date' => $comment->start_date ? \Carbon\Carbon::parse($comment->start_date)->format(\APP\Tools::getConfig('defaultDateFormat')) : ''
						];
					}

					$response->getBody()->write(json_encode($print_data));
					return $response->withHeader('Content-Type', 'application/json');
				break;
			}

			if(\APP\Auth::isLearner() && !empty($args['curr_page']) && $args['curr_page'] == 'future_pathway') {
				$standardService = new \APP\Services\UserApprenticeshipStandardService();
				$enrollableStandards = $standardService->getUserUnAssignedEnrollableStandards($args);
				$user->setRelation('standards', $user->standards->merge($enrollableStandards));
			}

			$response->getBody()->write(gzencode(json_encode($user)));
			return $response
				->withHeader('Content-Encoding', 'gzip')
				->withHeader('Content-Type', 'application/json')
			;

		} catch(\Exception $e){
			print_r($e->getMessage());
		}
	})->add(\APP\Auth::getStructureAccessCheck(['system-setup-organisation-users', 'trainee-learning-results'], 'select'));

	$group->post('/learning_result/comments/{id:[0-9]+}', function (Request $request, Response $response, $args)
	{
		if (!\Models\Role::getRoleParam('lfp_show_comment_log')) return $response->withStatus(403)->withHeader('Content-Type', 'text/html');

		$params = $request->getParsedBody();
		$user_id = $args['id'];
		if (\APP\Auth::isLearner()) {
			$user_id = \APP\Auth::getUserId();
		}


		$query = Models\LearningResultsComment
			::select(
				'learning_results_comments.*',
				'uone.*',
				'learning_results_comments.created_at AS start_date',
				"user_learning_modules.learning_module_id AS user_learning_status",
				"learning_results.id as learning_result_current_id",
				"lmone.name"
			)
			->where(function ($query) use ($user_id) {
				$query
					->where('comment_by_user_id', $user_id)
					->orWhere('created_for_user_id', $user_id)
				;
			})
			->where('learning_results_comments.status', true)
			->join('users as uone', function ($query) use($params) {
				$query->on('uone.id', '=', 'learning_results_comments.comment_by_user_id');

			})->join("learning_modules as lmone", function ($query) use ($params) {
				$query->on("lmone.id", "=", "learning_results_comments.learning_module_id");
			})
			->leftjoin("user_learning_modules", function ($query) use ($params,$user_id) {
				$query->on("user_learning_modules.learning_module_id", "=", "learning_results_comments.learning_module_id")
				->where("user_learning_modules.user_id",$user_id)->whereNull('user_learning_modules.deleted_at');
			})->leftjoin('learning_results',function ($query) use ($user_id){
			   $query->on("learning_results.id","learning_results_comments.learning_results_id")
			   ->where("learning_results.user_id",$user_id)
			   ->whereNull('learning_results.deleted_at');
			})
			// Remove the whereNotNull condition to allow comments without learning results
			// ->whereNotNull('learning_results.id')
		;
		if (\APP\Auth::isLearner()) {
			$query = $query
				->where('learning_results_comments.visible_learner', true);
		}
		if (isset($params["search"])) {
			if (isset($params["search"]["name"])) {
				$query->join("learning_modules as lmtwo", function ($query) use ($params) {
					$query->on("lmtwo.id", "=", "learning_results_comments.learning_module_id")
						->where('lmtwo.name', 'LIKE', '%'.$params["search"]["name"].'%');
				});
				unset($params["search"]["name"]);
			}
			if (isset($params["search"]["fnameAAAlname"])) {
				$query->join('users as utwo', function ($join) use($params) {
					$join->on('utwo.id', '=', 'learning_results_comments.comment_by_user_id')
					->where(function($query) use ($params) {
						$query
							->where('utwo.fname', 'LIKE', '%'.$params["search"]["fnameAAAlname"].'%')
							->orWhere('utwo.lname', 'LIKE', '%'.$params["search"]["fnameAAAlname"].'%')
						;
					});
				});
				unset($params["search"]["fnameAAAlname"]);
			}
			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}
		}
		$data = \APP\SmartTable::searchPaginate($params, $query);
		$response->getBody()->write($data->toJson());
		return $response->withHeader('Content-Type', 'application/json');


	})->add(\APP\Auth::getStructureAccessCheck(['system-setup-organisation-users', 'trainee-learning-results'], 'select'));

	$group->delete("/comment/{id:[0-9]+}", function (Request $request, Response $response, array $args) {
		$data = \Models\LearningResultsComment::find($args['id']);
		if ($data) {
			$data->deleted_by = \APP\Auth::getUserId();
			$data->save();
			$data->delete();
		}
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck(['misc-permissions-learning-results-comments'], 'disable'));

	// Update 2FA
	$group->put('/disable2FA/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$user = \Models\User::find($args["id"]);

		// Check if user is allowed to edit users
		if (
			\APP\Auth::roleAdminInterface() &&
			!\APP\Auth::roleAdminInterfaceEditUsers()
		) {
			return \APP\Tools::returnCode($request, $response, 403, 'You don\'t have permissions to update user!');
		}


		if (!\Models\User::combinedRoleCheck($data, $user)) {
			return \APP\Tools::returnCode($request, $response, 403, 'You don\'t have permissions to add or update user with this role!');
		}
		$user->enabled_google_2FA = false;
		$user->updated_by = \APP\Auth::getUserId();
		$user->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-users', 'update'));

	// Update individual user, update user
	$group->put('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$user = \Models\User::find($args["id"]);

		// Check if user is allowed to edit users
		if (
			\APP\Auth::roleAdminInterface() &&
			!\APP\Auth::roleAdminInterfaceEditUsers()
		) {
			return \APP\Tools::returnCode($request, $response, 403, 'You don\'t have permissions to update user!');
		}


		if (isset($data["password"]) && $data["password"] && !preg_match('/' . $this->get('settings')["LMSPasswordPattern"] . '/', $data["password"])) {
			return \APP\Tools::returnCode($request, $response, 406, 'Password validation failed!');
		} elseif (isset($data["password"]) && $data["password"]) {
			$user->password = password_hash($data["password"], PASSWORD_BCRYPT, ['cost' => 12]);
		}
		if(isset($data['custom_field']))
		{
		Form::saveCustomForm($data['custom_field'],'user',$user->id);
		}
		$previous_department = $user->department_id;

		$fields = [
			"username", "usercode", "altusercode", "fname", "lname", "email", "skype_id", "zoom_id", "teams_id", "email2", "phone",
			"designation_id", "country_id", "company_id", "department_id",
			"location_id", "city_id", "role_id", "description", "expiration_dt",
			"registration_dt", "staff_type_id", "report_to", "week_hours", "school",
			"emergency_name", "emergency_relationship", "emergency_contact_numbers",
			"visa_length", "visa_number", "visa_date", "exclude_from_reports", "exclude_from_ilr_export", "exclude_from_emails","discount_percentage",

			"ULN", "UKPRN", "LearnRefNumber", "PrevLearnRefNumber", "PrevUKPRN", "PMUKPRN",
			"DateOfBirth", "Ethnicity", "Sex", "LLDDHealthProb", "NINumber", "PriorAttainLegacy", "PriorAttain",
			"Accom", "ALSCost", "PlanLearnHours", "PlanEEPHours", "MathGrade", "EngGrade",
			"PostcodePrior", "Postcode", "AddLine1", "AddLine2", "AddLine3", "AddLine4",
			"ContactPreference", "LLDDandHealthProblem", "LearnerFAM",
			"ProviderSpecLearnerMonitoring", "LearnerHE",
			"LearningDelivery", "CampId", "OTJHours", "accessible_ui", "account_type_id","startup_instructions_sent",
			"password_attempts",

			// Lancs fields, not sure yet hot to differenciate them
			"position_ref", "watch", "watch_id",
		];

		// User can be updated only by admin
		// If manager is updated by non admin, \APP\Auth::allowAssigningManagerRoles() will be checked
		if (
			\APP\Auth::getUserId() != $user->id &&
			!\Models\User::combinedRoleCheck($data, $user)
		) {
			return \APP\Tools::returnCode($request, $response, 403, 'You don\'t have permissions to add or update user with this role!');
		}


		// Sometimes this is served as string, this is just a workaround! What a hassle!
		if (empty($data['PrevUKPRN']) || $data['PrevUKPRN'] == 'null') {
			$data['PrevUKPRN'] = null;
			$user->PrevUKPRN = null;
		}

		if (
			empty($data['ULN']) ||
			$data['ULN'] == 'null'
		) {
			$data['ULN'] = null;
			$user->ULN = null;
		}

		$old_designation = false;
		if (isset($data['designation_id'])) {
			$old_designation = $data['designation_id'];
		}

		foreach ($fields as $field) {

			// if manager has no access to all companies, company field can not be updated for other users.
			if (
				!\APP\Auth::isAdmin() &&
				!\APP\Auth::accessAllCompanies() &&
				$field == 'company_id'
			) {
				continue;
			}

			if (array_key_exists($field, $data)) {
				$user->$field = $data[$field];
			}
		}

		// encode json object to json string, for ILR entries
		//$user->ContactPreference = \APP\Tools::ilrJsonEncod($data, 'ContactPreference');
		$user->LLDDandHealthProblem = \APP\Tools::ilrJsonEncod($data, 'LLDDandHealthProblem');
		$user->LearnerFAM = \APP\Tools::ilrJsonEncod($data, 'LearnerFAM');

		$user->ProviderSpecLearnerMonitoring = \APP\Tools::ilrJsonEncod($data, 'ProviderSpecLearnerMonitoring');
		$user->LearnerEmploymentStatus = \APP\Tools::ilrJsonEncod($data, 'LearnerEmploymentStatus');
		$user->LearnerHE = \APP\Tools::ilrJsonEncod($data, 'LearnerHE');
		$user->LearningDelivery = \APP\Tools::ilrJsonEncod($data, 'LearningDelivery');
		$user->LearnerDestinationandProgression = \APP\Tools::ilrJsonEncod($data, 'LearnerDestinationandProgression');


		// Convert date to safe date
		if (!empty($data["DateOfBirth"])) {
			$user->DateOfBirth = \Carbon\Carbon::parse($data["DateOfBirth"]);
		}

		if (!empty($data["visa_date"])) {
			$user->visa_date = \Carbon\Carbon::parse($data["visa_date"]);
		}

		if (!empty($data["expiration_dt"])) {
			$user->expiration_dt = \Carbon\Carbon::parse($data["expiration_dt"]);
		} else {
			$user->expiration_dt = null;
		}

		if (!empty($data["registration_dt"])) {
			$user->registration_dt = \Carbon\Carbon::parse($data["registration_dt"]);
		} else {
			$user->registration_dt = null;
		}

		$user->updated_by = \APP\Auth::getUserId();
		$user->save();

		// IF extension fields are present loop them and update data accordingly.
		if (isset($data["extended"])) {

			// check if Contact_Relationship__c needs updating!
			if ($this->get('settings')["licensing"]['version'] == 'nras') {
				$data["extended"]['Contact_Relationship__c'] = \APP\Api::salesforceContactRelationshipCheck(isset($data["extended"]['Contact_Relationship__c']) ? $data["extended"]['Contact_Relationship__c'] : '');
			}

			foreach ($data["extended"] as $field_name => $value) {
				\Models\TableExtension::updateField('users', $user->id, $field_name, $value);
			}
		}

		if (
			$this->get('settings')["licensing"]['version'] == 'nras' &&
			$user->email
		) {
			$api_response = \APP\Api::salesforceUpdateRemoteUser($user);
			// $api_response will be returned only when exception is cought, give user some usable information what is going on.
			if ($api_response) {
				return \APP\Tools::returnCode($request, $response, 400, $api_response);
			}
		}

		// do SMCR check in case jobs change then assign/remove lesson asociated with that job
		//\Models\User::AssignDesignation($user->designation_id, $this->get('settings'), $old_designation, $user->id);

		// Copy Learning Delivery to seperate table, temporary solution, eventually I will have to remove JSON functionality and use proper SQL relationships.
		try {
			\Models\User::syncIlrData($this->get('settings')['ilr_fields'], $user->id);
		} catch (\Exception $e) {
			return \APP\Tools::returnCode($request, $response, 400, 'Invalid ILR data: ' . json_encode($e->getMessage()));
		}

		// Check if learning delivery has standard reference code, if it does and user is assigned that standard, use ILR dates!
		if (
			$this->get('settings')['licensing']['isApprentix'] &&
			\APP\Tools::getConfig('linkIlrToUserProgramme')
		) {
			\Models\ApprenticeshipStandardUser::linkIlrProgramme([$user->id]);
		}


		// If department_id is given, assign user to managers responsible for given department.
		if (
			isset($data['department_id']) &&
			$data['department_id'] &&
			$data['department_id'] != $previous_department
		) {

			//remove old department
			$managers = \Models\ManagerDepartment
				::where('department_id', '=', $previous_department)
				->get();
			foreach ($managers as $manager) {
				$managerUser = \Models\ManagerUser
					::where('manager_id', '=', $manager->manager_id)
					->where('user_id', '=', $user->id)
					->delete();
			}

			// add to new department
			$managers = \Models\ManagerDepartment
				::where('department_id', '=', $data['department_id'])
				->get();
			foreach ($managers as $manager) {
				$managerUser = new \Models\ManagerUser;
				$managerUser->manager_id = $manager->manager_id;
				$managerUser->user_id = $user->id;
				$managerUser->save();
			}
		}

		// Send instruction e-mail, if needed
		if (
			isset($data['send_instruction_email']) &&
			$data['send_instruction_email']
		) {
			\APP\Email::sendInstructionEmail($user->id);
		}


		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-users', 'update'));

	// Add new user, add user
	$group->post('/new', function (Request $request, Response $response) {
		$data = $request->getParsedBody();

		// if sharedClients, check if user limit is reached.
		if (
			\APP\Tools::getConfig('sharedClients') &&
			\APP\Auth::isManager() &&
			!\APP\Auth::isAdmin()
		) {
			$user_limit = $this->get('settings')['clientCompanyUserLimit'];
			$company_users = \Models\User
				::where('company_id', \APP\Auth::getUserCompanyId())
				->get()
			;
			if (count($company_users) >= $user_limit) {
				return \APP\Tools::returnCode($request, $response, 403, 'Limit reached');
			}
		}
        if(\DB\LicenseFeatures::checkFeatureLimitReached(Auth::getUser(),'users') && \APP\Tools::getConfig('sharedClients')){
            return \APP\Tools::returnCode($request, $response, 403, 'Licence Limit reached');
        }


		$user = new \Models\User;
		$user->creation_notes = 'Manually created from administration interface.';

		$fields = [
			"username", "usercode", "altusercode", "fname", "lname", "email", "skype_id", "zoom_id", "teams_id", "email2", "phone",
			"designation_id", "country_id", "company_id", "department_id",
			"location_id", "city_id", "role_id", "description", "expiration_dt",
			"registration_dt", "staff_type_id", "report_to", "week_hours", "school",
			"emergency_name", "emergency_relationship", "emergency_contact_numbers",
			"visa_length", "visa_number", "visa_date", "exclude_from_reports", "exclude_from_ilr_export", "exclude_from_emails","discount_percentage",

			"ULN", "UKPRN", "LearnRefNumber", "PrevLearnRefNumber", "PrevUKPRN", "PMUKPRN",
			"DateOfBirth", "Ethnicity", "Sex", "LLDDHealthProb", "NINumber", "PriorAttainLegacy", "PriorAttain",
			"Accom", "ALSCost", "PlanLearnHours", "PlanEEPHours", "MathGrade", "EngGrade",
			"PostcodePrior", "Postcode", "AddLine1", "AddLine2", "AddLine3", "AddLine4",
			"ContactPreference", "LLDDandHealthProblem", "LearnerFAM",
			"ProviderSpecLearnerMonitoring", "LearnerHE",
			"LearningDelivery", "CampId", "OTJHours", "accessible_ui", "account_type_id", "startup_instructions_sent",

			// Lancs fields, not sure yet hot to differenciate them
			"position_ref", "watch", "watch_id",
		];

		// Check if user adding this user is admin, if not, check if added role is admin, if so, return 403
		\Models\User::adminRoleCheck($response, $data['role_id']);

		// Ir person adding user is not admin and role added is managerial role that is not admin, check for allow_assigning_manager_roles role permission
		\Models\User::managerRoleCheck($response, $data['role_id']);

		// Sometimes this is served as string, this is just a workaround! What a hassle!
		if (empty($data['PrevUKPRN']) || $data['PrevUKPRN'] == 'null') {
			$data['PrevUKPRN'] = null;
			$user->PrevUKPRN = null;
		}


		foreach ($fields as $field) {
			// If manager has no access to all companies, user company is set to same as manager company.
			if (
				!\APP\Auth::isAdmin() &&
				!\APP\Auth::accessAllCompanies() &&
				$field == 'company_id'
			) {
				if (\APP\Auth::getUserCompanyId()) {
					$user->company_id = \APP\Auth::getUserCompanyId();
				}
				continue;
			}

			if (array_key_exists($field, $data)) {
				$user->$field = $data[$field];
			}
		}

		// encode json object to json string, for ILR entries
		//$user->ContactPreference = \APP\Tools::ilrJsonEncod($data, 'ContactPreference');
		$user->LLDDandHealthProblem = \APP\Tools::ilrJsonEncod($data, 'LLDDandHealthProblem');
		$user->LearnerFAM = \APP\Tools::ilrJsonEncod($data, 'LearnerFAM');
		$user->ProviderSpecLearnerMonitoring = \APP\Tools::ilrJsonEncod($data, 'ProviderSpecLearnerMonitoring');
		$user->LearnerEmploymentStatus = \APP\Tools::ilrJsonEncod($data, 'LearnerEmploymentStatus');
		$user->LearnerHE = \APP\Tools::ilrJsonEncod($data, 'LearnerHE');
		$user->LearningDelivery = \APP\Tools::ilrJsonEncod($data, 'LearningDelivery');
		$user->LearnerDestinationandProgression = \APP\Tools::ilrJsonEncod($data, 'LearnerDestinationandProgression');

		// Convert date to safe date
		if (!empty($data["DateOfBirth"])) {
			$user->DateOfBirth = \Carbon\Carbon::parse($data["DateOfBirth"]);
		}

		if (!empty($data["visa_date"])) {
			$user->visa_date = \Carbon\Carbon::parse($data["visa_date"]);
		}

		if (empty($data["registration_dt"])) {
			$user->registration_dt = \Carbon\Carbon::now();
		}

		if (isset($data["password"]) && $data["password"] && preg_match('/' . $this->get('settings')["LMSPasswordPattern"] . '/', $data["password"])) {
			$user->password = password_hash($data["password"], PASSWORD_BCRYPT, ['cost' => 12]);
			$user->status = 1;
			$user->save();
			if (
				$this->get('settings')['licensing']['isApprentix'] &&
				\APP\Tools::getConfig('linkIlrToUserProgramme')
			) {
				\Models\ApprenticeshipStandardUser::linkIlrProgramme([$user->id]);
			}
			Form::saveCustomForm($data['custom_field'],'user',$user->id);
			\APP\Tools::updateLearnerRefNum();

			// Copy Learning Delivery to seperate table, temporary solution, eventually I will have to remove JSON functionality and use proper SQL relationships.
			\Models\User::syncIlrData($this->get('settings')['ilr_fields'], $user->id);

			// If department_id is given, assign user to managers responsible for given department.
			if (isset($data['department_id']) && $data['department_id']) {
				$managers = \Models\ManagerDepartment
					::where('department_id', '=', $data['department_id'])
					->get();
				foreach ($managers as $manager) {
					$managerUser = new \Models\ManagerUser;
					$managerUser->manager_id = $manager->manager_id;
					$managerUser->user_id = $user->id;
					$managerUser->save();
				}
			}

			// If groups are specified, add user to groups
			if (isset($data['groups'])) {
				foreach ($data['groups'] as $key => $group) {
					Models\GroupUser::firstOrCreate(
						[
							'user_id' => $user->id,
							'group_id' => $group['id'],
							'status' => 1,
						]
					);
					\Models\GroupLearningModule::AssingToUser($group['id'], $user);
				}
			}

			// If sub departments are specified, add mapping
			if (isset($data['sub_departments'])) {
				foreach ($data['sub_departments'] as $key => $user_sub_dep) {
					Models\UserSubDepartment::firstOrCreate(
						[
							'user_id' => $user->id,
							'department_id' => $user_sub_dep['department_id']
						]
					);
				}
			}

		} else {
			// no password given!
			return \APP\Tools::returnCode($request, $response, 406, 'Password validation failed!');
		}

		// Send instruction e-mail, if needed
		if (
			isset($data['send_instruction_email']) &&
			$data['send_instruction_email']
		) {
			\APP\Email::sendInstructionEmail($user->id);
		}

		// IF extension fields are present loop them and update data accordingly.
		if (isset($data["extended"])) {
			foreach ($data["extended"] as $field_name => $value) {
				\Models\TableExtension::updateField('users', $user->id, $field_name, $value);
			}
		}

		// If manager is on administrate interface and role allows for assigning user upon adding, assign user to logged in manager
		if (
			\APP\Auth::isManager() &&
			\APP\Auth::roleAdminInterface() &&
			\APP\Auth::roleAdminInterfaceAddUsersAssign() &&
			\APP\Auth::getUserId()
		) {
			$managerUser = new \Models\ManagerUser;
			$managerUser->manager_id = \APP\Auth::getUserId();
			$managerUser->user_id = $user->id;
			$managerUser->save();
		}

		// if new user is learner check configuration "OnboardingSignoffFormWorkflow" , assign form workflow mentioned in the configuration
        if($user->role->is_learner) {
			$form_workflow_id = \APP\Tools::getConfig('OnboardingSignoffFormWorkflow');
			\Models\FormWorkflow::assignUsersToWorkFlow($user->id, $form_workflow_id, [
				"user_id" => $user->id,
				"type" => "direct",
				"type_id" => $form_workflow_id,
				"assigned_method"=>'direct',
				"reference_type_id"=>NULL,
				"reference_type"=>NULL
			]);
		}

		// If manager is adding user, add user managed by
		if (
			\APP\Auth::isManager() &&
			!\APP\Auth::isAdmin()
		) {
			$managerUser = new \Models\ManagerUser;
			$managerUser->manager_id = \APP\Auth::getUserId();
			$managerUser->user_id = $user->id;
			$managerUser->save();
		}

		$response->getBody()->write(json_encode($user->id));
		return $response;

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-users', 'insert'));

	$group->group("/validate", function ($group) {

		$group->get('/uln/{uln: .+}', function (Request $request, Response $response, $args) {

			$uln = $args["uln"];
			$params = $request->getQueryParams();

			// If user is not logged in(I have no idea how to detect it here), do not use exclude_id
			if (!isset($params["exclude_id"])) {
				$params["exclude_id"] = 0;
			}

			if (isset($params["exclude_id"])) {
				if (
				\Models\User::where("ULN", $uln)
					->where("id", "<>", $params["exclude_id"])
					->first()
				) {
					return \APP\Tools::returnCode($request, $response, 409, 'Already exists');
				}
			}

			$response
				->getBody()
				->write("ok")
			;
			return $response;
		});


		$group->get('/username/{username: .+}', function (Request $request, Response $response, $args) {

			$username = $args["username"];
			$params = $request->getQueryParams();

			// If user is not logged in(I have no idea how to detect it here), do not use exclude_id
			if (!isset($params["exclude_id"])) {
				$params["exclude_id"] = 0;
			}

			if (isset($params["exclude_id"])) {
				if (
					\Models\User::where("username", "=", $username)->where("id", "<>", $params["exclude_id"])->first() &&
					\APP\Tools::getConfig('uniqueUsernamePerUser')
				) {
					return \APP\Tools::returnCode($request, $response, 409, 'Already exists');
				}
			}

			$response
				->getBody()
				->write("ok")
			;
			return $response;
		});

		$group->get('/email/[{email: .+}]', function (Request $request, Response $response, $args) {

			$email = $args["email"];

			$params = $request->getQueryParams();

			// If user is not logged in(I have no idea how to detect it here), do not use exclude_id
			if (empty($params["exclude_id"])) {
				$params["exclude_id"] = 0;
			}

			if (isset($params["exclude_id"])) {
				if (
				\Models\User::where("email", "=", $email)
					->where("id", "<>", $params["exclude_id"])->first() && \APP\Tools::getConfig('uniqueEmailPerUser')
				) {
					return \APP\Tools::returnCode($request, $response, 409, 'Already exists');
				}
			}

			$response
				->getBody()
				->write("ok")
			;
			return $response;
		});

		$group->get('/usercode', function (Request $request, Response $response) {
			$params = $request->getQueryParams();

			$usercode = $params['value'] ?? null;
			$excludeId = $params['exclude_id'] ?? 0;

			if (
				$usercode &&
				\Models\User::where("usercode", $usercode)
					->where("id", "<>", $excludeId)->first() &&
				\APP\Tools::getConfig('uniqueUserCodePerUser')
			) {
				return \APP\Tools::returnCode($request, $response, 409, 'Already exists');
			}

			$response->getBody()->write("ok");
			return $response;
		});

	})->add(\APP\Auth::getSessionRegisterCheck());

	// get all users if admin or ones assigned to you as manager
	$group->post('/learners/list', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();

		$query = \Models\User
			::where('id', '>', 0)
			->select(
				'id', 'fname', 'lname', 'company_id', 'department_id', 'location_id',
				DB::raw("CONCAT(users.fname, ' ', users.lname) as name")
			);

		if (
			!\APP\Auth::accessAllLearners() &&
			!\APP\Auth::isAdmin()
		) {
			$query = $query
				->whereIn("id", function ($query) {
					$query
						->select("user_id")
						->from("manager_users")
						->where("manager_id", "=", \APP\Auth::getUserId())
						->whereNull('manager_users.deleted_at');
				})
			;
		}
		if (isset($params['company_id']) && $params['company_id']) {
			$query = $query
				->where('company_id', '=', $params['company_id']);
		}
		if (isset($params['department_id']) && $params['department_id']) {
			$query = $query
				->where('department_id', '=', $params['department_id']);
		}
		if (isset($params['location_id']) && $params['location_id']) {
			$query = $query
				->where('location_id', '=', $params['location_id']);
		}
		$query = $query->get();


		$response->getBody()->write(gzencode(json_encode($query)));
		return $response
			->withHeader('Content-Encoding', 'gzip')
			->withHeader('Content-Type', 'application/json')
		;
	})->add(\APP\Auth::getStructureAccessCheck(['system-setup-organisation-users'], 'select'));

	// Paginated list of all users
	$group->post('/list{download:[\/a-z]*}', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();

		$query = \Models\User
			::select([
				'users.*',
			])
			->with(['groups' => function($query){
				$query->where('group_users.status','=',1);

			}])
			->with(['company' => function($query) {
				$query->select('id','name');
			}])
			->with(['department' => function($query){
				$query->select('id', 'name');
			}])
			->with(['designation' => function ($query) {
				$query->select('id', 'name');
			}])
			->with(['location' => function ($query) {
				$query->select('id', 'name');
			}])
			->with(['role' => function($query){
				$query->select('id', 'name', 'is_learner', 'status', 'is_manager', 'is_admin', 'is_qa', 'is_cd', 'is_fa');
            }])
            ->with(['Managers' => function($query){
                $query->select('users.id', 'users.fname', 'users.lname');
            }])
		;

		$query = \Models\Role::userAccessList($query);
		$query = \Models\Schedule::countAndConditions($query, $params);


		if (
			isset($params["search"]) &&
			is_array($params["search"])
		) {

			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}
			if(isset($params["search"]["group_id"])) {
				$query->whereHas('groups', function ($query) use ($params) {
					$query
						->where('group_users.group_id','=', $params["search"]["group_id"])
					;
				});
				unset($params["search"]["group_id"]);
            }
            if(isset($params['search']['manager']))
            {
                $searchTerm = $params['search']['manager'];
                $query->whereHas('Managers', function ($query) use ($searchTerm) {
                $query->where(DB::raw('CONCAT(laravel_reserved_0.fname, " ", laravel_reserved_0.lname)'), 'LIKE', "%{$searchTerm}%")
                        ->orWhere('laravel_reserved_0.fname', 'like',"%{$searchTerm}%")
                        ->orWhere('laravel_reserved_0.lname', 'like',"%{$searchTerm}%")
                    ;
                });
                unset($params['search']['manager']);
            }

			if (isset($params["search"]["status"])) {
				$params["search"]["users__status"] = $params["search"]["status"];
				unset($params["search"]["status"]);
			}
			if (isset($params["search"]["approval_status"])) {
				$params["search"]["users__approval_status"] = $params["search"]["approval_status"];
				unset($params["search"]["approval_status"]);
			}
		}

		if (isset($params["sort"]) && isset($params["sort"]["predicate"]) && isset($params["sort"]["reverse"])) {
			$query->orderBy($params["sort"]["predicate"], $params["sort"]["reverse"] ? "DESC" : "ASC");
		}


		if (isset($args["download"]) && $args["download"] == "/download") {

			$query = $query
				->selectRaw('DATE_FORMAT(registration_dt, "' . \APP\Tools::defaultDateFormatMYSQL() . '") as registration_date')
				->with(['groups' => function ($query) {
					$query->select('groups.id', 'groups.name');
				}]);

			$data = \APP\SmartTable::searchPaginate($params, $query, false, false);


			// Need to add groups to user, coma seperated!
			// LOOP IT!

			foreach ($data as $key => $user) {
				$group_cnt = 0;
				foreach ($user->groups as $groupKey => $group) {
					$user->group_names = $user->group_names . ($group_cnt == 0 ? '' : ', ') . $group->name;
					$group_cnt++;
				}
			}

			$export_fields = [
				"ID" => "id",
				"Employee Code" => "usercode",
				"Alternative Code" => "altusercode",
				"First Name" => "fname",
				"Last Name" => "lname",
				"Company" => "company.name",
				"Department" => "department.name",
				"Designation" => "designation.name",
				"Location" => "location.name",
				"Role" => "role.name",
				"Country" => "country.name",
				"City" => "city.name",
				"Email" => "email",
				"Phone" => "phone",
				"User Name" => "username",
				"Description" => "description",
				"Group Name" => "group_names",
				"Registration Date" => "registration_date",
				"Accessible UI" => "accessible_ui"
			];

			// IF apprentix, add ULN too
			if ($this->get('settings')['licensing']['isApprentix']) {
				$export_fields["ULN"] = "ULN";
			}


			$download_file_name = uniqid("users.list.") . ".xlsx";

			\APP\Tools::generateExcelDownload(
				$data,
				$export_fields,
				$this->get('settings')["LMSTempPath"] . $download_file_name
			);

			$response->getBody()->write(json_encode($download_file_name));
			return $response->withHeader('Content-Type', 'application/json');
		} else {
			$p = \APP\SmartTable::searchPaginate($params, $query);
			$response->getBody()->write($p->toJson());
			return $response->withHeader('Content-Type', 'application/json');

		}
	})->add(\APP\Auth::getStructureAccessCheck(['system-setup-organisation-users'], 'select'));

	$group->post('/import', function (Request $request, Response $response) {
		$params = $request->getParsedBody();

		$n_users_updated = 0;
		$n_users_inserted = 0;
		$n_users_disabled = 0;
		$n_users_deleted = 0;
		$n_users_rejected = 0;



		// If import is sent with checkboxes for introduction emails, prepare roles
		$notify_roles = [];
		if (
			isset($params["type"]) &&
			(
				$params["type"] == "import_ilr" ||
				$params["type"] == "import_new"
			)
		) {
			$notify_types = [
				'notify_admin' => 'is_admin',
				'notify_manager' => 'is_manager',
				'notify_learner' => 'is_learner',
			];

			foreach ($notify_types as $key => $notify_type) {
				if (
					isset($params[$key]) &&
					$params[$key] &&
					(
						$params[$key] === true ||
						$params[$key] === 'true'
					)
				) {
					$notify_roles[] = $notify_type;
				}
			}
		}

		if (isset($_FILES['importFile'])) {

			// Set up the filesystem adapter and filesystem
			$adapter = new LocalFilesystemAdapter($this->get('settings')["LMSTempPath"]);
			$filesystem = new Filesystem($adapter);

			// Get the uploaded file
			$file = $_FILES['importFile'];
			$import_file_name = basename($file['name']);
			$import_file_id = uniqid();
			$destination_path = $import_file_id . '.' . pathinfo($import_file_name, PATHINFO_EXTENSION);


			try {
				// Read the file content
				$stream = fopen($file['tmp_name'], 'r+');
				if ($stream === false) {
					throw new \Exception('Failed to open file for reading');
				}

				// Write the file to the filesystem
				$filesystem->writeStream($destination_path, $stream);

				if (is_resource($stream)) {
					fclose($stream);
				}
			} catch (FilesystemException $e) {
				return \APP\Tools::returnCode($request, $response, 500, $e->getMessage());
			} catch (\Exception $e) {
				return \APP\Tools::returnCode($request, $response, 500, $e->getMessage());
			}

			// Log import files!
			$imported_file_content = $filesystem->read($destination_path);
			\Models\LogExportImport::insertRecord($imported_file_content, '.' . pathinfo($import_file_name, PATHINFO_EXTENSION), $import_file_name, false, 'imports');


			// Imports standard ILR format XML file
			//https://www.gov.uk/government/uploads/system/uploads/attachment_data/file/647314/ILRSpecification2017-2018v3.pdf
			if ($params['type'] == 'import_ilr') {
				$xml_string = file_get_contents($this->get('settings')["LMSTempPath"] . $destination_path);
				$xml = @simplexml_load_string($xml_string);

				// get default role
				$defaultRole = \Models\Configuration
					::where('key', '=', 'defaultIlrRole')
					->where('status', '=', 1)
					->first()
				;

				if ($xml) {
					// Iterate all Learners to be imported
					foreach ($xml->Learner as $learnerKey => $learner) {
						// If learner with this ULN exists - update entries, else insert.
						if (isset($learner->ULN) && $learner->ULN) {
							$user = \Models\User
								::firstOrNew(
									['ULN' => $learner->ULN]
								)
							;
							// If learner can't be found by ULN, find by email, if Email exists.
							if (
								!$user->exists &&
								$learner->Email
							) {
								$user = \Models\User
									::firstOrNew(
										['email' => $learner->Email]
									)
								;
								if ($learner->ULN) {
									$user->ULN = $learner->ULN;
								}
							}
							if (!$user->exists) {
								$user->status = 1;
							}
							$user->username = $user->username ? $user->username : \APP\Tools::ilrUsername($learner);
							$user->fname = $learner->GivenNames;
							$user->lname = $learner->FamilyName;
							$user->password = $user->password ? $user->password : null;
							$user->email = $user->email ? $user->email : \APP\Tools::setUniqueEmail($learner->Email, $learner->ULN);
							$user->phone = $learner->TelNo ? $learner->TelNo : ($user->phone ? $user->phone : "");
							$user->designation_id = $user->designation_id ? $user->designation_id : null;
							$user->country_id = $user->country_id ? $user->country_id : null;
							$user->company_id = isset($params['company']) && $params['company'] ? $params['company'] : ($user->company_id ? $user->company_id : null);
							$user->department_id = isset($params['department']) && $params['department'] ? $params['department'] : ($user->department_id ? $user->department_id : null);
							$user->location_id = $user->location_id ? $user->location_id : null;
							$user->city_id = $user->city_id ? $user->city_id : null;

							// if (
							// 	empty($user->role_id) &&
							// 	isset($defaultRole->value) &&
							// 	// Will return false if you don't have permissions to add this role, without true it will redirect with 403
							// 	\Models\User::adminRoleCheck($response, $defaultRole->value, true)
							// ) {
							// 	$user->role_id = $defaultRole->value;
							// } else if ($user->role_id) {
							// 	$user->role_id = $user->role_id;
							// } else {
							// 	$user->role_id = null;
							// }

							if (
								isset($user->role_id) &&
								isset($defaultRole->value) &&
								\Models\User::adminRoleCheck($response, $user->role_id, true)
							) {
								$user->role_id = $user->role_id;
							} else {
								$user->role_id = $defaultRole->value;
							}

							$user->description = $user->description ? $user->description : null;
							$user->registration_dt = $user->registration_dt ? $user->registration_dt : \Carbon\Carbon::now();


							$user->UKPRN = $xml->LearningProvider->UKPRN > 0 ? $xml->LearningProvider->UKPRN : \APP\Tools::getConfig('defaultUKPRN');
							$user->LearnRefNumber = $learner->LearnRefNumber ? $learner->LearnRefNumber : \APP\Tools::getLearnerRefNum();
							$user->PrevLearnRefNumber = $learner->PrevLearnRefNumber;
							$user->PrevUKPRN = isset($learner->PrevUKPRN) ? $learner->PrevUKPRN : (\APP\Tools::getConfig('defaultPrevUKPRN') ? \APP\Tools::getConfig('defaultPrevUKPRN') : null);
							$user->PMUKPRN = isset($learner->PMUKPRN) ? $learner->PMUKPRN : null;
							//ULN - used to identify existing entries
							//FamilyName - uses default field "lname"
							//GivenNames - uses default field "fname"
							$user->CampId = $learner->CampId;
							$user->OTJHours = isset($learner->OTJHours) ? $learner->OTJHours : null;
							$user->DateOfBirth = \Carbon\Carbon::parse($learner->DateOfBirth);
							$user->Ethnicity = $learner->Ethnicity;
							$user->Sex = $learner->Sex;
							$user->LLDDHealthProb = $learner->LLDDHealthProb;
							$user->NINumber = $learner->NINumber;
							$user->PriorAttainLegacy = isset($learner->PriorAttainLegacy) ? $learner->PriorAttainLegacy : null;
							$user->Accom = isset($learner->Accom) ? $learner->Accom : null;
							$user->ALSCost = isset($learner->ALSCost) ? $learner->ALSCost : null;
							$user->PlanLearnHours = isset($learner->PlanLearnHours) ? $learner->PlanLearnHours : null;
							$user->PlanEEPHours = isset($learner->PlanEEPHours) ? $learner->PlanEEPHours : null;
							$user->MathGrade = $learner->MathGrade;
							$user->EngGrade = $learner->EngGrade;
							$user->PostcodePrior = $learner->PostcodePrior;
							$user->Postcode = $learner->Postcode;
							$user->AddLine1 = $learner->AddLine1;
							$user->AddLine2 = $learner->AddLine2;
							$user->AddLine3 = $learner->AddLine3;
							$user->AddLine4 = $learner->AddLine4;
							//TelNo - field "phone"
							//Email - field email

							// JSON blocks
							// Convert all nested properties into json object, string it and save as text.
							$user->PriorAttain = \APP\Tools::ilrjSonEncodeField($learner, 'PriorAttain', true);
							$user->ContactPreference = \APP\Tools::ilrjSonEncodeField($learner, 'ContactPreference');
							$user->LLDDandHealthProblem = \APP\Tools::ilrjSonEncodeField($learner, 'LLDDandHealthProblem');
							$user->LearnerFAM = \APP\Tools::ilrjSonEncodeField($learner, 'LearnerFAM');
							$user->ProviderSpecLearnerMonitoring = \APP\Tools::ilrjSonEncodeField($learner, 'ProviderSpecLearnerMonitoring');
							$user->LearnerEmploymentStatus = \APP\Tools::ilrjSonEncodeField($learner, 'LearnerEmploymentStatus');
							$user->LearnerHE = \APP\Tools::ilrjSonEncodeField($learner, 'LearnerHE');
							$user->LearningDelivery = \APP\Tools::ilrjSonEncodeField($learner, 'LearningDelivery');
							// EOF JSON blocks

							try {
								// if user exists and update checkbox is passed, update user
								if ($user->exists) {
									if (isset($params['update']) && $params['update'] && $params['update'] != 'false') {
										$n_users_updated++;
										$user->saveWithoutEvents();
										// Copy Learning Delivery to seperate table, temporary solution, eventually I will have to remove JSON functionality and use proper SQL relationships.
										\Models\User::syncIlrData($this->get('settings')['ilr_fields'], $user->id);
									}
									// If user does not exists, just insert it.
								} else {
									$n_users_inserted++;
									$user->saveWithoutEvents();
									// Copy Learning Delivery to seperate table, temporary solution, eventually I will have to remove JSON functionality and use proper SQL relationships.
									\Models\User::syncIlrData($this->get('settings')['ilr_fields'], $user->id);
									\APP\Tools::updateLearnerRefNum();
								}
								if (
									$this->get('settings')['licensing']['isApprentix'] &&
									\APP\Tools::getConfig('linkIlrToUserProgramme')
								) {
									\Models\ApprenticeshipStandardUser::linkIlrProgramme([$user->id]);
								}
								if (count($notify_roles) > 0) {
									\APP\Email::sendInstructionEmail($user->id, $notify_roles);
								}
							} catch (\Exception $e) {
								continue;
								//throw $e;
							}
						}
					}

					// Loop LearnerDestinationandProgression and update learning deliveries
					foreach ($xml->LearnerDestinationandProgression as $learnerKey => $LearnerDestinationandProgression) {
						$LDPUser = \Models\User::where('ULN', $LearnerDestinationandProgression->ULN)->first();

						// If user exists, get field "LearnerDestinationandProgression", if field is empty create empty array
						if ($LDPUser) {
							if ($LDPUser->LearnerDestinationandProgression) {
								$LDP = json_decode($LDPUser->LearnerDestinationandProgression, true);
							} else {
								$LDP = [
									'LearnRefNumber' => (string)$LearnerDestinationandProgression->LearnRefNumber,
									'ULN' => (string)$LearnerDestinationandProgression->ULN,
									'DPOutcome' => []
								];
							}

							// check if DPOutcome exists, if not, array it is!
							if (!isset($LDP['DPOutcome'])) {
								$LDP['DPOutcome'] = [];
							}

							if ($LearnerDestinationandProgression->DPOutcome) {
								foreach ($LearnerDestinationandProgression->DPOutcome as $key => $LDP_DPOutcome) {
									$outcome_exists = false;
									foreach ($LDP['DPOutcome'] as $key => $DPOutcome) {
										if (
											(
												isset($DPOutcome['OutType']) &&
												isset($LDP_DPOutcome->OutType) &&
												$DPOutcome['OutType'] == (string)$LDP_DPOutcome->OutType
											) &&
											(
												isset($DPOutcome['OutCode']) &&
												isset($LDP_DPOutcome->OutCode) &&
												$DPOutcome['OutCode'] == (string)$LDP_DPOutcome->OutCode
											) &&
											(
												isset($DPOutcome['OutStartDate']) &&
												isset($LDP_DPOutcome->OutStartDate) &&
												$DPOutcome['OutStartDate'] == (string)$LDP_DPOutcome->OutStartDate
											) &&
											(
												isset($DPOutcome['OutCollDate']) &&
												isset($LDP_DPOutcome->OutCollDate) &&
												$DPOutcome['OutCollDate'] == (string)$LDP_DPOutcome->OutCollDate
											)
										) {
											$outcome_exists = true;
										}
									}

									if (!$outcome_exists) {
										$new_outcome = [];
										if (isset($LDP_DPOutcome->OutType)) {
											$new_outcome['OutType'] = (string)$LDP_DPOutcome->OutType;
										}
										if (isset($LDP_DPOutcome->OutCode)) {
											$new_outcome['OutCode'] = (string)$LDP_DPOutcome->OutCode;
										}
										if (isset($LDP_DPOutcome->OutStartDate)) {
											$new_outcome['OutStartDate'] = (string)$LDP_DPOutcome->OutStartDate;
										}
										if (isset($LDP_DPOutcome->OutEndDate)) {
											$new_outcome['OutEndDate'] = (string)$LDP_DPOutcome->OutEndDate;
										}
										if (isset($LDP_DPOutcome->OutCollDate)) {
											$new_outcome['OutCollDate'] = (string)$LDP_DPOutcome->OutCollDate;
										}
										$LDP['DPOutcome'][] = $new_outcome;
									}
								}
							}

							$LDP['DPOutcome'] = array_values($LDP['DPOutcome']);
							$LDPUser->LearnerDestinationandProgression = json_encode($LDP);
							$LDPUser->save();
						}
					}
				}

			}

			if (
				$params['type'] == 'import_new' ||
				$params['type'] == 'import_delete' ||
				$params['type'] == 'import_disable' ||
				$params['type']=='import_gtaa'
				//|| $params['type'] == 'import_exclusive_disable'
			) {
				if($params['type']=='import_gtaa')
				{
					$n_users = ImportUserController::import($this->get('settings'), $this->get('settings')["LMSTempPath"] . $destination_path, $params, $response, $notify_roles);

				}else{
				$n_users = \Models\User::importNewUsers($this->get('settings'), $this->get('settings')["LMSTempPath"] . $destination_path, $params, $response, $notify_roles);
				}
				// In case something fails, return response object
				if (!is_array($n_users)) {
					return $n_users;
				}


				$n_users_updated = $n_users['n_users_updated'];
				$n_users_inserted = $n_users['n_users_inserted'];
				$n_users_disabled = $n_users['n_users_disabled'];
				$n_users_deleted = $n_users['n_users_deleted'];
				$n_users_rejected = $n_users['n_users_rejected'];
			}

			$response
				->getBody()
				->write(
					json_encode(
						[
							'updated' => $n_users_updated,
							'inserted' => $n_users_inserted,
							'disabled' => $n_users_disabled,
							'deleted' => $n_users_deleted,
							'rejected' => $n_users_rejected,
						]
					)
				)
			;
			return $response;
		};

	})->add(\APP\Auth::getStructureAccessCheck(['system-setup-organisation-users'], 'insert'));

	// Returns ILR fields defined in settings and templates saved in DB
	$group->get('/ilr_fields', function (Request $request, Response $response, $args) {
		//$user = \Models\User::findOrFail(\APP\Auth::getUserId());
		$ilr = new stdClass;
		$ilr->fields = $this->get('settings')['ilr_fields'];
		//$ilr->groups = $this->get('settings')['ilr_groups'];
		$ilr->templates = \Models\IlrTemplates::get();
		foreach ($ilr->templates as $key => $value) {
			$value->template = json_decode($value->template, true);
		}

		$response->getBody()->write(json_encode($ilr));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['system-setup-organisation-users'], 'select'));

	// Saves user's ILR template, to be reused with other users.
	$group->post('/ilr_template', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		$template = new \Models\IlrTemplates;
		$template->name = $data['name'];
		$template->template = json_encode($data['template']);
		$template->created_by = \APP\Auth::getUserId();
		$template->save();

	})->add(\APP\Auth::getStructureAccessCheck(['system-setup-organisation-users'], 'update'));

	$group->post('/ilr_template_edit/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		$template = \Models\IlrTemplates::find($args["id"]);
		$template->name = $data['name'];
		$template->template = json_encode($data['template']);
		$template->created_by = \APP\Auth::getUserId();
		$template->save();

	})->add(\APP\Auth::getStructureAccessCheck(['system-setup-organisation-users'], 'update'));

	$group->post('/ilr_template_delete/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		$template = \Models\IlrTemplates::find($args["id"]);
		$template->delete();

	})->add(\APP\Auth::getStructureAccessCheck(['system-setup-organisation-users'], 'update'));

	// Get list of managers
	$group->get('/managers', function (Request $request, Response $response, $args) {
		$managers = \Models\User
			::where('status', '=', 1)
			->whereIn('role_id',
				\Models\Role
					::where('is_manager', true)
					->orWhere('is_admin', true)
					->orWhere('is_qa', true)
					->orWhere('is_cd', true)
					->orWhere('is_fa', true)
					->select('id')
					->get()
			)
			->select('id', 'fname', 'lname')
			->get();

		$response->getBody()->write(json_encode($managers));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['system-setup-organisation-users'], 'select'));


	$group->get("/skype/{user_ids}[/{icon_size}]", function (Request $request, Response $response, $args) {

		$user_ids = explode(",", $args["user_ids"]);
		if (isset($args["icon_size"])) {
			$icon_size = intval($args["icon_size"]);
		} else {
			$icon_size = 32;
		}

		$skype_ids = \Models\User
			::whereIn("id", $user_ids)
			->pluck('skype_id')->toArray();

		foreach ($skype_ids as $i => $skype_id) {
			$skype_ids[$i] = "\"{$skype_id}\"";
		}

		$skype_html = "
			<html xmlns=\"http://www.w3.org/1999/xhtml\">
				<head>
					<meta content=\"text/html; charset=iso-8859-1\" http-equiv=\"Content-Type\">
					<title>test</title>
					<style>
						.skyype{border: 0px; float: right; width: 100px; height: {$icon_size}px; display:flex;}
						#SkypeButton_Call_italy-amo_1_paraElement {margin:0px !important;padding:0px !important;}
						#SkypeButton_Call_italy-amo_2_paraElement {margin:0px !important;padding:0px !important;}
						.skyype img {margin:0px !important; vertical-align:0px !important;}
					</style>
				</head>
				<body>
					<script type=\"text/javascript\" src=\"" . ($this->get('settings')['LMSUri']) . "js/skype-uri.js\"></script>
					<div id=\"SkypeButton_Call_italy-amo_1\" class=\"skyype\"></div>
					<span id=\"SkypeButton_Call_italy-amo_2\" class=\"skyype\"></span>
				</body>
			</html>
			<script type=\"text/javascript\">
				Skype.ui({
					\"name\": \"chat\",
					\"element\": \"SkypeButton_Call_italy-amo_2\",
					\"participants\": [" . implode(",", $skype_ids) . ", \"skype.test.user.1\"],
					\"imageSize\": {$icon_size}
				});
				Skype.ui({
					\"name\": \"call\",
					\"element\": \"SkypeButton_Call_italy-amo_1\",
					\"participants\": [" . implode(",", $skype_ids) . "],
					\"imageSize\": {$icon_size}
				});
			</script>
		";

		$response->getBody()->write($skype_html);
		return $response;

	})->add(\APP\Auth::getSessionCheck());

	// Get the managers of the current user which have an skype account
	$group->get('/my/managers/skype', function (Request $request, Response $response) {
		$managers = \Models\User
			::select(
				"fname", "lname", "email", "username", "skype_id", "teams_id", "zoom_id", "id"
			)
			->whereIn("id", function ($query) {
				$query
					->from("manager_users")
					->where("user_id", "=", \APP\Auth::getUserId())
					->select("manager_id")
					->whereNull('manager_users.deleted_at');
			})
			->where('status', '=', 1)
			//->whereNotNull("skype_id")
			//->where("skype_id", "<>", "")
			->get();

		$response->getBody()->write(json_encode($managers));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());


	// Get the colleagues of the current user which have an skype account
	$group->get('/my/colleagues/skype/{standard_id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$current_user_standards = \APP\Auth::getUser()->standards;
		$standard_ids = [];
		foreach ($current_user_standards as $standard) {
			$standard_ids[] = $standard->id;

		}
		if (count($standard_ids) > 1) {
			if ($args["standard_id"] > 0) {
				$standard_ids = [$args["standard_id"]];
			}
		}
		$colleagues = \Models\User
			::select(
				"fname", "lname", "email", "username", "skype_id", "teams_id", "zoom_id", "id"
			)
			->whereIn("id", function ($query) use ($standard_ids) {
				$query
					->from("apprenticeship_standards_users")
					->where("user_id", "<>", \APP\Auth::getUserId())
					->whereIn("standard_id", $standard_ids)
					->select("user_id");
			})
			->where('status', '=', 1)
			//->whereNotNull("skype_id")
			//->where("skype_id", "<>", "")
			->get();

		if (!\APP\Tools::getConfig('enableContactYourColleagues')) {
			$colleagues = [];
		}

		$response->getBody()->write(json_encode($colleagues));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());

	$group->get('/learner-ref-num', function (Request $request, Response $response, $args) {
		$number = \APP\Tools::getLearnerRefNum();

		$response->getBody()->write($number);
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck(['system-setup-organisation-users'], 'select'));

	// Get staff types
	$group->get('/staff-type/all', function (Request $request, Response $response, $args) {
		$staff_types = \Models\SmcrStaffType
			::where('status', true)
			->get();

		$response->getBody()->write(json_encode($staff_types));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['system-setup-organisation-users'], 'select'));

	$group->get('/update-progress', function (Request $request, Response $response, $args) {
		if (
			\APP\Auth::isAdmin() ||
			\APP\Auth::isCd()
		) {


			// This will mark userProgress and apprentixNightly cron tasks as to be run now

			$userProgress = \Models\Cron
				::where('function', 'userProgress')
				->first()
			;

			$apprentixNightly = \Models\Cron
				::where('function', 'apprentixNightly')
				->first()
			;

			$translator = \APP\Templates::getTranslator();
			$progreamme_translated = $translator->replaceVersionLabels('%%programme%%');

			if (
				(
					$userProgress->locked == 1 ||
					$userProgress->force_run == 1
				) &&
				(
					$apprentixNightly->locked == 1 ||
					$apprentixNightly->force_run == 1
				)
			) {
				$response = $response
					->withStatus(500)
				;
				$response_message = "Global user progress and $progreamme_translated progress are both set to be processed now or are running now, please wait for them to finish!";
			} else if (
				(
					$userProgress->locked == 0 &&
					$userProgress->force_run == 0
				) &&
				(
					$apprentixNightly->locked == 1 ||
					$apprentixNightly->force_run == 1
				)
			) {
				$response_message = "$progreamme_translated progress calculation is running now, Global user progress will be set to run next!";
				$userProgress->force_run = 1;
				$userProgress->save();
			} else if (
				(
					$userProgress->locked == 1 ||
					$userProgress->force_run == 1
				) &&
				(
					$apprentixNightly->locked == 0 &&
					$apprentixNightly->force_run == 0
				)
			) {
				$response_message = "Global user progress calculation is running now, $progreamme_translated progress will be set to run next!";
				$apprentixNightly->force_run = 1;
				$apprentixNightly->save();
			} else if (
				(
					$userProgress->locked == 0 &&
					$userProgress->force_run == 0
				) &&
				(
					$apprentixNightly->locked == 0 &&
					$apprentixNightly->force_run == 0
				)
			) {
				$response_message = "Global user progress and $progreamme_translated progress calculation will be set to run next!";
				$userProgress->force_run = 1;
				$userProgress->save();

				$apprentixNightly->force_run = 1;
				$apprentixNightly->save();
			}

			$response->getBody()->write($response_message);
			return $response;

		} else {
			return \APP\Tools::returnCode($request, $response, 403);
		}

	})->add(\APP\Auth::getSessionCheck());



	$group->put('/learning-status', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		if (
			$this->get('settings')['licensing']['isApprentix'] &&
			(
				\APP\Auth::isAdmin() ||
				\APP\Auth::signOffLearnerStatus()
			)
		) {
			$user = \Models\User::find($data['user_id']);
			if ($user) {
				$user->learning_status = $data['learning_status'];
				$user->save();
			}

			return
				$response;
		} else {
			return \APP\Tools::returnCode($request, $response, 403);
		}

	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'update'));

	// Return list of files user has uploaded
	$group->get('/uploaded-files/{user_id:[0-9]+}', function (Request $request, Response $response, $args) {
		$user = \Models\User::find($args["user_id"]);

		if (
			$user &&
			(
				\APP\Auth::isAdmin() ||
				\APP\Auth::accessAllLearners() ||
				\APP\Auth::isManagerOf($user->id)
			)
		) {
			$learning_module_evidences = \Models\LearningModuleEvidence
				::select(
					'learning_module_evidences.*',
					DB::raw("DATE_FORMAT(learning_module_evidences.created_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS created_at_uk")
				)
				->where(function($query) use ($user) {
					$query
						->where('user_id', $user->id)
						->orWhere('manager', 1)
					;
				})
				->where('evidence_type', '!=', 'comment')
				->withCount(['UserLearningModule' => function ($query) use ($user) {
					$query = $query
						->whereNull('deleted_at')
						->where('user_id', $user->id);
				}])
				->with(['Module' => function ($query) use ($user) {
					$query = $query
						->select(
							'id',
							'name',
							'type_id'
						)
						->with(['LearningResult' => function ($query) use ($user) {
							$query = $query
								->select(
									'id',
									'user_id',
									'learning_module_id',
									'favorite'
								)
								->where('refreshed', 0)
								->where('user_id', $user->id);
						}])
						->with('type')
					;
				}])
				/*
				->whereIn('learning_modules_id',
					\Models\UserLearningModule
						::select('learning_module_id')
						->where('user_id', $user->id)
						->get()
				)
				*/
				->orderBy('learning_module_evidences.created_at', 'DESC')
				->get();

			//loop resources and attach standard_ids
			foreach ($learning_module_evidences as $key => $learning_module_evidence) {
				$learning_module_evidence->standards = \Models\LearningModule::Standards($learning_module_evidence->learning_modules_id, $user->id);
				$learning_module_evidence->outcomes = \Models\LearningModule::Outcomes($learning_module_evidence->learning_modules_id, $user->id);
				$learning_module_evidence->criteria = \Models\LearningModule::Criteria($learning_module_evidence->learning_modules_id, $user->id);
			}

			// Will Get all Reflective log entries for user as well, downloaded file will be PDF containing text data added by user.
			$additional_resources = \Models\LearningModule
				::where('status', 1)
				->whereIn('type_id',
					\Models\LearningModuleType
						::select('id')
						->whereIn('slug', ['reflective_log'])
						->geT()
				)
				->whereHas('LearningResult', function ($query) use ($user) {
					$query
						->where('learning_results.user_id', $user->id)
						->whereNull('learning_results.deleted_at')
						->where('learning_results.refreshed', 0)
					;
				})
				->withCount(['UserLearningModule' => function ($query) use ($user) {
					$query = $query
						->whereNull('deleted_at')
						->where('user_learning_modules.user_id', $user->id)
					;
				}])
				/*
				->whereIn('id',
					\Models\UserLearningModule
						::select('learning_module_id')
						->where('user_id', $user->id)
						->get()
				)
				*/
				->with('type')
				->with(['LearningResult' => function ($query) use ($user) {
					$query = $query
						->select(
							'id',
							'user_id',
							'learning_module_id',
							'favorite',
							DB::raw("DATE_FORMAT(learning_results.created_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS created_at_uk")
						)
						->where('user_id', $user->id);
				}])
				->get()
			;

			foreach ($additional_resources as $key => $additional_resource) {
				$entry = new \stdClass;
				$entry->added_by = $additional_resource->created_by;
				$entry->created_at = $additional_resource->LearningResult->created_at;
				$entry->created_at_uk = $additional_resource->LearningResult->created_at_uk;
				$entry->evidence = \APP\Tools::safeName($additional_resource->name, '_') . '.pdf';
				$entry->evidence_type = 'file';
				$entry->extension = 'pdf';
				$entry->hash = false;
				$entry->id = $additional_resource->LearningResult->id;
				$entry->learning_modules_id = $additional_resource->id;
				$entry->user_learning_module_count = $additional_resource->user_learning_module_count;

				$entry->module = new \stdClass;
				$entry->module->id = $additional_resource->id;
				$entry->module->name = $additional_resource->name;

				$entry->module->learning_result = new \stdClass;
				$entry->module->learning_result->favorite = $additional_resource->LearningResult->favorite;
				$entry->module->learning_result->id = $additional_resource->LearningResult->id;

				$entry->module->type = new \stdClass;
				$entry->module->type->name = $additional_resource->type->name;
				$entry->module->type->slug = $additional_resource->type->slug;

				$entry->standards = \Models\LearningModule::Standards($additional_resource->id, $user->id);
				$entry->outcomes = \Models\LearningModule::Outcomes($additional_resource->id, $user->id);
				$entry->criteria = \Models\LearningModule::Criteria($additional_resource->id, $user->id);
				$learning_module_evidences[] = $entry;
			}

			$response->getBody()->write(json_encode($learning_module_evidences));
		return $response->withHeader('Content-Type', 'application/json');
		} else {
			return \APP\Tools::returnCode($request, $response, 403);
		}

	})->add(\APP\Auth::getSessionCheck());

	// Will combine all files specified in file_ids, zip up in private temp directory and serve URL to download that file
	$group->post('/prepare-download-uploaded-files/{user_id:[0-9]+}', function (Request $request, Response $response, $args) {
		$user = \Models\User::find($args["user_id"]);
		$data = $request->getParsedBody();

		if (
			$user &&
			(
				\APP\Auth::isAdmin() ||
				\APP\Auth::accessAllLearners() ||
				\APP\Auth::isManagerOf($user->id)
			) &&
			(
				(
					isset($data['file_ids']) &&
					is_array($data['file_ids'])
				) ||
				(
					isset($data['result_ids']) &&
					is_array($data['result_ids'])
				)
			)
		) {
			$learning_module_evidences = \Models\LearningModuleEvidence
				::where(function($query) use ($user) {
					$query
						->where('user_id', $user->id)
						->orWhere('manager', 1)
					;
				})
				->whereIn('id', $data['file_ids'] ?? [])
				->where('evidence_type', '!=', 'comment')
				->with('module')
				->get()
			;

			// result_ids will hold IDs of learning results that need to be converted into PDF file
			$learning_resources_results = \Models\LearningResult
				::where('user_id', $user->id)
				->whereIn('id', $data['result_ids'] ?? [])
				->with(['module' => function ($query) {
					$query
						->with('type')
					;
				}])
				->with(["comments" => function ($query) use ($data) {
					$query
						->where('status', true)
						->select()
						->addSelect(DB::raw("DATE_FORMAT(created_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS created_at_uk"))
						->with(["createdby" => function ($query) {
							$query
								->select("id", "fname", "lname", "role_id")
								->with('role');
						}])
						->whereIn('learning_results_comments.comment_by_user_id',
							\Models\LearningResult
								::whereIn('id', $data['result_ids'] ?? [])
								->pluck('user_id')
								->toArray()
						)
					;
				}])
				->get()
			;

			// Set into session key and map user_id and file name against that key
			$token = sha1(mt_rand(1, 90000) . 'SALT');

			$zip_path = $this->get('settings')["LMSPrivateTempPath"] . "{$token}.zip";
			$zip = new \ZipArchive();
			$zip->open($zip_path, \ZipArchive::CREATE);


			$files_exist = false;
			foreach ($learning_module_evidences as $key => $learning_module_evidence) {
				if (is_file($this->get('settings')["LMSEvidencePath"] . $learning_module_evidence->hash . '.' . $learning_module_evidence->extension)) {
					$zip_file_name = $learning_module_evidence->module->name . '/' . $learning_module_evidence->evidence;
					if ($zip->locateName($zip_file_name)) {
						$zip_file_name = $learning_module_evidence->module->name . '/' . substr(md5(microtime()), rand(0, 26), 5) . '_' . $learning_module_evidence->evidence;
					}
					$zip->addFile($this->get('settings')["LMSEvidencePath"] . $learning_module_evidence->hash . '.' . $learning_module_evidence->extension, $zip_file_name);
					$files_exist = true;
				}
			}

			$to_be_unlinked = [];
			foreach ($learning_resources_results as $key => $learning_resources_result) {
				$zip_file_name = $learning_resources_result->module->name . '/' . \APP\Tools::safeName($learning_resources_result->module->name, '_') . '.pdf';
				if ($zip->locateName($zip_file_name)) {
					$zip_file_name = $learning_resources_result->module->name . '/' . substr(md5(microtime()), rand(0, 26), 5) . '_' . \APP\Tools::safeName($learning_resources_result->module->name, '_') . '.pdf';
				}

				switch ($learning_resources_result->module->type->slug) {
					case 'reflective_log':
						$pdf_path = $this->get('settings')["LMSPrivateTempPath"] . APP\Tools::safeName($learning_resources_result->module->name, '_') . '.pdf';

						// If file exists, then rename file with resourc id at the end
						if (is_file($pdf_path)) {
							$pdf_path = $this->get('settings')["LMSPrivateTempPath"] . APP\Tools::safeName($learning_resources_result->module->name, '_') . '__' . $learning_resources_result->module->id . '.pdf';
						}
						if (
							\Models\LearningModule::saveDataAsPDF($learning_resources_result, $pdf_path) &&
							is_file($pdf_path)
						) {
							$zip->addFile($pdf_path, $zip_file_name);
							$to_be_unlinked[] = $pdf_path;
							$files_exist = true;
						}
					break;
				}
			}

			$zip->close();

			// Delete files that were generated only for this action
			foreach($to_be_unlinked as $file) {
				unlink($file);
			}

			if (!$files_exist) {
				return \APP\Tools::returnCode($request, $response, 404);
			}

			// Link token/file name to user id in session
			$_SESSION[$token] = $user->id;

			$response->getBody()->write(json_encode($token));
			return $response->withHeader('Content-Type', 'application/json');
		} else {
			return \APP\Tools::returnCode($request, $response, 403);
		}

	})->add(\APP\Auth::getSessionCheck());


	$group->get('/download-uploaded-files/{token}[/{file_name_append}]', function (Request $request, Response $response, $args) {

		if (!isset($_SESSION[$args['token']])) {
			return \APP\Tools::returnCode($request, $response, 404);
		}

		$user = \Models\User::find($_SESSION[$args['token']]);
		if (
			!$user ||
			!(\APP\Auth::isAdmin() || \APP\Auth::accessAllLearners() || \APP\Auth::isManagerOf($user->id))
		) {
			return \APP\Tools::returnCode($request, $response, 403);
		}

		$file = $this->get('settings')["LMSPrivateTempPath"] . $args['token'] . ".zip";

		if (!is_file($file)) {
			return \APP\Tools::returnCode($request, $response, 404);
		}

		$fh = fopen($file, 'rb');
		$stream = new Stream($fh);

		@unlink($file);

		$file_append = 'pack_and_go';
		if (isset($args['file_name_append'])) {
			$file_append = \APP\Tools::safeName(urldecode($args['file_name_append']), '_');
		}

		$safe_name = \APP\Tools::safeName($user->fname . '_' . $user->lname . '__' . $file_append);

		return $response
			->withHeader('Content-Type', 'application/zip')
			->withHeader('Content-Disposition', 'attachment; filename="' . $safe_name . '.zip"')
			->withBody($stream);

	})->add(\APP\Auth::getSessionCheck());


	$group->get('/pull-salesforce-data/{user_id:[0-9]+}', function (Request $request, Response $response, $args) {
		$user = \Models\User::find($args['user_id']);
		if ($user) {
			$update_cnt = \APP\Api::salesforceUpdateLocalUser($user, false, 'admin_interface');
			if ($update_cnt == 0) {
				return \APP\Tools::returnCode($request, $response, 404, "User not found in SalesForce!");
			}
		}

	})->add(\APP\Auth::getStructureAccessCheck(['system-setup-organisation-users'], 'update'));

	// Administrator only function, to impersonate user.
	$group->get('/impersonate/{user_id:[0-9]+}', function (Request $request, Response $response, $args) {

		if (
			!\APP\Tools::getConfig('enableImpersonate')  ||
			!\APP\Auth::getUser()
		) {
			return \APP\Tools::returnCode($request, $response, 403);
		}

		$user = \Models\User
			::where('id', $args['user_id'])
			->where('status', true)
			->with('role')
			->first()
		;


		if (!$user) {
			return \APP\Tools::returnCode($request, $response, 404);
		} else {
			if (!\APP\Auth::allowImpersonate()) {
				/*Check email for validating learner mail id*/
				if (strtolower($user->email) != strtolower(\APP\Auth::getUserEmail())) {
					return \APP\Tools::returnCode($request, $response, 403);
				}
			}

			if (
				\APP\Auth::allowImpersonate() &&
				(
					!\APP\Auth::isAdmin() ||
					(
						\APP\Auth::isAdmin() &&
						$user->role->is_admin
					)
				) &&
				strtolower($user->email) != strtolower(\APP\Auth::getUserEmail()) &&
				(
					$user->role->is_admin ||
					$user->role->is_manager ||
					$user->role->is_cd ||
					$user->role->is_fa ||
					$user->role->is_qa
				)
			) {
				return \APP\Tools::returnCode($request, $response, 403);
			}
			//destroy existing session, create new one with user
			\APP\Auth::logout();
			\APP\Auth::login($user->username, $user->password, true, false, false, 'impersonate', $user->id);
		}

		return
			$response;
	})->add(\APP\Auth::getSessionCheck());

	# update color scheme preference for user.
	$group->put('/set-color-scheme', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		if (
			isset($data['color_scheme']) &&
			in_array($data['color_scheme'], ['light', 'dark'])
		) {
			\Models\TableExtension::updateField('users', \APP\Auth::getUserId(), 'color_scheme', $data['color_scheme']);
		}

		return $response;
	})->add(\APP\Auth::getSessionCheck());

    $group->put('/set-default-screen', function (Request $request, Response $response, $args) {
        $data = $request->getParsedBody();
        if (isset($data['default_screen']) && in_array($data['default_screen'], ['eportfolio', 'list', 'calendar'])) {
            \Models\TableExtension::updateField('users', \APP\Auth::getUserId(), 'default_screen', $data['default_screen']);
        }
        return $response;
    })->add(\APP\Auth::getSessionCheck());

    $group->put('/set-background-image', function (Request $request, Response $response, $args) {
        $data = $request->getParsedBody();
        if (isset($data['background_image'])) {
            // background_image is a picture, upload it
            //TODO;
            \Models\TableExtension::updateField('users', \APP\Auth::getUserId(), 'background_image', $data['background_image']);
        }
        return $response;
    })->add(\APP\Auth::getSessionCheck());

    $group->put('/set-date-format', function (Request $request, Response $response, $args) {
        $data = $request->getParsedBody();
        $allowedDates = [
            "d/m/Y",
            "Y/m/d",
            "m/d/Y",
			"d.m.Y",
			"d-m-Y",
			"Y-m-d",
        ];
        if (isset($data['default_date_format']) && in_array($data['default_date_format'], $allowedDates)) {
            \Models\TableExtension::updateField('users', \APP\Auth::getUserId(), 'default_date_format', $data['default_date_format']);
        }
        return $response;
    })->add(\APP\Auth::getSessionCheck());

    $group->put('/set-accessible-ui', function (Request $request, Response $response, $args) {
        $data = $request->getParsedBody();
        $user = \Models\User::find(Auth::getUserId());
        if (isset($data['accessible_ui']) && $user) {
            $user->accessible_ui = $data['accessible_ui'];
            $user->save();
        }
        return $response;
    })->add(\APP\Auth::getSessionCheck());


	#list Assigned Events
	$group->get('/assigned-events/{user_id:[0-9]+}', function (Request $request, Response $response, $args) {
		$schedule = \Models\Schedule::whereHas("Users",function ($query) use ($args){
									$query->where("users.id",$args['user_id']);
									$query->where('completion_status', '%%event_completion_state_completed%%');
								});
							if (\APP\Auth::isLearner()) {
								$query = $query
									->whereHas('Schedule', function ($query) {
										$query
											->where('visible_learner', true);
									});
							}
							$schedule=$schedule->orderBy('name')->get();
					$response->getBody()->write(json_encode($schedule));
		return $response->withHeader('Content-Type', 'application/json');

	});


	#list Unassigned Events
	$group->get('/unassigned-events/{user_id:[0-9]+}', function (Request $request, Response $response, $args) {
		$schedules = \Models\Schedule::whereHas("Users",function ($query) use ($args){
			$query->where("users.id",'!=',$args['user_id']);
		});

		if (\APP\Auth::isLearner()) {
			$schedules = $schedules
				->whereHas('Schedule', function ($query) {
					$query
						->where('visible_learner', true)
					;
				})
			;
		}

		$schedules = $schedules->orderBy('name')->get();
		$response->getBody()->write(json_encode($schedules));
		return $response->withHeader('Content-Type', 'application/json');

	});

	# Assigned and Unassigned Programmes
	$group->get('/assigned-programmes/{user_id:[0-9]+}', function (Request $request,Response $response, $args) {
		$programmes = \Models\ApprenticeshipStandard::whereHas("StandardUser",function ($query) use ($args){
									$query->where("user_id", $args['user_id']);
								})->where('apprenticeship_standards.status', 1);
							$programmes=$programmes->orderBy('name')->get();


		$response->getBody()->write(json_encode($programmes));
		return $response->withHeader('Content-Type', 'application/json');

	});

	$group->get('/unassigned-programmes/{user_id:[0-9]+}', function (Request $request, Response $response, $args) {
		$programmes = \Models\ApprenticeshipStandard::whereHas("StandardUser",function ($query) use ($args){
									$query->where("user_id",'!=',$args['user_id']);
									;
								})->where('apprenticeship_standards.status', 1);
							$programmes=$programmes->orderBy('name')->get();
		$response->getBody()->write(json_encode($programmes));
		return $response->withHeader('Content-Type', 'application/json');

	});

	$group->post('/import-logs', function (Request $req, Response $res) {
		$params = $req->getParsedBody();
		$query = ImportLog::query();
		$data = \APP\SmartTable::searchPaginate($params, $query);
		$res->getBody()->write(json_encode($data));
		return $res->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getSessionCheck());

	$group->post('/import-logs/{id:[0-9]+}', function (Request $req, Response $res,$args) {
		$params = $req->getParsedBody();
		$query = ImportLogError::where('import_log_id',$args['id']);
		$data = \APP\SmartTable::searchPaginate($params, $query);
		$res->getBody()->write(json_encode($data));
		return $res->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());


	$group->post('/integration-api-request-logs', function (Request $req, Response $res) {
		$params = $req->getParsedBody();
		$query = \Models\IntegrationApiRequestLog::query();
		$data = \APP\SmartTable::searchPaginate($params, $query);
		$res->getBody()->write(json_encode($data));
		return $res->withHeader('Content-Type', 'application/json');
	});

	$group->post('/integration-api-request-logs/{id:[0-9]+}', function (Request $req, Response $res, $args) {
		$params = $req->getParsedBody();
		$query = \Models\IntegrationApiRequestLog::query()->where('id', '=', $args['id']);
		$data = \APP\SmartTable::searchPaginate($params, $query);
		$res->getBody()->write(json_encode($data));
		return $res->withHeader('Content-Type', 'application/json');
	});

	$group->get('/import-cron',function(Request $request,Response $response){
		return ImportUserController::importCron($this->get('settings'));
	});
});
