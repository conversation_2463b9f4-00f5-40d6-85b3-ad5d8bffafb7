<?php

use APP\Controllers\CourseBasketController;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;

$app->group("/course-basket", function ($group) {
	$group->post('/add-to-basket', CourseBasketController::class . ':addToBasket')->add(\APP\Auth::getSessionCheck());
	$group->get('/basket-count', CourseBasketController::class . ':getBasketCount')->add(\APP\Auth::getSessionCheck());
	$group->get('/list', CourseBasketController::class . ':listBaskets')->add(\APP\Auth::getSessionCheck());
	$group->post('/remove', CourseBasketController::class . ':removeItemFromBasket')->add(\APP\Auth::getSessionCheck());
	$group->post('/clear', CourseBasketController::class . ':clearBasket')->add(\APP\Auth::getSessionCheck());
	$group->post('/stripe/payment', CourseBasketController::class . ':stripePayment')->add(\APP\Auth::getSessionCheck());
	$group->get('/stripe/payment/callback', CourseBasketController::class . ':stripeCallback')->add(\APP\Auth::getSessionCheck());
	$group->get('/stripe/payment/success', CourseBasketController::class . ':stripePaymentSuccess')->add(\APP\Auth::getSessionCheck());
	$group->get('/stripe/payment/failure', CourseBasketController::class . ':stripePaymentFailure')->add(\APP\Auth::getSessionCheck());

	$group->post('/global/payment', CourseBasketController::class . ':globalPayment')->add(\APP\Auth::getSessionCheck());
	$group->post('/global/payment/response', CourseBasketController::class . ':globalPaymentResponse')->add(\APP\Auth::getSessionCheck());

	$group->post('/pay360/payment', CourseBasketController::class . ':pay360Payment')->add(\APP\Auth::getSessionCheck());
	$group->get('/pay360/callback/{system_generated_transaction_id}', CourseBasketController::class . ':pay360callback')->add(\APP\Auth::getSessionCheck());
	$group->get('/pay360/response/{system_generated_transaction_id}', CourseBasketController::class . ':pay360response')->add(\APP\Auth::getSessionCheck());
});