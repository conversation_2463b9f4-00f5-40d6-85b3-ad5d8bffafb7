<?php

use APP\Auth;
use APP\Tools;
use Models\Company;
use Models\User;
use Models\LicensePlan;
use Models\CourseBasket;
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;

$app->get("/menu", function (Request $request, Response $response) {

	// Get user ID early and validate authentication
	$userId = \APP\Auth::getUserId();
	if (!$userId) {
		$response->getBody()->write(json_encode(['error' => 'User not authenticated']));
		return $response->withStatus(401)->withHeader('Content-Type', 'application/json');
	}

	// Bulk load all configurations in one query - this replaces 200+ individual queries
	$allConfigs = \APP\ConfigCache::bulkLoadAll(); // Internal use only - contains sensitive configs

	$user = \Models\User
		::select("id", "fname", "lname", "image", "username", "usercode", "email", "role_id", "shadow_role_id", "staff_type_id", "designation_id", "total_resources", "not_started_resources", "in_progress_resources", "completed_resources", "time_spent", "completed", "report_to", "learning_status", "last_completion_date", "next_completion_date", "previous_last_login_dt", "company_id", "location_id", "self_attested", "certified", "accessible_ui","country_id","city_id","AddLine1","AddLine2","Postcode","has_visited_learner_interface","phone")
		->selectRaw('DATEDIFF(next_completion_date, NOW()) as number_of_days_till_assessment')
		->with('role.Permissions')
		->with('designation')
		->with('stafftype')
		->with('StaffType')
		->with('company')
		->with('Country')
		->with('City')
		->with('location')
		->with(['reportsTo' => function($query) {
			$query->select('id','fname', 'lname');
		}])
		->with('ManagersCompact')
	;

	$roles = \APP\Cache\RoleAccessCache::getAccessIds(\APP\Auth::roleId(true));

	// Check if user's shadow role is in his access list
	if (
		\APP\Auth::isAdmin(true) ||
		in_array(\APP\Auth::roleId(), $roles)
	) {
		$user = $user
			->with('shadowRole.Permissions')
		;
	}

	if ($this->get('settings')['licensing']['isSMCR']) {
		$user = $user
			->with(['StaffFunctionResponsibility' => function($query) use ($userId) {
				$query
					->with('FunctionResponsibility')
					->withCount(['SimilarAssignments' => function ($query) use ($userId) {
						$query->where('user_id', '!=', $userId);
					}])
				;
			}])
			->addSelect('description')
			->with(['SmcrReports' => function($query) {
				$query
					->where('status', true)
					->with('Type')
				;
			}])
		;
	}

	$user = $user->find($userId);

	// Check if user was found in database
	if (!$user) {
		$response->getBody()->write(json_encode(['error' => 'User not found or not authenticated']));
		return $response->withStatus(401)->withHeader('Content-Type', 'application/json');
	}

	// Add default color_scheme
	$company = \Models\Company::find($user->company_id);
	if ($company) {
		$company->learner_theme = \Models\TableExtension::getValue('companies', $company->id, 'learner_theme');
	}
	$color_scheme = \Models\TableExtension::getValue('users', $user->id, 'color_scheme');
	$default_color_scheme  = \Models\Configuration::where('key', 'isBlackColourScheme')->first();
	if (!$color_scheme) {
		if (
			$company &&
			$company->learner_theme
		) {
			\Models\TableExtension::updateField('users', $userId, 'color_scheme', $company->learner_theme);
			$color_scheme = $company->learner_theme;
		} else {
			if ($default_color_scheme && $default_color_scheme->value === "1") {
				\Models\TableExtension::updateField('users', $userId, 'color_scheme', "dark");
				$color_scheme = 'dark';
			} else {
				\Models\TableExtension::updateField('users', $userId, 'color_scheme', "light");
				$color_scheme = 'light';
			}
		}
	}

	// Get structure tree, depending what permissions user has.
	$structure = \Models\Structure::allItems($this->get('settings')['licensing']['hiddenMenuItems']);

	// if SMCR, replace name for one menu item, need to sink about this more carefully in future!
	// Does not seems right.
	if ($this->get('settings')['licensing']['isSMCR']) {
		foreach ($structure as $key => $value) {
			if ($value->id == 1) {
				$value->name = 'Manage SMCR';
			}
		}
	}

	if(\APP\Auth::isLearningInterface()){
	    $user->has_visited_learner_interface = 1;
	    $user->save();
	}

	// Append frontend-only fields AFTER save() to avoid database column errors
	$user->color_scheme = $color_scheme;
	$user->default_screen = \Models\TableExtension::getValue('users', $user->id, 'default_screen');
	$user->default_date_format = \Models\TableExtension::getValue('users', $user->id, 'default_date_format');
	$user->background_image = \Models\TableExtension::getValue('users', $user->id, 'background_image');

	$custom_dashboard = \APP\Auth::getDashboard();
	$plan = \DB\LicenseFeatures::getPlanDetails();

	$hasFullDiscount = User::hasFullDiscount($user->id);

	$data = [
		"user" => $user,
		"plan" => $plan ,
		"structure" => \APP\Tools::buildTree($structure->toArray()),
		"config" => [],
		"assignments" => \Models\Assignment::getUserEntries(),
		"licensing" => [
			"isOpenElmsTMS" => $this->get('settings')['licensing']['isOpenElmsTMS'],
			"isOpenElms" => $this->get('settings')['licensing']['isOpenElms'],
			"isApprentix" => $this->get('settings')['licensing']['isApprentix'],
			"isJackdawCloud" => $this->get('settings')['licensing']['isJackdawCloud'],
			"isSMCR" => $this->get('settings')['licensing']['isSMCR'],
			"version" => $this->get('settings')['licensing']['version'],
		],
		"permissions" => [
			"importUsers" => \APP\Auth::checkStructureAccess('misc-permissions-import-users', 'insert'),
			"signOffLearnerStatus" => \APP\Auth::signOffLearnerStatus(),
			"showAdminGUI" => \APP\Auth::showAdminGUI(),
			"users" => \Models\RoleStructure
				::select('view', 'select', 'insert', 'update', 'disable')
				->whereIn(
					'structure_id',
					\Models\Structure
						::select('id')
						->where('key', 'system-setup-organisation-users')
				)
				->where('role_id', \APP\Auth::roleId())
				->first()

		],
		"customDashboard" =>  $custom_dashboard ? $custom_dashboard->id : false,
		"workUrl" => \Models\Structure::firstLink($this->get('settings'), true, false, 1, true),
		"workUrlKey" => \Models\Structure::firstLink($this->get('settings'), true, true, 1, true),
		"dashboardUrl" => !empty($allConfigs['hideHomeButton']) ? \Models\Structure::firstLink($this->get('settings'), true, false, 1, true) : \Models\Structure::firstLink($this->get('settings'), true),
		"dashboardUrlKey" => !empty($allConfigs['hideHomeButton']) ? \Models\Structure::firstLink($this->get('settings'), true, true, 1, true) : \Models\Structure::firstLink($this->get('settings'), true, true),
	];

	$data["user"]["accessible_ui"] = $data["user"]["accessible_ui"] == '1' ? true : false;

	if ($this->get('settings')['licensing']['isApprentix']) {
		$data["config"]["UUID"] = $this->get('settings')['ilr_UUID'];
	}

	if ($this->get('settings')['licensing']['isJackdawCloud']) {
		// This will return full calculated Jackdaw Cloud settings, or false
		$data["config"]["jackdaw"] = \APP\Jackdaw::getSettings($this->get('settings'));
	}

	//Translate structure element names, recursively descending into structure tree
	$translator = \APP\Templates::getTranslator();
	$translator->translateNestedStructures($data["structure"], "name", "children");
	$translator->translateNestedStructures($data["structure"], "description", "children");

	// Most config values are now loaded via bulkLoad above

	if ($this->get('settings')['licensing']['isOpenElmsTMS']) {
		$data["config"]["learnerRefNum"] = \APP\Tools::getLearnerRefNum();
	}
	// Already loaded via bulkLoad

	// Already loaded via bulkLoad
	$data["config"]["logo"] = $this->get('settings')["DefaultLogo"].'?'.($allConfigs['randomString'] ?? '');
	// If logged in user is in company that has custom logo, serve that!
	if (\APP\Auth::getUserCompanyId()) {
		$company = \Models\Company
			::where('status', true)
			->where('id', \APP\Auth::getUserCompanyId())
			->first()
		;
		if (
			$company &&
			!empty($company->logo) &&
			is_file($this->get('settings')["CompanyLogosPath"] . $company->logo)
		) {
			$data["config"]["logo"] = $this->get('settings')["LMSCompanyLogosUri"] . $company->logo;
		}
	}

	// Merge ONLY frontend-safe configurations into data array (security critical!)
	$frontendSafeConfigs = \APP\ConfigCache::getFrontendSafeConfigs();
	$data["config"] = array_merge($data["config"] ?? [], $frontendSafeConfigs);

	// Special cases that use different methods
	$data["config"]["isSkillScan"] = \APP\Tools::isSkillScan();
	$data["config"]["isPendingAssessment"] = \APP\Tools::isPendingAssessment();
	// Special cases that need custom logic
	$data["config"]["andersPinkEnabled"] = !empty($allConfigs['AndersPinkApiKey']);
	$data["config"]["isH5PEnabled"] = !empty($allConfigs['H5P_CLIENT_ID']) && !empty($allConfigs['H5P_CLIENT_SECRET']);

	// Non-config values from settings
	$data["config"]["LMSUrl"] = $this->get('settings')['LMSUrl'];
	$data["config"]["release"] = file_get_contents($this->get('settings')['LMSAppPath'] . 'version.txt');

	// Special permission-based config
	$data["config"]["canDeleteEvents"] = \APP\Auth::isAdmin() || \APP\Auth::isCD() || \APP\Auth::checkStructureAccess(['lessons-and-learning-resources'], 'disable');

	// Special case: showResetCustomDatesButton depends on user role
	if (!empty($allConfigs['showResetCustomDatesButton'])){
		switch($allConfigs['showResetCustomDatesButton']){
			case "admin":
				$data["config"]["showResetCustomDatesButton"] = \APP\Auth::isAdmin(true) ? 1 : 0;
				break;
			case "manager":
				$data["config"]["showResetCustomDatesButton"] = \APP\Auth::isAdminInterface() ? 1 : 0;
				break;
			default:
				$data["config"]["showResetCustomDatesButton"] = 0;
				break;
		}
	}

	// https://emil-reisser-weston.atlassian.net/browse/SCOR-3270

//

	$data["config"]["showReportMenuLearnerInterface"] = \APP\Auth::checkStructureAccess(['custom-report-data'], 'select');

	// Remove all remaining redundant getConfig calls - they're already in $allConfigs from bulkLoad()

	// These config values are already loaded in $allConfigs from bulkLoad()

	$data["config"]["openelmsAiLinkEnabled"] = strlen((string)($allConfigs['openelmsAiLinkToken'] ?? '')) > 0;
	// Special case: RolePreferencesPassword only shown to admin with permissions
	if (
		\APP\Auth::isAdminInterface() &&
		\APP\Auth::checkStructureAccess('system-setup-organisation-roles', 'view')
	) {
		$data['config']['RolePreferencesPassword'] = $allConfigs['RolePreferencesPassword'] ?? null;
	}

	$defaultDateFormat = $allConfigs['defaultDateFormat'] ?? 'd/m/Y';
	$userDateFormat = \Models\TableExtension::getValue('users', $userId, 'default_date_format');
	if ($userDateFormat) {
		$defaultDateFormat = $userDateFormat;
	}
	// Convert PHP date pattern to usable in Javascript
	$data['config']['defaultDateFormat'] = preg_replace(
		['/Y/','/m/','/d/'],
		['yyyy','MM','dd'],
		$defaultDateFormat
	);

	$data['config']['defaultMomentFormat'] = \APP\Tools::phpToMomentFormat($defaultDateFormat);
	$data['config']['defaultDateFormatPattern'] = \APP\Tools::convertDateFormatToHtmlPattern($defaultDateFormat);

	// Special case: gradeWork depends on two API keys
	$data['config']['gradeWork'] = !empty($allConfigs['AIGradingOpenAIAPIKEY']) && !empty($allConfigs['AIGradingOpenAIAsisstantID']);

	// Special case: AIPoweredHelpAssistant uses role-based method
	$data["config"]['AIPoweredHelpAssistant'] = \APP\Auth::roleDocsBotId();

	// Add multi-cart item count for course basket feature
	$data['multiCartItemCount'] = CourseBasket::where('user_id', $userId)->count();
	$data['hasFullDiscount'] = User::hasFullDiscount($user->id);

  $response->getBody()->write(json_encode($data));
  return $response->withHeader('Content-Type', 'application/json');
})->add(\APP\Auth::getLoginCheck($settings["LMSUri"] . "login"));