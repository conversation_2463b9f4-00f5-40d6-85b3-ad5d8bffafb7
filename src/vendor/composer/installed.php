<?php return array(
    'root' => array(
        'name' => '__root__',
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'reference' => 'dbdf23bf35706a0b7c68e9ef3abe996729f3e4fe',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => false,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => 'dbdf23bf35706a0b7c68e9ef3abe996729f3e4fe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ankitpokhrel/tus-php' => array(
            'pretty_version' => 'v2.4.0',
            'version' => '2.4.0.0',
            'reference' => 'a466a9b835f3ea29ec4df7e37a2dc9e00d194304',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ankitpokhrel/tus-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'aws/aws-crt-php' => array(
            'pretty_version' => 'v1.2.7',
            'version' => '1.2.7.0',
            'reference' => 'd71d9906c7bb63a28295447ba12e74723bd3730e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../aws/aws-crt-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'aws/aws-sdk-php' => array(
            'pretty_version' => '3.342.10',
            'version' => '3.342.10.0',
            'reference' => '93dc5f0852d9e67f237060814b8abc748c0e4be5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../aws/aws-sdk-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'bacon/bacon-qr-code' => array(
            'pretty_version' => '2.0.8',
            'version' => '2.0.8.0',
            'reference' => '8674e51bb65af933a5ffaf1c308a660387c35c22',
            'type' => 'library',
            'install_path' => __DIR__ . '/../bacon/bacon-qr-code',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'baileyherbert/envato' => array(
            'pretty_version' => '3.1.0',
            'version' => '3.1.0.0',
            'reference' => 'f3c7cca13c30963b9278cb56f1842c05bcdbf5bc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../baileyherbert/envato',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'bjeavons/zxcvbn-php' => array(
            'pretty_version' => '1.4.2',
            'version' => '1.4.2.0',
            'reference' => '426f664501a0747beb8f3ee17ac30c7dd6327ffa',
            'type' => 'library',
            'install_path' => __DIR__ . '/../bjeavons/zxcvbn-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'box/spout' => array(
            'pretty_version' => 'v3.3.0',
            'version' => '3.3.0.0',
            'reference' => '9bdb027d312b732515b884a341c0ad70372c6295',
            'type' => 'library',
            'install_path' => __DIR__ . '/../box/spout',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'brick/math' => array(
            'pretty_version' => '0.12.3',
            'version' => '0.12.3.0',
            'reference' => '866551da34e9a618e64a819ee1e01c20d8a588ba',
            'type' => 'library',
            'install_path' => __DIR__ . '/../brick/math',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'carbonphp/carbon-doctrine-types' => array(
            'pretty_version' => '2.1.0',
            'version' => '2.1.0.0',
            'reference' => '99f76ffa36cce3b70a4a6abce41dba15ca2e84cb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../carbonphp/carbon-doctrine-types',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'codeguy/upload' => array(
            'pretty_version' => '1.3.2',
            'version' => '1.3.2.0',
            'reference' => '6a9e5e1fb58d65346d0e557db2d46fb25efd3e37',
            'type' => 'library',
            'install_path' => __DIR__ . '/../codeguy/upload',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'composer/pcre' => array(
            'pretty_version' => '3.3.2',
            'version' => '3.3.2.0',
            'reference' => 'b2bed4734f0cc156ee1fe9c0da2550420d99a21e',
            'type' => 'library',
            'install_path' => __DIR__ . '/./pcre',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'dasprid/enum' => array(
            'pretty_version' => '1.0.6',
            'version' => '1.0.6.0',
            'reference' => '8dfd07c6d2cf31c8da90c53b83c026c7696dda90',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dasprid/enum',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'dflydev/fig-cookies' => array(
            'pretty_version' => 'v3.1.0',
            'version' => '3.1.0.0',
            'reference' => 'ebe6c15c9895fc490efe620ad734c8ef4a85bdb0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dflydev/fig-cookies',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/cache' => array(
            'pretty_version' => '2.2.0',
            'version' => '*******',
            'reference' => '1ca8f21980e770095a31456042471a57bc4c68fb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/dbal' => array(
            'pretty_version' => '3.9.4',
            'version' => '3.9.4.0',
            'reference' => 'ec16c82f20be1a7224e65ac67144a29199f87959',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/dbal',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/deprecations' => array(
            'pretty_version' => '1.1.4',
            'version' => '1.1.4.0',
            'reference' => '31610dbb31faa98e6b5447b62340826f54fbc4e9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/deprecations',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/event-manager' => array(
            'pretty_version' => '2.0.1',
            'version' => '2.0.1.0',
            'reference' => 'b680156fa328f1dfd874fd48c7026c41570b9c6e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/event-manager',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/inflector' => array(
            'pretty_version' => '2.0.10',
            'version' => '2.0.10.0',
            'reference' => '5817d0659c5b50c9b950feb9af7b9668e2c436bc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/inflector',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'dompdf/dompdf' => array(
            'pretty_version' => 'v2.0.8',
            'version' => '2.0.8.0',
            'reference' => 'c20247574601700e1f7c8dab39310fca1964dc52',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dompdf/dompdf',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'endroid/qr-code' => array(
            'pretty_version' => '3.9.7',
            'version' => '3.9.7.0',
            'reference' => '94563d7b3105288e6ac53a67ae720e3669fac1f6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../endroid/qr-code',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ezyang/htmlpurifier' => array(
            'pretty_version' => 'v4.18.0',
            'version' => '4.18.0.0',
            'reference' => 'cb56001e54359df7ae76dc522d08845dc741621b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ezyang/htmlpurifier',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'facebook/graph-sdk' => array(
            'pretty_version' => '5.1.4',
            'version' => '5.1.4.0',
            'reference' => '38fd7187a6704d3ab14ded2f3a534ac4ee6f3481',
            'type' => 'library',
            'install_path' => __DIR__ . '/../facebook/graph-sdk',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'fig/http-message-util' => array(
            'pretty_version' => '1.1.5',
            'version' => '1.1.5.0',
            'reference' => '9d94dc0154230ac39e5bf89398b324a86f63f765',
            'type' => 'library',
            'install_path' => __DIR__ . '/../fig/http-message-util',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'firebase/php-jwt' => array(
            'pretty_version' => 'v6.11.0',
            'version' => '6.11.0.0',
            'reference' => '8f718f4dfc9c5d5f0c994cdfd103921b43592712',
            'type' => 'library',
            'install_path' => __DIR__ . '/../firebase/php-jwt',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'fproject/php-jwt' => array(
            'pretty_version' => '4.0.5',
            'version' => '4.0.5.0',
            'reference' => '91047b202bbe7d966e8fce67ab16ebca8bdcb6b7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../fproject/php-jwt',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'globalpayments/php-sdk' => array(
            'pretty_version' => '3.1.0',
            'version' => '3.1.0.0',
            'reference' => 'bf778a70431cfe7e8cab6eaa6d8a8dc1b59c04c3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../globalpayments/php-sdk',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'google/apiclient' => array(
            'pretty_version' => 'v2.14.0',
            'version' => '2.14.0.0',
            'reference' => '789c8b07cad97f420ac0467c782036f955a2ad89',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/apiclient',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'google/apiclient-services' => array(
            'pretty_version' => 'v0.396.0',
            'version' => '0.396.0.0',
            'reference' => 'ceb2e432e4326c6775d24f62d554395a1a9ad3dd',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/apiclient-services',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'google/auth' => array(
            'pretty_version' => 'v1.46.0',
            'version' => '1.46.0.0',
            'reference' => '7fafae99a41984cbfb92508174263cf7bf3049b9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/auth',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle' => array(
            'pretty_version' => '7.9.3',
            'version' => '7.9.3.0',
            'reference' => '7b2f29fe81dc4da0ca0ea7d42107a0845946ea77',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/promises' => array(
            'pretty_version' => '2.0.4',
            'version' => '2.0.4.0',
            'reference' => 'f9c436286ab2892c7db7be8c8da4ef61ccf7b455',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/promises',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '2.7.0',
            'version' => '2.7.0.0',
            'reference' => 'a70f5c95fb43bc83f07c9c948baa0dc1829bf201',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/bus' => array(
            'pretty_version' => 'v10.48.28',
            'version' => '**********',
            'reference' => 'c66d57011eec385055e1426d026c270aeecb05aa',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/bus',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/cache' => array(
            'pretty_version' => 'v10.48.28',
            'version' => '**********',
            'reference' => '20f36c3209107ee5c8c646f88a0562a2c1b05a6c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/collections' => array(
            'pretty_version' => 'v10.48.28',
            'version' => '**********',
            'reference' => '48de3d6bc6aa779112ddcb608a3a96fc975d89d8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/collections',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/conditionable' => array(
            'pretty_version' => 'v10.48.28',
            'version' => '**********',
            'reference' => '3ee34ac306fafc2a6f19cd7cd68c9af389e432a5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/conditionable',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/config' => array(
            'pretty_version' => 'v10.48.28',
            'version' => '**********',
            'reference' => 'd5e83ceff5c4d5607b1b81763eb4c436911c35da',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/config',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/console' => array(
            'pretty_version' => 'v10.48.28',
            'version' => '**********',
            'reference' => 'e30d1f6ec68bd8138ba5f21998a2ee1c65ca82ca',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/console',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/container' => array(
            'pretty_version' => 'v10.48.28',
            'version' => '**********',
            'reference' => 'ed6253f7dd3a67d468b2cc7a69a657e1f14c7ba3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/contracts' => array(
            'pretty_version' => 'v10.48.28',
            'version' => '**********',
            'reference' => 'f90663a69f926105a70b78060a31f3c64e2d1c74',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/database' => array(
            'pretty_version' => 'v10.48.28',
            'version' => '**********',
            'reference' => '2246a636ba1f1e0cc6a5711f0e3929c6c303d937',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/database',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/events' => array(
            'pretty_version' => 'v10.48.28',
            'version' => '**********',
            'reference' => '3edcdad2f2fe6da6802afb0a256b0f7ee00d72e9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/events',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/filesystem' => array(
            'pretty_version' => 'v10.48.28',
            'version' => '**********',
            'reference' => '584ff4da2218e63e7210bba1c541ce526f24f37e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/filesystem',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/macroable' => array(
            'pretty_version' => 'v10.48.28',
            'version' => '**********',
            'reference' => 'dff667a46ac37b634dcf68909d9d41e94dc97c27',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/macroable',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/pagination' => array(
            'pretty_version' => 'v10.48.28',
            'version' => '**********',
            'reference' => '616874b9607ff35925347e1710a8b5151858cdf2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/pagination',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/pipeline' => array(
            'pretty_version' => 'v10.48.28',
            'version' => '**********',
            'reference' => '3030a131e5e9cb18c9a826428fcffc076df9dcd7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/pipeline',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/queue' => array(
            'pretty_version' => 'v10.48.28',
            'version' => '**********',
            'reference' => '7047f449072313ee9a04fa140d9044685a4d5741',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/queue',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/redis' => array(
            'pretty_version' => 'v10.48.28',
            'version' => '**********',
            'reference' => '446d36aeb21fd2b6719293a8d930ae9ac8135be0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/redis',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/support' => array(
            'pretty_version' => 'v10.48.28',
            'version' => '**********',
            'reference' => '6d09b480d34846245d9288f4dcefb17a73ce6e6a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/support',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/view' => array(
            'pretty_version' => 'v10.48.28',
            'version' => '**********',
            'reference' => '51c9feaf1364bc35ffb056463cde1d6e639b7c6e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/view',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'imsglobal/lti' => array(
            'pretty_version' => '3.0.2',
            'version' => '*******',
            'reference' => '06b12f70c2803ee08da116bcda2951eefd411ac9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../imsglobal/lti',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'imsglobal/lti-1p3-tool' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '3a192de99f3783d76caea462b0d04db28569c123',
            'type' => 'library',
            'install_path' => __DIR__ . '/../imsglobal/lti-1p3-tool',
            'aliases' => array(
                0 => '9999999-dev',
            ),
            'dev_requirement' => false,
        ),
        'itsgoingd/clockwork' => array(
            'pretty_version' => 'v5.3.4',
            'version' => '5.3.4.0',
            'reference' => 'c27ad77a08a9e58bf0049de46969fa4fe3b506e5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../itsgoingd/clockwork',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'khanamiryan/qrcode-detector-decoder' => array(
            'pretty_version' => '1.0.6',
            'version' => '1.0.6.0',
            'reference' => '45326fb83a2a375065dbb3a134b5b8a5872da569',
            'type' => 'library',
            'install_path' => __DIR__ . '/../khanamiryan/qrcode-detector-decoder',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/prompts' => array(
            'pretty_version' => 'v0.1.25',
            'version' => '0.1.25.0',
            'reference' => '7b4029a84c37cb2725fc7f011586e2997040bc95',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/prompts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/serializable-closure' => array(
            'pretty_version' => 'v1.3.7',
            'version' => '1.3.7.0',
            'reference' => '4f48ade902b94323ca3be7646db16209ec76be3d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/serializable-closure',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/flysystem' => array(
            'pretty_version' => '3.29.1',
            'version' => '3.29.1.0',
            'reference' => 'edc1bb7c86fab0776c3287dbd19b5fa278347319',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/flysystem',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/flysystem-local' => array(
            'pretty_version' => '3.29.0',
            'version' => '3.29.0.0',
            'reference' => 'e0e8d52ce4b2ed154148453d321e97c8e931bd27',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/flysystem-local',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/mime-type-detection' => array(
            'pretty_version' => '1.16.0',
            'version' => '1.16.0.0',
            'reference' => '2d6702ff215bf922936ccc1ad31007edc76451b9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/mime-type-detection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/oauth2-client' => array(
            'pretty_version' => '2.8.1',
            'version' => '2.8.1.0',
            'reference' => '9df2924ca644736c835fc60466a3a60390d334f9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/oauth2-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/oauth2-google' => array(
            'pretty_version' => '4.0.1',
            'version' => '4.0.1.0',
            'reference' => '1b01ba18ba31b29e88771e3e0979e5c91d4afe76',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/oauth2-google',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'luracast/restler' => array(
            'pretty_version' => '5.0.13',
            'version' => '5.0.13.0',
            'reference' => '069b5c55ef5153ce628d9382604ded570d6bac48',
            'type' => 'library',
            'install_path' => __DIR__ . '/../luracast/restler',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'madcoda/php-youtube-api' => array(
            'pretty_version' => 'v1.2.6',
            'version' => '*******',
            'reference' => '70debe2a140339e145a57d64cd4e9e12f393332f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../madcoda/php-youtube-api',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'maennchen/zipstream-php' => array(
            'pretty_version' => '3.1.1',
            'version' => '*******',
            'reference' => '6187e9cc4493da94b9b63eb2315821552015fca9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../maennchen/zipstream-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'markbaker/complex' => array(
            'pretty_version' => '3.0.2',
            'version' => '*******',
            'reference' => '95c56caa1cf5c766ad6d65b6344b807c1e8405b9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../markbaker/complex',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'markbaker/matrix' => array(
            'pretty_version' => '3.0.1',
            'version' => '*******',
            'reference' => '728434227fe21be27ff6d86621a1b13107a2562c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../markbaker/matrix',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'masterminds/html5' => array(
            'pretty_version' => '2.9.0',
            'version' => '*******',
            'reference' => 'f5ac2c0b0a2eefca70b2ce32a5809992227e75a6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../masterminds/html5',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'matthiasmullie/minify' => array(
            'pretty_version' => '1.3.73',
            'version' => '1.3.73.0',
            'reference' => 'cb7a9297b4ab070909cefade30ee95054d4ae87a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../matthiasmullie/minify',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'matthiasmullie/path-converter' => array(
            'pretty_version' => '1.1.3',
            'version' => '1.1.3.0',
            'reference' => 'e7d13b2c7e2f2268e1424aaed02085518afa02d9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../matthiasmullie/path-converter',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'monolog/monolog' => array(
            'pretty_version' => '3.8.1',
            'version' => '3.8.1.0',
            'reference' => 'aef6ee73a77a66e404dd6540934a9ef1b3c855b4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../monolog/monolog',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mtdowling/jmespath.php' => array(
            'pretty_version' => '2.8.0',
            'version' => '2.8.0.0',
            'reference' => 'a2a865e05d5f420b50cc2f85bb78d565db12a6bc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mtdowling/jmespath.php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'myclabs/php-enum' => array(
            'pretty_version' => '1.8.5',
            'version' => '1.8.5.0',
            'reference' => 'e7be26966b7398204a234f8673fdad5ac6277802',
            'type' => 'library',
            'install_path' => __DIR__ . '/../myclabs/php-enum',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nesbot/carbon' => array(
            'pretty_version' => '2.73.0',
            'version' => '2.73.0.0',
            'reference' => '9228ce90e1035ff2f0db84b40ec2e023ed802075',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nesbot/carbon',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nikic/fast-route' => array(
            'pretty_version' => 'v1.3.0',
            'version' => '1.3.0.0',
            'reference' => '181d480e08d9476e61381e04a71b34dc0432e812',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nikic/fast-route',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nunomaduro/termwind' => array(
            'pretty_version' => 'v1.17.0',
            'version' => '1.17.0.0',
            'reference' => '5369ef84d8142c1d87e4ec278711d4ece3cbf301',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nunomaduro/termwind',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'openspout/openspout' => array(
            'pretty_version' => 'v4.25.0',
            'version' => '4.25.0.0',
            'reference' => '519affe730d92e1598720a6467227fc28550f0e6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../openspout/openspout',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phenx/php-font-lib' => array(
            'pretty_version' => '0.5.6',
            'version' => '0.5.6.0',
            'reference' => 'a1681e9793040740a405ac5b189275059e2a9863',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phenx/php-font-lib',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phenx/php-svg-lib' => array(
            'pretty_version' => '0.5.4',
            'version' => '0.5.4.0',
            'reference' => '46b25da81613a9cf43c83b2a8c2c1bdab27df691',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phenx/php-svg-lib',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-di/invoker' => array(
            'pretty_version' => '2.3.6',
            'version' => '2.3.6.0',
            'reference' => '59f15608528d8a8838d69b422a919fd6b16aa576',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-di/invoker',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-di/php-di' => array(
            'pretty_version' => '7.0.9',
            'version' => '7.0.9.0',
            'reference' => 'd8480267f5cf239650debba704f3ecd15b638cde',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-di/php-di',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpforce/common' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => 'aa96dfb6b0f43024c95a9d9c88396013e7513f9c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpforce/common',
            'aliases' => array(
                0 => '9999999-dev',
            ),
            'dev_requirement' => false,
        ),
        'phpforce/soap-client' => array(
            'pretty_version' => '0.1.0',
            'version' => '0.1.0.0',
            'reference' => '9a17e514794683bda9d9008a6672bbf36495b4d9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpforce/soap-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpmailer/phpmailer' => array(
            'pretty_version' => 'v6.9.3',
            'version' => '6.9.3.0',
            'reference' => '2f5c94fe7493efc213f643c23b1b1c249d40f47e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpmailer/phpmailer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpoffice/phpspreadsheet' => array(
            'pretty_version' => '1.29.10',
            'version' => '1.29.10.0',
            'reference' => 'c80041b1628c4f18030407134fe88303661d4e4e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoffice/phpspreadsheet',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpseclib/phpseclib' => array(
            'pretty_version' => '2.0.48',
            'version' => '2.0.48.0',
            'reference' => 'eaa7be704b8b93a6913b69eb7f645a59d7731b61',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpseclib/phpseclib',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'predis/predis' => array(
            'pretty_version' => 'v2.4.0',
            'version' => '2.4.0.0',
            'reference' => 'f49e13ee3a2a825631562aa0223ac922ec5d058b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../predis/predis',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => 'aa5030cfa5405eccfdcb1083ce040c2cb8d253bf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/cache-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.0|3.0',
            ),
        ),
        'psr/clock' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'e41a24703d4560fd0acb709162f73b8adfc3aa0d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/clock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/clock-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/container' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => 'c71ecc56dfe541dbd90c5360474fbc405f8d5963',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/container-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.1|2.0',
                1 => '^1.0',
            ),
        ),
        'psr/event-dispatcher' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'dbefd12671e8a14ec7f180cab83036ed26714bb0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-client' => array(
            'pretty_version' => '1.0.3',
            'version' => '1.0.3.0',
            'reference' => 'bb5906edc1c324c9a05aa0873d40117941e5fa90',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.1.0',
            'version' => '1.1.0.0',
            'reference' => '2b4765fddfe3b508ac62f829e852b1501d3f6e8a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
                1 => '^1.0',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => '2.0',
            'version' => '2.0.0.0',
            'reference' => '402d35bcb92c70c026d1a6a9883f06b2ead23d71',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
                1 => '^1.0 || ^2.0',
            ),
        ),
        'psr/http-server-handler' => array(
            'pretty_version' => '1.0.2',
            'version' => '1.0.2.0',
            'reference' => '84c4fb66179be4caaf8e97bd239203245302e7d4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-server-handler',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-server-middleware' => array(
            'pretty_version' => '1.0.2',
            'version' => '1.0.2.0',
            'reference' => 'c1481f747daaa6a0782775cd6a8c26a1bf4a3829',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-server-middleware',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log' => array(
            'pretty_version' => '3.0.2',
            'version' => '*******',
            'reference' => 'f16e1d5863e37f8d8c2a01719f5b34baa2b714d3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0|3.0',
                1 => '3.0.0',
            ),
        ),
        'psr/simple-cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => '764e0b3939f5ca87cb904f570ef9be2d78a07865',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/simple-cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/simple-cache-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0|3.0',
            ),
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ramsey/collection' => array(
            'pretty_version' => '2.1.0',
            'version' => '2.1.0.0',
            'reference' => '3c5990b8a5e0b79cd1cf11c2dc1229e58e93f109',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ramsey/collection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ramsey/uuid' => array(
            'pretty_version' => '4.7.6',
            'version' => '4.7.6.0',
            'reference' => '91039bc1faa45ba123c4328958e620d382ec7088',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ramsey/uuid',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'rebelinblue/laravel-zxcvbn' => array(
            'pretty_version' => '1.5.0',
            'version' => '1.5.0.0',
            'reference' => '9c588afedc2e19116929f0b3afec202fb522f9b7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../rebelinblue/laravel-zxcvbn',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'respect/stringifier' => array(
            'pretty_version' => '0.2.0',
            'version' => '0.2.0.0',
            'reference' => 'e55af3c8aeaeaa2abb5fa47a58a8e9688cc23b59',
            'type' => 'library',
            'install_path' => __DIR__ . '/../respect/stringifier',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'respect/validation' => array(
            'pretty_version' => '2.4.0',
            'version' => '2.4.0.0',
            'reference' => '48b38bd91e0badbc2c4381dce726b09fd68850d9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../respect/validation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'rhumsaa/uuid' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '4.7.6',
            ),
        ),
        'sabberworm/php-css-parser' => array(
            'pretty_version' => 'v8.7.0',
            'version' => '8.7.0.0',
            'reference' => 'f414ff953002a9b18e3a116f5e462c56f21237cf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sabberworm/php-css-parser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'slim/php-view' => array(
            'pretty_version' => '3.4.0',
            'version' => '3.4.0.0',
            'reference' => 'ef1821663a6a028b9e446e8c6818fd257bf70313',
            'type' => 'library',
            'install_path' => __DIR__ . '/../slim/php-view',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'slim/psr7' => array(
            'pretty_version' => '1.7.0',
            'version' => '1.7.0.0',
            'reference' => '753e9646def5ff4db1a06e5cf4ef539bfd30f467',
            'type' => 'library',
            'install_path' => __DIR__ . '/../slim/psr7',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'slim/slim' => array(
            'pretty_version' => '4.14.0',
            'version' => '4.14.0.0',
            'reference' => '5943393b88716eb9e82c4161caa956af63423913',
            'type' => 'library',
            'install_path' => __DIR__ . '/../slim/slim',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sonata-project/google-authenticator' => array(
            'pretty_version' => '2.3.1',
            'version' => '2.3.1.0',
            'reference' => '71a4189228f93a9662574dc8c65e77ef55061b59',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sonata-project/google-authenticator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'staudenmeir/eloquent-has-many-deep' => array(
            'pretty_version' => 'v1.19.4',
            'version' => '1.19.4.0',
            'reference' => 'd9651c2c64d34a8fd4c680090d3521ed136f2ead',
            'type' => 'library',
            'install_path' => __DIR__ . '/../staudenmeir/eloquent-has-many-deep',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'staudenmeir/eloquent-has-many-deep-contracts' => array(
            'pretty_version' => 'v1.1',
            'version' => '1.1.0.0',
            'reference' => 'c39317b839d6123be126b9980e4a3d38310f5939',
            'type' => 'library',
            'install_path' => __DIR__ . '/../staudenmeir/eloquent-has-many-deep-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'stevenmaguire/oauth2-microsoft' => array(
            'pretty_version' => '2.2.0',
            'version' => '*******',
            'reference' => 'f24f79d8c47224d24a1240270ca3b0a4c1521ed4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../stevenmaguire/oauth2-microsoft',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'stripe/stripe-php' => array(
            'pretty_version' => 'v14.10.0',
            'version' => '*********',
            'reference' => '7e1c4b5d2beadeaeddc42fd1f8a50fdb18b37f30',
            'type' => 'library',
            'install_path' => __DIR__ . '/../stripe/stripe-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/cache' => array(
            'pretty_version' => 'v6.4.21',
            'version' => '********',
            'reference' => 'd1abcf763a7414f2e572f676f22da7a06c8cd9ee',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/cache-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '*******',
            'reference' => '5d68a57d66910405e5c0b63d6f0af941e66fc868',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/cache-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/cache-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.1|2.0|3.0',
            ),
        ),
        'symfony/console' => array(
            'pretty_version' => 'v6.4.17',
            'version' => '6.4.17.0',
            'reference' => '799445db3f15768ecc382ac5699e6da0520a0a04',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/console',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.5.1',
            'version' => '3.5.1.0',
            'reference' => '74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher' => array(
            'pretty_version' => 'v6.4.13',
            'version' => '6.4.13.0',
            'reference' => '0ffc48080ab3e9132ea74ef4e09d8dcf26bf897e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-contracts' => array(
            'pretty_version' => 'v3.5.1',
            'version' => '3.5.1.0',
            'reference' => '7642f5e970b672283b7823222ae8ef8bbc160b9f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.0|3.0',
            ),
        ),
        'symfony/finder' => array(
            'pretty_version' => 'v6.4.17',
            'version' => '6.4.17.0',
            'reference' => '1d0e8266248c5d9ab6a87e3789e6dc482af3c9c7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/finder',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-foundation' => array(
            'pretty_version' => 'v6.4.18',
            'version' => '6.4.18.0',
            'reference' => 'd0492d6217e5ab48f51fca76f64cf8e78919d0db',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-foundation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/mime' => array(
            'pretty_version' => 'v6.4.19',
            'version' => '6.4.19.0',
            'reference' => 'ac537b6c55ccc2c749f3c979edfa9ec14aaed4f3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/mime',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/options-resolver' => array(
            'pretty_version' => 'v5.4.45',
            'version' => '5.4.45.0',
            'reference' => '74e5b6f0db3e8589e6cfd5efb317a1fc2bb52fb6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/options-resolver',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => 'a3cc8b044a6ea513310cbd48ef7333b384945638',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-grapheme' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => 'b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-grapheme',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-idn' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => 'c36586dcf89a12315939e00ec9b4474adcb1d773',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-idn',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-normalizer' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '3833d7255cc303546435cb650316bff708a1c75c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-normalizer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '85181ba99b2345b0ef10ce42ecac37612d9fd341',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php73' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '0f68c03565dcaaf25a890667542e8bd75fe7e5bb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php73',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '60328e362d4c2c802a54fcbf04f9d3fb892b4cf8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php83' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '2fb86d65e2d424369ad2905e83b236a8805ba491',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php83',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/process' => array(
            'pretty_version' => 'v6.4.19',
            'version' => '6.4.19.0',
            'reference' => '7a1c12e87b08ec9c97abdd188c9b3f5a40e37fc3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/process',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/property-access' => array(
            'pretty_version' => 'v5.4.45',
            'version' => '5.4.45.0',
            'reference' => '111e7ed617509f1a9139686055d234aad6e388e0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/property-access',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/property-info' => array(
            'pretty_version' => 'v6.4.18',
            'version' => '6.4.18.0',
            'reference' => '94d18e5cc11a37fd92856d38b61d9cdf72536a1e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/property-info',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/service-contracts' => array(
            'pretty_version' => 'v3.5.1',
            'version' => '3.5.1.0',
            'reference' => 'e53260aabf78fb3d63f8d79d69ece59f80d5eda0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/service-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/string' => array(
            'pretty_version' => 'v6.4.15',
            'version' => '6.4.15.0',
            'reference' => '73a5e66ea2e1677c98d4449177c5a9cf9d8b4c6f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/string',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation' => array(
            'pretty_version' => 'v6.4.19',
            'version' => '6.4.19.0',
            'reference' => '3b9bf9f33997c064885a7bfc126c14b9daa0e00e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-contracts' => array(
            'pretty_version' => 'v3.5.1',
            'version' => '3.5.1.0',
            'reference' => '4667ff3bd513750603a09c8dedbea942487fb07c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.3|3.0',
            ),
        ),
        'symfony/var-exporter' => array(
            'pretty_version' => 'v6.4.22',
            'version' => '6.4.22.0',
            'reference' => 'f28cf841f5654955c9f88ceaf4b9dc29571988a9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/var-exporter',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'twilio/sdk' => array(
            'pretty_version' => '8.6.0',
            'version' => '8.6.0.0',
            'reference' => '6b50788637a27d27409cbb6bbf69fada8195888e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../twilio/sdk',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'vimeo/vimeo-api' => array(
            'pretty_version' => '3.0.10',
            'version' => '3.0.10.0',
            'reference' => '79885b642594b17f6c356dc949d3e4a58c356c9b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../vimeo/vimeo-api',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'voku/portable-ascii' => array(
            'pretty_version' => '2.0.3',
            'version' => '2.0.3.0',
            'reference' => 'b1d923f88091c6bf09699efcd7c8a1b1bfd7351d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../voku/portable-ascii',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
