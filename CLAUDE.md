# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Open eLMS (e-Learning Management System) - a comprehensive learning management platform built with PHP using the Slim Framework 4 and modern web technologies. The system supports multiple learning delivery methods including SCORM, LTI, and custom content delivery.

## Core Architecture

### Backend
- **Slim Framework 4**: Core PHP framework for routing and middleware
- **Eloquent ORM**: Database interactions using <PERSON><PERSON>'s Eloquent ORM
- **PHP-DI**: Dependency injection container
- **Monolog**: Logging system
- **PHP 8.1**: Currently running (see [PHP_8_COMPATIBILITY.md](PHP_8_COMPATIBILITY.md) for upgrade issues)

### Database
- **MySQL/MariaDB**: Primary database
- **Eloquent Models**: Located in `src/classes/Models/`
- **Migrations**: Database schema changes handled via `src/crons/upgrade.php`

### Frontend
- **AngularJS**: Primary frontend framework
- **SCSS/Sass**: CSS preprocessing
- **Gulp**: Build system for assets

## Quick Start

```bash
# Docker setup
cd docker
./generate-ssl.sh
docker compose up -d

# Access at: https://localhost
```

For complete Docker setup instructions, see: [docker/README.md](docker/README.md)

## Development Commands

### Asset Compilation
```bash
# SCSS/CSS
cd src/compiler_new
npm install
npm run gulp  # or: gulp

# JavaScript (always edit source files in src/tpls/js/)
docker compose exec web php crons/combine.php
```

### Database Operations
```bash
# Run database upgrade
docker compose exec web php crons/upgrade.php

# Access database
docker compose exec db mysql -u root -proot surrey
```

### Cron Tasks
```bash
# Run all scheduled tasks
docker compose exec web php crons/cron-tasks.php

# Process mail queue
docker compose exec web php crons/process_mail_queue.php
```

For detailed cron system documentation, see: [CRON.md](CRON.md)

## Project Structure

### Core Directories
- `src/app/`: Application configuration, routes, and settings
- `src/classes/`: PHP classes organized by namespace
  - `APP/`: Application logic and services
  - `Models/`: Eloquent database models
  - `DB/`: Database utilities and schema management
  - `Middleware/`: HTTP middleware classes
- `src/public/`: Web-accessible files (entry point)
- `src/tpls/`: Template files and SCSS
- `src/crons/`: Scheduled tasks and maintenance scripts
- `src/private/`: Private files and uploads
- `src/logs/`: Application logs

### Key Configuration Files
- `src/app/lms_config.php`: Main configuration (copy from `lms_config.default.php`)
- `src/app/routes.php`: Route definitions
- `src/app/dependencies.php`: DI container setup

## Development Guidelines

### Code Standards
- PSR-4 autoloading for PHP classes
- Eloquent ORM for database operations
- Dependency injection throughout the application
- Structured logging with Monolog
- Use consistent indentation (tabs, not spaces)
- Break complex conditions across multiple lines with proper alignment
- For Eloquent queries, use method chaining with proper line breaks and indentation
- End statements with semicolon on separate line when using method chaining

### Jira Integration
- **Never add comments to Jira issues automatically** - Always ask for confirmation
- When analyzing Jira issues, provide insights and suggestions to the user first
- Only add comments to Jira when explicitly requested and confirmed by the user

### Slim Framework & PSR-7
Always use proper PSR-7 response format:

```php
// CORRECT
$response->getBody()->write($content);
return $response
    ->withHeader('Content-Type', 'application/json')
    ->withHeader('Content-Disposition', 'attachment; filename="export.json"');

// INCORRECT
header('Content-Type: application/json');  // ❌ Causes Slim errors
echo json_encode($data);                   // ❌ Corrupts response
```

### Path Configuration
**Base Paths** (defined in `src/app/lms_config.php`):
- `AppFilePath` = "/var/www/html/"
- `PublicFilePath` = "/var/www/html/public/"

**Access in Routes**:
```php
// Best practice - DI container
$path = $this->get('settings')['LMSPrivatePath'];

// Legacy (still widely used)
$path = $GLOBALS["CONFIG"]->LMSPrivatePath;
```

## Key Features & Configuration

### Adding New Configuration Options

1. **Add to Configuration.php** (`src/classes/DB/Configuration.php`):
   ```php
   'ConfigKeyName' => [
       'name' => 'Display Name',
       'type' => 'boolean', // or 'string', 'integer', 'select-list'
       'status' => 1,
       'value' => 0, // default value
       'description' => 'Description of what this config does',
       'created_by' => 0,
   ],
   ```

2. **Add category assignment** in the same file:
   ```php
   'ConfigKeyName' => 'category_name',
   ```

3. **Load in menu.php** (`src/app/routes/menu.php`):
   ```php
   $data["config"]["ConfigKeyName"] = \APP\Tools::getConfig('ConfigKeyName');
   ```

4. **Use in frontend** as `config.ConfigKeyName`

5. **Run database upgrade**: `docker compose exec web php crons/upgrade.php`

### Smart Update System for JSON Configurations

**Method**: `Configuration::smartUpdateJsonConfiguration($configuration_values, $configKey, $itemIdentifier = 'name')`

**Location**: `src/classes/DB/Configuration.php:5100`

- Compares default configuration with existing client configurations
- Adds only missing entries - **never overwrites existing values**
- Works with any JSON configuration structure

### Email Template System

**Template Definitions**: `src/classes/DB/EmailTemplates.php`

**Template Usage**:
```php
// By slug (preferred)
$template = \Models\EmailTemplate::getTemplate('schedule_reminder');

// By name
$template = \Models\EmailTemplate::getTemplate('Forgotten Password Link');
```

**Variable Placeholders**: Use `%%VARIABLE_NAME%%` format

### Logging System

```php
\Models\Log::addEntry(false, false, 500, [
    'type' => 'api-error',
    'message' => 'Failed to process request'
]);
```

**Supported Options**: `type`, `message`, `uri`, `method`, `headers`

## SmartTable Best Practices

### Search Filters
```html
<!-- CORRECT: Double underscore for joined tables -->
<input st-search="learning_modules__name" placeholder="Module name" type="search"/>

<!-- INCORRECT: Dot notation creates nested objects -->
<input st-search="learning_modules.name" placeholder="Module name" type="search"/>
```

### Table Refresh Pattern
```javascript
// Controller
$scope.refreshTable = 0;
// Refresh
$scope.refreshTable = $scope.refreshTable + 1;
```

## Performance Optimizations

### ResourceQuery Optimization
See detailed documentation in CLAUDE.md sections:
- Database Indexes (High Impact)
- Simple Course Module Caching
- ALL Valid Resources Caching
- Enterprise-Scale Batch Processing (Critical for 20k+ Users)

### Caching Strategies
- **Course Modules**: Per-course caching with 24h TTL
- **Valid Resources**: Single cache entry for all valid module IDs
- **Query Results**: Selective caching for results < 5000 users

## Import System Architecture

Different import types use different logging tables:
- **General imports**: `ImportLog` table via `\Models\ImportLog`
- **Learning resource imports**: `LogExportImport` table via `LogExportImport::insertRecord()`
- **Location**: `src/classes/APP/Import.php`
- **Note**: Learning Import Resource Data will not appear in standard Import Log reports

## External Documentation

For specialized topics, refer to these documentation files:

- **[DEVELOPMENT.md](DEVELOPMENT.md)**: Development practices, SCORM handling, frontend best practices, technical patterns
- **[CRON.md](CRON.md)**: Complete cron system documentation, architecture, debugging, best practices
- **[PHP_8_COMPATIBILITY.md](PHP_8_COMPATIBILITY.md)**: PHP 8.0+ compatibility issues and migration guide
- **[docker/README.md](docker/README.md)**: Complete Docker setup, troubleshooting, configuration
- **[PERFORMANCE_OPTIMIZATION.md](PERFORMANCE_OPTIMIZATION.md)**: System performance optimization strategies
- **[TINYMCE_TO_CKEDITOR_MIGRATION.md](TINYMCE_TO_CKEDITOR_MIGRATION.md)**: Editor migration guide

## Special Considerations

### SCORM Suspend Data Handling
```php
// Properly decode suspend_data that was escaped by addslashes_js
$suspend_data_raw = str_replace(['\\\\', '\\"', '\\\'', '\\n', '\\r', '\\0', '<\/'], 
                                ['\\', '"', "'", "\n", "\r", "\0", '</'], $suspend_data_raw);
$suspend_data = json_decode($suspend_data_raw, true);
```

### Learning Module Completion State
- **Field**: `after_completion_do_not_reset_completion_state` in `learning_modules` table
- **Purpose**: Prevents resetting learner completion when new resources are added
- **Location**: `src/app/routes/learning.php:3585-3593`

### Payment Gateway Requirements
- **Pay360**: Requires PHP SOAP extension (included in Docker)
- **Stripe**: Only initialized when API key is configured

## API Development

- **Backend API**: Located in `src/app/routes/backend.php`
- **Authentication**: Token-based via `\APP\Auth::getBasicTokenCheck("APITokenAccess")`
- **Documentation**: Keep https://openelms.com/support-3/open-elms-api/ in sync with code