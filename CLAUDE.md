# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is Open eLMS (e-Learning Management System) - a comprehensive learning management platform built with PHP using the Slim Framework and modern web technologies. The system supports multiple learning delivery methods including SCORM, LTI, and custom content delivery.

### Repository Search and Navigation

The project includes both comprehensive repository summary and AI-powered semantic code search capabilities:

#### Repomix Integration
```bash
# Generate comprehensive repository summary (81MB file covering key directories)
repomix --include "src/app/,src/classes/,src/crons/,src/scripts/,src/tpls/,src/public/index.php,src/public/scorm/,src/public/api/" --ignore "src/public/api/data/"

# Output file: repomix-output.xml (contains structured representation of codebase)
```

#### AI-Powered Code Search (PostgreSQL + pgvector)

The system includes a complete vectorization infrastructure for semantic code search using OpenAI embeddings and PostgreSQL with pgvector extension.

**Architecture**:
- **PostgreSQL with pgvector**: Vector similarity search with 1536-dimensional embeddings
- **Python Vectorizer Service**: FastAPI service for code processing and search
- **Sentence Transformers Fallback**: Local embeddings when OpenAI is unavailable
- **Docker Integration**: Complete containerized setup

**Usage**:
```bash
# Start vectorization infrastructure
docker compose up -d postgres python_vectorizer

# Check vectorization status
docker compose exec python_vectorizer python /app/scripts/vectorizer_cli.py stats

# Search for code functionality
docker compose exec python_vectorizer python /app/scripts/vectorizer_cli.py search "user authentication" -l 5
docker compose exec python_vectorizer python /app/scripts/vectorizer_cli.py search "learning module completion" -l 10

# Re-vectorize codebase (if needed)
docker compose exec python_vectorizer python /app/scripts/vectorizer_cli.py vectorize
```

**Search Capabilities**:
- **Semantic Search**: Find functions, classes, and routes by meaning, not just keywords
- **Code Understanding**: Search for "user login" finds authentication-related functions
- **Threshold Control**: Adjustable similarity matching (default: 0.3 for broader results)
- **Content Types**: Functions, classes, routes automatically categorized

**Current Status**: 
- **6,859 code chunks** processed from **1,434 PHP files**
- **3 content types**: Functions, classes, routes
- **Vector similarity search** with cosine distance
- **Full-text search** integration for hybrid results

**Examples**:
```bash
# Find authentication code
search "user login"           # → Authentication functions, login handlers

# Find learning functionality  
search "learning completion"  # → LearningResult models, completion logic

# Find database operations
search "database migration"   # → Schema creation, upgrade functions

# Find API endpoints
search "REST API endpoint"    # → Route definitions, controllers
```

**Benefits**:
- **Faster Discovery**: Find relevant code without knowing exact function names
- **Conceptual Search**: Search by purpose rather than implementation details
- **Cross-Reference**: Discover related functionality across the codebase
- **Documentation**: Understand code relationships and patterns

**Current Coverage**:
- ✅ Application configuration and routes (`src/app/`)
- ✅ All PHP classes and models (`src/classes/`)
- ✅ Cron jobs and scheduled tasks (`src/crons/`)
- ✅ Utility scripts (`src/scripts/`)
- ✅ Templates and frontend logic (`src/tpls/`)
- ✅ Main entry point (`src/public/index.php`)
- ✅ SCORM implementation (`src/public/scorm/`)
- ✅ API endpoints (`src/public/api/`)
- ❌ Static assets (CSS, images, compiled JS)
- ❌ Vendor dependencies
- ❌ Docker configuration
- ❌ Test files

**Suggested Improvements**:
For even better coverage, consider adding:
```bash
# More comprehensive command including configuration and documentation:
repomix --include "src/app/,src/classes/,src/crons/,src/scripts/,src/tpls/,src/public/index.php,src/public/scorm/,src/public/api/,src/compiler_new/,docker/,*.md,*.json,*.yml" --ignore "src/public/api/data/,vendor/,node_modules/,src/logs/,src/temp/,src/private/,*.log,*.cache"

# Or for a focused backend-only view:
repomix --include "src/app/,src/classes/,src/crons/,src/scripts/" --ignore "vendor/,*.log"
```

## Core Architecture

### Backend Framework
- **Slim Framework 4**: Core PHP framework for routing and middleware
- **Eloquent ORM**: Database interactions using Laravel's Eloquent ORM
- **PHP-DI**: Dependency injection container
- **Monolog**: Logging system

### Database
- **MySQL/MariaDB**: Primary database
- **Eloquent Models**: Located in `src/classes/Models/`
- **Migrations**: Database schema changes handled via `src/crons/upgrade.php`

#### Import Logging Architecture
Different import types use different logging tables:
- **General imports**: `ImportLog` table via `\Models\ImportLog`
- **Learning resource imports**: `LogExportImport` table via `LogExportImport::insertRecord()`
- **Location**: `src/classes/APP/Import.php`
- **Note**: Learning Import Resource Data will not appear in standard Import Log reports

### Frontend
- **AngularJS**: Primary frontend framework
- **SCSS/Sass**: CSS preprocessing
- **Gulp**: Build system for assets

### Key Components
- **Learning Modules**: Core learning content system
- **User Management**: Authentication, roles, and permissions
- **SCORM Support**: Standards-compliant e-learning content
- **LTI Integration**: Learning Tools Interoperability
- **Assessment System**: Quizzes, assignments, and grading
- **Reporting**: Comprehensive analytics and reporting system

## Development Commands

### Docker Development Environment
The project includes a Docker setup for local development in the `docker/` directory.

**For complete Docker setup instructions, troubleshooting, and configuration details, see: [docker/README.md](docker/README.md)**

**Quick Setup**:
```bash
cd docker
./generate-ssl.sh
docker compose up -d
```

Access the application at: https://localhost

### Asset Compilation

#### SCSS/CSS Compilation
```bash
# Navigate to compiler directory
cd src/compiler_new

# Install dependencies
npm install

# Run Gulp build process
npm run gulp              # Runs default watch task
# or
gulp

# Compile specific assets
npx gulp buildStyles      # Compiles SCSS only
npx gulp buildJavaScript  # Compiles JS only

# Watch for changes (development)
gulp default
```

#### JavaScript Compilation
```bash
# Combine JavaScript assets (from docker directory)
docker compose exec web php crons/combine.php
```

**Important Notes:**
- **Source files**: Located in `src/tpls/js/` (controllers, services, directives, etc.)
- **Compiled files**: Located in `src/public/js/` (DO NOT EDIT DIRECTLY)
- **Always edit source files** in `src/tpls/js/` and then compile using the combine.php script
- The combine.php script concatenates and processes all JavaScript source files

### Backend Dependencies
```bash
# Install PHP dependencies
cd src
composer install

# Update dependencies
composer update
```

### Cron Tasks
```bash
# Run all scheduled tasks
docker compose exec web php crons/cron-tasks.php

# Process imports
docker compose exec web php crons/import.php

# Run database setup/upgrade
docker compose exec web php crons/setup.php

# Process mail queue
docker compose exec web php crons/process_mail_queue.php
```

**For detailed cron system documentation, see: [CRON.md](CRON.md)**

## Project Structure

### Core Directories
- `src/app/`: Application configuration, routes, and settings
- `src/classes/`: PHP classes organized by namespace
  - `APP/`: Application logic and services
  - `Models/`: Eloquent database models
  - `DB/`: Database utilities and schema management
  - `Middleware/`: HTTP middleware classes
- `src/public/`: Web-accessible files (entry point)
- `src/tpls/`: Template files and SCSS
- `src/crons/`: Scheduled tasks and maintenance scripts
- `src/private/`: Private files and uploads
- `src/logs/`: Application logs

### Key Configuration Files
- `src/app/lms_config.php`: Main configuration (copy from `lms_config.default.php`)
- `src/composer.json`: PHP dependencies
- `src/app/routes.php`: Route definitions
- `src/app/dependencies.php`: DI container setup

### Asset Compilation
- `src/compiler_new/`: Modern Gulp build system
- `src/compiler/`: Legacy Gulp build system
- Source SCSS files in `src/tpls/sass/`
- Compiled CSS output to `src/public/css/`

## Key Features & Modules

### Learning Management
- **Learning Modules**: Content delivery system
- **SCORM Packages**: Standards-compliant e-learning
- **Assessments**: Quizzes and assignments
- **Prerequisites**: Learning path dependencies
- **Certificates**: Automated certificate generation

#### Completion State Preservation
- **Field**: `after_completion_do_not_reset_completion_state` in `learning_modules` table
- **Purpose**: Prevents resetting learner completion when new resources are added to lessons
- **Location**: `src/app/routes/learning.php:3585-3593`
- **Trade-off**: Preserves completion but may interfere with refresher notification logic
- **Use Case**: Historical training records that shouldn't be affected by content updates

### User Management
- **Role-based Access**: Granular permission system
- **Multi-tenancy**: Company/department isolation
- **Authentication**: Local and OAuth support
- **User Profiles**: Comprehensive user data management

### Reporting & Analytics
- **Learning Reports**: Progress and completion tracking
- **ILR Export**: UK government reporting compliance
- **Custom Reports**: Flexible reporting system
- **Batch Reports**: Scheduled report generation

### Integrations
- **LTI**: Learning Tools Interoperability
- **Microsoft Teams**: Video conferencing integration
- **Zoom**: Meeting integration
- **Payment Gateways**: Stripe, Pay360 support
- **Email Systems**: PHPMailer integration

#### Email System Integration
- **Office 365 Detection**: System detects OAuth-enabled accounts and routes emails accordingly
- **Fallback Logic**: Non-OAuth accounts use standard SMTP routing
- **Configuration**: Controlled via email settings and OAuth connection status

### Cron System & Background Processing
The system includes a comprehensive cron task system for background processing.

**For complete cron system documentation, including architecture, debugging, and best practices, see: [CRON.md](CRON.md)**

**Quick Commands**:
```bash
# Run all scheduled tasks
docker compose exec web php crons/cron-tasks.php

# Process mail queue
docker compose exec web php crons/process_mail_queue.php
```

## SmartTable Best Practices

### Search Filter Implementation
**Always use double underscore notation for joined table searches:**
```html
<!-- Correct: Double underscore notation -->
<input st-search="learning_module_evidences__evidence" placeholder="File name" type="search"/>
<input st-search="learning_modules__name" placeholder="Module name" type="search"/>

<!-- Incorrect: Dot notation creates nested objects -->
<input st-search="learning_module_evidences.evidence" placeholder="File name" type="search"/>
```

**Frontend Issue**: Dot notation in HTML search fields creates nested objects like:
```json
{"search":{"learning_module_evidences":{"evidence":"search_term"}}}
```
**SmartTable expects**: Flat structure like:
```json
{"search":{"learning_module_evidences__evidence":"search_term"}}
```

### Table Refresh Pattern
**Use the simple counter increment pattern for reliable table refreshes:**

**HTML Template:**
```html
<table st-pipe="callServer" st-table="data" class="table table-striped report">
    <thead>
        <tr style="display: none;">
            <td>
                <input search-watch-model="refreshTable" st-search="refresh" type="hidden"/>
            </td>
        </tr>
        <!-- Regular header rows -->
    </thead>
</table>
```

**JavaScript Controller:**
```javascript
// Initialize counter
$scope.refreshTable = 0;

// Refresh function - simple increment
$scope.refreshTable = $scope.refreshTable + 1;
```

**Backend Route:**
```php
// Remove refresh parameter before SmartTable processing
if (isset($params["search"]["refresh"])) {
    unset($params["search"]["refresh"]);
}
```

**Benefits**: Much simpler than manual state management, more reliable, framework-native approach.

### SmartTable ID Field Logic
**Understanding SmartTable's automatic behavior:**
- Fields matching `/\.id$|_id$|^id$/` get exact matching (`=`)
- All other fields get LIKE matching with wildcards (`%search%`)
- Use this regex pattern when designing field names

### Concatenated Field Searches
**For searching across multiple fields (like full names):**
```html
<input st-search="users.fnameAAAusers.lname" placeholder="Full name" type="search"/>
```
**SmartTable converts**: `users.fnameAAAusers.lname` becomes `CONCAT(users.fname, ' ', users.lname)`

## Development Notes

### Code Standards
- PSR-4 autoloading for PHP classes
- Eloquent ORM for database operations
- Dependency injection throughout the application
- Structured logging with Monolog

### Preferred Code Output Format
When providing code suggestions or implementations, use this format:

```
**Location**: `path/to/file.php` (line numbers if applicable)
**Purpose**: Brief description of what the code does
**Implementation**: 
```php
// Code block with clear comments explaining the logic
if (
	!empty($data['id']) &&
	$learning->is_course == 0 &&
	$learning->is_skill == 0 &&
	!empty($data['version']) &&
	$data['version'] != $learning->getOriginal('version')
) {
	\Models\LearningResult
		::where('learning_module_id', $learning->id)
		->where('completion_status', \Models\LearningResult::COMPLETION_STATUS_NOT_ATTEMPTED)
		->where('refreshed', 0)
		->update(['started_version' => $data['version']])
	;
}
```
**Benefits**:
- Bullet point explaining advantages
- Another benefit  
- Final benefit

**Notes**: Any additional context or considerations
```

**Code Formatting Style Preferences**:
- Use consistent indentation (tabs, not spaces)
- Break complex conditions across multiple lines with proper alignment
- For Eloquent queries, use method chaining with proper line breaks and indentation
- End statements with semicolon on separate line when using method chaining
- Use clear, descriptive variable names
- Add comments to explain complex logic

### Jira Interaction Guidelines
- **Never add comments to Jira issues automatically** - Always ask for confirmation before adding any comments
- When analyzing Jira issues, provide insights and suggestions to the user first
- Only add comments to Jira when explicitly requested and confirmed by the user
- This prevents unintended modifications to issue tracking systems

### Adding New Configuration Options

To add new system configuration options that can be controlled via the admin interface:

1. **Add to Configuration.php** (`src/classes/DB/Configuration.php`):
   ```php
   'ConfigKeyName' => [
       'name' => 'Display Name',
       'type' => 'boolean', // or 'string', 'integer', 'select-list'
       'status' => 1,
       'value' => 0, // default value
       'description' => 'Description of what this config does',
       'created_by' => 0,
   ],
   ```

2. **Add category assignment** in the same file:
   ```php
   'ConfigKeyName' => 'category_name', // e.g., 'learner_interface', 'uploading_work'
   ```

3. **Load in menu.php** (`src/app/routes/menu.php`):
   ```php
   $data["config"]["ConfigKeyName"] = \APP\Tools::getConfig('ConfigKeyName');
   ```

4. **Use in frontend** as `config.ConfigKeyName` in HTML/AngularJS

5. **Run database upgrade**: See Database Upgrade Process section below

### Adding New Fields to Database Models

To add new fields to existing database tables:

1. **Update the Model** (`src/classes/Models/ModelName.php`):
   - Add fields to the `$fillable` array
   - Add appropriate `$casts` if needed

2. **Add to Database Schema** (`src/classes/DB/UpgradeDb.php`):
   ```php
   if (!Capsule::schema()->hasColumn('table_name', 'field_name')) {
       Capsule::schema()->table('table_name', function ($table) {
           $table->text('field_name')->nullable()->after('existing_field');
       });
   }
   ```

3. **Run database upgrade**: `docker compose exec web php crons/upgrade.php`

### Email Template System

**Template Definitions**: `src/classes/DB/EmailTemplates.php` contains all default email templates

**Template Structure**:
```php
$templates[] = [
    'name' => 'Template Name',
    'subject' => 'Email Subject',
    'body' => 'HTML email body with %%VARIABLES%%',
    'site_versions' => '"apprentix" "openelmsschools"', // Space-separated quoted values
    'slug' => 'url_friendly_slug',
    'force_update' => false, // Force update existing templates
    'system_trigger' => 'What triggers this email',
    'recipients' => 'Who receives this email',
    'timings_recurrence' => 'When/how often it is sent'
];
```

**Template Usage in Code**:
Templates are retrieved using `\Models\EmailTemplate::getTemplate()` which accepts either name or slug:

```php
// By template name
$template = \Models\EmailTemplate::getTemplate('Forgotten Password Link');

// By slug (more common for programmatic usage)
$template = \Models\EmailTemplate::getTemplate('schedule_reminder');

// Conditional slug selection
switch ($schedule->type) {
    case 'lesson':
        $template_slug = 'schedule_reminder';
        break;
    case 'meeting':
        $template_slug = 'schedule_meeting_reminder';
        break;
    default:
        $template_slug = 'schedule_reminder';
        break;
}
$template = \Models\EmailTemplate::getTemplate($template_slug);
```

**Variable Placeholders**: Use `%%VARIABLE_NAME%%` format in templates

**SCOR-5559 Documentation Process**: 
To complete the email template documentation, each template needs to be:
1. Located in the codebase (search for `getTemplate()` calls)
2. Analyzed for system triggers and timing logic
3. Documented with proper `system_trigger`, `recipients`, and `timings_recurrence` values

### Database Upgrade Process
Always run database upgrades after configuration changes:

```bash
# Run database upgrade (recommended method)
docker compose exec web php crons/upgrade.php

# For memory-intensive operations, memory limit is already set to 1024M in Docker
# If you encounter memory issues, rebuild containers:
docker compose down && docker compose up --build -d
```

**Note**: Configuration changes in `src/classes/DB/Configuration.php` require running the upgrade script to be created in the database.

### Smart Update System for JSON Configurations

The system includes a reusable method for safely updating JSON-based configuration options without overwriting existing client values.

#### Adding New Items to JSON Configurations

**Method**: `Configuration::smartUpdateJsonConfiguration($configuration_values, $configKey, $itemIdentifier = 'name')`

**Location**: `src/classes/DB/Configuration.php:5100`

**Usage**:
```php
// Basic usage (identifier field: 'name')
self::smartUpdateJsonConfiguration($configuration_values, 'PurgeAuditRecordsDays');

// Custom identifier field
self::smartUpdateJsonConfiguration($configuration_values, 'NotificationSettings', 'type');
self::smartUpdateJsonConfiguration($configuration_values, 'FeatureFlags', 'feature');
```

**How it works**:
1. Compares default configuration with existing client configurations
2. Identifies missing entries using the specified identifier field
3. Adds only missing entries - **never overwrites existing values**
4. Only saves when changes are actually made

**Example JSON Structures**:
```json
// PurgeAuditRecordsDays (identifier: 'name')
[
    {"name": "TableHistory", "value": 365},
    {"name": "PaymentHistory", "value": 365}
]

// NotificationSettings (identifier: 'type') 
[
    {"type": "email", "enabled": true, "frequency": "daily"},
    {"type": "sms", "enabled": false, "frequency": "weekly"}
]
```

**Adding Smart Updates**:
1. Add your JSON configuration to `$configuration_values` array
2. Add smart update call in `insertOrUpdateCategories()` method:
   ```php
   self::smartUpdateJsonConfiguration($configuration_values, 'YourConfigKey', 'identifier_field');
   ```
3. Run `docker compose exec web php crons/upgrade.php`

**Benefits**:
- Client-safe: Preserves existing configuration values
- Future-proof: Automatically handles new configuration options
- Reusable: Works with any JSON configuration structure
- Efficient: Only updates when necessary

### Database Models
- All models extend `Illuminate\Database\Eloquent\Model`
- Located in `src/classes/Models/`
- Follow Laravel Eloquent conventions
- Use proper relationships and fillable properties

### Frontend Development
- SCSS files in `src/tpls/sass/`
- JavaScript in `src/public/js/`
- AngularJS controllers in `src/public/js/controllers.js`
- Use Gulp for asset compilation

#### Utility Classes (utilities.scss)
The `src/tpls/sass/partials/utilities.scss` file contains reusable utility classes following BEM naming conventions:

**Available Utility Classes:**
- **Margins**: `u-margin--left-five`, `u-margin--left-ten`, `u-margin--left-fifteen`, etc.
- **HTTP Status Colors**: `u-http-status--200`, `u-http-status--404`, `u-http-status--500`, etc.
- **Layout**: `u-flexbox`, `u-pointer`, etc.

**Adding New Utility Classes:**
1. Open `src/tpls/sass/partials/utilities.scss`
2. Find the appropriate section (e.g., margins, colors, layout)
3. Follow the existing BEM naming pattern: `u-category--modifier-value`
4. Add your class within the existing SCSS structure

**Examples:**
```scss
// Adding a new margin-left option
&--left {
    &-five { margin-left: 5px; }     // Creates u-margin--left-five
    &-ten { margin-left: 10px; }     // Creates u-margin--left-ten
}

// Adding custom component styles
.log-expand-btn {
    opacity: 0.7;
    transition: opacity 0.2s;
    &:hover { opacity: 1; }
}
```

**Best Practices:**
- Always check if a similar utility already exists before creating new ones
- Follow the established naming conventions
- Place utilities in the appropriate section within the file
- Compile assets after changes: `cd src/compiler_new && npm run gulp`

#### Conditional Display Patterns
Use configuration-based conditional display in templates:

```html
<!-- Hide elements based on configuration -->
<element ng-if="!config.HideFeatureName">Content</element>

<!-- Show elements based on configuration -->
<element ng-if="config.ShowFeatureName">Content</element>

<!-- Multiple conditions -->
<element ng-if="isApprentix && !config.HideReflectiveLog">Content</element>

<!-- Complex conditional logic -->
<element ng-if="config.allowAddBlogEntry || (isApprentix && !config.HideReflectiveLog)">Content</element>
```

**Notes:**
- Configuration values are automatically available as `config.ConfigKeyName` in all frontend templates
- Config values are loaded in `menu.php` as: `$data["config"]["ConfigKeyName"] = \APP\Tools::getConfig('ConfigKeyName');`
- Use boolean logic for complex conditional displays
- Always test both true/false states of configuration options

### Slim Framework & PSR-7 Response Handling

#### Export/Download Endpoints
When creating endpoints that export files or JSON data, always use proper PSR-7 response format:

```php
// CORRECT: Use PSR-7 response format
public function export(Request $request, Response $response, array $args) {
    $content = json_encode($data);
    
    $response->getBody()->write($content);
    return $response
        ->withHeader('Content-Type', 'application/json')
        ->withHeader('Content-Disposition', 'attachment; filename="export.json"')
        ->withHeader('Content-Length', strlen($content));
}

// INCORRECT: Old PHP header functions cause corruption
public function export(Request $request, Response $response, array $args) {
    header('Content-Type: application/json');  // ❌ Causes Slim errors
    echo json_encode($data);                   // ❌ Corrupts response
    // Missing return statement                // ❌ Causes framework errors
}
```

**Common Issues:**
- Using `header()` functions instead of `withHeader()` 
- Using `echo` or `readfile()` instead of `$response->getBody()->write()`
- Not returning the response object (causes "null returned" errors)
- Framework errors get appended to file content, corrupting downloads

#### JSON Import/Export Safety
When handling JSON import/export, always use defensive programming:

```php
// Safe array access with null coalescing
$workflows = $json['workflows'] ?? [];
$dependencyForms = $json['dependencyForms'] ?? [];

// Safe workflow name extraction
$workflowName = 'Unknown';
if (!empty($json['workflows']) && isset($json['workflows'][0]['name'])) {
    $workflowName = $json['workflows'][0]['name'];
}

// Safe iteration over potentially null arrays
foreach ($workflows as $workflowData) {
    foreach ($workflowData['form_workflow_templates'] ?? [] as $templateData) {
        // Process template data safely
    }
}
```

#### Route Groups and Settings Access
When working with Slim 4 route groups, use the correct syntax and settings access patterns:

```php
// CORRECT: Slim 4 route group with $group parameter
$app->group("/disk-space", function ($group) {
    
    $group->get("/stats", function (Request $request, Response $response, array $args) {
        // Access settings via $this->get('settings')
        $privatePath = $this->get('settings')['LMSPrivatePath'];
        $publicPath = $this->get('settings')['LMSPublicPath'];
        
        // Use PSR-7 response
        $response->getBody()->write(json_encode($data));
        return $response->withHeader('Content-Type', 'application/json');
    });
    
});

// INCORRECT: Old Slim 3 style
$app->group("/disk-space", function () {  // ❌ Missing $group parameter
    
    $this->get("/stats", function (Request $request, Response $response, array $args) {  // ❌ Using $this instead of $group
        $privatePath = $this->settings['LMSPrivatePath'];  // ❌ Wrong settings access
        
        return $response->write(json_encode($data));  // ❌ ->write() doesn't exist in PSR-7
    });
    
});
```

#### File Serving Through PSR-7
When serving files (like viewing uploaded documents), use proper PSR-7 streaming instead of raw headers:

```php
// CORRECT: PSR-7 file serving
$stream = fopen($filePath, 'rb');
if ($stream) {
    $body = $response->getBody();
    while (!feof($stream)) {
        $body->write(fread($stream, 8192));
    }
    fclose($stream);
}

return $response
    ->withHeader('Content-Type', $contentType)
    ->withHeader('Content-Length', (string)$fileSize)
    ->withHeader('Content-Disposition', 'inline; filename="' . addslashes($fileName) . '"')
    ->withHeader('Cache-Control', 'private, max-age=3600')
    ->withHeader('X-Content-Type-Options', 'nosniff');

// INCORRECT: Raw headers and file output
header('Content-Type: ' . $contentType);        // ❌ Bypasses Slim middleware
header('Content-Length: ' . $fileSize);         // ❌ Raw headers
readfile($filePath);                             // ❌ Direct file output
exit;                                            // ❌ Breaks middleware chain
```

#### Path Configuration System
The LMS uses a structured path configuration system:

**Base Paths** (defined in `src/app/lms_config.php`):
- `AppFilePath` = "/var/www/html/" (main application directory)
- `PublicFilePath` = "/var/www/html/public/" (web-accessible files)

**Derived Paths** (built in `src/app/settings.php`):
- `LMSPrivatePath` = `AppFilePath . "private/"` (user uploads, evidence)
- `LMSPublicPath` = `AppFilePath . "public/"` (public assets) 
- `LMSTempPath` = `AppFilePath . "temp/"` (temporary files)
- `LMSScormDataPath` = `PublicFilePath . "scormdata/"` (SCORM content)
- And many others...

**Usage in Routes** (see Route Groups section for more examples):
```php
// Access paths via settings
$privatePath = $this->get('settings')['LMSPrivatePath'];  // /var/www/html/private/
$evidencePath = $privatePath . 'evidence/';              // /var/www/html/private/evidence/
$logsPath = $this->get('settings')['AppFilePath'] . 'logs/';  // /var/www/html/logs/
```

**Common Mistakes**:
- Using non-existent path keys (like `LMSRootPath`)
- Hardcoding paths instead of using configuration
- Accessing settings incorrectly (`$this->settings` vs `$this->get('settings')`)

### Debug Scripts and Tools

When creating debug scripts in the `scripts/` directory:

1. **Use proper setup**: Always include `include __DIR__ . '/../crons/setup.php';` at the top
2. **Follow existing patterns**: Look at `scripts/remove_base64_image_from_email_template.php` for structure
3. **Include error handling**: Wrap database operations in try-catch blocks
4. **Provide clear output**: Use echo statements to show progress and results

**Example debug script structure:**
```php
<?php
include __DIR__ . '/../crons/setup.php';

use Models\SomeModel;

// Parse command line arguments
if ($argc < 2) {
    echo "Usage: php debug_script.php <argument>\n";
    exit(1);
}

$arg = $argv[1];
echo "=== Debug Script Name ===\n";
echo "Processing: $arg\n";

try {
    // Your debug logic here
    $result = SomeModel::find($arg);
    echo "✓ Found: " . $result->name . "\n";
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
    exit(1);
}

echo "=== Debug Complete ===\n";
```

### Troubleshooting JSON Corruption

If JSON export files are corrupted with error messages appended:

1. **Check the export function**: Ensure it uses proper PSR-7 response format
2. **Use the JSON fixer tool**: `php scripts/fix_corrupted_workflow_json.php input.json output.json`
3. **Validate JSON**: Use `python3 -m json.tool file.json` to check for syntax errors
4. **Look for error patterns**: Search for `}{"error":` which indicates response corruption

### Security Considerations
- Configuration files contain sensitive data (excluded from git)
- User authentication handled by `APP\Auth` class
- SQL injection prevention via Eloquent ORM
- File upload restrictions in place

## Common Tasks

### Adding New Features
1. Create database models in `src/classes/Models/`
2. Add routes in `src/app/routes/`
3. Implement business logic in `src/classes/APP/`
4. Update frontend templates and scripts
5. Run database migrations if needed

### Debugging
- Check logs in `src/logs/`
- Enable debug mode in configuration
- Use Clockwork middleware for profiling
- Database query logging available

### Logging System
The system uses a custom logging mechanism through `\Models\Log::addEntry()` for application-specific logging:

**Method Signature:**
```php
\Models\Log::addEntry($request = false, $response = false, $code = false, $options = [])
```

**Supported Options:**
- `'type'` - Log entry type/category
- `'message'` - Main log message (required)
- `'uri'` - Request URI
- `'method'` - HTTP method
- `'headers'` - Request headers

**Usage Examples:**
```php
// Basic error logging
\Models\Log::addEntry(false, false, 500, [
    'type' => 'api-error',
    'message' => 'Failed to process request'
]);

// Detailed logging with context
\Models\Log::addEntry(false, false, 200, [
    'type' => 'outlook-integration',
    'message' => "Event created | ID: {$eventId} | Subject: {$subject}"
]);

// Multiple data points in message
\Models\Log::addEntry(false, false, 500, [
    'type' => 'subscription-failed',
    'message' => "User: {$email} | Request: {$requestData} | Response: {$apiResponse}"
]);
```

**Important Notes:**
- Only the fields listed in "Supported Options" are stored
- Additional fields in `$options` array are ignored
- For complex debugging data, concatenate into the `message` field
- 500-level errors trigger email notifications if configured

### API Development
- **Backend API**: Located in `src/app/routes/backend.php`
- **Authentication**: Token-based authentication via `\APP\Auth::getBasicTokenCheck("APITokenAccess")`
- **Token Configuration**: Set in "System Setup/Defaults/Configuration/APITokenAccess"
- **Response Format**: Always use PSR-7 response format for consistency
- **Error Handling**: Use `\APP\Tools::returnCode()` for standardized error responses

**Common API Patterns:**
```php
// Correct PSR-7 response format (see Slim Framework section for detailed examples)
$response->getBody()->write(json_encode($data));
return $response->withHeader('Content-Type', 'application/json');

// Error handling
return \APP\Tools::returnCode($request, $response, 500, 'Error message', ['type' => 'api']);

// Success with custom status
\Models\Log::addEntry($request, $response, 201, ['type' => 'api', 'message' => 'Resource created']);
return $response->withStatus(201);
```

### Testing API Endpoints
```bash
# Test API endpoint with curl
curl -H "Authorization: Basic {TOKEN}" \
     -H "Content-Type: application/json" \
     -X GET "https://localhost/backend/users/1"

# Test with pagination
curl -H "Authorization: Basic {TOKEN}" \
     "https://localhost/backend/users?limit=10&offset=0"

# Test POST endpoint
curl -H "Authorization: Basic {TOKEN}" \
     -H "Content-Type: application/json" \
     -X POST "https://localhost/backend/users" \
     -d '{"fname":"John","lname":"Doe","username":"jdoe","email":"<EMAIL>"}'
```

### Documentation Maintenance
- **API Documentation**: External documentation at https://openelms.com/support-3/open-elms-api/ must be kept in sync with `src/app/routes/backend.php`
- **When adding new API endpoints**: Update both the code and external documentation
- **Before releases**: Audit API documentation against actual implementation
- **API changes**: Log in Jira with "API Documentation" label for tracking

### Deployment
- Use `bitbucket-pipelines.yml` for CI/CD
- Run database upgrade after deployment (see Database Upgrade Process section)
- Compile assets (see Asset Compilation section)
- Clear caches and restart services

### Important Development Notes

**For detailed development practices, SCORM handling, frontend best practices, and technical patterns, see: [DEVELOPMENT.md](DEVELOPMENT.md)**

#### PHP Version Compatibility
**⚠️ IMPORTANT**: The system currently has PHP 8.1 compatibility issues that need to be addressed:
- **Critical blocker**: `create_function()` usage in SCORM libraries (removed in PHP 8.0)
- **For full compatibility details and fixes, see: [PHP_8_COMPATIBILITY.md](PHP_8_COMPATIBILITY.md)**
- **Compatibility check script**: Run `php scripts/php81_compatibility_check.php`

#### SCORM Suspend Data Handling
**Issue**: JSON corruption in SCORM suspend_data due to improper escaping

**Solution** (`src/public/scorm/api.php`):
```php
// Properly decode suspend_data that was escaped by addslashes_js
$suspend_data_raw = $userdata->{'cmi.suspend_data'};
// Reverse the escaping done by addslashes_js
$suspend_data_raw = str_replace(['\\\\', '\\"', '\\\'', '\\n', '\\r', '\\0', '<\/'], 
                                ['\\', '"', "'", "\n", "\r", "\0", '</'], $suspend_data_raw);
$suspend_data = json_decode($suspend_data_raw, true);

// Filter out null entries from interactions array to prevent SCORM player hanging
if (isset($suspend_data['cmi']['interactions']) && is_array($suspend_data['cmi']['interactions'])) {
    $suspend_data['cmi']['interactions'] = array_values(array_filter($suspend_data['cmi']['interactions'], function($interaction) {
        return $interaction !== null;
    }));
}
```

**Key Points**:
- `addslashes_js()` and `stripslashes()` are not perfect inverses
- Always properly reverse `addslashes_js()` escaping before JSON decoding
- Filter null interactions to prevent SCORM player issues

#### Asset Compilation Reference
For complete asset compilation instructions, see the "Asset Compilation" section above.

#### Frontend Development Best Practices

**Report Builder Enhancements:**
- When implementing features that affect user data (like downloads), always ensure current filters are applied
- Use `$scope.needsRefresh` to track when filters have changed
- Implement fail-safes for critical operations (e.g., refresh before download)

**CSS Animations and Visual Feedback:**
- Keep animations subtle but noticeable
- Ensure icons remain visible during animations
- Use warm colors (orange/amber) for attention-grabbing elements rather than harsh reds
- Test animations to ensure they don't distract from functionality
- Example refresh button animation in `src/tpls/sass/partials/app.scss`

**AngularJS Service Patterns:**
- Services are injected with dependencies like `$timeout`, `$http`, etc.
- Use `$timeout` instead of `setTimeout` for Angular digest cycle compatibility
- Always check if required dependencies are injected before using them

#### Learning Results Display Logic
- **Progress Section**: Shows active results (`refreshed = 0`)
- **Archives Section**: Shows completed/historical results (`refreshed = 1`)
- **Key Filter**: `src/classes/Models/LearningResult.php:1441`
- **Purpose**: Separates current training from audit trail records

#### Puppeteer Default Settings
When using Puppeteer for testing or screenshots:
- Default URL: `https://localhost/`
- Default resolution: 1920x1080 (Full HD)
- SSL handling: Use `allowDangerous: true` and `ignoreHTTPSErrors: true` for local development
- Launch options example:
```javascript
{
  "headless": false,
  "defaultViewport": {"width": 1920, "height": 1080},
  "args": ["--window-size=1920,1080", "--ignore-certificate-errors"],
  "ignoreHTTPSErrors": true
}