services:
  db:
    image: mariadb:10.11
    container_name: lms_mariadb
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: root
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./import:/import:ro
    networks:
      - lms_network
    command: --sql-mode="" --max_allowed_packet=1073741824 --innodb_buffer_pool_size=1G --tmp_table_size=1G --max_heap_table_size=1G

  redis:
    image: redis:7-alpine
    container_name: lms_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - lms_network
    command: redis-server --appendonly yes


  web:
    build: .
    container_name: lms_web
    restart: unless-stopped
    working_dir: /var/www/html
    volumes:
      - ../src:/var/www/html
    depends_on:
      - db
    networks:
      - lms_network
    environment:
      - PHP_IDE_CONFIG=serverName=lms

  nginx:
    image: nginx:alpine
    container_name: lms_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ../src:/var/www/html
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - web
    networks:
      - lms_network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  lms_network:
    driver: bridge