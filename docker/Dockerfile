FROM php:8.1-fpm

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    libzip-dev \
    libpq-dev \
    zip \
    unzip \
    && rm -rf /var/lib/apt/lists/*

# Install PHP extensions
RUN docker-php-ext-install pdo pdo_mysql pdo_pgsql pgsql mysqli mbstring exif pcntl bcmath gd zip opcache soap

# Copy OPcache configuration
COPY php/opcache.ini /usr/local/etc/php/conf.d/opcache.ini

# Set PHP configuration for uploads and memory
RUN echo "memory_limit = 1024M" > /usr/local/etc/php/conf.d/99-memory-limit.ini && \
    echo "upload_max_filesize = 200M" > /usr/local/etc/php/conf.d/99-upload-limits.ini && \
    echo "post_max_size = 200M" >> /usr/local/etc/php/conf.d/99-upload-limits.ini && \
    echo "max_execution_time = 300" >> /usr/local/etc/php/conf.d/99-upload-limits.ini && \
    echo "max_input_time = 300" >> /usr/local/etc/php/conf.d/99-upload-limits.ini

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Set working directory
WORKDIR /var/www/html

# Install PHP dependencies will be handled by volume mount
# No need to copy files here as they will be mounted

# Create necessary directories and set permissions
# These will be created at runtime via volume mounts

# Expose port 9000 for PHP-FPM
EXPOSE 9000

CMD ["php-fpm"]